<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Lending - Onboarding Engine" thread-count="10" preserve-order="true" verbose="30">
    <test name="Lending" preserve-order="true" thread-count="10">

        <parameter name="stagingLendingUrl" value="https://goldengate-staging3.paytm.com/MerchantService"></parameter>
        <parameter name="stagingOCLUrl" value="https://goldengate-staging12.paytm.com/MerchantService"></parameter>
        <parameter name="DB" value="oe_lending_staging10_220526"></parameter>

        <parameter name="ssh_host_xml" value="************"></parameter>
        <parameter name="ssh_user_xml" value="nishant_30099"></parameter>
        <parameter name="db_name_xml" value="oe_lending_staging10_231107"></parameter>


        <classes>
            <class name="Rquest.DatabaseDetails"/>
            <class name="OCL.Lending.CreditCard.DatabaseConnect.DatabaseClassIntegration"/>




            <!--      </SHIVANGI>-->
            <class name="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow"/>
            <class name="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp"/>
            <class name="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge"/>
            <class name="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar"/>
            <class name="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited"/>
            <class name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker"/>
            <class name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
            <class name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
            <class name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp"/>
            <class name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker"/>
            <class name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
            <class name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
            <class name="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode"/>
            <class name="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate"/>
            <class name="OCL.Lending.ConsumerLending.Fibe.TestFibeDistribution"/>
            <!--            </SHIVANGI>-->


            <!--            <HIMIKA>-->
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PL_PO_CPV_Journey"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandatePostOnboardingFlow"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.PLHERO_Compliance_CPV"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.PLHERO_Compliance_CPV_Address_Consent_Accepted"/>
            <class name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.PLHERO_Compliance_Current_Address_Unserviceable"/>
            <!--                        </HIMIKA>-->



            <!-- <NISHANT>-->
            <class name="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA"/>
            <class name="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP"/>
            <class name="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP"/>
            <class name="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.Miscellaneous.Postpaid_PAN_Dedupe"/>
            <class name="OCL.Lending.CreditCard.HDFC.ETB"/>
            <class name="OCL.Lending.CreditCard.HDFC.NTB"/>
            <class name="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow"/>
            <class name="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow"/>
            <class name="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow"/>
            <class name="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintRTOFlow"/>
            <!--        <NISHANT>-->


        </classes>
    </test>
    <listeners>
        <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
        <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
        <listener class-name="AfterSuite.ListenerTest"/>
    </listeners>
</suite>