<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">


<suite name="Devices - Onboarding Engine" parallel="tests" thread-count="10" preserve-order="true" verbose="30">


    <!--    <test name="Devices" preserve-order="true" thread-count="5">



            <classes>


                &lt;!&ndash;            <Jyoti>&ndash;&gt;
                <class name="OCL.KYB.EditAddressInKYB"/>
                &lt;!&ndash;            </Jyoti>&ndash;&gt;



            </classes>
        </test>-->
    <!-- <test name="Payments" preserve-order="true" thread-count="5" parallel="classes">


         <packages>

             <package name="BeforeSuite"/>
             &lt;!&ndash;    <package name="OCL.Business.AssistedMerchantOnboard.*"/>
                <package name="OCL.Business.OrganisedMerchant.*"/>
                <package name="OCL.Business.QRMerchant500K.*"/>
                <package name="OCL.Business.QRMerchantUnlimited.*"/>

        <package name="OCL.Business.UpgradeMerchantPlan.*"/>
         <package name="OCL.DIY.OnlineMerchant.*"/>
         <package name="OCL.Individual.Merchant50K.*"/>
         <package name="OCL.Individual.Merchant100K.*"/>
         <package name="OCL.Individual.ProfileUpdate.*"/>
         <package name="OCL.Individual.QrMapping.*"/>
         <package name="OCL.Individual.RevisitMerchant.*"/>
         <package name="OCL.Individual.UnifiedPaymentMerchant.*"/> &ndash;&gt;
         </packages>
     </test>-->
    <!--

    <test name="Lending" preserve-order="true" thread-count="5" parallel="classes" >

    <packages>
        <package name="OCL.ConsumerLending.*"/>
        <package name="OCL.Lending.*"/>
    </packages>

    </test>-->
    <!--
      <test name="Lending" preserve-order="true" thread-count="5" parallel="classes" >

          <packages>
               <package name="OCL.ConsumerLending.*"/>
           <package name="OCL.Lending.*"/>
       </packages>

   </test>-->


    <!--    <test name="Devices" preserve-order="true" thread-count="5" parallel="classes">-->

    <!-- Parallel execution at the test level, meaning each <test> tag will run in parallel -->
    <test name="Devices" preserve-order="true" parallel="classes" thread-count="10">
        <!--    <test name="Devices" preserve-order="true">-->

        <packages>
<!--            <package name="OCL.Business.MapEDC.*"/>-->
          <!--  <package name="OCL.Business.MapPOS.*"/>-->
            <package name="OCL.Business.UnmapEDC.*">
                <exclude name="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
            </package>
            <package name="OCL.Business.UpgradeMerchantPlan.*"/>
            <package name="OCL.Business.UpgradeEDC.*"/>
            <package name="OCL.CommonOnboarding.EDC.*">
                <exclude name="OCL.CommonOnboarding.EDC.saveOrder20"/>
            </package>
        </packages>
        <classes>
            <class name="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
                <methods>
                    <exclude name=".*"/>
                </methods>
            </class>
        </classes>
        <packages>
            <package name="OCL.DIY.CashAtPosDIY.*"/>
            <package name="OCL.DIY.EMIOfferingDIY.*"/>
            <!--  <package name="OCL.DIY.MapEdcDIY.*"/> -->
            <!--  <package name="OCL.DIY.MposDIY.*"/> -->
            <package name="OCL.DIY.BrandEmiDIY.*"/>
            <package name="OCL.BrandEMI.*"/>
            <!-- <package name="OCL.ICICI.*"/> -->
            <!-- <package name="OCL.HDFC.POS.Onboarding.*"/> -->
            <package name="OCL.PG.*"/>
            <package name="OCL.PlanManagement.*"/>
            <package name="OCL.ODS.*"/>
            <package name="OCL.IOT.*"/>
            <!-- <package name="OCL.RestApiForEdcPlans.*"/> -->
            <!--            <package name="OCL.Individual.FastagAssisted.*"/>-->
            <!--            <package name="OCL.Individual.FastagDiy.*"/>-->
            <package name="OCL.Individual.SoundBox.*"/>
            <package name="OCL.EnterpriseMerchantOnboarding.*"/>
            <package name="OCL.Subscription.*"/>
            <package name="OCL.GenericTestsForApis.*"/>
            <!--  <package name="OCL.Individual.ChannelOnboarding.*"/> -->
            <package name="OCL.Individual.PSA.*"/>
            <!--  <package name="OCL.Individual.PSA_DIY.*"/> -->
            <package name="OCL.Individual.RegisterLead.*"/>
       <!--     <package name="OCL.ManageAgent.*"/>-->
            <!--    <package name="OCL.Panel.*"/> -->
            <package name="OCL.UAD.*"/>
            <package name="OCL.KYB.*"/>
            <package name="OCL.Billing.*"/>
            <package name="ats.subpartbarcodes.*"/>
            <package name="OCL.EDCDIY.*"/>
            <package name="OCL.FSM.*"/>
            <package name="OCL.EGS.*"/>
            <package name="OCL.Business.ReplaceSubpartEDC.*"/>
            <package name="OCL.Business.DeploymentDetails.*"/>
            <package name="OCL.ATS.*"/>
            <package name="OCL.PGProfileUpdateAddAddress.*"/>
            <package name="OCL.UpdateDeviceAddress.*"/>
            <package name="OCL.CIF.*"/>
            <package name="OCL.ManageSim.*"/>
            <package name="OCL.SHOPinsurance.*"/>
            <package name="OCL.RetentionOfferEDC.*"/>
            <package name="OCL.OnboardBankV3.*"/>
            <package name="OCL.Individual.RevisitFlow"/>
            <package name="OCL.EmbeddedInsurance.*"/>
            <package name="OCL.EDCDeviceUpgradeV2.*"/>
        </packages>


    </test>

    <!--
      <test name="ATS" preserve-order="true" thread-count="5" parallel="classes">
          <packages>
           <package name="ats.*"/>
         </packages>
   </test> -->
    <!--
       <test name="Others" preserve-order="true" thread-count="5" parallel="classes">

           <packages>
               <package name="OCL.Individual.ChannelOnboarding.*"/>
               <package name="OCL.Individual.PSA.*"/>
               <package name="OCL.Individual.PSA_DIY.*"/>
               <package name="OCL.Individual.RegisterLead.*"/>
               <package name="OCL.Insurance.*"/>
               <package name="OCL.ManageAgent.*"/>
               <package name="OCL.Panel.*"/>
               <package name="OCL.GenericTestsForApis.*"/>
               <package name="OCL.CRM.*"/>

           </packages>

       </test>

            -->

    <!--   <test name="Lending" preserve-order="true" thread-count="5" parallel="classes" >
           <packages>
             <package name="OCL.Lending.*"/>
         </packages> -->


    <!--  </test>

     <test name="Devices" preserve-order="true" thread-count="5" parallel="classes">

         <packages>
             <package name="OCL.Business.MapEDC.*"/>
             <package name="OCL.Business.MapPOS.*"/>
             <package name="OCL.Business.UnmapEDC.*"/>
             <package name="OCL.DIY.CashAtPosDIY.*"/>
             <package name="OCL.DIY.EMIOfferingDIY.*"/>
             <package name="OCL.DIY.MapEdcDIY.*"/>
             <package name="OCL.DIY.MposDIY.*"/>
             <package name="OCL.Individual.FastagAssisted.*"/>
             <package name="OCL.Individual.FastagDiy.*"/>
             <package name="OCL.Individual.SoundBox.*"/>
         </packages>

     </test>

     <test name="ATS" preserve-order="true" thread-count="5" parallel="classes">
         <packages>
             <package name="ats.*"/>
         </packages>
     </test>

     <test name="Others" preserve-order="true" thread-count="5" parallel="classes">

         <packages>
             <package name="OCL.Individual.ChannelOnboarding.*"/>
             <package name="OCL.Individual.PSA.*"/>
             <package name="OCL.Individual.PSA_DIY.*"/>
             <package name="OCL.Individual.RegisterLead.*"/>
             <package name="OCL.Insurance.*"/>
             <package name="OCL.ManageAgent.*"/>
             <package name="OCL.Panel.*"/>
             <package name="OCL.GenericTestsForApis.*"/>
             <package name="OCL.CRM.*"/>

         </packages>

     </test> -->

    <listeners>
        <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
        <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
        <!--        <listener class-name="com.paytm.framework.reporting.listeners.RPTestListener"/>-->
        <listener class-name="AfterSuite.ListenerTest"/>
    </listeners>
</suite>
