<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.paytm</groupId>
    <artifactId>paytm-oe-automation-suite</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
<!--        <suiteFilePath>testng_suites/api.xml</suiteFilePath>-->
<!--        <suiteFilePath>Devices.xml</suiteFilePath>-->
        <suiteFilePath>src/test/resources/Devices.xml</suiteFilePath>


        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
<!--        <httpclient.version>4.5.3</httpclient.version>-->
        <httpclient.version>4.5.13</httpclient.version>
        <!-- <log4j.version>2.20.0</log4j.version> -->
        <java.version>1.8</java.version>
        <paytm.automation.api>2.5-SNAPSHOT</paytm.automation.api>
        <rest-assured.version>3.3.0</rest-assured.version>
        <maven.compiler.plugin.version>3.3</maven.compiler.plugin.version>
<!--        <maven.surefire.plugin.version>2.19.1</maven.surefire.plugin.version>-->
        <!-- <maven.surefire.plugin.version>2.22.2</maven.surefire.plugin.version> -->
        <maven.surefire.plugin.version>3.0.0-M5</maven.surefire.plugin.version>
        <codehaus.exec.plugin.version>1.4.0</codehaus.exec.plugin.version>
        <codehaus.aspectj.plugin.version>1.8</codehaus.aspectj.plugin.version>
        <maven.antrun.plugin.version>1.8</maven.antrun.plugin.version>
        <maven.assembly.plugin.version>2.6</maven.assembly.plugin.version>
        <maven.resources.plugin.version>2.7</maven.resources.plugin.version>
        <maven.dependency.plugin.version>2.10</maven.dependency.plugin.version>
<!--        <maven.compiler.source>19</maven.compiler.source>-->
        <maven.compiler.source>1.8</maven.compiler.source>
<!--        <maven.compiler.target>19</maven.compiler.target>  -->
        <maven.compiler.target>1.8</maven.compiler.target>

    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>
            <!-- Control log4j versions -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.17.2</version> <!-- Java 8 compatible version -->
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.17.2</version> <!-- Java 8 compatible version -->
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>2.17.2</version> <!-- Java 8 compatible version -->
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.paytm</groupId>
            <artifactId>paytm-automation-api</artifactId>
            <version>${paytm.automation.api}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jayway.jsonpath</groupId>
                    <artifactId>json-path</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.paytm.autumn</groupId>-->
        <!--            <artifactId>autumn</artifactId>-->
        <!--            <version>3.0.13-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!-- https://mvnrepository.com/artifact/net.minidev/json-smart -->
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>2.4.10</version>
        </dependency>

        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>${rest-assured.version}</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.23</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <!-- Use version from dependencyManagement -->
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version> <!-- Java 8 compatible version -->
        </dependency>



        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.46.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.33</version> <!-- Java 8 compatible version -->
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>5.3.30</version> <!-- Java 8 compatible version -->
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>5.3.30</version> <!-- Java 8 compatible version -->
        </dependency>


        <dependency>
            <groupId>org.skyscreamer</groupId>
            <artifactId>jsonassert</artifactId>
            <version>1.5.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.testng</groupId>-->
<!--            <artifactId>testng</artifactId>-->
<!--            <version>7.0.0-beta1</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

<!--        This Requies java version of atleast 11 to run on jenkins but our jenkins working on java 8.-->
<!--        <dependency>-->
<!--            <groupId>org.testng</groupId>-->
<!--            <artifactId>testng</artifactId>-->
<!--            <version>7.8.0</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>7.4.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.relevantcodes</groupId>
            <artifactId>extentreports</artifactId>
            <version>2.41.2</version>
        </dependency>
        <dependency>
            <groupId>com.github.fge</groupId>
            <artifactId>json-schema-validator</artifactId>
            <version>2.2.6</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
            <version>4.1.111.Final</version>
        </dependency>


        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>3.141.59</version>
        </dependency>

        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-server</artifactId>
            <version>3.141.59</version>
        </dependency>

        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-support</artifactId>
            <version>3.141.59</version>
        </dependency>

        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-chrome-driver</artifactId>
            <version>3.141.59</version>
        </dependency>


        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>4.1</version>
        </dependency>


        <dependency>
            <groupId>com.github.javafaker</groupId>
            <artifactId>javafaker</artifactId>
            <version>0.12</version>
        </dependency>

        <dependency>
            <groupId>com.maxmind.geoip2</groupId>
            <artifactId>geoip2</artifactId>
            <version>4.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-annotations -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.15.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.15.0</version>
        </dependency>



        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.10</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.5</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client</artifactId>
            <version>1.34.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.http-client</groupId>
            <artifactId>google-http-client-jackson2</artifactId>
            <version>1.34.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.oauth-client</groupId>
            <artifactId>google-oauth-client-jetty</artifactId>
            <version>1.36.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.apis</groupId>
            <artifactId>google-api-services-sheets</artifactId>
            <version>v4-rev493-1.23.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.apis/google-api-services-drive -->
        <dependency>
            <groupId>com.google.apis</groupId>
            <artifactId>google-api-services-drive</artifactId>
            <version>v3-rev173-1.25.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.4.0</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.10.7</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.10.7</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.10.7</version>
            <scope>runtime</scope>
        </dependency>


        <!-- https://mvnrepository.com/artifact/de.otto/hmac-auth-client -->
        <dependency>
            <groupId>de.otto</groupId>
            <artifactId>hmac-auth-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>5.1.3</version>
        </dependency>


        <!-- &lt;!&ndash; https://mvnrepository.com/artifact/org.springframework/spring-jdbc &ndash;&gt;
         <dependency>
             <groupId>org.springframework</groupId>
             <artifactId>spring-jdbc</artifactId>
             <version>5.1.9.RELEASE</version>
         </dependency>-->


        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.9</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>24.0.1</version> <!-- Using specific version instead of RELEASE -->
            <scope>compile</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.awaitility/awaitility -->
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>4.2.0</version>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.awaitility/awaitility-proxy -->
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility-proxy</artifactId>
            <version>3.1.6</version>
            <scope>test</scope>
        </dependency>


        <!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
            <!-- Use version from dependencyManagement -->
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <!-- Use version from dependencyManagement -->
        </dependency>

      <dependency>
    <groupId>commons-logging</groupId>
    <artifactId>commons-logging</artifactId>
    <!-- Use version from dependencyManagement -->
</dependency>


        <!-- Removed duplicate paytm-automation-api dependency -->


    </dependencies>







    <repositories>
        <repository>
            <id>paytm-mktpls-snapshots</id>
            <name>Paytm Mktplace Repo</name>
            <url>https://mktplaceartifacts.paytm.com/repository/paytm-mktplace-snapshots</url>
        </repository>

        <!--  <repository>
             <id>snapshots</id>
             <name>libs-snapshots</name>
             <url>http://10.143.18.41/artifactory/libs-snapshot</url>
         </repository> -->

        <repository>
            <id>snapshots</id>
            <name>libs-snapshots</name>
            <url>https://pgp-automation.paytm.in/artifactory/libs-snapshot</url>
        </repository>

    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>paytm-mktpls-snapshots</id>
            <name>Paytm MktPlace Repo</name>
            <url>https://mktplaceartifacts.paytm.com/repository/paytm-mktplace-snapshots</url>
        </snapshotRepository>


    </distributionManagement>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <fork>true</fork>
                        <compilerArgs>
                            <arg>-Xlint:all</arg>
                            <arg>-J-Xss4m</arg> <!-- Increase stack size for annotation processing -->
                        </compilerArgs>
                        <showWarnings>true</showWarnings>
                        <showDeprecation>true</showDeprecation>
                        <verbose>true</verbose>
                        <encoding>UTF-8</encoding>
                        <release>8</release>
                    </configuration>
                </plugin>
                <!--               <plugin>
                                   <groupId>org.apache.maven.plugins</groupId>
                                   <artifactId>maven-compiler-plugin</artifactId>
                                   <version>3.1</version>
                                   <configuration>
                                       <source>1.7</source>
                                       <target>1.7</target>
                                   </configuration>
                               </plugin>-->

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire.plugin.version}</version>
                    <configuration>
                        <forkCount>1</forkCount>
                        <reuseForks>false</reuseForks>
                        <!-- Removed MaxPermSize as it's not supported in Java 9+ -->
                        <argLine>-Xmx2048m -Djava.awt.headless=true</argLine>
                        <testFailureIgnore>true</testFailureIgnore>
                        <suiteXmlFiles>
                            <suiteXmlFile>${suiteFilePath}</suiteXmlFile>
                        </suiteXmlFiles>
                    </configuration>
                </plugin>


                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>1.2.1</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>test</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>


            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <fork>true</fork>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-J-Xss4m</arg>
                    </compilerArgs>
                    <showWarnings>true</showWarnings>
                    <showDeprecation>true</showDeprecation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>enforce-java</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>1.8</version>
                                </requireJavaVersion>
                                <!-- Removed enforceBytecodeVersion rule as it was causing issues -->
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven.surefire.plugin.version}</version>
                <configuration>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                    <!-- Removed MaxPermSize as it's not supported in Java 9+ -->
                    <argLine>-Xmx2048m -Djava.awt.headless=true</argLine>
                    <testFailureIgnore>true</testFailureIgnore>
                    <suiteXmlFiles>
                        <suiteXmlFile>${suiteFilePath}</suiteXmlFile>
                    </suiteXmlFiles>
                </configuration>
            </plugin>
        </plugins>
        <!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <source>9</source>-->
<!--                    <target>9</target>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
    </build>

    <profiles>
        <profile>
            <id>jenkins</id>
            <activation>
                <property>
                    <name>env.BUILD_NUMBER</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <testFailureIgnore>true</testFailureIgnore>
                            <!-- For Java 8 on Jenkins -->
                            <argLine>-Xmx2048m -Djava.awt.headless=true</argLine>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- Profile for Java 8 -->
        <profile>
            <id>java8</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- For Java 8 -->
                            <argLine>-Xmx2048m -Djava.awt.headless=true</argLine>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- Profile for Java 11+ -->
        <profile>
            <id>java11plus</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- For Java 11+ -->
                            <argLine>-Xmx2048m -Djava.awt.headless=true --add-opens java.base/java.lang=ALL-UNNAMED</argLine>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
