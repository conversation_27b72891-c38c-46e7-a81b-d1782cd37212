<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">


<suite name="payment - Onboarding Engine" parallel="tests" thread-count="10" preserve-order="true" verbose="30">


    <!-- Parallel execution at the test level, meaning each <test> tag will run in parallel -->
    <test name="payment" preserve-order="true">
        <classes>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividual"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividualRejection"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingPrivateLimited"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingPrivateLimitedRejection"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingProprietorship"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingProprietorshipRejection"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividualWithForm60"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingRekyc_KycUpdate"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingRekycKyc_UpdateAutoQC_Individual_WithPAN"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingRekycKyc_UpdateAutoQC_Prop_With_GSTN"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividualNegative"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingPrivateLimitedNegative"/>
            <class name="OCL.ManageAgentOEPanel.ManageAgent"/>
            <class name="OCL.CommonOnboarding.AdditionalDetailsCoAPI"/>
            <class name="OCL.CommonOnboarding.AudioDetailsCoAPI"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingGetallResources"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingLightWeightAPI"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingMerchantAgreementTnC"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingTnCAccept"/>
            <class name="OCL.DIY.MCO.CreateLead"/>
            <class name="OCL.DIY.MerchantLimitUpgrade.AadharNotVerifiedLimitUpgradeFlow"/>
            <class name="OCL.DIY.MerchantLimitUpgrade.FetchScreenDetail"/>
            <class name="OCL.DIY.MCO.DiyMcoCreateLeadV3"/>
            <class name="OCL.DIY.MerchantLimitUpgrade.DiyUpgrade"/>
            <class name="OCL.DIY.MerchantLimitUpgrade.DiyReKYC"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.BillingAddressUpdate"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.DisplayNameUpdate"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.BusinessNameUpdateE2E"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.RegisteredAddressUpdateE2E"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.BusinessNameUpdateE2E"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.BusinessNameUpdate"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.GstUpdate"/>
            <class name="OCL.PGProfileUpdate.BankDetailUpdate.BankDetailUpdate"/>
            <class name="OCL.PGProfileUpdate.BankDetailUpdate.BankDetailRevampV3"/>
            <class name="OCL.FseOnboardingDiy.FseDiy"/>
            <class name="OCL.FseOnboardingDiy.FseDiyPositiveFlow"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.RegisteredAddressUpdate"/>
            <class name="OCL.SFtoOE2024.BusinessLeadCreation"/>
            <class name="OCL.SFtoOE2024.SolutionLeadCreation"/>

        </classes>

    </test>


    <listeners>
        <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
        <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
        <!--        <listener class-name="com.paytm.framework.reporting.listeners.RPTestListener"/>-->
        <listener class-name="AfterSuite.ListenerTest"/>
    </listeners>
</suite>

