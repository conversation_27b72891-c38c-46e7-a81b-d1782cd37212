<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">


<suite name="Maker Checker - Workflow Engine" parallel="tests" thread-count="10" preserve-order="true" verbose="30">
    <test name="Maker-Checker" preserve-order="true" thread-count="5" parallel="classes">

    <packages>
    </packages>
        <classes>
<!--            <class name="OCL.MakerChecker.WorkflowEngine.ReviewRequestCallbackTest" />-->
<!--            <class name="OCL.MakerChecker.ReviewService.GetReviewCaseDetailsTest"/>-->
<!--            <class name="OCL.MakerChecker.ReviewService.UpdateReviewCaseStageTest"></class>-->
<!--            <class name="OCL.MakerChecker.ReviewService.FetchReviewStatusCountTest"></class>-->
<!--                <class name="OCL.MakerChecker.WorkflowEngine.CreateWorkflowTest" />-->
<!--                <class name="OCL.MakerChecker.WorkflowEngine.GetWorkflowDetailsTest" />-->
                <class name="OCL.MakerChecker.ReviewService.FetchSubGroupCheckersTest" />




        </classes>
    </test>
    <listeners>
        <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
        <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
        <listener class-name="AfterSuite.ListenerTest"/>
    </listeners>
</suite>