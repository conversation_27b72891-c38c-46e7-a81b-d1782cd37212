<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite thread-count="10" parallel="classes" name="Failed suite [Devices - Onboarding Engine]">
  <test thread-count="10" parallel="classes" name="Devices(failed)">
    <classes>
      <class name="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr">
        <methods>
          <include name="getStatusInCaseOfSuccessfullQrValidation"/>
          <include name="getStatusInCaseOfInvalidModel"/>
          <include name="agentToken"/>
          <include name="getStatusInCaseOfLeadIdISNotvalid"/>
          <include name="getStatusInCaseOfInvalidDeviceType"/>
          <include name="getStatusInCaseOfInvalidOS"/>
        </methods>
      </class> <!-- OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr -->
      <class name="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade">
        <methods>
          <include name="AgentLogin"/>
          <include name="TC_021"/>
        </methods>
      </class> <!-- OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade -->
    </classes>
  </test> <!-- Devices(failed) -->
</suite> <!-- Failed suite [Devices - Onboarding Engine] -->
