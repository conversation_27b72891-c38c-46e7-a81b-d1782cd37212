<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Failed suite [Command line suite]">
  <test thread-count="5" name="Command line test(failed)">
    <classes>
      <class name="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr">
        <methods>
          <include name="getStatusInCaseOfSuccessfullQrValidation"/>
          <include name="getStatusInCaseOfInvalidModel"/>
          <include name="agentToken"/>
          <include name="getStatusInCaseOfLeadIdISNotvalid"/>
          <include name="getStatusInCaseOfInvalidDeviceType"/>
          <include name="getStatusInCaseOfInvalidOS"/>
        </methods>
      </class> <!-- OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr -->
      <class name="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade">
        <methods>
          <include name="AgentLogin"/>
          <include name="TC_021"/>
        </methods>
      </class> <!-- OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade -->
    </classes>
  </test> <!-- Command line test(failed) -->
</suite> <!-- Failed suite [Command line suite] -->
