<html>
<head>
<title>TestNG:  Command line test</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Command line test</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>39/6/2</td>
</tr><tr>
<td>Started on:</td><td>Fri Aug 23 19:25:22 IST 2024</td>
</tr>
<tr><td>Total time:</td><td>149 seconds (149061 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021()'><b>TC_021</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without device identifier</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace686815807", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace686815807'><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:926)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1127)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType()'><b>getStatusInCaseOfInvalidDeviceType</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1107717901", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1107717901'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>4</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel()'><b>getStatusInCaseOfInvalidModel</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace299617083", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace299617083'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS()'><b>getStatusInCaseOfInvalidOS</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1581036942", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1581036942'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>4</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid()'><b>getStatusInCaseOfLeadIdISNotvalid</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [500] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2046578329", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2046578329'><pre>java.lang.AssertionError: did not expect to find [500] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation()'><b>getStatusInCaseOfSuccessfullQrValidation</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1972176213", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1972176213'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingMultiplePINSecondary()'><b>AddingMultiplePINSecondary</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Multiple PIN with Secondary details</td>
<td></td>
<td>0</td>
<td>OCL.UAD.AddUADPincode@1ad9b8d3</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingMultiplePINs()'><b>AddingMultiplePINs</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Multiple PIN with Primary details</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@1ad9b8d3</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingSinglePINSecondary()'><b>AddingSinglePINSecondary</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Single PIN with Secondary details</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@1ad9b8d3</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingSinglePINs()'><b>AddingSinglePINs</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Single PIN with Primary details</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@1ad9b8d3</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.BlankPin()'><b>BlankPin</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Blank pincode</td>
<td></td>
<td>0</td>
<td>OCL.UAD.AddUADPincode@1ad9b8d3</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.InvalidPin()'><b>InvalidPin</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Invalid pincode</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@1ad9b8d3</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_001()'><b>TC_001</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with all valid details</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_002()'><b>TC_002</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without token</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_003()'><b>TC_003</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid token</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_004()'><b>TC_004</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without param</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_005()'><b>TC_005</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with inavlid param</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_006()'><b>TC_006</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without entity type</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_007()'><b>TC_007</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid entity type</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_008()'><b>TC_008</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without user custid</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_009()'><b>TC_009</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid custid</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_010()'><b>TC_010</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without agent custid</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_011()'><b>TC_011</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid agent custid</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_012()'><b>TC_012</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without mobile number</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_013()'><b>TC_013</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid mobile number</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_014()'><b>TC_014</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid MID</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_015()'><b>TC_015</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without KYBID</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_016()'><b>TC_016</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with INVALID KYBID</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_017()'><b>TC_017</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead withOUT WORKFLOW VERSION</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_018()'><b>TC_018</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with INVALID WORKFLOW VERSION</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_019()'><b>TC_019</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without service reason</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_020()'><b>TC_020</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without version</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_022()'><b>TC_022</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without checksum</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceBindedWithDifferentMid()'><b>getStatusInCaseOfDeviceBindedWithDifferentMid</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>4</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceBindingDoesNotExist()'><b>getStatusInCaseOfDeviceBindingDoesNotExist</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceIDNotPassedInRequest()'><b>getStatusInCaseOfDeviceIDNotPassedInRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceOEM()'><b>getStatusInCaseOfInvalidDeviceOEM</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidTokenIsPassedInRequest()'><b>getStatusInCaseOfInvalidTokenIsPassedInRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIsNotPassedINRequest()'><b>getStatusInCaseOfLeadIsNotPassedINRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>4</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader()'><b>getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfTokenIsNotPassedInRequest()'><b>getStatusInCaseOfTokenIsNotPassedInRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfVersionIsNotPassedInReq()'><b>getStatusInCaseOfVersionIsNotPassedInReq</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.test()'><b>test</b><br>Test class: OCL.UAD.AddUADPincode</td>
<td></td>
<td>0</td>
<td>OCL.UAD.AddUADPincode@1ad9b8d3</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.test()'><b>test</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@4f93bf0a</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.test()'><b>test</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021()'><b>TC_021</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without device identifier</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace277861845", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace277861845'><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021()'><b>TC_021</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without device identifier</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1645088054", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1645088054'><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:926)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1127)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at TestRunner.main(TestRunner.java:24)
</pre></div></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@168ad26f</td></tr>
</table><p>
</body>
</html>