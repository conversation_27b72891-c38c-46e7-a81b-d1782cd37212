<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="197NODMB30184.local" failures="6" tests="47" name="Command line test" time="149.061" errors="0" timestamp="2024-08-23T19:27:56 IST">
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.19"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfDeviceBindedWithDifferentMid" time="4.133"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfDeviceBindingDoesNotExist" time="3.874"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfDeviceIDNotPassedInRequest" time="3.778"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidDeviceOEM" time="3.768"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidDeviceType" time="4.134">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfInvalidDeviceType -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidModel" time="3.431">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfInvalidModel -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidOS" time="4.427">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfInvalidOS -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidTokenIsPassedInRequest" time="1.84"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfLeadIdISNotvalid" time="3.328">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfLeadIdISNotvalid -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfLeadIsNotPassedINRequest" time="4.117"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader" time="3.129"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfSuccessfullQrValidation" time="3.429">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfSuccessfullQrValidation -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfTokenIsNotPassedInRequest" time="2.833"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfVersionIsNotPassedInReq" time="2.57"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_001" time="2.11"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_002" time="0.954"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_003" time="0.893"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_004" time="2.912"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_005" time="2.684"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_006" time="1.838"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_007" time="1.844"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_008" time="2.156"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_009" time="1.725"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_010" time="2.161"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_011" time="3.476"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_012" time="2.157"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_013" time="2.043"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_014" time="1.525"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_015" time="2.053"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_016" time="3.067"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_017" time="3.221"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_018" time="2.066"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_019" time="2.087"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_020" time="0.983"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_021" time="2.274">
    <skipped/>
  </testcase> <!-- TC_021 -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_021" time="2.255">
    <skipped/>
  </testcase> <!-- TC_021 -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_021" time="3.586">
    <failure type="java.lang.AssertionError" message="did not expect to find [410] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [410] but found [200]
at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
at TestRunner.main(TestRunner.java:24)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- TC_021 -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_022" time="0.919"/>
  <testcase classname="OCL.UAD.AddUADPincode" name="AddingMultiplePINs" time="1.387"/>
  <testcase classname="OCL.UAD.AddUADPincode" name="AddingSinglePINs" time="1.177"/>
  <testcase classname="OCL.UAD.AddUADPincode" name="AddingMultiplePINSecondary" time="0.978"/>
  <testcase classname="OCL.UAD.AddUADPincode" name="AddingSinglePINSecondary" time="1.104"/>
  <testcase classname="OCL.UAD.AddUADPincode" name="InvalidPin" time="1.182"/>
  <testcase classname="OCL.UAD.AddUADPincode" name="BlankPin" time="0.982"/>
</testsuite> <!-- Command line test -->
