<h2>Reporter output</h2><table><tr><td>test = [Pass]<br></td></tr>
<tr><td>test = [Pass]<br></td></tr>
<tr><td>test = [Pass]<br></td></tr>
<tr><td>TC_001 = [Pass]<br></td></tr>
<tr><td>TC_002 = [Pass]<br></td></tr>
<tr><td>TC_003 = [Pass]<br></td></tr>
<tr><td>TC_004 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfDeviceBindedWithDifferentMid = [Pass]<br></td></tr>
<tr><td>TC_005 = [Pass]<br></td></tr>
<tr><td>TC_006 = [Pass]<br></td></tr>
<tr><td>TC_007 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfDeviceBindingDoesNotExist = [Pass]<br></td></tr>
<tr><td>TC_008 = [Pass]<br></td></tr>
<tr><td>TC_009 = [Pass]<br></td></tr>
<tr><td>TC_010 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfDeviceIDNotPassedInRequest = [Pass]<br></td></tr>
<tr><td>TC_011 = [Pass]<br></td></tr>
<tr><td>TC_012 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfInvalidDeviceOEM = [Pass]<br></td></tr>
<tr><td>TC_013 = [Pass]<br></td></tr>
<tr><td>TC_014 = [Pass]<br></td></tr>
<tr><td>TC_015 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfInvalidDeviceType = [Fail]<br></td></tr>
<tr><td>TC_016 = [Pass]<br></td></tr>
<tr><td>TC_017 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfInvalidModel = [Fail]<br></td></tr>
<tr><td>TC_018 = [Pass]<br></td></tr>
<tr><td>TC_019 = [Pass]<br></td></tr>
<tr><td>TC_020 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfInvalidOS = [Fail]<br></td></tr>
<tr><td>TC_021 = [Skip]<br></td></tr>
<tr><td>TC_021 = [Skip]<br></td></tr>
<tr><td>getStatusInCaseOfInvalidTokenIsPassedInRequest = [Pass]<br></td></tr>
<tr><td>TC_021 = [Fail]<br></td></tr>
<tr><td>TC_022 = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfLeadIdISNotvalid = [Fail]<br></td></tr>
<tr><td>getStatusInCaseOfLeadIsNotPassedINRequest = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfSuccessfullQrValidation = [Fail]<br></td></tr>
<tr><td>getStatusInCaseOfTokenIsNotPassedInRequest = [Pass]<br></td></tr>
<tr><td>getStatusInCaseOfVersionIsNotPassedInReq = [Pass]<br></td></tr>
<tr><td>AddingMultiplePINs = [Pass]<br></td></tr>
<tr><td>AddingSinglePINs = [Pass]<br></td></tr>
<tr><td>AddingMultiplePINSecondary = [Pass]<br></td></tr>
<tr><td>AddingSinglePINSecondary = [Pass]<br></td></tr>
<tr><td>InvalidPin = [Pass]<br></td></tr>
<tr><td>BlankPin = [Pass]<br></td></tr>
</table>