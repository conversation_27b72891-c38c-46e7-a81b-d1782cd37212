<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Devices - Onboarding Engine</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="debd82">  <td>24/08/23 20:11:24</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="AddUADPincode.AddingMultiplePINSecondary()[pri:3, instance:OCL.UAD.AddUADPincode@6c977dcf]">AddingMultiplePINSecondary</td> 
  <td>TestNG-test=Devices-6@1667215328</td>   <td></td> </tr>
<tr bgcolor="debd82">  <td>24/08/23 20:11:20</td>   <td>-3670</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="AddUADPincode.AddingMultiplePINs()[pri:1, instance:OCL.UAD.AddUADPincode@6c977dcf]">AddingMultiplePINs</td> 
  <td>TestNG-test=Devices-4@510335184</td>   <td></td> </tr>
<tr bgcolor="debd82">  <td>24/08/23 20:11:25</td>   <td>1165</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="AddUADPincode.AddingSinglePINSecondary()[pri:4, instance:OCL.UAD.AddUADPincode@6c977dcf]">AddingSinglePINSecondary</td> 
  <td>TestNG-test=Devices-7@529346573</td>   <td></td> </tr>
<tr bgcolor="debd82">  <td>24/08/23 20:11:22</td>   <td>-1694</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="AddUADPincode.AddingSinglePINs()[pri:2, instance:OCL.UAD.AddUADPincode@6c977dcf]">AddingSinglePINs</td> 
  <td>TestNG-test=Devices-5@509793834</td>   <td></td> </tr>
<tr bgcolor="debd82">  <td>24/08/23 20:09:45</td>   <td>-98856</td> <td>&nbsp;</td><td title="&gt;&gt;AddUADPincode.AgentLogin()[pri:0, instance:OCL.UAD.AddUADPincode@6c977dcf]">&gt;&gt;AgentLogin</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@725931728</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:09:49</td>   <td>-94103</td> <td>&nbsp;</td><td title="&gt;&gt;V2EDCDeviceUpgrade.AgentLogin()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">&gt;&gt;AgentLogin</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@725931728</td>   <td></td> </tr>
<tr bgcolor="debd82">  <td>24/08/23 20:11:27</td>   <td>3714</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="AddUADPincode.BlankPin()[pri:6, instance:OCL.UAD.AddUADPincode@6c977dcf]">BlankPin</td> 
  <td>TestNG-test=Devices-9@689162738</td>   <td></td> </tr>
<tr bgcolor="debd82">  <td>24/08/23 20:11:26</td>   <td>2527</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="AddUADPincode.InvalidPin()[pri:5, instance:OCL.UAD.AddUADPincode@6c977dcf]">InvalidPin</td> 
  <td>TestNG-test=Devices-8@814652861</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:09:56</td>   <td>-87789</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_001()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_001</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:09:59</td>   <td>-84208</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_002()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_002</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:01</td>   <td>-83003</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_003()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_003</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:02</td>   <td>-81938</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_004()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_004</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:04</td>   <td>-79716</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_005()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_005</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:06</td>   <td>-77482</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_006()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_006</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:08</td>   <td>-75627</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_007()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_007</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:10</td>   <td>-73728</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_008()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_008</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:13</td>   <td>-70757</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_009()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_009</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:15</td>   <td>-68465</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_010()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_010</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:17</td>   <td>-66331</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_011()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_011</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:19</td>   <td>-64204</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_012()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_012</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:22</td>   <td>-61971</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_013()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_013</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:24</td>   <td>-59745</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_014()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_014</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:26</td>   <td>-57221</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_015()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_015</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:29</td>   <td>-54106</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_016()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_016</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:32</td>   <td>-51202</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_017()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_017</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:35</td>   <td>-49003</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_018()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_018</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:37</td>   <td>-46877</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_019()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_019</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:39</td>   <td>-44836</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_020()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_020</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:41</td>   <td>-42389</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_021()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_021</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:43</td>   <td>-40286</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_021()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_021</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:45</td>   <td>-38104</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_021()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_021</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="ce708d">  <td>24/08/23 20:10:48</td>   <td>-35109</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="V2EDCDeviceUpgrade.TC_022()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">TC_022</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:09:55</td>   <td>-88103</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:09:59</td>   <td>-84249</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:05</td>   <td>-78317</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:11</td>   <td>-72539</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:18</td>   <td>-65776</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:23</td>   <td>-60401</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:31</td>   <td>-52500</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:37</td>   <td>-47029</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:43</td>   <td>-40462</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:47</td>   <td>-36882</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:52</td>   <td>-31610</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:59</td>   <td>-24759</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:11:04</td>   <td>-19549</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:11:10</td>   <td>-13171</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:11:15</td>   <td>-8258</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestDeviceUpgradevalidateEDCQr.agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">&gt;&gt;agentToken</td> 
<td>&nbsp;</td>  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:02</td>   <td>-82050</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceBindedWithDifferentMid()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfDeviceBindedWithDifferentMid</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:07</td>   <td>-76397</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceBindingDoesNotExist()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfDeviceBindingDoesNotExist</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:14</td>   <td>-69667</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceIDNotPassedInRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfDeviceIDNotPassedInRequest</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:20</td>   <td>-63966</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceOEM()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfInvalidDeviceOEM</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:27</td>   <td>-56825</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfInvalidDeviceType</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:33</td>   <td>-50737</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfInvalidModel</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:39</td>   <td>-44906</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfInvalidOS</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:45</td>   <td>-38641</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidTokenIsPassedInRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfInvalidTokenIsPassedInRequest</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:48</td>   <td>-35144</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfLeadIdISNotvalid</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:10:55</td>   <td>-28161</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIsNotPassedINRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfLeadIsNotPassedINRequest</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:11:01</td>   <td>-22491</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:11:06</td>   <td>-17700</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfSuccessfullQrValidation</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:11:12</td>   <td>-11233</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfTokenIsNotPassedInRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfTokenIsNotPassedInRequest</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="818481">  <td>24/08/23 20:11:17</td>   <td>-6486</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfVersionIsNotPassedInReq()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">getStatusInCaseOfVersionIsNotPassedInReq</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
<tr bgcolor="f5ce82">  <td>24/08/23 20:09:55</td>   <td>-88163</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="BaseMethod.test()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]">test</td> 
  <td>TestNG-test=Devices-2@999960202</td>   <td></td> </tr>
<tr bgcolor="f5ce82">  <td>24/08/23 20:09:55</td>   <td>-88162</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="BaseMethod.test()[pri:0, instance:OCL.UAD.AddUADPincode@6c977dcf]">test</td> 
  <td>TestNG-test=Devices-3@1627148598</td>   <td></td> </tr>
<tr bgcolor="f5ce82">  <td>24/08/23 20:09:59</td>   <td>-84267</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="BaseMethod.test()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]">test</td> 
  <td>TestNG-test=Devices-1@484960473</td>   <td></td> </tr>
</table>
