<table border='1'>
<tr>
<th>Class name</th>
<th>Method name</th>
<th>Groups</th>
</tr><tr>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfSuccessfullQrValidation</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfDeviceBindingDoesNotExist</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfTokenIsNotPassedInRequest</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfDeviceIDNotPassedInRequest</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfVersionIsNotPassedInReq</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>test</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfDeviceBindedWithDifferentMid</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfLeadIdISNotvalid</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfInvalidTokenIsPassedInRequest</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfInvalidDeviceOEM</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfInvalidModel</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfInvalidDeviceType</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfLeadIsNotPassedINRequest</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>getStatusInCaseOfInvalidOS</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>agentToken</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>OCL.UAD.AddUADPincode</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>test</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>AddingMultiplePINs</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>BlankPin</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>AddingMultiplePINSecondary</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>AddingSinglePINs</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>AddingSinglePINSecondary</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>InvalidPin</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>test</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>TC_010</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_021</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_020</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_003</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_014</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_002</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_013</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_001</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_012</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_011</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_022</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_007</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_018</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_006</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_017</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_005</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_016</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_004</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_015</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_009</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_008</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_019</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
</table>
