<table border='1'>
<tr>
<th>Class name</th>
<th>Method name</th>
<th>Groups</th>
</tr><tr>
<td>OCL.CommonOnboarding.EDC.saveOrder20</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_1_saveOrdertWithAMEXDisable</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_10_cartSaveWithInvalidQuantity</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_1_saveOrderWithCorporateDisable</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_5_cartSaveWithoutLeadID</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>test</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>TC_6_cartSaveWithoutPlanId</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_4_cartSaveInvalidLeadID</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_1_saveOrder</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_9_cartSaveWithoutQuanity</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_1_saveCartWithPrepaidMDRDisable</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_2_saveCartSessiontoken</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_3_saveCartWithoutDeviceIdentifer</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_1_saveOrderWithdinersDisable</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_7_cartSaveWithInvalidAddOnsAMC</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC_8_cartSaveWithInvalidAddOnsEMIRental</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
</table>
