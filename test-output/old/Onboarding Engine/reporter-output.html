<h2>Reporter output</h2><table><tr><td>TC001_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC001_PLv3ABFL_fetchlLead = [Pass]<br></td></tr>
<tr><td>TC001_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC001_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC001_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC001_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC002_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC002_PLv3ABFL_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC002_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC002_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC002_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC002_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC003_CreatePLRenewalLead = [Pass]<br></td></tr>
<tr><td>TC003_PLv3ABFL_CreateLead = [Pass]<br></td></tr>
<tr><td>TC003_CreateCitiBankLead = [Pass]<br></td></tr>
<tr><td>TC003_CreateCitiBankLead_WithoutPassingBaseId = [Pass]<br></td></tr>
<tr><td>TC003_CreateHomeFirstLead = [Pass]<br></td></tr>
<tr><td>TC003_CreateBTDistributionPiramalLead = [Pass]<br></td></tr>
<tr><td>TC004_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC004_PLV3ABFL_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC004_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC004_CreateCitiBankLead_WithoutPassingProductId = [Pass]<br></td></tr>
<tr><td>TC004_UpdateLeadBasicDetails = [Pass]<br></td></tr>
<tr><td>TC004_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC005_UpdateBureauDataSetInSAI = [Pass]<br></td></tr>
<tr><td>TC005_PLV3ABFL_UpdateLeadOccupationDetails = [Pass]<br></td></tr>
<tr><td>TC005_LISCallbackToLoanAccepted = [Pass]<br></td></tr>
<tr><td>TC005_CreateCitiBankLead_WithoutPassingProductVersion = [Pass]<br></td></tr>
<tr><td>TC005_UpdateBureauDataSetInSAI = [Pass]<br></td></tr>
<tr><td>TC005_UpdateLeadBasicDetails = [Pass]<br></td></tr>
<tr><td>TC006_FetchCIR = [Pass]<br></td></tr>
<tr><td>TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate = [Pass]<br></td></tr>
<tr><td>TC006_LISCallbackToLoanProcessingError = [Pass]<br></td></tr>
<tr><td>TC006_CreateCitiBankLead_WithoutPassingProductType = [Pass]<br></td></tr>
<tr><td>TC006_FetchCIR = [Pass]<br></td></tr>
<tr><td>TC006_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC007_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC007_PLV3ABFL_UpdateLeadDetailsinSAI = [Pass]<br></td></tr>
<tr><td>TC007_LISCallbackToLoanDisbursedFromLoanProcessingError = [Pass]<br></td></tr>
<tr><td>TC007_CreateCitiBankLead_WithoutPassingLenderId = [Pass]<br></td></tr>
<tr><td>TC007_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC007_UpdateBureauDataSetInSAI = [Pass]<br></td></tr>
<tr><td>TC008_UpdateExistingDetailsInSAI = [Pass]<br></td></tr>
<tr><td>TC008_PLv3ABFL_FetchDataPostSAIlUpdate = [Pass]<br></td></tr>
<tr><td>TC008_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC008_CreateCitiBankLead_WithoutPassingRiskSegment = [Pass]<br></td></tr>
<tr><td>TC008_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC008_FetchCIR = [Pass]<br></td></tr>
<tr><td>TC009_PLRenewal_LoanOfferAccept = [Pass]<br></td></tr>
<tr><td>TC009_PLv3ABFL_FetchCIR = [Pass]<br></td></tr>
<tr><td>TC009_LMSDataCallback = [Pass]<br></td></tr>
<tr><td>TC009_CreateCitiBankLead_WithoutPassingFlowType = [Pass]<br></td></tr>
<tr><td>TC009_UpdateExistingDetailsInSAI = [Pass]<br></td></tr>
<tr><td>TC009_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC010_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC010_PLv3ABFL_BRE1Callback = [Pass]<br></td></tr>
<tr><td>TC010_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount = [Pass]<br></td></tr>
<tr><td>TC010_UpdateAdditionalDetails = [Pass]<br></td></tr>
<tr><td>TC010_UpdateExistingDetailsInSAI = [Pass]<br></td></tr>
<tr><td>TC011_PLv3HERO_UploadCustomerPhoto = [Pass]<br></td></tr>
<tr><td>TC011_PLv3ABFL_FetchDataPostBRE1Success = [Pass]<br></td></tr>
<tr><td>TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest = [Pass]<br></td></tr>
<tr><td>TC011_VerifyLeadStage = [Pass]<br></td></tr>
<tr><td>TC011_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC012_PLv3HERO_VerifyUploadedCustomerPhoto = [Pass]<br></td></tr>
<tr><td>TC012_PLv3ABFL_LoanOfferAccept = [Pass]<br></td></tr>
<tr><td>TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName = [Pass]<br></td></tr>
<tr><td>TC012_UploadSelfie = [Pass]<br></td></tr>
<tr><td>TC013_PLv3HERO_UploadSelfie = [Pass]<br></td></tr>
<tr><td>TC013_PLv3ABFL_FetchDataPostLoanOfferAccept = [Pass]<br></td></tr>
<tr><td>TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName = [Pass]<br></td></tr>
<tr><td>TC013_UploadCustomerPhoto = [Pass]<br></td></tr>
<tr><td>TC014_PLv3HERO_VerifyUploadedSelfie = [Pass]<br></td></tr>
<tr><td>TC014_PLV3ABFL_UpdateLeadDetailsinSAI = [Pass]<br></td></tr>
<tr><td>TC014_CreateCitiBankLead_WithoutPassingApplicationId = [Pass]<br></td></tr>
<tr><td>TC015_VerifyUploadedDocument = [Pass]<br></td></tr>
<tr><td>TC015_CKYCCallback = [Pass]<br></td></tr>
<tr><td>TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan = [Pass]<br></td></tr>
<tr><td>TC015_CreateCitiBankLead_WithoutPassingOfferURL = [Pass]<br></td></tr>
<tr><td>TC016_UpdateSAIWithLenderDetails = [Pass]<br></td></tr>
<tr><td>TC016_VerifyLeadStage = [Pass]<br></td></tr>
<tr><td>TC016_PLv3ABFL_FetchDataPostLoanOfferAccept = [Pass]<br></td></tr>
<tr><td>TC016_CreateCitiBankLead_PassingInvalidSolutionName = [Pass]<br></td></tr>
<tr><td>TC017_CKYCCallback = [Pass]<br></td></tr>
<tr><td>TC017_PLv3HERO_AdditionalIsRequiredorNot = [Pass]<br></td></tr>
<tr><td>TC017_PLv3ABFL_FetchDataPostKYCIntiated = [Pass]<br></td></tr>
<tr><td>TC017_CreateCitiBankLead_PassingInvalidChannelName = [Pass]<br></td></tr>
<tr><td>TC018_VerifyLeadStage = [Pass]<br></td></tr>
<tr><td>TC018_PLv3HERO_FetchLeadVerifyAdditionalData = [Pass]<br></td></tr>
<tr><td>TC018_PLv3HERO_UploadCustomerPhoto = [Pass]<br></td></tr>
<tr><td>TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2 = [Pass]<br></td></tr>
<tr><td>TC020_UpdateKYCNameInSAI = [Pass]<br></td></tr>
<tr><td>TC019_PLv3HERO_UpdateKYCNameInSAI = [Pass]<br></td></tr>
<tr><td>TC019_PLv3HERO_VerifyUploadedCustomerPhoto = [Pass]<br></td></tr>
<tr><td>TC019_CreateCitiBankLead_PassingInvalidEntityType = [Pass]<br></td></tr>
<tr><td>TC021_SaveBankDetails = [Fail]<br></td></tr>
<tr><td>TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI = [Pass]<br></td></tr>
<tr><td>TC020_PLv3HERO_UploadSelfie = [Pass]<br></td></tr>
<tr><td>TC020_CreateCitiBankLead = [Pass]<br></td></tr>
<tr><td>TC022_GenerateLoanAgreement = [Skip]<br></td></tr>
<tr><td>TC021_PLv3HERO_SaveBankDetails = [Pass]<br></td></tr>
<tr><td>TC021_PLv3HERO_VerifyUploadedSelfie = [Pass]<br></td></tr>
<tr><td>TC021_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC023_GenerateSanctionLetter = [Skip]<br></td></tr>
<tr><td>TC022_PLv3HERO_UpdateKYCNameInSAI = [Pass]<br></td></tr>
<tr><td>TC022_PLv3ABFL_FetchDataPostKYCIntiated = [Pass]<br></td></tr>
<tr><td>TC022_CreateLeadAgain = [Pass]<br></td></tr>
<tr><td>TC024_UpdateSAI = [Skip]<br></td></tr>
<tr><td>TC023_PLv3HERO_EmandateCallback = [Pass]<br></td></tr>
<tr><td>TC023_PLv3ABFL_FetchDataPostSelfieUploaded = [Pass]<br></td></tr>
<tr><td>TC023_LISCallbackToLoanRejection = [Pass]<br></td></tr>
<tr><td>TC025_AcceptLoanAgreement = [Skip]<br></td></tr>
<tr><td>TC024_PLv3HERO_FetchLeadPostEmandate = [Pass]<br></td></tr>
<tr><td>TC024_PLv3ABFL_SecondBRECallback = [Pass]<br></td></tr>
<tr><td>TC024_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC026_EmandateCallback = [Skip]<br></td></tr>
<tr><td>TC025_PLv3HERO_GenerateLoanAgreement = [Pass]<br></td></tr>
<tr><td>TC025_PLv3ABFL_FetchDataAfterBRE2Success = [Pass]<br></td></tr>
<tr><td>TC026_PLv3ABFL_AdditionalIsRequiredorNot = [Pass]<br></td></tr>
<tr><td>TC025_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC027_VerifyPDCCallback = [Skip]<br></td></tr>
<tr><td>TC026_PLv3HERO_GenerateSanctionLetter = [Pass]<br></td></tr>
<tr><td>TC027_PLv3ABFL_FetchLeadVerifyAdditionalData = [Pass]<br></td></tr>
<tr><td>TC026_CreateCitiBankLead = [Pass]<br></td></tr>
<tr><td>TC028_UploadSheetONPanel = [Skip]<br></td></tr>
<tr><td>TC027_PLv3HERO_SubmitApplication = [Pass]<br></td></tr>
<tr><td>TC028_PLv3ABFL_AdditionalDataCapture = [Pass]<br></td></tr>
<tr><td>TC027_LISCallbackToLoanAccepted = [Pass]<br></td></tr>
<tr><td>TC029_VerifyLISCallback = [Skip]<br></td></tr>
<tr><td>TC028_PLv3HERO_FetchLeadPostSubmitApplication = [Pass]<br></td></tr>
<tr><td>TC029_PLv3ABFL_BRE3Success = [Pass]<br></td></tr>
<tr><td>TC028_LISCallbackToLoanRejection = [Pass]<br></td></tr>
<tr><td>TC030_LMSDataCallback = [Skip]<br></td></tr>
<tr><td>TC029_PLv3HERO_PDCCallback = [Fail]<br></td></tr>
<tr><td>TC030_PLv3ABFL_UpdateKYCNameInSAI = [Pass]<br></td></tr>
<tr><td>TC029_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC031_FetchLeadAllData = [Skip]<br></td></tr>
<tr><td>TC030_PLv3HERO_FetchLeadPostPDCCallback = [Skip]<br></td></tr>
<tr><td>TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI = [Pass]<br></td></tr>
<tr><td>TC030_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC031PLv3HERO_SubmitApplicationLMSApprovedCallback = [Skip]<br></td></tr>
<tr><td>TC032_PLv3ABFL_SaveBankDetails = [Pass]<br></td></tr>
<tr><td>TC031_CreateCitiBankLead = [Pass]<br></td></tr>
<tr><td>TC032_FetchLeadAllData = [Skip]<br></td></tr>
<tr><td>TC033_PLv3ABFL_FetchLeadPostBankVerification = [Pass]<br></td></tr>
<tr><td>TC032_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC034_PLv3ABFL_EmandateCallback = [Pass]<br></td></tr>
<tr><td>TC033_LISCallbackToLoanAccepted = [Pass]<br></td></tr>
<tr><td>TC035_PLv3ABFL_FetchLeadPostEmandate = [Pass]<br></td></tr>
<tr><td>TC034_LISCallbackToLoanProcessingError = [Pass]<br></td></tr>
<tr><td>TC036_PLv3ABFL_GenerateLoanAgreement = [Pass]<br></td></tr>
<tr><td>TC035_LISCallbackToLoanRejection = [Pass]<br></td></tr>
<tr><td>TC037_PLv3ABFL_GenerateSanctionLetter = [Pass]<br></td></tr>
<tr><td>TC036_FetchLeadDeatils = [Pass]<br></td></tr>
<tr><td>TC038_PLv3ABFL_SubmitApplication = [Pass]<br></td></tr>
<tr><td>TC037_DeleteExistingLead = [Pass]<br></td></tr>
<tr><td>TC039_PLv3ABFL_FetchLeadPostSubmitApplication = [Pass]<br></td></tr>
<tr><td>TC038_CreateCitiBankLead = [Pass]<br></td></tr>
<tr><td>TC040_PLv3ABFL_PDCCallback = [Pass]<br></td></tr>
<tr><td>TC039_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC041_PLv3ABFL_FetchLeadPostPDCCallback = [Pass]<br></td></tr>
<tr><td>TC040_LISCallbackWithInvalidSolutionName = [Pass]<br></td></tr>
<tr><td>TC042PLV3HERO_SubmitApplicationLMSApprovedCallback = [Fail]<br></td></tr>
<tr><td>TC041_LISCallbackWithInvalidLeadId = [Pass]<br></td></tr>
<tr><td>TC043_FetchLeadAllData = [Skip]<br></td></tr>
<tr><td>TC043_LISCallbackWithInvalidEntityType = [Pass]<br></td></tr>
<tr><td>TC044_LISCallbackToLoanAccepted = [Pass]<br></td></tr>
<tr><td>TC045_LISCallbackToLoanDisbursedFromLoanAccepted = [Pass]<br></td></tr>
<tr><td>TC046_LISCallbackWithInvalidWorkflowOperation = [Pass]<br></td></tr>
<tr><td>TC047_FetchLeadAllData = [Pass]<br></td></tr>
<tr><td>TC048_LMSDataCallbackWithInvalidSolutionName = [Pass]<br></td></tr>
<tr><td>TC049_LMSDataCallbackWithInvalidLeadId = [Pass]<br></td></tr>
<tr><td>TC050_LMSDataCallbackWithInvalidChannel = [Pass]<br></td></tr>
<tr><td>TC051_LMSDataCallbackWithInvalidEntityType = [Pass]<br></td></tr>
<tr><td>TC052_LMSDataCallback = [Pass]<br></td></tr>
<tr><td>TC053_FetchLeadAllData = [Pass]<br></td></tr>
</table>