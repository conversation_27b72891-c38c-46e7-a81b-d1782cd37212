<h2>Groups used for this test run</h2><table border="1">
<tr> <td align="center"><b>Group name</b></td><td align="center"><b>Methods</b></td></tr><tr><td>Regression</td><td>TestCitiBankRegressionFlow.TC035_LISCallbackToLoanRejection()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankFlow.TC010_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>TestCitiBankRegressionFlow.TC021_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC026_EmandateCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestCitiBankRegressionFlow.TC020_CreateCitiBankLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC022_PLv3ABFL_FetchDataPostKYCIntiated()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC050_LMSDataCallbackWithInvalidChannel()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC003_PLv3ABFL_CreateLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestMCAFullertonV3Workflow.TC009_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC005_PLV3ABFL_UpdateLeadOccupationDetails()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC033_LISCallbackToLoanAccepted()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC026_PLv3ABFL_AdditionalIsRequiredorNot()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC014_CreateCitiBankLead_WithoutPassingApplicationId()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankFlow.TC007_LISCallbackToLoanDisbursedFromLoanProcessingError()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>TestPLABFLOneClick.TC037_PLv3ABFL_GenerateSanctionLetter()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>PersonalLoanRenewal.TC015_CKYCCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC020_PLv3HERO_UploadSelfie()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC012_PLv3ABFL_LoanOfferAccept()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestMCAFullertonV3Workflow.TC030_LMSDataCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>PersonalLoanRenewal.TC017_PLv3HERO_AdditionalIsRequiredorNot()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestPLABFLOneClick.TC010_PLv3ABFL_BRE1Callback()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC033_PLv3ABFL_FetchLeadPostBankVerification()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestMCAFullertonV3Workflow.TC006_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestCitiBankFlow.TC002_DeleteExistingLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>PersonalLoanRenewal.TC024_PLv3HERO_FetchLeadPostEmandate()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>PersonalLoanRenewal.TC025_PLv3HERO_GenerateLoanAgreement()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>PersonalLoanRenewal.TC011_PLv3HERO_UploadCustomerPhoto()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>PersonalLoanRenewal.TC003_CreatePLRenewalLead()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestMCAFullertonV3Workflow.TC025_AcceptLoanAgreement()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC024_PLv3ABFL_SecondBRECallback()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC006_CreateCitiBankLead_WithoutPassingProductType()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC034_PLv3ABFL_EmandateCallback()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC028_PLv3ABFL_AdditionalDataCapture()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankFlow.TC001_FetchLeadDeatils()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>TestPLABFLOneClick.TC042PLV3HERO_SubmitApplicationLMSApprovedCallback()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>PersonalLoanRenewal.TC018_PLv3HERO_FetchLeadVerifyAdditionalData()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestMCAFullertonV3Workflow.TC022_GenerateLoanAgreement()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>HomeFirst.TC005_UpdateBureauDataSetInSAI()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>PersonalLoanRenewal.TC008_UpdateExistingDetailsInSAI()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestMCAFullertonV3Workflow.TC020_UpdateKYCNameInSAI()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC018_PLv3HERO_UploadCustomerPhoto()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC039_PLv3ABFL_FetchLeadPostSubmitApplication()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>PersonalLoanRenewal.TC021_PLv3HERO_SaveBankDetails()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestPLABFLOneClick.TC040_PLv3ABFL_PDCCallback()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC027_LISCallbackToLoanAccepted()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC029_FetchLeadDeatils()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC028_UploadSheetONPanel()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC007_PLV3ABFL_UpdateLeadDetailsinSAI()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC053_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC018_VerifyLeadStage()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestMCAFullertonV3Workflow.TC031_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>PersonalLoanRenewal.TC026_PLv3HERO_GenerateSanctionLetter()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC049_LMSDataCallbackWithInvalidLeadId()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC038_PLv3ABFL_SubmitApplication()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankFlow.TC004_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>TestMCAFullertonV3Workflow.TC012_UploadSelfie()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC011_PLv3ABFL_FetchDataPostBRE1Success()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC004_PLV3ABFL_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC007_CreateCitiBankLead_WithoutPassingLenderId()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC025_PLv3ABFL_FetchDataAfterBRE2Success()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>HomeFirst.TC010_UpdateAdditionalDetails()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestCitiBankRegressionFlow.TC051_LMSDataCallbackWithInvalidEntityType()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC023_PLv3ABFL_FetchDataPostSelfieUploaded()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC009_PLv3ABFL_FetchCIR()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>HomeFirst.TC004_UpdateLeadBasicDetails()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestPLABFLOneClick.TC043_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC029_PLv3ABFL_BRE3Success()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC021_PLv3HERO_VerifyUploadedSelfie()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>PersonalLoanRenewal.TC009_PLRenewal_LoanOfferAccept()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestMCAFullertonV3Workflow.TC013_UploadCustomerPhoto()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC008_PLv3ABFL_FetchDataPostSAIlUpdate()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestPLABFLOneClick.TC032_PLv3ABFL_SaveBankDetails()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>PersonalLoanRenewal.TC030_PLv3HERO_FetchLeadPostPDCCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestMCAFullertonV3Workflow.TC023_GenerateSanctionLetter()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestMCAFullertonV3Workflow.TC015_VerifyUploadedDocument()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC019_PLv3HERO_VerifyUploadedCustomerPhoto()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestMCAFullertonV3Workflow.TC011_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>PersonalLoanRenewal.TC023_PLv3HERO_EmandateCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankFlow.TC008_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>TestCitiBankRegressionFlow.TC017_CreateCitiBankLead_PassingInvalidChannelName()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>PersonalLoanRenewal.TC022_PLv3HERO_UpdateKYCNameInSAI()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>PersonalLoanRenewal.TC027_PLv3HERO_SubmitApplication()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestPLABFLOneClick.TC041_PLv3ABFL_FetchLeadPostPDCCallback()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC009_CreateCitiBankLead_WithoutPassingFlowType()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>HomeFirst.TC001_FetchLeadDeatils()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestMCAFullertonV3Workflow.TC004_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestCitiBankRegressionFlow.TC005_CreateCitiBankLead_WithoutPassingProductVersion()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC022_CreateLeadAgain()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC034_LISCallbackToLoanProcessingError()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC047_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>PersonalLoanRenewal.TC010_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestPLABFLOneClick.TC027_PLv3ABFL_FetchLeadVerifyAdditionalData()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC026_CreateCitiBankLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC023_LISCallbackToLoanRejection()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC036_FetchLeadDeatils()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC046_LISCallbackWithInvalidWorkflowOperation()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC017_PLv3ABFL_FetchDataPostKYCIntiated()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>HomeFirst.TC009_UpdateExistingDetailsInSAI()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestPLABFLOneClick.TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC008_CreateCitiBankLead_WithoutPassingRiskSegment()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>PersonalLoanRenewal.TC012_PLv3HERO_VerifyUploadedCustomerPhoto()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC031_CreateCitiBankLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC035_PLv3ABFL_FetchLeadPostEmandate()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankFlow.TC005_LISCallbackToLoanAccepted()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>TestCitiBankRegressionFlow.TC039_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC043_LISCallbackWithInvalidEntityType()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>HomeFirst.TC002_DeleteExistingLead()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestCitiBankRegressionFlow.TC025_DeleteExistingLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankFlow.TC006_LISCallbackToLoanProcessingError()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>TestMCAFullertonV3Workflow.TC007_UpdateBureauDataSetInSAI()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestMCAFullertonV3Workflow.TC027_VerifyPDCCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC013_PLv3ABFL_FetchDataPostLoanOfferAccept()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>PersonalLoanRenewal.TC002_DeleteExistingLead()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC001_FetchLeadDeatils()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>PersonalLoanRenewal.TC014_PLv3HERO_VerifyUploadedSelfie()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>PersonalLoanRenewal.TC016_VerifyLeadStage()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestMCAFullertonV3Workflow.TC003_CreateBTDistributionPiramalLead()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC030_PLv3ABFL_UpdateKYCNameInSAI()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestMCAFullertonV3Workflow.TC010_UpdateExistingDetailsInSAI()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestPLABFLOneClick.TC036_PLv3ABFL_GenerateLoanAgreement()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>PersonalLoanRenewal.TC001_FetchLeadDeatils()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC002_DeleteExistingLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>HomeFirst.TC003_CreateHomeFirstLead()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>PersonalLoanRenewal.TC031PLv3HERO_SubmitApplicationLMSApprovedCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>PersonalLoanRenewal.TC013_PLv3HERO_UploadSelfie()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC048_LMSDataCallbackWithInvalidSolutionName()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC014_PLV3ABFL_UpdateLeadDetailsinSAI()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC015_CreateCitiBankLead_WithoutPassingOfferURL()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC016_UpdateSAIWithLenderDetails()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>PersonalLoanRenewal.TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC037_DeleteExistingLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC017_CKYCCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>PersonalLoanRenewal.TC004_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>PersonalLoanRenewal.TC028_PLv3HERO_FetchLeadPostSubmitApplication()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC024_FetchLeadDeatils()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>PersonalLoanRenewal.TC019_PLv3HERO_UpdateKYCNameInSAI()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC028_LISCallbackToLoanRejection()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC005_UpdateLeadBasicDetails()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestMCAFullertonV3Workflow.TC029_VerifyLISCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestCitiBankRegressionFlow.TC044_LISCallbackToLoanAccepted()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>HomeFirst.TC008_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestCitiBankRegressionFlow.TC019_CreateCitiBankLead_PassingInvalidEntityType()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>HomeFirst.TC011_VerifyLeadStage()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestCitiBankFlow.TC003_CreateCitiBankLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>PersonalLoanRenewal.TC029_PLv3HERO_PDCCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>HomeFirst.TC006_FetchCIR()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestCitiBankRegressionFlow.TC040_LISCallbackWithInvalidSolutionName()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>HomeFirst.TC007_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.HomeFirst@aaee2a2]<br/>TestCitiBankRegressionFlow.TC003_CreateCitiBankLead_WithoutPassingBaseId()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC002_PLv3ABFL_DeleteExistingLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankRegressionFlow.TC030_DeleteExistingLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC024_UpdateSAI()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestCitiBankRegressionFlow.TC004_CreateCitiBankLead_WithoutPassingProductId()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC032_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC008_FetchCIR()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestCitiBankRegressionFlow.TC038_CreateCitiBankLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC002_DeleteExistingLead()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>PersonalLoanRenewal.TC007_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC045_LISCallbackToLoanDisbursedFromLoanAccepted()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC001_FetchLeadDeatils()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>TestCitiBankRegressionFlow.TC041_LISCallbackWithInvalidLeadId()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestCitiBankRegressionFlow.TC052_LMSDataCallback()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestMCAFullertonV3Workflow.TC021_SaveBankDetails()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb]<br/>PersonalLoanRenewal.TC006_FetchCIR()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestCitiBankRegressionFlow.TC016_CreateCitiBankLead_PassingInvalidSolutionName()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6]<br/>TestPLABFLOneClick.TC016_PLv3ABFL_FetchDataPostLoanOfferAccept()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/>TestCitiBankFlow.TC009_LMSDataCallback()[pri:0, instance:OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf]<br/>PersonalLoanRenewal.TC005_UpdateBureauDataSetInSAI()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>PersonalLoanRenewal.TC032_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6]<br/>TestPLABFLOneClick.TC001_PLv3ABFL_fetchlLead()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165]<br/></td></tr>
</table>
