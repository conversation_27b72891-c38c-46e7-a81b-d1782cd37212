<table border='1'>
<tr>
<th>Class name</th>
<th>Method name</th>
<th>Groups</th>
</tr><tr>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC030_PLv3HERO_FetchLeadPostPDCCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC016_VerifyLeadStage</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC018_PLv3HERO_FetchLeadVerifyAdditionalData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC008_UpdateExistingDetailsInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC024_PLv3HERO_FetchLeadPostEmandate</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC031PLv3HERO_SubmitApplicationLMSApprovedCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC001_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC022_PLv3HERO_UpdateKYCNameInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC027_PLv3HERO_SubmitApplication</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC023_PLv3HERO_EmandateCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC006_FetchCIR</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC032_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC021_PLv3HERO_SaveBankDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC005_UpdateBureauDataSetInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC009_PLRenewal_LoanOfferAccept</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC003_CreatePLRenewalLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC025_PLv3HERO_GenerateLoanAgreement</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC014_PLv3HERO_VerifyUploadedSelfie</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC002_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC007_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC011_PLv3HERO_UploadCustomerPhoto</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC029_PLv3HERO_PDCCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC017_PLv3HERO_AdditionalIsRequiredorNot</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC012_PLv3HERO_VerifyUploadedCustomerPhoto</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC015_CKYCCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC004_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC026_PLv3HERO_GenerateSanctionLetter</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC010_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC028_PLv3HERO_FetchLeadPostSubmitApplication</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC013_PLv3HERO_UploadSelfie</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC019_PLv3HERO_UpdateKYCNameInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>intitializeInputData</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>OCL.Lending.BusinessLending.HomeFirst</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC009_UpdateExistingDetailsInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC003_CreateHomeFirstLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC001_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC010_UpdateAdditionalDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC011_VerifyLeadStage</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC006_FetchCIR</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC002_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC008_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC004_UpdateLeadBasicDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC007_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC005_UpdateBureauDataSetInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>intitializeInputData</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC006_LISCallbackToLoanProcessingError</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC004_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC010_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC001_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC005_LISCallbackToLoanAccepted</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC007_LISCallbackToLoanDisbursedFromLoanProcessingError</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC003_CreateCitiBankLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC002_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC008_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC009_LMSDataCallback</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>intitializeInputData</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC021_SaveBankDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC011_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC020_UpdateKYCNameInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC022_GenerateLoanAgreement</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC010_UpdateExistingDetailsInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC001_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC003_CreateBTDistributionPiramalLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC015_VerifyUploadedDocument</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC028_UploadSheetONPanel</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC016_UpdateSAIWithLenderDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC024_UpdateSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC007_UpdateBureauDataSetInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC013_UploadCustomerPhoto</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC023_GenerateSanctionLetter</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC025_AcceptLoanAgreement</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC008_FetchCIR</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC002_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC027_VerifyPDCCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC030_LMSDataCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC006_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC004_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC012_UploadSelfie</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC031_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC018_VerifyLeadStage</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC026_EmandateCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC017_CKYCCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC005_UpdateLeadBasicDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC009_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC029_VerifyLISCallback</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>intitializeInputData</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC014_PLV3ABFL_UpdateLeadDetailsinSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC039_PLv3ABFL_FetchLeadPostSubmitApplication</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC001_PLv3ABFL_fetchlLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC028_PLv3ABFL_AdditionalDataCapture</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC007_PLV3ABFL_UpdateLeadDetailsinSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC040_PLv3ABFL_PDCCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC030_PLv3ABFL_UpdateKYCNameInSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC042PLV3HERO_SubmitApplicationLMSApprovedCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC019_PLv3HERO_VerifyUploadedCustomerPhoto</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC018_PLv3HERO_UploadCustomerPhoto</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC036_PLv3ABFL_GenerateLoanAgreement</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC034_PLv3ABFL_EmandateCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC032_PLv3ABFL_SaveBankDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC024_PLv3ABFL_SecondBRECallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC013_PLv3ABFL_FetchDataPostLoanOfferAccept</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC008_PLv3ABFL_FetchDataPostSAIlUpdate</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC016_PLv3ABFL_FetchDataPostLoanOfferAccept</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC033_PLv3ABFL_FetchLeadPostBankVerification</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC043_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC009_PLv3ABFL_FetchCIR</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC029_PLv3ABFL_BRE3Success</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC002_PLv3ABFL_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC017_PLv3ABFL_FetchDataPostKYCIntiated</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC020_PLv3HERO_UploadSelfie</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC023_PLv3ABFL_FetchDataPostSelfieUploaded</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC021_PLv3HERO_VerifyUploadedSelfie</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC025_PLv3ABFL_FetchDataAfterBRE2Success</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC010_PLv3ABFL_BRE1Callback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC012_PLv3ABFL_LoanOfferAccept</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC004_PLV3ABFL_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC022_PLv3ABFL_FetchDataPostKYCIntiated</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC005_PLV3ABFL_UpdateLeadOccupationDetails</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC026_PLv3ABFL_AdditionalIsRequiredorNot</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC003_PLv3ABFL_CreateLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC011_PLv3ABFL_FetchDataPostBRE1Success</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC038_PLv3ABFL_SubmitApplication</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC037_PLv3ABFL_GenerateSanctionLetter</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC027_PLv3ABFL_FetchLeadVerifyAdditionalData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC041_PLv3ABFL_FetchLeadPostPDCCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC035_PLv3ABFL_FetchLeadPostEmandate</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>intitializeInputData</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC045_LISCallbackToLoanDisbursedFromLoanAccepted</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC038_CreateCitiBankLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC030_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC032_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC004_CreateCitiBankLead_WithoutPassingProductId</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC017_CreateCitiBankLead_PassingInvalidChannelName</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC016_CreateCitiBankLead_PassingInvalidSolutionName</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC052_LMSDataCallback</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC041_LISCallbackWithInvalidLeadId</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC044_LISCallbackToLoanAccepted</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC028_LISCallbackToLoanRejection</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC019_CreateCitiBankLead_PassingInvalidEntityType</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC049_LMSDataCallbackWithInvalidLeadId</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC037_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC024_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC053_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC003_CreateCitiBankLead_WithoutPassingBaseId</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC040_LISCallbackWithInvalidSolutionName</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC051_LMSDataCallbackWithInvalidEntityType</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC007_CreateCitiBankLead_WithoutPassingLenderId</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC006_CreateCitiBankLead_WithoutPassingProductType</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC001_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC025_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC048_LMSDataCallbackWithInvalidSolutionName</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC027_LISCallbackToLoanAccepted</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC015_CreateCitiBankLead_WithoutPassingOfferURL</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC029_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC002_DeleteExistingLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC033_LISCallbackToLoanAccepted</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC050_LMSDataCallbackWithInvalidChannel</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC026_CreateCitiBankLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC023_LISCallbackToLoanRejection</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC005_CreateCitiBankLead_WithoutPassingProductVersion</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC014_CreateCitiBankLead_WithoutPassingApplicationId</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC035_LISCallbackToLoanRejection</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC009_CreateCitiBankLead_WithoutPassingFlowType</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC021_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC020_CreateCitiBankLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC047_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC022_CreateLeadAgain</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC034_LISCallbackToLoanProcessingError</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC031_CreateCitiBankLead</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC008_CreateCitiBankLead_WithoutPassingRiskSegment</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC043_LISCallbackWithInvalidEntityType</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC046_LISCallbackWithInvalidWorkflowOperation</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC039_FetchLeadAllData</td>
<td>Regression </td>
</tr>
<tr>
<td>&nbsp;</td>
<td>TC036_FetchLeadDeatils</td>
<td>Regression </td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>intitializeInputData</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
</table>
