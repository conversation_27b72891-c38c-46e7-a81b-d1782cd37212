<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="197NODMB24984" failures="29" tests="976" name="Lending" time="1018.243" errors="0" timestamp="2023-09-28T18:25:25 IST">
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.258"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC001_PLv3ABFL_fetchlLead" time="1.193"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.003"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC001_PLv3_HERO_TOPUP_fetchlLead" time="1.849"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC002_PLv3ABFL_DeleteExistingLead" time="0.662"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC002_PLv3_HERO_TOPUP_DeleteExistingLead" time="0.867"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC003_PLv3ABFL_CreateLead" time="2.13">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_CREATED] but found [OFFER_LINKING_IN_PROGRESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_CREATED] but found [OFFER_LINKING_IN_PROGRESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge.TC003_PLv3ABFL_CreateLead(PLv3_ABFL_LP_BD_OD_Merge.java:212)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC003_PLv3ABFL_CreateLead -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC003_PLv3_HERO_TOPUP_CreateLead" time="1.185"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC004_PLV3ABFL_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC004_PLV3ABFL_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC004_FetchLeadAllData" time="24.642">
    <failure type="java.lang.AssertionError" message="did not expect to find [BRE2_IN_PROGRESS] but found [LENDING_LIS_SUBMIT_APPLICATION_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE2_IN_PROGRESS] but found [LENDING_LIS_SUBMIT_APPLICATION_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback.TC004_FetchLeadAllData(PLHEROTopupCIRMinimalJourney.java:853)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC004_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC005_PLv3ABFL_FetchDataPostBRE1Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC005_PLv3ABFL_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC05_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC05_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC006_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC006_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC06_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC06_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC007_PLv3ABFL_LoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC007_PLv3ABFL_LoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC07_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC07_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC008_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC008_PLv3ABFL_FetchDataPostLoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC08_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC08_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC009_PLv3ABFL_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_PLv3ABFL_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC09_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC09_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC010_PLv3ABFL_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_PLv3ABFL_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC010_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC011_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney$PL_Emandate_Dupliate_callback" name="TC011_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC012_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC013_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC014_PLv3ABFL_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC015_PLv3ABFL_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PLv3ABFL_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC016_PLv3ABFL_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_PLv3ABFL_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC017_PLv3ABFL_AdditionalIsRequiredorNot" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_PLv3ABFL_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC018_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC019_PLv3ABFL_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC020_PLv3ABFL_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC021_PLv3ABFL_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC022_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC023_PLv3ABFL_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_PLv3ABFL_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC024_PLv3ABFL_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_PLv3ABFL_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC025_PLv3ABFL_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_PLv3ABFL_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC026_PLv3ABFL_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC027_PLv3ABFL_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_PLv3ABFL_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC028_PLv3ABFL_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_PLv3ABFL_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC029_PLv3ABFL_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_PLv3ABFL_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_BD_OD_Merge" name="TC030_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_PLv3ABFL_FetchLeadPostSubmitApplication -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.011"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_fetchLead" time="0.623"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_ResetLead" time="0.588"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_CreateLead" time="0.779"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_FetchLeadAllData" time="1.647"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_UpdateLeadOccupationDetails" time="1.243"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_FetchDataPostOccupationDetailUpdate" time="1.457"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_UpdateLeadDetailsinSAI" time="0.688"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_FetchDataPostSAIlUpdate" time="0.702"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_FetchCIR" time="1.2"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_BRE1Callback" time="13.058"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_FetchDataPostBRE1Success" time="0.733"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HEROKYC_UpdateLeadDetailsinSAI" time="0.572"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_LoanOfferAccept" time="0.975"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3Hero_FetchDataPostLoanOfferAccept" time="0.835"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_UploadSelfiePhoto" time="3.464"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_VerifyUploadedCSelfiePhoto" time="0.995"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HEROKYC_InitiateKYCSearchByPAN" time="0.795"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchDataPostKYCInitiated" time="3.514">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOCATION_REQUIRED] but found [SECOND_BRE_INITIATED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOCATION_REQUIRED] but found [SECOND_BRE_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP.PLv3HERO_FetchDataPostKYCInitiated(PLHerousingSBP.java:859)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PLv3HERO_FetchDataPostKYCInitiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchDataPostLocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchDataPostLocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchLeadPostSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_PDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_PDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchLeadPostPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchLeadPostPDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_SubmitApplicationLMSApprovedCallback" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_SubmitApplicationLMSApprovedCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHerousingSBP" name="PLv3HERO_FetchLeadPostLMSCallback" time="0.0">
    <skipped/>
  </testcase> <!-- PLv3HERO_FetchLeadPostLMSCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.013"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC01_PLDistribution_fetchLead" time="0.777"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC02_PLDistribution_ResetLead" time="0.689"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC03_PLDistribution_CreateLead" time="0.777"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC04_PLDistribution_FetchLeadAllData" time="1.283"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC05_PLDistribution_UpdateLeadOccupationDetails" time="1.212"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC06_PLDistribution_FetchDataPostOccupationDetailUpdate" time="1.09"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC07_PLDistribution_UpdateLeadDetailsinSAI" time="0.684"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC08_PLDistribution_FetchDataPostSAIlUpdate" time="0.659"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC09_PLDistribution_FetchCIR" time="0.828"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC10_PLDistribution_BRE1Callback" time="11.511"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC11_PLDistribution_FetchDataPostBRE1Success" time="0.549"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC12_PLDistribution_UpdateLeadDetailsinSAI" time="0.565"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC13_PLDistribution_FetchDataPostLeadUpdate" time="1.604"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC14_PLDistribution_LoanOfferAccept" time="1.032"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC15_PLDistribution_FetchDataPostLoanOfferAccept" time="0.902"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC16_PLDistribution_UploadSelfiePhoto" time="3.404"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC17_PLDistribution_VerifyUploadedCSelfiePhoto" time="0.558"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC18_PLDistribution_InitiateKYCSearchByPAN" time="0.668"/>
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC19_PLDistribution_FetchDataPostKYCInitiated" time="4.933">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOCATION_REQUIRED] but found [SECOND_BRE_INITIATED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOCATION_REQUIRED] but found [SECOND_BRE_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS.TC19_PLDistribution_FetchDataPostKYCInitiated(PLHeroDistribution_ServivibilityinHTS.java:872)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC19_PLDistribution_FetchDataPostKYCInitiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC20_PLDistribution_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC20_PLDistribution_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC21_PLDistribution_FetchDataPostLocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC21_PLDistribution_FetchDataPostLocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC22_PLDistribution_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC22_PLDistribution_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC23_PLDistribution_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC23_PLDistribution_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC24_PLDistribution_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC24_PLDistribution_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC25_PLDistribution_FetchLeadAdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC25_PLDistribution_FetchLeadAdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC26_PLDistribution_RetryAdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC26_PLDistribution_RetryAdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC27_PLDistribution_FetchLeadAfterRetryAdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC27_PLDistribution_FetchLeadAfterRetryAdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC28_PLDistribution_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC28_PLDistribution_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC29_PLDistribution_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC29_PLDistribution_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC30_PLDistribution_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC30_PLDistribution_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC31_PLDistribution_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC31_PLDistribution_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC32_PLDistribution_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC32_PLDistribution_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC33_PLDistribution_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC33_PLDistribution_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC34_PLDistribution_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC34_PLDistribution_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC35_PLDistribution_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC35_PLDistribution_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC36_PLDistribution_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC36_PLDistribution_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC37_PLDistribution_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC37_PLDistribution_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC38_PLDistribution_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC38_PLDistribution_FetchLeadPostSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC39_PLDistribution_PDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC39_PLDistribution_PDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC40_PLDistribution_FetchLeadPostPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC40_PLDistribution_FetchLeadPostPDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC41_PLDistribution_SubmitApplicationLMSApprovedCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC41_PLDistribution_SubmitApplicationLMSApprovedCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PLHero.PLHeroDistribution_ServivibilityinHTS" name="TC42_PLDistribution_FetchLeadPostLMSCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC42_PLDistribution_FetchLeadPostLMSCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.01"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC001_FetchLeadDeatils" time="0.676"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC002_DeleteExistingLead" time="0.66"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC003_CreateFullertonLead" time="1.705"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC004_FetchLeadAllData" time="1.632"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC005_UpdateLeadBasicDetails" time="2.263"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC006_FetchLeadAllData" time="1.41"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC007_UpdateBureauDataSetInSAI" time="0.612"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC008_FetchCIR" time="10.538">
    <failure type="java.lang.AssertionError" message="did not expect to find [BUREAU_SUCCESS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BUREAU_SUCCESS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow.TC008_FetchCIR(TestMCAFullertonV3Workflow.java:527)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC010_UpdateExistingDetailsInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_UpdateExistingDetailsInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC011_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC012_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC013_uploadCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC014_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC015_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC016_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC017_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC018_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC019_MCA_BRE2Requested" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_MCA_BRE2Requested -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC020_MCA_Fullerton_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_MCA_Fullerton_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC021_FetchLead_AfterBRE2" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC024_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC025_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC026_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC027_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC028_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC029_UpdateActualPanInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC030_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC031_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC032_VerifyPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC033_UploadSheetONPanel" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC034_VerifyLISCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_VerifyLISCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC035_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC036_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.004"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC001_FetchLeadDeatils" time="0.625"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC002_DeleteExistingLead" time="0.583"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC003_CreateFullertonLead" time="1.136"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC004_FetchLeadAllData" time="1.155"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC005_UpdateLeadBasicDetails" time="2.13"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC006_FetchLeadAllData" time="1.988"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC007_UpdateBureauDataSetInSAI" time="0.962"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC008_FetchCIR" time="0.959"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC009_FetchLeadAllData" time="10.331"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC010_UpdateExistingDetailsInSAI" time="0.431"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC011_FetchLeadAllData" time="0.4"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC012_LeadDataUpdateForKYC_InSAI" time="0.42"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC013_uploadCustomerPhoto" time="2.804"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC014_VerifyUploadedCustomerPhoto" time="0.416"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC015_UploadSelfie" time="2.636"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC016_VerifyUploadedSelfie" time="0.395"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC017_InitiateKYC_UsingOfflineAAdhaar" time="0.806"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC018_FetchDataPostKYCIntiated" time="1.493"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC019_MCA_BRE2Requested" time="1.516"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC020_MCA_Fullerton_SecondBRECallback" time="1.155"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC021_FetchLead_AfterBRE2" time="2.642"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC024_UpdateKYCNameInSAI" time="1.856"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC025_SaveBankDetails" time="28.538"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC026_FetchLeadPostBankVerification" time="2.15">
    <failure type="java.lang.AssertionError" message="did not expect to find [BANKING_ACTION_DONE] but found [BANK_VALIDATION_PENDING]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BANKING_ACTION_DONE] but found [BANK_VALIDATION_PENDING]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp.TC026_FetchLeadPostBankVerification(TestMCASoleProp.java:1105)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC026_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC027_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC028_GenerateSanctionLetter" time="0.001">
    <skipped/>
  </testcase> <!-- TC028_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC029_UpdateActualPanInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC030_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC031_GenerateEDIDeclaration" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_GenerateEDIDeclaration -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC032_AcceptEDIDeclaration" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_AcceptEDIDeclaration -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC033_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC034_VerifyPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC035_UploadSheetONPanel" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC036_VerifyLISCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_VerifyLISCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC037_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC037_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC038_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC038_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.014"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC001_FetchLeadDeatils" time="0.406"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC002_DeleteExistingLead" time="0.517"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC003_CreateFullertonLead" time="0.851">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_CREATED] but found [PAN_DEDUPE_FAILED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_CREATED] but found [PAN_DEDUPE_FAILED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge.TC003_CreateFullertonLead(TestMCAFullertonLPBDMerge.java:302)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC003_CreateFullertonLead -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC004_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC004_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC005_UpdateBureauDataSetInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC005_UpdateBureauDataSetInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC006_FetchCIR" time="0.0">
    <skipped/>
  </testcase> <!-- TC006_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC007_FetchLeadAllData" time="0.001">
    <skipped/>
  </testcase> <!-- TC007_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC008_UpdateExistingDetailsInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC008_UpdateExistingDetailsInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC011_uploadCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC012_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC013_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC014_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC015_InitiateKYC_UsingOfflineAAdhaar" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC016_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC017_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC018_MCA_Fullerton_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_MCA_Fullerton_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC019_FetchLead_AfterBRE2" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC020_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC021_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC022_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC023_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC024_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC025_UpdateActualPanInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC026_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC027_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC028_VerifyPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC029_UploadSheetONPanel" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC030_VerifyLISCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_VerifyLISCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC031_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC032_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.006"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC001_FetchLeadDeatils" time="0.613"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC002_DeleteExistingLead" time="0.332"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC003_CreateFullertonLead" time="0.88"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC004_FetchLeadAllData" time="0.934"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC005_UpdateLeadBasicDetails" time="0.979"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC006_FetchLeadAllData" time="1.038"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC007_UpdateBureauDataSetInSAI" time="0.717"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC008_FetchCIR" time="0.833"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC009_FetchLeadAllData" time="2.151"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC010_UpdateExistingDetailsInSAI" time="0.427"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC011_FetchLeadAllData" time="0.432"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC012_LeadDataUpdateForKYC_InSAI" time="0.426"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC013_uploadCustomerPhoto" time="2.713"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC014_VerifyUploadedCustomerPhoto" time="0.371"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC015_UploadSelfie" time="2.928"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC016_VerifyUploadedSelfie" time="0.874"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC017_InitiateKYC_UsingInvalidShareCode_FirstAttempt" time="0.752"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC018_FetchDataPostKYCIntiated" time="1.436"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC019_InitiateKYC_UsingInvalidShareCode_SecondAttempt" time="0.533"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC020_FetchDataPostKYCIntiated" time="0.536"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC021_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_ThirdAttempt" time="0.576"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestInvalidShareCodeForOfflineAadhaar" name="TC022_FetchDataPostKYCIntiated" time="0.818"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.003"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC001_FetchLeadDeatils" time="0.381"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC002_DeleteExistingLead" time="0.512"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC003_CreateFullertonLead" time="0.836"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC004_FetchLeadAllData" time="1.183"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC005_UpdateLeadBasicDetails" time="1.533"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC006_FetchLeadAllData" time="0.952"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC007_UpdateBureauDataSetInSAI" time="0.581"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC008_FetchCIR" time="0.66"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC009_FetchLeadAllData" time="1.621"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC010_UpdateExistingDetailsInSAI" time="0.481"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC011_FetchLeadAllData" time="10.448"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC012_uploadCustomerPhoto" time="3.08"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC013_VerifyUploadedCustomerPhoto" time="0.87"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC014_UploadSelfie" time="2.708"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC015_VerifyUploadedSelfie" time="0.809"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC016_InitiateKYC_UsingSearchByPan" time="0.516"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC017_FetchDataPostKYCIntiated" time="0.469"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC018_InitiateKYC_UsingOfflineAAdhaar" time="0.893"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC019_FetchDataPostKYCIntiated" time="1.688"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC020_InitiateKYC_UsingDigiLocker" time="1.017"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC021_FetchDataPostKYCIntiated" time="5.966"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC022_UploadSheetONPanel" time="5.136"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.003"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC001_FetchLeadDetails_MCA_Piramal" time="0.793"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC002_DeleteExistingLead" time="0.36"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC003_Create_MCA_V3_ABFL_Lead" time="0.888"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC004_FetchLeadAllData" time="0.991"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC005_UpdateLeadBasicDetails" time="1.517"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC006_FetchLeadAllData" time="1.091"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC007_UpdateBureauDataSetInSAI" time="0.381"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC008_FetchCIR" time="8.782">
    <failure type="java.lang.AssertionError" message="did not expect to find [BUREAU_SUCCESS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BUREAU_SUCCESS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker.TC008_FetchCIR(TestMCAABFLUsingDigilocker.java:516)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC011_uploadCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC012_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC013_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC014_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC019_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC020_InitiateKYC_UsingDigiLocker" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_InitiateKYC_UsingDigiLocker -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC021_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC022_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC023_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC024_FetchLead_AfterBRE2" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC025_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC026_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC027_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC028_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC029_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC030_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC031_UpdateActualPanInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC032_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC033_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC034_VerifyPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC035_VerifyLeadStage_LMSSubmitApplicationJob" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC036_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC037_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC037_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC001_FetchLeadDetails_MCA_Piramal" time="0.408"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC002_DeleteExistingLead" time="0.286"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC003_Create_MCA_V3_ABFL_Lead" time="0.839"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC004_FetchLeadAllData" time="0.737"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC005_UpdateLeadBasicDetails" time="1.427"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC006_FetchLeadAllData" time="0.851"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC007_UpdateBureauDataSetInSAI" time="0.529"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC008_FetchCIR" time="7.211">
    <failure type="java.lang.AssertionError" message="did not expect to find [BUREAU_SUCCESS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BUREAU_SUCCESS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA.TC008_FetchCIR(TestMCAABFLUsingOA.java:533)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC010_uploadCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC011_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC012_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC013_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC014_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC015_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC016_InitiateKYC_UsingOfflineAAdhaar" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC017_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC018_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC019_InitiateKYC_UsingOfflineAAdhaar" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC020_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC021_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC022_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC023_FetchLead_AfterBRE2" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC024_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC025_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC026_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC027_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC028_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC029_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC030_UpdateActualPanInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC031_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC032_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC033_VerifyPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC034_VerifyLeadStage_LMSSubmitApplicationJob" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC035_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" name="TC036_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC001_FetchLeadDetails_MCA_Piramal" time="0.374"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC002_DeleteExistingLead" time="0.304"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC003_Create_MCA_V3_ABFL_Lead" time="0.877"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC004_FetchLeadAllData" time="0.724"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC005_UpdateLeadBasicDetails" time="2.504"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC006_FetchLeadAllData" time="0.817"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC007_UpdateBureauDataSetInSAI" time="0.546">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at Services.LendingService.LendingBaseAPI.verifyResponseCodeAs200OK(LendingBaseAPI.java:3499)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP.TC007_UpdateBureauDataSetInSAI(TestMCAABFLusingSBP.java:484)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC007_UpdateBureauDataSetInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC008_FetchCIR" time="0.0">
    <skipped/>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC011_uploadCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC012_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC013_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC014_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC015_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC016_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC017_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC018_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC019_FetchLead_AfterBRE2" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC020_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC021_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC022_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC023_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC024_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC025_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC026_UpdateActualPanInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC027_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC028_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC029_VerifyLeadStage_LMSSubmitApplicationJob" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC030_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" name="TC031_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC001_FetchLeadDetails_MCA_Piramal" time="0.534"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC002_DeleteExistingLead" time="0.56"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC003_Create_MCA_V3_ABFL_Lead" time="0.881"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC004_FetchLeadAllData" time="0.949"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC005_UpdateLeadBasicDetails" time="1.453"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC006_FetchLeadAllData" time="0.964"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC007_UpdateBureauDataSetInSAI" time="0.372"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC008_FetchCIR" time="8.066">
    <failure type="java.lang.AssertionError" message="did not expect to find [BUREAU_SUCCESS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BUREAU_SUCCESS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp.TC008_FetchCIR(TestMCAABFLIdentifySoleProp.java:531)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC011_uploadCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC012_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC013_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC014_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC019_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC020_InitiateKYC_UsingDigiLocker" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_InitiateKYC_UsingDigiLocker -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC021_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC022_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC023_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC024_FetchLead_AfterBRE2" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC025_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC026_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC027_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC028_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC029_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC030_UpdateActualPanInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC031_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC032_GenerateEDIDeclaration" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_GenerateEDIDeclaration -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC033_AcceptEDIDeclaration" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_AcceptEDIDeclaration -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC034_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC035_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC036_VerifyPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC037_VerifyLeadStage_LMSSubmitApplicationJob" time="0.0">
    <skipped/>
  </testcase> <!-- TC037_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC038_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC038_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC039_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC039_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.008"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC001_FetchLeadDeatils" time="0.393"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC002_DeleteExistingLead" time="0.609"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC003_CreateFullertonLead" time="1.936"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC004_FetchLeadAllData" time="0.79"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC005_UpdateBureauDataSetInSAI" time="0.393"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC006_FetchCIR" time="1.062"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC007_FetchLeadAllData" time="12.555"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC008_UpdateExistingDetailsInSAI" time="0.403"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC009_FetchLeadAllData" time="0.492"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.562"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC011_uploadCustomerPhoto" time="2.838"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC012_VerifyUploadedCustomerPhoto" time="1.422"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC013_UploadSelfie" time="2.741"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC014_VerifyUploadedSelfie" time="0.504"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC015_InitiateKYC_UsingDigiLocker" time="0.675"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC016_FetchDataPostKYCIntiated" time="9.97"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC017_FetchDataKYCInitiate" time="1.102"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC018_MCA_Fullerton_SecondBRECallback" time="1.15"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC019_FetchLead_AfterBRE2" time="3.503"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC020_UpdateKYCNameInSAI" time="0.763"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC021_SaveBankDetails" time="5.491"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC022_FetchLeadPostBankVerification" time="0.404"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC023_GenerateLoanAgreement" time="1.348"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC024_GenerateSanctionLetter" time="0.689"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC025_UpdateActualPanInSAI" time="0.389"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC026_AcceptLoanAgreement" time="0.848">
    <failure type="java.lang.AssertionError" message="did not expect to find [EMANDATE_REQUIRED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [EMANDATE_REQUIRED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode.TC026_AcceptLoanAgreement(TestMCAFullertonUsingDGMode.java:1172)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC026_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC027_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC028_VerifyPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC029_UploadSheetONPanel" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC030_VerifyLISCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_VerifyLISCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC031_LMSDataCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC032_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC001_FetchLeadDetails_PL_AXIS" time="0.413"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC002_DeleteExistingLead" time="0.665"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC003_Create_PL_AXIS_DISTRIBUTION_Lead" time="0.49"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC004_FetchLeadAllData" time="1.7"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC005_UpdateLeadBasicDetails" time="0.724"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC006_FetchLeadAllData" time="0.834"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC007_UpdateOccupationDetails" time="0.575"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC008_UpdateBureauDataSetInSAI" time="0.359"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC009_FetchCIR_Async" time="0.692"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC010_FetchLeadAllData_AfterBureauSuccess" time="4.724"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC011_FetchLeadAllData_BRE1Success" time="52.186">
    <failure type="java.lang.AssertionError" message="did not expect to find [LENDER_BRE_SUCCESS] but found [LIS_CREATE_APPLICATION_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LENDER_BRE_SUCCESS] but found [LIS_CREATE_APPLICATION_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate.TC011_FetchLeadAllData_BRE1Success(TestAxisDistributionETBFlowWithoutMandate.java:712)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC011_FetchLeadAllData_BRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC012_LoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_LoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC013_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC014_InitiateKYCForETBUser" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_InitiateKYCForETBUser -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC015_SubmitKYCForETBUser" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_SubmitKYCForETBUser -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC016_KYCStatusForETBUser" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_KYCStatusForETBUser -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC017_FetchLead" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_FetchLead -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC019_UpdatePanOnLead" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_UpdatePanOnLead -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC020_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC021_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC022_CheckLoanOnboardingCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_CheckLoanOnboardingCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC023_CheckLoanDisbursementCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_CheckLoanDisbursementCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlowWithoutMandate" name="TC024_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.02"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC001_PLv3Hero_fetchlLead" time="0.709"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC002_PLv3Hero_DeleteExistingLead" time="0.909"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC003_PLv3Hero_CreateLead" time="0.526"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC004_PLV3Hero_FetchLeadAllData" time="0.787"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC005_BD_OD_Details" time="1.249"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC006_FetchDetailsAfterBD_OD_Details" time="0.358"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC009_PLV3Hero_UpdateLeadDetailsinSAI" time="0.38"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC010_PLv3HERO_FetchDataPostSAIlUpdate" time="0.643"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC011_PLv3HERO_FetchCIR" time="0.899"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC012_PLv3HERO_BRE1Callback" time="14.434"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC013_PLv3HERO_FetchDataPostBRE1Success" time="0.504">
    <failure type="java.lang.AssertionError" message="did not expect to find [BRE_COMPLETED] but found [OCCUPATION_DETAILS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE_COMPLETED] but found [OCCUPATION_DETAILS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP.TC013_PLv3HERO_FetchDataPostBRE1Success(TestPersonalLoanv3Hero_SBP.java:519)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC013_PLv3HERO_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC018_PLV3_UpdateSearchByPANMockData" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_PLV3_UpdateSearchByPANMockData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC014_PLv3HERO_LoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_PLv3HERO_LoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC015_PLv3HERO_FetchDataPostLoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PLv3HERO_FetchDataPostLoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC016_PLv3HERO_UploadSelfiePhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_PLv3HERO_UploadSelfiePhoto -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC017_PLv3HERO_VerifyUploadedCSelfiePhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_PLv3HERO_VerifyUploadedCSelfiePhoto -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC019_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC020_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC027_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC026_PLv3ABFL_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC022_PLv3HERO_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_PLv3HERO_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC023_PLv3HERO_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_PLv3HERO_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC024_PLv3HERO_AdditionalIsRequiredorNot" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_PLv3HERO_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC025_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_PLv3HERO_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC026_PLv3HERO_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3HERO_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC027_PLv3HERO_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_PLv3HERO_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC028_PLv3HERO_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_PLv3HERO_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC029_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_PLv3HERO_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC030_PLv3HERO_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_PLv3HERO_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC031_PLv3HERO_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_PLv3HERO_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC032_PLv3HERO_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_PLv3HERO_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC033_PLv3HERO_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_PLv3HERO_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC034_PLv3HERO_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_PLv3HERO_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC035_PLv3HERO_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_PLv3HERO_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC036_PLv3HERO_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_PLv3HERO_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC037_PLv3HERO_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC037_PLv3HERO_FetchLeadPostSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC038_PLv3HERO_PDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC038_PLv3HERO_PDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.TestPersonalLoanv3Hero_SBP" name="TC039_PLv3HERO_FetchLeadPostPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC039_PLv3HERO_FetchLeadPostPDCCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.005"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC001_FetchLeadDeatils" time="0.685"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC002_DeleteExistingLead" time="0.619"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC003_CreatePLRenewalLead" time="1.278"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC004_FetchLeadAllData" time="2.978"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC005_UpdateBureauDataSetInSAI" time="0.402"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC006_FetchCIR" time="0.901"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC007_FetchLeadAllData" time="19.532"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC08_PLV3_UpdateSearchByPANMockData" time="0.386"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC009_PLRenewal_LoanOfferAccept" time="0.538"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC010_FetchLeadAllData" time="0.345"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC011_PLv3HERO_UploadSelfiePhoto" time="5.275"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC012_PLv3HERO_VerifyUploadedCSelfiePhoto" time="0.692"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC013_InitiateKYC_UsingSearchByPan" time="0.475"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC014_FetchDataPostKYCIntiated" time="3.458"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC015_FetchDataKYCInitiate" time="12.832">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOCATION_REQUIRED] but found [KYC_FAILED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOCATION_REQUIRED] but found [KYC_FAILED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP.TC015_FetchDataKYCInitiate(TestPLRenewalHERO_SBP.java:696)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC015_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC016_PLv3ABFL_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_PLv3ABFL_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC017_PLv3HERO_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_PLv3HERO_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC018_PLv3HERO_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_PLv3HERO_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC019_PLv3HERO_AdditionalIsRequiredorNot" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_PLv3HERO_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC020_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_PLv3HERO_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC021_PLv3HERO_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_PLv3HERO_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC022_PLv3HERO_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_PLv3HERO_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC023_PLv3HERO_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_PLv3HERO_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC024_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_PLv3HERO_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC025_PLv3HERO_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_PLv3HERO_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC026_PLv3HERO_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3HERO_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC027_PLv3HERO_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_PLv3HERO_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC028_PLv3HERO_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_PLv3HERO_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC029_PLv3HERO_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_PLv3HERO_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC030_PLv3HERO_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_PLv3HERO_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC031_PLv3HERO_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_PLv3HERO_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHERO_SBP" name="TC032_PLv3HERO_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_PLv3HERO_FetchLeadPostSubmitApplication -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC001_PLv3Hero_fetchlLead" time="0.403"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC002_PLv3Hero_DeleteExistingLead" time="0.941"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC003_PLv3Hero_CreateLead" time="0.495"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC004_PLV3Hero_FetchLeadAllData" time="0.75"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC005_BD_OD_Details" time="1.138"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC006_FetchDetailsAfterBD_OD_Details" time="0.403"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC009_PLV3Hero_UpdateLeadDetailsinSAI" time="0.347"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC010_PLv3HERO_FetchDataPostSAIlUpdate" time="0.391"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC011_PLv3HERO_FetchCIR" time="0.669"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC012_PLv3HERO_BRE1Callback" time="5.727"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC013_PLv3HERO_FetchDataPostBRE1Success" time="0.575"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC014_PLV3_UpdateSearchByPANMockData" time="0.347"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC015_PLv3HERO_LoanOfferAccept" time="0.855"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC016_PLv3HERO_FetchDataPostLoanOfferAccept" time="0.441"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC017_PLv3HERO_UploadSelfiePhoto" time="3.327"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC018_PLv3HERO_VerifyUploadedCSelfiePhoto" time="0.739"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC019_InitiateKYC_UsingSearchByPan" time="0.542"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC020_FetchDataPostKYCIntiated" time="2.644"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC021_FetchDataKYCInitiate" time="10.899">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOCATION_REQUIRED] but found [KYC_FAILED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOCATION_REQUIRED] but found [KYC_FAILED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution.TC021_FetchDataKYCInitiate(PLv3Hero_Distribution.java:784)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC021_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC022_PLv3ABFL_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC023_PLv3HERO_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_PLv3HERO_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC024_PLv3HERO_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_PLv3HERO_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC025_PLv3HERO_AdditionalIsRequiredorNot" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_PLv3HERO_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC026_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3HERO_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC027_PLv3HERO_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_PLv3HERO_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC028_PLv3HERO_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_PLv3HERO_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC029_PLv3HERO_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_PLv3HERO_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC030_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_PLv3HERO_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC031_PLv3HERO_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_PLv3HERO_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC031_PLv3HERO_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_PLv3HERO_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC032_PLv3HERO_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_PLv3HERO_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC033_PLv3HERO_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_PLv3HERO_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC034_PLv3HERO_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_PLv3HERO_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC035_PLv3HERO_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_PLv3HERO_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC036_PLv3HERO_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_PLv3HERO_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC037_PLv3HERO_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC037_PLv3HERO_FetchLeadPostSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC038_PLv3HERO_PDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC038_PLv3HERO_PDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_DISTRIBUTION.PLv3Hero_Distribution" name="TC039_PLv3HERO_FetchLeadPostPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC039_PLv3HERO_FetchLeadPostPDCCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC001_PLv3ABFL_fetchlLead" time="0.369"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC002_PLv3ABFL_DeleteExistingLead" time="0.307"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC003_PLv3ABFL_CreateLead" time="0.491"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC004_FetchLeadAllData" time="5.913"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC005_BD_OD_Details" time="0.902"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC006_FetchLeadAllDataPostOccupationDetails" time="6.336"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC007_LeadDataUpdateForKYC_InSAI" time="0.375"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC008_PLv3ABFL_LoanOfferAccept" time="2.257"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC009_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.458"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC010_PLv3ABFL_UploadSelfie" time="2.816"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC011_PLv3ABFL_VerifyUploadedSelfie" time="0.703"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC012_InitiateKYC_UsingSearchByPan" time="0.485"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC013_FetchDataKYCInitiate" time="11.66">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOCATION_REQUIRED] but found [KYC_INITIATED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOCATION_REQUIRED] but found [KYC_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW.TC013_FetchDataKYCInitiate(PLv3_ABFL_RTO_FLOW.java:578)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC013_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC014_PLv3ABFL_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC015_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC016_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC017_AdditionalIsRequiredorNot" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC018_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC019_PLv3ABFL_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC019_PLv3ABFL_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC020_PLv3ABFL_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC021_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW" name="TC022_PLv3ABFL_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_SaveBankDetails -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC001_PLv3ABFL_fetchlLead" time="0.509"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC002_PLv3ABFL_DeleteExistingLead" time="0.737"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC003_PLv3ABFL_CreateLead" time="0.691"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC004_PLV3ABFL_FetchLeadAllData" time="0.861"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC005_BD_OD_Details" time="1.056"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC006_FetchDetailsAfterBD_OD_Details" time="0.889"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC009_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.743"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC010_PLv3ABFL_FetchDataPostSAIlUpdate" time="0.591"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC011_PLv3ABFL_FetchCIR" time="0.95"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC012_PLv3ABFL_BRE1Callback" time="11.266"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC013_PLv3ABFL_FetchDataPostBRE1Success" time="0.412">
    <failure type="java.lang.AssertionError" message="did not expect to find [BRE_COMPLETED] but found [OCCUPATION_DETAILS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE_COMPLETED] but found [OCCUPATION_DETAILS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA.TC013_PLv3ABFL_FetchDataPostBRE1Success(TestPersonalLoanV3ABFL_OA.java:523)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC013_PLv3ABFL_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC024_LeadDataUpdateForKYC_InSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC014_PLv3ABFL_LoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_LoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC015_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PLv3ABFL_FetchDataPostLoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC018_PLv3ABFL_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC019_PLv3ABFL_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC020_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC021_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC022_InitiateKYC_UsingOfflineAAdhaar" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC026_FetchDataPostKYCIntiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC027_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC026_PLv3ABFL_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC026_PLv3ABFL_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC027_PLv3ABFL_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_PLv3ABFL_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC028_PLv3ABFL_AdditionalIsRequiredorNot" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_PLv3ABFL_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC029_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_PLv3ABFL_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC030_PLv3ABFL_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_PLv3ABFL_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC031_PLv3ABFL_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC031_PLv3ABFL_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC032_PLv3ABFL_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC032_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC033_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC033_PLv3ABFL_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC034_PLv3ABFL_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC034_PLv3ABFL_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC035_PLv3ABFL_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC035_PLv3ABFL_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC036_PLv3ABFL_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC036_PLv3ABFL_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC037_PLv3ABFL_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC037_PLv3ABFL_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC038_PLv3ABFL_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC038_PLv3ABFL_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC039_PLv3ABFL_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC039_PLv3ABFL_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC040_PLv3ABFL_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC040_PLv3ABFL_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC041_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC041_PLv3ABFL_FetchLeadPostSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC042_PLv3ABFL_PDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC042_PLv3ABFL_PDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC043_PLv3ABFL_FetchLeadPostPDCCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC043_PLv3ABFL_FetchLeadPostPDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.TestPersonalLoanV3ABFL_OA" name="TC044PLv3ABFL_SubmitApplicationLMSApprovedCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC044PLv3ABFL_SubmitApplicationLMSApprovedCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC001_PLv3ABFL_fetchlLead" time="0.631"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC002_PLv3ABFL_DeleteExistingLead" time="4.504"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC003_PLv3ABFL_CreateLead" time="2.719"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC004_PLV3ABFL_FetchLeadAllData" time="8.043"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC005_PLv3ABFL_FetchDataPostBRE1Success" time="0.371"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC006_LeadDataUpdateForKYC_InSAI" time="0.545"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC007_PLv3ABFL_LoanOfferAccept" time="0.767"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC008_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.764"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC009_PLv3ABFL_UploadSelfie" time="3.009"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC010_PLv3ABFL_VerifyUploadedSelfie" time="0.42"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC011_InitiateKYC_UsingSearchByPan" time="0.559"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC012_FetchDataPostKYCIntiated" time="13.206">
    <failure type="java.lang.AssertionError" message="did not expect to find [KYC_FAILED] but found [KYC_INITIATED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [KYC_FAILED] but found [KYC_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged.TC012_FetchDataPostKYCIntiated(PLv3_ABFL_LP_OD_Merged.java:532)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC012_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC013_FetchDataKYCInitiate" time="0.001">
    <skipped/>
  </testcase> <!-- TC013_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC014_PLv3ABFL_LocationCaptured" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_LocationCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC015_PLv3ABFL_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PLv3ABFL_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC016_PLv3ABFL_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_PLv3ABFL_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC017_PLv3ABFL_AdditionalIsRequiredorNot" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_PLv3ABFL_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC018_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC019_PLv3ABFL_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC020_PLv3ABFL_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC021_PLv3ABFL_UpdateKYCNameInSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC022_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC023_PLv3ABFL_SaveBankDetails" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_PLv3ABFL_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC024_PLv3ABFL_FetchLeadPostBankVerification" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_PLv3ABFL_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC025_PLv3ABFL_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_PLv3ABFL_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC026_PLv3ABFL_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC027_PLv3ABFL_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC027_PLv3ABFL_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC028_PLv3ABFL_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC028_PLv3ABFL_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC029_PLv3ABFL_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC029_PLv3ABFL_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_LP_OD_Merged" name="TC030_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC030_PLv3ABFL_FetchLeadPostSubmitApplication -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.004"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC001_PLv3_ABFL_TOPUP_fetchlLead" time="0.379"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC002_PLv3_ABFL_TOPUP_DeleteExistingLead" time="0.55"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC003_PLv3_ABFL_TOPUP_CreateLead" time="0.887"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC004_PLV3ABFLTopup_FetchLeadAllData" time="2.68"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC005_PLV3ABFL_Topup_UpdateLeadDetailsinSAI" time="0.321"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC06_PLv3ABFL_Topup_FetchDataPost_SAI_Update" time="0.384"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC07_PLv3ABFL_TOPUP_FetchCIR" time="1.073"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC008_PLv3ABFL_Topup_BRE1Callback" time="6.963"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC009_PLv3ABFL_Topup_FetchDataPostBRE1Success" time="0.39">
    <failure type="java.lang.AssertionError" message="did not expect to find [BRE_COMPLETED] but found [DATA_PREFILL_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE_COMPLETED] but found [DATA_PREFILL_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP.TC009_PLv3ABFL_Topup_FetchDataPostBRE1Success(TestPersonalLoanTopupABFLviaSBP.java:495)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC009_PLv3ABFL_Topup_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC010_PLV3ABFL_Topup_UpdatePANinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_PLV3ABFL_Topup_UpdatePANinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC011_PLv3ABFL_UpdateLeadDetailsinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_PLv3ABFL_UpdateLeadDetailsinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC012_PLv3ABFL_TOPUP_LoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_PLv3ABFL_TOPUP_LoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC013_PLv3ABFL_TOPUP_FetchDataPostLoanOfferAccept" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_PLv3ABFL_TOPUP_FetchDataPostLoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC014_PLv3ABFL_TOPUP_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_TOPUP_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC015_PLv3ABFL_TOPUP_VerifyUploadedSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PLv3ABFL_TOPUP_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC016_PLv3ABFL_InitiateKYCSearchByPAN" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_PLv3ABFL_InitiateKYCSearchByPAN -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC017_PLv3ABFL_TOPUP_FetchDataPostKYC" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_PLv3ABFL_TOPUP_FetchDataPostKYC -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC018_PLv3ABFL_TOPUP_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_TOPUP_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC019_PLv3ABFL_TOPUP_FetchDataAfterBRE2Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_TOPUP_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC020_PLv3ABFL_TOPUP_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_TOPUP_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC021_PLv3ABFL_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC022_PLv3ABFL_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC023_PLv3ABFL_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_PLv3ABFL_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC024_PLv3ABFL_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_PLv3ABFL_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC025_PLv3ABFL_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_PLv3ABFL_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPersonalLoanTopupABFLviaSBP" name="TC026_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_FetchLeadPostSubmitApplication -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandatePostOnboardingFlow" name="TC001_PLSetMandate_PostOnBoarding_fetchlLead" time="0.407"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandatePostOnboardingFlow" name="TC002_PLSetMandate_PostOnBoarding_DeleteExistingLead" time="0.733"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandatePostOnboardingFlow" name="TC003_PLSetMandate_PostOnBoarding_CreateLead" time="2.516"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandatePostOnboardingFlow" name="TC004_PLSetMandate_PostOnBoarding_FetchLeadUpdateCKYCinSAI" time="0.358"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandatePostOnboardingFlow" name="TC006_PLSetMandate_PostOnBoarding_Emandatecallback" time="0.393"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandatePostOnboardingFlow" name="TC005_PLSetMandate_PostOnBoarding_SaveBankDetails" time="1.909"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC001_PLSetMandate_Onboarding_fetchlLead" time="0.381"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC002_PLSetMandate_Onboarding_DeleteExistingLead" time="0.66"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC003_PLSetMandate_Onboarding_CreateLead" time="0.43"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC004_PLSetMandate_Onboarding_UpdateKYCNameInSAI" time="0.68"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC005_PLSetMandate_Onboarding_FetchLeadUpdateCKYCinSAI" time="0.365"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC006_PLSetMandate_Onboarding_SaveBankDetails" time="1.736"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC007_PLSetMandate_Onboarding_Emandatecallback" time="0.663"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" name="TC008_PLSetMandate_Onboarding_FetchLeadUpdate" time="0.597"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC001_PLSetMandate_Onboarding_fetchlLead" time="0.634"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC002_PLSetMandate_Onboarding_DeleteExistingLead" time="0.636"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC003_PLSetMandate_Onboarding_CreateLead" time="0.406"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC004_PLSetMandate_Onboarding_UpdateKYCNameInSAI" time="1.714"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC005_PLSetMandate_Onboarding_FetchLeadUpdateCKYCinSAI" time="0.381"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC006_PLSetMandate_Onboarding_SaveBankDetails" time="8.17"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC007_PLSetMandate_Onboarding_Emandatecallback" time="0.55"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI" name="TC008_PLSetMandate_Onboarding_FetchLeadUpdate" time="0.367"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC001_PLv3_ABFL_TOPUP_fetchlLead" time="0.38"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC002_PLv3_ABFL_TOPUP_DeleteExistingLead" time="0.345"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC003_PLv3_ABFL_TOPUP_CreateLead" time="0.816"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC004_PLV3ABFLTopup_FetchLeadAllData" time="8.963"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC005_PLV3ABFLTopup_UploadSelfie" time="2.75"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC06_PLv3ABFL_TOPUP_VerifyUploadedSelfie" time="0.365"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC07_LeadDataUpdateForKYC_InSAI" time="0.538"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC08_InitiateKYC_UsingSearchByPan" time="0.465"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC09_FetchDataPostKYCIntiated" time="16.848">
    <failure type="java.lang.AssertionError" message="did not expect to find [KYC_FAILED] but found [KYC_INITIATED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [KYC_FAILED] but found [KYC_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney.TC09_FetchDataPostKYCIntiated(PLABFLTopupCIRMinimalJourney.java:432)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC09_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC010_InitiateKYC_UsingOfflineAAdhaar" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC011_FetchDataKYCInitiate" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC012_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC013_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC014_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC015_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC020_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC021_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC022_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC023_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC024_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.PLABFLTopupCIRMinimalJourney" name="TC025_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC025_PLv3ABFL_FetchLeadPostSubmitApplication -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC001_PLv3_HERO_TOPUP_fetchlLead" time="0.33"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC002_PLv3_HERO_TOPUP_DeleteExistingLead" time="0.316"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC003_PLv3_HERO_TOPUP_CreateLead" time="2.026"/>
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC004_FetchLeadAllData" time="16.143">
    <failure type="java.lang.AssertionError" message="did not expect to find [BRE2_IN_PROGRESS] but found [LENDING_LIS_SUBMIT_APPLICATION_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE2_IN_PROGRESS] but found [LENDING_LIS_SUBMIT_APPLICATION_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney.TC004_FetchLeadAllData(PLHEROTopupCIRMinimalJourney.java:292)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC004_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC05_SecondBRECallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC05_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC06_FetchLeadVerifyAdditionalData" time="0.0">
    <skipped/>
  </testcase> <!-- TC06_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC07_AdditionalDataCapture" time="0.0">
    <skipped/>
  </testcase> <!-- TC07_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC08_BRE3Success" time="0.0">
    <skipped/>
  </testcase> <!-- TC08_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC09_EmandateCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC09_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC010_FetchLeadPostEmandate" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC011_GenerateLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC012_GenerateSanctionLetter" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC013_SubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP.PLHEROTopupCIRMinimalJourney" name="TC014_FetchLeadPostSubmitApplication" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_FetchLeadPostSubmitApplication -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC001_FetchLeadDeatils" time="0.414"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC002_DeleteExistingLead" time="0.334"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC003_CreatePostpaidLead" time="1.239"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC004_FetchLeadAllData" time="0.391"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC005_UpdateBureauDataSetInSAI" time="0.461"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC006_FetchCIR" time="1.207"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC007_BRE1Callback" time="17.43"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC008_FetchDataPostBRE1Success" time="0.351"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC009_FetchLeadAllData" time="20.476"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC010_PPV3ABFL_UpdateLeadDetailsinSAI" time="0.501"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC011_SELFIEREQUIRED" time="10.724"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC012_UploadSelfie" time="2.621"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC013_VerifyUploadedCustomerPhoto" time="0.351"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC014_InitiateKYC_UsingSearchByPan" time="0.563"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC015_FetchDataPostKYCIntiated" time="0.404"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC016_PPV3ABFL_UpdateLeadDetailsinSAI" time="0.357"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC017_InitiateKYC_UsingOfflineAAdhaar" time="0.871"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC019_PPV3ABFL_UpdateLeadDetailsinSAI" time="1.411"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC020_OfferStage2Callback" time="10.774"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC021_FetchDynamicTnc" time="0.446"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC022_AcceptLoanAgreement" time="0.768">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA.TC022_AcceptLoanAgreement(PostpaidABFL_OA.java:788)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC022_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC023_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_OA" name="TC024_LMSCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC024_LMSCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC001_FetchLeadDeatils" time="0.378"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC002_DeleteExistingLead" time="0.529"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC003_CreatePostpaidLead" time="1.098"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC004_FetchLeadAllData" time="0.387"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC005_UpdateBureauDataSetInSAI" time="0.359"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC006_FetchCIR" time="1.066"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC007_BRE1Callback" time="17.922"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC008_FetchDataPostBRE1Success" time="0.438"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC009_FetchLeadAllData" time="20.566"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC010_PPV3ABFL_UpdateLeadDetailsinSAI" time="0.371"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC011_SELFIEREQUIRED" time="10.721"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC012_UploadSelfie" time="2.841"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC013_VerifyUploadedCustomerPhoto" time="0.358"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC014_InitiateKYC_UsingSearchByPan" time="10.476"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC015_PPV3ABFL_UpdateLeadDetailsinSAI" time="0.616"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC016_OfferStage2Callback" time="10.757"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC017_FetchDynamicTnc" time="0.666"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC018_AcceptLoanAgreement" time="1.151">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA.TC018_AcceptLoanAgreement(PostpaidABFL_SBA.java:703)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC018_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC019_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBA" name="TC020_LMSCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_LMSCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC001_FetchLeadDeatils" time="1.064"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC002_DeleteExistingLead" time="0.631"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC003_CreatePostpaidLead" time="2.017"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC004_FetchLeadAllData" time="0.759"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC005_UpdateBureauDataSetInSAI" time="0.48"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC006_FetchCIR" time="2.17"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC007_BRE1Callback" time="10.348"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC008_FetchDataPostBRE1Success" time="0.695">
    <failure type="java.lang.AssertionError" message="did not expect to find [OFFER_GENERATED] but found [LEAD_REJECTED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [OFFER_GENERATED] but found [LEAD_REJECTED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP.TC008_FetchDataPostBRE1Success(PostpaidABFL_SBP.java:400)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC008_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC010_PPV3ABFL_UpdateLeadDetailsinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_PPV3ABFL_UpdateLeadDetailsinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC011_SELFIEREQUIRED" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_SELFIEREQUIRED -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC012_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC013_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC014_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC015_PPV3ABFL_UpdateLeadDetailsinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PPV3ABFL_UpdateLeadDetailsinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC016_OfferStage2Callback" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_OfferStage2Callback -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC017_FetchDynamicTnc" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC018_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC019_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL.PostpaidABFL_SBP" name="TC020_LMSCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_LMSCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.003"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC001_FetchLeadDeatils" time="0.455"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC002_DeleteExistingLead" time="0.312"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC003_CreatePostpaidLead" time="1.008"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC004_FetchLeadAllData" time="1.26"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC005_UpdateBureauDataSetInSAI" time="0.403"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC006_FetchCIR" time="1.078"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC007_BRE1Callback" time="17.965"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC008_FetchDataPostBRE1Success" time="0.535">
    <failure type="java.lang.AssertionError" message="did not expect to find [OFFER_GENERATED] but found [LEAD_REJECTED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [OFFER_GENERATED] but found [LEAD_REJECTED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP.TC008_FetchDataPostBRE1Success(PostpaidFullerton_SBP.java:385)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC008_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC009_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC010_PPV3Fullerton_UpdateLeadDetailsinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_PPV3Fullerton_UpdateLeadDetailsinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC011_SELFIEREQUIRED" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_SELFIEREQUIRED -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC012_UploadSelfie" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC013_VerifyUploadedCustomerPhoto" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC014_InitiateKYC_UsingSearchByPan" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC015_PPV3Fullerton_UpdateLeadDetailsinSAI" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PPV3Fullerton_UpdateLeadDetailsinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC016_OfferStage2Callback" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_OfferStage2Callback -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC017_FetchDynamicTnc" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC018_AcceptLoanAgreement" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC019_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.Fullerton.PostpaidFullerton_SBP" name="TC020_LMSCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_LMSCallback -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.Miscellaneous.Postpaid_PAN_Dedupe" name="TC001_FetchLeadDeatils" time="0.697"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.Miscellaneous.Postpaid_PAN_Dedupe" name="TC002_DeleteExistingLead" time="0.359"/>
  <testcase classname="OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.Miscellaneous.Postpaid_PAN_Dedupe" name="TC003_CreatePostpaidLead" time="1.036"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC001_FetchLeadDeatils" time="0.447"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC002_DeleteExistingLead" time="0.34"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC003_CreateHDFCLead" time="0.488"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC004_FetchLeadAllData" time="0.426"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC005_UpdateLeadBasicDetails" time="0.828"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC006_FetchLeadAllData" time="0.414"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC007_HDFCQDEInitiated" time="0.451"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC008_FetchLeadAllData" time="0.48"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC009_HDFCQDESubmitted" time="0.484"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC010_FetchLeadAllData" time="0.405"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC011_CustomerSoftApproved" time="0.528"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC012_FetchLeadAllData" time="0.369"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC013_DDEInitiated" time="0.431"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC014_FetchLeadAllData" time="0.401"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC015_DDESubmitted" time="0.845"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC016_FetchLeadAllData" time="0.442"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC017_NetBankingVerificationSuccess" time="0.433"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC018_FetchLeadAllData" time="0.384"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC019_ApplicationApproved" time="0.491"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC020_FetchLeadAllData" time="0.385"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC021_ApplicationClosed" time="0.398"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.ETB" name="TC022_FetchLeadAllData" time="0.378"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC001_FetchLeadDeatils" time="0.733"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC002_DeleteExistingLead" time="0.312"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC003_CreateHDFCLead" time="0.441"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC004_FetchLeadAllData" time="0.416"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC005_UpdateLeadBasicDetails" time="1.969"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC006_FetchLeadAllData" time="0.505"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC007_HDFCQDEInitiated" time="0.452"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC008_FetchLeadAllData" time="0.996"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC009_HDFCQDESubmitted" time="0.39"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC010_FetchLeadAllData" time="0.404"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC011_CustomerSoftApproved" time="0.424"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC012_FetchLeadAllData" time="0.477"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC013_DDEInitiated" time="0.412"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC014_FetchLeadAllData" time="0.372"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC015_DDESubmitted" time="0.458"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC016_FetchLeadAllData" time="0.489"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC017_EKYCVerificationSuccess" time="0.594"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC018_FetchLeadAllData" time="0.367"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC019_VKYCInitiated" time="0.382"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC020_FetchLeadAllData" time="0.483"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC021_VKYCCompleted" time="0.551"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC022_FetchLeadAllData" time="0.369"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC023_VKYCVerificationSuccess" time="0.444"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC024_FetchLeadAllData" time="0.456"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC025_ApplicationApproved" time="0.739"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC026_FetchLeadAllData" time="0.37"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC027_ApplicationClosed" time="0.438"/>
  <testcase classname="OCL.Lending.CreditCard.HDFC.NTB" name="TC028_FetchLeadAllData" time="0.38"/>
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.002"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC001_FetchLeadDeatils" time="1.423"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC002_DeleteExistingLead" time="0.337"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC003_CreateSBILead" time="0.449"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC004_FetchLeadDeatils" time="0.453"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC005_SBIQDEInitiated" time="0.57"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC006_FetchLeadAllData" time="0.381"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC007_CustomerSoftApproved" time="10.862">
    <failure type="java.lang.AssertionError" message="did not expect to find [CUSTOMER_SOFT_APPROVED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [CUSTOMER_SOFT_APPROVED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow.TC007_CustomerSoftApproved(CreditCardSBIDREFlow.java:375)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC007_CustomerSoftApproved -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC008_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC008_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC009_DDEInitiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_DDEInitiated -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC010_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC011_DDESubmitted" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_DDESubmitted -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC012_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC013_AppointmentBookingSuccess" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_AppointmentBookingSuccess -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC014_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC015_ApplicationApproved" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_ApplicationApproved -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow" name="TC016_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.001"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC001_FetchLeadDeatils" time="0.794"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC002_DeleteExistingLead" time="0.361"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC003_CreateSBILead" time="0.456"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC004_FetchLeadDeatils" time="0.42"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC005_SBIBasicDetails" time="0.75"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC006_FetchLeadDeatils" time="0.404"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC007_UpdateBureauDataSetInSAI" time="0.38"/>
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC008_FetchCIR" time="7.448">
    <failure type="java.lang.AssertionError" message="did not expect to find [BUREAU_SUCCESS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BUREAU_SUCCESS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow.TC008_FetchCIR(CreditCardSBIRTOFlow.java:410)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC009_FetchLeadDeatils" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadDeatils -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC010_OfferAccepted" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_OfferAccepted -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC011_FetchLeadDeatils" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_FetchLeadDeatils -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC012_SBIQDEInitiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_SBIQDEInitiated -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC013_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC014_CustomerSoftApproved" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_CustomerSoftApproved -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC015_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC016_DDEInitiated" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_DDEInitiated -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC017_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC018_DDESubmitted" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_DDESubmitted -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC019_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC020_AppointmentBookingSuccess" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_AppointmentBookingSuccess -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC021_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC022_ApplicationApproved" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_ApplicationApproved -->
  <testcase classname="OCL.Lending.CreditCard.SBI.CreditCardSBIRTOFlow" name="TC023_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC023_FetchLeadAllData -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.005"/>
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC001_FetchLeadDeatils" time="0.398"/>
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC002_DeleteExistingLead" time="0.448"/>
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC003_CreateSBILead" time="0.49"/>
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC004_FetchLeadDeatils" time="0.39"/>
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC005_SBIQDEInitiated" time="0.975"/>
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC006_FetchLeadAllData" time="0.559"/>
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC007_CustomerSoftApproved" time="12.038">
    <failure type="java.lang.AssertionError" message="did not expect to find [CUSTOMER_SOFT_APPROVED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [CUSTOMER_SOFT_APPROVED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow.TC007_CustomerSoftApproved(CreditCardSBISprintDREFlow.java:373)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC007_CustomerSoftApproved -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC008_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC008_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC009_AddressConfirmSuccessCallback" time="0.0">
    <skipped/>
  </testcase> <!-- TC009_AddressConfirmSuccessCallback -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC010_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC010_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC011_PhotoMatched" time="0.0">
    <skipped/>
  </testcase> <!-- TC011_PhotoMatched -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC012_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC012_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC013_PennyDropCreditSuccess" time="0.0">
    <skipped/>
  </testcase> <!-- TC013_PennyDropCreditSuccess -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC014_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC014_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC015_PennyDropDebitSuccess" time="0.0">
    <skipped/>
  </testcase> <!-- TC015_PennyDropDebitSuccess -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC016_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC016_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC017_MITCCompleted" time="0.0">
    <skipped/>
  </testcase> <!-- TC017_MITCCompleted -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC018_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC018_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC019_ECardGenerated" time="0.0">
    <skipped/>
  </testcase> <!-- TC019_ECardGenerated -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC020_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC020_FetchLeadAllData -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC021_ApplicationApproved" time="0.0">
    <skipped/>
  </testcase> <!-- TC021_ApplicationApproved -->
  <testcase classname="OCL.Lending.CreditCard.SBISprint.CreditCardSBISprintDREFlow" name="TC022_FetchLeadAllData" time="0.0">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadAllData -->
</testsuite> <!-- Lending -->
