<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite thread-count="10" name="Lending - Onboarding Engine" guice-stage="DEVELOPMENT" verbose="30">
  <listeners>
    <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
    <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
    <listener class-name="com.paytm.framework.reporting.listeners.RPTestListener"/>
    <listener class-name="AfterSuite.ListenerTest"/>
  </listeners>
  <test thread-count="10" name="Lending" verbose="30">
    <classes>
      <class name="OCL.Lending.ConsumerLending.TestPostpaidRTOFlow">
        <methods>
          <include name="TC021_OfferStage2Callback"/>
          <include name="intitializeInputData"/>
          <include name="TC020_FetchLeadAllData"/>
          <include name="TC002_DeleteExistingLead"/>
          <include name="TC009_FetchLeadAllData"/>
          <include name="TC017_UploadCustomerPhoto"/>
          <include name="TC025_LMSCallback"/>
          <include name="TC022_FetchDynamicTnc"/>
          <include name="TC003_CreatePostpaidLead"/>
          <include name="TC015_FetchLeadAllData"/>
          <include name="TC013_UpdateBureauDataSetInSAI"/>
          <include name="TC023_AcceptLoanAgreement"/>
          <include name="TC010_AcceptOffer"/>
          <include name="TC016_UploadSelfie"/>
          <include name="TC012_CKYCIDFound"/>
          <include name="TC024_FetchLeadAllData"/>
          <include name="TC011_FetchLeadAllData"/>
          <include name="TC001_FetchLeadDeatils"/>
          <include name="TC014_SBPValidationSuccess"/>
          <include name="TC019_SelfieMatch"/>
          <include name="TC018_VerifyUploadedDocument"/>
        </methods>
      </class> <!-- OCL.Lending.ConsumerLending.TestPostpaidRTOFlow -->
    </classes>
  </test> <!-- Lending -->
</suite> <!-- Lending - Onboarding Engine -->
