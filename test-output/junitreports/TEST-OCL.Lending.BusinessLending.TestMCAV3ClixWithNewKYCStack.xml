<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="18" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack" tests="31" failures="1" timestamp="2022-07-27T17:21:33 IST" time="10.284" errors="0">
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.294" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC026_VerifyPDCCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC026_VerifyPDCCallback -->
  <testcase name="TC003_Create_MCA_V3_Clix" time="1.576" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC022_SaveBankDetails" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC022_SaveBankDetails -->
  <testcase name="TC023_GenerateLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC023_GenerateLoanAgreement -->
  <testcase name="TC013_MCAV3ABFL_InitiateKYC_UsingSearchByPan" time="0.352" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at Services.LendingService.LendingBaseAPI.verifyResponseCodeAs200OK(LendingBaseAPI.java:3408)
at OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack.TC013_MCAV3ABFL_InitiateKYC_UsingSearchByPan(TestMCAV3ClixWithNewKYCStack.java:742)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC013_MCAV3ABFL_InitiateKYC_UsingSearchByPan -->
  <testcase name="TC024_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC024_GenerateSanctionLetter -->
  <testcase name="TC028_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC028_FetchLeadAllData -->
  <testcase name="TC010_UpdateExistingDetailsInSAI" time="0.377" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC018_PLv3ABFL_FetchDataPostKYCIntiated" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_FetchDataPostKYCIntiated -->
  <testcase name="TC015_PLv3HERO_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC015_PLv3HERO_UploadCustomerPhoto -->
  <testcase name="TC006_FetchLeadAllData" time="0.742" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC011_FetchLeadAllData" time="0.405" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC020_PLv3ABFL_SecondBRECallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_SecondBRECallback -->
  <testcase name="TC027_UploadSheetONPanel" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC027_UploadSheetONPanel -->
  <testcase name="TC024_AcceptLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC024_AcceptLoanAgreement -->
  <testcase name="TC005_UpdateLeadBasicDetails" time="2.556" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC016_PLv3HERO_VerifyUploadedCustomerPhoto" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC016_PLv3HERO_VerifyUploadedCustomerPhoto -->
  <testcase name="TC021_PLv3ABFL_UpdateKYCNameInSAI" time="0.001" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase name="TC002_DeleteExistingLead" time="0.325" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC017_PLv3HERO_UploadSelfie" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC017_PLv3HERO_UploadSelfie -->
  <testcase name="TC009_FetchLeadAllData" time="1.374" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC019_PLv3ABFL_FetchDataPostSelfieUploaded" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_FetchDataPostSelfieUploaded -->
  <testcase name="TC004_FetchLeadAllData" time="0.655" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC008_FetchCIR" time="0.690" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.590" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC012_MCAV3ABFL_UpdateLeadDetailsinSAI" time="0.347" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack"/>
  <testcase name="TC017_PLv3HERO_VerifyUploadedSelfie" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC017_PLv3HERO_VerifyUploadedSelfie -->
  <testcase name="TC014_PLv3ABFL_FetchDataPostKYCIntiated" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_FetchDataPostKYCIntiated -->
  <testcase name="TC027_LMSDataCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC027_LMSDataCallback -->
  <testcase name="TC025_EmandateCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack">
    <skipped/>
  </testcase> <!-- TC025_EmandateCallback -->
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAV3ClixWithNewKYCStack -->
