<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Individual.RegisterLead.FlowRegisterLead" tests="13" failures="3" timestamp="2021-11-17T11:52:54 IST" time="44.208" errors="0">
  <testcase name="RegisterLeadPositiveMobile" time="11.803" classname="OCL.Individual.RegisterLead.FlowRegisterLead">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.RegisterLead.FlowRegisterLead.RegisterLeadPositiveMobile(FlowRegisterLead.java:76)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- RegisterLeadPositiveMobile -->
  <testcase name="RegisterLeadPositiveEmail" time="11.517" classname="OCL.Individual.RegisterLead.FlowRegisterLead">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.RegisterLead.FlowRegisterLead.RegisterLeadPositiveEmail(FlowRegisterLead.java:103)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- RegisterLeadPositiveEmail -->
  <testcase name="RegisterLeadPositiveEditLead" time="0.000" classname="OCL.Individual.RegisterLead.FlowRegisterLead">
    <skipped/>
  </testcase> <!-- RegisterLeadPositiveEditLead -->
  <testcase name="RegisterLeadPositiveNoEmail" time="11.394" classname="OCL.Individual.RegisterLead.FlowRegisterLead">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.RegisterLead.FlowRegisterLead.RegisterLeadPositiveNoEmail(FlowRegisterLead.java:159)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- RegisterLeadPositiveNoEmail -->
  <testcase name="RegisterLeadPositiveNoMobile" time="1.195" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffAdditionalNumber" time="1.058" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffAdditionalEmail" time="0.922" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffMobileInAdditional" time="1.294" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffEmailInAdditional" time="0.970" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffEmailInParam" time="1.015" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffMobileInParam" time="1.128" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffJWTMobile" time="0.963" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
  <testcase name="RegisterLeadDiffJWTEmail" time="0.949" classname="OCL.Individual.RegisterLead.FlowRegisterLead"/>
</testsuite> <!-- OCL.Individual.RegisterLead.FlowRegisterLead -->
