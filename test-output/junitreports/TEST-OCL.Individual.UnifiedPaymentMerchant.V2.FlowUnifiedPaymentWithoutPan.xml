<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="18" hostname="197NODMB24984.local" name="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan" tests="57" failures="3" timestamp="2022-07-13T13:25:47 IST" time="29.380" errors="0">
  <testcase name="TC0001_CreateApplicantOauth" time="1.377" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0002_GetApplicantToken" time="1.465" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0003_FetchDetails" time="0.638" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0004_FetchDetails" time="0.335" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0005_FetchDetails" time="0.370" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0006_FetchDetails" time="0.370" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0007_FetchDetails" time="0.637" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0008_CreateLead" time="0.428" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0009_CreateLead" time="0.393" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00010_CreateLead" time="0.390" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00011_CreateLead" time="0.718" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00012_FetchDetails" time="0.660" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00013_CreateLead" time="0.384" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00014_SaveBusiness" time="0.391" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00015_SaveBusiness" time="0.463" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00016_SaveBusiness" time="0.388" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00017_SaveBusiness" time="0.385" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00018_SaveBusiness" time="0.619" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00019_FetchDetails" time="0.510" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00020_AdditionalDetails" time="0.380" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00021_AdditionalDetails" time="0.372" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00022_AdditionalDetails" time="0.527" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0023_AdditonalDetails" time="0.354" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00024_FetchDetails" time="0.662" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0025_SaveIdentityDetails" time="0.369" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan.TC0025_SaveIdentityDetails(FlowUnifiedPaymentWithoutPan.java:661)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0025_SaveIdentityDetails -->
  <testcase name="TC0026_SaveIdentityDetails" time="2.816" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0027_SaveIdentityDetails" time="2.421" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0028_SaveIdentityDetails" time="1.961" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0029_SaveIdentityDetails" time="0.429" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0030_SaveIdentityDetails" time="3.098" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00031_FetchDetails" time="0.694" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0032_SaveIdentityPANDetails" time="0.430" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan.TC0032_SaveIdentityPANDetails(FlowUnifiedPaymentWithoutPan.java:840)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0032_SaveIdentityPANDetails -->
  <testcase name="TC0033_SaveIdentityPANDetails" time="0.385" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC00034_FetchDetails" time="0.598" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0035_ValidateBank" time="0.421" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0036_ValidateBank" time="0.499" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0037_ValidateBank" time="0.432" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0038_ValidateBank" time="0.456" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0039_ValidateBank" time="1.153" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <failure type="java.lang.AssertionError" message="expected [null] but found [Name Match failed for bank details]">
      <![CDATA[java.lang.AssertionError: expected [null] but found [Name Match failed for bank details]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotSame(Assert.java:965)
at org.testng.Assert.assertNull(Assert.java:898)
at org.testng.Assert.assertNull(Assert.java:886)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan.TC0039_ValidateBank(FlowUnifiedPaymentWithoutPan.java:1042)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0039_ValidateBank -->
  <testcase name="TC0040_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0040_SaveBankDetails -->
  <testcase name="TC0041_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0041_SaveBankDetails -->
  <testcase name="TC0042_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0042_SaveBankDetails -->
  <testcase name="TC0043_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0043_SaveBankDetails -->
  <testcase name="TC0044_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0044_SaveBankDetails -->
  <testcase name="TC0045_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0045_SaveBankDetails -->
  <testcase name="TC00046_FetchDetails" time="0.001" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC00046_FetchDetails -->
  <testcase name="TC0047_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0047_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0048_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0048_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0049_SaveRefreeCodeAndWhatsappNotification" time="0.001" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0049_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0050_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0050_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC00051_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC00051_FetchDetails -->
  <testcase name="TC0052_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0052_CreateAccountForUPM_V2 -->
  <testcase name="TC0053_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0053_CreateAccountForUPM_V2 -->
  <testcase name="TC0054_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0054_CreateAccountForUPM_V2 -->
  <testcase name="TC0055_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0055_CreateAccountForUPM_V2 -->
  <testcase name="TC0056_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0056_CreateAccountForUPM_V2 -->
  <testcase name="TC00057_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC00057_FetchDetails -->
</testsuite> <!-- OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithoutPan -->
