<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="15" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPostpaidFullerton" tests="27" failures="1" timestamp="2022-07-13T13:12:25 IST" time="81.354" errors="0">
  <testcase name="TC004_FetchLeadAllData" time="0.385" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC003_CreatePostpaidLead" time="1.277" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC007_UpdateLeadBasicDetails" time="20.718" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.441" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC008_FetchLeadAllData" time="0.840" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC010_FetchCIR" time="4.343" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC002_DeleteExistingLead" time="0.350" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC012_AcceptOffer" time="0.632" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOAN_OFFER_ACCEPTED] but found [BUREAU_EXPIRED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOAN_OFFER_ACCEPTED] but found [BUREAU_EXPIRED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPostpaidFullerton.TC012_AcceptOffer(TestPostpaidFullerton.java:630)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC012_AcceptOffer -->
  <testcase name="TC027_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC027_FetchLeadAllData -->
  <testcase name="TC019_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC019_UploadCustomerPhoto -->
  <testcase name="TC021_SelfieMatch" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC021_SelfieMatch -->
  <testcase name="TC014_CKYCIDFound" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC014_CKYCIDFound -->
  <testcase name="TC011_BRE1Callback" time="10.726" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC012_FetchDataPostBRE1Success" time="0.456" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC023_OfferStage2Callback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC023_OfferStage2Callback -->
  <testcase name="TC016_SBPValidationSuccess" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC016_SBPValidationSuccess -->
  <testcase name="TC025_FetchDynamicTnc" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC025_FetchDynamicTnc -->
  <testcase name="TC015_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC015_UpdateBureauDataSetInSAI -->
  <testcase name="TC017_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC017_FetchLeadAllData -->
  <testcase name="TC022_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadAllData -->
  <testcase name="TC028_LMSCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC028_LMSCallback -->
  <testcase name="TC011_FetchLeadAllData" time="40.781" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC026_AcceptLoanAgreement" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC026_AcceptLoanAgreement -->
  <testcase name="TC018_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC018_UploadSelfie -->
  <testcase name="TC009_UpdateBureauDataSetInSAI" time="0.405" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton"/>
  <testcase name="TC020_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC020_VerifyUploadedDocument -->
  <testcase name="TC013_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidFullerton">
    <skipped/>
  </testcase> <!-- TC013_FetchLeadAllData -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostpaidFullerton -->
