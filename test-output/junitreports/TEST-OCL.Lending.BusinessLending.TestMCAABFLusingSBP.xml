<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.TestMCAABFLusingSBP" tests="16" failures="1" timestamp="2023-05-17T17:22:22 IST" time="31.827" errors="0">
  <testcase name="TC002_DeleteExistingLead" time="0.445" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC005_UpdateLeadBasicDetails" time="1.955" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC008_FetchCIR" time="0.815" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.550" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC004_FetchLeadAllData" time="1.020" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC009_FetchLeadAllData" time="7.784" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC014_VerifyUploadedSelfie" time="0.583" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC012_VerifyUploadedCustomerPhoto" time="0.396" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC003_Create_MCA_V3_ABFL_Lead" time="1.533" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC006_FetchLeadAllData" time="1.104" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC016_FetchDataPostKYCIntiated" time="6.973" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP">
    <failure type="java.lang.AssertionError" message="did not expect to find [KYC_FAILED] but found [KYC_INITIATED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [KYC_FAILED] but found [KYC_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.TestMCAABFLusingSBP.TC016_FetchDataPostKYCIntiated(TestMCAABFLusingSBP.java:750)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC016_FetchDataPostKYCIntiated -->
  <testcase name="TC015_InitiateKYC_UsingSearchByPan" time="0.521" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC011_uploadCustomerPhoto" time="3.205" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC013_UploadSelfie" time="3.246" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC010_LeadDataUpdateForKYC_InSAI" time="0.402" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
  <testcase name="TC001_FetchLeadDetails_MCA_Piramal" time="1.295" classname="OCL.Lending.BusinessLending.TestMCAABFLusingSBP"/>
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAABFLusingSBP -->
