<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="35" name="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" time="44.236" errors="0" timestamp="2023-07-28T18:48:44 IST" skipped="4">
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC001_FetchLeadDeatils" time="0.336"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC020_MCA_Fullerton_SecondBRECallback" time="0.664"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC029_UpdateActualPanInSAI" time="0.280"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC009_FetchLeadAllData" time="1.289"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC032_VerifyPDCCallback" time="2.938">
    <failure message="did not expect to find [APPLICATION_PENDING] but found [EMANDATE_SUCCESS]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [APPLICATION_PENDING] but found [EMANDATE_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC032_VerifyPDCCallback(TestMCAFullertonV3Workflow.java:1362)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC032_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC011_FetchLeadAllData" time="10.323"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC012_LeadDataUpdateForKYC_InSAI" time="0.268"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC003_CreateFullertonLead" time="0.788"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC007_UpdateBureauDataSetInSAI" time="0.422"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC025_SaveBankDetails" time="0.510"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC004_FetchLeadAllData" time="0.608"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC028_GenerateSanctionLetter" time="0.418"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC010_UpdateExistingDetailsInSAI" time="0.310"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC016_VerifyUploadedSelfie" time="0.252"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC033_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC027_GenerateLoanAgreement" time="0.744"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC017_InitiateKYC_UsingSearchByPan" time="0.520"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="test" time="0.111"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC034_VerifyLISCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_VerifyLISCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC036_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC015_UploadSelfie" time="2.595"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC013_uploadCustomerPhoto" time="2.635"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC026_FetchLeadPostBankVerification" time="0.254"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC008_FetchCIR" time="7.048"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC019_MCA_BRE2Requested" time="0.255"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC030_AcceptLoanAgreement" time="1.057"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC005_UpdateLeadBasicDetails" time="2.302"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC021_FetchLead_AfterBRE2" time="4.540"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC031_EmandateCallback" time="0.305"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC018_FetchDataPostKYCIntiated" time="0.789"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC006_FetchLeadAllData" time="0.552"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC014_VerifyUploadedCustomerPhoto" time="0.260"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC024_UpdateKYCNameInSAI" time="0.540"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC035_LMSDataCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow" name="TC002_DeleteExistingLead" time="0.323"/>
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow -->
