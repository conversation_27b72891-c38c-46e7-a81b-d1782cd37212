<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow" tests="8" failures="1" timestamp="2022-07-13T13:12:25 IST" time="9.892" errors="0">
  <testcase name="TC003_PLSetMandate_Onboarding_CreateLead" time="0.638" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
  <testcase name="TC008_PLSetMandate_Onboarding_FetchLeadUpdate" time="1.328" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_CREATED] but found [LMS_DATA_UPDATE_REQUEST_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_CREATED] but found [LMS_DATA_UPDATE_REQUEST_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow.TC008_PLSetMandate_Onboarding_FetchLeadUpdate(TestSetMandateOnboardingFlow.java:308)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC008_PLSetMandate_Onboarding_FetchLeadUpdate -->
  <testcase name="TC001_PLSetMandate_Onboarding_fetchlLead" time="0.418" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
  <testcase name="TC002_PLSetMandate_Onboarding_DeleteExistingLead" time="0.533" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
  <testcase name="TC007_PLSetMandate_Onboarding_Emandatecallback" time="0.473" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
  <testcase name="TC005_PLSetMandate_Onboarding_FetchLeadUpdateCKYCinSAI" time="0.341" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
  <testcase name="TC006_PLSetMandate_Onboarding_SaveBankDetails" time="4.736" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
  <testcase name="TC004_PLSetMandate_Onboarding_UpdateKYCNameInSAI" time="1.425" classname="OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlow -->
