<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="5" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate" tests="9" failures="1" timestamp="2022-07-13T13:25:47 IST" time="14.483" errors="0">
  <testcase name="TC007_FetchLeadDetailsBankUpdate" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate">
    <skipped/>
  </testcase> <!-- TC007_FetchLeadDetailsBankUpdate -->
  <testcase name="TC001_FetchLeadStatusBankUpdate" time="0.425" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate"/>
  <testcase name="TC003_CreateLeadBankUpdate" time="2.089" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate.TC003_CreateLeadBankUpdate(FlowPGProfileUpdateBankUpdate.java:207)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC003_CreateLeadBankUpdate -->
  <testcase name="TC005_FetchDocumentsBankUpdate" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate">
    <skipped/>
  </testcase> <!-- TC005_FetchDocumentsBankUpdate -->
  <testcase name="TC002_FetchSavedBanksBankUpdate" time="1.634" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate"/>
  <testcase name="TC006_UploadDocumentBankUpdate" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate">
    <skipped/>
  </testcase> <!-- TC006_UploadDocumentBankUpdate -->
  <testcase name="TC000_GetSessionToken" time="10.335" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate"/>
  <testcase name="TC008_SubmitLeadBankUpdate" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate">
    <skipped/>
  </testcase> <!-- TC008_SubmitLeadBankUpdate -->
  <testcase name="TC004_CreateBankUpdateNameMatchFail" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate">
    <skipped/>
  </testcase> <!-- TC004_CreateBankUpdateNameMatchFail -->
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowPGProfileUpdateBankUpdate -->
