<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="ats.FetchPermission.AclFetchPermissionTest" tests="13" failures="0" timestamp="2022-07-13T13:28:49 IST" time="4.984" errors="1">
  <testcase name="FetchPermissionsFor_OCL_FSE" time="0.768" classname="ats.FetchPermission.AclFetchPermissionTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ACLFetchPermission(MiddlewareServices.java:4626)
at ats.FetchPermission.AclFetchPermissionTest.FetchPermissionsFor_OCL_FSE(AclFetchPermissionTest.java:37)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- FetchPermissionsFor_OCL_FSE -->
  <testcase name="FetchPermissionsFor_OCL_WAREHOUSE" time="0.373" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_OCL_ADMIN" time="0.307" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_PPBL_ATS_BC" time="0.336" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_OCL_PSA" time="0.293" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_EXTERNAL" time="0.382" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_IMT" time="0.409" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_PPBL_ADMIN" time="0.590" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_PPBL_WAREHOUSE" time="0.410" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_PPBL_CITYSPOC" time="0.314" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_PPBL_BC_FSE" time="0.264" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_PPBL_BC" time="0.264" classname="ats.FetchPermission.AclFetchPermissionTest"/>
  <testcase name="FetchPermissionsFor_OCL_CITYSPOC" time="0.274" classname="ats.FetchPermission.AclFetchPermissionTest"/>
</testsuite> <!-- ats.FetchPermission.AclFetchPermissionTest -->
