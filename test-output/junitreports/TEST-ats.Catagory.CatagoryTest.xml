<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="ats.Catagory.CatagoryTest" tests="16" failures="0" timestamp="2022-07-13T13:28:49 IST" time="8.564" errors="0">
  <testcase name="createCatagoryWithAllMandatoryAttributes" time="1.149" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="createCatagoryWithMissingNameAttributes" time="0.592" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="createCatagoryWithMissingBarcodePrefixAttributes" time="0.687" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithValidName" time="0.365" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithInvalidName" time="0.399" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithoutName" time="0.375" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="createCatagoryWithMissingPrefixAndCategoryName" time="1.379" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="createCatagoryWithAlreadyExistingCategoryName" time="0.364" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="createCatagoryWithNumberInPrefix" time="0.543" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="createCatagoryWithoutAccept" time="0.439" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="createCatagoryWithoutAcceptLanguage" time="0.334" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithCategoryIdInplaceOfName" time="0.300" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithValidCategotyId" time="0.339" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithInValidCategotyId" time="0.311" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithCategoryNameInPlaceOfId" time="0.413" classname="ats.Catagory.CatagoryTest"/>
  <testcase name="searchCatagoryWithCategoryIdNull" time="0.575" classname="ats.Catagory.CatagoryTest"/>
</testsuite> <!-- ats.Catagory.CatagoryTest -->
