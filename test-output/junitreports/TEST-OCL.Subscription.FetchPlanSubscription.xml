<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="17" name="OCL.Subscription.FetchPlanSubscription" time="18.171" errors="0" timestamp="2022-07-14T20:53:32 IST" skipped="0">
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanBlankAuthxclienttoken" time="0.306"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="FetchSubscriptionPlanTerminalActive" time="5.169"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanNoMID" time="0.389"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="FetchSubscriptionPlanWithoutRefund" time="0.363"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanNoSTATUS" time="0.429"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanInvalidCientId" time="0.266"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanBlankxclientId" time="0.290"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanBlankAuthxclientId" time="0.315"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanInvalid" time="0.277"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanInvalidAuthx" time="0.254"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlan" time="0.318"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanInvalidId" time="0.253"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlaninvalidstatus" time="0.234"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="FetchSubscriptionPlanTerminalInActive" time="8.446"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanInvalidtoken" time="0.273"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanBlankAuth" time="0.279"/>
  <testcase classname="OCL.Subscription.FetchPlanSubscription" name="NegativeFetchSubscriptionPlanNoUSN" time="0.310"/>
</testsuite> <!-- OCL.Subscription.FetchPlanSubscription -->
