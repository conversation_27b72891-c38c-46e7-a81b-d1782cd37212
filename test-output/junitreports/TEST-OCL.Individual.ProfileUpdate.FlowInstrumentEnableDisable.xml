<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable" tests="11" failures="0" timestamp="2022-07-13T13:25:47 IST" time="68.946" errors="0">
  <testcase name="TC005_CreateEnableDisableNb" time="6.836" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC001_CreateEnableDisablePostpaid" time="2.020" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC007_CreateVipEnableDisableCC" time="6.609" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC008_CreateVipEnableDisablePpi" time="6.719" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC003_CreateEnableDisableCc" time="6.013" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC007_CreateVipEnableDisableDc" time="6.513" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC002_CreateEnableDisablePpi" time="6.098" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC001_GetSessionToken" time="4.253" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC004_CreateEnableDisableDc" time="6.665" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC006_CreateVipEnableDisableNb" time="15.748" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
  <testcase name="TC009_CreateVipEnableDisablePostPaid" time="1.472" classname="OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable"/>
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowInstrumentEnableDisable -->
