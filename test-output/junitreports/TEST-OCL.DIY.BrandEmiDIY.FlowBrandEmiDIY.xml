<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="15" hostname="197NODMB24984.local" name="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY" tests="50" failures="2" timestamp="2022-07-13T12:28:08 IST" time="22.691" errors="0">
  <testcase name="TC_34_FetchPlanEDCDIYWithEmptyToken" time="0.273" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_045_fetchLeadStatusBrandEMIDIY" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_045_fetchLeadStatusBrandEMIDIY -->
  <testcase name="TC_21_ValidateDealerWithInCorrectDealerCodePassed" time="0.658" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_22_ValidateDealerWithInCorrectBrandIdPassed" time="0.358" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_40_CreateLeadBrandEMIDIY" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_40_CreateLeadBrandEMIDIY -->
  <testcase name="TC_46_FetchParentLeadBrandEMIDIY" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_46_FetchParentLeadBrandEMIDIY -->
  <testcase name="TC_18_GetAllBrandsWithEmptyMIDPassed" time="0.336" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_10_CheckEligibilityforBrandEmiDIYWithInvalidPlanTypePassed" time="0.667" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_30_ValidateDealerWithCorrectDealerPassed" time="0.387" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_29_ValidateDealerWithAllParamsNullPassed" time="0.314" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_50_FetchNoOfLeadsBrandEMIDIY" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_50_FetchNoOfLeadsBrandEMIDIY -->
  <testcase name="TC_15_CheckEligibilityforBrandEmiDIYWithEmptySolutionTypePassed" time="1.173" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_05_CheckEligibilityforBrandEmiDIYWithoutpassingPlanType" time="0.283" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_13_CheckEligibilityforBrandEmiDIYWithEmptyPlanTypePassed" time="0.582" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_04_CheckEligibilityforBrandEmiDIYWithoutpassingChannel" time="0.339" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_28_ValidateDealerWithNullCustIdPassed" time="0.608" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_33_FetchPlanEDCDIYWithEmptyMID" time="0.346" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_08_CheckEligibilityforBrandEmiDIYWithInvalidSolutionTypePassed" time="1.552" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_17_GetAllBrandsWithInvalidMIDPassed" time="0.373" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_041_fetchPlanforBrandEMIDIYWithEmptySolutionType" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_041_fetchPlanforBrandEMIDIYWithEmptySolutionType -->
  <testcase name="TC_24_ValidateDealerWithoutTokenPassed" time="0.264" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_25_ValidateDealerWithNullMidPassed" time="0.410" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_32_FetchPlanEDCDIYWithEmptyPlanType" time="0.371" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_07_CheckEligibilityforBrandEmiDIYWithInvalidMidPassed" time="0.339" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_38_CreateLeadBrandEMIDIYWithInvalidSolutionTypeLevel2" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_38_CreateLeadBrandEMIDIYWithInvalidSolutionTypeLevel2 -->
  <testcase name="TC_043_fetchPlanforBrandEMIDIYWithInvalidPlanType" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_043_fetchPlanforBrandEMIDIYWithInvalidPlanType -->
  <testcase name="TC_02_CheckEligibilityforBrandEmiDIYWithoutpassingMID" time="0.452" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_37_CreateLeadBrandEMIDIYWithInvalidChannel" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_37_CreateLeadBrandEMIDIYWithInvalidChannel -->
  <testcase name="TC_20_ValidateDealerWithInCorrectmidPassed" time="0.608" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_39_CreateLeadBrandEMIDIYWithInvalidEntity" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_39_CreateLeadBrandEMIDIYWithInvalidEntity -->
  <testcase name="TC_31_FetchPlanEDCDIYWithEmptySolutionType" time="0.336" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_06_CheckEligibilityforBrandEmiDIYWithInvalidCustidPassed" time="1.644" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY.TC_06_CheckEligibilityforBrandEmiDIYWithInvalidCustidPassed(FlowBrandEmiDIY.java:148)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_06_CheckEligibilityforBrandEmiDIYWithInvalidCustidPassed -->
  <testcase name="TC_49_FetchParentLeadBrandEMIDIYWithoutParamAll" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_49_FetchParentLeadBrandEMIDIYWithoutParamAll -->
  <testcase name="TC_03_CheckEligibilityforBrandEmiDIYWithoutpassingSolutionType" time="0.333" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_23_ValidateDealerWithInCorrectCustIdPassed" time="0.669" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_09_CheckEligibilityforBrandEmiDIYWithInvalidChannelPassed" time="1.355" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_01_CheckEligibilityforBrandEmiDIYWithoutpassingCustID" time="0.569" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_19_GetAllBrandsWithCorrectMIDPassed" time="2.396" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_044_fetchPlanforBrandEMIDIY" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_044_fetchPlanforBrandEMIDIY -->
  <testcase name="TC_36_CreateLeadBrandEMIDIYWithInvalidSolution" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_36_CreateLeadBrandEMIDIYWithInvalidSolution -->
  <testcase name="TC_11_CheckEligibilityforBrandEmiDIYWithEmptyCustIDPassed" time="0.292" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_27_ValidateDealerWithNullDealerCodePassed" time="0.694" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_47_FetchChildLeadBrandEMIDIY" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_47_FetchChildLeadBrandEMIDIY -->
  <testcase name="TC_042_fetchPlanforBrandEMIDIYWithInvalidmid" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_042_fetchPlanforBrandEMIDIYWithInvalidmid -->
  <testcase name="TC_35_FetchPlanEDCDIY" time="0.381" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY.TC_35_FetchPlanEDCDIY(FlowBrandEmiDIY.java:620)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_35_FetchPlanEDCDIY -->
  <testcase name="TC_12_CheckEligibilityforBrandEmiDIYWithEmptyMIDPassed" time="0.311" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_48_FetchParentLeadBrandEMIDIYWithInvalidSolution" time="0.000" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_48_FetchParentLeadBrandEMIDIYWithInvalidSolution -->
  <testcase name="TC_26_ValidateDealerWithNullBrandIDPassed" time="0.614" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_14_CheckEligibilityforBrandEmiDIYWithEmptyChannelPassed" time="1.165" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_16_CheckEligibilityforBrandEmiDIYWithAllParametersPassed" time="1.239" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
</testsuite> <!-- OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY -->
