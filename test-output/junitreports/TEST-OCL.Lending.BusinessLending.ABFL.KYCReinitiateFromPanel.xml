<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="21" name="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" time="28.953" errors="0" timestamp="2023-08-25T20:55:53 IST" skipped="5">
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC006_FetchLeadAllData" time="0.854"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC016_InitiateKYC_UsingOfflineAAdhaar" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC003_Create_MCA_V3_ABFL_Lead" time="1.445"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC005_UpdateLeadBasicDetails" time="1.668"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC010_uploadCustomerPhoto" time="3.089"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC018_InitiateKYC_UsingDigiLocker" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_InitiateKYC_UsingDigiLocker -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC008_FetchCIR" time="1.807"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC020_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC001_FetchLeadDetails_MCA_ABFL" time="0.592"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC012_UploadSelfie" time="2.688"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC019_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="test" time="0.172"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC015_FetchDataPostKYCIntiated" time="6.153">
    <failure message="did not expect to find [KYC_FAILED] but found [KYC_INITIATED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [KYC_FAILED] but found [KYC_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel.TC015_FetchDataPostKYCIntiated(KYCReinitiateFromPanel.java:714)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC015_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC009_FetchLeadAllData" time="7.444"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC013_VerifyUploadedSelfie" time="0.364"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC011_VerifyUploadedCustomerPhoto" time="0.428"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC004_FetchLeadAllData" time="1.008"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC002_DeleteExistingLead" time="0.336"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC014_InitiateKYC_UsingSearchByPan" time="0.531"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC007_UpdateBureauDataSetInSAI" time="0.374"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel" name="TC017_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_FetchDataPostKYCIntiated -->
</testsuite> <!-- OCL.Lending.BusinessLending.ABFL.KYCReinitiateFromPanel -->
