<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="7" name="OCL.CommonOnboarding.EDC.GetLeadStatus" time="6.435" errors="0" timestamp="2023-11-07T13:40:46 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.GetLeadStatus" name="TC_006_GetMCOLeadStatusWithoutdeviceIdentifier" time="0.588"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetLeadStatus" name="test" time="0.279"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetLeadStatus" name="TC_005_GetMCOLeadstatusWithoutVersion" time="0.549"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetLeadStatus" name="TC_2_getexistingChildLeadStatus" time="2.247"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetLeadStatus" name="TC_3_getexistingParentLeadStatus" time="0.373"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetLeadStatus" name="TC_1_getLeadStatus" time="1.704"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetLeadStatus" name="TC_4_getLeadStatusWithoutLeadID" time="0.695"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.GetLeadStatus -->
