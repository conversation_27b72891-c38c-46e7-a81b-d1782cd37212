<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="31" hostname="197NODMB24984.local" name="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex" tests="34" failures="0" timestamp="2022-07-13T12:28:08 IST" time="1.841" errors="1">
  <testcase name="TC004_02_PlanAmexFetchMIDemptyCustId" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_02_PlanAmexFetchMIDemptyCustId -->
  <testcase name="TC005_07_PlanAmexFetchPlanNoEntity" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_07_PlanAmexFetchPlanNoEntity -->
  <testcase name="TC004_04_PlanAmexFetchMIDEmptyReqType" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_04_PlanAmexFetchMIDEmptyReqType -->
  <testcase name="TC005_19_PlanAmexFetchPlanNoEdcFlag" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_19_PlanAmexFetchPlanNoEdcFlag -->
  <testcase name="TC004_07_PlanAmexPositiveFetchMID" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_07_PlanAmexPositiveFetchMID -->
  <testcase name="TC005_11_PlanAmexFetchPlanNoCustId" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_11_PlanAmexFetchPlanNoCustId -->
  <testcase name="TC005_08_PlanAmexFetchPlanEmptyCustId" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_08_PlanAmexFetchPlanEmptyCustId -->
  <testcase name="TC005_14_PlanAmexFetchPlanDifferentPPI" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_14_PlanAmexFetchPlanDifferentPPI -->
  <testcase name="TC001_PlanAmexPositiveSendOtpBusiness" time="0.774" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex"/>
  <testcase name="TC005_01_PlanAmexFetchPlanEmptyMid" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_01_PlanAmexFetchPlanEmptyMid -->
  <testcase name="TC003_PlanAmexPositiveGetBusinessProfile" time="0.628" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex.TC003_PlanAmexPositiveGetBusinessProfile(FlowUpgradeMechantPlanAmex.java:108)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC003_PlanAmexPositiveGetBusinessProfile -->
  <testcase name="TC005_13_PlanAmexFetchPlanInvalidPPI" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_13_PlanAmexFetchPlanInvalidPPI -->
  <testcase name="TC005_02_PlanAmexFetchPlanInvalidMid" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_02_PlanAmexFetchPlanInvalidMid -->
  <testcase name="TC002_PlanAmexPositiveGetBusiness" time="0.439" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex"/>
  <testcase name="TC005_04_PlanAmexFetchPlanEmptyEntity" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_04_PlanAmexFetchPlanEmptyEntity -->
  <testcase name="TC005_03_PlanAmexFetchPlanNoMid" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_03_PlanAmexFetchPlanNoMid -->
  <testcase name="TC008_PlanAmexPositiveValidateOtpCreate" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC008_PlanAmexPositiveValidateOtpCreate -->
  <testcase name="TC004_03_PlanAmexFetchMIDWrongCustId" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_03_PlanAmexFetchMIDWrongCustId -->
  <testcase name="TC005_05_PlanAmexFetchPlanInvalidEntity" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_05_PlanAmexFetchPlanInvalidEntity -->
  <testcase name="TC004_01_PlanAmexFetchMIDInvalidCustId" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_01_PlanAmexFetchMIDInvalidCustId -->
  <testcase name="TC005_12_PlanAmexFetchPlanEmptyPPI" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_12_PlanAmexFetchPlanEmptyPPI -->
  <testcase name="TC004_05_PlanAmexFetchMIDInvalidReqType" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_05_PlanAmexFetchMIDInvalidReqType -->
  <testcase name="TC005_15_PlanAmexFetchPlanNoPPI" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_15_PlanAmexFetchPlanNoPPI -->
  <testcase name="TC019_PGCallBackFromPanel" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC019_PGCallBackFromPanel -->
  <testcase name="TC005_10_PlanAmexFetchPlanDifferentCustId" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_10_PlanAmexFetchPlanDifferentCustId -->
  <testcase name="TC005_18_PlanAmexFetchPlanDifferentEdcFlag" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_18_PlanAmexFetchPlanDifferentEdcFlag -->
  <testcase name="TC005_16_PlanAmexFetchPlanInvalidEdcFlag" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_16_PlanAmexFetchPlanInvalidEdcFlag -->
  <testcase name="TC005_09_PlanAmexFetchPlanInvalidCustId" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_09_PlanAmexFetchPlanInvalidCustId -->
  <testcase name="TC005_PlanAmexPositiveFetchUpgradePlans" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_PlanAmexPositiveFetchUpgradePlans -->
  <testcase name="TC007_PlanAmexPositiveSendOtpCreate" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC007_PlanAmexPositiveSendOtpCreate -->
  <testcase name="TC005_17_PlanAmexFetchPlanEmptyEdcFlag" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_17_PlanAmexFetchPlanEmptyEdcFlag -->
  <testcase name="TC006_PlanAmexPositiveFetchTnC" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC006_PlanAmexPositiveFetchTnC -->
  <testcase name="TC004_06_PlanAmexFetchMIDDIfferentReqType" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_06_PlanAmexFetchMIDDIfferentReqType -->
  <testcase name="TC005_06_PlanAmexFetchPlanDifferentEntity" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_06_PlanAmexFetchPlanDifferentEntity -->
</testsuite> <!-- OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex -->
