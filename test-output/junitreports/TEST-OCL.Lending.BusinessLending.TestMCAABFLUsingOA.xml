<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.TestMCAABFLUsingOA" tests="35" failures="0" timestamp="2023-05-16T14:51:25 IST" time="74.109" errors="1">
  <testcase name="TC020_FetchDataPostKYCIntiated" time="10.537" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC014_InitiateKYC_UsingSearchByPan" time="0.562" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC003_Create_MCA_V3_Piramal" time="1.693" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC018_LeadDataUpdateForKYC_InSAI" time="0.480" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC031_EmandateCallback" time="0.385" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC005_UpdateLeadBasicDetails" time="1.968" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC015_FetchDataPostKYCIntiated" time="1.525" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC021_FetchDataKYCInitiate" time="1.083" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC004_FetchLeadAllData" time="1.081" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC030_UpdateActualPanInSAI" time="0.390" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC029_AcceptLoanAgreement" time="1.291" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.878" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC012_UploadSelfie" time="2.835" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC032_FetchLeadPostEmandate" time="0.422" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC001_FetchLeadDetails_MCA_Piramal" time="1.194" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC006_FetchLeadAllData" time="1.377" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC033_VerifyLeadStage_LMSSubmitApplicationJob" time="3.420" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC019_InitiateKYC_UsingOfflineAAdhaar" time="0.595" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC009_FetchLeadAllData" time="21.957" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC013_VerifyUploadedSelfie" time="0.460" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC026_FetchLeadPostBankVerification" time="0.578" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC034_LMSDataCallback" time="1.078" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Lending.BusinessLending.TestMCAABFLUsingOA.TC034_LMSDataCallback(TestMCAABFLUsingOA.java:1363)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 30 more
]]>
    </error>
  </testcase> <!-- TC034_LMSDataCallback -->
  <testcase name="TC008_FetchCIR" time="1.528" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC022_SecondBRECallback" time="1.176" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC025_SaveBankDetails" time="0.724" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC035_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC035_FetchLeadAllData -->
  <testcase name="TC011_VerifyUploadedCustomerPhoto" time="0.786" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC027_GenerateLoanAgreement" time="1.439" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC002_DeleteExistingLead" time="0.510" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC024_UpdateKYCNameInSAI" time="0.784" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC010_uploadCustomerPhoto" time="3.222" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC016_InitiateKYC_UsingOfflineAAdhaar" time="0.651" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC028_GenerateSanctionLetter" time="2.623" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC017_FetchDataPostKYCIntiated" time="0.491" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
  <testcase name="TC023_FetchLead_AfterBRE2" time="4.386" classname="OCL.Lending.BusinessLending.TestMCAABFLUsingOA"/>
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAABFLUsingOA -->
