<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="22" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.MCAMigrationSSFB" tests="26" failures="1" timestamp="2022-05-09T19:01:17 IST" time="18.946" errors="0">
  <testcase name="TC002_DeleteExistingLead" time="0.480" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB"/>
  <testcase name="TC004_FetchLeadAllData" time="5.917" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_CREATED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_CREATED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.MCAMigrationSSFB.TC004_FetchLeadAllData(MCAMigrationSSFB.java:328)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC004_FetchLeadAllData -->
  <testcase name="TC021_AcceptLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC021_AcceptLoanAgreement -->
  <testcase name="TC005_UpdateLeadBasicDetails" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC005_UpdateLeadBasicDetails -->
  <testcase name="TC011_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC011_FetchLeadAllData -->
  <testcase name="TC009_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase name="TC001_FetchLeadDeatils" time="10.237" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB"/>
  <testcase name="TC022_EmandateCallback" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC022_EmandateCallback -->
  <testcase name="TC010_UpdateExistingDetailsInSAI" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC010_UpdateExistingDetailsInSAI -->
  <testcase name="TC017_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC017_UpdateKYCNameInSAI -->
  <testcase name="TC003_CreateBTDistributionPiramalLead" time="2.312" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB"/>
  <testcase name="TC016_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC016_VerifyLeadStage -->
  <testcase name="TC012_UploadSelfie" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC012_UploadSelfie -->
  <testcase name="TC018_SaveBankDetails" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC018_SaveBankDetails -->
  <testcase name="TC023_UploadSheetONPanel" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC023_UploadSheetONPanel -->
  <testcase name="TC019_GenerateLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC019_GenerateLoanAgreement -->
  <testcase name="TC026_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC026_FetchLeadAllData -->
  <testcase name="TC008_FetchCIR" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase name="TC013_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC013_UploadCustomerPhoto -->
  <testcase name="TC025_LMSDataCallback" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC025_LMSDataCallback -->
  <testcase name="TC006_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC006_FetchLeadAllData -->
  <testcase name="TC016_AdditionalDataCapture" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC016_AdditionalDataCapture -->
  <testcase name="TC014_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedDocument -->
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC007_UpdateBureauDataSetInSAI -->
  <testcase name="TC015_CKYCCallback" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC015_CKYCCallback -->
  <testcase name="TC020_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.BusinessLending.MCAMigrationSSFB">
    <skipped/>
  </testcase> <!-- TC020_GenerateSanctionLetter -->
</testsuite> <!-- OCL.Lending.BusinessLending.MCAMigrationSSFB -->
