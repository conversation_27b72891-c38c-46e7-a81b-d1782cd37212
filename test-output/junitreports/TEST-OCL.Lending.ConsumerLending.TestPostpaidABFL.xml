<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="ip-192-168-29-76.ap-south-1.compute.internal" failures="0" tests="33" name="OCL.Lending.ConsumerLending.TestPostpaidABFL" time="102.497" errors="0" timestamp="11 May 2021 12:42:51 GMT" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC012_VerifyLeadStage" time="0.691"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC015_UploadCustomerPhoto" time="3.000"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC002_FetchLeadDeatils" time="0.798"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC007_AddBasicDetails" time="0.695"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC006_FetchTheCreatedLeadDeatils" time="0.686"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC023_CheckBREResponse" time="62.149"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC032_LoanStatusCallbackAfterSubmitApplication" time="11.256"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC026_VerifyLeadStage" time="0.643"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC018_VerifyLeadStage" time="0.771"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC008_FetchTheLeadDeatils" time="0.697"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC017_CKYCCallback" time="0.776"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC025_AddAddress" time="0.670"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC013_UploadSelfie" time="3.194"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC030_VerifyLeadStage" time="0.728"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC001_DeleteExistingLead" time="0.804"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC031_SubmitApplication" time="0.833"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC019_BREValidationPendingCallback" time="0.688"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC027_FetchDynamicTnc" time="1.068"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC010_FetchLeadStage" time="0.702"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC024_VerifyLeadStage" time="0.748"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC003_CreatePostpaidLead" time="1.374"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC020_VerifyLeadStage" time="0.691"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC029_FetchDynamicTncSanctionLetter" time="0.630"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC005_PPBLOTPCallback" time="1.719"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC021_FetchBREResponse" time="0.706"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC014_VerifyUploadedDocument" time="0.762"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC016_VerifyUploadedDocument" time="0.652"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC011_CheckCKYCStatus" time="0.765"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC022_VerifyLeadStage" time="0.628"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC004_FetchTheCreatedLeadDeatils" time="0.979"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC009_OTPCallback" time="0.654"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC033_VerifyLeadStage" time="0.612"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFL" name="TC028_VerifyLeadStage" time="0.728"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostpaidABFL -->
