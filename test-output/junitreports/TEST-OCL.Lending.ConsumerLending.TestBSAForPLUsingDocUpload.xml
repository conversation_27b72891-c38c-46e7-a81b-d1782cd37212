<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="25" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload" tests="28" failures="1" timestamp="2023-06-26T17:29:02 IST" time="3.118" errors="0">
  <testcase name="TC013_UploadThirdankingProof" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC013_UploadThirdankingProof -->
  <testcase name="TC014_VerifyUploadedDocIsVisibleInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedDocIsVisibleInFetchLead -->
  <testcase name="TC006_FetchLeadAllDataUsingALLDATA" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC006_FetchLeadAllDataUsingALLDATA -->
  <testcase name="TC015_UploadFourthBankingProof" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC015_UploadFourthBankingProof -->
  <testcase name="TC008_VerifyUploadedDocIsVisibleInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC008_VerifyUploadedDocIsVisibleInFetchLead -->
  <testcase name="TC021_VerifyDocumentDeletion" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC021_VerifyDocumentDeletion -->
  <testcase name="TC024_VerifyDocumentUploadedStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC024_VerifyDocumentUploadedStage -->
  <testcase name="TC020_VerifyUploadedDocIsVisibleInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC020_VerifyUploadedDocIsVisibleInFetchLead -->
  <testcase name="TC023_VerifyUploadedDocIsVisibleInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC023_VerifyUploadedDocIsVisibleInFetchLead -->
  <testcase name="TC009_UploadAlreadyUploadedDocument_CanBeUploadedAgain" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC009_UploadAlreadyUploadedDocument_CanBeUploadedAgain -->
  <testcase name="TC003_CreateHLDistributionPiramalLead" time="1.065" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_CREATED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_CREATED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload.TC003_CreateHLDistributionPiramalLead(TestBSAForPLUsingDocUpload.java:212)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC003_CreateHLDistributionPiramalLead -->
  <testcase name="TC007_UploadFirstBankingProof" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC007_UploadFirstBankingProof -->
  <testcase name="TC005_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC005_SaveBankDetails -->
  <testcase name="TC027_MoveTheLead_To_BSACompleted" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC027_MoveTheLead_To_BSACompleted -->
  <testcase name="TC016_VerifyUploadedDocIsVisibleInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC016_VerifyUploadedDocIsVisibleInFetchLead -->
  <testcase name="TC012_VerifyUploadedDocIsVisibleInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedDocIsVisibleInFetchLead -->
  <testcase name="TC026_MoveTheLead_To_BankVerificationSuccessStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC026_MoveTheLead_To_BankVerificationSuccessStage -->
  <testcase name="TC028_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC028_FetchLeadAllData -->
  <testcase name="TC019_UploadSixthBankingProof" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC019_UploadSixthBankingProof -->
  <testcase name="TC022_UploadSixthBankingProof" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC022_UploadSixthBankingProof -->
  <testcase name="TC025_MoveTheLead_To_BSADocUploadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC025_MoveTheLead_To_BSADocUploadStage -->
  <testcase name="TC010_VerifyUploadedDocDetailsAreUpdatedInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC010_VerifyUploadedDocDetailsAreUpdatedInFetchLead -->
  <testcase name="TC011_UploadSecondBankingProof" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC011_UploadSecondBankingProof -->
  <testcase name="TC004_FetchLeadAllDataUsingBASICDATA" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC004_FetchLeadAllDataUsingBASICDATA -->
  <testcase name="TC001_FetchLeadDeatils" time="1.456" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload"/>
  <testcase name="TC002_DeleteExistingLead" time="0.597" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload"/>
  <testcase name="TC018_VerifyUploadedDocIsVisibleInFetchLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC018_VerifyUploadedDocIsVisibleInFetchLead -->
  <testcase name="TC017_UploadFifthBankingProof" time="0.000" classname="OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload">
    <skipped/>
  </testcase> <!-- TC017_UploadFifthBankingProof -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestBSAForPLUsingDocUpload -->
