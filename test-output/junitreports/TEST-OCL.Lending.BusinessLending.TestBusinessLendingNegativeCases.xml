<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="1" tests="175" name="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" time="14.954" errors="0" timestamp="2021-07-05T14:47:46 IST" skipped="154">
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC009_CreateMCALead_WithoutPassing_LoanProcessionFees" time="1.499"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC007_CreateMCALead_WithoutPassing_Channel" time="0.682"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC062_UpdateGenderAndPincodeDetails_WithoutPassing_StatusMessage_InRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC062_UpdateGenderAndPincodeDetails_WithoutPassing_StatusMessage_InRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC124_UploadSelfie_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC124_UploadSelfie_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC164_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC164_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC143_CKYCCallback_Passing_InvalidDOBInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC143_CKYCCallback_Passing_InvalidDOBInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC084_FetchBREResponse_Without_Passing_Channel" time="0.000">
    <skipped/>
  </testcase> <!-- TC084_FetchBREResponse_Without_Passing_Channel -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC103_BRECallbackAPI" time="0.000">
    <skipped/>
  </testcase> <!-- TC103_BRECallbackAPI -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC005_CreateMCALead_WithoutPassing_SolutionTypeLevel2" time="0.768"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC074_BREOTPVerification_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC074_BREOTPVerification_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC082_FetchBREResponse_Without_Passing_SolutionType" time="0.000">
    <skipped/>
  </testcase> <!-- TC082_FetchBREResponse_Without_Passing_SolutionType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC132_UploadCustomerPhoto_WithoutPassing_SolutionTypeLevel2_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC132_UploadCustomerPhoto_WithoutPassing_SolutionTypeLevel2_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC107_OTPCallback_Without_Passing_LeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC107_OTPCallback_Without_Passing_LeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC154_SaveBankDetails_WithoutPassingChannel" time="0.000">
    <skipped/>
  </testcase> <!-- TC154_SaveBankDetails_WithoutPassingChannel -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC075_BREValidationPending_Without_Passing_LeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC075_BREValidationPending_Without_Passing_LeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC134_UploadCustomerPhoto_WithoutPassing_SessionToken_InHeaders" time="0.000">
    <skipped/>
  </testcase> <!-- TC134_UploadCustomerPhoto_WithoutPassing_SessionToken_InHeaders -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC012_CreateMCALead_WithoutPassing_LoanTenure" time="0.716"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC157_SaveBankDetails_WithoutPassingBankAccountNumber" time="0.000">
    <skipped/>
  </testcase> <!-- TC157_SaveBankDetails_WithoutPassingBankAccountNumber -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC002_FetchLeadDeatils" time="1.119"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC0176_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC0176_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC049_UpdatePANAndDOBDetails_Without_Passing_solution" time="0.000">
    <skipped/>
  </testcase> <!-- TC049_UpdatePANAndDOBDetails_Without_Passing_solution -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC136_UploadCustomerPhoto_HappyCase" time="0.001">
    <skipped/>
  </testcase> <!-- TC136_UploadCustomerPhoto_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC160_SaveAlreadyAddedBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC160_SaveAlreadyAddedBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC030_FetchTheCreatedLeadDeatils" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_FetchTheCreatedLeadDeatils -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC159_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC159_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC158_SaveBankDetails_WithoutPassingIFSC" time="0.000">
    <skipped/>
  </testcase> <!-- TC158_SaveBankDetails_WithoutPassingIFSC -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC004_CreateMCALead_WithoutPassing_Solution" time="0.632"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC109_OTPCallback_Without_Passing_Status_In_RequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC109_OTPCallback_Without_Passing_Status_In_RequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC022_FetchTheCreatedLeadDeatils" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_FetchTheCreatedLeadDeatils -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC057_UpdatePANAndDOBDetails_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC057_UpdatePANAndDOBDetails_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC112_FetchLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC112_FetchLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC101_BRECallbackAPI_Without_Passing_Status_In_Request_Body" time="0.000">
    <skipped/>
  </testcase> <!-- TC101_BRECallbackAPI_Without_Passing_Status_In_Request_Body -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC100_BRECallbackAPI_Without_Passing_Status_In_Request_Body" time="0.000">
    <skipped/>
  </testcase> <!-- TC100_BRECallbackAPI_Without_Passing_Status_In_Request_Body -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC174_EmandateCallback_PassingBlankStatus" time="0.000">
    <skipped/>
  </testcase> <!-- TC174_EmandateCallback_PassingBlankStatus -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC042_AddBasicDetails_WithouPassing_PAN_VALUE_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC042_AddBasicDetails_WithouPassing_PAN_VALUE_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC096_BRECallbackAPI_Without_Passing_Authorization_Token_In_headers" time="0.000">
    <skipped/>
  </testcase> <!-- TC096_BRECallbackAPI_Without_Passing_Authorization_Token_In_headers -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC102_BRECallbackAPI_Passing_Status_AsFailure_In_Request_Body" time="0.000">
    <skipped/>
  </testcase> <!-- TC102_BRECallbackAPI_Passing_Status_AsFailure_In_Request_Body -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC066_UpdateGenderAndPincodeDetails_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC066_UpdateGenderAndPincodeDetails_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC032_AddBasicDetails_WithouPassing_SolutionTypeLevel2" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_AddBasicDetails_WithouPassing_SolutionTypeLevel2 -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC149_CKYCCallback_AgainHitAfterSuccessCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC149_CKYCCallback_AgainHitAfterSuccessCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC116_CheckCKYCStatus_Without_Passing_SeesionToken_InHeaders" time="0.000">
    <skipped/>
  </testcase> <!-- TC116_CheckCKYCStatus_Without_Passing_SeesionToken_InHeaders -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC060_UpdateGenderAndPincodeDetails_Without_Passing_AuthToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC060_UpdateGenderAndPincodeDetails_Without_Passing_AuthToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC038_AddBasicDetails_WithouPassing_DOB_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC038_AddBasicDetails_WithouPassing_DOB_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC108_OTPCallback_Without_Passing_AuthorizationToken_InRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC108_OTPCallback_Without_Passing_AuthorizationToken_InRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC165_SubmitApplication_WithoutPassingQueryParam" time="0.000">
    <skipped/>
  </testcase> <!-- TC165_SubmitApplication_WithoutPassingQueryParam -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC051_UpdatePANAndDOBDetails_Without_Passing_CustId" time="0.000">
    <skipped/>
  </testcase> <!-- TC051_UpdatePANAndDOBDetails_Without_Passing_CustId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC151_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC151_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC024_PPBLOTPCallback_WithoutPassing_LeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_PPBLOTPCallback_WithoutPassing_LeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC145_CKYCCallback_Passing_BlankGenderInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC145_CKYCCallback_Passing_BlankGenderInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC168_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC168_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC088_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC088_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC110_OTPCallback_Passing_Invalid_Status_In_RequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC110_OTPCallback_Passing_Invalid_Status_In_RequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC031_AddBasicDetails_WithouPassing_EntityType" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_AddBasicDetails_WithouPassing_EntityType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC114_CheckCKYCStatus_Without_Passing_SolutionType" time="0.000">
    <skipped/>
  </testcase> <!-- TC114_CheckCKYCStatus_Without_Passing_SolutionType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC026_PPBLOTPCallback_WithoutPassing_AuthorizationToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_PPBLOTPCallback_WithoutPassing_AuthorizationToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC139_CKYCCallback_WithoutPassing_LeadId_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC139_CKYCCallback_WithoutPassing_LeadId_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC117_CheckCKYCStatus_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC117_CheckCKYCStatus_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC169_EmandateCallback_WithoutPassingLeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC169_EmandateCallback_WithoutPassingLeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC098_BRECallbackAPI_Without_Passing_StatusMessage_In_Request_Body" time="0.000">
    <skipped/>
  </testcase> <!-- TC098_BRECallbackAPI_Without_Passing_StatusMessage_In_Request_Body -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC052_UpdatePANAndDOBDetails_Without_Passing_StatusMessage" time="0.000">
    <skipped/>
  </testcase> <!-- TC052_UpdatePANAndDOBDetails_Without_Passing_StatusMessage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC046_AddBasicDetails_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC046_AddBasicDetails_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC121_UploadSelfie_Without_Passing_SolutionTypeInQueryParams" time="0.000">
    <skipped/>
  </testcase> <!-- TC121_UploadSelfie_Without_Passing_SolutionTypeInQueryParams -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC035_AddBasicDetails_WithouPassing_Channel" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_AddBasicDetails_WithouPassing_Channel -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC037_AddBasicDetails_WithouPassing_workflowSubOperation_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_AddBasicDetails_WithouPassing_workflowSubOperation_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC025_PPBLOTPCallback_WithoutPassing_solution" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_PPBLOTPCallback_WithoutPassing_solution -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC131_UploadCustomerPhoto_Passing_InvalidSolutionType_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC131_UploadCustomerPhoto_Passing_InvalidSolutionType_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC059_UpdateGenderAndPincodeDetails_WithoutPassing_Solution" time="0.000">
    <skipped/>
  </testcase> <!-- TC059_UpdateGenderAndPincodeDetails_WithoutPassing_Solution -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC027_PPBLOTPCallback_Without_Passing_custid_Header" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_PPBLOTPCallback_Without_Passing_custid_Header -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC079_BREValidationPending_Without_Passing_StatusInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC079_BREValidationPending_Without_Passing_StatusInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC093_CheckBREResponse_Without_Passing_sessionToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC093_CheckBREResponse_Without_Passing_sessionToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC020_CreateMCALead_WithoutPassing_PRODUCT_ID" time="0.601"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC081_BREValidationPending_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC081_BREValidationPending_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC072_BREOTPVerification_Passing_Invalid_StatusInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC072_BREOTPVerification_Passing_Invalid_StatusInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC152_SaveBankDetails_WithoutPassingSolutionName" time="0.000">
    <skipped/>
  </testcase> <!-- TC152_SaveBankDetails_WithoutPassingSolutionName -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC010_CreateMCALead_WithoutPassing_LoanRateOfInterest" time="0.546"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC015_CreateMCALead_WithoutPassing_LoanAmountInWords" time="0.622"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC0177_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC0177_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC138_CKYCCallback_WithoutPassing_SolutionType_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC138_CKYCCallback_WithoutPassing_SolutionType_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC018_CreateMCALead_WithoutPassing_PRODUCT_TYPE" time="0.588"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC122_UploadSelfie_Without_Passing_SolutionTypeLevel2InQueryParams" time="0.000">
    <skipped/>
  </testcase> <!-- TC122_UploadSelfie_Without_Passing_SolutionTypeLevel2InQueryParams -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC017_CreateMCALead_WithoutPassing_BASE_ID" time="0.768"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC006_CreateMCALead_WithoutPassing_SolutionTypeLevel3" time="0.557"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC148_CKYCCallback_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC148_CKYCCallback_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC083_FetchBREResponse_Without_Passing_SolutionType" time="0.000">
    <skipped/>
  </testcase> <!-- TC083_FetchBREResponse_Without_Passing_SolutionType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC085_FetchBREResponse_Without_Passing_SessionToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC085_FetchBREResponse_Without_Passing_SessionToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC054_UpdatePANAndDOBDetails_Without_Passing_Status" time="0.000">
    <skipped/>
  </testcase> <!-- TC054_UpdatePANAndDOBDetails_Without_Passing_Status -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC080_BREValidationPending_Passing_Invalid_StatusInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC080_BREValidationPending_Passing_Invalid_StatusInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC142_CKYCCallback_WuthoutPassing_StatusInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC142_CKYCCallback_WuthoutPassing_StatusInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC041_AddBasicDetails_WithIncorrectFormatFor_PAN_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC041_AddBasicDetails_WithIncorrectFormatFor_PAN_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC162_FetchDynamicTnc" time="0.000">
    <skipped/>
  </testcase> <!-- TC162_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC008_CreateMCALead_WithoutPassing_sessionToken_Header" time="0.521"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC115_CheckCKYCStatus_Without_Passing_Channel" time="0.000">
    <skipped/>
  </testcase> <!-- TC115_CheckCKYCStatus_Without_Passing_Channel -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC173_EmandateCallback_PassingInvalidStatus" time="0.000">
    <skipped/>
  </testcase> <!-- TC173_EmandateCallback_PassingInvalidStatus -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC172_EmandateCallback_WithoutPassingCustIdInHeaders" time="0.000">
    <skipped/>
  </testcase> <!-- TC172_EmandateCallback_WithoutPassingCustIdInHeaders -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC111_OTPCallback_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC111_OTPCallback_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC104_BRECallbackAPI_HittingTheAPI_Again" time="0.000">
    <skipped/>
  </testcase> <!-- TC104_BRECallbackAPI_HittingTheAPI_Again -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC171_EmandateCallback_WithoutPassingJWTToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC171_EmandateCallback_WithoutPassingJWTToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC001_DeleteExistingLead_ForMCAClix" time="0.735"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC053_UpdatePANAndDOBDetails_Passing_Invalid_StatusMessage" time="0.000">
    <skipped/>
  </testcase> <!-- TC053_UpdatePANAndDOBDetails_Passing_Invalid_StatusMessage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC058_UpdateGenderAndPincodeDetails_WithoutPassing_LeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC058_UpdateGenderAndPincodeDetails_WithoutPassing_LeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC170_EmandateCallback_WithoutPassingSolutionName" time="0.000">
    <skipped/>
  </testcase> <!-- TC170_EmandateCallback_WithoutPassingSolutionName -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC013_CreateMCALead_WithoutPassing_LoanEquatedDailyInstallment" time="0.589"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC089_CheckBREResponse_Without_Passing_SolutionType" time="0.000">
    <skipped/>
  </testcase> <!-- TC089_CheckBREResponse_Without_Passing_SolutionType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC150_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC150_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC161_FetchDynamicTnc_WithoutPassingLeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC161_FetchDynamicTnc_WithoutPassingLeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC039_AddBasicDetails_WithIncorrectFormatFor_EMAIL_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC039_AddBasicDetails_WithIncorrectFormatFor_EMAIL_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC056_UpdatePANAndDOBDetails_Passing_DOBValidationStatus_AsFalse" time="0.000">
    <skipped/>
  </testcase> <!-- TC056_UpdatePANAndDOBDetails_Passing_DOBValidationStatus_AsFalse -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC016_CreateMCALead_WithoutPassing_IS_PAYTM_VINTAGE_OLDER_THAN_90D" time="0.660"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC153_SaveBankDetails_WithoutPassingEntityName" time="0.000">
    <skipped/>
  </testcase> <!-- TC153_SaveBankDetails_WithoutPassingEntityName -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC064_UpdateGenderAndPincodeDetails_WithoutPassing_Status_InRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC064_UpdateGenderAndPincodeDetails_WithoutPassing_Status_InRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC086_FetchBREResponse" time="0.000">
    <skipped/>
  </testcase> <!-- TC086_FetchBREResponse -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC014_CreateMCALead_WithoutPassing_LoanInterestAmount" time="0.546"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC133_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC133_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC077_BREValidationPending_Without_Passing_AuthorizationToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC077_BREValidationPending_Without_Passing_AuthorizationToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC163_FetchDynamicTncSanctionLetter" time="0.001">
    <skipped/>
  </testcase> <!-- TC163_FetchDynamicTncSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC029_PPBLOTPCallback_HappyCase" time="0.001">
    <skipped/>
  </testcase> <!-- TC029_PPBLOTPCallback_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC129_UploadCustomerPhoto_WithoutPassing_DocProvided_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC129_UploadCustomerPhoto_WithoutPassing_DocProvided_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC141_CKYCCallback_WithoutPassing_custIdInHeaders" time="0.000">
    <skipped/>
  </testcase> <!-- TC141_CKYCCallback_WithoutPassing_custIdInHeaders -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC043_AddBasicDetails_WithouPassing_EMAIL_VALUE_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC043_AddBasicDetails_WithouPassing_EMAIL_VALUE_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC126_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams" time="0.000">
    <skipped/>
  </testcase> <!-- TC126_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC092_CheckBREResponse_Without_Passing_Channel" time="0.000">
    <skipped/>
  </testcase> <!-- TC092_CheckBREResponse_Without_Passing_Channel -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC147_CKYCCallback_Passing_BlankSelfieMatcgPercenatgenRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC147_CKYCCallback_Passing_BlankSelfieMatcgPercenatgenRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC175_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC175_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC130_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC130_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC028_PPBLOTPCallback_Without_StatusKey_InRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_PPBLOTPCallback_Without_StatusKey_InRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC113_CheckCKYCStatus_Without_Passing_EntityType" time="0.000">
    <skipped/>
  </testcase> <!-- TC113_CheckCKYCStatus_Without_Passing_EntityType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC055_UpdatePANAndDOBDetails_PassingStatusAs_Failure" time="0.000">
    <skipped/>
  </testcase> <!-- TC055_UpdatePANAndDOBDetails_PassingStatusAs_Failure -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC033_AddBasicDetails_WithouPassing_Solution" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_AddBasicDetails_WithouPassing_Solution -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC045_AddBasicDetails_WithIncorrectFormatFor_DOB_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC045_AddBasicDetails_WithIncorrectFormatFor_DOB_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC021_CreateMCALead_HappyCase" time="0.584">
    <failure message="did not expect to find [Lead successfully created.] but found [null]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases.TC021_CreateMCALead_HappyCase(TestBusinessLendingNegativeCases.java:1222)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- TC021_CreateMCALead_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC036_AddBasicDetails_WithouPassing_sessionToken_InHeaders" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_AddBasicDetails_WithouPassing_sessionToken_InHeaders -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC063_UpdateGenderAndPincodeDetails_Passing_Invalid_StatusMessage_InRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC063_UpdateGenderAndPincodeDetails_Passing_Invalid_StatusMessage_InRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC095_BRECallbackAPI_Without_Passing_LeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC095_BRECallbackAPI_Without_Passing_LeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC166_SubmitApplication_WithoutPassingSessionToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC166_SubmitApplication_WithoutPassingSessionToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC137_VerifyUploadedDocument" time="0.000">
    <skipped/>
  </testcase> <!-- TC137_VerifyUploadedDocument -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC069_BREOTPVerification_Without_Passing_AuthToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC069_BREOTPVerification_Without_Passing_AuthToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC070_BREOTPVerification_Without_Passing_CustId" time="0.000">
    <skipped/>
  </testcase> <!-- TC070_BREOTPVerification_Without_Passing_CustId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC003_CreateMCALead_WithoutPassing_EntityType" time="1.117"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC119_UploadSelfie_PassingInvalid_LeadId_InQueryParams" time="0.000">
    <skipped/>
  </testcase> <!-- TC119_UploadSelfie_PassingInvalid_LeadId_InQueryParams -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC044_AddBasicDetails_WithouPassing_DOB_VALUE_InBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC044_AddBasicDetails_WithouPassing_DOB_VALUE_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC023_PPBLOTPCallback_Without_Passing_Headers" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_PPBLOTPCallback_Without_Passing_Headers -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC091_CheckBREResponse_Without_Passing_EntityType" time="0.000">
    <skipped/>
  </testcase> <!-- TC091_CheckBREResponse_Without_Passing_EntityType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC065_UpdateGenderAndPincodeDetails_PassingStatusAs_Failure" time="0.000">
    <skipped/>
  </testcase> <!-- TC065_UpdateGenderAndPincodeDetails_PassingStatusAs_Failure -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC128_UploadCustomerPhoto_Paasing_InvalidLeadId_InURL" time="0.000">
    <skipped/>
  </testcase> <!-- TC128_UploadCustomerPhoto_Paasing_InvalidLeadId_InURL -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC067_BREOTPVerification_Without_Passing_LeadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC067_BREOTPVerification_Without_Passing_LeadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC050_UpdatePANAndDOBDetails_Without_Passing_AuthToken" time="0.000">
    <skipped/>
  </testcase> <!-- TC050_UpdatePANAndDOBDetails_Without_Passing_AuthToken -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC061_UpdateGenderAndPincodeDetails_WithoutPassing_CustId" time="0.000">
    <skipped/>
  </testcase> <!-- TC061_UpdateGenderAndPincodeDetails_WithoutPassing_CustId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC011_CreateMCALead_WithoutPassing_LoanAmountNumber" time="0.618"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC135_UploadCustomerPhoto_WithoutPassing_ImageFile" time="0.000">
    <skipped/>
  </testcase> <!-- TC135_UploadCustomerPhoto_WithoutPassing_ImageFile -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC140_CKYCCallback_WithoutPassing_sessionTokenInHeaders" time="0.000">
    <skipped/>
  </testcase> <!-- TC140_CKYCCallback_WithoutPassing_sessionTokenInHeaders -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC105_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC105_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC106_OTPCallback_Without_Passing_Solution_Type" time="0.000">
    <skipped/>
  </testcase> <!-- TC106_OTPCallback_Without_Passing_Solution_Type -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC048_UpdatePANAndDOBDetails_Without_Passing_leadId" time="0.000">
    <skipped/>
  </testcase> <!-- TC048_UpdatePANAndDOBDetails_Without_Passing_leadId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC097_BRECallbackAPI_Without_Passing_CustId_In_headers" time="0.000">
    <skipped/>
  </testcase> <!-- TC097_BRECallbackAPI_Without_Passing_CustId_In_headers -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC125_UploadSelfie_WithoutPassing_ImageFile" time="0.000">
    <skipped/>
  </testcase> <!-- TC125_UploadSelfie_WithoutPassing_ImageFile -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC078_BREValidationPending_Without_Passing_CustId" time="0.000">
    <skipped/>
  </testcase> <!-- TC078_BREValidationPending_Without_Passing_CustId -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC146_CKYCCallback_Passing_InvalidPanFormatInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC146_CKYCCallback_Passing_InvalidPanFormatInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC127_UploadSelfie_HappyCase" time="0.000">
    <skipped/>
  </testcase> <!-- TC127_UploadSelfie_HappyCase -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC087_FetchBREResponse_When_HittingTheAPIAgain" time="0.000">
    <skipped/>
  </testcase> <!-- TC087_FetchBREResponse_When_HittingTheAPIAgain -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC019_CreateMCALead_WithoutPassing_PRODUCT_VERSION" time="0.481"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC076_BREValidationPending_Without_Passing_Solution" time="0.000">
    <skipped/>
  </testcase> <!-- TC076_BREValidationPending_Without_Passing_Solution -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC123_UploadSelfie_Without_Passing_SolutionTypeLevel3InQueryParams" time="0.000">
    <skipped/>
  </testcase> <!-- TC123_UploadSelfie_Without_Passing_SolutionTypeLevel3InQueryParams -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC073_BREOTPVerification_Passing_Blank_StatusInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC073_BREOTPVerification_Passing_Blank_StatusInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC099_BRECallbackAPI_Passing_Invalid_StatusMessage_In_Request_Body" time="0.000">
    <skipped/>
  </testcase> <!-- TC099_BRECallbackAPI_Passing_Invalid_StatusMessage_In_Request_Body -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC047_FetchTheCreatedLeadDeatils" time="0.000">
    <skipped/>
  </testcase> <!-- TC047_FetchTheCreatedLeadDeatils -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC040_AddBasicDetails_WithouPassing_PAN_InBody" time="0.001">
    <skipped/>
  </testcase> <!-- TC040_AddBasicDetails_WithouPassing_PAN_InBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC094_BRECallbackAPI_Without_Passing_SolutionType" time="0.000">
    <skipped/>
  </testcase> <!-- TC094_BRECallbackAPI_Without_Passing_SolutionType -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC120_UploadSelfie_Without_Passing_DocProvidedInQueryParams" time="0.000">
    <skipped/>
  </testcase> <!-- TC120_UploadSelfie_Without_Passing_DocProvidedInQueryParams -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC156_SaveBankDetails_WithoutPassingSessionTokenInHeaders" time="0.000">
    <skipped/>
  </testcase> <!-- TC156_SaveBankDetails_WithoutPassingSessionTokenInHeaders -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC071_BREOTPVerification_WithoutPassing_StatusInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC071_BREOTPVerification_WithoutPassing_StatusInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC144_CKYCCallback_Passing_BlankDOBInRequestBody" time="0.000">
    <skipped/>
  </testcase> <!-- TC144_CKYCCallback_Passing_BlankDOBInRequestBody -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC034_AddBasicDetails_WithouPassing_SolutionTypeLevel3" time="0.001">
    <skipped/>
  </testcase> <!-- TC034_AddBasicDetails_WithouPassing_SolutionTypeLevel3 -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC118_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC118_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC167_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC167_SubmitApplication -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases" name="TC068_BREOTPVerification_Without_Passing_Solution" time="0.000">
    <skipped/>
  </testcase> <!-- TC068_BREOTPVerification_Without_Passing_Solution -->
</testsuite> <!-- OCL.Lending.BusinessLending.TestBusinessLendingNegativeCases -->
