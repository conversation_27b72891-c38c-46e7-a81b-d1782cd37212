<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="12" hostname="197NODMB24984.local" name="OCL.Individual.QrMapping.FlowCustomizedQr" tests="13" failures="0" timestamp="2022-07-13T13:25:47 IST" time="0.396" errors="1">
  <testcase name="SendOtpCustomizedQr" time="0.396" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.QrMapping.FlowCustomizedQr.SendOtpCustomizedQr(FlowCustomizedQr.java:86)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- SendOtpCustomizedQr -->
  <testcase name="FetchMidCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- FetchMidCustomizedQr -->
  <testcase name="ValidateOtpCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- ValidateOtpCustomizedQr -->
  <testcase name="GetMerchantCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- GetMerchantCustomizedQr -->
  <testcase name="SubmitLeadCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- SubmitLeadCustomizedQr -->
  <testcase name="MakePaymentCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- MakePaymentCustomizedQr -->
  <testcase name="UpdateLeadCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- UpdateLeadCustomizedQr -->
  <testcase name="FetchAndSubmitDocsCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- FetchAndSubmitDocsCustomizedQr -->
  <testcase name="FetchLeadPanelCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- FetchLeadPanelCustomizedQr -->
  <testcase name="RejectedLeadPanelCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- RejectedLeadPanelCustomizedQr -->
  <testcase name="ApproveLeadPanelCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- ApproveLeadPanelCustomizedQr -->
  <testcase name="UdateLeadStatusCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- UdateLeadStatusCustomizedQr -->
  <testcase name="PersonalizedQrFileUploadCustomizedQr" time="0.000" classname="OCL.Individual.QrMapping.FlowCustomizedQr">
    <skipped/>
  </testcase> <!-- PersonalizedQrFileUploadCustomizedQr -->
</testsuite> <!-- OCL.Individual.QrMapping.FlowCustomizedQr -->
