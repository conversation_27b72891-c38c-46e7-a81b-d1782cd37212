<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="0" tests="38" name="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" time="131.935" errors="0" timestamp="2022-09-22T19:14:15 IST" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC002_PLv3Hero_DeleteExistingLead" time="1.158"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC015_PLv3HERO_FetchDataPostLoanOfferAccept" time="0.914"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC016_PLv3HERO_UploadCustomerPhoto" time="3.105"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC022_PLv3HERO_SecondBREInitiated" time="0.818"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC026_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.737"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC038_PLv3HERO_SubmitApplication" time="1.144"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC037_PLv3HERO_GenerateSanctionLetter" time="1.089"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC013_PLv3HERO_FetchDataPostBRE1Success" time="6.102"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC034_PLv3HERO_EmandateCallback" time="1.790"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC021_PLv3HERO_FetchDataPostPanVerified" time="0.772"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC006_PLv3HERO_FetchDataPostBasicDetailUpdate" time="1.295"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC027_PLv3HERO_AdditionalDataCapture" time="1.524"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC025_PLv3HERO_AdditionalIsRequiredorNot" time="5.868"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC029_PLv3HERO_BRE3Success" time="1.306"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC035_PLv3HERO_FetchLeadPostEmandate" time="5.816"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC012_PLv3HERO_BRE1Callback" time="16.111"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC004_PLV3Hero_FetchLeadAllData" time="1.808"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC010_PLv3HERO_FetchDataPostSAIlUpdate" time="0.947"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC007_PLV3Hero_UpdateLeadOccupationDetails" time="6.496"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC033_PLv3HERO_FetchLeadPostBankVerification" time="7.612"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC040_PLv3HERO_FetchLeadAgentAllocationJob" time="5.736"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC031_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.657"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC003_PLv3Hero_CreateLead" time="1.288"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC011_PLv3HERO_FetchCIR" time="8.490"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC030_PLv3HERO_UpdateKYCNameInSAI" time="6.244"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC008_PLv3HERO_FetchDataPostOccupationDetailUpdate" time="6.237"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC005_PLV3Hero_UpdateLeadBasicDetails" time="6.411"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC014_PLv3HERO_LoanOfferAccept" time="1.038"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC018_PLv3HERO_UploadSelfie" time="3.066"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC019_PLv3HERO_VerifyUploadedSelfie" time="0.758"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC032_PLv3HERO_SaveBankDetails" time="3.336"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC020_PLv3HERO_CKYCCallback" time="6.046"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="C036_PLv3HERO_GenerateLoanAgreement" time="1.380"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC023_PLv3HERO_SecondBRECallback" time="1.301"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC001_PLv3Hero_fetchlLead" time="5.962"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC017_PLv3HERO_VerifyUploadedCustomerPhoto" time="5.994"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC009_PLV3Hero_UpdateLeadDetailsinSAI" time="0.789"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO" name="TC024_PLv3HERO_FetchDataAfterBRE2Success" time="0.790"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestQCScenariosForPLv3HERO -->
