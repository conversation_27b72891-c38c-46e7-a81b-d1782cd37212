<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="41" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.TestMCAWithSSFB" tests="44" failures="2" timestamp="2022-06-27T17:32:43 IST" time="4.202" errors="0">
  <testcase name="TC026_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC026_VerifyLeadStage -->
  <testcase name="TC027_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC027_UpdateBureauDataSetInSAI -->
  <testcase name="TC018_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC018_VerifyLeadStage -->
  <testcase name="TC025_CKYCCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC025_CKYCCallback -->
  <testcase name="TC015_FetchBREResponse" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC015_FetchBREResponse -->
  <testcase name="TC024_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC024_VerifyUploadedDocument -->
  <testcase name="TC021_UploadSelfie" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC021_UploadSelfie -->
  <testcase name="TC030_CheckBRE2Response" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC030_CheckBRE2Response -->
  <testcase name="TC034_SaveBankDetails" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC034_SaveBankDetails -->
  <testcase name="TC035_FetchDynamicTnc" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC035_FetchDynamicTnc -->
  <testcase name="TC019_CheckCKYCStatus" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC019_CheckCKYCStatus -->
  <testcase name="TC013_BREValidationPending" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC013_BREValidationPending -->
  <testcase name="TC006_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC006_FetchTheCreatedLeadDeatils -->
  <testcase name="TC032_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC032_VerifyLeadStage -->
  <testcase name="TC042_PDCCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC042_PDCCallback -->
  <testcase name="TC044_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC044_VerifyLeadStage -->
  <testcase name="TC031_AdditionalDataCaptureCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC031_AdditionalDataCaptureCallback -->
  <testcase name="TC037_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC037_VerifyLeadStage -->
  <testcase name="TC007_AddBasicDetails" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC007_AddBasicDetails -->
  <testcase name="TC010_UpdateGenderAndPincodeDetails" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC010_UpdateGenderAndPincodeDetails -->
  <testcase name="TC012_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC012_FetchTheCreatedLeadDeatils -->
  <testcase name="TC023_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC023_UploadCustomerPhoto -->
  <testcase name="TC020_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC020_VerifyLeadStage -->
  <testcase name="TC038_SubmitApplication" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC038_SubmitApplication -->
  <testcase name="TC001_DeleteExistingLead_ForSSFB" time="0.816" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB"/>
  <testcase name="TC004_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC004_FetchTheCreatedLeadDeatils -->
  <testcase name="TC029_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC029_VerifyLeadStage -->
  <testcase name="TC014_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC014_VerifyLeadStage -->
  <testcase name="TC016_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC016_VerifyLeadStage -->
  <testcase name="TC022_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC022_VerifyUploadedDocument -->
  <testcase name="TC003_CreateSSFBLead" time="0.895" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <failure type="java.lang.AssertionError" message="did not expect to find [Lead successfully created.] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.TestMCAWithSSFB.TC003_CreateSSFBLead(TestMCAWithSSFB.java:164)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC003_CreateSSFBLead -->
  <testcase name="TC017_CheckBREResponse" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC017_CheckBREResponse -->
  <testcase name="TC008_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC008_FetchTheCreatedLeadDeatils -->
  <testcase name="TC009_UpdatePANAndDOBDetails" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC009_UpdatePANAndDOBDetails -->
  <testcase name="TC043_UploadSheetONPanel" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC043_UploadSheetONPanel -->
  <testcase name="TC005_PPBLOTPCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC005_PPBLOTPCallback -->
  <testcase name="TC041_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC041_VerifyLeadStage -->
  <testcase name="TC002_FetchLeadDeatils" time="2.491" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_NOT_PRESENT] but found [BRE_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_NOT_PRESENT] but found [BRE_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at Services.LendingService.LendingBaseAPI.fetchTheCurrentLeadStage(LendingBaseAPI.java:506)
at OCL.Lending.BusinessLending.TestMCAWithSSFB.TC002_FetchLeadDeatils(TestMCAWithSSFB.java:104)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC002_FetchLeadDeatils -->
  <testcase name="TC033_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC033_UpdateKYCNameInSAI -->
  <testcase name="TC036_FetchDynamicTncSanctionLetter" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC036_FetchDynamicTncSanctionLetter -->
  <testcase name="TC039_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC039_VerifyLeadStage -->
  <testcase name="TC011_BREOTPVerification" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC011_BREOTPVerification -->
  <testcase name="TC028_FetchBRE2Response" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC028_FetchBRE2Response -->
  <testcase name="TC040_EmandateCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestMCAWithSSFB">
    <skipped/>
  </testcase> <!-- TC040_EmandateCallback -->
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAWithSSFB -->
