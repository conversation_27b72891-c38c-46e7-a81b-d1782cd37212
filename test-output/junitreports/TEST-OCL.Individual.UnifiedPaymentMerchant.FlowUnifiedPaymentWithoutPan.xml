<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan" tests="12" failures="1" timestamp="2022-07-13T13:25:47 IST" time="84.880" errors="0">
  <testcase name="TC0011_03_CreateAccount" time="0.411" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan.TC0011_03_CreateAccount(FlowUnifiedPaymentWithoutPan.java:314)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0011_03_CreateAccount -->
  <testcase name="TC0001_CreateApplicantOauth" time="1.479" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0002_GetApplicantToken" time="1.535" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0008_03_CreateAdditionalDetails" time="0.397" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0003_11_FetchDetails" time="0.567" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0010_PGCallBackforInsatntMid" time="72.074" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0006_11_ValidateBankDetails" time="1.241" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0004_10_CreateLeadUMO" time="0.691" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0007_05_UpdateBankDetails" time="0.803" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0008_01_UpdatingBusiness" time="5.321" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
  <testcase name="TC0012_PgCallBackUMO" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan">
    <skipped/>
  </testcase> <!-- TC0012_PgCallBackUMO -->
  <testcase name="TC0005_16_CreateBusiness" time="0.361" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan"/>
</testsuite> <!-- OCL.Individual.UnifiedPaymentMerchant.FlowUnifiedPaymentWithoutPan -->
