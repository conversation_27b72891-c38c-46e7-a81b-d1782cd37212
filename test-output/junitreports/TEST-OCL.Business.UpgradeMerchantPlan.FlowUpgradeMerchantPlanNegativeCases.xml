<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="11" hostname="197NODMB24984.local" name="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases" tests="14" failures="0" timestamp="2022-07-13T12:28:08 IST" time="2.064" errors="1">
  <testcase name="TC007_UpgradeMerchantPositiveSendOtpCreate" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC007_UpgradeMerchantPositiveSendOtpCreate -->
  <testcase name="TC009_AmexPlanValidateOtp" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC009_AmexPlanValidateOtp -->
  <testcase name="TC013_CashAtPosPlanFetchTerminal" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC013_CashAtPosPlanFetchTerminal -->
  <testcase name="TC011_EmiOfferingPlanValidateOtp" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC011_EmiOfferingPlanValidateOtp -->
  <testcase name="TC001_UpgradeMerchantPositiveSendOtpBusiness" time="0.760" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases"/>
  <testcase name="TC012_EdcAmcPlanValidateOtp" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC012_EdcAmcPlanValidateOtp -->
  <testcase name="TC014_PaymentConfirmationPlanFetchTerminal" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC014_PaymentConfirmationPlanFetchTerminal -->
  <testcase name="TC006_UpgradeMerchantPositiveFetchTnC" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC006_UpgradeMerchantPositiveFetchTnC -->
  <testcase name="TC002_UpgradeMerchantPositiveGetBusiness" time="0.440" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases"/>
  <testcase name="TC010_InternationCardPlanValidateOtp" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC010_InternationCardPlanValidateOtp -->
  <testcase name="TC003_UpgradeMerchantPositiveGetBusinessProfile" time="0.864" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases.TC003_UpgradeMerchantPositiveGetBusinessProfile(FlowUpgradeMerchantPlanNegativeCases.java:108)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC003_UpgradeMerchantPositiveGetBusinessProfile -->
  <testcase name="TC004_UpgradeMerchantPositiveFetchMID" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC004_UpgradeMerchantPositiveFetchMID -->
  <testcase name="TC005_UpgradeMerchantPositiveFetchUpgradePlans" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC005_UpgradeMerchantPositiveFetchUpgradePlans -->
  <testcase name="TC008_GroceryPlanValidateOtp" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC008_GroceryPlanValidateOtp -->
</testsuite> <!-- OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases -->
