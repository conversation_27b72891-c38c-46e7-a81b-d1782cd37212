<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="43" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero" tests="47" failures="2" timestamp="2022-04-21T19:50:20 IST" time="2.810" errors="1">
  <testcase name="TC010_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC010_FetchTheCreatedLeadDeatils -->
  <testcase name="TC017_FetchBREResponse" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC017_FetchBREResponse -->
  <testcase name="TC045_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC045_VerifyLeadStage -->
  <testcase name="TC012_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC012_FetchTheCreatedLeadDeatils -->
  <testcase name="TC034_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC034_VerifyLeadStage -->
  <testcase name="TC032_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC032_VerifyLeadStage -->
  <testcase name="TC047_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC047_VerifyLeadStage -->
  <testcase name="TC015_BREValidationPending" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC015_BREValidationPending -->
  <testcase name="TC003_CreatePersonalLoanLead" time="0.705" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <failure type="java.lang.AssertionError" message="did not expect to find [Lead successfully created.] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPersonalLoanWithHero.TC003_CreatePersonalLoanLead(TestPersonalLoanWithHero.java:126)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC003_CreatePersonalLoanLead -->
  <testcase name="TC008_AdiitionalInfoDOBUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC008_AdiitionalInfoDOBUpdate -->
  <testcase name="TC004_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC004_FetchTheCreatedLeadDeatils -->
  <testcase name="TC021_AcceptLoanOffer" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC021_AcceptLoanOffer -->
  <testcase name="TC007_AdiitionalInfoPANUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC007_AdiitionalInfoPANUpdate -->
  <testcase name="TC037_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC037_UpdateKYCNameInSAI -->
  <testcase name="TC016_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC016_VerifyLeadStage -->
  <testcase name="TC027_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC027_UploadCustomerPhoto -->
  <testcase name="TC014_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC014_VerifyLeadStage -->
  <testcase name="TC031_SecondBREInitiateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC031_SecondBREInitiateCallback -->
  <testcase name="TC009_BasicDetailsUpdateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC009_BasicDetailsUpdateCallback -->
  <testcase name="TC038_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC038_SaveBankDetails -->
  <testcase name="TC018_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC018_VerifyLeadStage -->
  <testcase name="TC019_CheckBREResponse" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC019_CheckBREResponse -->
  <testcase name="TC040_EmandateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC040_EmandateCallback -->
  <testcase name="TC025_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC025_UploadSelfie -->
  <testcase name="TC013_BREOTPVerification" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC013_BREOTPVerification -->
  <testcase name="TC026_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC026_VerifyUploadedDocument -->
  <testcase name="TC041_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC041_VerifyLeadStage -->
  <testcase name="TC023_CheckCKYCStatus" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC023_CheckCKYCStatus -->
  <testcase name="TC042_FetchDynamicTnc" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC042_FetchDynamicTnc -->
  <testcase name="TC044_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC044_SubmitApplication -->
  <testcase name="TC039_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC039_VerifyLeadStage -->
  <testcase name="TC020_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC020_VerifyLeadStage -->
  <testcase name="TC035_AdditionalDataCapture" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC035_AdditionalDataCapture -->
  <testcase name="TC033_SecondBREStatusCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC033_SecondBREStatusCallback -->
  <testcase name="TC011_OccupationDetailsCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC011_OccupationDetailsCallback -->
  <testcase name="TC028_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC028_VerifyUploadedDocument -->
  <testcase name="TC022_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC022_VerifyLeadStage -->
  <testcase name="TC046_UploadSheetONPanel" time="0.908" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:460)
at java.util.Properties.setProperty(Properties.java:166)
at Services.MechantService.MiddlewareServices.v1Token(MiddlewareServices.java:1166)
at OCL.Lending.ConsumerLending.TestPersonalLoanWithHero.TC046_UploadSheetONPanel(TestPersonalLoanWithHero.java:1196)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </error>
  </testcase> <!-- TC046_UploadSheetONPanel -->
  <testcase name="TC036_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC036_VerifyLeadStage -->
  <testcase name="TC029_CKYCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC029_CKYCCallback -->
  <testcase name="TC005_PPBLOTPCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC005_PPBLOTPCallback -->
  <testcase name="TC001_DeleteExistingLead" time="0.486" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero"/>
  <testcase name="TC002_FetchLeadDeatils" time="0.710" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_NOT_PRESENT] but found [BRE_RESPONSE_AWAITED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_NOT_PRESENT] but found [BRE_RESPONSE_AWAITED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at Services.LendingService.LendingBaseAPI.fetchTheCurrentLeadStage(LendingBaseAPI.java:503)
at OCL.Lending.ConsumerLending.TestPersonalLoanWithHero.TC002_FetchLeadDeatils(TestPersonalLoanWithHero.java:90)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC002_FetchLeadDeatils -->
  <testcase name="TC043_FetchDynamicTncSanctionLetter" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC043_FetchDynamicTncSanctionLetter -->
  <testcase name="TC024_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC024_VerifyLeadStage -->
  <testcase name="TC030_VerifyLeadStage" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC030_VerifyLeadStage -->
  <testcase name="TC006_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanWithHero">
    <skipped/>
  </testcase> <!-- TC006_FetchTheCreatedLeadDeatils -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanWithHero -->
