<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="18" hostname="197NODMB24984.local" name="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan" tests="96" failures="29" timestamp="2022-07-13T13:25:47 IST" time="41.752" errors="1">
  <testcase name="TC0001_CreateApplicantOauth" time="1.386" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0002_GetApplicantToken" time="1.165" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0003_FetchDetails" time="0.323" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0004_FetchDetails" time="0.308" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0005_FetchDetails" time="0.373" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0006_FetchDetails" time="0.610" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0007_FetchDetails" time="0.636" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0008_CreateLead" time="0.428" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0009_CreateLead" time="0.387" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00010_CreateLead" time="0.385" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00011_CreateLead" time="0.712" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00012_FetchDetails" time="0.660" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00013_CreateLead" time="0.369" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00014_SaveBusiness" time="0.381" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00015_SaveBusiness" time="0.368" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00016_SaveBusiness" time="0.326" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00017_SaveBusiness" time="0.413" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00018_SaveBusiness" time="0.621" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00019_FetchDetails" time="0.524" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00020_AdditionalDetails" time="0.381" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00021_AdditionalDetails" time="0.336" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00022_AdditionalDetails" time="0.525" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0023_AdditonalDetails" time="0.407" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00024_FetchDetails" time="0.502" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0025_SaveIdentityDetails" time="0.314" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0025_SaveIdentityDetails(FlowUnifiedPaymentWithPan.java:663)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0025_SaveIdentityDetails -->
  <testcase name="TC0026_SaveIdentityDetails" time="1.984" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Update request Submitted Successfully]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Update request Submitted Successfully]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0026_SaveIdentityDetails(FlowUnifiedPaymentWithPan.java:688)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0026_SaveIdentityDetails -->
  <testcase name="TC0027_SaveIdentityDetails" time="1.228" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0028_SaveIdentityDetails" time="1.200" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0029_SaveIdentityDetails" time="0.452" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0030_SaveIdentityDetails" time="1.652" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00031_FetchDetails" time="0.694" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0032_SaveIdentityPANDetails" time="0.430" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0032_SaveIdentityPANDetails(FlowUnifiedPaymentWithPan.java:842)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0032_SaveIdentityPANDetails -->
  <testcase name="TC0033_SaveIdentityPANDetails" time="0.376" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0034_SaveIdentityPANDetails" time="0.519" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0034_SaveIdentityPANDetails(FlowUnifiedPaymentWithPan.java:890)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0034_SaveIdentityPANDetails -->
  <testcase name="TC0035_SaveIdentityPANDetails" time="0.530" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0035_SaveIdentityPANDetails(FlowUnifiedPaymentWithPan.java:915)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0035_SaveIdentityPANDetails -->
  <testcase name="TC0036_SaveIdentityPANDetails" time="0.483" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0036_SaveIdentityPANDetails(FlowUnifiedPaymentWithPan.java:940)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0036_SaveIdentityPANDetails -->
  <testcase name="TC0037_SaveIdentityPANDetails" time="0.476" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Invalid Request For Update] but found [Update request Submitted Successfully]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0037_SaveIdentityPANDetails(FlowUnifiedPaymentWithPan.java:965)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0037_SaveIdentityPANDetails -->
  <testcase name="TC0038_SaveIdentityPANDetails" time="0.416" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0038_SaveIdentityPANDetails(FlowUnifiedPaymentWithPan.java:990)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0038_SaveIdentityPANDetails -->
  <testcase name="TC0039_SaveIdentityPANDetails" time="0.462" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0040_SaveIdentityPANDetails" time="0.422" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [Invalid Request For Update] but found [Name entered does not match with the given PAN]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Invalid Request For Update] but found [Name entered does not match with the given PAN]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0040_SaveIdentityPANDetails(FlowUnifiedPaymentWithPan.java:1041)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0040_SaveIdentityPANDetails -->
  <testcase name="TC0041_SaveIdentityPANDetails" time="0.391" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00042_FetchDetails" time="0.563" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0043_ValidateBank" time="0.302" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0044_ValidateBank" time="0.315" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0045_ValidateBank" time="0.428" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0046_ValidateBank" time="0.359" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0047_ValidateBank" time="0.891" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="expected [null] but found [Name Match failed for bank details]">
      <![CDATA[java.lang.AssertionError: expected [null] but found [Name Match failed for bank details]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotSame(Assert.java:965)
at org.testng.Assert.assertNull(Assert.java:898)
at org.testng.Assert.assertNull(Assert.java:886)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0047_ValidateBank(FlowUnifiedPaymentWithPan.java:1245)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0047_ValidateBank -->
  <testcase name="TC0048_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0048_SaveBankDetails -->
  <testcase name="TC0049_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0049_SaveBankDetails -->
  <testcase name="TC0050_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0050_SaveBankDetails -->
  <testcase name="TC0051_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0051_SaveBankDetails -->
  <testcase name="TC0052_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0052_SaveBankDetails -->
  <testcase name="TC0053_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0053_SaveBankDetails -->
  <testcase name="TC00054_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC00054_FetchDetails -->
  <testcase name="TC0055_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0055_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0056_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0056_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0057_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0057_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0058_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0058_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC00059_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC00059_FetchDetails -->
  <testcase name="TC0060_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0060_CreateAccountForUPM_V2 -->
  <testcase name="TC0061_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0061_CreateAccountForUPM_V2 -->
  <testcase name="TC0062_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0062_CreateAccountForUPM_V2 -->
  <testcase name="TC0063_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0063_CreateAccountForUPM_V2 -->
  <testcase name="TC0064_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC0064_CreateAccountForUPM_V2 -->
  <testcase name="TC00065_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <skipped/>
  </testcase> <!-- TC00065_FetchDetails -->
  <testcase name="TC00066_FetchDetails" time="0.479" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00067_FetchDetails" time="0.408" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00068_CreateLead" time="0.318" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00069_CreateLead" time="0.360" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00069_CreateLead(FlowUnifiedPaymentWithPan.java:1791)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00069_CreateLead -->
  <testcase name="TC00070_CreateLead" time="0.581" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [Lead successfully created.] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00070_CreateLead(FlowUnifiedPaymentWithPan.java:1816)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00070_CreateLead -->
  <testcase name="TC00071_CreateLead" time="0.457" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [Lead successfully created.] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00071_CreateLead(FlowUnifiedPaymentWithPan.java:1839)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00071_CreateLead -->
  <testcase name="TC00072_SaveBusiness" time="0.325" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00072_SaveBusiness(FlowUnifiedPaymentWithPan.java:1862)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00072_SaveBusiness -->
  <testcase name="TC00073_SaveBusiness" time="0.352" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00074_SaveBusiness" time="0.346" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00074_SaveBusiness(FlowUnifiedPaymentWithPan.java:1910)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00074_SaveBusiness -->
  <testcase name="TC00075_SaveBusiness" time="0.365" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00076_FetchDetails" time="0.781" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [404] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [404] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00076_FetchDetails(FlowUnifiedPaymentWithPan.java:1952)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00076_FetchDetails -->
  <testcase name="TC00077_FetchDetails" time="0.390" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [404] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [404] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00077_FetchDetails(FlowUnifiedPaymentWithPan.java:1970)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00077_FetchDetails -->
  <testcase name="TC00078_AdditionalDetails" time="0.354" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00079_AdditionalDetails" time="0.344" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC00080_AdditionalDetails" time="0.378" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC00080_AdditionalDetails(FlowUnifiedPaymentWithPan.java:2059)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00080_AdditionalDetails -->
  <testcase name="TC0081_SaveIdentityDetails" time="0.359" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0081_SaveIdentityDetails(FlowUnifiedPaymentWithPan.java:2085)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0081_SaveIdentityDetails -->
  <testcase name="TC0082_SaveIdentityDetails" time="0.356" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0082_SaveIdentityDetails(FlowUnifiedPaymentWithPan.java:2111)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0082_SaveIdentityDetails -->
  <testcase name="TC0083_SaveIdentityDetails" time="0.342" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0083_SaveIdentityDetails(FlowUnifiedPaymentWithPan.java:2137)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0083_SaveIdentityDetails -->
  <testcase name="TC0084_SaveIdentityDetails" time="0.404" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0085_ValidateBank" time="0.885" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [404] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [404] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0085_ValidateBank(FlowUnifiedPaymentWithPan.java:2191)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0085_ValidateBank -->
  <testcase name="TC0086_ValidateBank" time="0.827" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [404] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [404] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0086_ValidateBank(FlowUnifiedPaymentWithPan.java:2219)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0086_ValidateBank -->
  <testcase name="TC0087_ValidateBank" time="0.866" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [404] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [404] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0087_ValidateBank(FlowUnifiedPaymentWithPan.java:2247)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0087_ValidateBank -->
  <testcase name="TC0088_ValidateBank" time="0.487" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your Bank details. Please try again.] but found [We could not verify your Bank Details. Please ensure Bank Account and IFSC code entered are valid and try again. ]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your Bank details. Please try again.] but found [We could not verify your Bank Details. Please ensure Bank Account and IFSC code entered are valid and try again. ]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0088_ValidateBank(FlowUnifiedPaymentWithPan.java:2276)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0088_ValidateBank -->
  <testcase name="TC0089_ValidateBank" time="0.575" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not verify your Bank Details. Please ensure Bank Account and IFSC code entered are valid and try again.] but found [We could not verify your Bank Details. Please ensure Bank Account and IFSC code entered are valid and try again. ]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not verify your Bank Details. Please ensure Bank Account and IFSC code entered are valid and try again.] but found [We could not verify your Bank Details. Please ensure Bank Account and IFSC code entered are valid and try again. ]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0089_ValidateBank(FlowUnifiedPaymentWithPan.java:2304)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0089_ValidateBank -->
  <testcase name="TC0090_ValidateBank" time="1.103" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0090_ValidateBank(FlowUnifiedPaymentWithPan.java:2331)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0090_ValidateBank -->
  <testcase name="TC0091_SaveBankDetails" time="0.092" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <error type="java.lang.RuntimeException" message="Method type and path are not specified for: SaveBankUPM">
      <![CDATA[java.lang.RuntimeException: Method type and path are not specified for: SaveBankUPM
at com.paytm.apitools.core.AbstractApi.init(AbstractApi.java:76)
at com.paytm.apitools.core.AbstractApi.<init>(AbstractApi.java:46)
at com.paytm.apitools.core.AbstractApiV2.<init>(AbstractApiV2.java:47)
at com.paytm.apitools.core.AbstractApiV2.<init>(AbstractApiV2.java:68)
at Request.MerchantService.v1.sdMerchant.saveBank.SaveBankUPM.<init>(SaveBankUPM.java:11)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0091_SaveBankDetails(FlowUnifiedPaymentWithPan.java:2341)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0091_SaveBankDetails -->
  <testcase name="TC0092_SaveRefreeCodeAndWhatsappNotification" time="0.330" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0093_SaveRefreeCodeAndWhatsappNotification" time="0.403" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0094_SaveRefreeCodeAndWhatsappNotification" time="0.310" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0094_SaveRefreeCodeAndWhatsappNotification(FlowUnifiedPaymentWithPan.java:2434)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0094_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0095_CreateAccountForUPM_V2" time="0.446" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan"/>
  <testcase name="TC0096_CreateAccountForUPM_V2" time="0.316" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan.TC0096_CreateAccountForUPM_V2(FlowUnifiedPaymentWithPan.java:2481)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0096_CreateAccountForUPM_V2 -->
</testsuite> <!-- OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithPan -->
