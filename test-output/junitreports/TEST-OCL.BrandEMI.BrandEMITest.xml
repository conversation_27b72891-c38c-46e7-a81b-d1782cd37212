<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="8" hostname="************" name="OCL.BrandEMI.BrandEMITest" tests="27" failures="1" timestamp="2022-08-23T08:35:14 IST" time="35.365" errors="0">
  <testcase name="TC05_CompanyOnboardWithoutDoc" time="0.658" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC28_EditMerchantonPGWithoutCreatedBY" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC28_EditMerchantonPGWithoutCreatedBY -->
  <testcase name="TC08_CompanyOnboardWithoutCompanyType" time="0.844" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC26_EditMerchantonPGWithoutMID" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC26_EditMerchantonPGWithoutMID -->
  <testcase name="TC06_CompanyOnboardWithoutRole" time="0.617" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC19_CheckCreatedMerchantonPG" time="3.162" classname="OCL.BrandEMI.BrandEMITest">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.BrandEMI.BrandEMIBaseAPi.verifyResponseCodeAs200OK(BrandEMIBaseAPi.java:144)
at OCL.BrandEMI.BrandEMITest.TC19_CheckCreatedMerchantonPG(BrandEMITest.java:1531)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC19_CheckCreatedMerchantonPG -->
  <testcase name="TC04_CompanyOnboardWithoutPan" time="0.456" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC14_CreateMerchantonPGWithoutRequestID" time="1.278" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC03_CompanyOnboardWithoutCustId" time="0.768" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC15_CreateMerchantonPGWithoutRequestIDSourceID" time="0.806" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC27_EditMerchantonPGWithoutBusinessName" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC27_EditMerchantonPGWithoutBusinessName -->
  <testcase name="TC16_CreateMerchantonPG" time="15.803" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC22_EditMerchantonPGWithourSourceID" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC22_EditMerchantonPGWithourSourceID -->
  <testcase name="TC25_EditMerchantonPGWithoutCustID" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC25_EditMerchantonPGWithoutCustID -->
  <testcase name="TC24_EditMerchantonPGWithoutRequestSourceID" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC24_EditMerchantonPGWithoutRequestSourceID -->
  <testcase name="TC02_FetchUserId" time="0.667" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC10_CompanyOnboardWithoutEntityType" time="0.564" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC09_CompanyOnboardWithoutCompanyFlag" time="0.437" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC20_EditMerchantonPGWithoutHeaders" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC20_EditMerchantonPGWithoutHeaders -->
  <testcase name="TC01_CreateUser" time="2.460" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC11_CompanyOnboardWithoutPANName" time="0.687" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC17_CreateMerchantonPGWithoutKYB" time="1.004" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC13_CreateMerchantonPGWithoutRequestID" time="1.854" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC07_CompanyOnboardWithInvalidPan" time="0.656" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC12_CompanyOnboard" time="1.748" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC23_EditMerchantonPGWithoutRequestID" time="0.000" classname="OCL.BrandEMI.BrandEMITest">
    <skipped/>
  </testcase> <!-- TC23_EditMerchantonPGWithoutRequestID -->
  <testcase name="TC18_CheckCreatedMerchantWithoutToken" time="0.896" classname="OCL.BrandEMI.BrandEMITest"/>
</testsuite> <!-- OCL.BrandEMI.BrandEMITest -->
