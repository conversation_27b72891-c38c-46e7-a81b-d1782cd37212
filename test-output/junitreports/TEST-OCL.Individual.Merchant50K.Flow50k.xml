<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Individual.Merchant50K.Flow50k" tests="22" failures="1" timestamp="2022-07-13T13:25:47 IST" time="13.981" errors="0">
  <testcase name="TC00016_createAdditionalDetails50kWithSpecialCharecterDisplayName" time="0.429" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0007_createBusiness50kEmptyCategoryAndSubCategory" time="0.393" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00018_validateBankDetailsTestWithInvalidAccountNumber" time="0.432" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00015_createAdditionalDetails50kWithInvalidDisplayName" time="0.558" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00019_validateBankDetailsTestWithInvalidIFSCCode" time="0.348" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00020_validateBankDetailsTest" time="0.911" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0004_createLead50kInvalidPan" time="0.509" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0002_getApplicantToken" time="1.168" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00017_createAdditionalDetails50k" time="0.767" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00014_createBusiness50k" time="0.585" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0008_createBusiness50kCategoryEmptyAndSubCategoryNotEmpty" time="0.283" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0005_createLead50kOtherEntityType" time="0.359" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00010_createBusiness50kEntityTypeIsEmpty" time="0.387" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0006_createLead50k" time="0.615" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00013_createBusiness50kBuisnessIsNameIsLessThan2" time="0.317" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00011_createBusiness50kInvalidEntitytype" time="0.398" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0009_createBusiness50kCategorynotEmptyAndSubCategoryEmpty" time="0.456" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC0003_createLead50kNullPan" time="0.490" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00021_updateBankDetailsTest" time="2.206" classname="OCL.Individual.Merchant50K.Flow50k">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.Merchant50K.Flow50k.TC00021_updateBankDetailsTest(Flow50k.java:610)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC00021_updateBankDetailsTest -->
  <testcase name="TC0022_PGCallBackforInsatntMid" time="0.000" classname="OCL.Individual.Merchant50K.Flow50k">
    <skipped/>
  </testcase> <!-- TC0022_PGCallBackforInsatntMid -->
  <testcase name="TC0001_CreateApplicantOauth" time="2.023" classname="OCL.Individual.Merchant50K.Flow50k"/>
  <testcase name="TC00012_createBusiness50kEntitytypeIsPublicLimited" time="0.347" classname="OCL.Individual.Merchant50K.Flow50k"/>
</testsuite> <!-- OCL.Individual.Merchant50K.Flow50k -->
