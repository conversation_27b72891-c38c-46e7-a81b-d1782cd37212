<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="6" name="OCL.CommonOnboarding.EDC.GetMerchantBank" time="5.456" errors="0" timestamp="2023-11-07T14:13:50 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank" name="TC_6_GetBankErrorWithoutDeviceIdentifer" time="0.364"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank" name="test" time="0.260"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank" name="TC_1_getExistingLeadBankDetailsUPIFalse" time="2.866"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank" name="TC_5_GetBankErrorWithoutVersion" time="0.330"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank" name="TC_4_GetBankErrorWithoutSessionToken" time="0.310"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank" name="TC_3_GetBankWithUPIFetchTrue" time="1.326"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.GetMerchantBank -->
