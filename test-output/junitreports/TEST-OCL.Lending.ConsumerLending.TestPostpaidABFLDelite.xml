<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="0" tests="33" name="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" time="91.774" errors="1" timestamp="2021-07-05T14:47:46 IST" skipped="10">
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC004_FetchTheCreatedLeadDeatils" time="1.305"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC025_AddAddress" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_AddAddress -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC015_UploadCustomerPhoto" time="3.841"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC022_VerifyLeadStage" time="0.874"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC031_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC033_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC016_VerifyUploadedDocument" time="0.743"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC013_UploadSelfie" time="3.266"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC008_FetchTheLeadDeatils" time="0.890"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC009_OTPCallback" time="0.811"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC012_VerifyLeadStage" time="1.272"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC018_VerifyLeadStage" time="0.860"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC021_FetchBREResponse" time="0.795"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC024_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC026_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC027_FetchDynamicTnc" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC006_FetchTheCreatedLeadDeatils" time="0.782"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC019_BREValidationPendingCallback" time="0.899"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC023_CheckBREResponse" time="64.776">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.base/java.math.BigInteger.<init>(BigInteger.java:479)
at java.base/java.math.BigInteger.<init>(BigInteger.java:672)
at OCL.Lending.ConsumerLending.TestPostpaidABFLDelite.TC023_CheckBREResponse(TestPostpaidABFLDelite.java:591)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </error>
  </testcase> <!-- TC023_CheckBREResponse -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC011_CheckCKYCStatus" time="0.919"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC003_CreatePostpaidLead" time="1.680"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC017_CKYCCallback" time="1.591"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC002_FetchLeadDeatils" time="0.656"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC032_LoanStatusCallbackAfterSubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_LoanStatusCallbackAfterSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC014_VerifyUploadedDocument" time="0.740"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC001_DeleteExistingLead" time="0.636"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC005_PPBLOTPCallback" time="1.679"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC007_AddBasicDetails" time="1.379"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC028_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC010_FetchLeadStage" time="0.719"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC020_VerifyLeadStage" time="0.661"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC029_FetchDynamicTncSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_FetchDynamicTncSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLDelite" name="TC030_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_VerifyLeadStage -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostpaidABFLDelite -->
