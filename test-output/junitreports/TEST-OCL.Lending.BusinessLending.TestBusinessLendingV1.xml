<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="1" tests="33" name="OCL.Lending.BusinessLending.TestBusinessLendingV1" time="4.845" errors="0" timestamp="22 Apr 2021 07:18:18 GMT" skipped="30">
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC022_UploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_UploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC026_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC002_FetchLeadDeatils" time="1.302"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC028_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC014_CheckBREResponse" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_CheckBREResponse -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC033_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC006_AddBasicDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC006_AddBasicDetails -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC005_PPBLOTPCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC005_PPBLOTPCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC030_FetchDynamicTncSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_FetchDynamicTncSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC021_VerifyUploadedDocument" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_VerifyUploadedDocument -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC010_BREValidationPending" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_BREValidationPending -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC031_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_SubmitApplication -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC012_FetchBREResponse" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_FetchBREResponse -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC003_CreateBusinessLendingLead" time="2.273">
    <failure message="expected [Lead successfully created.] but found [Request already in progress, please check status after some time]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [Lead successfully created.] but found [Request already in progress, please check status after some time]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:453)
at org.testng.Assert.assertEquals(Assert.java:463)
at OCL.Lending.BusinessLending.TestBusinessLendingV1.TC003_CreateBusinessLendingLead(TestBusinessLendingV1.java:162)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- TC003_CreateBusinessLendingLead -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC019_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC001_DeleteExistingLead" time="1.270"/>
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC007_UpdatePANAndDOBDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC007_UpdatePANAndDOBDetails -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC016_OTPCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_OTPCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC024_CKYCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_CKYCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC018_CheckCKYCStatus" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_CheckCKYCStatus -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC015_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC017_FetchLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_FetchLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC025_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC008_UpdateGenderAndPincodeDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC008_UpdateGenderAndPincodeDetails -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC027_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC009_BREOTPVerification" time="0.000">
    <skipped/>
  </testcase> <!-- TC009_BREOTPVerification -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC029_FetchDynamicTnc" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC011_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC004_FetchTheCreatedLeadDeatils" time="0.000">
    <skipped/>
  </testcase> <!-- TC004_FetchTheCreatedLeadDeatils -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC032_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC020_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC013_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestBusinessLendingV1" name="TC023_VerifyUploadedDocument" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_VerifyUploadedDocument -->
</testsuite> <!-- OCL.Lending.BusinessLending.TestBusinessLendingV1 -->
