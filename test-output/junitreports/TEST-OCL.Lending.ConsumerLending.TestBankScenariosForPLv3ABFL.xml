<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="0" tests="41" name="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" time="259.343" errors="0" timestamp="2022-09-22T19:14:20 IST" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC001_PLv3ABFL_fetchlLead" time="1.288"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC040_PLv3ABFL_EmandateCallback" time="0.753"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC010_PLv3ABFL_FetchDataPostSAIlUpdate" time="0.900"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC005_PLV3ABFL_UpdateLeadBasicDetails" time="1.878"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC021_PLv3ABFL_FetchDataPostPanVerified" time="0.923"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC002_PLv3ABFL_DeleteExistingLead" time="0.725"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC039_PLv3ABFL_SaveBankDetailsAgain" time="0.813"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC013_PLv3ABFL_FetchDataPostBRE1Success" time="58.364"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC024_PLv3ABFL_FetchDataAfterBRE2Success" time="5.838"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC036_PLv3ABFL_UpdateKYCNameInSAIForNewBankValidation" time="3.831"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC028_PLv3ABFL_BRE3Success" time="1.202"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC017_PLv3ABFL_VerifyUploadedCustomerPhoto" time="5.989"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC023_PLv3ABFL_SecondBRECallback" time="1.323"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC019_PLv3ABFL_VerifyUploadedSelfie" time="0.834"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC018_PLv3ABFL_UploadSelfie" time="3.230"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC006_PLv3ABFL_FetchDataPostBasicDetailUpdate" time="6.345"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC003_PLv3ABFL_CreateLead" time="6.520"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC030_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.805"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC011_PLv3ABFL_FetchCIR" time="13.420"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC014_PLv3ABFL_LoanOfferAccept" time="13.051"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC008_PLv3ABFL_FetchDataPostOccupationDetailUpdate" time="6.321"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC027_PLv3ABFL_AdditionalDataCapture" time="1.329"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC012_PLv3ABFL_BRE1Callback" time="21.962"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC016_PLv3ABFL_UploadCustomerPhoto" time="32.076"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC004_PLV3ABFL_FetchLeadAllData" time="1.858"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC025_PLv3ABFL_AdditionalIsRequiredorNot" time="0.812"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC033_PLv3ABFL_BankValidationPending" time="7.648"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC032_PLv3ABFL_FetchLeadAndValidatePennyDropFailedDetails" time="1.073"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC015_PLv3ABFL_FetchDataPostLoanOfferAccept" time="12.301"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC007_PLV3ABFL_UpdateLeadOccupationDetails" time="6.051"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC035_PLv3ABFL_BANKING_ACTION_DONE" time="6.683"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC031_PLv3ABFL_SaveBankDetailsPennyDropfailed" time="1.115"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC022_PLv3ABFL_SecondBREInitiated" time="0.764"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC041_PLv3ABFL_SaveBankDetailsPostEmandateCallback" time="5.761"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC034_PLv3ABFL_UpdateKYCNameInSAIPostBANK_VALIDATION_PENDING" time="2.927"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC038_PLv3ABFL_VerifyPennydropForNewBankDetails" time="0.980"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC026_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.856"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC037_PLv3ABFL_UpdateNewBankDetails" time="7.299"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC029_PLv3ABFL_UpdateKYCNameInSAI" time="6.404"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC020_PLv3ABFL_CKYCCallback" time="6.258"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL" name="TC009_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.833"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestBankScenariosForPLv3ABFL -->
