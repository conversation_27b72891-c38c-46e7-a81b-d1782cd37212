<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="24" name="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" time="36.477" errors="0" timestamp="2023-09-08T17:49:18 IST" skipped="12">
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC010_FetchLeadAllData_AfterBureauSuccess" time="3.808"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC003_Create_PL_AXIS_DISTRIBUTION_Lead" time="0.816"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC012_LoanOfferAccept" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_LoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC016_KYCStatusForETBUser" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_KYCStatusForETBUser -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC013_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC021_AcceptLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC022_CheckLoanOnboardingCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_CheckLoanOnboardingCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC006_FetchLeadAllData" time="1.164"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC007_UpdateOccupationDetails" time="0.955"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC017_FetchLead" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_FetchLead -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC001_FetchLeadDetails_PL_AXIS" time="0.659"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="test" time="0.185"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC019_UpdatePanOnLead" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_UpdatePanOnLead -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC011_FetchLeadAllData_BRE1Success" time="24.936">
    <failure message="did not expect to find [LENDER_BRE_SUCCESS] but found [LENDER_BRE_INITIATED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [LENDER_BRE_SUCCESS] but found [LENDER_BRE_INITIATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow.TC011_FetchLeadAllData_BRE1Success(TestAxisDistributionETBFlow.java:712)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC011_FetchLeadAllData_BRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC014_InitiateKYCForETBUser" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_InitiateKYCForETBUser -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC004_FetchLeadAllData" time="0.997"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC009_FetchCIR_Async" time="0.708"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC015_SubmitKYCForETBUser" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_SubmitKYCForETBUser -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC020_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC005_UpdateLeadBasicDetails" time="1.141"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC008_UpdateBureauDataSetInSAI" time="0.745"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC002_DeleteExistingLead" time="0.363"/>
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC023_CheckLoanDisbursementCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_CheckLoanDisbursementCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow" name="TC024_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_FetchLeadAllData -->
</testsuite> <!-- OCL.Lending.ConsumerLending.AxisBank.TestAxisDistributionETBFlow -->
