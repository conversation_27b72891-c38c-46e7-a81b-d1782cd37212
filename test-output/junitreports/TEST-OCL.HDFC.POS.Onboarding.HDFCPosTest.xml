<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB27706.local" failures="1" tests="27" name="OCL.HDFC.POS.Onboarding.HDFCPosTest" time="27.737" errors="0" timestamp="2022-07-14T18:49:17 IST" skipped="0">
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC05_CreateLeadWithoutMCC" time="0.335"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC14_CreateLeadWithoutEmailBusinessName" time="0.214"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC20_CreateLeadWithoutMECodeTID" time="0.335"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC09_CreateLeadWithoutDisplayName" time="5.334"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC24_CreateLeadWithoutBankName" time="5.312"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC08_CreateLeadWithoutBusinessName" time="0.655"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC13_CreateLeadWithoutMobileEmail" time="0.219"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC02_CreateLeadWithoutSolutionTypeLevel2" time="0.295"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC27_CreateLeadWithoutPincode" time="0.356"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC15_CreateLeadWithoutBusinessDisplayName" time="0.240"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC04_CreateLeadWithInvalidSolutionTypeLevel2" time="0.334"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC06_CreateLeadWithInvlalidMCC" time="0.341"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC18_CreateLeadWithoutBankAccNum" time="0.338"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC10_CreateLeadWithoutBankAccHolderName" time="0.336"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC26_CreateLeadWithInvalidIFSC" time="0.342"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC19_CreateLeadWithoutMECode" time="0.271"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC21_CreateLeadWithoutMECodeMCC" time="0.267"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC22_CreateLeadWithoutMECodeMCC" time="0.284"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC23_CreateLeadWithInvalidBankAccNum" time="1.892">
    <failure message="did not expect to find [Missing  Bank Account Number] but found [We are not able to process this onboarding request as we already have a higher limit merchant account tagged with this mobile number.]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [Missing  Bank Account Number] but found [We are not able to process this onboarding request as we already have a higher limit merchant account tagged with this mobile number.]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.HDFC.POS.Onboarding.HDFCPosTest.TC23_CreateLeadWithInvalidBankAccNum(HDFCPosTest.java:1141)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC23_CreateLeadWithInvalidBankAccNum -->
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC17_CreateLeadWithoutMCCandTID" time="0.399"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC16_CreateLeadWithoutBusinessDisplayMCC" time="0.436"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC25_CreateLeadWithoutIFSC" time="0.329"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC12_CreateLeadWithoutSolutionMobile" time="0.234"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC11_CreateLeadWithoutSolutionTypeandSolutionTypeLevel2" time="0.308"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC01_CreateLeadWithoutSolutionType" time="7.776"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC07_CreateLeadWithoutMobile" time="0.288"/>
  <testcase classname="OCL.HDFC.POS.Onboarding.HDFCPosTest" name="TC03_CreateLeadWithoutMobile" time="0.267"/>
</testsuite> <!-- OCL.HDFC.POS.Onboarding.HDFCPosTest -->
