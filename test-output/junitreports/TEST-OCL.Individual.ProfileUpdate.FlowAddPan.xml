<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="19" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowAddPan" tests="22" failures="0" timestamp="2022-07-13T13:25:47 IST" time="14.137" errors="1">
  <testcase name="TC000_CreateApplicantOauth" time="1.509" classname="OCL.Individual.ProfileUpdate.FlowAddPan"/>
  <testcase name="TC001_CreateMerchantOnPG" time="0.641" classname="OCL.Individual.ProfileUpdate.FlowAddPan"/>
  <testcase name="TC002_GetPGMID" time="11.984" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.goldengate.common.BaseMethod.FetchMIDFromPG(BaseMethod.java:1442)
at OCL.Individual.ProfileUpdate.FlowAddPan.TC002_GetPGMID(FlowAddPan.java:96)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC002_GetPGMID -->
  <testcase name="TC003_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC003_AddPan -->
  <testcase name="TC004_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC004_AddPan -->
  <testcase name="TC005_AddPan" time="0.001" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC005_AddPan -->
  <testcase name="TC006_AddPan" time="0.001" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC006_AddPan -->
  <testcase name="TC007_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC007_AddPan -->
  <testcase name="TC008_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC008_AddPan -->
  <testcase name="TC009_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC009_AddPan -->
  <testcase name="TC010_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC010_AddPan -->
  <testcase name="TC011_AddPan" time="0.001" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC011_AddPan -->
  <testcase name="TC012_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC012_AddPan -->
  <testcase name="TC013_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC013_AddPan -->
  <testcase name="TC014_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC014_AddPan -->
  <testcase name="TC015_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC015_AddPan -->
  <testcase name="TC016_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC016_AddPan -->
  <testcase name="TC017_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC017_AddPan -->
  <testcase name="TC018_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC018_AddPan -->
  <testcase name="TC019_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC019_AddPan -->
  <testcase name="TC020_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC020_AddPan -->
  <testcase name="TC021_AddPan" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddPan">
    <skipped/>
  </testcase> <!-- TC021_AddPan -->
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowAddPan -->
