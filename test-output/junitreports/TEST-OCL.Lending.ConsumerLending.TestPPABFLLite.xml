<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="ip-192-168-29-76.ap-south-1.compute.internal" failures="0" tests="33" name="OCL.Lending.ConsumerLending.TestPPABFLLite" time="103.261" errors="0" timestamp="11 May 2021 12:54:19 GMT" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC012_VerifyLeadStage" time="0.678"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC015_UploadCustomerPhoto" time="2.969"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC002_FetchLeadDeatils" time="0.804"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC007_AddBasicDetails" time="0.690"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC006_FetchTheCreatedLeadDeatils" time="0.721"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC023_CheckBREResponse" time="62.223"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC032_LoanStatusCallbackAfterSubmitApplication" time="11.283"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC026_VerifyLeadStage" time="0.647"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC018_VerifyLeadStage" time="0.731"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC008_FetchTheLeadDeatils" time="0.726"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC017_CKYCCallback" time="0.725"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC025_AddAddress" time="0.700"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC013_UploadSelfie" time="3.162"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC030_VerifyLeadStage" time="0.618"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC001_DeleteExistingLead" time="0.900"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC031_SubmitApplication" time="0.839"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC019_BREValidationPendingCallback" time="0.675"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC027_FetchDynamicTnc" time="1.521"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC010_FetchLeadStage" time="0.644"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC024_VerifyLeadStage" time="0.677"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC003_CreatePostpaidLead" time="1.643"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC020_VerifyLeadStage" time="0.645"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC029_FetchDynamicTncSanctionLetter" time="0.615"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC005_PPBLOTPCallback" time="1.714"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC021_FetchBREResponse" time="0.933"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC014_VerifyUploadedDocument" time="0.674"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC016_VerifyUploadedDocument" time="0.697"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC011_CheckCKYCStatus" time="0.794"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC022_VerifyLeadStage" time="0.620"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC004_FetchTheCreatedLeadDeatils" time="1.032"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC009_OTPCallback" time="0.599"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC033_VerifyLeadStage" time="0.608"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPPABFLLite" name="TC028_VerifyLeadStage" time="0.754"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPPABFLLite -->
