<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="5" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestStashfin" tests="10" failures="1" timestamp="2022-06-27T17:44:26 IST" time="9.582" errors="0">
  <testcase name="TC011_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestStashfin">
    <skipped/>
  </testcase> <!-- TC011_FetchLeadAllData -->
  <testcase name="TC002_DeleteExistingLead" time="0.332" classname="OCL.Lending.ConsumerLending.TestStashfin"/>
  <testcase name="TC007_LISCallbackToLoanSanctionedFromLoanAccepted" time="0.000" classname="OCL.Lending.ConsumerLending.TestStashfin">
    <skipped/>
  </testcase> <!-- TC007_LISCallbackToLoanSanctionedFromLoanAccepted -->
  <testcase name="TC010_LMSDataCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestStashfin">
    <skipped/>
  </testcase> <!-- TC010_LMSDataCallback -->
  <testcase name="TC005_FetchLeadAllData" time="6.149" classname="OCL.Lending.ConsumerLending.TestStashfin">
    <failure type="java.lang.AssertionError" message="did not expect to find [LENDER_SUBMIT_SUCCESS] but found [BASIC_DETAILS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LENDER_SUBMIT_SUCCESS] but found [BASIC_DETAILS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestStashfin.TC005_FetchLeadAllData(TestStashfin.java:356)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC005_FetchLeadAllData -->
  <testcase name="TC004_UpdateLeadBasicDetails" time="0.814" classname="OCL.Lending.ConsumerLending.TestStashfin"/>
  <testcase name="TC003_CreateStashfinLead" time="1.184" classname="OCL.Lending.ConsumerLending.TestStashfin"/>
  <testcase name="TC009_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestStashfin">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase name="TC001_FetchLeadDeatils" time="1.103" classname="OCL.Lending.ConsumerLending.TestStashfin"/>
  <testcase name="TC008_LISCallbackToLoanDisbursedFromLoanSanctioned" time="0.000" classname="OCL.Lending.ConsumerLending.TestStashfin">
    <skipped/>
  </testcase> <!-- TC008_LISCallbackToLoanDisbursedFromLoanSanctioned -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestStashfin -->
