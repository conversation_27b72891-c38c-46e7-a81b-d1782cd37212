<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="0" tests="13" name="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" time="18.916" errors="0" timestamp="2023-08-25T17:56:58 IST" skipped="0">
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC006_FetchLeadAllData" time="0.955"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC008_FetchCIR" time="0.896"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC005_UpdateLeadBasicDetails" time="2.047"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC010_UpdateExistingDetailsInSAI" time="0.309"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC012_LeadDataUpdateForKYC_InSAI" time="0.372"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC007_UpdateBureauDataSetInSAI" time="0.489"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC009_FetchLeadAllData" time="7.165"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC003_CreateFullertonLead" time="1.382"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC011_FetchLeadAllData" time="0.424"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC013_uploadCustomerPhoto" time="2.838"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC001_FetchLeadDeatils" time="0.692"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC002_DeleteExistingLead" time="0.349"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp" name="TC004_FetchLeadAllData" time="0.998"/>
</testsuite> <!-- OCL.Lending.BusinessLending.Fullerton.TestMCASoleProp -->
