<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="17" hostname="197NODMB24984.local" name="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess" tests="20" failures="0" timestamp="2022-07-13T13:25:47 IST" time="5.191" errors="2">
  <testcase name="TC009_positiveBank" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC009_positiveBank -->
  <testcase name="TC016_PositiveRejectedLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC016_PositiveRejectedLeadPanel -->
  <testcase name="TC008_positiveGetLanguage" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC008_positiveGetLanguage -->
  <testcase name="TC017_PositiveGetMerchantStatusAfterRejection" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC017_PositiveGetMerchantStatusAfterRejection -->
  <testcase name="TC019_PositiveSubmitLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC019_PositiveSubmitLeadPanel -->
  <testcase name="TC011_positiveSubmitMerchant" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC011_positiveSubmitMerchant -->
  <testcase name="TC007_positiveGetPincode" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC007_positiveGetPincode -->
  <testcase name="TC018_positiveSubmitMerchantAfterRejection" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC018_positiveSubmitMerchantAfterRejection -->
  <testcase name="TC014_PositiveCreateUserWallet100K" time="0.001" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC014_PositiveCreateUserWallet100K -->
  <testcase name="TC020_PgCallBack100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC020_PgCallBack100K -->
  <testcase name="TC005_PositiveGetCategoryGetCat" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC005_PositiveGetCategoryGetCat -->
  <testcase name="TC010_positivePennyDrop" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC010_positivePennyDrop -->
  <testcase name="TC003_PositiveValidateOtp" time="3.872" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <error type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:238)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at com.goldengate.common.BaseMethod.getOTPFromSellerPanel(BaseMethod.java:1868)
at OCL.Individual.Merchant100K.Flow100KNameMatchSuccess.TC003_PositiveValidateOtp(Flow100KNameMatchSuccess.java:155)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor152.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 16 more
]]>
    </error>
  </testcase> <!-- TC003_PositiveValidateOtp -->
  <testcase name="TC002_positiveGetTnc" time="0.322" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.Merchant100K.Flow100KNameMatchSuccess.TC002_positiveGetTnc(Flow100KNameMatchSuccess.java:144)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC002_positiveGetTnc -->
  <testcase name="TC012_PositiveFetchDocs100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC012_PositiveFetchDocs100K -->
  <testcase name="TC006_PositiveGetSubCategory" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC006_PositiveGetSubCategory -->
  <testcase name="TC015_PositiveFetchLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC015_PositiveFetchLeadPanel -->
  <testcase name="TC013_PGCallBackforInsatnt50K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC013_PGCallBackforInsatnt50K -->
  <testcase name="TC001_PositiveSendOtpSendOTP" time="0.996" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess"/>
  <testcase name="TC004_PositiveGetMerchantStatus" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KNameMatchSuccess">
    <skipped/>
  </testcase> <!-- TC004_PositiveGetMerchantStatus -->
</testsuite> <!-- OCL.Individual.Merchant100K.Flow100KNameMatchSuccess -->
