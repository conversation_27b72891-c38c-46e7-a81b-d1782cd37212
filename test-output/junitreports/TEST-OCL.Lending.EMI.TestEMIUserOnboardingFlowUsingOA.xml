<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="35" name="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" time="19.827" errors="0" timestamp="2023-07-31T18:04:42 IST" skipped="27">
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC028_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_EmandateCallback -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC029_EMI_FetchLeadPostEmandate" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_EMI_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC008_LoanOfferAccept" time="0.000">
    <skipped/>
  </testcase> <!-- TC008_LoanOfferAccept -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC007_FetchLeadAllData" time="10.037">
    <failure message="did not expect to find [BRE1_SUCCESS] but found [null]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE1_SUCCESS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA.TC007_FetchLeadAllData(TestEMIUserOnboardingFlowUsingOA.java:506)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC007_FetchLeadAllData -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC011_VerifyUploadedCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC034_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_FetchLeadAllData -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC033_LMSDataCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_LMSDataCallback -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC027_FetchLeadPostBankVerification" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC019_InitiateKYC_UsingOfflineAAdhaar" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC012_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_UploadSelfie -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC010_uploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC016_InitiateKYC_UsingOfflineAAdhaar" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_InitiateKYC_UsingOfflineAAdhaar -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC017_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC018_LeadDataUpdateForKYC_InSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC024_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_SaveBankDetails -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="test" time="0.108"/>
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC005_UpdateBureauDataSetInSAI" time="0.369"/>
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC015_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC020_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC002_DeleteExistingLead" time="0.330"/>
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC031_AcceptSanctionLettert" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_AcceptSanctionLettert -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC023_LeadDataUpdateForKYC_InSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC025_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC032_VerifyLeadStage_LMSSubmitApplicationJob" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC004_FetchLeadAllData" time="5.721"/>
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC022_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_VerifyLeadStage -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC021_Accept_KYCAddressConsent" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_Accept_KYCAddressConsent -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC014_InitiateKYC_UsingSearchByPan" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC001_EMI_FetchLeadDeatils" time="0.448"/>
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC006_FetchCIR" time="1.729"/>
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC013_VerifyUploadedSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC003_CreateEMILead" time="1.085"/>
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC009_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC026_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_SaveBankDetails -->
  <testcase classname="OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA" name="TC030_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_GenerateSanctionLetter -->
</testsuite> <!-- OCL.Lending.EMI.TestEMIUserOnboardingFlowUsingOA -->
