<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="33" name="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" time="2.307" errors="0" timestamp="2023-09-25T16:35:03 IST" skipped="29">
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC001_FetchLeadDeatils" time="0.920"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC028_VerifyPDCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC031_LMSDataCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC022_FetchLeadPostBankVerification" time="0.001">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC023_GenerateLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC003_CreateFullertonLead" time="0.955">
    <failure message="did not expect to find [LEAD_CREATED] but found [PAN_DEDUPE_FAILED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_CREATED] but found [PAN_DEDUPE_FAILED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge.TC003_CreateFullertonLead(TestMCAFullertonLPBDMerge.java:302)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC003_CreateFullertonLead -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC009_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC021_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC004_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC004_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC012_VerifyUploadedCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC020_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC026_AcceptLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC017_FetchDataKYCInitiate" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC018_MCA_Fullerton_SecondBRECallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_MCA_Fullerton_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC014_VerifyUploadedSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC025_UpdateActualPanInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC011_uploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC032_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="test" time="0.200"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC024_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC006_FetchCIR" time="0.000">
    <skipped/>
  </testcase> <!-- TC006_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC027_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC019_FetchLead_AfterBRE2" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC002_DeleteExistingLead" time="0.231"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC007_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC007_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC016_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC005_UpdateBureauDataSetInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC005_UpdateBureauDataSetInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC030_VerifyLISCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_VerifyLISCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC029_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC013_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC008_UpdateExistingDetailsInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC008_UpdateExistingDetailsInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge" name="TC015_InitiateKYC_UsingOfflineAAdhaar" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_InitiateKYC_UsingOfflineAAdhaar -->
</testsuite> <!-- OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonLPBDMerge -->
