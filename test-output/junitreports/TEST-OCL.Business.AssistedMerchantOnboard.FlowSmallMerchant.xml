<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="13" hostname="197NODMB24984.local" name="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant" tests="15" failures="0" timestamp="2022-07-13T13:25:47 IST" time="74.274" errors="1">
  <testcase name="TC008_PositiveAcceptTnCSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC008_PositiveAcceptTnCSmallMerchant -->
  <testcase name="TC0011_PGCallBackforInsatnt50K" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC0011_PGCallBackforInsatnt50K -->
  <testcase name="TC010_PositiveFetchDocsSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC010_PositiveFetchDocsSmallMerchant -->
  <testcase name="TC0012_PositiveSubmitLeadPanelSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC0012_PositiveSubmitLeadPanelSmallMerchant -->
  <testcase name="TC008_PositiveGetCommissionSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC008_PositiveGetCommissionSmallMerchant -->
  <testcase name="TC0011_PositiveFetchLeadPanelSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC0011_PositiveFetchLeadPanelSmallMerchant -->
  <testcase name="TC001_PositiveSendOtpAssitedMerchant" time="60.632" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant.TC001_PositiveSendOtpAssitedMerchant(FlowSmallMerchant.java:105)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- TC001_PositiveSendOtpAssitedMerchant -->
  <testcase name="TC009_PositiveUpdateQrCodeID" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC009_PositiveUpdateQrCodeID -->
  <testcase name="TC004_PositiveValidateOtpSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC004_PositiveValidateOtpSmallMerchant -->
  <testcase name="TC003_GetSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC003_GetSmallMerchant -->
  <testcase name="TC001_CreateUPIAccount" time="13.642" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant"/>
  <testcase name="TC002_PositiveValidateOtpAssistedMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC002_PositiveValidateOtpAssistedMerchant -->
  <testcase name="TC007_PositiveSubmitLeadSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC007_PositiveSubmitLeadSmallMerchant -->
  <testcase name="TC005_PositiveGetSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC005_PositiveGetSmallMerchant -->
  <testcase name="TC006_PositivePartialSaveSmallMerchant" time="0.000" classname="OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant">
    <skipped/>
  </testcase> <!-- TC006_PositivePartialSaveSmallMerchant -->
</testsuite> <!-- OCL.Business.AssistedMerchantOnboard.FlowSmallMerchant -->
