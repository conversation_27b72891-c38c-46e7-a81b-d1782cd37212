<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPLABFLOneClick" tests="43" failures="1" timestamp="2022-07-27T18:54:57 IST" time="78.822" errors="0">
  <testcase name="TC041_PLv3ABFL_FetchLeadPostPDCCallback" time="0.346" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC035_PLv3ABFL_FetchLeadPostEmandate" time="0.539" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC036_PLv3ABFL_GenerateLoanAgreement" time="1.149" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC007_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.519" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC001_PLv3ABFL_fetchlLead" time="0.476" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC009_PLv3ABFL_FetchCIR" time="0.581" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC005_PLV3ABFL_UpdateLeadOccupationDetails" time="0.766" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC025_PLv3ABFL_FetchDataAfterBRE2Success" time="0.380" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC011_PLv3ABFL_FetchDataPostBRE1Success" time="0.534" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC030_PLv3ABFL_UpdateKYCNameInSAI" time="0.830" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC010_PLv3ABFL_BRE1Callback" time="4.597" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate" time="0.832" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.428" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC034_PLv3ABFL_EmandateCallback" time="0.373" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC013_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.343" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC027_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.395" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC019_PLv3HERO_VerifyUploadedCustomerPhoto" time="0.516" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC004_PLV3ABFL_FetchLeadAllData" time="0.952" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC017_PLv3ABFL_FetchDataPostKYCIntiated" time="0.421" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC003_PLv3ABFL_CreateLead" time="0.961" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC028_PLv3ABFL_AdditionalDataCapture" time="0.373" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan" time="0.685" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC023_PLv3ABFL_FetchDataPostSelfieUploaded" time="15.634" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC043_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick">
    <skipped/>
  </testcase> <!-- TC043_FetchLeadAllData -->
  <testcase name="TC008_PLv3ABFL_FetchDataPostSAIlUpdate" time="0.508" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC022_PLv3ABFL_FetchDataPostKYCIntiated" time="10.320" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC032_PLv3ABFL_SaveBankDetails" time="1.503" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC037_PLv3ABFL_GenerateSanctionLetter" time="0.605" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC018_PLv3HERO_UploadCustomerPhoto" time="4.698" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC014_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.378" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC012_PLv3ABFL_LoanOfferAccept" time="0.690" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC020_PLv3HERO_UploadSelfie" time="4.821" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC039_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.418" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC042PLV3HERO_SubmitApplicationLMSApprovedCallback" time="2.600" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick">
    <failure type="java.lang.AssertionError" message="did not expect to find [LMS_CALLBACK_SUCCESS] but found [LOAN_AGREEMENT_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LMS_CALLBACK_SUCCESS] but found [LOAN_AGREEMENT_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC042PLV3HERO_SubmitApplicationLMSApprovedCallback(TestPLABFLOneClick.java:1557)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC042PLV3HERO_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC038_PLv3ABFL_SubmitApplication" time="0.629" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC024_PLv3ABFL_SecondBRECallback" time="0.912" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC021_PLv3HERO_VerifyUploadedSelfie" time="0.428" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC029_PLv3ABFL_BRE3Success" time="1.646" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC040_PLv3ABFL_PDCCallback" time="2.813" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC033_PLv3ABFL_FetchLeadPostBankVerification" time="1.600" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC002_PLv3ABFL_DeleteExistingLead" time="0.404" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC026_PLv3ABFL_AdditionalIsRequiredorNot" time="0.774" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC016_PLv3ABFL_FetchDataPostLoanOfferAccept" time="10.445" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPLABFLOneClick -->
