<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="1" tests="8" name="OCL.CommonOnboarding.EDC.AddBank" time="57.499" errors="0" timestamp="2023-11-07T14:17:16 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="TC_7_addBankWithoutIFSC" time="7.859"/>
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="test" time="0.131"/>
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="TC_1_CreatingLeadAndAddingBankDetails" time="15.504"/>
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="TC_4_addBankWithoutSessiontoken" time="4.738"/>
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="TC_8_addBankWithoutAccountNumber" time="4.840">
    <failure message="Testcase Failed did not expect to find [true] but found [false]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: Testcase Failed did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at OCL.CommonOnboarding.EDC.AddBank.TC_8_addBankWithoutAccountNumber(AddBank.java:654)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:78)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:567)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC_8_addBankWithoutAccountNumber -->
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="TC_5_addBankWithoutDeviceIdentifier" time="5.215"/>
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="TC_6_addBankWithoutVersion" time="6.480"/>
  <testcase classname="OCL.CommonOnboarding.EDC.AddBank" name="TC_2_ContinuewithExistingLeadAndAddingBankDetails" time="12.732"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.AddBank -->
