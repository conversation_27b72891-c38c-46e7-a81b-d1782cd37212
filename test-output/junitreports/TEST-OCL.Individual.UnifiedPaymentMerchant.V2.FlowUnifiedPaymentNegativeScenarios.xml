<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios" tests="32" failures="4" timestamp="2022-07-13T13:25:47 IST" time="15.081" errors="1">
  <testcase name="TC0_CreateApplicantOauth" time="1.348" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC0002_GetApplicantToken" time="1.260" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC031_CreateAccountForUPM_V2" time="0.483" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios.TC031_CreateAccountForUPM_V2(FlowUnifiedPaymentNegativeScenarios.java:111)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC031_CreateAccountForUPM_V2 -->
  <testcase name="TC030_CreateAccountForUPM_V2" time="0.428" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC029_SaveRefreeCodeAndWhatsappNotification" time="0.495" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC028_SaveRefreeCodeAndWhatsappNotification" time="0.359" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC027_SaveRefreeCodeAndWhatsappNotification" time="0.377" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC026_SaveBankDetails" time="0.094" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios">
    <error type="java.lang.RuntimeException" message="Method type and path are not specified for: SaveBankUPM">
      <![CDATA[java.lang.RuntimeException: Method type and path are not specified for: SaveBankUPM
at com.paytm.apitools.core.AbstractApi.init(AbstractApi.java:76)
at com.paytm.apitools.core.AbstractApi.<init>(AbstractApi.java:46)
at com.paytm.apitools.core.AbstractApiV2.<init>(AbstractApiV2.java:47)
at com.paytm.apitools.core.AbstractApiV2.<init>(AbstractApiV2.java:68)
at Request.MerchantService.v1.sdMerchant.saveBank.SaveBankUPM.<init>(SaveBankUPM.java:11)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios.TC026_SaveBankDetails(FlowUnifiedPaymentNegativeScenarios.java:215)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC026_SaveBankDetails -->
  <testcase name="TC025_ValidateBank" time="0.367" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC024_ValidateBank" time="0.423" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC023_ValidateBank" time="0.388" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC022_ValidateBank" time="0.438" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC021_ValidateBank" time="0.586" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC020_SaveIdentityDetails" time="0.481" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC019_SaveIdentityDetails" time="0.314" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios.TC019_SaveIdentityDetails(FlowUnifiedPaymentNegativeScenarios.java:429)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC019_SaveIdentityDetails -->
  <testcase name="TC017_SaveIdentityDetails" time="0.359" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios.TC017_SaveIdentityDetails(FlowUnifiedPaymentNegativeScenarios.java:455)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC017_SaveIdentityDetails -->
  <testcase name="TC016_SaveIdentityDetails" time="0.368" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios.TC016_SaveIdentityDetails(FlowUnifiedPaymentNegativeScenarios.java:481)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC016_SaveIdentityDetails -->
  <testcase name="TC015_AdditionalDetails" time="0.347" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC014_AdditionalDetails" time="0.401" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC013_AdditionalDetails" time="0.598" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC012_FetchDetails" time="0.434" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC011_FetchDetails" time="0.473" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC010_SaveBusiness" time="0.387" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC009_SaveBusiness" time="0.514" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC008_SaveBusiness" time="0.379" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC007_SaveBusiness" time="0.368" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC006_CreateLead" time="0.631" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC005_CreateLead" time="0.334" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC004_CreateLead" time="0.410" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC003_CreateLead" time="0.415" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC002_FetchDetails" time="0.425" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
  <testcase name="TC001_FetchDetails" time="0.397" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios"/>
</testsuite> <!-- OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentNegativeScenarios -->
