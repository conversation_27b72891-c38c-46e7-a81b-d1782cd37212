<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="11" name="OCL.CommonOnboarding.EDC.Fetchqna" time="59.874" errors="0" timestamp="2023-07-24T17:10:10 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_5_fetchqnAWithoutleadID" time="6.259"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="test" time="0.305"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_10_fetchqnAWithoutentityType" time="6.232"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_2_fetchqnAWithoutSessiontoken" time="6.700"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_3_fetchqnAWithoutDeviceIdentifier" time="4.861"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_9_fetchqnAWithoutInvalidsolutiontype" time="7.755"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_7_fetchqnAWithInvalidquestionType" time="5.087"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_8_fetchqnAWithoutsolutiontype" time="7.163"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_4_fetchqnAWithoutVersion" time="5.272"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_6_fetchqnAWithoutquestionType" time="4.746"/>
  <testcase classname="OCL.CommonOnboarding.EDC.Fetchqna" name="TC_1_fetchqnA" time="5.494"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.Fetchqna -->
