<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="ip-192-168-225-46.ap-south-1.compute.internal" failures="0" tests="45" name="OCL.Lending.BusinessLending.TestMCAContactibilityClix" time="75.896" errors="0" timestamp="28 May 2021 14:50:44 GMT" skipped="0">
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC007_AddBasicDetails" time="0.661"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC035_UpdateKYCNameInSAI" time="0.527"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC036_SaveBankDetails" time="6.128"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC014_VerifyLeadStage" time="0.653"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC021_OTPCallback" time="0.643"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC028_VerifyUploadedDocument" time="0.627"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC037_VerifyLeadStage" time="0.756"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC003_CreateBusinessLendingLead" time="1.517"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC044_UploadSheetONPanel" time="17.239"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC026_VerifyUploadedDocument" time="0.641"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC025_UploadSelfie" time="3.167"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC010_VerifyLeadStage" time="0.758"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC029_CKYCCallback" time="0.775"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC013_BREOTPVerification" time="0.708"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC022_FetchLeadStage" time="0.618"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC027_UploadCustomerPhoto" time="2.972"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC012_VerifyLeadStage" time="0.642"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC009_UpdatePANAndDOBDetails" time="0.605"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC017_FetchBREResponse" time="0.931"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC008_VerifyLeadStage" time="0.633"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC005_PPBLOTPCallback" time="1.749"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC042_VerifyLeadStage" time="0.618"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC038_FetchDynamicTnc" time="1.082"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC001_DeleteExistingLead" time="0.841"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC015_BREValidationPending" time="0.712"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC030_VerifyLeadStage" time="0.778"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC041_SubmitApplication" time="1.211"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC040_VerifyLeadStage" time="0.634"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC045_VerifyLeadStage" time="2.463"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC019_CheckBREResponse" time="13.515"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC034_VerifyLeadStage" time="0.626"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC004_FetchTheCreatedLeadDeatils" time="1.536"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC024_VerifyLeadStage" time="0.590"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC031_AdditionalInfoUpdate" time="0.580"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC016_VerifyLeadStage" time="0.629"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC039_FetchDynamicTncSanctionLetter" time="0.793"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC002_FetchLeadDeatils" time="0.717"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC011_UpdateGenderAndPincodeDetails" time="0.640"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC023_CheckCKYCStatus" time="0.873"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC033_AdditionalDataCaptureWithKycAndUserAddress" time="0.593"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC006_VerifyLeadStage" time="0.666"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC032_VerifyLeadStage" time="0.718"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC018_VerifyLeadStage" time="0.627"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC020_VerifyLeadStage" time="0.704"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilityClix" name="TC043_EmandateCallback" time="0.800"/>
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAContactibilityClix -->
