<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="8" name="OCL.CommonOnboarding.EDC.sendingOTPLead" time="3.333" errors="0" timestamp="2023-11-07T12:15:06 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="TC_3_sendOTPLeadNewMerchantWithoutVersion" time="0.282"/>
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="TC_2_sendOTPLeadNewMerchant" time="0.589"/>
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="TC_1_sendOTPLeadExistingMerchant" time="0.925"/>
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="test" time="0.263"/>
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="TC_006_sendOTPInvalidMobile" time="0.411"/>
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="TC_005_sendOTPWithoutSessionToken" time="0.358"/>
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="TC_007_sendOTPWithoutUserType" time="0.257"/>
  <testcase classname="OCL.CommonOnboarding.EDC.sendingOTPLead" name="TC_4_sendOTPLeadNewMerchantWithoutdeviceIdentifier" time="0.248"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.sendingOTPLead -->
