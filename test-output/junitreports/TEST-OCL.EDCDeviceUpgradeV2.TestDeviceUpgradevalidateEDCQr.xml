<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="5" tests="15" name="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" time="84.312" errors="0" timestamp="2024-08-23T20:11:52 IST" skipped="0">
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfDeviceIDNotPassedInRequest" time="7.464"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfVersionIsNotPassedInReq" time="4.629"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidDeviceType" time="6.215">
    <failure message="did not expect to find [200] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfInvalidDeviceType -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfTokenIsNotPassedInRequest" time="4.744"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader" time="4.700"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfDeviceBindingDoesNotExist" time="5.774"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidModel" time="5.509">
    <failure message="did not expect to find [200] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfInvalidModel -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="test" time="1.739"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfLeadIdISNotvalid" time="5.773">
    <failure message="did not expect to find [500] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfLeadIdISNotvalid -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidDeviceOEM" time="5.411"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfDeviceBindedWithDifferentMid" time="6.599"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfLeadIsNotPassedINRequest" time="5.599"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidOS" time="8.318">
    <failure message="did not expect to find [200] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfInvalidOS -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfSuccessfullQrValidation" time="6.635">
    <failure message="did not expect to find [200] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
    </failure>
  </testcase> <!-- getStatusInCaseOfSuccessfullQrValidation -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" name="getStatusInCaseOfInvalidTokenIsPassedInRequest" time="5.203"/>
</testsuite> <!-- OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr -->
