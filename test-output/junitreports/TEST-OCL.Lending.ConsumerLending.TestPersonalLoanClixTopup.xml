<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="4" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup" tests="29" failures="1" timestamp="2022-06-27T18:34:42 IST" time="31.743" errors="0">
  <testcase name="TC07_PLv3CLIX_TOPUP_FetchCIR" time="8.417" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC019_PLv3CLIX_TOPUP_FetchLeadUpdateCKYCinSAI" time="0.280" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC018_PLv3CLIX_TOPUP_UpdateKYCNameInSAI" time="0.702" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC005_PLV3CLIX_Topup_UpdateLeadDetailsinSAI" time="0.367" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC017_PLv3CLIX_TOPUP_BRE3Success" time="3.238" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC009_PLv3CLIX_Topup_FetchDataPostBRE1Success" time="0.337" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC022_PLv3CLIX_TOPUP_EmandateCallback" time="0.318" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC026_PLv3CLIX_TOPUP_SubmitApplication" time="0.301" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup.TC026_PLv3CLIX_TOPUP_SubmitApplication(TestPersonalLoanClixTopup.java:1010)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC026_PLv3CLIX_TOPUP_SubmitApplication -->
  <testcase name="TC028_PLv3CLIX_TOPUP_PDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup">
    <skipped/>
  </testcase> <!-- TC028_PLv3CLIX_TOPUP_PDCCallback -->
  <testcase name="TC030PLv3CLIX_TOPUP_SubmitApplicationLMSApprovedCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup">
    <skipped/>
  </testcase> <!-- TC030PLv3CLIX_TOPUP_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC06_PLv3CLIX_Topup_FetchDataPost_SAI_Update" time="0.438" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC012_PLv3CLIX_TOPUP_SecondBREInitiated" time="0.269" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC027_PLv3CLIX_TOPUP_FetchLead_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup">
    <skipped/>
  </testcase> <!-- TC027_PLv3CLIX_TOPUP_FetchLead_SubmitApplication -->
  <testcase name="TC011_PLv3CLIX_Topup_FetchDataPostLoanOfferAccept" time="0.585" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC001_PLv3_CLIX_TOPUP_fetchlLead" time="0.714" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC008_PLv3CLIX_Topup_BRE1Callback" time="6.500" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC004_PLV3CLIXTopup_FetchLeadAllData" time="1.607" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC025_PLv3CLIX_TOPUP_GenerateSanctionLetter" time="0.527" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC016_PLv3CLIX_TOPUP_AdditionalDataCapture" time="0.356" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC024_PLv3CLIX_TOPUP_GenerateLoanAgreement" time="0.441" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC013_PLv3CLIX_TOPUP_SecondBRECallback" time="0.717" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC002_PLv3_CLIX_TOPUP_DeleteExistingLead" time="0.324" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC029_PLv3CLIX_TOPUP_FetchLeadPostPDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup">
    <skipped/>
  </testcase> <!-- TC029_PLv3CLIX_TOPUP_FetchLeadPostPDCCallback -->
  <testcase name="TC021_PLv3CLIX_TOPUP_FetchLeadPostBankVerification" time="1.459" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC023_PLv3CLIX_TOPUP_FetchLeadPostEmandate" time="0.299" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC003_PLv3_CLIX_TOPUP_CreateLead" time="1.121" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC010_PLv3CLIX_TOPUP_LoanOfferAccept" time="0.851" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC020_PLv3CLIX_CLIX_SaveBankDetails" time="1.187" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
  <testcase name="TC015_PLv3CLIX_TOPUP_FetchLeadVerifyAdditionalData" time="0.388" classname="OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanClixTopup -->
