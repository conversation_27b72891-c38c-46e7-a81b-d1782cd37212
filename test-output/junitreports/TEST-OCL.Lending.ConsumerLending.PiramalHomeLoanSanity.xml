<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="7" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity" tests="20" failures="1" timestamp="2022-06-27T17:56:35 IST" time="35.365" errors="0">
  <testcase name="TC017_LISCallbackToLoanDisbursedFromLoanSanctioned" time="0.000" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <skipped/>
  </testcase> <!-- TC017_LISCallbackToLoanDisbursedFromLoanSanctioned -->
  <testcase name="TC004_FetchLeadAllData" time="1.105" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC020_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <skipped/>
  </testcase> <!-- TC020_FetchLeadAllData -->
  <testcase name="TC018_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <skipped/>
  </testcase> <!-- TC018_FetchLeadAllData -->
  <testcase name="TC019_LMSDataCallback" time="0.000" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <skipped/>
  </testcase> <!-- TC019_LMSDataCallback -->
  <testcase name="TC014_UpdateToAppointmentBookedFailureNode" time="0.000" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <skipped/>
  </testcase> <!-- TC014_UpdateToAppointmentBookedFailureNode -->
  <testcase name="TC005_UpdateLeadBasicDetails" time="0.967" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC016_CallbackToLoanSanctioned" time="0.000" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <skipped/>
  </testcase> <!-- TC016_CallbackToLoanSanctioned -->
  <testcase name="TC008_FetchCIR" time="8.365" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC006_FetchLeadAllData" time="1.136" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC001_FetchLeadDeatils" time="1.097" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC002_DeleteExistingLead" time="0.653" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC012_UpdateAdditionalDetailsWithoutCoApplicantDetails" time="0.692" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC009_FetchLeadAllData" time="1.133" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC013_AcceptLoanOffer" time="13.016" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOAN_OFFER_ACCEPTED] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOAN_OFFER_ACCEPTED] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PiramalHomeLoanSanity.TC013_AcceptLoanOffer(PiramalHomeLoanSanity.java:819)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC013_AcceptLoanOffer -->
  <testcase name="TC003_CreateHLDistributionPiramalLead" time="1.478" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC011_FetchLeadAllData" time="4.724" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC015_UpdateToAppointmentBookedSuccessStage" time="0.000" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity">
    <skipped/>
  </testcase> <!-- TC015_UpdateToAppointmentBookedSuccessStage -->
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.530" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
  <testcase name="TC010_UpdateExistingDetailsInSAI" time="0.469" classname="OCL.Lending.ConsumerLending.PiramalHomeLoanSanity"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.PiramalHomeLoanSanity -->
