<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="6" name="OCL.CommonOnboarding.EDC.GetMerchantBank7" time="6.544" errors="0" timestamp="2023-11-07T14:14:40 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank7" name="TC_6_GetBankErrorWithoutDeviceIdentifer" time="0.305"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank7" name="test" time="0.283"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank7" name="TC_1_getExistingLeadBankDetailsUPIFalse" time="2.171"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank7" name="TC_5_GetBankErrorWithoutVersion" time="0.549"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank7" name="TC_4_GetBankErrorWithoutSessionToken" time="1.391"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantBank7" name="TC_3_GetBankWithUPIFetchTrue" time="1.845"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.GetMerchantBank7 -->
