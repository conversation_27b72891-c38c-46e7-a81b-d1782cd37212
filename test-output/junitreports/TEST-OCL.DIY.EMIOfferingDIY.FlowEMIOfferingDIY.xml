<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY" tests="24" failures="0" timestamp="2022-07-13T12:28:08 IST" time="45.788" errors="0">
  <testcase name="fetchPlanforEMIOfferingDIY" time="1.058" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchProductIdforEMIOfferingDIY" time="0.978" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchtotalPriceforEMIOfferingDIY" time="0.827" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchRentalpriceforEMIOfferingDIY" time="0.789" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="AcceptTermsAndConditionsEMIOfferingDIY" time="0.327" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="CheckEligibilityforEMIOfferingDIY" time="1.149" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidPriceforEmiOfferingDiy" time="0.743" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidmidforEmiOfferingDiy" time="0.685" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidPlanTypeforEmiOfferingDiy" time="0.350" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidProductIdforEmiOfferingDiy" time="0.325" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidSolutionTypeforEmiOfferingDiy" time="0.499" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="failedToFetchedPlansForEMIOfferingDIY" time="0.407" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidMidInFetchPlanForEMIOfferingDIY" time="0.546" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidSolutionTypeInFetchPlanForEMIOfferingDIY" time="0.334" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidCustIdInCheckEligibilityforEMIOfferingDIY" time="0.385" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidMIdInCheckEligibilityforEMIOfferingDIY" time="0.653" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="validateOrderforEMIOfferingDiy" time="0.833" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="createAccessToken" time="0.229" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="createQRforEmiofferingDiy" time="3.536" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchPaymentStatusforEmiofferingDiy" time="1.029" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="CreateLeadEmiofferingDiy" time="3.862" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="CallbackinprogressEmiofferingDiy" time="0.734" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchLeadEmiofferingDiy" time="3.070" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchStatusLeadEmiofferingDiy" time="22.440" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
</testsuite> <!-- OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY -->
