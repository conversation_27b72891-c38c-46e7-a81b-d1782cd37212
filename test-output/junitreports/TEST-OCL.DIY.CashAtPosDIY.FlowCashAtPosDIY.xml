<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY" tests="30" failures="5" timestamp="2022-07-13T12:28:08 IST" time="44.310" errors="9">
  <testcase name="fetchPlanforCashAtPosDIY" time="0.681" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchProductIdforCashAtPosDIY" time="0.560" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchRentalAmountforCashAtPosDIY" time="0.569" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="AcceptTermsAndConditionsCashAtPosDIY" time="0.378" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="CheckEligibilityforCashAtPosDIY" time="0.578" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="CheckEligibilityWithInvalidMIDforCashAtPosDIY" time="0.954" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="CheckEligibilityWithInvalidCustIdforCashAtPosDIY" time="0.426" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="CheckEligibilityWithInvalidPlanTypeforCashAtPosDIY" time="0.654" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="invalidPriceforCashAtPosDiy" time="0.379" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="invalidmidforCashAtPosDiy" time="0.313" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="validateOrderforCashAtPosDiy" time="0.374" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="createLeadforCashAtPosDiy" time="1.492" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.createLeadforCashAtPosDiy(FlowCashAtPosDIY.java:324)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- createLeadforCashAtPosDiy -->
  <testcase name="fetchLeadforCashAtPosDiy" time="1.520" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchLeadforCashAtPosDiy(FlowCashAtPosDIY.java:353)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchLeadforCashAtPosDiy -->
  <testcase name="fetchDocumentStatusforCashAtPosDiy" time="0.105" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchDocumentStatusCashAtPosDIY(MiddlewareServices.java:4145)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchDocumentStatusforCashAtPosDiy(FlowCashAtPosDIY.java:374)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchDocumentStatusforCashAtPosDiy -->
  <testcase name="UploadDocforCashAtPosDiy" time="0.109" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.UploadDocCashAtPosDIY(MiddlewareServices.java:4162)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.UploadDocforCashAtPosDiy(FlowCashAtPosDIY.java:401)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- UploadDocforCashAtPosDiy -->
  <testcase name="createAccessToken" time="0.206" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="createQRforCashAtPosDiy" time="0.207" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.OMS.CreateQR.createQRviaOMS(CreateQR.java:21)
at com.goldengate.common.BaseMethod.createQRviaOMS(BaseMethod.java:1543)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.createQRforCashAtPosDiy(FlowCashAtPosDIY.java:424)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createQRforCashAtPosDiy -->
  <testcase name="fetchPaymentStatusforCashAtPosDiy" time="0.000" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <skipped/>
  </testcase> <!-- fetchPaymentStatusforCashAtPosDiy -->
  <testcase name="PaymentCashAtPosDiy" time="0.485" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.PaymentCashAtPosDiy(FlowCashAtPosDIY.java:462)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- PaymentCashAtPosDiy -->
  <testcase name="fetchStatusLeadCashAtPosDiy" time="7.037" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchStatusLeadCashAtPosDiy(FlowCashAtPosDIY.java:477)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchStatusLeadCashAtPosDiy -->
  <testcase name="assignAgentForQC" time="1.157" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="FetchDocidviaPanel" time="4.125" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.FetchDocidviaPanel(FlowCashAtPosDIY.java:512)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- FetchDocidviaPanel -->
  <testcase name="QCRejectionForCashAtPos" time="0.548" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [405]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [405]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.QCRejectionForCashAtPos(FlowCashAtPosDIY.java:533)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- QCRejectionForCashAtPos -->
  <testcase name="UploadDocAfterQCRejectionforCashAtPosDiy" time="0.097" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.UploadDocCashAtPosDIY(MiddlewareServices.java:4162)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.UploadDocAfterQCRejectionforCashAtPosDiy(FlowCashAtPosDIY.java:563)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- UploadDocAfterQCRejectionforCashAtPosDiy -->
  <testcase name="FetchDocidviaPanelAfterAgainDocUpload" time="4.148" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.FetchDocidviaPanelAfterAgainDocUpload(FlowCashAtPosDIY.java:578)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- FetchDocidviaPanelAfterAgainDocUpload -->
  <testcase name="QCApprovedForCashAtPos" time="0.373" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [405]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [405]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.QCApprovedForCashAtPos(FlowCashAtPosDIY.java:599)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- QCApprovedForCashAtPos -->
  <testcase name="fetchStatusLeadCashAtPosDiyAfterQC" time="6.986" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchStatusLeadCashAtPosDiyAfterQC(FlowCashAtPosDIY.java:614)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchStatusLeadCashAtPosDiyAfterQC -->
  <testcase name="DIYCashAtPosFetchLeadDetailsForPgReferenceId" time="0.349" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="ManualPGCallbackForCashAtPos" time="2.395" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchStatusLeadCashAtPosDiyAfterPGCallback" time="7.105" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchStatusLeadCashAtPosDiyAfterPGCallback(FlowCashAtPosDIY.java:646)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchStatusLeadCashAtPosDiyAfterPGCallback -->
</testsuite> <!-- OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY -->
