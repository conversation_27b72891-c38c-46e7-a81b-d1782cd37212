<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="0" tests="5" name="OCL.Lending.ConsumerLending.TestPersonalLoanHeroPanDedupefailed" time="15.577" errors="0" timestamp="2022-09-22T19:14:20 IST" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroPanDedupefailed" name="TC005_PLV3Hero_PAN_DEDUPE_FAILED" time="6.450"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroPanDedupefailed" name="TC001_PLv3Hero_fetchlLead" time="5.861"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroPanDedupefailed" name="TC002_PLv3Hero_DeleteExistingLead" time="1.099"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroPanDedupefailed" name="TC003_PLv3Hero_CreateLead" time="0.800"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroPanDedupefailed" name="TC004_PLV3Hero_FetchLeadAllData" time="1.367"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanHeroPanDedupefailed -->
