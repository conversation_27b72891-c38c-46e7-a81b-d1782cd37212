<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Panel.SalesLead.LeadCreationViaMobile" tests="5" failures="1" timestamp="2021-11-17T11:52:54 IST" time="6.478" errors="0">
  <testcase name="TC001_RegisterLeadViaMobileNumber" time="0.990" classname="OCL.Panel.SalesLead.LeadCreationViaMobile"/>
  <testcase name="TC002_GetDetailsOfRegisteredLead" time="0.785" classname="OCL.Panel.SalesLead.LeadCreationViaMobile"/>
  <testcase name="TC005_UploadDocuments" time="3.006" classname="OCL.Panel.SalesLead.LeadCreationViaMobile">
    <failure type="java.lang.AssertionError" message="did not expect to find [false] but found [true]">
      <![CDATA[java.lang.AssertionError: did not expect to find [false] but found [true]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:751)
at org.testng.Assert.assertEquals(Assert.java:761)
at Services.MechantService.MiddlewareServices.utilityForDocumentUpload(MiddlewareServices.java:2586)
at OCL.Panel.SalesLead.LeadCreationViaMobile.TC005_UploadDocuments(LeadCreationViaMobile.java:169)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC005_UploadDocuments -->
  <testcase name="TC004_GetListOfBusinessDocuments" time="0.693" classname="OCL.Panel.SalesLead.LeadCreationViaMobile"/>
  <testcase name="TC003_SubmitRegisterLead" time="1.004" classname="OCL.Panel.SalesLead.LeadCreationViaMobile"/>
</testsuite> <!-- OCL.Panel.SalesLead.LeadCreationViaMobile -->
