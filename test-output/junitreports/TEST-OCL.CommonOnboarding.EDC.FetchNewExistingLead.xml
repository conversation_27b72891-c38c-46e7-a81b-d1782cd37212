<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="7" name="OCL.CommonOnboarding.EDC.FetchNewExistingLead" time="9.789" errors="0" timestamp="2023-11-07T12:08:12 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.FetchNewExistingLead" name="test" time="0.347"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchNewExistingLead" name="TC_1_FetchExistingLead" time="1.845"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchNewExistingLead" name="TC_5_FetchExistingLeadWithInvalidSolutionType" time="0.299"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchNewExistingLead" name="TC_7_FetchExistingLeadWithoutVersion" time="0.282"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchNewExistingLead" name="TC_4_FetchNewLeadAndMobileNumberLeadCreatedsubstage" time="6.386"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchNewExistingLead" name="TC_2_FetchExistingLeadWithInvalidentityType" time="0.352"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchNewExistingLead" name="TC_6_FetchExistingLeadWithoutSessionToken" time="0.278"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.FetchNewExistingLead -->
