<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowAddGstin" tests="23" failures="6" timestamp="2022-07-13T13:25:47 IST" time="28.429" errors="1">
  <testcase name="TC008_14_CreateLeadAddGstin" time="1.321" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowAddGstin.TC008_14_CreateLeadAddGstin(FlowAddGstin.java:645)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC008_14_CreateLeadAddGstin -->
  <testcase name="TC009_PgCallbackAddGstin" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <skipped/>
  </testcase> <!-- TC009_PgCallbackAddGstin -->
  <testcase name="TC001_CreateApplicantOauth" time="2.702" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_05_CreateLeadAddGstinNullGstin" time="0.303" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_12_CreateLeadAddGstinEmptyBusinessName" time="0.452" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_02_CreateLeadAddGstinWrongMid" time="1.150" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowAddGstin.TC008_02_CreateLeadAddGstinWrongMid(FlowAddGstin.java:303)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC008_02_CreateLeadAddGstinWrongMid -->
  <testcase name="TC008_11_CreateLeadAddGstinNullState" time="1.134" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowAddGstin.TC008_11_CreateLeadAddGstinNullState(FlowAddGstin.java:540)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC008_11_CreateLeadAddGstinNullState -->
  <testcase name="TC003_createMerchantOnPG" time="0.851" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_01_CreateLeadAddGstinEmptyMid" time="0.356" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC004_PgCallback" time="10.286" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_13_CreateLeadAddGstinNoBusinessName" time="0.303" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_06_CreateLeadAddGstinNullMid" time="0.379" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC005_EditMerchantKyb" time="0.106" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.KYB.KybServices.KybEdit(KybServices.java:41)
at OCL.Individual.ProfileUpdate.FlowAddGstin.TC005_EditMerchantKyb(FlowAddGstin.java:167)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC005_EditMerchantKyb -->
  <testcase name="TC008_09_CreateLeadAddGstinWrongState" time="1.229" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowAddGstin.TC008_09_CreateLeadAddGstinWrongState(FlowAddGstin.java:472)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC008_09_CreateLeadAddGstinWrongState -->
  <testcase name="TC007_04_FetchLeadStatusAddGstinEmptyToken" time="0.299" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_08_CreateLeadAddGstinWrongGstin" time="1.057" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC008_03_CreateLeadAddGstinEmptyGstin" time="0.288" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC007_02_FetchLeadStatusAddGstinWrongToken" time="0.397" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <failure type="java.lang.AssertionError" message="did not expect to find [410] but found [401]">
      <![CDATA[java.lang.AssertionError: did not expect to find [410] but found [401]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowAddGstin.TC007_02_FetchLeadStatusAddGstinWrongToken(FlowAddGstin.java:205)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC007_02_FetchLeadStatusAddGstinWrongToken -->
  <testcase name="TC008_10_CreateLeadAddGstinEmptyState" time="1.191" classname="OCL.Individual.ProfileUpdate.FlowAddGstin">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowAddGstin.TC008_10_CreateLeadAddGstinEmptyState(FlowAddGstin.java:506)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC008_10_CreateLeadAddGstinEmptyState -->
  <testcase name="TC007_05_FetchLeadStatusAddGstin" time="0.302" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC002_createMerchantKyb" time="3.199" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC007_03_FetchLeadStatusAddGstinEmptyMid" time="0.618" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
  <testcase name="TC007_01_FetchLeadStatusAddGstinWrongMid" time="0.506" classname="OCL.Individual.ProfileUpdate.FlowAddGstin"/>
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowAddGstin -->
