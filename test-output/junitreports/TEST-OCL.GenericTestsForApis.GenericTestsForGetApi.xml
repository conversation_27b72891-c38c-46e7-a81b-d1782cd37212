<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.GenericTestsForApis.GenericTestsForGetApi" tests="10" failures="0" timestamp="2021-11-17T11:52:54 IST" time="106.948" errors="0">
  <testcase name="TC_0001_FetchBankProfileUpdate" time="15.388" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0008_FetchTnCv1UpgradeMid" time="3.918" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0009_FetchLeadv1UpgradeMid" time="44.803" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0010_FetchCombineLeadStatusV1ProfileUpdate" time="3.879" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0002_FetchLeadDetailsV1SdMerchant" time="11.577" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0004_FetchSubCategoryV1Ump" time="4.048" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0003_FetchCategoryV1Ump" time="4.269" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0007_FetchBusinessV1UpgradeMid" time="4.491" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0006_FetchLeadStatusV1UpgradeMid" time="9.386" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
  <testcase name="TC_0005_FetchBankListV1SdMerchant" time="5.189" classname="OCL.GenericTestsForApis.GenericTestsForGetApi"/>
</testsuite> <!-- OCL.GenericTestsForApis.GenericTestsForGetApi -->
