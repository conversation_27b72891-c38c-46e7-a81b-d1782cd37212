<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.TestBusinessLendingV2" tests="35" failures="1" timestamp="2022-06-28T15:58:55 IST" time="56.992" errors="0">
  <testcase name="TC038_UploadSheetONPanel" time="4.462" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC011_VerifyLeadStage" time="0.483" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC010_BREValidationPending" time="0.611" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC035_SubmitApplication" time="1.332" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC002_FetchLeadDeatils" time="1.223" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC013_VerifyLeadStage" time="0.449" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC006_AddBasicDetails" time="0.696" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC009_BREOTPVerification" time="0.726" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC021_VerifyUploadedDocument" time="0.569" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC001_DeleteExistingLead" time="1.186" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC037_EmandateCallback" time="0.848" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC020_UploadSelfie" time="2.908" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC030_UpdateKYCNameInSAI" time="0.446" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC022_UploadCustomerPhoto" time="3.032" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC025_VerifyLeadStage" time="0.667" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC017_FetchLeadStage" time="0.522" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC033_FetchDynamicTncSanctionLetter" time="0.785" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC024_CKYCCallback" time="0.999" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC034_VerifyLeadStage" time="0.490" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC008_UpdateGenderAndPincodeDetails" time="0.572" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC016_OTPCallback" time="0.736" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC023_VerifyUploadedDocument" time="0.533" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC005_PPBLOTPCallback" time="1.883" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC007_UpdatePANAndDOBDetails" time="0.728" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC015_VerifyLeadStage" time="0.524" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC039_VerifyLeadStage" time="7.957" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2">
    <failure type="java.lang.AssertionError" message="did not expect to find [LMS_SUBMIT_APPLICATION_SUCCESS] but found [KAFKA_PUSH_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LMS_SUBMIT_APPLICATION_SUCCESS] but found [KAFKA_PUSH_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.TestBusinessLendingV2.TC039_VerifyLeadStage(TestBusinessLendingV2.java:1351)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC039_VerifyLeadStage -->
  <testcase name="TC014_CheckBREResponse" time="8.897" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC019_VerifyLeadStage" time="0.488" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC003_CreateBusinessLendingLead" time="6.502" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC012_FetchBREResponse" time="1.003" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC026_UpdateBureauDataSetInSAI" time="0.469" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC004_FetchTheCreatedLeadDeatils" time="1.115" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC032_FetchDynamicTnc" time="1.392" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC031_SaveBankDetails" time="1.210" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
  <testcase name="TC036_VerifyLeadStage" time="0.549" classname="OCL.Lending.BusinessLending.TestBusinessLendingV2"/>
</testsuite> <!-- OCL.Lending.BusinessLending.TestBusinessLendingV2 -->
