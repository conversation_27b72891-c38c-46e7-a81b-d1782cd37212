<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="ats.SKU.SKUTest" tests="48" failures="0" timestamp="2022-07-13T13:28:49 IST" time="21.779" errors="48">
  <testcase name="createSKUNotBarcodeEnable" time="0.735" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUNotBarcodeEnable(SKUTest.java:73)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUNotBarcodeEnable -->
  <testcase name="createSKUBarcodeEnable" time="0.492" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUBarcodeEnable(SKUTest.java:126)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUBarcodeEnable -->
  <testcase name="createSKUNotFactoryGenerated" time="0.411" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUNotFactoryGenerated(SKUTest.java:179)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUNotFactoryGenerated -->
  <testcase name="createSKUFactoryGenerated" time="0.396" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUFactoryGenerated(SKUTest.java:232)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUFactoryGenerated -->
  <testcase name="createSubPartSKUWithBarcodeEnable" time="1.341" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSubSKURequest(MiddlewareServices.java:4376)
at ats.SKU.SKUTest.createSubPartSKUWithBarcodeEnable(SKUTest.java:258)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSubPartSKUWithBarcodeEnable -->
  <testcase name="createSubPartSKUWithoutBarcodeEnable" time="0.346" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSubSKURequest(MiddlewareServices.java:4376)
at ats.SKU.SKUTest.createSubPartSKUWithoutBarcodeEnable(SKUTest.java:286)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSubPartSKUWithoutBarcodeEnable -->
  <testcase name="createSubPartSKUFactoryGenerated" time="0.265" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSubSKURequest(MiddlewareServices.java:4376)
at ats.SKU.SKUTest.createSubPartSKUFactoryGenerated(SKUTest.java:314)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSubPartSKUFactoryGenerated -->
  <testcase name="createSubPartSKUWithoutFactoryGenerated" time="0.279" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSubSKURequest(MiddlewareServices.java:4376)
at ats.SKU.SKUTest.createSubPartSKUWithoutFactoryGenerated(SKUTest.java:342)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSubPartSKUWithoutFactoryGenerated -->
  <testcase name="createIndividualSKU" time="0.498" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createIndividualSKU(SKUTest.java:396)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createIndividualSKU -->
  <testcase name="createIndividualSKUWithPack" time="0.271" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createIndividualSKUWithPack(SKUTest.java:449)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createIndividualSKUWithPack -->
  <testcase name="createIndividualSKUWithoutPack" time="0.280" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createIndividualSKUWithoutPack(SKUTest.java:502)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createIndividualSKUWithoutPack -->
  <testcase name="createIndividualSKUWithReturnable" time="0.271" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createIndividualSKUWithReturnable(SKUTest.java:555)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createIndividualSKUWithReturnable -->
  <testcase name="createIndividualSKUWithoutReturnable" time="0.259" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createIndividualSKUWithoutReturnable(SKUTest.java:608)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createIndividualSKUWithoutReturnable -->
  <testcase name="createSKUWithSubPartEnable_WithIncorrectSubpartID" time="0.385" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKURequest(MiddlewareServices.java:4351)
at ats.SKU.SKUTest.createSKUWithSubPartEnable_WithIncorrectSubpartID(SKUTest.java:666)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSubPartEnable_WithIncorrectSubpartID -->
  <testcase name="createSKUWithSubPartEnable_WithCorrectSubpartID" time="0.263" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKURequest(MiddlewareServices.java:4351)
at ats.SKU.SKUTest.createSKUWithSubPartEnable_WithCorrectSubpartID(SKUTest.java:723)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSubPartEnable_WithCorrectSubpartID -->
  <testcase name="createSKUWithSubPartEnable_WithMorethanOneSubparts" time="0.712" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKURequest(MiddlewareServices.java:4351)
at ats.SKU.SKUTest.createSKUWithSubPartEnable_WithMorethanOneSubparts(SKUTest.java:780)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSubPartEnable_WithMorethanOneSubparts -->
  <testcase name="createIndividualSKUWithoutSubPartEnable" time="0.475" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createIndividualSKUWithoutSubPartEnable(SKUTest.java:833)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createIndividualSKUWithoutSubPartEnable -->
  <testcase name="createSKUWithSkipValidationDisable" time="0.309" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithSkipValidationDisable(SKUTest.java:886)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSkipValidationDisable -->
  <testcase name="createSKUWithSkipValidationBypassForExistingBarcode" time="0.296" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithSkipValidationBypassForExistingBarcode(SKUTest.java:939)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSkipValidationBypassForExistingBarcode -->
  <testcase name="createSKUWithSkipValidationBypassForAllBarcodes" time="0.354" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithSkipValidationBypassForAllBarcodes(SKUTest.java:992)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSkipValidationBypassForAllBarcodes -->
  <testcase name="createSKUWithoutSelectCategory" time="0.362" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithoutSelectCategory(SKUTest.java:1045)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithoutSelectCategory -->
  <testcase name="createSKUWithSelectCategory" time="0.376" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithSelectCategory(SKUTest.java:1098)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSelectCategory -->
  <testcase name="createSKUWithAttribute_IMEI_Enable" time="0.643" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithAttribute_IMEI_Enable(SKUTest.java:1151)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithAttribute_IMEI_Enable -->
  <testcase name="createSKUWithAttribute_SIM_Enable" time="0.320" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithAttribute_SIM_Enable(SKUTest.java:1205)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithAttribute_SIM_Enable -->
  <testcase name="createSKUWithAttribute_MobileNumber_Enable" time="0.495" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithAttribute_MobileNumber_Enable(SKUTest.java:1259)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithAttribute_MobileNumber_Enable -->
  <testcase name="createSKUWith_WithCorrectSuplierID" time="0.309" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWith_WithCorrectSuplierID(SKUTest.java:1312)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWith_WithCorrectSuplierID -->
  <testcase name="createSKUWithSubPartEnable_WithIncorrectSuplierID" time="0.441" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWithSubPartEnable_WithIncorrectSuplierID(SKUTest.java:1365)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSubPartEnable_WithIncorrectSuplierID -->
  <testcase name="createSKUWith_WithMoreThanOneSupliers" time="0.357" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKUWithoutSubPartRequest(MiddlewareServices.java:4326)
at ats.SKU.SKUTest.createSKUWith_WithMoreThanOneSupliers(SKUTest.java:1418)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWith_WithMoreThanOneSupliers -->
  <testcase name="createSKUWithSubPartEnable_WithPrice" time="0.403" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKURequest(MiddlewareServices.java:4351)
at ats.SKU.SKUTest.createSKUWithSubPartEnable_WithPrice(SKUTest.java:1475)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSubPartEnable_WithPrice -->
  <testcase name="createSKUWithSubPartEnable_WithoutPrice" time="0.333" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSCreateSKURequest(MiddlewareServices.java:4351)
at ats.SKU.SKUTest.createSKUWithSubPartEnable_WithoutPrice(SKUTest.java:1532)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- createSKUWithSubPartEnable_WithoutPrice -->
  <testcase name="fetchSkuDetailsByCorrectSkuID" time="0.386" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSFetchSKUDetails(MiddlewareServices.java:4401)
at ats.SKU.SKUTest.fetchSkuDetailsByCorrectSkuID(SKUTest.java:1556)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchSkuDetailsByCorrectSkuID -->
  <testcase name="fetchSkuDetailsByIncorrectSkuID" time="0.341" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSFetchSKUDetails(MiddlewareServices.java:4401)
at ats.SKU.SKUTest.fetchSkuDetailsByIncorrectSkuID(SKUTest.java:1580)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchSkuDetailsByIncorrectSkuID -->
  <testcase name="getSkuListParams" time="0.316" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSGetSKUListOfParams(MiddlewareServices.java:4426)
at ats.SKU.SKUTest.getSkuListParams(SKUTest.java:1603)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- getSkuListParams -->
  <testcase name="searchSKUWithCorrectSkuID" time="0.280" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithCorrectSkuID(SKUTest.java:1627)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithCorrectSkuID -->
  <testcase name="searchSKUWithIncorrectSkuID" time="0.280" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithIncorrectSkuID(SKUTest.java:1652)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithIncorrectSkuID -->
  <testcase name="searchSKUWithIncorrectName" time="0.328" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithIncorrectName(SKUTest.java:1677)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithIncorrectName -->
  <testcase name="searchSKUWithExactName" time="0.282" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithExactName(SKUTest.java:1702)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithExactName -->
  <testcase name="searchSKUWithExactFalse" time="0.391" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithExactFalse(SKUTest.java:1727)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithExactFalse -->
  <testcase name="searchSKUWithCorrectCatagoryName" time="0.751" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithCorrectCatagoryName(SKUTest.java:1751)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithCorrectCatagoryName -->
  <testcase name="searchSKUWithIncorrectCatagoryName" time="0.272" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithIncorrectCatagoryName(SKUTest.java:1775)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithIncorrectCatagoryName -->
  <testcase name="searchSKUWithoutAnyQueryParam" time="0.275" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSSearchSKU(MiddlewareServices.java:4451)
at ats.SKU.SKUTest.searchSKUWithoutAnyQueryParam(SKUTest.java:1799)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- searchSKUWithoutAnyQueryParam -->
  <testcase name="updateSKU_ChangeSkipValidation_BypassForAllBarcodes" time="0.291" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSUpdateSKU(MiddlewareServices.java:4476)
at ats.SKU.SKUTest.updateSKU_ChangeSkipValidation_BypassForAllBarcodes(SKUTest.java:1824)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- updateSKU_ChangeSkipValidation_BypassForAllBarcodes -->
  <testcase name="updateSKU_ChangeSkipValidation_BypassForExitingBarcode" time="0.374" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSUpdateSKU(MiddlewareServices.java:4476)
at ats.SKU.SKUTest.updateSKU_ChangeSkipValidation_BypassForExitingBarcode(SKUTest.java:1849)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- updateSKU_ChangeSkipValidation_BypassForExitingBarcode -->
  <testcase name="updateSKU_ChangeSkipValidation_Disable" time="0.291" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSUpdateSKU(MiddlewareServices.java:4476)
at ats.SKU.SKUTest.updateSKU_ChangeSkipValidation_Disable(SKUTest.java:1874)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- updateSKU_ChangeSkipValidation_Disable -->
  <testcase name="updateSKU_ChangeDomain_ATS_domain_sound_box" time="2.971" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSUpdateSKU(MiddlewareServices.java:4476)
at ats.SKU.SKUTest.updateSKU_ChangeDomain_ATS_domain_sound_box(SKUTest.java:1900)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- updateSKU_ChangeDomain_ATS_domain_sound_box -->
  <testcase name="updateSKU_ChangeDomain_ATS_domain_fastag" time="0.514" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSUpdateSKU(MiddlewareServices.java:4476)
at ats.SKU.SKUTest.updateSKU_ChangeDomain_ATS_domain_fastag(SKUTest.java:1927)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- updateSKU_ChangeDomain_ATS_domain_fastag -->
  <testcase name="updateSKU_ChangeDomain_ATS_domain_EDC" time="0.382" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSUpdateSKU(MiddlewareServices.java:4476)
at ats.SKU.SKUTest.updateSKU_ChangeDomain_ATS_domain_EDC(SKUTest.java:1954)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- updateSKU_ChangeDomain_ATS_domain_EDC -->
  <testcase name="updateSKU_AddSupplier" time="0.377" classname="ats.SKU.SKUTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ATSUpdateSKU(MiddlewareServices.java:4476)
at ats.SKU.SKUTest.updateSKU_AddSupplier(SKUTest.java:1992)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- updateSKU_AddSupplier -->
</testsuite> <!-- ats.SKU.SKUTest -->
