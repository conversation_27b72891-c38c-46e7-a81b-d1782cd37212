<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack" tests="24" failures="0" timestamp="2022-08-02T18:48:01 IST" time="44.632" errors="0">
  <testcase name="TC020_PLv3HERO_UploadSelfie" time="4.299" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC016_PLv3ABFL_FetchDataPostKYCIntiated" time="1.285" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC005_UpdateLeadBasicDetails" time="1.454" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC015_MCAV3ABFL_InitiateKYC_UsingSearchByAadhaar" time="0.516" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC023_PLv3ABFL_FetchDataPostSelfieUploaded" time="2.876" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC019_UpdateSAIWithLenderDetails" time="0.616" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC012_MCAV3ABFL_InitiateKYC_UsingSearchByPan" time="0.381" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC013_PLv3ABFL_FetchDataPostKYCIntiated" time="0.437" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC004_FetchLeadAllData" time="0.732" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC014_MCAV3ABFL_UpdateLeadDetailsinSAI" time="0.355" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC024_VerifyLeadStage" time="3.401" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC010_UpdateExistingDetailsInSAI" time="0.350" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC003_CreateBTDistributionPiramalLead" time="1.464" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC008_FetchCIR" time="3.594" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC017_PLv3HERO_UploadCustomerPhoto" time="4.454" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC009_FetchLeadAllData" time="5.211" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC022_PLv3ABFL_FetchDataPostKYCIntiated" time="0.227" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.696" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC006_FetchLeadAllData" time="0.667" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC011_FetchLeadAllData" time="10.372" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC021_PLv3HERO_VerifyUploadedSelfie" time="0.316" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC002_DeleteExistingLead" time="0.297" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC018_PLv3HERO_VerifyUploadedCustomerPhoto" time="0.260" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.372" classname="OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack"/>
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAV3FullertonWithNewKYCStack -->
