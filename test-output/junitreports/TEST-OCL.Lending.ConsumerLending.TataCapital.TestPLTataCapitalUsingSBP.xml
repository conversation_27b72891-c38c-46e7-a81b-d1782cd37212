<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="35" name="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" time="34.204" errors="0" timestamp="2023-09-25T20:00:23 IST" skipped="22">
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC023_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC028_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC019_FetchLeadAllData_AfterAddressConfirmation" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_FetchLeadAllData_AfterAddressConfirmation -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC003_Create_PL_TCL_Lead" time="0.860"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC009_FetchLeadAllData_AfterBureauSuccess" time="3.995"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC011_LoanOfferAccept" time="1.190"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC015_InitiateKYC_UsingSearchByPan" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC031_VerifyPDCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC030_FetchLeadPostAgreementAcceptance" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_FetchLeadPostAgreementAcceptance -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="test" time="0.185"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC007_UpdateBureauDataSetInSAI" time="0.411"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC018_AddressConfirmation" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_AddressConfirmation -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC024_FetchLeadPostBankVerification" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC004_FetchLeadAllData" time="0.873"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC010_UpdateKYCDataSetInSAI" time="0.347"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC027_GenerateLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC020_CaptureAdditionalData" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_CaptureAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC008_FetchCIR" time="0.804"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC033_LMSDataCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_LMSDataCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC026_FetchLeadPostEmandate" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC001_FetchLeadDetails_PL_TCL" time="0.600"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC021_FetchLeadAllData_AfterAdditionalDataCaptured" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_FetchLeadAllData_AfterAdditionalDataCaptured -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC029_AcceptLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC002_DeleteExistingLead" time="0.352"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC014_VerifyUploadedSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC022_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC025_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC005_UpdateLeadBasicDetails" time="1.182"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC034_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC017_UpdateKYCAddress" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_UpdateKYCAddress -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC016_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC012_FetchLeadAllData_AfterOfferAcceptance" time="22.621">
    <failure message="did not expect to find [LIS_SUBMIT_SUCCESS] but found [LOAN_OFFER_ACCEPTED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [LIS_SUBMIT_SUCCESS] but found [LOAN_OFFER_ACCEPTED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP.TC012_FetchLeadAllData_AfterOfferAcceptance(TestPLTataCapitalUsingSBP.java:714)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC012_FetchLeadAllData_AfterOfferAcceptance -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC013_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC032_VerifyLeadStage_LMSSubmitApplicationJob" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP" name="TC006_FetchLeadAllData" time="0.784"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP -->
