<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="38" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPostPaidKYCReject" tests="46" failures="2" timestamp="2022-07-13T13:12:25 IST" time="24.907" errors="0">
  <testcase name="TC031_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC031_UpdateBureauDataSetInSAI -->
  <testcase name="TC028_AcceptOffer" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC028_AcceptOffer -->
  <testcase name="TC023_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC023_UpdateBureauDataSetInSAI -->
  <testcase name="TC045_CKYCIDNotFound" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC045_CKYCIDNotFound -->
  <testcase name="TC042_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC042_FetchLeadAllData -->
  <testcase name="TC038_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC038_UpdateBureauDataSetInSAI -->
  <testcase name="TC020_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC020_FetchLeadAllData -->
  <testcase name="TC051_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC051_VerifyUploadedDocument -->
  <testcase name="TC032_OfflineAadharValidationFailed" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC032_OfflineAadharValidationFailed -->
  <testcase name="TC034_CreatePostpaidLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC034_CreatePostpaidLead -->
  <testcase name="TC046_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC046_UpdateBureauDataSetInSAI -->
  <testcase name="TC009_UpdateBureauDataSetInSAI" time="0.416" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject"/>
  <testcase name="TC049_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC049_UploadSelfie -->
  <testcase name="TC008_FetchLeadAllData" time="0.863" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject"/>
  <testcase name="TC035_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC035_FetchLeadAllData -->
  <testcase name="TC010_FetchCIR" time="0.381" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <failure type="java.lang.AssertionError" message="did not expect to find [BUREAU_SUCCESS] but found [OFFER_REQUESTED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BUREAU_SUCCESS] but found [OFFER_REQUESTED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPostPaidKYCReject.TC010_FetchCIR(TestPostPaidKYCReject.java:402)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC010_FetchCIR -->
  <testcase name="TC037_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC037_FetchLeadAllData -->
  <testcase name="TC039_FetchCIR" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC039_FetchCIR -->
  <testcase name="TC013_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC013_FetchLeadAllData -->
  <testcase name="TC014_CKYCIDFound" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC014_CKYCIDFound -->
  <testcase name="TC015_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC015_UpdateBureauDataSetInSAI -->
  <testcase name="TC052_SelfieMatch" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC052_SelfieMatch -->
  <testcase name="TC044_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC044_FetchLeadAllData -->
  <testcase name="TC002_DeleteExistingLead" time="0.327" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject"/>
  <testcase name="TC048_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC048_FetchLeadAllData -->
  <testcase name="TC012_AcceptOffer" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC012_AcceptOffer -->
  <testcase name="TC003_CreatePostpaidLead" time="1.008" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject"/>
  <testcase name="TC024_FetchCIR" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC024_FetchCIR -->
  <testcase name="TC004_FetchLeadAllData" time="0.383" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject"/>
  <testcase name="TC036_UpdateLeadBasicDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC036_UpdateLeadBasicDetails -->
  <testcase name="TC016_SBPValidationSuccess" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC016_SBPValidationSuccess -->
  <testcase name="TC029_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC029_FetchLeadAllData -->
  <testcase name="TC030_CKYCIDFound" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC030_CKYCIDFound -->
  <testcase name="TC007_UpdateLeadBasicDetails" time="20.793" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <failure type="java.lang.AssertionError" message="did not expect to find [BASIC_DETAILS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BASIC_DETAILS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPostPaidKYCReject.TC007_UpdateLeadBasicDetails(TestPostPaidKYCReject.java:301)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC007_UpdateLeadBasicDetails -->
  <testcase name="TC011_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC011_FetchLeadAllData -->
  <testcase name="TC018_DeleteExistingLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC018_DeleteExistingLead -->
  <testcase name="TC021_UpdateLeadBasicDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC021_UpdateLeadBasicDetails -->
  <testcase name="TC0017_FetchLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC0017_FetchLeadDeatils -->
  <testcase name="TC022_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadAllData -->
  <testcase name="TC047_OfflineAadharValidationSuccess" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC047_OfflineAadharValidationSuccess -->
  <testcase name="TC050_UploadCustomerPhoto" time="0.001" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC050_UploadCustomerPhoto -->
  <testcase name="TC027_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC027_FetchLeadAllData -->
  <testcase name="TC033_DeleteExistingLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC033_DeleteExistingLead -->
  <testcase name="TC043_AcceptOffer" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC043_AcceptOffer -->
  <testcase name="TC001_FetchLeadDeatils" time="0.735" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject"/>
  <testcase name="TC019_CreatePostpaidLead" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostPaidKYCReject">
    <skipped/>
  </testcase> <!-- TC019_CreatePostpaidLead -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostPaidKYCReject -->
