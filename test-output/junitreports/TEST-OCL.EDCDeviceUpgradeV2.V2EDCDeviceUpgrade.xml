<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="1" tests="23" name="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" time="49.597" errors="0" timestamp="2024-08-23T20:11:57 IST" skipped="0">
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_004" time="2.220"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_010" time="2.132"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_021" time="2.993">
    <failure message="did not expect to find [410] but found [200]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [410] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.retryFailed(Invoker.java:926)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1127)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
    </failure>
  </testcase> <!-- TC_021 -->
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_011" time="2.126"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_022" time="0.910"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_016" time="2.903"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_007" time="1.896"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_006" time="1.853"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_015" time="3.108"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_012" time="2.232"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_008" time="2.967"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_003" time="1.064"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_019" time="2.039"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_020" time="2.445"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_005" time="2.232"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_017" time="2.194"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="test" time="0.344"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_013" time="2.224"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_014" time="2.523"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_018" time="2.123"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_009" time="2.290"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_001" time="3.578"/>
  <testcase classname="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" name="TC_002" time="1.201"/>
</testsuite> <!-- OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade -->
