<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="20" name="OCL.CommonOnboarding.EDC.FetchBeauruDetails" time="8.306" errors="0" timestamp="2023-07-18T22:09:00 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="test" time="0.282"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.362"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_1_getExistingLeadbeauru" time="1.986"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.546"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.302"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.538"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_2_ErrorWithoutSessionTokengetExistingLeadbeauru" time="0.321"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.255"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.399"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_4_GetBeauruErrorWithoutDeviceIdentifer" time="0.237"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.300"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.268"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.293"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.362"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.308"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.329"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.404"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.259"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.257"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.298"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.FetchBeauruDetails -->
