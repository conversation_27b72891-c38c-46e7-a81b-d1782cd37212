<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestStashfinNegativeCases" tests="62" failures="1" timestamp="2022-04-28T19:31:48 IST" time="61.279" errors="0">
  <testcase name="TC054_LISCallbackToLoanDisbursed_WithoutpassingLoanRepaymentAmount" time="0.821" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC025_UpdateLeadBasicDetails_WhenPincodeIsInvalid" time="0.847" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC053_LISCallbackToLoanDisbursed_WithoutpassingLoanRepaymentAmount" time="0.638" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC047_LISCallbackToLoanDisbursed_WithoutpassingLoanTenureUnit" time="0.608" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC013_CreateStashfinLead_WithoutPassingLocationLongitude" time="0.775" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC016_UpdateLeadBasicDetails_PassPanAsEmpty" time="0.745" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC029_LISCallbackToLoanAccepted_WithInvalidWorkflowOperation" time="0.597" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC036_LISCallbackToLoanSanctioned_WithoutpassingLoanRateOfInterestUnit" time="0.571" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC004_CreateStashfinLead_WithoutPassingStaticTNCSetName" time="0.921" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC050_LISCallbackToLoanDisbursed_WithoutpassingProcessingFeeRate" time="0.594" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC014_CreateStashfinLead_HappyCase" time="1.579" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC063_FetchLeadAllData" time="7.283" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases">
    <failure type="java.lang.AssertionError" message="did not expect to find [LEAD_NOT_PRESENT] but found [LOAN_ACCOUNT_ACKNOWLEDGED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LEAD_NOT_PRESENT] but found [LOAN_ACCOUNT_ACKNOWLEDGED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestStashfinNegativeCases.TC063_FetchLeadAllData(TestStashfinNegativeCases.java:3259)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC063_FetchLeadAllData -->
  <testcase name="TC012_CreateStashfinLead_WithoutPassingLocationLatitude" time="2.005" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC043_LISCallbackToLoanSanctionedFromLoanAccepted" time="0.988" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC049_LISCallbackToLoanDisbursed_WithoutpassingLoanRateOfInterest" time="0.535" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC038_LISCallbackToLoanSanctioned_WithoutpassingProcessingFeeRate" time="0.622" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC021_UpdateLeadBasicDetails_PassEmailAsEmpty" time="1.109" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.806" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC027_UpdateLeadBasicDetails_WhenleadIsAlreadyAtBasicDetailStage" time="0.859" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC028_FetchLeadAllData" time="1.278" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC005_CreateStashfinLead_WithoutPassingObligation" time="0.985" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC052_LISCallbackToLoanDisbursed_WithoutpassingInstallmentFrequency" time="1.007" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC002_DeleteExistingLead" time="0.691" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC018_UpdateLeadBasicDetails_PassDOBAsEmpty" time="0.968" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC058_LMSDataCallback_PassingEmptyLenderLan" time="0.537" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC009_CreateStashfinLead_WithoutPassingBaseId" time="0.857" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC011_CreateStashfinLead_WithoutPassingUserIPAddress" time="0.922" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC017_UpdateLeadBasicDetails_PassIncorrectPan" time="0.848" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC020_UpdateLeadBasicDetails_PassIncorrectEmailFormat" time="0.769" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC048_LISCallbackToLoanDisbursed_WithoutpassingLoanRateOfInterestUnit" time="0.641" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC045_LISCallbackToLoanDisbursed_WithoutpassingLoanAmount" time="0.906" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC034_LISCallbackToLoanSanctioned_WithoutpassingLoanTenure" time="0.557" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC051_LISCallbackToLoanDisbursed_WithoutpassingInstallmentAmount" time="0.618" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC035_LISCallbackToLoanSanctioned_WithoutpassingLoanTenureUnit" time="0.592" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC007_CreateStashfinLead_WithoutPassingFlowType" time="1.084" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC024_UpdateLeadBasicDetails_WhenOccupationIsEmpty" time="0.816" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC046_LISCallbackToLoanDisbursed_WithoutpassingLoanTenure" time="0.588" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC040_LISCallbackToLoanSanctioned_WithoutpassingInstallmentFrequency" time="0.589" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC056_FetchLeadAllData" time="0.974" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC057_LMSDataCallback_PassingEmptyLoanAccountNumber" time="0.630" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC019_UpdateLeadBasicDetails_PassIncorrectDOBFormat" time="0.697" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC033_LISCallbackToLoanSanctioned_WithoutpassingLoanAmount" time="0.694" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC042_LISCallbackToLoanSanctioned_WithoutpassingLoanRepaymentAmount" time="0.655" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC041_LISCallbackToLoanSanctioned_WithoutpassingLoanRepaymentAmount" time="0.637" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC031_LISCallbackToLoanAccepted" time="0.645" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC008_CreateStashfinLead_WithoutPassingLenderId" time="0.963" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC062_LMSDataCallback" time="5.091" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC037_LISCallbackToLoanSanctioned_WithoutpassingLoanRateOfInterest" time="0.613" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC010_CreateStashfinLead_WithoutPassingOfferId" time="0.861" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC055_LISCallbackToLoanDisbursedFromLoanSanctioned" time="1.071" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC023_UpdateLeadBasicDetails_PassInvalidGender" time="0.759" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC044_LISCallbackToLoanDisbursed_WithoutpassingApplicationId" time="0.548" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC032_LISCallbackToLoanSanctioned_WithoutpassingApplicationId" time="0.869" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC059_LMSDataCallback_PassingEmptyAccountStatus" time="0.542" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC060_LMSDataCallback_PassingEmptyAccountCreationDate" time="0.566" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC003_CreateStashfinLead_WithoutPassingMobileNumber" time="0.858" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC015_CreateStashfinLead_WhenOneLeadAlreadyExists" time="0.654" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC026_UpdateLeadBasicDetails" time="1.791" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC022_UpdateLeadBasicDetails_PassGenderAsEmpty" time="0.737" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC030_LISCallbackToLoanAccepted" time="0.673" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC039_LISCallbackToLoanSanctioned_WithoutpassingInstallmentAmount" time="0.612" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
  <testcase name="TC006_CreateStashfinLead_WithoutPassingProductType" time="0.983" classname="OCL.Lending.ConsumerLending.TestStashfinNegativeCases"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestStashfinNegativeCases -->
