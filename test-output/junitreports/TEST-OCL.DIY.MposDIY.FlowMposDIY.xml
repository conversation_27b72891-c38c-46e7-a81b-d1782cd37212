<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="2" hostname="197NODMB24984.local" name="OCL.DIY.MposDIY.FlowMposDIY" tests="35" failures="3" timestamp="2022-07-13T12:28:08 IST" time="63.598" errors="6">
  <testcase name="fetchPlanforMposDIY" time="0.651" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MposDIY.FlowMposDIY.fetchPlanforMposDIY(FlowMposDIY.java:88)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchPlanforMposDIY -->
  <testcase name="fetchPlanforInvalidMidMposDIY" time="1.420" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchProductIdforEMIOfferingDIY" time="1.404" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPriceforMposDIY" time="1.095" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchRentalAmountforMposDIY" time="1.127" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchMposPlanIdforMposDIY" time="1.620" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPlanforInvalidSolutionTypeMposDIY" time="0.396" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPlanWithAdditionalParamsforMposDIY" time="1.321" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPlanWithSmallMerchantEditDisableFlagMposDIY" time="1.146" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPlanWithSmallMerchantFlagMposDIY" time="1.028" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="AcceptTermsAndConditionsEdcDIY" time="0.357" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="validateOrderforMposDiy" time="0.310" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="invalidPriceforMposDiy" time="0.672" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="invalidPlanIdforMposDiy" time="0.412" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="invalidProductIdforMposDiy" time="0.394" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="invalidSolutionTypeforMposDiy" time="0.353" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="createAccessTokenMposDIY" time="0.218" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="createQRforMposDiy" time="3.851" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="LeadAlreadyExistMposDiy" time="0.407" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="createLeadMposDiy" time="0.497" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPaymentStatusforMposDiy" time="0.849" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchLeadMposDiyBeforeLeadCreation" time="0.743" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchLeadMposDiyAfterLeadCreation" time="0.659" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="OrderNotifyMposDiy" time="0.000" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <skipped/>
  </testcase> <!-- OrderNotifyMposDiy -->
  <testcase name="OrderNotifyMposDiyWithoutOrderID" time="0.351" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MposDIY.FlowMposDIY.OrderNotifyMposDiyWithoutOrderID(FlowMposDIY.java:594)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- OrderNotifyMposDiyWithoutOrderID -->
  <testcase name="fetchLeadMposWithInvalidChannel" time="0.363" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:993)
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadMposWithInvalidChannel(FlowMposDIY.java:694)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 17 more
]]>
    </error>
  </testcase> <!-- fetchLeadMposWithInvalidChannel -->
  <testcase name="fetchLeadMposDiyAfterCallback" time="0.000" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <skipped/>
  </testcase> <!-- fetchLeadMposDiyAfterCallback -->
  <testcase name="CallbackinprogressMposDiy" time="0.461" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MposDIY.FlowMposDIY.CallbackinprogressMposDiy(FlowMposDIY.java:652)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- CallbackinprogressMposDiy -->
  <testcase name="fetchLeadMposDiy" time="6.003" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at com.goldengate.common.BaseMethod.findXMWTokenforPanel(BaseMethod.java:1728)
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadMposDiy(FlowMposDIY.java:706)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchLeadMposDiy -->
  <testcase name="fetchStatusLeadMposDiy" time="11.837" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at com.goldengate.common.BaseMethod.findXMWTokenforPanel(BaseMethod.java:1728)
at OCL.DIY.MposDIY.FlowMposDIY.fetchStatusLeadMposDiy(FlowMposDIY.java:718)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchStatusLeadMposDiy -->
  <testcase name="AddGSTForMpos" time="1.662" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MposDIY.FlowMposDIY.AddGSTForMpos(FlowMposDIY.java:745)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- AddGSTForMpos -->
  <testcase name="IsGSTValidForMpos" time="1.239" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="IsNameMatchSuccessForMpos" time="1.040" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchLeadPgProfileLead" time="7.039" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadPgProfileLead(FlowMposDIY.java:810)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchLeadPgProfileLead -->
  <testcase name="fetchLeadPgProfileLeadAfterMaquette" time="12.673" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadPgProfileLeadAfterMaquette(FlowMposDIY.java:822)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchLeadPgProfileLeadAfterMaquette -->
</testsuite> <!-- OCL.DIY.MposDIY.FlowMposDIY -->
