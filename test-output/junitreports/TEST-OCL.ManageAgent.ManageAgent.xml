<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.ManageAgent.ManageAgent" tests="11" failures="2" timestamp="2021-11-17T11:52:54 IST" time="38.682" errors="2">
  <testcase name="getOE_XMW" time="0.292" classname="OCL.ManageAgent.ManageAgent"/>
  <testcase name="searchAgentEmptyNumber" time="1.537" classname="OCL.ManageAgent.ManageAgent"/>
  <testcase name="UpdateAgent" time="10.898" classname="OCL.ManageAgent.ManageAgent">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.ManageAgent.ManageAgent.UpdateAgent(ManageAgent.java:378)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- UpdateAgent -->
  <testcase name="UpdateAgentWithIncorrectId" time="4.148" classname="OCL.ManageAgent.ManageAgent">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.ManageAgent.ManageAgent.UpdateAgentWithIncorrectId(ManageAgent.java:333)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- UpdateAgentWithIncorrectId -->
  <testcase name="UpdateAgentWithoutMobile" time="2.027" classname="OCL.ManageAgent.ManageAgent"/>
  <testcase name="searchAgentInvalidNumber" time="5.031" classname="OCL.ManageAgent.ManageAgent"/>
  <testcase name="AddAgentWithNoRoles" time="3.238" classname="OCL.ManageAgent.ManageAgent"/>
  <testcase name="UpdateAgentWithNoRoles" time="2.067" classname="OCL.ManageAgent.ManageAgent"/>
  <testcase name="searchAgentNoTokenHeader" time="5.810" classname="OCL.ManageAgent.ManageAgent"/>
  <testcase name="searchAgentPositive" time="1.883" classname="OCL.ManageAgent.ManageAgent">
    <error type="java.lang.RuntimeException" message="Validation against Json schema failed: 
[]: object has missing required properties ([&quot;address&quot;,&quot;agentName&quot;,&quot;agentTeam&quot;,&quot;bankAgentType&quot;,&quot;bucketZoneStates&quot;,&quot;createdAt&quot;,&quot;emailId&quot;,&quot;groups&quot;,&quot;logoutRequired&quot;,&quot;roleList&quot;,&quot;userAdditionalInfo&quot;])
">
      <![CDATA[java.lang.RuntimeException: Validation against Json schema failed: 
[]: object has missing required properties (["address","agentName","agentTeam","bankAgentType","bucketZoneStates","createdAt","emailId","groups","logoutRequired","roleList","userAdditionalInfo"])

at com.paytm.apitools.validation.JsonValidator.validateJsonAgainstSchema(JsonValidator.java:74)
at com.paytm.apitools.core.AbstractApiV2.validateResponseAgainstJSONSchema(AbstractApiV2.java:206)
at OCL.ManageAgent.ManageAgent.searchAgentPositive(ManageAgent.java:144)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </error>
  </testcase> <!-- searchAgentPositive -->
  <testcase name="searchAgentPositiveOnlyTokenHeader" time="1.751" classname="OCL.ManageAgent.ManageAgent">
    <error type="java.lang.RuntimeException" message="Validation against Json schema failed: 
[]: object has missing required properties ([&quot;address&quot;,&quot;agentName&quot;,&quot;agentTeam&quot;,&quot;bankAgentType&quot;,&quot;bucketZoneStates&quot;,&quot;createdAt&quot;,&quot;emailId&quot;,&quot;groups&quot;,&quot;logoutRequired&quot;,&quot;roleList&quot;,&quot;userAdditionalInfo&quot;])
">
      <![CDATA[java.lang.RuntimeException: Validation against Json schema failed: 
[]: object has missing required properties (["address","agentName","agentTeam","bankAgentType","bucketZoneStates","createdAt","emailId","groups","logoutRequired","roleList","userAdditionalInfo"])

at com.paytm.apitools.validation.JsonValidator.validateJsonAgainstSchema(JsonValidator.java:74)
at com.paytm.apitools.core.AbstractApiV2.validateResponseAgainstJSONSchema(AbstractApiV2.java:206)
at OCL.ManageAgent.ManageAgent.searchAgentPositiveOnlyTokenHeader(ManageAgent.java:120)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </error>
  </testcase> <!-- searchAgentPositiveOnlyTokenHeader -->
</testsuite> <!-- OCL.ManageAgent.ManageAgent -->
