<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="0" tests="33" name="OCL.Lending.ConsumerLending.TestPostPaidClix" time="30.135" errors="1" timestamp="2021-07-05T14:47:46 IST" skipped="10">
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC023_CheckBREResponse" time="3.385">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.base/java.math.BigInteger.<init>(BigInteger.java:479)
at java.base/java.math.BigInteger.<init>(BigInteger.java:672)
at OCL.Lending.ConsumerLending.TestPostPaidClix.TC023_CheckBREResponse(TestPostPaidClix.java:597)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </error>
  </testcase> <!-- TC023_CheckBREResponse -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC028_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC024_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC019_BREValidationPendingCallback" time="0.985"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC011_CheckCKYCStatus" time="1.384"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC018_VerifyLeadStage" time="0.876"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC001_DeleteExistingLead" time="0.624"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC016_VerifyUploadedDocument" time="0.707"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC015_UploadCustomerPhoto" time="3.616"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC006_FetchTheCreatedLeadDeatils" time="0.875"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC032_LoanStatusCallbackAfterSubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_LoanStatusCallbackAfterSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC013_UploadSelfie" time="3.013"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC033_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC020_VerifyLeadStage" time="0.728"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC002_FetchLeadDeatils" time="0.926"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC030_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC031_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC005_PPBLOTPCallback" time="1.813"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC008_FetchTheLeadDeatils" time="0.742"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC021_FetchBREResponse" time="0.934"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC012_VerifyLeadStage" time="1.570"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC010_FetchLeadStage" time="0.697"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC009_OTPCallback" time="0.745"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC029_FetchDynamicTncSanctionLetter" time="0.001">
    <skipped/>
  </testcase> <!-- TC029_FetchDynamicTncSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC017_CKYCCallback" time="1.201"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC022_VerifyLeadStage" time="0.772"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC004_FetchTheCreatedLeadDeatils" time="1.441"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC003_CreatePostpaidLead" time="1.573"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC007_AddBasicDetails" time="0.839"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC014_VerifyUploadedDocument" time="0.688"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC025_AddAddress" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_AddAddress -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC026_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaidClix" name="TC027_FetchDynamicTnc" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_FetchDynamicTnc -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostPaidClix -->
