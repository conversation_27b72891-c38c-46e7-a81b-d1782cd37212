<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="8" name="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" time="6.847" errors="0" timestamp="2023-07-18T11:24:31 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="TC_006_GetMerchantMIDWithoutMobilenumber" time="0.752"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="TC_001_GetMerchantMID" time="0.875"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="test" time="0.197"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="TC_003_GetMerchantMIDWithoutDeviceIdentifer" time="0.750"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="TC_006_GetMerchantMIDWithoutSessionToken" time="0.625"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="TC_004_GetMerchantMIDWithoutSolutionType" time="1.160"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="TC_002_GetMerchantMIDWithoutVersion" time="1.686"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GetMerchantMIDStatus" name="TC_005_GetMerchantMIDWithInvalidSolutionType" time="0.802"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.GetMerchantMIDStatus -->
