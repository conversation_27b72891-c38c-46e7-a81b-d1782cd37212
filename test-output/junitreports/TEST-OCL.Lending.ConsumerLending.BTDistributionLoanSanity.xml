<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="10" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.BTDistributionLoanSanity" tests="21" failures="1" timestamp="2022-06-27T17:51:23 IST" time="16.582" errors="0">
  <testcase name="TC002_DeleteExistingLead" time="0.499" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC012_UpdateAdditionalDetailsWithCoApplicantDetails" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC012_UpdateAdditionalDetailsWithCoApplicantDetails -->
  <testcase name="TC014_AcceptLoanOffer" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC014_AcceptLoanOffer -->
  <testcase name="TC001_FetchLeadDeatils" time="1.216" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC009_FetchLeadAllData" time="1.930" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC019_LISCallbackToLoanDisbursedFromLoanSanctioned" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC019_LISCallbackToLoanDisbursedFromLoanSanctioned -->
  <testcase name="TC018_CallbackToLoanSanctioned" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC018_CallbackToLoanSanctioned -->
  <testcase name="TC010_UpdateExistingDetailsInSAI" time="0.457" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC004_FetchLeadAllData" time="1.425" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC005_UpdateLeadBasicDetails" time="0.991" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC011_FetchLeadAllData" time="3.703" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <failure type="java.lang.AssertionError" message="did not expect to find [BRE1_SUCCESS] but found [BRE1_FAILURE]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE1_SUCCESS] but found [BRE1_FAILURE]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.BTDistributionLoanSanity.TC011_FetchLeadAllData(BTDistributionLoanSanity.java:594)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC011_FetchLeadAllData -->
  <testcase name="TC020_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC020_FetchLeadAllData -->
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.501" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC006_FetchLeadAllData" time="1.072" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC016_UpdateToAppointmentBookedSuccessStage" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC016_UpdateToAppointmentBookedSuccessStage -->
  <testcase name="TC008_FetchCIR" time="3.430" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
  <testcase name="TC013_UpdateCoApplicantDetails" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC013_UpdateCoApplicantDetails -->
  <testcase name="TC021_LMSDataCallback" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC021_LMSDataCallback -->
  <testcase name="TC022_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadAllData -->
  <testcase name="TC015_UpdateToAppointmentBookedFailureNode" time="0.000" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity">
    <skipped/>
  </testcase> <!-- TC015_UpdateToAppointmentBookedFailureNode -->
  <testcase name="TC003_CreateBTDistributionPiramalLead" time="1.358" classname="OCL.Lending.ConsumerLending.BTDistributionLoanSanity"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.BTDistributionLoanSanity -->
