<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow" tests="36" failures="2" timestamp="2022-07-13T13:25:47 IST" time="67.059" errors="21">
  <testcase name="TC0032_PositiveCustID" time="0.476" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0009_fetchLeadStatus" time="0.343" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0021_fetchAllBusiness" time="0.357" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0021_fetchAllBusiness(TestOnlineMerchantV1CancelChequeFlow.java:910)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0021_fetchAllBusiness -->
  <testcase name="TC0036_PositiveGetOEPanelCookieUnlimited" time="42.350" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0027_postUpdateAdditionalDetails" time="0.107" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateAdditonalDetails(MiddlewareServices.java:3828)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0027_postUpdateAdditionalDetails(TestOnlineMerchantV1CancelChequeFlow.java:1158)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0027_postUpdateAdditionalDetails -->
  <testcase name="TC0019_fetchAllBusiness" time="0.362" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0019_fetchAllBusiness(TestOnlineMerchantV1CancelChequeFlow.java:848)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0019_fetchAllBusiness -->
  <testcase name="TC0033_PositiveFetchLeadPanelOnlinMerchant" time="12.406" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0005_postCreateLead" time="0.349" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0005_postCreateLead(TestOnlineMerchantV1CancelChequeFlow.java:345)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- TC0005_postCreateLead -->
  <testcase name="TC0023_fetchAllBusiness" time="0.434" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0023_fetchAllBusiness(TestOnlineMerchantV1CancelChequeFlow.java:973)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0023_fetchAllBusiness -->
  <testcase name="TC0017_paymentsLeadStatus" time="0.501" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0017_paymentsLeadStatus(TestOnlineMerchantV1CancelChequeFlow.java:775)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0017_paymentsLeadStatus -->
  <testcase name="TC0016_paymentsLeadStatus" time="0.461" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0003_fetchAllBusiness" time="0.355" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0030_postTncUpdate" time="0.117" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineMerchantUpdateTnc(MiddlewareServices.java:4006)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0030_postTncUpdate(TestOnlineMerchantV1CancelChequeFlow.java:1287)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0030_postTncUpdate -->
  <testcase name="TC0001_fetchAllBusiness" time="0.419" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0028_postValidateBankDetailsTest" time="0.491" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [404]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [404]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0028_postValidateBankDetailsTest(TestOnlineMerchantV1CancelChequeFlow.java:1213)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0028_postValidateBankDetailsTest -->
  <testcase name="TC0024_fetchAllBusiness" time="0.473" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0002_fetchAllBusiness" time="0.332" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0022_fetchAllBusiness" time="0.382" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0022_fetchAllBusiness(TestOnlineMerchantV1CancelChequeFlow.java:941)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0022_fetchAllBusiness -->
  <testcase name="TC0012_fetchLeadData" time="0.097" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLeadData(MiddlewareServices.java:3698)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0012_fetchLeadData(TestOnlineMerchantV1CancelChequeFlow.java:592)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0012_fetchLeadData -->
  <testcase name="TC0020_fetchAllBusiness" time="1.421" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0020_fetchAllBusiness(TestOnlineMerchantV1CancelChequeFlow.java:879)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0020_fetchAllBusiness -->
  <testcase name="TC0034_PostAllocateAgent" time="0.096" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at com.goldengate.common.BaseMethod.ReallocatingAgent(BaseMethod.java:453)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0034_PostAllocateAgent(TestOnlineMerchantV1CancelChequeFlow.java:1405)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0034_PostAllocateAgent -->
  <testcase name="TC0011_fetchLeadData" time="0.194" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLeadData(MiddlewareServices.java:3698)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0011_fetchLeadData(TestOnlineMerchantV1CancelChequeFlow.java:558)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0011_fetchLeadData -->
  <testcase name="TC0029_postUpdateBankDetailsTest" time="0.098" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineMerchantUpdateBank(MiddlewareServices.java:3992)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0029_postUpdateBankDetailsTest(TestOnlineMerchantV1CancelChequeFlow.java:1256)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0029_postUpdateBankDetailsTest -->
  <testcase name="TC0031_postUploadCancelCheque" time="0.106" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchDocumentStatusv2(MiddlewareServices.java:1730)
at com.goldengate.common.BaseMethod.FetchUploadDiyDoc(BaseMethod.java:935)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0031_postUploadCancelCheque(TestOnlineMerchantV1CancelChequeFlow.java:1346)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0031_postUploadCancelCheque -->
  <testcase name="TC0010_fetchLeadStatus" time="0.297" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLead(MiddlewareServices.java:3678)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0010_fetchLeadStatus(TestOnlineMerchantV1CancelChequeFlow.java:523)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0010_fetchLeadStatus -->
  <testcase name="TC0035_PositiveSubmitLeadPanel500K" time="0.304" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [405]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [405]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0035_PositiveSubmitLeadPanel500K(TestOnlineMerchantV1CancelChequeFlow.java:1432)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0035_PositiveSubmitLeadPanel500K -->
  <testcase name="TC0026_postUpdateBusiness" time="0.094" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateBusiness(MiddlewareServices.java:3805)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0026_postUpdateBusiness(TestOnlineMerchantV1CancelChequeFlow.java:1066)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0026_postUpdateBusiness -->
  <testcase name="TC0015_paymentsLeadStatus" time="0.410" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0025_fetchAllBusiness" time="0.354" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0008_fetchLeadStatus" time="0.109" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLead(MiddlewareServices.java:3678)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0008_fetchLeadStatus(TestOnlineMerchantV1CancelChequeFlow.java:457)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0008_fetchLeadStatus -->
  <testcase name="TC0014_fetchLeadData" time="0.706" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0004_fetchAllBusiness" time="0.352" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow"/>
  <testcase name="TC0013_fetchLeadData" time="0.100" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLeadData(MiddlewareServices.java:3698)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0013_fetchLeadData(TestOnlineMerchantV1CancelChequeFlow.java:628)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0013_fetchLeadData -->
  <testcase name="TC0018_paymentsLeadStatus" time="1.105" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0018_paymentsLeadStatus(TestOnlineMerchantV1CancelChequeFlow.java:811)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0018_paymentsLeadStatus -->
  <testcase name="TC0007_fetchLeadStatus" time="0.090" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLead(MiddlewareServices.java:3678)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0007_fetchLeadStatus(TestOnlineMerchantV1CancelChequeFlow.java:423)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0007_fetchLeadStatus -->
  <testcase name="TC0006_postCreateLead" time="0.411" classname="OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow.TC0006_postCreateLead(TestOnlineMerchantV1CancelChequeFlow.java:390)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- TC0006_postCreateLead -->
</testsuite> <!-- OCL.DIY.OnlineMerchant.TestOnlineMerchantV1CancelChequeFlow -->
