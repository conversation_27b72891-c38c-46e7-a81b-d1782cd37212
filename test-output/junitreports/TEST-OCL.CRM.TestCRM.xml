<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB27706.local" failures="0" tests="47" name="OCL.CRM.TestCRM" time="14.439" errors="0" timestamp="2021-12-22T17:17:47 IST" skipped="2">
  <testcase classname="OCL.CRM.TestCRM" name="TC15_CreateEventWithoutRoles" time="0.199"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC07_CreateEventWithoutContextKey" time="0.221"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC06_CreateEventWithoutContextValue" time="0.227"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC05_CreateEventWithoutEventType" time="4.457"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC17_CreateEventWithoutSolutionType" time="0.207"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC19_CreateEventWithCorrectLeadId" time="0.213"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC12_CreateEventWithoutCustId" time="0.242"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC11_CreateEventWithoutEndTime" time="0.217"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC09_CreateEventWithoutDescription" time="0.288"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC24_CreateEventWithCustomizeRole" time="0.321"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC22_CreateEventWhenStartTimeLessThanEndTime" time="0.211"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC18_CreateEventWithInvalidEventType" time="0.187"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC08_CreateEventWithoutTitle" time="0.237"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC16_CreateEventWithoutSolutionSubType" time="0.239"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC14_CreateEventWithoutMobileNumber" time="0.224"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC25_CreateEventWithThreeStakeHolders" time="0.234"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC13_CreateEventWithoutEmail" time="0.209"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC20_CreateEventWithDifferentRole" time="0.229"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC46_CreateEventWithOneRoleEmpty" time="0.192"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC10_CreateEventWithoutStartTime" time="0.209"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC23_CreateEventWhenStartTimeIsGreaterThanEndTime" time="0.226"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC01_CreateEvent" time="0.295"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC02_UpdateEvent" time="0.231"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC29_UpdateEventWithMultipleEventId" time="0.145"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC30_UpdateEventWithoutChangingCustId" time="0.226"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC31_UpdateEventWithoutChangingMobile" time="0.302"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC32_UpdateEventWithoutChangingEmail" time="0.224"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC33_UpdateEventWithoutTitle" time="0.305"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC34_UpdateEventWithoutDescription" time="0.179"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC35_UpdateEventWithoutStartTime" time="0.223"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC36_UpdateEventWithoutTime" time="0.208"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC37_UpdateEventWithoutEventMetadata" time="0.213"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC38_UpdateEventWithoutSolutionType" time="0.222"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC39_UpdateEventWithoutEventMetadata" time="0.184"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC40_UpdateEventWithoutRole" time="0.148"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC41_UpdateEventHappyCase" time="0.382"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC42_UpdateEventWithDifferentSoltionSubType" time="0.361"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC03_CreateEventWithMultipleRoles" time="0.184"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC47_UpdateEventWhenOneRoleEmpty" time="0.199"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC04_CancelEvent" time="0.223"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC44_CancelAlreadyCancelledEvent" time="0.194"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC45_CancelNotExistEvent" time="0.311"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC43_CancelEventWithoutEventId" time="0.169"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC26_UpdateEventWithoutEventId" time="0.161"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC27_UpdateEventWithoutContextValue" time="0.161"/>
  <testcase classname="OCL.CRM.TestCRM" name="TC21_CreateEventWithInvalidJWTToken">
    <skipped/>
  </testcase> <!-- TC21_CreateEventWithInvalidJWTToken -->
  <testcase classname="OCL.CRM.TestCRM" name="TC28_UpdateEventWithInvalidJWTToken">
    <skipped/>
  </testcase> <!-- TC28_UpdateEventWithInvalidJWTToken -->
</testsuite> <!-- OCL.CRM.TestCRM -->
