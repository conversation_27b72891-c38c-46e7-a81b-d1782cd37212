<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="0" tests="23" name="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" time="59.919" errors="0" timestamp="2023-09-28T18:05:58 IST" skipped="0">
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC020_InitiateKYC_UsingDigiLocker" time="0.747"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="test" time="0.192"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC015_VerifyUploadedSelfie" time="0.739"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC013_VerifyUploadedCustomerPhoto" time="0.982"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC010_UpdateExistingDetailsInSAI" time="0.568"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC009_FetchLeadAllData" time="2.239"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC022_UploadSheetONPanel" time="7.413"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC001_FetchLeadDeatils" time="2.075"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC007_UpdateBureauDataSetInSAI" time="0.789"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC003_CreateFullertonLead" time="1.952"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC021_FetchDataPostKYCIntiated" time="9.990"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC019_FetchDataPostKYCIntiated" time="2.947"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC006_FetchLeadAllData" time="1.572"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC008_FetchCIR" time="1.312"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC011_FetchLeadAllData" time="10.818"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC016_InitiateKYC_UsingSearchByPan" time="0.915"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC014_UploadSelfie" time="4.665"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC017_FetchDataPostKYCIntiated" time="0.668"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC005_UpdateLeadBasicDetails" time="2.623"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC018_InitiateKYC_UsingOfflineAAdhaar" time="1.041"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC004_FetchLeadAllData" time="1.450"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC012_uploadCustomerPhoto" time="3.461"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited" name="TC002_DeleteExistingLead" time="0.761"/>
</testsuite> <!-- OCL.Lending.BusinessLending.Fullerton.TestAllKYCModesFailedAndKYCReinitaited -->
