<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv" tests="6" failures="0" timestamp="2022-07-13T13:25:47 IST" time="3.916" errors="3">
  <testcase name="getApplicantTokenUpdate" time="3.241" classname="OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv"/>
  <testcase name="leadStatus" time="0.317" classname="OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv"/>
  <testcase name="updateLeadGiftVoucherWithInvalidMID" time="0.112" classname="OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv">
    <error type="java.lang.RuntimeException" message="freemarker.template.TemplateNotFoundException: Template not found for name &quot;updateGiftVoucher&quot;.
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath=&quot;/&quot;).">
      <![CDATA[java.lang.RuntimeException: freemarker.template.TemplateNotFoundException: Template not found for name "updateGiftVoucher".
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath="/").
at com.paytm.apitools.builder.MessageBuilder.buildStringMessage(MessageBuilder.java:30)
at com.paytm.apitools.message.TemplateMessage.getMessageText(TemplateMessage.java:91)
at com.paytm.apitools.core.AbstractApiV2.applyTemplate(AbstractApiV2.java:263)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:138)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ProfileUpdate(MiddlewareServices.java:546)
at OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv.updateLeadGiftVoucherWithInvalidMID(FlowPgProfileUpdateGv.java:99)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: freemarker.template.TemplateNotFoundException: Template not found for name "updateGiftVoucher".
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath="/").
at freemarker.template.Configuration.getTemplate(Configuration.java:1833)
at freemarker.template.Configuration.getTemplate(Configuration.java:1646)
at com.paytm.apitools.builder.MessageBuilder.buildStringMessage(MessageBuilder.java:28)
... 19 more
]]>
    </error>
  </testcase> <!-- updateLeadGiftVoucherWithInvalidMID -->
  <testcase name="updateLeadGiftVoucherWithInvalidPAN" time="0.078" classname="OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv">
    <skipped/>
  </testcase> <!-- updateLeadGiftVoucherWithInvalidPAN -->
  <testcase name="updateLeadGiftVoucherWithInvalidEntityType" time="0.088" classname="OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv">
    <error type="java.lang.RuntimeException" message="freemarker.template.TemplateNotFoundException: Template not found for name &quot;updateGiftVoucher&quot;.
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath=&quot;/&quot;).">
      <![CDATA[java.lang.RuntimeException: freemarker.template.TemplateNotFoundException: Template not found for name "updateGiftVoucher".
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath="/").
at com.paytm.apitools.builder.MessageBuilder.buildStringMessage(MessageBuilder.java:30)
at com.paytm.apitools.message.TemplateMessage.getMessageText(TemplateMessage.java:91)
at com.paytm.apitools.core.AbstractApiV2.applyTemplate(AbstractApiV2.java:263)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:138)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ProfileUpdate(MiddlewareServices.java:546)
at OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv.updateLeadGiftVoucherWithInvalidEntityType(FlowPgProfileUpdateGv.java:174)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: freemarker.template.TemplateNotFoundException: Template not found for name "updateGiftVoucher".
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath="/").
at freemarker.template.Configuration.getTemplate(Configuration.java:1833)
at freemarker.template.Configuration.getTemplate(Configuration.java:1646)
at com.paytm.apitools.builder.MessageBuilder.buildStringMessage(MessageBuilder.java:28)
... 19 more
]]>
    </error>
  </testcase> <!-- updateLeadGiftVoucherWithInvalidEntityType -->
  <testcase name="updateLeadGiftVoucher" time="0.080" classname="OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv">
    <error type="java.lang.RuntimeException" message="freemarker.template.TemplateNotFoundException: Template not found for name &quot;updateGiftVoucher&quot;.
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath=&quot;/&quot;).">
      <![CDATA[java.lang.RuntimeException: freemarker.template.TemplateNotFoundException: Template not found for name "updateGiftVoucher".
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath="/").
at com.paytm.apitools.builder.MessageBuilder.buildStringMessage(MessageBuilder.java:30)
at com.paytm.apitools.message.TemplateMessage.getMessageText(TemplateMessage.java:91)
at com.paytm.apitools.core.AbstractApiV2.applyTemplate(AbstractApiV2.java:263)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:138)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ProfileUpdate(MiddlewareServices.java:546)
at OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv.updateLeadGiftVoucher(FlowPgProfileUpdateGv.java:212)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: freemarker.template.TemplateNotFoundException: Template not found for name "updateGiftVoucher".
The name was interpreted by this TemplateLoader: ClassTemplateLoader(resourceLoaderClass=com.paytm.apitools.builder.MessageBuilder, basePackagePath="/").
at freemarker.template.Configuration.getTemplate(Configuration.java:1833)
at freemarker.template.Configuration.getTemplate(Configuration.java:1646)
at com.paytm.apitools.builder.MessageBuilder.buildStringMessage(MessageBuilder.java:28)
... 19 more
]]>
    </error>
  </testcase> <!-- updateLeadGiftVoucher -->
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowPgProfileUpdateGv -->
