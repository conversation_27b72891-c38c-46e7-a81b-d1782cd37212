<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="15" hostname="197NODMB24984.local" name="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission" tests="24" failures="1" timestamp="2022-07-13T13:25:47 IST" time="5.473" errors="0">
  <testcase name="TC009_UpdatingChildLead" time="0.375" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission.TC009_UpdatingChildLead(FlowOrganisedOnlineMerchantForZeroCommission.java:339)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC009_UpdatingChildLead -->
  <testcase name="TC001_FetchPanDetails" time="0.435" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
  <testcase name="TC0024_PositiveSubmitCmtProduction" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0024_PositiveSubmitCmtProduction -->
  <testcase name="TC0018__PositiveSubmitRa" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0018__PositiveSubmitRa -->
  <testcase name="TC0023_PositiveSubmitCmtStaging" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0023_PositiveSubmitCmtStaging -->
  <testcase name="TC0020_AcceptingTnC" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0020_AcceptingTnC -->
  <testcase name="TC0017__PositiveSubmitMandatoryParams" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0017__PositiveSubmitMandatoryParams -->
  <testcase name="TC0019_PostiveRegisterEmail" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0019_PostiveRegisterEmail -->
  <testcase name="TC004_CreateSolution" time="0.790" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
  <testcase name="TC007_CreateChildSolution" time="0.509" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
  <testcase name="TC0015__PositiveSubmitLob" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0015__PositiveSubmitLob -->
  <testcase name="TC0014__PositiveSubmitVeto" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0014__PositiveSubmitVeto -->
  <testcase name="TC0021_ManualPgCallBack" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0021_ManualPgCallBack -->
  <testcase name="TC002_CompanyOnboard" time="1.522" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
  <testcase name="TC0011_PositiveFetchLeadPanel" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0011_PositiveFetchLeadPanel -->
  <testcase name="TC0016__PositiveSubmitBankingLob" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0016__PositiveSubmitBankingLob -->
  <testcase name="TC006_UpdatingLead" time="0.723" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
  <testcase name="TC0010_UploadingDocs" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0010_UploadingDocs -->
  <testcase name="TC0012__PositiveRejectedLeadPanel" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0012__PositiveRejectedLeadPanel -->
  <testcase name="TC0013__PositiveSubmitLeadPanel" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0013__PositiveSubmitLeadPanel -->
  <testcase name="TC0022_PositiveSubmitSap" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission">
    <skipped/>
  </testcase> <!-- TC0022_PositiveSubmitSap -->
  <testcase name="TC005_VerifyGstin" time="0.334" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
  <testcase name="TC008_PennyDrop" time="0.380" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
  <testcase name="TC003_FetchBusinessProfile" time="0.405" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission"/>
</testsuite> <!-- OCL.Business.OrganisedMerchant.FlowOrganisedOnlineMerchantForZeroCommission -->
