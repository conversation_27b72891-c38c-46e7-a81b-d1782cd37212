<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="36" name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" time="21.628" errors="0" timestamp="2023-08-30T17:30:24 IST" skipped="26">
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC003_Create_MCA_V3_ABFL_Lead" time="1.460"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC007_UpdateBureauDataSetInSAI" time="0.556"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC033_AcceptEDIDeclaration" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_AcceptEDIDeclaration -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC013_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC039_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC039_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC005_UpdateLeadBasicDetails" time="2.310"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC034_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC028_GenerateLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC022_FetchDataKYCInitiate" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_FetchDataKYCInitiate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC021_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC035_FetchLeadPostEmandate" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC037_VerifyLeadStage_LMSSubmitApplicationJob" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC001_FetchLeadDetails_MCA_Piramal" time="0.756"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC009_FetchLeadAllData" time="10.027">
    <failure message="did not expect to find [BRE1_COMPLETED] but found [BRE1_SUCCESS]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE1_COMPLETED] but found [BRE1_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp.TC009_FetchLeadAllData(TestMCAABFLIdentifySoleProp.java:594)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC026_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC027_FetchLeadPostBankVerification" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC023_SecondBRECallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC025_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC019_LeadDataUpdateForKYC_InSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC029_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC004_FetchLeadAllData" time="1.188"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC020_InitiateKYC_UsingDigiLocker" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_InitiateKYC_UsingDigiLocker -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC038_LMSDataCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC038_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC011_uploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC031_AcceptLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC014_VerifyUploadedSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC032_GenerateEDIDeclaration" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_GenerateEDIDeclaration -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC036_VerifyPDCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC024_FetchLead_AfterBRE2" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC030_UpdateActualPanInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_UpdateActualPanInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC006_FetchLeadAllData" time="1.274"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC002_DeleteExistingLead" time="0.528"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC008_FetchCIR" time="3.353"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="TC012_VerifyUploadedCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp" name="test" time="0.176"/>
</testsuite> <!-- OCL.Lending.BusinessLending.ABFL.TestMCAABFLIdentifySoleProp -->
