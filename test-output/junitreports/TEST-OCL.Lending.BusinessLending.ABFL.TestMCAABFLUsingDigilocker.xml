<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="34" name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" time="89.211" errors="0" timestamp="2023-08-30T17:32:44 IST" skipped="4">
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC027_FetchLeadPostBankVerification" time="0.462"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC031_UpdateActualPanInSAI" time="0.521"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC024_FetchLead_AfterBRE2" time="11.772"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC033_FetchLeadPostEmandate" time="0.405">
    <failure message="did not expect to find [APPLICATION_SUBMISSION_IN_PROGRESS] but found [APPLICATION_SUBMISSION_SUCCESS]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [APPLICATION_SUBMISSION_IN_PROGRESS] but found [APPLICATION_SUBMISSION_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker.TC033_FetchLeadPostEmandate(TestMCAABFLUsingDigilocker.java:1340)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC033_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="test" time="0.205"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC001_FetchLeadDetails_MCA_Piramal" time="0.696"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC003_Create_MCA_V3_ABFL_Lead" time="1.460"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC006_FetchLeadAllData" time="1.014"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC011_uploadCustomerPhoto" time="3.328"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC037_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC026_SaveBankDetails" time="5.912"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC019_LeadDataUpdateForKYC_InSAI" time="0.390"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC004_FetchLeadAllData" time="0.937"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC023_SecondBRECallback" time="31.330"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC014_VerifyUploadedSelfie" time="0.459"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC025_UpdateKYCNameInSAI" time="0.924"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC021_FetchDataPostKYCIntiated" time="7.796"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC012_VerifyUploadedCustomerPhoto" time="0.452"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC009_FetchLeadAllData" time="7.690"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC002_DeleteExistingLead" time="0.366"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.425"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC028_GenerateLoanAgreement" time="0.645"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC035_VerifyLeadStage_LMSSubmitApplicationJob" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC029_GenerateSanctionLetter" time="1.004"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC034_VerifyPDCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC013_UploadSelfie" time="3.135"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC022_FetchDataKYCInitiate" time="1.184"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC036_LMSDataCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC007_UpdateBureauDataSetInSAI" time="1.078"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC005_UpdateLeadBasicDetails" time="1.788"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC020_InitiateKYC_UsingDigiLocker" time="0.538"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC032_EmandateCallback" time="0.337"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC008_FetchCIR" time="2.088"/>
  <testcase classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker" name="TC030_AcceptLoanAgreement" time="0.870"/>
</testsuite> <!-- OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingDigilocker -->
