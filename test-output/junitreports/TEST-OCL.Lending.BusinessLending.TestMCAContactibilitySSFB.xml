<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="2" tests="44" name="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" time="38.981" errors="0" timestamp="31 May 2021 07:06:03 GMT" skipped="13">
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC018_VerifyLeadStage" time="0.565"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC016_VerifyLeadStage" time="0.764"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC002_FetchLeadDeatils" time="0.945"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC004_FetchTheCreatedLeadDeatils" time="1.727">
    <failure message="expected [5] but found [67]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [5] but found [67]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:453)
at org.testng.Assert.assertEquals(Assert.java:463)
at OCL.Lending.BusinessLending.TestMCAContactibilitySSFB.TC004_FetchTheCreatedLeadDeatils(TestMCAContactibilitySSFB.java:222)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at org.testng.TestRunner.privateRun(TestRunner.java:648)
at org.testng.TestRunner.run(TestRunner.java:505)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
at org.testng.SuiteRunner.run(SuiteRunner.java:364)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
at org.testng.TestNG.runSuites(TestNG.java:1049)
at org.testng.TestNG.run(TestNG.java:1017)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC004_FetchTheCreatedLeadDeatils -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC036_FetchDynamicTnc" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC009_UpdatePANAndDOBDetails" time="0.522"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC033_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC021_CheckCKYCStatus" time="0.709"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC031_AdditionalDataCaptureWithKycAndUserAddress" time="0.296">
    <failure message="expected [200] but found [500]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [200] but found [500]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at Services.LendingService.LendingBaseAPI.verifyResponseCodeAs200OK(LendingBaseAPI.java:2641)
at OCL.Lending.BusinessLending.TestMCAContactibilitySSFB.TC031_AdditionalDataCaptureWithKycAndUserAddress(TestMCAContactibilitySSFB.java:1052)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at org.testng.TestRunner.privateRun(TestRunner.java:648)
at org.testng.TestRunner.run(TestRunner.java:505)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
at org.testng.SuiteRunner.run(SuiteRunner.java:364)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
at org.testng.TestNG.runSuites(TestNG.java:1049)
at org.testng.TestNG.run(TestNG.java:1017)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC031_AdditionalDataCaptureWithKycAndUserAddress -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC006_FetchTheCreatedLeadDeatils" time="0.776"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC028_VerifyLeadStage" time="0.774"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC029_AdditionalInfoUpdate" time="0.383"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC019_CheckBREResponse" time="7.625"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC038_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC038_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC040_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC040_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC005_PPBLOTPCallback" time="0.648"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC044_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC044_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC042_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC042_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC023_UploadSelfie" time="2.867"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC025_UploadCustomerPhoto" time="2.661"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC027_CKYCCallback" time="0.605"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC037_FetchDynamicTncSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_FetchDynamicTncSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC008_FetchTheCreatedLeadDeatils" time="0.815"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC010_VerifyLeadStage" time="0.846"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC035_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC017_FetchBREResponse" time="6.275"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC011_UpdateGenderAndPincodeDetails" time="0.399"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC045_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC045_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC007_AddBasicDetails" time="0.422"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC020_VerifyLeadStage" time="0.556"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC032_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_VerifyLeadStage -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC022_VerifyLeadStage" time="0.594"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC003_CreateSSFBLead" time="1.893"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC001_DeleteExistingLead_ForSSFB" time="0.724"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC012_VerifyLeadStage" time="0.637"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC034_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC014_FetchTheCreatedLeadDeatils" time="0.862"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC041_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC041_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC013_BREOTPVerification" time="0.512"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC015_BREValidationPending" time="0.457"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC024_VerifyUploadedDocument" time="0.753"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC026_VerifyUploadedDocument" time="0.655"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC030_VerifyLeadStage" time="0.714"/>
  <testcase classname="OCL.Lending.BusinessLending.TestMCAContactibilitySSFB" name="TC039_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC039_SubmitApplication -->
</testsuite> <!-- OCL.Lending.BusinessLending.TestMCAContactibilitySSFB -->
