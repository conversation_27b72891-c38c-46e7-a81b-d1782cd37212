<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="8" hostname="197NODMB24984.local" name="OCL.Individual.PSA.FlowPSA" tests="12" failures="1" timestamp="2021-11-17T11:52:54 IST" time="16.208" errors="0">
  <testcase name="CreateNewApplicantPSA" time="2.844" classname="OCL.Individual.PSA.FlowPSA"/>
  <testcase name="SendOtpPSA" time="2.403" classname="OCL.Individual.PSA.FlowPSA"/>
  <testcase name="ValidateOtpPSA" time="1.586" classname="OCL.Individual.PSA.FlowPSA"/>
  <testcase name="SubmitLeadPSA" time="9.375" classname="OCL.Individual.PSA.FlowPSA">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.PSA.FlowPSA.SubmitLeadPSA(FlowPSA.java:144)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- SubmitLeadPSA -->
  <testcase name="FetchPaymentStatusPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- FetchPaymentStatusPSA -->
  <testcase name="ExtractQrCodeIdPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- ExtractQrCodeIdPSA -->
  <testcase name="FetchQrDetailsPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- FetchQrDetailsPSA -->
  <testcase name="PayMerchantPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- PayMerchantPSA -->
  <testcase name="UpdateLeadPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- UpdateLeadPSA -->
  <testcase name="UploadVoterIdPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- UploadVoterIdPSA -->
  <testcase name="UploadAgentPhotoPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- UploadAgentPhotoPSA -->
  <testcase name="UploadEducationProofPSA" time="0.000" classname="OCL.Individual.PSA.FlowPSA">
    <skipped/>
  </testcase> <!-- UploadEducationProofPSA -->
</testsuite> <!-- OCL.Individual.PSA.FlowPSA -->
