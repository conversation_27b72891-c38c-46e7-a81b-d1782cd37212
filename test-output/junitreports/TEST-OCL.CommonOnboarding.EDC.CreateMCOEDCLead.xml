<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="7" name="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" time="3.250" errors="0" timestamp="2023-07-08T14:06:46 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" name="TC_005_GetMerchantMIDWithInvalidSolutionType" time="0.385"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" name="TC_002_GetMerchantMIDWithoutVersion" time="0.813"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" name="TC_001_GetMerchantMID" time="0.535"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" name="TC_006_GetMerchantMIDWithoutMobilenumber" time="0.408"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" name="TC_004_GetMerchantMIDWithoutSolutionType" time="0.273"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" name="TC_003_GetMerchantMIDWithoutDeviceIdentifer" time="0.444"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOEDCLead" name="TC_006_GetMerchantMIDWithoutSessionToken" time="0.392"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.CreateMCOEDCLead -->
