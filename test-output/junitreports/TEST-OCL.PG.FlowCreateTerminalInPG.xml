<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="6" hostname="197NODMB24984.local" name="OCL.PG.FlowCreateTerminalInPG" tests="15" failures="1" timestamp="2022-07-13T12:28:08 IST" time="235.318" errors="0">
  <testcase name="TC_006_CreateTerminalInPGWithMappedSerialNumber" time="2.131" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_002_CreateTerminalInPGWithInvalidOSType" time="2.057" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_012_CreateTerminalInPGWithInvalidMID" time="0.000" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_012_CreateTerminalInPGWithInvalidMID -->
  <testcase name="TC_001_MerchantLogin" time="2.110" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_003_CreateTerminalInPGWithInvalidVendorType" time="1.938" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_015_ActiveTerminalInPG" time="0.000" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_015_ActiveTerminalInPG -->
  <testcase name="TC_004_CreateTerminalInPGWithInvalidModelType" time="2.448" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_008_ActiveTerminalInPGWithInvalidTID" time="1.922" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_010_ActiveTerminalInPGWithInvalidMID" time="0.000" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_010_ActiveTerminalInPGWithInvalidMID -->
  <testcase name="TC_014_CreateTerminalInPGWithValidData" time="0.000" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_014_CreateTerminalInPGWithValidData -->
  <testcase name="TC_007_ActiveTerminalInPGWithInvalidSerialNumber" time="2.323" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_011_ActiveTerminalInPGWithInvalidToken" time="0.000" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_011_ActiveTerminalInPGWithInvalidToken -->
  <testcase name="TC_005_CreateTerminalInPG" time="114.610" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_009_ActiveTerminalInPGWithInvalidModelName" time="105.779" classname="OCL.PG.FlowCreateTerminalInPG">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.PG.FlowCreateTerminalInPG.TC_009_ActiveTerminalInPGWithInvalidModelName(FlowCreateTerminalInPG.java:238)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_009_ActiveTerminalInPGWithInvalidModelName -->
  <testcase name="TC_013_CreateTerminalInPGWithInvalidToken" time="0.000" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_013_CreateTerminalInPGWithInvalidToken -->
</testsuite> <!-- OCL.PG.FlowCreateTerminalInPG -->
