<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="8" tests="49" name="OCL.Lending.loanTap" time="59.442" errors="3" timestamp="22 Apr 2021 07:18:18 GMT" skipped="0">
  <testcase classname="OCL.Lending.loanTap" name="fetchLead_LoanTap_invalidEntityType" time="2.010"/>
  <testcase classname="OCL.Lending.loanTap" name="fetchLead_LoanTap_invalidSolution" time="1.816"/>
  <testcase classname="OCL.Lending.loanTap" name="createLead_LoanTap_invalidTNCSetName" time="1.056">
    <failure message="expected [500] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [500] but found [400]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.createLead_LoanTap_invalidTNCSetName(loanTap.java:157)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- createLead_LoanTap_invalidTNCSetName -->
  <testcase classname="OCL.Lending.loanTap" name="createLead_LoanTap_TNCAcceptedVersionAsNull" time="0.914"/>
  <testcase classname="OCL.Lending.loanTap" name="createLead_LoanTap_incorrectTNCName" time="0.859">
    <failure message="expected [500] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [500] but found [400]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.createLead_LoanTap_incorrectTNCName(loanTap.java:226)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- createLead_LoanTap_incorrectTNCName -->
  <testcase classname="OCL.Lending.loanTap" name="createLead_LoanTap_TNCAcceptedCodeAsNull" time="1.151"/>
  <testcase classname="OCL.Lending.loanTap" name="panVerification_LoanTap_invalidWorkflowSubOperation" time="0.910"/>
  <testcase classname="OCL.Lending.loanTap" name="panVerification_LoanTap_duplicatePan" time="2.341"/>
  <testcase classname="OCL.Lending.loanTap" name="panVerification_LoanTap_invalidValueForIsPartialSave" time="0.959"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_incorrectGenderValuePassed" time="0.820"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_TypeOfBusinessAsNull" time="0.870"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_invalidWorkflowSuboperation" time="0.900"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_invalidYearInBusiness" time="0.905"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_GenderAsNull" time="0.950"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_incorrectDOBValuePassed" time="0.919"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_EmailAsNull" time="2.079"/>
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap_DOBAsNull" time="1.217"/>
  <testcase classname="OCL.Lending.loanTap" name="updateAddressDetails_LoanTap_LenderNameAsNull" time="0.939">
    <failure message="expected [200] but found [401]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [200] but found [401]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.updateAddressDetails_LoanTap_LenderNameAsNull(loanTap.java:776)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- updateAddressDetails_LoanTap_LenderNameAsNull -->
  <testcase classname="OCL.Lending.loanTap" name="updateAddressDetails_LoanTap_LenderProductIdasNull" time="0.894">
    <failure message="expected [200] but found [401]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [200] but found [401]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.updateAddressDetails_LoanTap_LenderProductIdasNull(loanTap.java:817)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- updateAddressDetails_LoanTap_LenderProductIdasNull -->
  <testcase classname="OCL.Lending.loanTap" name="updateAddressDetails_LoanTap_invalidLenderName" time="0.779">
    <failure message="expected [200] but found [401]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [200] but found [401]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.updateAddressDetails_LoanTap_invalidLenderName(loanTap.java:736)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- updateAddressDetails_LoanTap_invalidLenderName -->
  <testcase classname="OCL.Lending.loanTap" name="updateAddressDetails_LoanTap_invalidLenderProductId" time="0.766">
    <failure message="expected [200] but found [401]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [200] but found [401]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.updateAddressDetails_LoanTap_invalidLenderProductId(loanTap.java:697)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- updateAddressDetails_LoanTap_invalidLenderProductId -->
  <testcase classname="OCL.Lending.loanTap" name="updateAddressDetails_LoanTap_CustIdasNull" time="0.847"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_COMBINATION_IDAsNull" time="0.763"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_LoanProcessingFeeAsNull" time="0.869"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_LoanAmountInNumberAsNull" time="0.817"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_MERCHANT_IDAsNull" time="0.821"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_solutionTypeLevel3AsNull" time="0.898"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_workflowSubopertaionAsNull" time="0.870"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_LOAN_TENURE_UNITAsNull" time="0.757"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_LoanTenureAsNull" time="0.860"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_shopRelatedBusinessUuidAsNull" time="0.874"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap_LOAN_EQUATED_MONTHLY_INSTALLMENTAsNull" time="0.796"/>
  <testcase classname="OCL.Lending.loanTap" name="lmsCallBack_LoanTap_leadIdAsNull" time="0.880"/>
  <testcase classname="OCL.Lending.loanTap" name="lmsCallBack_LoanTap_CustIdAsNull" time="0.899"/>
  <testcase classname="OCL.Lending.loanTap" name="lmsCallBack_LoanTap_StatusIsInvalid" time="2.161"/>
  <testcase classname="OCL.Lending.loanTap" name="lmsCallBack_LoanTap_AuthAsNull" time="1.117"/>
  <testcase classname="OCL.Lending.loanTap" name="lmsCallBack_LoanTap_StatusAsNull" time="1.349"/>
  <testcase classname="OCL.Lending.loanTap" name="lmsCallBack_LoanTap_StatusAsRejected" time="0.848"/>
  <testcase classname="OCL.Lending.loanTap" name="fetchLead_LoanTap" time="1.730"/>
  <testcase classname="OCL.Lending.loanTap" name="createLead_LoanTap" time="0.940">
    <failure message="expected [200] but found [400]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [200] but found [400]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.createLead_LoanTap(loanTap.java:1522)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- createLead_LoanTap -->
  <testcase classname="OCL.Lending.loanTap" name="fetchLead_LoanTap_fetchPan" time="1.893"/>
  <testcase classname="OCL.Lending.loanTap" name="panVerification_LoanTap" time="1.567">
    <failure message="expected [200] but found [500]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [200] but found [500]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:652)
at org.testng.Assert.assertEquals(Assert.java:662)
at OCL.Lending.loanTap.panVerification_LoanTap(loanTap.java:1586)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- panVerification_LoanTap -->
  <testcase classname="OCL.Lending.loanTap" name="updatePersonalDetails_LoanTap" time="0.999"/>
  <testcase classname="OCL.Lending.loanTap" name="fetchLead_LoanTap_fetchnameAsPerPan" time="3.086"/>
  <testcase classname="OCL.Lending.loanTap" name="updateAddressDetails_LoanTap" time="0.024">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.AdditionalDetails(MiddlewareServices.java:935)
at OCL.Lending.loanTap.updateAddressDetails_LoanTap(loanTap.java:1704)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </error>
  </testcase> <!-- updateAddressDetails_LoanTap -->
  <testcase classname="OCL.Lending.loanTap" name="fetchLead_LoanTap_ValidateLenderName" time="1.797"/>
  <testcase classname="OCL.Lending.loanTap" name="loanOffer_LoanTap" time="0.003">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.AdditionalDetails(MiddlewareServices.java:935)
at OCL.Lending.loanTap.loanOffer_LoanTap(loanTap.java:1788)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </error>
  </testcase> <!-- loanOffer_LoanTap -->
  <testcase classname="OCL.Lending.loanTap" name="lmsCallBack_LoanTap" time="5.067">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.lmsCallback_loanTap(MiddlewareServices.java:950)
at OCL.Lending.loanTap.lmsCallBack_LoanTap(loanTap.java:1826)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </error>
  </testcase> <!-- lmsCallBack_LoanTap -->
  <testcase classname="OCL.Lending.loanTap" name="fetchLead_LoanTap_AfterLeadClosed" time="1.856"/>
</testsuite> <!-- OCL.Lending.loanTap -->
