<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="33" name="OCL.CommonOnboarding.EDC.CreateMCOLead" time="21.077" errors="0" timestamp="2023-11-07T12:05:29 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_002_ExistingLeadinValidateOTPCall" time="1.830"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_009_NewcreateNewMCOLeadValidateOTPInvaliduserType" time="0.252"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_006_NewcreateNewMCOLeadValidateOTPWithoutentityType" time="0.790"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_005_NewcreateNewMCOLeadValidateOTPWithoutXMWChecksumbypass" time="0.224"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_008_NewcreateNewMCOLeadValidateOTPInvalidentityType" time="0.258"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_009_createNewMCOLeadValidateOTPInvaliduserType" time="1.579"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_007_NewcreateNewMCOLeadValidateOTPInvalidentityType" time="0.413"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="test" time="0.265"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_010_NewcreateNewMCOLeadValidateOTPWithoutMobileNumber" time="0.275"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_010_createNewMCOLeadValidateOTPWithoutMobileNumber" time="0.730"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_005_createNewMCOLeadValidateOTPWithoutXMWChecksumbypass" time="0.230"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_004_NewcreateNewMCOLeadValidateOTPWithoutdeviceIdentifier" time="0.216"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_013_EDCContextLeadinValidateOTPCall" time="0.960"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_008_createNewMCOLeadValidateOTPInvalidentityType" time="0.232"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_12_getcreateNewMCOExistingLead" time="1.382"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_003_NewcreateNewMCOLeadValidateOTPWithoutVersion" time="0.285"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_006_createNewMCOLeadValidateOTPWithoutentityType" time="0.751"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_12_NewgetcreateNewMCOExistingLead" time="1.473"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_004_createNewMCOLeadValidateOTPWithoutdeviceIdentifier" time="0.232"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_003_createNewMCOLeadValidateOTPWithoutVersion" time="0.238"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_007_createNewMCOLeadValidateOTPInvalidentityType" time="0.275"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_002_NewExistingLeadinValidateOTPCall" time="1.916"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_013_NewEDCContextLeadinValidateOTPCall" time="1.073"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_003_UnauthorizedAccess" time="0.399"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_008_InvalidMobileNumber" time="0.748"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_004_InvalidSolutionType" time="0.695"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_005_MissingMobileNumber" time="0.240"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_009_MissingUserType" time="0.246"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_007_ValidResponseFo" time="1.062"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_010_MissingSkipOtp" time="0.299"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_007_ValidResponseFormatNew" time="0.331"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_006_ValidResponseFormat" time="0.572"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead" name="TC_008_ValidResponseFormatNew" time="0.606"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.CreateMCOLead -->
