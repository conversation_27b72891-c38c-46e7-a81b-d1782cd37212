<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="10" hostname="197NODMB24984.local" name="OCL.Individual.SoundBox.FlowSoundBox" tests="12" failures="1" timestamp="2022-07-13T12:28:08 IST" time="4.650" errors="0">
  <testcase name="SoundBoxPositiveFetchMID" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxPositiveFetchMID -->
  <testcase name="PositiveGetMerchantStatus" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- PositiveGetMerchantStatus -->
  <testcase name="FetchSoundBoxBindUrl" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- FetchSoundBoxBindUrl -->
  <testcase name="SoundBoxFetchPlan" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxFetchPlan -->
  <testcase name="SoundBoxPositiveValidateOtp" time="3.830" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.SoundBox.FlowSoundBox.SoundBoxPositiveValidateOtp(FlowSoundBox.java:99)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- SoundBoxPositiveValidateOtp -->
  <testcase name="SoundBoxFetchPaymentStatus" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxFetchPaymentStatus -->
  <testcase name="SoundBoxChoosePlan" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxChoosePlan -->
  <testcase name="SoundBoxPayment" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxPayment -->
  <testcase name="SoundBoxPositiveSendOtp" time="0.820" classname="OCL.Individual.SoundBox.FlowSoundBox"/>
  <testcase name="SoundBoxCreateLead" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxCreateLead -->
  <testcase name="SoundBoxFetchQrDetails" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxFetchQrDetails -->
  <testcase name="SoundBoxExtractQrCodeId" time="0.000" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxExtractQrCodeId -->
</testsuite> <!-- OCL.Individual.SoundBox.FlowSoundBox -->
