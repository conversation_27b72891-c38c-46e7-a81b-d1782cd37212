<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="6" name="OCL.CommonOnboarding.EDC.getCKYC9" time="2.988" errors="0" timestamp="2023-11-07T14:27:57 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.getCKYC9" name="test" time="0.289"/>
  <testcase classname="OCL.CommonOnboarding.EDC.getCKYC9" name="TC_1_getExistingLeadckyc" time="0.967"/>
  <testcase classname="OCL.CommonOnboarding.EDC.getCKYC9" name="TC_4_GetCKYCErrorWithoutDeviceIdentifer" time="0.623"/>
  <testcase classname="OCL.CommonOnboarding.EDC.getCKYC9" name="TC_3_ErrorWithoutVersiongetExistingLeadckyc" time="0.399"/>
  <testcase classname="OCL.CommonOnboarding.EDC.getCKYC9" name="TC_2_ErrorWithoutSessionTokengetExistingLeadckyc" time="0.329"/>
  <testcase classname="OCL.CommonOnboarding.EDC.getCKYC9" name="TC_4_EmptyLeadID" time="0.381"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.getCKYC9 -->
