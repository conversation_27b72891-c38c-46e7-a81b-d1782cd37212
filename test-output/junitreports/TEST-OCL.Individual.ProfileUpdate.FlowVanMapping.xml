<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="4" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowVanMapping" tests="18" failures="1" timestamp="2022-07-13T13:25:47 IST" time="7.087" errors="2">
  <testcase name="TC003_UploadDocumentAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVanMapping">
    <skipped/>
  </testcase> <!-- TC003_UploadDocumentAddVendor -->
  <testcase name="TC000_GetSessionToken" time="2.166" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC004_FetchLeadDetailsAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVanMapping">
    <skipped/>
  </testcase> <!-- TC004_FetchLeadDetailsAddVendor -->
  <testcase name="TC005_ApproveLeadAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVanMapping">
    <skipped/>
  </testcase> <!-- TC005_ApproveLeadAddVendor -->
  <testcase name="TC0010_CreateVendorOnboardingWithEmptySubCategory" time="0.338" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0006_CreateVendorOnboardingWithEmptyvendorMobile" time="0.297" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0003_CreateVendorOnboardingWithEmptyMID" time="0.635" classname="OCL.Individual.ProfileUpdate.FlowVanMapping">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.ProfileUpdate.FlowVanMapping.TC0003_CreateVendorOnboardingWithEmptyMID(FlowVanMapping.java:228)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0003_CreateVendorOnboardingWithEmptyMID -->
  <testcase name="TC0008_CreateVendorOnboardingWithEmptybusinessType" time="0.292" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0011_CreateVendorOnboardingWithEmptyBankAccount" time="0.394" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0012_CreateVendorOnboardingWithEmptybeneficiaryName" time="0.315" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0005_CreateVendorOnboardingWithEmptyvendorEmail" time="0.384" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0007_CreateVendorOnboardingWithEmptypan" time="0.308" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0004_CreateVendorOnboardingWithEmptyvendorName" time="0.338" classname="OCL.Individual.ProfileUpdate.FlowVanMapping">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.ProfileUpdate.FlowVanMapping.TC0004_CreateVendorOnboardingWithEmptyvendorName(FlowVanMapping.java:273)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0004_CreateVendorOnboardingWithEmptyvendorName -->
  <testcase name="TC0002_CreateVendorOnboardingWithEmptySessionToken" time="0.289" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC001_CreateVendorOnboarding" time="0.356" classname="OCL.Individual.ProfileUpdate.FlowVanMapping">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.ProfileUpdate.FlowVanMapping.TC001_CreateVendorOnboarding(FlowVanMapping.java:645)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC001_CreateVendorOnboarding -->
  <testcase name="TC0009_CreateVendorOnboardingWithEmptycategory" time="0.355" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC0001_CreateVendorOnboardingWithEmptysolutionSubType" time="0.620" classname="OCL.Individual.ProfileUpdate.FlowVanMapping"/>
  <testcase name="TC002_FetchDocumentsAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVanMapping">
    <skipped/>
  </testcase> <!-- TC002_FetchDocumentsAddVendor -->
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowVanMapping -->
