<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="29" hostname="197NODMB24984.local" name="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited" tests="31" failures="0" timestamp="2022-07-13T13:25:47 IST" time="62.026" errors="1">
  <testcase name="TC0002_PositiveSendOtpBusinessUnlimited" time="60.332" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited.TC0002_PositiveSendOtpBusinessUnlimited(FlowQRMerchantUnlimited.java:176)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- TC0002_PositiveSendOtpBusinessUnlimited -->
  <testcase name="TC0023_PositiveFetchLeadPanelUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0023_PositiveFetchLeadPanelUnlimited -->
  <testcase name="TC0001_CreateApplicantOauth" time="1.693" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited"/>
  <testcase name="TC0022_PositiveGetOEPanelCookieUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0022_PositiveGetOEPanelCookieUnlimited -->
  <testcase name="TC0021_PositiveFetchDocsUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0021_PositiveFetchDocsUnlimited -->
  <testcase name="TC0014_PositiveSendOtpMerchantDeclareUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0014_PositiveSendOtpMerchantDeclareUnlimited -->
  <testcase name="TC0027_PositiveSubmitRejectedLeadUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0027_PositiveSubmitRejectedLeadUnlimited -->
  <testcase name="TC0006_PositiveGetBusinessAfterCompanyUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0006_PositiveGetBusinessAfterCompanyUnlimited -->
  <testcase name="TC0016_PositiveGetGstExemptionUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0016_PositiveGetGstExemptionUnlimited -->
  <testcase name="TC0025_PositiveRejectedLeadPaneUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0025_PositiveRejectedLeadPaneUnlimited -->
  <testcase name="TC0024_ReallocatingAgentUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0024_ReallocatingAgentUnlimited -->
  <testcase name="TC0010_PositiveValidateOtpQRMerchantUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0010_PositiveValidateOtpQRMerchantUnlimited -->
  <testcase name="TC0003_PositiveGetBusinessUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0003_PositiveGetBusinessUnlimited -->
  <testcase name="TC0009_PositiveGetTnCUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0009_PositiveGetTnCUnlimited -->
  <testcase name="TC0008_PositiveValidateOtpQRMerchantUnlimited" time="0.001" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0008_PositiveValidateOtpQRMerchantUnlimited -->
  <testcase name="TC0005_PositivePostComapnyUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0005_PositivePostComapnyUnlimited -->
  <testcase name="TC0004_PositiveGetComapnyUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0004_PositiveGetComapnyUnlimited -->
  <testcase name="TC0017_PositiveGetDocStatusUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0017_PositiveGetDocStatusUnlimited -->
  <testcase name="TC0015_PositiveValidateOtpMerchantDeclareUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0015_PositiveValidateOtpMerchantDeclareUnlimited -->
  <testcase name="TC0013_PositiveGetSubCategoryUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0013_PositiveGetSubCategoryUnlimited -->
  <testcase name="TC0007_PositiveGetBusinessProfileUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0007_PositiveGetBusinessProfileUnlimited -->
  <testcase name="TC0012_PositiveGetCategoryUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0012_PositiveGetCategoryUnlimited -->
  <testcase name="TC0029_PgCallBackUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0029_PgCallBackUnlimited -->
  <testcase name="TC0020_PositiveSubmitLeadUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0020_PositiveSubmitLeadUnlimited -->
  <testcase name="TC0008_PositiveSendOtpQRMerchantUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0008_PositiveSendOtpQRMerchantUnlimited -->
  <testcase name="TC0026_PositiveGetMerchantStatusAfterRejection" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0026_PositiveGetMerchantStatusAfterRejection -->
  <testcase name="TC0018_PositiveGetBanksUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0018_PositiveGetBanksUnlimited -->
  <testcase name="TC0019_PositivePennyDropMultiNameMatchUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0019_PositivePennyDropMultiNameMatchUnlimited -->
  <testcase name="TC0011_PositiveGetMerchantUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0011_PositiveGetMerchantUnlimited -->
  <testcase name="TC0028_PositiveSubmitLeadPanelUnlimited" time="0.000" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0028_PositiveSubmitLeadPanelUnlimited -->
  <testcase name="TC0022_PositiveCreateUserWallet" classname="OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited">
    <skipped/>
  </testcase> <!-- TC0022_PositiveCreateUserWallet -->
</testsuite> <!-- OCL.Business.QRMerchantUnlimited.FlowQRMerchantUnlimited -->
