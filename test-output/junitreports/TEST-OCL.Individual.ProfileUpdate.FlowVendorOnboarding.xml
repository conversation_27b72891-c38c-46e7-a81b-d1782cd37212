<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="4" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowVendorOnboarding" tests="18" failures="0" timestamp="2022-07-13T13:25:47 IST" time="10.046" errors="2">
  <testcase name="TC000_GetSessionToken" time="4.003" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC005_ApproveLeadAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding">
    <skipped/>
  </testcase> <!-- TC005_ApproveLeadAddVendor -->
  <testcase name="TC004_FetchLeadDetailsAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding">
    <skipped/>
  </testcase> <!-- TC004_FetchLeadDetailsAddVendor -->
  <testcase name="TC003_UploadDocumentAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding">
    <skipped/>
  </testcase> <!-- TC003_UploadDocumentAddVendor -->
  <testcase name="TC0010_CreateVendorOnboardingWithEmptySubCategory" time="0.314" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0009_CreateVendorOnboardingWithEmptycategory" time="0.306" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0012_CreateVendorOnboardingWithEmptybeneficiaryName" time="0.319" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0002_CreateVendorOnboardingWithEmptySessionToken" time="0.305" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC001_CreateVendorOnboarding" time="1.565" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.ProfileUpdate.FlowVendorOnboarding.TC001_CreateVendorOnboarding(FlowVendorOnboarding.java:646)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC001_CreateVendorOnboarding -->
  <testcase name="TC0011_CreateVendorOnboardingWithEmptyBankAccount" time="0.315" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0005_CreateVendorOnboardingWithEmptyvendorEmail" time="0.335" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0004_CreateVendorOnboardingWithEmptyvendorName" time="0.349" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0008_CreateVendorOnboardingWithEmptybusinessType" time="0.327" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0003_CreateVendorOnboardingWithEmptyMID" time="0.635" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.ProfileUpdate.FlowVendorOnboarding.TC0003_CreateVendorOnboardingWithEmptyMID(FlowVendorOnboarding.java:229)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0003_CreateVendorOnboardingWithEmptyMID -->
  <testcase name="TC0007_CreateVendorOnboardingWithEmptypan" time="0.369" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0006_CreateVendorOnboardingWithEmptyvendorMobile" time="0.307" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC0001_CreateVendorOnboardingWithEmptysolutionSubType" time="0.597" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding"/>
  <testcase name="TC002_FetchDocumentsAddVendor" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowVendorOnboarding">
    <skipped/>
  </testcase> <!-- TC002_FetchDocumentsAddVendor -->
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowVendorOnboarding -->
