<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="10" hostname="197NODMB24984.local" name="OCL.Individual.SoundBox.TestSoundBoxFlow" tests="30" failures="5" timestamp="2022-07-13T12:28:08 IST" time="17.760" errors="0">
  <testcase name="NegativecaseSoundboxLeadCreateNoSolutionSubType" time="0.629" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativecaseSoundboxLeadCreateNoUserCustID" time="1.763" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativecaseSoundboxLeadCreateNoMid" time="0.663" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="GetSoundBoxDeviceType" time="0.520" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [18] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [18] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.SoundBox.TestSoundBoxFlow.GetSoundBoxDeviceType(TestSoundBoxFlow.java:780)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- GetSoundBoxDeviceType -->
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithoutLeadID" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithoutLeadID -->
  <testcase name="GetSoundBoxLeadCount" time="0.581" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [1] but found [0]">
      <![CDATA[java.lang.AssertionError: did not expect to find [1] but found [0]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.SoundBox.TestSoundBoxFlow.GetSoundBoxLeadCount(TestSoundBoxFlow.java:1000)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- GetSoundBoxLeadCount -->
  <testcase name="FetchSoundBoxReplacementQNA" time="0.653" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="GetSoundBoxMerchantDetails" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- GetSoundBoxMerchantDetails -->
  <testcase name="SoundboxReplacementLeadCreate" time="0.658" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="SoundboxLeadCreate" time="2.485" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.SoundBox.TestSoundBoxFlow.SoundboxLeadCreate(TestSoundBoxFlow.java:314)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- SoundboxLeadCreate -->
  <testcase name="NegativecaseSoundboxLeadCreateNoEntityType" time="0.504" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativecaseSoundboxLeadCreateNoMerchantName" time="0.552" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="GetSoundBoxMorefunDetails" time="0.567" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="GetPinCodeDetails" time="0.989" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailsInvalidLeadID" time="0.001" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailsInvalidLeadID -->
  <testcase name="NegativecaseSoundboxLeadCreateNoAgentCustID" time="0.595" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailsIncorrectLeadIDKey" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailsIncorrectLeadIDKey -->
  <testcase name="NegativeCaseGetMerchantMIDwithoutsendCustID" time="0.765" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="FetchTnCSoundBox" time="0.536" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [https://s3-ap-southeast-1.amazonaws.com/accounts-assets/test/2769.html] but found [https://cif-staging.paytm.in/kyc/tnc/get/*********]">
      <![CDATA[java.lang.AssertionError: did not expect to find [https://s3-ap-southeast-1.amazonaws.com/accounts-assets/test/2769.html] but found [https://cif-staging.paytm.in/kyc/tnc/get/*********]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.SoundBox.TestSoundBoxFlow.FetchTnCSoundBox(TestSoundBoxFlow.java:1410)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- FetchTnCSoundBox -->
  <testcase name="FetchPlanSoundBox" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- FetchPlanSoundBox -->
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithInvalidSessionToken" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithInvalidSessionToken -->
  <testcase name="FetchPlanMorefun" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- FetchPlanMorefun -->
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithNoEntityType" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithNoEntityType -->
  <testcase name="NegativecaseSoundboxLeadCreateInvalidMID" time="0.715" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="morefunLeadCreate" time="2.540" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.SoundBox.TestSoundBoxFlow.morefunLeadCreate(TestSoundBoxFlow.java:737)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- morefunLeadCreate -->
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithNoLeadID" time="0.000" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithNoLeadID -->
  <testcase name="GetMerchantMID" time="0.825" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativeCaseGetMerchantMIDwithInvalidCustID" time="0.627" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativecaseFetchPlanSoundBoxwithoutLeadID" time="0.001" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativecaseFetchPlanSoundBoxwithoutLeadID -->
  <testcase name="NegativeCaseGetMerchantMIDwithInValidKeyForCustID" time="0.591" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
</testsuite> <!-- OCL.Individual.SoundBox.TestSoundBoxFlow -->
