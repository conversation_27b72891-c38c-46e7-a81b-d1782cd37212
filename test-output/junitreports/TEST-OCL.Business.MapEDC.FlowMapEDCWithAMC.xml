<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="22" hostname="197NODMB24984.local" name="OCL.Business.MapEDC.FlowMapEDCWithAMC" tests="24" failures="0" timestamp="2022-07-13T12:28:08 IST" time="2.380" errors="1">
  <testcase name="TC0016_ManualPGCallbackForEdc" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0016_ManualPGCallbackForEdc -->
  <testcase name="TC015_MapEdcFetchLeadDetailsForPgReferenceId" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC015_MapEdcFetchLeadDetailsForPgReferenceId -->
  <testcase name="TC003_MapEdcPositiveGetBusinessProfile" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC003_MapEdcPositiveGetBusinessProfile -->
  <testcase name="TC0019_MapEdcResendOtp" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0019_MapEdcResendOtp -->
  <testcase name="TC013_MapEdcPayment" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC013_MapEdcPayment -->
  <testcase name="TC014_MapEdcFetchLeadDetailsAfterPayment" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC014_MapEdcFetchLeadDetailsAfterPayment -->
  <testcase name="TC0017_MapEdcFetchQnA" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0017_MapEdcFetchQnA -->
  <testcase name="TC024_MapEdcFetchLeadDetailsAfterAMCFileUpload" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC024_MapEdcFetchLeadDetailsAfterAMCFileUpload -->
  <testcase name="TC007_MapEdcFetchPlans" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC007_MapEdcFetchPlans -->
  <testcase name="TC022_AMCFileRejectedFromPanel" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC022_AMCFileRejectedFromPanel -->
  <testcase name="TC0020_MapEdcFinishMapping" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0020_MapEdcFinishMapping -->
  <testcase name="TC009_MapEdcUpdateLead" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC009_MapEdcUpdateLead -->
  <testcase name="TC021_MapEdcFetchLeadDetailsAfterMapping" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC021_MapEdcFetchLeadDetailsAfterMapping -->
  <testcase name="TC0018_MapEdcMachineMapping" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0018_MapEdcMachineMapping -->
  <testcase name="TC005_MapEdcPositiveSendOtpCreate" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC005_MapEdcPositiveSendOtpCreate -->
  <testcase name="TC023_UploadAMCFileFromPanel" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC023_UploadAMCFileFromPanel -->
  <testcase name="TC0012_MapEdcFetchQrDetails" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0012_MapEdcFetchQrDetails -->
  <testcase name="TC002_MapEdcPositiveGetBusiness" time="0.819" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <error type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:238)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at OCL.Business.MapEDC.FlowMapEDCWithAMC.TC002_MapEdcPositiveGetBusiness(FlowMapEDCWithAMC.java:122)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 15 more
]]>
    </error>
  </testcase> <!-- TC002_MapEdcPositiveGetBusiness -->
  <testcase name="TC0010_MapEdcFetchPaymentStatus" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0010_MapEdcFetchPaymentStatus -->
  <testcase name="TC006_MapEdcPositiveValidateOtpCreate" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC006_MapEdcPositiveValidateOtpCreate -->
  <testcase name="TC008_MapEdcFetchLeadDetails" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC008_MapEdcFetchLeadDetails -->
  <testcase name="TC0011_MapEdcExtractQrCodeId" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0011_MapEdcExtractQrCodeId -->
  <testcase name="TC001_MapEdcPositiveSendOtpBusiness" time="1.561" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC"/>
  <testcase name="TC004_MapEdcPositiveFetchMID" time="0.000" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC004_MapEdcPositiveFetchMID -->
</testsuite> <!-- OCL.Business.MapEDC.FlowMapEDCWithAMC -->
