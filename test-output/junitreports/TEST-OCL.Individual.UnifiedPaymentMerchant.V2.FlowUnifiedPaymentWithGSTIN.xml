<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="25" hostname="197NODMB24984.local" name="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN" tests="67" failures="3" timestamp="2022-07-13T13:25:47 IST" time="27.348" errors="1">
  <testcase name="TC0001_CreateApplicantOauth" time="2.932" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0002_GetApplicantToken" time="1.423" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0003_FetchDetails" time="0.499" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0004_FetchDetails" time="0.372" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0005_FetchDetails" time="0.381" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0006_FetchDetails" time="0.364" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0007_FetchDetails" time="0.636" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0008_CreateLead" time="0.444" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0009_CreateLead" time="0.392" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00010_CreateLead" time="0.388" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00011_CreateLead" time="0.839" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00012_FetchDetails" time="0.650" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00013_CreateLead" time="0.369" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00014_SaveBusiness" time="0.402" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00015_SaveBusiness" time="0.522" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00016_SaveBusiness" time="0.370" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00017_SaveBusiness" time="0.382" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00018_SaveBusiness" time="0.559" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00019_FetchDetails" time="0.490" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00020_AdditionalDetails" time="0.350" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00021_AdditionalDetails" time="0.342" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00022_AdditionalDetails" time="0.525" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0023_AdditonalDetails" time="0.412" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00024_FetchDetails" time="0.667" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0025_SaveIdentityDetails" time="0.339" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN.TC0025_SaveIdentityDetails(FlowUnifiedPaymentWithGSTIN.java:660)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0025_SaveIdentityDetails -->
  <testcase name="TC0026_SaveIdentityDetails" time="1.984" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Update request Submitted Successfully]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Update request Submitted Successfully]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN.TC0026_SaveIdentityDetails(FlowUnifiedPaymentWithGSTIN.java:685)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0026_SaveIdentityDetails -->
  <testcase name="TC0027_SaveIdentityDetails" time="1.232" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0028_SaveIdentityDetails" time="1.201" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0029_SaveIdentityDetails" time="0.426" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0030_SaveIdentityDetails" time="1.652" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00031_FetchDetails" time="0.616" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0032_SaveIdentityGSTINDetails" time="0.532" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0033_SaveIdentityGSTINDetails" time="0.378" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0034_SaveIdentityGSTINDetails" time="0.491" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0035_SaveIdentityGSTINDetails" time="0.436" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [Invalid Request For Update]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN.TC0035_SaveIdentityGSTINDetails(FlowUnifiedPaymentWithGSTIN.java:916)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0035_SaveIdentityGSTINDetails -->
  <testcase name="TC0036_SaveIdentityGSTINDetails" time="0.468" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0037_SaveIdentityGSTINDetails" time="0.415" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0038_SaveIdentityGSTINDetails" time="0.417" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0039_SaveIdentityGSTINDetails" time="0.462" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC00040_FetchDetails" time="0.610" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0041_ValidateBank" time="0.873" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN"/>
  <testcase name="TC0042_SaveBankDetails" time="0.105" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <error type="java.lang.RuntimeException" message="Method type and path are not specified for: SaveBankUPM">
      <![CDATA[java.lang.RuntimeException: Method type and path are not specified for: SaveBankUPM
at com.paytm.apitools.core.AbstractApi.init(AbstractApi.java:76)
at com.paytm.apitools.core.AbstractApi.<init>(AbstractApi.java:46)
at com.paytm.apitools.core.AbstractApiV2.<init>(AbstractApiV2.java:47)
at com.paytm.apitools.core.AbstractApiV2.<init>(AbstractApiV2.java:68)
at Request.MerchantService.v1.sdMerchant.saveBank.SaveBankUPM.<init>(SaveBankUPM.java:11)
at OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN.TC0042_SaveBankDetails(FlowUnifiedPaymentWithGSTIN.java:1087)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0042_SaveBankDetails -->
  <testcase name="TC0043_SaveIdentityGSTINDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0043_SaveIdentityGSTINDetails -->
  <testcase name="TC00044_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC00044_FetchDetails -->
  <testcase name="TC0045_ValidateBank" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0045_ValidateBank -->
  <testcase name="TC0046_ValidateBank" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0046_ValidateBank -->
  <testcase name="TC0047_ValidateBank" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0047_ValidateBank -->
  <testcase name="TC0048_ValidateBank" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0048_ValidateBank -->
  <testcase name="TC0049_ValidateBank" time="0.001" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0049_ValidateBank -->
  <testcase name="TC0050_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0050_SaveBankDetails -->
  <testcase name="TC0051_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0051_SaveBankDetails -->
  <testcase name="TC0052_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0052_SaveBankDetails -->
  <testcase name="TC0053_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0053_SaveBankDetails -->
  <testcase name="TC0054_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0054_SaveBankDetails -->
  <testcase name="TC0055_SaveBankDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0055_SaveBankDetails -->
  <testcase name="TC00056_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC00056_FetchDetails -->
  <testcase name="TC0057_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0057_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0058_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0058_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0059_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0059_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC0060_SaveRefreeCodeAndWhatsappNotification" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0060_SaveRefreeCodeAndWhatsappNotification -->
  <testcase name="TC00061_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC00061_FetchDetails -->
  <testcase name="TC0062_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0062_CreateAccountForUPM_V2 -->
  <testcase name="TC0063_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0063_CreateAccountForUPM_V2 -->
  <testcase name="TC0064_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0064_CreateAccountForUPM_V2 -->
  <testcase name="TC0065_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0065_CreateAccountForUPM_V2 -->
  <testcase name="TC0066_CreateAccountForUPM_V2" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC0066_CreateAccountForUPM_V2 -->
  <testcase name="TC00067_FetchDetails" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN">
    <skipped/>
  </testcase> <!-- TC00067_FetchDetails -->
</testsuite> <!-- OCL.Individual.UnifiedPaymentMerchant.V2.FlowUnifiedPaymentWithGSTIN -->
