<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline" tests="19" failures="3" timestamp="2022-07-13T12:06:15 IST" time="52.581" errors="14">
  <testcase name="TC0002_postUpdateBusiness" time="0.106" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateBusiness(MiddlewareServices.java:3805)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0002_postUpdateBusiness(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:253)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0002_postUpdateBusiness -->
  <testcase name="TC0017_postUpdateBankDetailsTest" time="0.086" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineMerchantUpdateBank(MiddlewareServices.java:3992)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0017_postUpdateBankDetailsTest(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:935)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0017_postUpdateBankDetailsTest -->
  <testcase name="TC0013_postUpdateAdditionalDetailsAdhaar" time="0.106" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateAddtionalDetailsAdhaarIndigoOnboarding(MiddlewareServices.java:3880)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0013_postUpdateAdditionalDetailsAdhaar(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:719)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0013_postUpdateAdditionalDetailsAdhaar -->
  <testcase name="TC0006_postTncUpdate" time="0.092" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineMerchantUpdateTnc(MiddlewareServices.java:4006)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0006_postTncUpdate(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:474)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0006_postTncUpdate -->
  <testcase name="TC0018_postTncUpdate" time="0.088" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineMerchantUpdateTnc(MiddlewareServices.java:4006)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0018_postTncUpdate(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:964)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0018_postTncUpdate -->
  <testcase name="TC0016_postValidateBankDetailsTest" time="0.402" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [404]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [404]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0016_postValidateBankDetailsTest(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:892)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0016_postValidateBankDetailsTest -->
  <testcase name="TC0008_fetchLeadData" time="1.092" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLeadData(MiddlewareServices.java:3698)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0008_fetchLeadData(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:541)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0008_fetchLeadData -->
  <testcase name="TC0005_postUpdateBankDetailsTest" time="0.092" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineMerchantUpdateBank(MiddlewareServices.java:3992)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0005_postUpdateBankDetailsTest(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:443)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0005_postUpdateBankDetailsTest -->
  <testcase name="TC0011_postUpdateAdditionalDetailsGSTIN" time="0.263" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateAddtionalDetailsGSTIndigoOnboarding(MiddlewareServices.java:3858)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0011_postUpdateAdditionalDetailsGSTIN(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:645)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0011_postUpdateAdditionalDetailsGSTIN -->
  <testcase name="TC0007_postUploadDocs" time="0.100" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchDocumentStatusv2(MiddlewareServices.java:1730)
at com.goldengate.common.BaseMethod.FetchUploadDiyDoc(BaseMethod.java:935)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0007_postUploadDocs(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:517)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0007_postUploadDocs -->
  <testcase name="TC0011_PG_CALLBACK_MANUAL" time="24.054" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline"/>
  <testcase name="TC009_PG_CALLBACK_MANUAL" time="24.036" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline"/>
  <testcase name="TC0001_postCreateLead" time="0.448" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0001_postCreateLead(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:215)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- TC0001_postCreateLead -->
  <testcase name="TC0012_fetchLeadData" time="0.110" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineFetchLeadData(MiddlewareServices.java:3698)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0012_fetchLeadData(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:677)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0012_fetchLeadData -->
  <testcase name="TC0004_postValidateBankDetailsTest" time="0.545" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [404]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [404]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0004_postValidateBankDetailsTest(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:400)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0004_postValidateBankDetailsTest -->
  <testcase name="TC0015_postUpdateBusiness" time="0.111" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateBusiness(MiddlewareServices.java:3805)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0015_postUpdateBusiness(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:839)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0015_postUpdateBusiness -->
  <testcase name="TC0014_postUpdateAdditionalDetailsAddress" time="0.091" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateAddtionalDetailsAddressIndigoOnboarding(MiddlewareServices.java:3947)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0014_postUpdateAdditionalDetailsAddress(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:804)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0014_postUpdateAdditionalDetailsAddress -->
  <testcase name="TC0003_postUpdateAdditionalDetails" time="0.095" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.onlineUpdateAdditonalDetails(MiddlewareServices.java:3828)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0003_postUpdateAdditionalDetails(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:345)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0003_postUpdateAdditionalDetails -->
  <testcase name="TC0010_postCreateOfflineToOnlineLead" time="0.664" classname="OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline.TC0010_postCreateOfflineToOnlineLead(TestOnlineMerchantV1UpgradeMIDOfflineToOnline.java:615)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0010_postCreateOfflineToOnlineLead -->
</testsuite> <!-- OCL.DIY.OnlineMerchantIndigoOnboarding.TestOnlineMerchantV1UpgradeMIDOfflineToOnline -->
