<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="1" name="OCL.UAD.AddUADCategory" time="0.614" errors="1" timestamp="2022-07-14T20:25:41 IST" skipped="0">
  <testcase classname="OCL.UAD.AddUADCategory" name="AddingNewCategory" time="0.614">
    <error type="java.lang.ExceptionInInitializerError">
      <![CDATA[java.lang.ExceptionInInitializerError
at com.paytm.apitools.core.http.HttpClient.<clinit>(HttpClient.java:24)
at com.goldengate.common.BaseMethod.AgentSessionToken(BaseMethod.java:208)
at OCL.UAD.AddUADCategory.AddingNewCategory(AddUADCategory.java:30)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:78)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:567)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
Caused by: com.paytm.apitools.exception.InvalidConfigurationException: Invalid config in 'TESTDATA': key can't be empty
at com.paytm.apitools.core.P.<clinit>(P.java:76)
... 28 more
]]>
    </error>
  </testcase> <!-- AddingNewCategory -->
</testsuite> <!-- OCL.UAD.AddUADCategory -->
