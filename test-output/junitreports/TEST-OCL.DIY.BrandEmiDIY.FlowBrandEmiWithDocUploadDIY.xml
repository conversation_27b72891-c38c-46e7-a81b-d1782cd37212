<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY" tests="13" failures="2" timestamp="2022-07-13T12:28:08 IST" time="37.902" errors="5">
  <testcase name="TC_006_CreateLeadBrandEMIDIY" time="0.728" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_006_CreateLeadBrandEMIDIY(FlowBrandEmiWithDocUploadDIY.java:176)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_006_CreateLeadBrandEMIDIY -->
  <testcase name="TC_002_GetAllBrandsWithCorrectMIDPassed" time="3.031" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="TC_011_FetchChildLeadStagefromPanelBrandEMIDiy" time="6.916" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_011_FetchChildLeadStagefromPanelBrandEMIDiy(FlowBrandEmiWithDocUploadDIY.java:267)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC_011_FetchChildLeadStagefromPanelBrandEMIDiy -->
  <testcase name="TC_008_FetchChildLeadBrandEMIDIY" time="0.163" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchleadBrandEmiDIY(MiddlewareServices.java:4128)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_008_FetchChildLeadBrandEMIDIY(FlowBrandEmiWithDocUploadDIY.java:222)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC_008_FetchChildLeadBrandEMIDIY -->
  <testcase name="TC_004_ValidateDealerWithCorrectDealerPassed" time="0.432" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="TC_005_GetActiveBrandListFromKyb" time="4.790" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="TC_003_ValidateDealerWithInCorrectDealerPassed" time="0.469" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="TC_013_CheckBrandActiveORNotInKyb" time="1.813" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="TC_010_ManualPGCallback" time="0.086" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="TC_012_FetchParentLeadStagefromPanelBrandEMIDiy" time="6.955" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_012_FetchParentLeadStagefromPanelBrandEMIDiy(FlowBrandEmiWithDocUploadDIY.java:287)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC_012_FetchParentLeadStagefromPanelBrandEMIDiy -->
  <testcase name="TC_009_FetchStatusLeadBrandEMIDiy" time="11.853" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_009_FetchStatusLeadBrandEMIDiy(FlowBrandEmiWithDocUploadDIY.java:238)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC_009_FetchStatusLeadBrandEMIDiy -->
  <testcase name="TC_001_CheckEligibilityforBrandEmiDIYWithAllParametersPassed" time="0.537" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_001_CheckEligibilityforBrandEmiDIYWithAllParametersPassed(FlowBrandEmiWithDocUploadDIY.java:81)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_001_CheckEligibilityforBrandEmiDIYWithAllParametersPassed -->
  <testcase name="TC_007_FetchParentLeadBrandEMIDIY" time="0.129" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchleadBrandEmiDIY(MiddlewareServices.java:4128)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_007_FetchParentLeadBrandEMIDIY(FlowBrandEmiWithDocUploadDIY.java:201)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC_007_FetchParentLeadBrandEMIDIY -->
</testsuite> <!-- OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY -->
