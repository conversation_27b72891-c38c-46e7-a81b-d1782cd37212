<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="8" hostname="197NODMB24984.local" name="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan" tests="11" failures="0" timestamp="2022-07-13T12:28:08 IST" time="1.779" errors="1">
  <testcase name="TC011_PGCallBackFromPanel" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC011_PGCallBackFromPanel -->
  <testcase name="TC003_InternationalCardPositiveGetBusinessProfile" time="0.436" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan.TC003_InternationalCardPositiveGetBusinessProfile(FlowUpgradeMerchantPlanInternationalCardPlan.java:118)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC003_InternationalCardPositiveGetBusinessProfile -->
  <testcase name="TC007_InternationalCardPositiveSendOtpCreate" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC007_InternationalCardPositiveSendOtpCreate -->
  <testcase name="TC010_InternationalCardPositivePanelSubmit" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC010_InternationalCardPositivePanelSubmit -->
  <testcase name="TC004_InternationalCardPositiveFetchMID" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC004_InternationalCardPositiveFetchMID -->
  <testcase name="TC005_InternationalCardPositiveFetchUpgradePlans" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC005_InternationalCardPositiveFetchUpgradePlans -->
  <testcase name="TC002_InternationalCardPositiveGetBusiness" time="0.529" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan"/>
  <testcase name="TC009_InternationalCardPositiveFetchDocs" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC009_InternationalCardPositiveFetchDocs -->
  <testcase name="TC006_InternationalCardPositiveFetchTnC" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC006_InternationalCardPositiveFetchTnC -->
  <testcase name="TC001_InternationalCardPositiveSendOtpBusiness" time="0.814" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan"/>
  <testcase name="TC008_InternationalCardPositiveValidateOtpCreate" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC008_InternationalCardPositiveValidateOtpCreate -->
</testsuite> <!-- OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan -->
