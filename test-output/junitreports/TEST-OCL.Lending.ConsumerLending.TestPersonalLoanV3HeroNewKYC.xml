<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="1" tests="43" name="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" time="102.329" errors="0" timestamp="2022-10-12T19:24:56 IST" skipped="18">
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC039_PLv3HERO_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC039_PLv3HERO_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC026_PLv3HERO_SecondBRECallback" time="5.775">
    <failure message="did not expect to find [SECOND_BRE_SUCCESS] but found [LENDER_KYC_DOC_SYNC_REQUESTED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [SECOND_BRE_SUCCESS] but found [LENDER_KYC_DOC_SYNC_REQUESTED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC.TC026_PLv3HERO_SecondBRECallback(TestPersonalLoanV3HeroNewKYC.java:1052)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC026_PLv3HERO_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC032_PLv3HERO_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_PLv3HERO_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC041_PLv3HERO_FetchLeadPostSubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC041_PLv3HERO_FetchLeadPostSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC011_PLv3HERO_FetchCIR" time="6.010"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC019_PLv3HERO_UploadCustomerPhoto" time="7.201"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC020_PLv3HERO_VerifyUploadedCustomerPhoto" time="5.470"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC017_PLv3HERONewKYC_InitiateKYCSearchByPAN" time="0.622"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC021_PLv3HERO_UploadSelfie" time="5.045"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC006_PLv3HERO_FetchDataPostBasicDetailUpdate" time="0.690"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC008_PLv3HERO_FetchDataPostOccupationDetailUpdate" time="0.669"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC027_PLv3HERO_FetchDataAfterBRE2Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_PLv3HERO_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC024_PLv3HERO_FetchDataPostSelfieUploaded" time="22.277"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC029_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_PLv3HERO_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC044PLv3HERO_SubmitApplicationLMSApprovedCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC044PLv3HERO_SubmitApplicationLMSApprovedCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC018_PLv3Hero_FetchDataPostKYCIntiated" time="0.992"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC034_PLv3HERO_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_PLv3HERO_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC014_PLv3HERO_LoanOfferAccept" time="0.628"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC015_PLv3HERO_FetchDataPostLoanOfferAccept" time="0.521"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC036_PLv3HERO_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_PLv3HERO_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC037_PLv3HERO_FetchLeadPostEmandate" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_PLv3HERO_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC022_PLv3HERO_VerifyUploadedSelfie" time="5.511"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC033_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_PLv3HERO_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC016_PLv3HERONewKYC_UpdateLeadDetailsinSAI" time="0.520"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC023_PLv3HERO_FetchDataPostKYCIntiated" time="11.287"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC012_PLv3HERO_BRE1Callback" time="6.326"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC031_PLv3HERO_BRE3Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_PLv3HERO_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC040_PLv3HERO_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC040_PLv3HERO_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC004_PLV3Hero_FetchLeadAllData" time="0.802"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC007_PLV3Hero_UpdateLeadOccupationDetails" time="0.947"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC002_PLv3Hero_DeleteExistingLead" time="0.887"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC038_PLv3HERO_GenerateLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC038_PLv3HERO_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC009_PLV3Hero_UpdateLeadDetailsinSAI" time="0.661"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC035_PLv3HERO_FetchLeadPostBankVerification" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_PLv3HERO_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC013_PLv3HERO_FetchDataPostBRE1Success" time="5.618"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC003_PLv3Hero_CreateLead" time="1.438"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC043_PLv3HERO_FetchLeadPostPDCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC043_PLv3HERO_FetchLeadPostPDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC001_PLv3Hero_fetchlLead" time="5.767"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC028_PLv3HERO_AdditionalIsRequiredorNot" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_PLv3HERO_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC030_PLv3HERO_AdditionalDataCapture" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_PLv3HERO_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC042_PLv3HERO_PDCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC042_PLv3HERO_PDCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC010_PLv3HERO_FetchDataPostSAIlUpdate" time="0.506"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC" name="TC005_PLV3Hero_UpdateLeadBasicDetails" time="6.159"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanV3HeroNewKYC -->
