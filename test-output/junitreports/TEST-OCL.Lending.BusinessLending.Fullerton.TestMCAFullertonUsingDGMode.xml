<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="0" tests="11" name="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" time="22.806" errors="0" timestamp="2023-08-25T18:20:44 IST" skipped="0">
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC003_CreateFullertonLead" time="3.499"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC006_FetchCIR" time="1.968"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC010_LeadDataUpdateForKYC_InSAI" time="0.323"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC011_uploadCustomerPhoto" time="3.006"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC002_DeleteExistingLead" time="0.586"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC001_FetchLeadDeatils" time="0.755"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC005_UpdateBureauDataSetInSAI" time="0.481"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC007_FetchLeadAllData" time="10.791"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC009_FetchLeadAllData" time="0.344"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC008_UpdateExistingDetailsInSAI" time="0.279"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode" name="TC004_FetchLeadAllData" time="0.774"/>
</testsuite> <!-- OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonUsingDGMode -->
