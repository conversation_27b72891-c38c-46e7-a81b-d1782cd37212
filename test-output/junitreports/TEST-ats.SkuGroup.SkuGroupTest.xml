<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="ats.SkuGroup.SkuGroupTest" tests="11" failures="0" timestamp="2022-07-13T13:28:49 IST" time="5.069" errors="0">
  <testcase name="PostCreateSkuGroupWithValidName" time="0.689" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="PostCreateSkuGroupWithInvalidName" time="0.468" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="PostCreateSkuGroupWithInvalidNameStartingWithsku_" time="0.437" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="PostCreateSkuGroupWithNumbers" time="0.679" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="PostCreateSkuGroupWhichAlreadyExists" time="0.355" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="PostCreateSkuGroupWithEmptyGroupName" time="0.419" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="GetFetchSkuGroupsWithValidPrefix" time="0.332" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="GetFetchSkuGroupsWithNull" time="0.316" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="GetFetchSkuGroupsWithValidGroupName" time="0.811" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="GetFetchSkuGroupWhichIsNotPresentInPageNumber2" time="0.283" classname="ats.SkuGroup.SkuGroupTest"/>
  <testcase name="GetFetchSkuGroupWithInvalidGroupName" time="0.280" classname="ats.SkuGroup.SkuGroupTest"/>
</testsuite> <!-- ats.SkuGroup.SkuGroupTest -->
