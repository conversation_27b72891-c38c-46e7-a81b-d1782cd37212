<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="3" hostname="197NODMB24984.local" name="OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant" tests="6" failures="0" timestamp="2022-07-13T13:25:47 IST" time="4.874" errors="1">
  <testcase name="TC001_FetchShopDetailsRevisitOrganised" time="2.726" classname="OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant"/>
  <testcase name="TC005_PositiveFetchLeadPanelRevisitOrganised" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC005_PositiveFetchLeadPanelRevisitOrganised -->
  <testcase name="TC003_SubmitRevisitOrganised" time="0.607" classname="OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant.TC003_SubmitRevisitOrganised(FlowRevisitOrganisedMerchant.java:158)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC003_SubmitRevisitOrganised -->
  <testcase name="TC002_FetchQnaRevisitOrganised" time="1.541" classname="OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant"/>
  <testcase name="TC004_FetchAndSubmitDocRevisitOrganised" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC004_FetchAndSubmitDocRevisitOrganised -->
  <testcase name="TC006_PositiveSubmitLeadPanel" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC006_PositiveSubmitLeadPanel -->
</testsuite> <!-- OCL.Individual.RevisitMerchant.FlowRevisitOrganisedMerchant -->
