<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="38" hostname="197NODMB24984.local" name="OCL.Business.QRMerchant500K.FlowQRMerchant500K" tests="40" failures="0" timestamp="2022-07-13T13:25:47 IST" time="1.831" errors="1">
  <testcase name="TC0037_PositiveFetchLeadPanel500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0037_PositiveFetchLeadPanel500K -->
  <testcase name="TC0031_PositiveGetBanks500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0031_PositiveGetBanks500K -->
  <testcase name="TC009_DiffSolutionGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC009_DiffSolutionGetComapny500K -->
  <testcase name="TC008_DiffEntityPANGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC008_DiffEntityPANGetComapny500K -->
  <testcase name="TC0035_PositiveCreateUserWallet" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0035_PositiveCreateUserWallet -->
  <testcase name="TC0036_PositiveGetOEPanelCookie500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0036_PositiveGetOEPanelCookie500K -->
  <testcase name="TC0038_ReallocatingAgent500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0038_ReallocatingAgent500K -->
  <testcase name="TC0039_PositiveSubmitLeadPanel500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0039_PositiveSubmitLeadPanel500K -->
  <testcase name="TC001_CreateApplicantOauth" time="1.530" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K"/>
  <testcase name="TC0023_PositiveValidateOtpQRMerchant500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0023_PositiveValidateOtpQRMerchant500K -->
  <testcase name="TC0029_PositiveGetGstExemption500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0029_PositiveGetGstExemption500K -->
  <testcase name="TC0026_PositiveGetSubCategory500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0026_PositiveGetSubCategory500K -->
  <testcase name="TC0017_FalseIsPropGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0017_FalseIsPropGetComapny500K -->
  <testcase name="TC0019_PositiveGetBusinessAfterCompany500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0019_PositiveGetBusinessAfterCompany500K -->
  <testcase name="TC002_PositiveSendOtpBusiness500K" time="0.300" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Business.QRMerchant500K.FlowQRMerchant500K.TC002_PositiveSendOtpBusiness500K(FlowQRMerchant500K.java:119)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- TC002_PositiveSendOtpBusiness500K -->
  <testcase name="TC0014_EmptyEntityGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0014_EmptyEntityGetComapny500K -->
  <testcase name="TC0032_PositivePennyDropMultiNameMatch500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0032_PositivePennyDropMultiNameMatch500K -->
  <testcase name="TC0016_EmptyCustIDGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0016_EmptyCustIDGetComapny500K -->
  <testcase name="TC0027_PositiveSendOtpMerchantDeclare500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0027_PositiveSendOtpMerchantDeclare500K -->
  <testcase name="TC0040_PgCallBack500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0040_PgCallBack500K -->
  <testcase name="TC003_PositiveGetBusiness500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC003_PositiveGetBusiness500K -->
  <testcase name="TC0025_PositiveGetCategory500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0025_PositiveGetCategory500K -->
  <testcase name="TC0024_PositiveGetMerchant500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0024_PositiveGetMerchant500K -->
  <testcase name="TC0011_EmptySolutionGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0011_EmptySolutionGetComapny500K -->
  <testcase name="TC0012_DiffEntityGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0012_DiffEntityGetComapny500K -->
  <testcase name="TC007_InvalidPANGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC007_InvalidPANGetComapny500K -->
  <testcase name="TC0021_PositiveSendOtpQRMerchant500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0021_PositiveSendOtpQRMerchant500K -->
  <testcase name="TC0015_InvalidCustIDGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0015_InvalidCustIDGetComapny500K -->
  <testcase name="TC005_EmptyCustIdGetBusiness500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC005_EmptyCustIdGetBusiness500K -->
  <testcase name="TC0020_PositiveGetBusinessProfile500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0020_PositiveGetBusinessProfile500K -->
  <testcase name="TC004_InvalidCustIdGetBusiness500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC004_InvalidCustIdGetBusiness500K -->
  <testcase name="TC0030_PositiveGetDocStatus500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0030_PositiveGetDocStatus500K -->
  <testcase name="TC0033_PositiveSubmitLead500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0033_PositiveSubmitLead500K -->
  <testcase name="TC006_PositiveGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC006_PositiveGetComapny500K -->
  <testcase name="TC0028_PositiveValidateOtpMerchantDeclare500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0028_PositiveValidateOtpMerchantDeclare500K -->
  <testcase name="TC0010_InvalidSolutionGetComapny500K" time="0.001" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0010_InvalidSolutionGetComapny500K -->
  <testcase name="TC0018_PositivePostComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0018_PositivePostComapny500K -->
  <testcase name="TC0013_InvalidEntityGetComapny500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0013_InvalidEntityGetComapny500K -->
  <testcase name="TC0022_PositiveGetTnC500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0022_PositiveGetTnC500K -->
  <testcase name="TC0034_PositiveFetchDocs500K" time="0.000" classname="OCL.Business.QRMerchant500K.FlowQRMerchant500K">
    <skipped/>
  </testcase> <!-- TC0034_PositiveFetchDocs500K -->
</testsuite> <!-- OCL.Business.QRMerchant500K.FlowQRMerchant500K -->
