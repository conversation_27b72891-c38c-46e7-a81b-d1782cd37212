<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow" tests="52" failures="0" timestamp="2022-07-27T18:54:57 IST" time="36.925" errors="0">
  <testcase name="TC034_LISCallbackToLoanProcessingError" time="0.447" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC030_DeleteExistingLead" time="0.381" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC007_CreateCitiBankLead_WithoutPassingLenderId" time="0.849" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount" time="1.641" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC009_CreateCitiBankLead_WithoutPassingFlowType" time="0.653" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest" time="0.910" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2" time="0.898" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC052_LMSDataCallback" time="1.839" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC031_CreateCitiBankLead" time="2.126" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC016_CreateCitiBankLead_PassingInvalidSolutionName" time="0.614" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC019_CreateCitiBankLead_PassingInvalidEntityType" time="0.650" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC028_LISCallbackToLoanRejection" time="0.537" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC032_FetchLeadAllData" time="0.662" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName" time="0.628" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC002_DeleteExistingLead" time="0.485" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC039_FetchLeadAllData" time="0.636" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC029_FetchLeadDeatils" time="0.392" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC040_LISCallbackWithInvalidSolutionName" time="0.427" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC014_CreateCitiBankLead_WithoutPassingApplicationId" time="0.732" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC046_LISCallbackWithInvalidWorkflowOperation" time="0.388" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC048_LMSDataCallbackWithInvalidSolutionName" time="0.395" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC020_CreateCitiBankLead" time="1.189" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC025_DeleteExistingLead" time="0.373" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC049_LMSDataCallbackWithInvalidLeadId" time="0.425" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.511" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC015_CreateCitiBankLead_WithoutPassingOfferURL" time="0.620" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC045_LISCallbackToLoanDisbursedFromLoanAccepted" time="0.602" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC041_LISCallbackWithInvalidLeadId" time="0.375" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC022_CreateLeadAgain" time="0.879" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName" time="0.736" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC027_LISCallbackToLoanAccepted" time="0.427" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC043_LISCallbackWithInvalidEntityType" time="0.539" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC047_FetchLeadAllData" time="0.846" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC005_CreateCitiBankLead_WithoutPassingProductVersion" time="0.725" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC008_CreateCitiBankLead_WithoutPassingRiskSegment" time="0.673" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC023_LISCallbackToLoanRejection" time="0.414" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC050_LMSDataCallbackWithInvalidChannel" time="0.447" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC004_CreateCitiBankLead_WithoutPassingProductId" time="0.760" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC006_CreateCitiBankLead_WithoutPassingProductType" time="0.852" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC038_CreateCitiBankLead" time="0.878" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC003_CreateCitiBankLead_WithoutPassingBaseId" time="0.806" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC036_FetchLeadDeatils" time="0.413" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC037_DeleteExistingLead" time="0.360" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC017_CreateCitiBankLead_PassingInvalidChannelName" time="0.630" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC044_LISCallbackToLoanAccepted" time="0.445" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC024_FetchLeadDeatils" time="0.376" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC053_FetchLeadAllData" time="2.106" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC026_CreateCitiBankLead" time="0.859" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC051_LMSDataCallbackWithInvalidEntityType" time="0.403" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC035_LISCallbackToLoanRejection" time="0.847" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC033_LISCallbackToLoanAccepted" time="0.473" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC021_FetchLeadAllData" time="0.646" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow -->
