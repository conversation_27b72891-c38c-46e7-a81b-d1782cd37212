<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="2" hostname="***********" name="OCL.PlanManagement.FetchSubCategoryTest" tests="8" failures="0" timestamp="2022-11-10T19:53:40 IST" time="4.718" errors="0">
  <testcase name="TC02_FetchSubCategorySoundbox" time="0.440" classname="OCL.PlanManagement.FetchSubCategoryTest"/>
  <testcase name="TC01_FetchSubCategoryEDC" time="2.556" classname="OCL.PlanManagement.FetchSubCategoryTest"/>
  <testcase name="TC07_FetchSubCategoryInvalidData" time="0.419" classname="OCL.PlanManagement.FetchSubCategoryTest"/>
  <testcase name="TC03_FetchSubCategoryEmpty" time="0.442" classname="OCL.PlanManagement.FetchSubCategoryTest"/>
  <testcase name="TC08_FetchWithNoSubCategory" time="0.412" classname="OCL.PlanManagement.FetchSubCategoryTest"/>
  <testcase name="TC04_FetchSubCategoryEdcAndSoundbox" time="0.449" classname="OCL.PlanManagement.FetchSubCategoryTest"/>
  <testcase name="TC06_FetchSubCategoryNoToken" classname="OCL.PlanManagement.FetchSubCategoryTest">
    <skipped/>
  </testcase> <!-- TC06_FetchSubCategoryNoToken -->
  <testcase name="TC05_FetchSubCategoryExpiredToken" classname="OCL.PlanManagement.FetchSubCategoryTest">
    <skipped/>
  </testcase> <!-- TC05_FetchSubCategoryExpiredToken -->
</testsuite> <!-- OCL.PlanManagement.FetchSubCategoryTest -->
