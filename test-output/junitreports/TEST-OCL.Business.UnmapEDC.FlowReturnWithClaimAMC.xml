<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="11" hostname="197NODMB24984.local" name="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC" tests="16" failures="0" timestamp="2022-07-13T12:28:08 IST" time="11.608" errors="1">
  <testcase name="UnMapEdcReturnPositiveSendOtpBusiness" time="0.993" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
  <testcase name="UnmapEDCReturnPositiveValidateOtp" time="6.009" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <error type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at com.goldengate.common.BaseMethod.getOTPFromSellerPanel(BaseMethod.java:1868)
at OCL.Business.UnmapEDC.FlowReturnWithClaimAMC.UnmapEDCReturnPositiveValidateOtp(FlowReturnWithClaimAMC.java:88)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 16 more
]]>
    </error>
  </testcase> <!-- UnmapEDCReturnPositiveValidateOtp -->
  <testcase name="UnMapEdcReturnPositiveGetBusiness" time="0.002" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnPositiveGetBusiness -->
  <testcase name="UnMapEdcPositiveFetchMID" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveFetchMID -->
  <testcase name="UnMapEdcReturnWithClaimAMCReasons" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnWithClaimAMCReasons -->
  <testcase name="UnMapEdcReturnWithClaimAMCCreateLead" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnWithClaimAMCCreateLead -->
  <testcase name="UnMapEdcReturnPositiveGetMerchantDetails" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnPositiveGetMerchantDetails -->
  <testcase name="UnMapEdcFetchQnA" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcFetchQnA -->
  <testcase name="UnmapEDCfetchAllTerminalFromPG" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnmapEDCfetchAllTerminalFromPG -->
  <testcase name="ValidateEdcQrReturnWithClaimAMC" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- ValidateEdcQrReturnWithClaimAMC -->
  <testcase name="FetchTnCUnmapEDC" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- FetchTnCUnmapEDC -->
  <testcase name="UnMapEdcPositiveSendOtp" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveSendOtp -->
  <testcase name="UnMapEdcPositiveValidateOtp" time="0.000" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveValidateOtp -->
  <testcase name="createQRforUnmapEDCWithClaimAMC" time="3.800" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
  <testcase name="UnMapEdcPayMerchant" time="0.445" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
  <testcase name="UnmapEDCMachine_ReturnWithClaimAMC" time="0.359" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
</testsuite> <!-- OCL.Business.UnmapEDC.FlowReturnWithClaimAMC -->
