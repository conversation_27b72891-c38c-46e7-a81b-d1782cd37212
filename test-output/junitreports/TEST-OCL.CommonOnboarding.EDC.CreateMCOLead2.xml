<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="33" name="OCL.CommonOnboarding.EDC.CreateMCOLead2" time="27.703" errors="0" timestamp="2023-11-07T13:39:52 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_010_createNewMCOLeadValidateOTPWithoutMobileNumber" time="0.575"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_006_NewcreateNewMCOLeadValidateOTPWithoutentityType" time="0.913"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_009_createNewMCOLeadValidateOTPInvaliduserType" time="0.441"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_005_NewcreateNewMCOLeadValidateOTPWithoutXMWChecksumbypass" time="0.301"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_002_ExistingLeadinValidateOTPCall" time="3.514"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_007_createNewMCOLeadValidateOTPInvalidentityType" time="0.492"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_007_NewcreateNewMCOLeadValidateOTPInvalidentityType" time="0.761"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="test" time="0.277"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_12_NewgetcreateNewMCOExistingLead" time="0.896"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_005_createNewMCOLeadValidateOTPWithoutXMWChecksumbypass" time="0.305"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_013_EDCContextLeadinValidateOTPCall" time="1.352"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_010_NewcreateNewMCOLeadValidateOTPWithoutMobileNumber" time="0.607"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_004_NewcreateNewMCOLeadValidateOTPWithoutdeviceIdentifier" time="0.343"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_002_NewExistingLeadinValidateOTPCall" time="2.190"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_006_createNewMCOLeadValidateOTPWithoutentityType" time="1.717"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_013_NewEDCContextLeadinValidateOTPCall" time="1.050"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_009_NewcreateNewMCOLeadValidateOTPInvaliduserType" time="0.959"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_008_NewcreateNewMCOLeadValidateOTPInvalidentityType" time="0.549"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_004_createNewMCOLeadValidateOTPWithoutdeviceIdentifier" time="0.264"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_008_createNewMCOLeadValidateOTPInvalidentityType" time="0.287"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_12_getcreateNewMCOExistingLead" time="1.128"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_003_NewcreateNewMCOLeadValidateOTPWithoutVersion" time="0.333"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_003_createNewMCOLeadValidateOTPWithoutVersion" time="0.454"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_003_UnauthorizedAccess" time="0.624"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_004_InvalidSolutionType" time="1.279"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_008_InvalidMobileNumber" time="0.942"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_005_MissingMobileNumber" time="0.485"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_009_MissingUserType" time="0.415"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_007_ValidResponseFormatNew" time="0.841"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_006_ValidResponseFormat" time="0.786"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_007_ValidResponseFo" time="0.604"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_010_MissingSkipOtp" time="1.395"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateMCOLead2" name="TC_008_ValidResponseFormatNew" time="0.624"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.CreateMCOLead2 -->
