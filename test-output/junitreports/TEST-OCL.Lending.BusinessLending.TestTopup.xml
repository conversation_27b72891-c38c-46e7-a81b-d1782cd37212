<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="2" tests="31" name="OCL.Lending.BusinessLending.TestTopup" time="31.008" errors="0" timestamp="12 May 2021 13:45:22 GMT" skipped="1">
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC021_VerifyUploadedDocument" time="0.690"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC020_UploadSelfie" time="3.000"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC024_CKYCCallback" time="0.899"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC004_FetchTheCreatedLeadDeatils" time="0.872">
    <failure message="expected [Two Lakh only] but found [One Lakh only]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [Two Lakh only] but found [One Lakh only]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:453)
at org.testng.Assert.assertEquals(Assert.java:463)
at OCL.Lending.BusinessLending.TestTopup.TC004_FetchTheCreatedLeadDeatils(TestTopup.java:186)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- TC004_FetchTheCreatedLeadDeatils -->
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC016_FetchLeadStage" time="0.639"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC018_VerifyLeadStage" time="0.637"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC010_VerifyLeadStage" time="0.852"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC028_EmandateCallback" time="0.643"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC012_VerifyLeadStage" time="0.814"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC025_VerifyLeadStage" time="0.769"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC017_CheckCKYCStatus" time="1.086"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC031_SubmitApplication" time="2.596">
    <failure message="expected [success] but found [failed]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [success] but found [failed]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:453)
at org.testng.Assert.assertEquals(Assert.java:463)
at OCL.Lending.BusinessLending.TestTopup.TC031_SubmitApplication(TestTopup.java:929)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- TC031_SubmitApplication -->
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC027_SaveBankDetails" time="1.194"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC011_FetchBREResponse" time="0.863"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC006_AddBasicDetails" time="0.655"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC009_BREValidationPending" time="0.667"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC003_CreateBusinessLendingLead" time="0.994"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC008_BREOTPVerification" time="0.686"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC023_VerifyUploadedDocument" time="0.803"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC032_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC026_UpdateKYCNameInSAI" time="0.610"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC015_OTPCallback" time="0.780"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC005_PPBLOTPCallback" time="1.725"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC013_CheckBREResponse" time="1.316"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC007_UpdateAdditionalDetails" time="0.664"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC014_VerifyLeadStage" time="0.668"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC030_FetchDynamicTncSanctionLetter" time="0.839"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC001_DeleteExistingLead" time="0.569"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC002_FetchLeadDeatils" time="0.703"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC022_UploadCustomerPhoto" time="2.915"/>
  <testcase classname="OCL.Lending.BusinessLending.TestTopup" name="TC029_FetchDynamicTnc" time="0.860"/>
</testsuite> <!-- OCL.Lending.BusinessLending.TestTopup -->
