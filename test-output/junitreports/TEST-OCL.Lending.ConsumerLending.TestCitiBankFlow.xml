<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestCitiBankFlow" tests="10" failures="0" timestamp="2022-07-27T18:54:57 IST" time="10.027" errors="0">
  <testcase name="TC008_FetchLeadAllData" time="0.849" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC002_DeleteExistingLead" time="0.428" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC003_CreateCitiBankLead" time="1.147" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.458" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC005_LISCallbackToLoanAccepted" time="0.399" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC010_FetchLeadAllData" time="0.395" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC009_LMSDataCallback" time="2.186" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC004_FetchLeadAllData" time="0.781" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC007_LISCallbackToLoanDisbursedFromLoanProcessingError" time="0.575" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC006_LISCallbackToLoanProcessingError" time="2.809" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestCitiBankFlow -->
