<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="2" tests="41" name="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" time="48.582" errors="0" timestamp="22 Apr 2021 07:18:18 GMT" skipped="12">
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC026_VerifyLeadStage" time="1.796"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC028_VerifyLeadStage" time="2.278">
    <failure message="expected [BRE_RESPONSE_AWAITED] but found [ADDRESS_DETAILS]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [BRE_RESPONSE_AWAITED] but found [ADDRESS_DETAILS]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:137)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:453)
at org.testng.Assert.assertEquals(Assert.java:463)
at OCL.Lending.LendingBaseAPI.fetchTheCurrentLeadStage(LendingBaseAPI.java:498)
at OCL.Lending.ConsumerLending.TestPersonalLoanCLIX.TC028_VerifyLeadStage(TestPersonalLoanCLIX.java:686)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- TC028_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC009_EmailCallback" time="0.894"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC035_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC029_CheckBREResponse" time="1.735">
    <failure message="expected [We could not fetch your details. Please try again later.] but found [null]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: expected [We could not fetch your details. Please try again later.] but found [null]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertEqualsImpl(Assert.java:132)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:453)
at org.testng.Assert.assertEquals(Assert.java:463)
at OCL.Lending.ConsumerLending.TestPersonalLoanCLIX.TC029_CheckBREResponse(TestPersonalLoanCLIX.java:754)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- TC029_CheckBREResponse -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC024_VerifyLeadStage" time="1.701"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC041_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC041_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC013_OTPCallback" time="0.886"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC018_VerifyUploadedDocument" time="1.671"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC040_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC040_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC030_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC025_AddAddress" time="0.828"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC019_UploadCustomerPhoto" time="3.412"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC010_FetchTheCreatedLeadDeatils" time="2.519"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC031_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC027_FetchBREResponse" time="1.064"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC033_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC005_PPBLOTPCallback" time="0.912"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC006_FetchTheCreatedLeadDeatils" time="2.084"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC003_CreatePersonalLoanLead" time="1.722"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC002_FetchLeadDeatils" time="1.652"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC022_VerifyLeadStage" time="1.734"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC001_DeleteExistingLead" time="1.704"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC034_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC012_FetchTheCreatedLeadDeatils" time="1.896"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC039_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC039_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC023_BREOTPVerification" time="0.990"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC036_FetchDynamicTnc" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC014_FetchLeadStage" time="1.397"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC015_CheckCKYCStatus" time="1.714"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC021_CKYCCallback" time="1.091"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC038_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC038_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC037_FetchDynamicTncSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_FetchDynamicTncSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC017_UploadSelfie" time="3.280"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC008_FetchTheCreatedLeadDeatils" time="1.589"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC016_VerifyLeadStage" time="1.507"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC004_FetchTheCreatedLeadDeatils" time="1.728"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC011_UpdateAdditionalDetails" time="1.467"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC020_VerifyUploadedDocument" time="1.749"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC032_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanCLIX" name="TC007_AddBasicDetails" time="1.582"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanCLIX -->
