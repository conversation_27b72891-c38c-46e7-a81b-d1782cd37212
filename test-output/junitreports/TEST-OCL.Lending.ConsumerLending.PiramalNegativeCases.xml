<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.PiramalNegativeCases" tests="58" failures="0" timestamp="2022-04-28T19:31:48 IST" time="57.796" errors="0">
  <testcase name="TC018_BasicDetailsWithBlankEmail" time="0.679" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC021_BasicDetailsWithEmptyPincode" time="0.766" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC044_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_PAN" time="0.577" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.745" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC023_BasicDetailsWithEmptyWorkflowOperation" time="0.749" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC009_CreateBTDistributionPiramalLead_WithoutOfferEndDate" time="0.631" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC004_CreateBTDistributionPiramalLead_WithoutProductId" time="0.684" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC052_UpdateToAppointmentBookedSuccessStage" time="1.246" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC046_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_DOB" time="0.689" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC053_CallbackToLoanSanctioned_WithoutPassing_APPLICATION_ID" time="0.553" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC055_CallbackToLoanSanctioned_WithoutPassing_LOAN_AMOUNT" time="0.777" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC054_CallbackToLoanSanctioned_WithoutPassing_APPLICATION_ID" time="0.607" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC012_CreateBTDistributionPiramalLead" time="2.410" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC036_UpdateAdditionalDetailsWithoutPassing_PROPERTY_TYPE" time="0.558" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC033_UpdateAdditionalDetailsWithoutPassing_LOAN_BALANCE_TRANSFER_BANK" time="0.579" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC002_DeleteExistingLead" time="0.868" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC006_CreateBTDistributionPiramalLead_WithoutProductVersion" time="0.762" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC057_CallbackToLoanSanctioned_WithoutPassing_LOAN_TENURE_UNIT" time="0.519" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC005_CreateBTDistributionPiramalLead_WithoutProductType" time="0.673" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC022_BasicDetailsWithInvalidWorkflowOperation" time="0.744" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC048_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_EMPLOYMENT_TYPE" time="0.567" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC017_BasicDetailsWithInvalidPan" time="0.683" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC050_AcceptLoanOffer" time="3.106" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC003_CreateBTDistributionPiramalLead_WithoutLenderId" time="0.858" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC032_UpdateAdditionalDetailsWithoutPassing_LOAN_BALANCE_TRANSFER_TYPE" time="0.677" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC022_BasicDetailsWithInvalidPincode" time="0.709" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC027_UpdateBureauDataSetInSAI" time="0.561" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC035_UpdateAdditionalDetailsWithoutPassing_EMPLOYMENT_TYPE" time="0.632" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC011_CreateBTDistributionPiramalLead_WithoutStaticTNCSetName" time="0.701" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC026_FetchLeadAllData" time="1.404" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC013_FetchLeadAllData" time="1.271" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC038_UpdateAdditionalDetailsWithoutPassing_LOAN_AMOUNT_REQUESTED" time="0.610" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC047_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_EMPLOYMENT_TYPE" time="0.554" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC016_BasicDetailsWithBlankPan" time="0.699" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC031_FetchLeadAllData" time="3.667" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC024_UpdateLeadBasicDetails" time="0.975" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC051_UpdateToAppointmentBookedFailureNode" time="0.685" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC040_UpdateAdditionalDetailsWithCoApplicantDetails" time="1.079" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC056_CallbackToLoanSanctioned_WithoutPassing_LOAN_TENURE" time="0.602" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC019_BasicDetailsWithInvalidEmail" time="0.564" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC042_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_GENDER" time="0.638" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC010_CreateBTDistributionPiramalLead_WithoutLoanAmount" time="0.681" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC014_BasicDetailsWithBlankDOB" time="0.622" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC020_BasicDetailsWithEmptyCityName" time="0.557" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC039_UpdateAdditionalDetailsWithoutPassing_LOAN_TENURE_REQUESTED" time="0.572" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC030_UpdateExistingDetailsInSAI" time="0.728" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC041_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_NAME" time="0.728" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC029_FetchLeadAllData" time="1.674" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC034_UpdateAdditionalDetailsWithoutPassing_LOAN_BALANCE_TRANSFER_DATE" time="1.730" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC037_UpdateAdditionalDetailsWithoutPassing_PROPERTY_VALUE" time="0.635" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC045_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_RELATION" time="0.532" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC025_HitBasicDetailsAgain_When_LeadhasAlreadyMovedToThisStage" time="0.661" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC015_BasicDetailsWithInvalidDOB" time="0.564" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC043_UpdateCoApplicantDetails_WithoutPassing_CO_APPLICANT_MOBILE_NUMBER" time="0.586" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC028_FetchCIR" time="7.540" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC008_CreateBTDistributionPiramalLead_WithoutOfferStartDate" time="0.779" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC049_UpdateCoApplicantDetails" time="1.414" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
  <testcase name="TC007_CreateBTDistributionPiramalLead_WithoutFlowType" time="0.735" classname="OCL.Lending.ConsumerLending.PiramalNegativeCases"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.PiramalNegativeCases -->
