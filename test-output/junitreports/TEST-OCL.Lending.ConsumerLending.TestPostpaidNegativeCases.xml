<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="1" tests="68" name="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" time="61.974" errors="0" timestamp="2021-07-05T14:47:46 IST" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0035_OTPCallback_Without_passing_LeadId" time="0.768"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0030_AddBasicDetails_Without_Passing_channel" time="0.527"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0033_FetchTheLeadDeatils" time="0.870"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0024_AddBasicDetails_With_Invalid_Pan" time="0.538"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0031_AddBasicDetails_Without_Passing_SSOToken" time="0.557"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0017_CreatePostpaidLead_without_passing_OFFER_END_DATE" time="0.556"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0038_OTPCallback_Invalid_solution" time="0.520"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC052_UploadSelfie_Without_Passing_DocProvided" time="2.634"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0028_AddBasicDetails_Without_Passing_EntityType" time="0.530"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0041_CheckCKYCStatus_without_passing_EntityType" time="0.650"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0015_CreatePostpaidLead_without_passing_LENDING_DYNAMIC_TNC" time="0.490"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0045_CheckCKYCStatus_without_passing_SolutionTypeLevel2" time="0.981"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC063_BREValidationPendingCallback" time="0.572"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC062_BREValidationPendingCallback_without_Passing_Status_RequestBody" time="0.719"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0019_CreatePostpaidLead_without_passing_TNC_ADDITIONAL_PARAM" time="0.642"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC067_FetchBREResponse_without_passing_channel" time="0.503"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC008_CreatePostpaidLead_without_passing_LenderId" time="0.629"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC068_FetchBREResponse" time="0.529">
    <failure message="did not expect to find [200] but found [500]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Lending.ConsumerLending.TestPostpaidNegativeCases.verifyResponseCodeAs200OK(TestPostpaidNegativeCases.java:2276)
at OCL.Lending.ConsumerLending.TestPostpaidNegativeCases.TC068_FetchBREResponse(TestPostpaidNegativeCases.java:2240)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </failure>
  </testcase> <!-- TC068_FetchBREResponse -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC005_CreatePostpaidLead_WithoutPassing_Channel" time="0.513"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC004_CreatePostpaidLead_WithoutPassing_Solution" time="0.509"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0037_OTPCallback_Without_passing_Jwttoken" time="0.649"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC060_BREValidationPendingCallback_without_Passing_token" time="0.666"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0023_AddBasicDetails_Without_Passing_Pan" time="0.613"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0042_CheckCKYCStatus_without_passing_Solution" time="0.495"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0011_CreatePostpaidLead_without_passing_ProductID" time="0.587"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0046_CheckCKYCStatus_without_passing_SSOToken" time="0.480"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC054_UploadSelfie" time="3.020"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC053_UploadSelfie_Without_Passing_SessionToken" time="2.724"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0039_OTPCallback" time="0.811"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC009_CreatePostpaidLead_without_passing_LoanMaxAmount" time="0.678"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC058_BREValidationPendingCallback_without_Passing_leadid" time="0.651"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC059_BREValidationPendingCallback_without_Passing_Solution" time="0.790"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC048_VerifyLeadStage" time="0.673"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC066_FetchBREResponse_without_passing_entityType" time="0.495"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC003_CreatePostpaidLead_WithoutPassing_EntityType" time="0.589"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC002_FetchLeadDeatils" time="2.071"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0036_OTPCallback_Without_passing_Solution" time="0.613"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0016_CreatePostpaidLead_without_passing_OFFER_START_DATE" time="0.576"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0047_CheckCKYCStatus" time="0.822"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0032_AddBasicDetails" time="0.690"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0018_CreatePostpaidLead_without_passing_CONVENIENCE_FEE" time="0.564"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0022_FetchTheCreatedLeadDeatils" time="0.954"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC061_BREValidationPendingCallback_without_Passing_CustomerId" time="1.455"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC006_CreatePostpaidLead_WithoutPassing_SSOToken" time="0.598"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0013_CreatePostpaidLead_without_passing_ProductVersion" time="0.554"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC064_VerifyLeadStage" time="0.571"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0029_AddBasicDetails_Without_Passing_Solution" time="0.478"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0014_CreatePostpaidLead_without_passing_ProductType" time="0.713"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0021_CreatePostpaidLead" time="1.083"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC057_VerifyUploadedDocument" time="0.859"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC001_DeleteExistingLead" time="0.619"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0025_AddBasicDetails_With_Invalid_DOB" time="1.410"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC055_VerifyUploadedDocument" time="0.638"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0044_CheckCKYCStatus_without_passing_SolutionTypeLevel2" time="0.784"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC049_UploadSelfie_Without_Passing_Solution" time="2.657"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0026_AddBasicDetails_With_Invalid_Email" time="0.701"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC007_CreatePostpaidLead_WithoutPassing_ContentType" time="0.612"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0034_OTPCallback_with_passing_wrong_LeadId" time="0.815"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC065_FetchBREResponse_without_passing_Solution" time="0.555"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0040_FetchLeadStage" time="0.761"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC050_UploadSelfie_Without_Passing_SolutionTypeLevel2" time="2.711"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0027_AddBasicDetails_Without_Passing_DOB" time="0.537"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0043_CheckCKYCStatus_without_passing_Channel" time="0.613"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC056_UploadCustomerPhoto" time="2.902"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0020_CreatePostpaidLead_without_passing_GST" time="0.623"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0010_CreatePostpaidLead_without_passing_BaseId" time="0.578"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC051_UploadSelfie_With_Passing_InvalidLeadid" time="3.186"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidNegativeCases" name="TC0012_CreatePostpaidLead_without_passing_WhitelistingSource" time="0.513"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostpaidNegativeCases -->
