<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="21" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup" tests="29" failures="1" timestamp="2022-07-13T13:12:25 IST" time="12.517" errors="0">
  <testcase name="TC024_PLv3HERO_TOPUP_GenerateLoanAgreement" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC024_PLv3HERO_TOPUP_GenerateLoanAgreement -->
  <testcase name="TC023_PLv3HERO_TOPUP_FetchLeadPostEmandate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC023_PLv3HERO_TOPUP_FetchLeadPostEmandate -->
  <testcase name="TC001_PLv3_HERO_TOPUP_fetchlLead" time="0.535" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup"/>
  <testcase name="TC030PLv3HERO_TOPUP_SubmitApplicationLMSApprovedCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC030PLv3HERO_TOPUP_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC021_PLv3HERO_TOPUP_FetchLeadPostBankVerification" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC021_PLv3HERO_TOPUP_FetchLeadPostBankVerification -->
  <testcase name="TC011_PLv3HERO_Topup_FetchDataPostLoanOfferAccept" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC011_PLv3HERO_Topup_FetchDataPostLoanOfferAccept -->
  <testcase name="TC005_PLV3Hero_Topup_UpdateLeadDetailsinSAI" time="0.444" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup"/>
  <testcase name="TC029_PLv3HERO_TOPUP_FetchLeadPostPDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC029_PLv3HERO_TOPUP_FetchLeadPostPDCCallback -->
  <testcase name="TC012_PLv3HERO_TOPUP_SecondBREInitiated" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC012_PLv3HERO_TOPUP_SecondBREInitiated -->
  <testcase name="TC009_PLv3HERO_Topup_FetchDataPostBRE1Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC009_PLv3HERO_Topup_FetchDataPostBRE1Success -->
  <testcase name="TC017_PLv3HERO_TOPUP_BRE3Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC017_PLv3HERO_TOPUP_BRE3Success -->
  <testcase name="TC019_PLv3HERO_TOPUP_FetchLeadUpdateCKYCinSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC019_PLv3HERO_TOPUP_FetchLeadUpdateCKYCinSAI -->
  <testcase name="TC003_PLv3_HERO_TOPUP_CreateLead" time="1.332" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup"/>
  <testcase name="TC027_PLv3HERO_TOPUP_FetchLead_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC027_PLv3HERO_TOPUP_FetchLead_SubmitApplication -->
  <testcase name="TC004_PLV3HeroTopup_FetchLeadAllData" time="1.054" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup"/>
  <testcase name="TC016_PLv3HERO_TOPUP_AdditionalDataCapture" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC016_PLv3HERO_TOPUP_AdditionalDataCapture -->
  <testcase name="TC018_PLv3HERO_TOPUP_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC018_PLv3HERO_TOPUP_UpdateKYCNameInSAI -->
  <testcase name="TC015_PLv3HERO_TOPUP_FetchLeadVerifyAdditionalData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC015_PLv3HERO_TOPUP_FetchLeadVerifyAdditionalData -->
  <testcase name="TC028_PLv3HERO_TOPUP_PDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC028_PLv3HERO_TOPUP_PDCCallback -->
  <testcase name="TC002_PLv3_HERO_TOPUP_DeleteExistingLead" time="0.335" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup"/>
  <testcase name="TC06_PLv3HERO_Topup_FetchDataPost_SAI_Update" time="0.534" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup"/>
  <testcase name="TC010_PLv3HERO_TOPUP_LoanOfferAccept" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC010_PLv3HERO_TOPUP_LoanOfferAccept -->
  <testcase name="TC013_PLv3HERO_TOPUP_SecondBRECallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC013_PLv3HERO_TOPUP_SecondBRECallback -->
  <testcase name="TC026_PLv3HERO_TOPUP_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC026_PLv3HERO_TOPUP_SubmitApplication -->
  <testcase name="TC020_PLv3HERO_HERO_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC020_PLv3HERO_HERO_SaveBankDetails -->
  <testcase name="TC07_PLv3HERO_TOPUP_FetchCIR" time="5.080" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup"/>
  <testcase name="TC008_PLv3HERO_Topup_BRE1Callback" time="3.203" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <failure type="java.lang.AssertionError" message="did not expect to find [OFFER_REQUESTED] but found [BUREAU_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [OFFER_REQUESTED] but found [BUREAU_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup.TC008_PLv3HERO_Topup_BRE1Callback(TestPersonalLoanHeroTopup.java:380)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC008_PLv3HERO_Topup_BRE1Callback -->
  <testcase name="TC022_PLv3HERO_TOPUP_EmandateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC022_PLv3HERO_TOPUP_EmandateCallback -->
  <testcase name="TC025_PLv3HERO_TOPUP_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup">
    <skipped/>
  </testcase> <!-- TC025_PLv3HERO_TOPUP_GenerateSanctionLetter -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopup -->
