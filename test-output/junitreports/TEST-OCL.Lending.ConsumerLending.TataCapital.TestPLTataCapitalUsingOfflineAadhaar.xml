<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="0" tests="10" name="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" time="9.761" errors="0" timestamp="2023-09-25T19:55:30 IST" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC008_FetchCIR" time="0.805"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC006_FetchLeadAllData" time="0.782"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC001_FetchLeadDetails_PL_TCL" time="0.569"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC002_DeleteExistingLead" time="0.345"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC003_Create_PL_TCL_Lead" time="0.785"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC005_UpdateLeadBasicDetails" time="1.277"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC004_FetchLeadAllData" time="0.824"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC007_UpdateBureauDataSetInSAI" time="0.382"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="TC009_FetchLeadAllData_AfterBureauSuccess" time="3.808"/>
  <testcase classname="OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar" name="test" time="0.184"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingOfflineAadhaar -->
