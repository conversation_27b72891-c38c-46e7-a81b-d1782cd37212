<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="6" hostname="197NODMB24984.local" name="OCL.Individual.PSA_DIY.FlowPsaDiy" tests="13" failures="1" timestamp="2021-11-17T11:52:54 IST" time="23.983" errors="0">
  <testcase name="TC002_CreateLeadPsaDiy" time="2.294" classname="OCL.Individual.PSA_DIY.FlowPsaDiy"/>
  <testcase name="TC003_UpdateLeadPsaDiy" time="0.700" classname="OCL.Individual.PSA_DIY.FlowPsaDiy"/>
  <testcase name="TC009_GenerateOrderPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowPsaDiy">
    <skipped/>
  </testcase> <!-- TC009_GenerateOrderPsaDiy -->
  <testcase name="TC0010_OrderPaymentPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowPsaDiy">
    <skipped/>
  </testcase> <!-- TC0010_OrderPaymentPsaDiy -->
  <testcase name="TC0011_RejectQcDocumentsPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowPsaDiy">
    <skipped/>
  </testcase> <!-- TC0011_RejectQcDocumentsPsaDiy -->
  <testcase name="TC004_FetchDocumentsPsaDiy" time="3.764" classname="OCL.Individual.PSA_DIY.FlowPsaDiy"/>
  <testcase name="TC005_SaveBakDetailsPsaDiy" time="0.712" classname="OCL.Individual.PSA_DIY.FlowPsaDiy"/>
  <testcase name="TC001_GetLeadStatusPsaDiy" time="1.176" classname="OCL.Individual.PSA_DIY.FlowPsaDiy"/>
  <testcase name="TC006_FetchApplicationStatusPsaDiy" time="0.759" classname="OCL.Individual.PSA_DIY.FlowPsaDiy"/>
  <testcase name="TC007_StartAssesmentPsaDiy" time="14.578" classname="OCL.Individual.PSA_DIY.FlowPsaDiy">
    <failure type="java.lang.AssertionError" message="did not expect to find [417] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [417] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.PSA_DIY.FlowPsaDiy.TC007_StartAssesmentPsaDiy(FlowPsaDiy.java:342)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC007_StartAssesmentPsaDiy -->
  <testcase name="TC0012_ApproveQcDocumentsPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowPsaDiy">
    <skipped/>
  </testcase> <!-- TC0012_ApproveQcDocumentsPsaDiy -->
  <testcase name="TC008_GamePindAssesmentPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowPsaDiy">
    <skipped/>
  </testcase> <!-- TC008_GamePindAssesmentPsaDiy -->
  <testcase name="TC0013_OrderCallbackPsaDiy" classname="OCL.Individual.PSA_DIY.FlowPsaDiy">
    <skipped/>
  </testcase> <!-- TC0013_OrderCallbackPsaDiy -->
</testsuite> <!-- OCL.Individual.PSA_DIY.FlowPsaDiy -->
