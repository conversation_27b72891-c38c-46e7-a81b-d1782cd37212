<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="5" hostname="197NODMB24984.local" name="OCL.Individual.PSA_DIY.FlowResellerUtility" tests="12" failures="1" timestamp="2021-11-17T11:52:54 IST" time="25.001" errors="0">
  <testcase name="GetLeadStatusPsaDiy" time="1.390" classname="OCL.Individual.PSA_DIY.FlowResellerUtility"/>
  <testcase name="CreateLeadPsaDiy" time="1.232" classname="OCL.Individual.PSA_DIY.FlowResellerUtility"/>
  <testcase name="UpdateLeadPsaDiy" time="0.970" classname="OCL.Individual.PSA_DIY.FlowResellerUtility"/>
  <testcase name="FetchDocumentsPsaDiy" time="4.076" classname="OCL.Individual.PSA_DIY.FlowResellerUtility"/>
  <testcase name="SaveBakDetailsPsaDiy" time="1.074" classname="OCL.Individual.PSA_DIY.FlowResellerUtility"/>
  <testcase name="FetchApplicationStatusPsaDiy" time="1.073" classname="OCL.Individual.PSA_DIY.FlowResellerUtility"/>
  <testcase name="StartAssesmentPsaDiy" time="15.186" classname="OCL.Individual.PSA_DIY.FlowResellerUtility">
    <failure type="java.lang.AssertionError" message="did not expect to find [417] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [417] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.PSA_DIY.FlowResellerUtility.StartAssesmentPsaDiy(FlowResellerUtility.java:337)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- StartAssesmentPsaDiy -->
  <testcase name="GamePindAssesmentPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowResellerUtility">
    <skipped/>
  </testcase> <!-- GamePindAssesmentPsaDiy -->
  <testcase name="GenerateOrderPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowResellerUtility">
    <skipped/>
  </testcase> <!-- GenerateOrderPsaDiy -->
  <testcase name="OrderPaymentPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowResellerUtility">
    <skipped/>
  </testcase> <!-- OrderPaymentPsaDiy -->
  <testcase name="RejectQcDocumentsPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowResellerUtility">
    <skipped/>
  </testcase> <!-- RejectQcDocumentsPsaDiy -->
  <testcase name="ApproveQcDocumentsPsaDiy" time="0.000" classname="OCL.Individual.PSA_DIY.FlowResellerUtility">
    <skipped/>
  </testcase> <!-- ApproveQcDocumentsPsaDiy -->
</testsuite> <!-- OCL.Individual.PSA_DIY.FlowResellerUtility -->
