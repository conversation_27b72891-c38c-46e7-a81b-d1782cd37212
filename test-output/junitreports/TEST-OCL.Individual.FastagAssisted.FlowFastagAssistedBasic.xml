<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="82" hostname="197NODMB24984.local" name="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic" tests="83" failures="0" timestamp="2022-07-13T12:28:08 IST" time="0.218" errors="1">
  <testcase name="TC00010_03_EmptyEntityFetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_03_EmptyEntityFetchMerchantFastag -->
  <testcase name="TC0005_20_InvalidIssuanceTypeCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_20_InvalidIssuanceTypeCreateLeadFastag -->
  <testcase name="TC00010_01_InvalidEntityFetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_01_InvalidEntityFetchMerchantFastag -->
  <testcase name="TC0005_11_EmptyTagCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_11_EmptyTagCreateLeadFastag -->
  <testcase name="TC0003_EmptyVersionDropDownList" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0003_EmptyVersionDropDownList -->
  <testcase name="TC00010_04_InvalidEntityFetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_04_InvalidEntityFetchMerchantFastag -->
  <testcase name="TC0007_09_FetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_09_FetchV3FastagDetails -->
  <testcase name="TC0005_04_InvalidEntityCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_04_InvalidEntityCreateLeadFastag -->
  <testcase name="TC00015_RejectLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00015_RejectLeadFastag -->
  <testcase name="TC00016_UploadRejectedDocumentsFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00016_UploadRejectedDocumentsFastag -->
  <testcase name="TC00010_07_EmptySessionTokenFetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_07_EmptySessionTokenFetchMerchantFastag -->
  <testcase name="TC00017_SubmitApproveDocFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00017_SubmitApproveDocFastag -->
  <testcase name="TC0006_08_InvalidTagValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_08_InvalidTagValidateFastag -->
  <testcase name="TC0003_AddingParamDropDownList" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0003_AddingParamDropDownList -->
  <testcase name="TC0004_WrongVrnIssuance" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_WrongVrnIssuance -->
  <testcase name="TC0005_10_NoVehicleNumCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_NoVehicleNumCreateLeadFastag -->
  <testcase name="TC0006_07_EmptyTagValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_07_EmptyTagValidateFastag -->
  <testcase name="TC0007_06_DifferentEntityFetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_06_DifferentEntityFetchV3FastagDetails -->
  <testcase name="TC00014_MakePaymentFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00014_MakePaymentFastag -->
  <testcase name="TC0007_08_NoEntityFetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_08_NoEntityFetchV3FastagDetails -->
  <testcase name="TC0005_05_EmptyEntityCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_05_EmptyEntityCreateLeadFastag -->
  <testcase name="TC00012_UploadDocumentsFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00012_UploadDocumentsFastag -->
  <testcase name="TC00011_SubmitLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00011_SubmitLeadFastag -->
  <testcase name="TC0006_04_EmptyVehicleValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_04_EmptyVehicleValidateFastag -->
  <testcase name="TC0005_08_EmptySolLevel3CreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_08_EmptySolLevel3CreateLeadFastag -->
  <testcase name="TC00010_02_DifferentEntityFetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_02_DifferentEntityFetchMerchantFastag -->
  <testcase name="TC0007_04_NoSolutionFetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_04_NoSolutionFetchV3FastagDetails -->
  <testcase name="TC00013_04_EmptyQrPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_04_EmptyQrPaymentStatusFastag -->
  <testcase name="TC0006_02_InvalidLeadIdValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_02_InvalidLeadIdValidateFastag -->
  <testcase name="TC00010_06_DifferentEntityFetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_06_DifferentEntityFetchMerchantFastag -->
  <testcase name="TC0005_17_EmptyFlowCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_17_EmptyFlowCreateLeadFastag -->
  <testcase name="TC0005_10_InvalidSolLevel3CreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_InvalidSolLevel3CreateLeadFastag -->
  <testcase name="TC0006_06_NoVehicleValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_06_NoVehicleValidateFastag -->
  <testcase name="TC0005_13_NoTagCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_13_NoTagCreateLeadFastag -->
  <testcase name="TC0004_FetchIssuance" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_FetchIssuance -->
  <testcase name="TC0002_FetchRandomTag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_FetchRandomTag -->
  <testcase name="TC0004_EmptyVrnIssuance" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_EmptyVrnIssuance -->
  <testcase name="TC00010_08_FetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_08_FetchMerchantFastag -->
  <testcase name="TC0001_FetchValidTags" time="0.217" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <error type="java.net.MalformedURLException" message="For input string: &quot;8084v1&quot;">
      <![CDATA[java.net.MalformedURLException: For input string: "8084v1"
at java.net.URL.<init>(URL.java:644)
at java.net.URL.<init>(URL.java:507)
at java.net.URL.<init>(URL.java:456)
at sun.reflect.GeneratedConstructorAccessor25.newInstance(Unknown Source)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:250)
at io.restassured.internal.RequestSpecificationImpl.getTargetURI(RequestSpecificationImpl.groovy:1636)
at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:169)
at io.restassured.internal.RequestSpecificationImpl.partiallyApplyPathParams(RequestSpecificationImpl.groovy:1777)
at sun.reflect.GeneratedMethodAccessor24.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.newFilterContext(RequestSpecificationImpl.groovy:1156)
at sun.reflect.GeneratedMethodAccessor34.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.applyPathParamsAndSendRequest(RequestSpecificationImpl.groovy:1747)
at sun.reflect.GeneratedMethodAccessor86.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.applyPathParamsAndSendRequest(RequestSpecificationImpl.groovy:1755)
at sun.reflect.GeneratedMethodAccessor85.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallCurrent(CallSiteArray.java:51)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:157)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.get(RequestSpecificationImpl.groovy:171)
at io.restassured.internal.RequestSpecificationImpl.get(RequestSpecificationImpl.groovy)
at com.paytm.apitools.core.http.HttpClient.send(HttpClient.java:86)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:224)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.FisFetchTags(MiddlewareServices.java:3159)
at OCL.Individual.FastagAssisted.FlowFastagAssistedBasic.TC0001_FetchValidTags(FlowFastagAssistedBasic.java:123)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NumberFormatException: For input string: "8084v1"
at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
at java.lang.Integer.parseInt(Integer.java:580)
at java.lang.Integer.parseInt(Integer.java:615)
at java.net.URLStreamHandler.parseURL(URLStreamHandler.java:222)
at java.net.URL.<init>(URL.java:639)
... 96 more
]]>
    </error>
  </testcase> <!-- TC0001_FetchValidTags -->
  <testcase name="TC0005_21_CreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_21_CreateLeadFastag -->
  <testcase name="TC0002_AddingExtraParamValidateTag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_AddingExtraParamValidateTag -->
  <testcase name="TC0002_EmptyBodyValidateTag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_EmptyBodyValidateTag -->
  <testcase name="TC0006_03_NoLeadIdValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_03_NoLeadIdValidateFastag -->
  <testcase name="TC0005_12_InvalidTagCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_12_InvalidTagCreateLeadFastag -->
  <testcase name="TC00013_07_NoParamsPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_07_NoParamsPaymentStatusFastag -->
  <testcase name="TC0005_09_NoSolLevel3CreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_09_NoSolLevel3CreateLeadFastag -->
  <testcase name="TC00013_05_InvalidQrPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_05_InvalidQrPaymentStatusFastag -->
  <testcase name="TC0005_07_NoEntityCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_07_NoEntityCreateLeadFastag -->
  <testcase name="TC0004_EmptyVersionFetchIssuance" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_EmptyVersionFetchIssuance -->
  <testcase name="TC0005_19_EmptyIssuanceTypeCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_19_EmptyIssuanceTypeCreateLeadFastag -->
  <testcase name="TC0002_EmptyVersionValidateTag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_EmptyVersionValidateTag -->
  <testcase name="TC00013_02_EmptyLeadFetchPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_02_EmptyLeadFetchPaymentStatusFastag -->
  <testcase name="TC00013_03_NoLeadFetchPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_03_NoLeadFetchPaymentStatusFastag -->
  <testcase name="TC0004_WrongParameterIssuance" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_WrongParameterIssuance -->
  <testcase name="TC0005_16_InvalidFlowCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_16_InvalidFlowCreateLeadFastag -->
  <testcase name="TC0007_01_InvalidSolutionFetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_01_InvalidSolutionFetchV3FastagDetails -->
  <testcase name="TC0007_07_EmptyEntityFetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_07_EmptyEntityFetchV3FastagDetails -->
  <testcase name="TC00013_08_WrongParamsPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_08_WrongParamsPaymentStatusFastag -->
  <testcase name="TC0005_14_NoFlowCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_14_NoFlowCreateLeadFastag -->
  <testcase name="TC0005_01_InvalidSolutionCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_01_InvalidSolutionCreateLeadFastag -->
  <testcase name="TC0005_10_EmptyVehicleNumCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_EmptyVehicleNumCreateLeadFastag -->
  <testcase name="TC0007_03_DifferentSolutionFetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_03_DifferentSolutionFetchV3FastagDetails -->
  <testcase name="TC0003_GettingDropDownList" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0003_GettingDropDownList -->
  <testcase name="TC00013_09_FetchPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_09_FetchPaymentStatusFastag -->
  <testcase name="TC0006_09_NoTagValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_09_NoTagValidateFastag -->
  <testcase name="TC00012_ValidateAfterSubmitFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00012_ValidateAfterSubmitFastag -->
  <testcase name="TC0005_10_InvalidVehicleNumCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_InvalidVehicleNumCreateLeadFastag -->
  <testcase name="TC0005_02_EmptySolutionCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_02_EmptySolutionCreateLeadFastag -->
  <testcase name="TC0005_18_NoIssuanceTypeCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_18_NoIssuanceTypeCreateLeadFastag -->
  <testcase name="TC0007_05_InvalidEntityFetchV3FastagDetails" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_05_InvalidEntityFetchV3FastagDetails -->
  <testcase name="TC00010_05_EmptyEntityFetchMerchantFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_05_EmptyEntityFetchMerchantFastag -->
  <testcase name="TC0007_02_EmptySolutionFetchV3FastagDetails" time="0.001" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_02_EmptySolutionFetchV3FastagDetails -->
  <testcase name="TC0009_ValidateOtpFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0009_ValidateOtpFastag -->
  <testcase name="TC0008_SentOtpFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0008_SentOtpFastag -->
  <testcase name="TC0006_01_EmptyLeadIdValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_01_EmptyLeadIdValidateFastag -->
  <testcase name="TC00013_06_NoQrPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_06_NoQrPaymentStatusFastag -->
  <testcase name="TC0006_05_InvalidVehicleValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_05_InvalidVehicleValidateFastag -->
  <testcase name="TC0002_FetchEmptyTag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_FetchEmptyTag -->
  <testcase name="TC0005_06_DifferentEntityCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_06_DifferentEntityCreateLeadFastag -->
  <testcase name="TC0005_03_NoSolutionCreateLeadFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_03_NoSolutionCreateLeadFastag -->
  <testcase name="TC0006_10_ValidateFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_10_ValidateFastag -->
  <testcase name="TC0002_ValidateTags" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_ValidateTags -->
  <testcase name="TC00013_01_InvalidLeadFetchPaymentStatusFastag" time="0.000" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_01_InvalidLeadFetchPaymentStatusFastag -->
</testsuite> <!-- OCL.Individual.FastagAssisted.FlowFastagAssistedBasic -->
