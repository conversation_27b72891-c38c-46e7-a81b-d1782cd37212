<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984.local" failures="0" tests="33" name="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" time="104.849" errors="1" timestamp="2021-07-05T14:47:46 IST" skipped="10">
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC015_UploadCustomerPhoto" time="3.993"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC003_CreatePostpaidLead" time="1.362"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC021_FetchBREResponse" time="0.788"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC002_FetchLeadDeatils" time="0.985"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC028_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC018_VerifyLeadStage" time="0.751"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC008_FetchTheLeadDeatils" time="1.245"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC022_VerifyLeadStage" time="0.720"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC014_VerifyUploadedDocument" time="0.836"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC009_OTPCallback" time="0.919"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC029_FetchDynamicTncSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_FetchDynamicTncSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC023_CheckBREResponse" time="76.022">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.base/java.math.BigInteger.<init>(BigInteger.java:479)
at java.base/java.math.BigInteger.<init>(BigInteger.java:672)
at OCL.Lending.ConsumerLending.TestPostpaidABFLLite.TC023_CheckBREResponse(TestPostpaidABFLLite.java:591)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:566)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)
]]>
    </error>
  </testcase> <!-- TC023_CheckBREResponse -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC030_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC027_FetchDynamicTnc" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC016_VerifyUploadedDocument" time="0.774"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC032_LoanStatusCallbackAfterSubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_LoanStatusCallbackAfterSubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC025_AddAddress" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_AddAddress -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC011_CheckCKYCStatus" time="0.995"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC019_BREValidationPendingCallback" time="1.611"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC010_FetchLeadStage" time="0.917"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC001_DeleteExistingLead" time="0.798"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC006_FetchTheCreatedLeadDeatils" time="1.076"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC005_PPBLOTPCallback" time="1.915"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC007_AddBasicDetails" time="0.814"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC004_FetchTheCreatedLeadDeatils" time="2.455"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC031_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC033_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC026_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_VerifyLeadStage -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC013_UploadSelfie" time="3.309"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC012_VerifyLeadStage" time="0.670"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC017_CKYCCallback" time="1.192"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC020_VerifyLeadStage" time="0.702"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidABFLLite" name="TC024_VerifyLeadStage" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_VerifyLeadStage -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostpaidABFLLite -->
