<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="1" tests="41" name="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" time="83.577" errors="0" timestamp="2022-09-22T19:14:20 IST" skipped="19">
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC018_PLv3HERO_UploadSelfie" time="3.142"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC029_PLv3HERO_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_PLv3HERO_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC026_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_PLv3HERO_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC010_PLv3HERO_FetchDataPostSAIlUpdate" time="0.812"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC011_PLv3HERO_FetchCIR" time="7.217"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC004_PLV3Hero_FetchLeadAllData" time="1.586"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC013_PLv3HERO_FetchDataPostBRE1Success" time="5.766"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC027_PLv3HERO_AdditionalDataCapture" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_PLv3HERO_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC028_PLv3HERO_BRE3Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_PLv3HERO_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC006_PLv3HERO_FetchDataPostBasicDetailUpdate" time="1.567"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC021_PLv3HERO_FetchDataPostPanVerified" time="0.798"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC040_PLv3HERO_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC040_PLv3HERO_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC039_PLv3HERO_SaveBankDetailsAgain" time="0.000">
    <skipped/>
  </testcase> <!-- TC039_PLv3HERO_SaveBankDetailsAgain -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC023_PLv3HERO_SecondBRECallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_PLv3HERO_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC002_PLv3Hero_DeleteExistingLead" time="1.266"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC007_PLV3Hero_UpdateLeadOccupationDetails" time="11.320"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC036_PLv3HERO_UpdateKYCNameInSAIForNewBankValidation" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_PLv3HERO_UpdateKYCNameInSAIForNewBankValidation -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC025_PLv3HERO_AdditionalIsRequiredorNot" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_PLv3HERO_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC033_PLv3HERO_BankValidationPending" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_PLv3HERO_BankValidationPending -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC020_PLv3HERO_CKYCCallback" time="6.248"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC015_PLv3HERO_FetchDataPostLoanOfferAccept" time="1.026"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC030_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_PLv3HERO_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC019_PLv3HERO_VerifyUploadedSelfie" time="0.772"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC014_PLv3HERO_LoanOfferAccept" time="0.854"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC032_PLv3HERO_FetchLeadAndValidatePennyDropFailedDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_PLv3HERO_FetchLeadAndValidatePennyDropFailedDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC008_PLv3HERO_FetchDataPostOccupationDetailUpdate" time="6.309"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC005_PLV3Hero_UpdateLeadBasicDetails" time="1.395"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC009_PLV3Hero_UpdateLeadDetailsinSAI" time="0.747"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC003_PLv3Hero_CreateLead" time="6.032"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC037_PLv3HERO_UpdateNewBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_PLv3HERO_UpdateNewBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC038_PLv3HERO_VerifyPennydropForNewBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC038_PLv3HERO_VerifyPennydropForNewBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC041_PLv3HERO_SaveBankDetailsPostEmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC041_PLv3HERO_SaveBankDetailsPostEmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC022_PLv3HERO_SecondBREInitiated" time="1.337">
    <failure message="did not expect to find [KYC_IN_PROGRESS] but found [BRE2_IN_PROGRESS]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [KYC_IN_PROGRESS] but found [BRE2_IN_PROGRESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero.TC022_PLv3HERO_SecondBREInitiated(TestBankScenariosForPLV3Hero.java:957)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC022_PLv3HERO_SecondBREInitiated -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC016_PLv3HERO_UploadCustomerPhoto" time="3.879"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC031_PLv3HERO_SaveBankDetailsPennyDropfailed" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_PLv3HERO_SaveBankDetailsPennyDropfailed -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC012_PLv3HERO_BRE1Callback" time="14.871"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC017_PLv3HERO_VerifyUploadedCustomerPhoto" time="5.792"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC034_PLv3HERO_UpdateKYCNameInSAIPostBANK_VALIDATION_PENDING" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_PLv3HERO_UpdateKYCNameInSAIPostBANK_VALIDATION_PENDING -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC024_PLv3HERO_FetchDataAfterBRE2Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_PLv3HERO_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC001_PLv3Hero_fetchlLead" time="0.841"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero" name="TC035_PLv3HERO_BANKING_ACTION_DONE" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_PLv3HERO_BANKING_ACTION_DONE -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestBankScenariosForPLV3Hero -->
