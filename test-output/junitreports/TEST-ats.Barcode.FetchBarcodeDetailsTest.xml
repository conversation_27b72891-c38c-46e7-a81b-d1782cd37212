<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="ats.Barcode.FetchBarcodeDetailsTest" tests="31" failures="0" timestamp="2022-07-13T13:28:49 IST" time="11.510" errors="0">
  <testcase name="fetchBarcodeDetails" time="1.037" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithoutBarcode" time="0.335" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithIncorrectBarcode" time="0.320" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithoutFetchStrategy" time="0.374" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithInvalidFetchStrategy" time="0.295" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsCanAssign" time="0.439" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsCanAssignWithoutBarcode" time="0.256" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsCanAssignWithIncorrectBarcode" time="0.640" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="v2FetchBarcodeDetails" time="0.471" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="v2FetchBarcodeDetailsWithoutBarcode" time="0.292" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="v2FetchBarcodeDetailsWithIncorrectBarcode" time="0.279" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="v2FetchBarcodeDetailsWithoutFetchStrategy" time="0.284" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="v2FetchBarcodeDetailsWithInvalidFetchStrategy" time="0.283" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="downloadBarcodeDetails" time="0.348" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="downloadBarcodeDetailsWithoutBarcode" time="0.253" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="downloadBarcodeDetailsWithIncorrectBarcode" time="0.412" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithStrategyBarcodeDetails" time="0.490" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithStrategyLinkedBarcodes" time="0.324" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithStrategyBarcodeSkuDetails" time="0.282" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithoutBarcodeAndFetchStrategy" time="0.267" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithoutAccept" time="0.386" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithoutAcceptLanguage" time="0.337" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithoutContentype" time="0.276" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsWithTwoStrategies" time="0.322" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsOfChildBarcode" time="0.344" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsOfChildBarcodeFetchStrategyBarcodeSkuDetails" time="0.305" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsOfChildBarcodeFetchStrategyLinkedBarcodes" time="0.270" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsOfMasterBarcode" time="0.603" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsOfMasterBarcodeWithStrategyLinkedBarcodes" time="0.336" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsOfMasterBarcodeWithStrategyBarcodeSkuDetails" time="0.385" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
  <testcase name="fetchBarcodeDetailsOfMasterBarcodeWithStrategy" time="0.265" classname="ats.Barcode.FetchBarcodeDetailsTest"/>
</testsuite> <!-- ats.Barcode.FetchBarcodeDetailsTest -->
