<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="87" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases" tests="181" failures="2" timestamp="2021-09-03T19:16:00 IST" time="143.870" errors="0">
  <testcase name="TC090_CheckBREResponse_WithoutPassingChannelName" time="1.373" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC096_AcceptLoanOffer_WithoutPassingChannel" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC096_AcceptLoanOffer_WithoutPassingChannel -->
  <testcase name="TC136_SecondBREInitiateCallback_PassingInvalidStatus" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC136_SecondBREInitiateCallback_PassingInvalidStatus -->
  <testcase name="TC082_BREValidationPending_HappyCase" time="1.884" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC100_AcceptLoanOffer_WithoutPassingLoanTenureUnit" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC100_AcceptLoanOffer_WithoutPassingLoanTenureUnit -->
  <testcase name="TC090_CheckBREResponse_WithoutPassingSolutionName" time="1.407" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC069_OccupationDetailsCallback_WithoutPassingEntityType" time="1.171" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC125_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC125_UploadCustomerPhoto -->
  <testcase name="TC120_UploadSelfie_WithoutPassingSolutionTypeLevel2" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC120_UploadSelfie_WithoutPassingSolutionTypeLevel2 -->
  <testcase name="TC164_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC164_VerifyLeadStage -->
  <testcase name="TC135_SecondBREInitiateCallback_WithoutPassingCustId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC135_SecondBREInitiateCallback_WithoutPassingCustId -->
  <testcase name="TC098_AcceptLoanOffer_PassingInvalidWorkflowSubOperation" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC098_AcceptLoanOffer_PassingInvalidWorkflowSubOperation -->
  <testcase name="TC145_SecondBREStatusCallback_PassingInvalidStatusMessage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC145_SecondBREStatusCallback_PassingInvalidStatusMessage -->
  <testcase name="TC080_BREValidationPending_PassingInvalidStatus" time="1.141" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC101_AcceptLoanOffer_WithoutPassingLoanAmountInNumber" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC101_AcceptLoanOffer_WithoutPassingLoanAmountInNumber -->
  <testcase name="TC150_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC150_VerifyLeadStage -->
  <testcase name="TC168_EmandateCallback_WithoutPassingCustId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC168_EmandateCallback_WithoutPassingCustId -->
  <testcase name="TC056_BasicDetailsUpdateCallback_PassingEmptyPAN" time="1.245" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC046_BasicDetailsUpdateCallback_WithoutPassingAuthorization" time="1.165" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC079_BREValidationPending_WithoutPassingCustId" time="1.113" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC122_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC122_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams -->
  <testcase name="TC137_SecondBREInitiateCallback_PassingBlankStatus" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC137_SecondBREInitiateCallback_PassingBlankStatus -->
  <testcase name="TC144_SecondBREStatusCallback_WithoutPassingCustId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC144_SecondBREStatusCallback_WithoutPassingCustId -->
  <testcase name="TC054_BasicDetailsUpdateCallback_PassingEmptyPAN" time="1.297" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC115_CheckCKYCStatus_HappyCase" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC115_CheckCKYCStatus_HappyCase -->
  <testcase name="TC068_OccupationDetailsCallback_WithoutPassingSolutionNameInQuery" time="1.158" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC123_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC123_UploadSelfie -->
  <testcase name="TC010_CreatePersonalLoanLead_WithoutPassingLoanBaseIdInRequest" time="1.164" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC153_AdditionalDataCapture_WithoutPassingAuthorization" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC153_AdditionalDataCapture_WithoutPassingAuthorization -->
  <testcase name="TC004_CreatePersonalLoanLead_WithoutPassingEntityType" time="1.364" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC075_VerifyLeadStage" time="1.370" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC076_BREValidationPending_WithoutPassingLeadId" time="1.048" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC058_BasicDetailsUpdateCallback_WithoutPassingLoanPurpose" time="1.193" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC067_FetchTheCreatedLeadDeatils" time="1.842" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC131_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC131_VerifyLeadStage -->
  <testcase name="TC064_BasicDetailsUpdateCallback_WithoutPassingStaticTnc" time="0.958" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC142_SecondBREStatusCallback_WithoutPassingSolutionName" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC142_SecondBREStatusCallback_WithoutPassingSolutionName -->
  <testcase name="TC143_SecondBREStatusCallback_WithoutPassingAuthorization" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC143_SecondBREStatusCallback_WithoutPassingAuthorization -->
  <testcase name="TC039_AdiitionalInfoDOBUpdate_WithoutPassingStatusMessage" time="1.652" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC118_UploadSelfie_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC118_UploadSelfie_WithoutPassingLeadId -->
  <testcase name="TC175_SubmitApplication_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC175_SubmitApplication_WithoutPassingLeadId -->
  <testcase name="TC026_AdditionalInfoPANUpdate_WithoutPassingSolutionTypeInHeaders" time="1.083" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC106_AcceptLoanOffer_WithoutPassingLoanProcessingFees" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC106_AcceptLoanOffer_WithoutPassingLoanProcessingFees -->
  <testcase name="TC159_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC159_UpdateKYCNameInSAI -->
  <testcase name="TC083_VerifyLeadStage" time="1.238" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC094_AcceptLoanOffer_WithoutPassingSolutionName" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC094_AcceptLoanOffer_WithoutPassingSolutionName -->
  <testcase name="TC156_AdditionalDataCapture_PassingInvalidStatus" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC156_AdditionalDataCapture_PassingInvalidStatus -->
  <testcase name="TC027_AdditionalInfoPANUpdate_WithoutPassingLeadIdInHeaders" time="0.998" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC102_AcceptLoanOffer_WithoutPassingLoanAmountInWords" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC102_AcceptLoanOffer_WithoutPassingLoanAmountInWords -->
  <testcase name="TC172_FetchDynamicTnc" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC172_FetchDynamicTnc -->
  <testcase name="TC127_CKYCCallback_WithoutPassingSolutionName" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC127_CKYCCallback_WithoutPassingSolutionName -->
  <testcase name="TC012_CreatePersonalLoanLead_WithoutPassingWhitelistingSourceInRequest" time="1.534" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC057_BasicDetailsUpdateCallback_WithoutPassingLoanPurposeKey" time="1.389" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC020_PPBLOTPCallback_WithoutPassingJWTToken" time="1.289" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC003_CreatePersonalLoanLead_WithoutPassingSolutionType" time="3.647" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC132_SecondBREInitiateCallback_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC132_SecondBREInitiateCallback_WithoutPassingLeadId -->
  <testcase name="TC149_SecondBREStatusCallback_ReHit" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC149_SecondBREStatusCallback_ReHit -->
  <testcase name="TC129_CKYCCallback_WithoutPassingAuthorization" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC129_CKYCCallback_WithoutPassingAuthorization -->
  <testcase name="TC090_CheckBREResponse_WithoutPassingEntityType" time="1.147" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC128_CKYCCallback_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC128_CKYCCallback_WithoutPassingLeadId -->
  <testcase name="TC050_BasicDetailsUpdateCallback_PassingInvalidStatusMessage" time="1.105" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at Services.LendingService.LendingBaseAPI.verifyResponseCodeAs500InternalServerError(LendingBaseAPI.java:3070)
at OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases.TC050_BasicDetailsUpdateCallback_PassingInvalidStatusMessage(TestPersonalLoanHeroNegativeCases.java:1665)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC050_BasicDetailsUpdateCallback_PassingInvalidStatusMessage -->
  <testcase name="TC114_CheckCKYCStatus_WithoutPassingSessionToken" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC114_CheckCKYCStatus_WithoutPassingSessionToken -->
  <testcase name="TC018_PPBLOTPCallback_WithoutPassingLeadIdInQueryParams" time="0.918" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC087_FetchBREResponse_WithoutPassingSessionToken" time="1.106" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC045_BasicDetailsUpdateCallback_WithoutPassingLeadId" time="1.317" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC116_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC116_VerifyLeadStage -->
  <testcase name="TC158_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC158_VerifyLeadStage -->
  <testcase name="TC108_AcceptLoanOffer_WithoutPassingLoanDisbursementAmount" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC108_AcceptLoanOffer_WithoutPassingLoanDisbursementAmount -->
  <testcase name="TC112_CheckCKYCStatus_WithoutPassingSolutionName" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC112_CheckCKYCStatus_WithoutPassingSolutionName -->
  <testcase name="TC163_SaveBankDetails_Rehit" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC163_SaveBankDetails_Rehit -->
  <testcase name="TC103_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallment" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC103_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallment -->
  <testcase name="TC176_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC176_SubmitApplication -->
  <testcase name="TC171_FetchDynamicTnc_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC171_FetchDynamicTnc_WithoutPassingLeadId -->
  <testcase name="TC019_PPBLOTPCallback_WithoutPassingSolutionInQueryParams" time="1.138" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC109_AcceptLoanOffer_HappyCase" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC109_AcceptLoanOffer_HappyCase -->
  <testcase name="TC077_BREValidationPending_WithoutPassingSolutionName" time="1.129" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC147_SecondBREStatusCallback_PassingBlankStatus" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC147_SecondBREStatusCallback_PassingBlankStatus -->
  <testcase name="TC099_AcceptLoanOffer_WithoutPassingLoanTenure" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC099_AcceptLoanOffer_WithoutPassingLoanTenure -->
  <testcase name="TC013_CreatePersonalLoanLead_WithoutPassingEmandateInRequest" time="1.278" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC014_CreatePersonalLoanLead_WithoutPassingPaytmVintageInRequest" time="1.314" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC030_AdditionalInfoPANUpdate_WithoutPassingStatusMessageInRequest" time="1.488" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC036_AdiitionalInfoDOBUpdate_WithoutPassingLeadId" time="1.373" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC060_BasicDetailsUpdateCallback_WithoutPassingLoanUserLongitude" time="1.133" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC177_SubmitApplication_Rehit" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC177_SubmitApplication_Rehit -->
  <testcase name="TC113_CheckCKYCStatus_WithoutPassingChannel" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC113_CheckCKYCStatus_WithoutPassingChannel -->
  <testcase name="TC086_FetchBREResponse_WithoutPassingChannelName" time="1.211" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC071_OccupationDetailsCallback_WithoutPassingOccupation" time="2.038" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC009_CreatePersonalLoanLead_WithoutPassingProductVersionInRequest" time="1.273" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC001_DeleteExistingLead" time="2.530" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC070_OccupationDetailsCallback_WithoutPassingSessionToken" time="1.110" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC073_FetchTheCreatedLeadDeatils" time="1.355" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC053_BasicDetailsUpdateCallback_PassingInvalidPAN" time="1.250" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC134_SecondBREInitiateCallback_WithoutPassingAuthorizationToken" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC134_SecondBREInitiateCallback_WithoutPassingAuthorizationToken -->
  <testcase name="TC167_EmandateCallback_WithoutPassingAuthorizationToken" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC167_EmandateCallback_WithoutPassingAuthorizationToken -->
  <testcase name="TC169_EmandateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC169_EmandateCallback -->
  <testcase name="TC104_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallmentInWords" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC104_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallmentInWords -->
  <testcase name="TC034_AdiitionalInfoPANUpdate" time="1.409" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC093_VerifyLeadStage" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC093_VerifyLeadStage -->
  <testcase name="TC151_AdditionalDataCapture_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC151_AdditionalDataCapture_WithoutPassingLeadId -->
  <testcase name="TC062_BasicDetailsUpdateCallback_WithoutPassingLenderCustomerId" time="1.143" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC105_AcceptLoanOffer_WithoutPassingLoanRateOfInterest" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC105_AcceptLoanOffer_WithoutPassingLoanRateOfInterest -->
  <testcase name="TC048_BasicDetailsUpdateCallback_WithoutPassingStatusInJsonRequest" time="1.099" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC0130_CKYCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC0130_CKYCCallback -->
  <testcase name="TC091_CheckBREResponse_WithoutPassingSessionToken" time="1.068" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC049_BasicDetailsUpdateCallback_WithoutPassingStatusMessage" time="1.389" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC155_AdditionalDataCapture_WithoutPassingStatus" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC155_AdditionalDataCapture_WithoutPassingStatus -->
  <testcase name="TC154_AdditionalDataCapture_WithoutPassingCustId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC154_AdditionalDataCapture_WithoutPassingCustId -->
  <testcase name="TC021_PPBLOTPCallback_WithoutPassingCustIdInHeaders" time="1.134" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC095_AcceptLoanOffer_WithoutPassingEntityType" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC095_AcceptLoanOffer_WithoutPassingEntityType -->
  <testcase name="TC166_EmandateCallback_WithoutPassingSolutionName" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC166_EmandateCallback_WithoutPassingSolutionName -->
  <testcase name="TC042_AdiitionalInfoDOBUpdate_PassingInvalidStatusInRequest" time="1.254" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC160_SaveBankDetails_WithoutPassingBankAccountNumber" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC160_SaveBankDetails_WithoutPassingBankAccountNumber -->
  <testcase name="TC016_CreatePersonalLoanLead" time="2.019" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC007_CreatePersonalLoanLead_WithoutPassingProductIdInRequest" time="1.538" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC031_AdditionalInfoPANUpdate_PassingInvalidStatusMessageInRequest" time="1.314" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC033_AdditionalInfoPANUpdate_PassingInvalidStatusInRequest" time="1.956" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC092_CheckBREResponse_HappyCase" time="19.797" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <failure type="java.lang.AssertionError" message="did not expect to find [BRE_SUCCESS] but found [BRE_RESPONSE_AWAITED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE_SUCCESS] but found [BRE_RESPONSE_AWAITED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases.TC092_CheckBREResponse_HappyCase(TestPersonalLoanHeroNegativeCases.java:3090)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC092_CheckBREResponse_HappyCase -->
  <testcase name="TC165_EmandateCallback_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC165_EmandateCallback_WithoutPassingLeadId -->
  <testcase name="TC023_PPBLOTPCallback_PassingInvalidStatus" time="1.246" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC006_CreatePersonalLoanLeadWithoutPassingSessionToken" time="1.099" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC174_FetchDynamicTncSanctionLetter" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC174_FetchDynamicTncSanctionLetter -->
  <testcase name="TC111_CheckCKYCStatus_WithoutPassingEntityType" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC111_CheckCKYCStatus_WithoutPassingEntityType -->
  <testcase name="TC178_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC178_VerifyLeadStage -->
  <testcase name="TC119_UploadSelfie_WithoutPassingSolutionType" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC119_UploadSelfie_WithoutPassingSolutionType -->
  <testcase name="TC041_AdiitionalInfoDOBUpdate_WithoutPassingStatus" time="1.412" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC052_BasicDetailsUpdateCallback_PassingEmptydDOB" time="1.057" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC008_CreatePersonalLoanLead_WithoutPassingProductTypeInRequest" time="1.730" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC180_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC180_VerifyLeadStage -->
  <testcase name="TC157_AdditionalDataCapture_HappyCase" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC157_AdditionalDataCapture_HappyCase -->
  <testcase name="TC061_BasicDetailsUpdateCallback_WithoutPassingLenderAppilicationId" time="1.185" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC044_BasicDetailsUpdateCallback_WithoutPassingSolutionType" time="1.382" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC148_SecondBREStatusCallback_HappyCase" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC148_SecondBREStatusCallback_HappyCase -->
  <testcase name="TC162_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC162_SaveBankDetails -->
  <testcase name="TC085_FetchBREResponse_WithoutPassingEntityType" time="1.176" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC040_AdiitionalInfoDOBUpdatePassingInvalidStatusMessage" time="1.211" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC065_BasicDetailsUpdateCallback_WithoutPassingStaticTncVersion" time="1.518" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC038_AdiitionalInfoDOBUpdate_WithoutPassingCustId" time="1.012" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC072_OccupationDetailsCallback" time="1.178" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC107_AcceptLoanOffer_WithoutPassingLoanProcessingFeeGST" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC107_AcceptLoanOffer_WithoutPassingLoanProcessingFeeGST -->
  <testcase name="TC015_CreatePersonalLoanLead_WithoutPassingLendingTncInRequest" time="1.189" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC024_PPBLOTPCallback" time="2.209" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC088_FetchBREResponse_HappyCase" time="1.377" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC029_AdditionalInfoPANUpdate_WithoutPassingCustIdInHeaders" time="0.926" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC078_BREValidationPending_WithoutPassingAuthorizationToken" time="1.109" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC146_SecondBREStatusCallback_PassingBlankStatusMessage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC146_SecondBREStatusCallback_PassingBlankStatusMessage -->
  <testcase name="TC005_CreatePersonalLoanLead_WithoutPassingChannel" time="1.174" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC126_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC126_VerifyUploadedDocument -->
  <testcase name="TC022_PPBLOTPCallback_WithoutPassingStatus" time="1.349" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC043_AdiitionalInfoDOBUpdate" time="0.969" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC025_FetchTheCreatedLeadDeatils" time="1.239" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC035_AdiitionalInfoDOBUpdate_WithoutPassingSolutionType" time="1.380" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC051_BasicDetailsUpdateCallback_PassingInvalidDOB" time="1.237" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC063_BasicDetailsUpdateCallback_WithoutPassingLenderRequestId" time="1.093" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC110_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC110_VerifyLeadStage -->
  <testcase name="TC161_SaveBankDetails_WithoutPassingIFSCCode" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC161_SaveBankDetails_WithoutPassingIFSCCode -->
  <testcase name="TC081_BREValidationPending_PassingBlankStatus" time="1.190" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC032_AdditionalInfoPANUpdate_WithoutPassingStatusInRequest" time="1.121" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC089_VerifyLeadStage" time="1.351" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC047_BasicDetailsUpdateCallback_WithoutPassingCustId" time="0.894" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC074_BREOTPVerification" time="1.255" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC117_UploadSelfie_WithoutPassingDocProvided" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC117_UploadSelfie_WithoutPassingDocProvided -->
  <testcase name="TC059_BasicDetailsUpdateCallback_WithoutPassingLoanUserLatitude" time="1.170" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC140_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC140_VerifyLeadStage -->
  <testcase name="TC152_AdditionalDataCapture_WithoutPassingSolutionName" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC152_AdditionalDataCapture_WithoutPassingSolutionName -->
  <testcase name="TC138_SecondBREInitiateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC138_SecondBREInitiateCallback -->
  <testcase name="TC141_SecondBREStatusCallback_WithoutPassingLeadId" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC141_SecondBREStatusCallback_WithoutPassingLeadId -->
  <testcase name="TC017_FetchTheCreatedLeadDeatils" time="2.189" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC066_BasicDetailsUpdateCallback" time="1.220" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC097_AcceptLoanOffer_WithoutPassingSessionToken" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC097_AcceptLoanOffer_WithoutPassingSessionToken -->
  <testcase name="TC179_UploadSheetONPanel" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC179_UploadSheetONPanel -->
  <testcase name="TC037_AdiitionalInfoDOBUpdate_WithoutPassingJWTToken" time="1.088" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC121_UploadSelfie_WithoutPassing_ImageFile" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC121_UploadSelfie_WithoutPassing_ImageFile -->
  <testcase name="TC124_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC124_VerifyUploadedDocument -->
  <testcase name="TC028_AdditionalInfoPANUpdate_WithoutPassingSolutionTypeInHeaders" time="1.368" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC002_FetchLeadDeatils" time="1.618" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC011_CreatePersonalLoanLead_WithoutPassingLoanLenderIdInRequest" time="1.389" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC055_BasicDetailsUpdateCallback_PassingEmptyEMAIL" time="1.394" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC139_SecondBREInitiateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC139_SecondBREInitiateCallback -->
  <testcase name="TC170_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC170_VerifyLeadStage -->
  <testcase name="TC084_FetchBREResponse_WithoutSolutionName" time="1.108" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases"/>
  <testcase name="TC173_FetchDynamicTncSanctionLetter_PassingIncorrectTNCType" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases">
    <skipped/>
  </testcase> <!-- TC173_FetchDynamicTncSanctionLetter_PassingIncorrectTNCType -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanHeroNegativeCases -->
