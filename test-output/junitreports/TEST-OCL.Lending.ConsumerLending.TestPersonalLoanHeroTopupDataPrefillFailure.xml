<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="1" tests="4" name="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopupDataPrefillFailure" time="484.301" errors="0" timestamp="2022-07-19T15:53:46 IST" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopupDataPrefillFailure" name="TC004_PLV3HeroTopup_FetchLeadAllData" time="481.207">
    <failure message="did not expect to find [LENDING_LIS_SUBMIT_APPLICATION_SUCCESS] but found [LEAD_CREATED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [LENDING_LIS_SUBMIT_APPLICATION_SUCCESS] but found [LEAD_CREATED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopupDataPrefillFailure.TC004_PLV3HeroTopup_FetchLeadAllData(TestPersonalLoanHeroTopupDataPrefillFailure.java:254)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC004_PLV3HeroTopup_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopupDataPrefillFailure" name="TC001_PLv3_HERO_TOPUP_fetchlLead" time="1.206"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopupDataPrefillFailure" name="TC002_PLv3_HERO_TOPUP_DeleteExistingLead" time="0.598"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopupDataPrefillFailure" name="TC003_PLv3_HERO_TOPUP_CreateLead" time="1.290"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanHeroTopupDataPrefillFailure -->
