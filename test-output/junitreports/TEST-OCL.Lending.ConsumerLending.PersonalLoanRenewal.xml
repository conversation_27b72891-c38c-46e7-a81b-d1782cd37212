<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="3" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.PersonalLoanRenewal" tests="32" failures="1" timestamp="2022-07-27T18:54:57 IST" time="50.916" errors="0">
  <testcase name="TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.453" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC029_PLv3HERO_PDCCallback" time="8.364" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [LOAN_AGREEMENT_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [LOAN_AGREEMENT_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC029_PLv3HERO_PDCCallback(PersonalLoanRenewal.java:1190)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC029_PLv3HERO_PDCCallback -->
  <testcase name="TC024_PLv3HERO_FetchLeadPostEmandate" time="0.362" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC026_PLv3HERO_GenerateSanctionLetter" time="0.725" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC021_PLv3HERO_SaveBankDetails" time="0.745" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC014_PLv3HERO_VerifyUploadedSelfie" time="0.452" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC019_PLv3HERO_UpdateKYCNameInSAI" time="0.926" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC012_PLv3HERO_VerifyUploadedCustomerPhoto" time="0.467" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC013_PLv3HERO_UploadSelfie" time="5.278" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC006_FetchCIR" time="0.746" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC004_FetchLeadAllData" time="0.810" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC011_PLv3HERO_UploadCustomerPhoto" time="3.021" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC005_UpdateBureauDataSetInSAI" time="0.446" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC016_VerifyLeadStage" time="14.405" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC010_FetchLeadAllData" time="0.765" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC008_UpdateExistingDetailsInSAI" time="0.406" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC028_PLv3HERO_FetchLeadPostSubmitApplication" time="0.467" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC009_PLRenewal_LoanOfferAccept" time="0.833" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC017_PLv3HERO_AdditionalIsRequiredorNot" time="0.478" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.692" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC030_PLv3HERO_FetchLeadPostPDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <skipped/>
  </testcase> <!-- TC030_PLv3HERO_FetchLeadPostPDCCallback -->
  <testcase name="TC031PLv3HERO_SubmitApplicationLMSApprovedCallback" time="0.000" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <skipped/>
  </testcase> <!-- TC031PLv3HERO_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC018_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.423" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC002_DeleteExistingLead" time="0.465" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC027_PLv3HERO_SubmitApplication" time="2.874" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC003_CreatePLRenewalLead" time="1.020" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC023_PLv3HERO_EmandateCallback" time="0.490" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC015_CKYCCallback" time="0.621" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC022_PLv3HERO_UpdateKYCNameInSAI" time="0.823" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC007_FetchLeadAllData" time="2.423" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC025_PLv3HERO_GenerateLoanAgreement" time="0.936" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC032_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <skipped/>
  </testcase> <!-- TC032_FetchLeadAllData -->
</testsuite> <!-- OCL.Lending.ConsumerLending.PersonalLoanRenewal -->
