<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="16" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA" tests="36" failures="1" timestamp="2023-07-18T19:35:09 IST" time="65.786" errors="0">
  <testcase name="TC036_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC036_FetchLeadAllData -->
  <testcase name="TC028_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC028_GenerateSanctionLetter -->
  <testcase name="TC013_VerifyUploadedSelfie" time="1.105" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC012_UploadSelfie" time="3.405" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC034_VerifyLeadStage_LMSSubmitApplicationJob" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC034_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase name="TC025_SaveBankDetails" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC025_SaveBankDetails -->
  <testcase name="TC004_FetchLeadAllData" time="1.358" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC002_DeleteExistingLead" time="0.591" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC015_FetchDataPostKYCIntiated" time="0.943" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC011_VerifyUploadedCustomerPhoto" time="0.551" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC020_FetchDataPostKYCIntiated" time="33.190" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <failure type="java.lang.AssertionError" message="did not expect to find [KYC_COMPLETED] but found [KYC_FAILED]">
      <![CDATA[java.lang.AssertionError: did not expect to find [KYC_COMPLETED] but found [KYC_FAILED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA.TC020_FetchDataPostKYCIntiated(TestMCAABFLUsingOA.java:872)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC020_FetchDataPostKYCIntiated -->
  <testcase name="TC010_uploadCustomerPhoto" time="3.041" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC016_InitiateKYC_UsingOfflineAAdhaar" time="0.882" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC033_VerifyPDCCallback" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC033_VerifyPDCCallback -->
  <testcase name="TC008_FetchCIR" time="1.013" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC018_LeadDataUpdateForKYC_InSAI" time="0.767" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC006_FetchLeadAllData" time="1.283" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC022_SecondBRECallback" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC022_SecondBRECallback -->
  <testcase name="TC009_FetchLeadAllData" time="8.422" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC029_AcceptLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC029_AcceptLoanAgreement -->
  <testcase name="TC032_FetchLeadPostEmandate" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC032_FetchLeadPostEmandate -->
  <testcase name="TC026_FetchLeadPostBankVerification" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC026_FetchLeadPostBankVerification -->
  <testcase name="TC021_FetchDataKYCInitiate" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC021_FetchDataKYCInitiate -->
  <testcase name="TC005_UpdateLeadBasicDetails" time="1.955" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC019_InitiateKYC_UsingOfflineAAdhaar" time="0.823" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.722" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC014_InitiateKYC_UsingSearchByPan" time="0.891" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC024_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC024_UpdateKYCNameInSAI -->
  <testcase name="TC035_LMSDataCallback" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC035_LMSDataCallback -->
  <testcase name="TC017_FetchDataPostKYCIntiated" time="1.848" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC001_FetchLeadDetails_MCA_Piramal" time="1.370" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC030_UpdateActualPanInSAI" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC030_UpdateActualPanInSAI -->
  <testcase name="TC031_EmandateCallback" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC031_EmandateCallback -->
  <testcase name="TC003_Create_MCA_V3_ABFL_Lead" time="1.626" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA"/>
  <testcase name="TC023_FetchLead_AfterBRE2" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC023_FetchLead_AfterBRE2 -->
  <testcase name="TC027_GenerateLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA">
    <skipped/>
  </testcase> <!-- TC027_GenerateLoanAgreement -->
</testsuite> <!-- OCL.Lending.BusinessLending.ABFL.TestMCAABFLUsingOA -->
