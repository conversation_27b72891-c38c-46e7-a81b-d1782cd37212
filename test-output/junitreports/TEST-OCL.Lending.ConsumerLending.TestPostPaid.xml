<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="ip-192-168-29-76.ap-south-1.compute.internal" failures="0" tests="33" name="OCL.Lending.ConsumerLending.TestPostPaid" time="44.474" errors="0" timestamp="11 May 2021 12:33:59 GMT" skipped="0">
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC023_CheckBREResponse" time="0.739"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC001_DeleteExistingLead" time="0.892"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC012_VerifyLeadStage" time="0.844"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC018_VerifyLeadStage" time="0.963"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC013_UploadSelfie" time="3.305"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC024_VerifyLeadStage" time="0.650"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC025_AddAddress" time="0.680"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC032_LoanStatusCallbackAfterSubmitApplication" time="11.254"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC009_OTPCallback" time="0.660"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC019_BREValidationPendingCallback" time="0.668"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC028_VerifyLeadStage" time="0.665"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC021_FetchBREResponse" time="0.726"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC026_VerifyLeadStage" time="0.701"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC005_PPBLOTPCallback" time="1.891"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC015_UploadCustomerPhoto" time="2.974"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC022_VerifyLeadStage" time="0.784"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC020_VerifyLeadStage" time="0.690"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC004_FetchTheCreatedLeadDeatils" time="1.428"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC011_CheckCKYCStatus" time="1.766"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC003_CreatePostpaidLead" time="2.665"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC017_CKYCCallback" time="0.783"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC008_FetchTheLeadDeatils" time="0.739"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC002_FetchLeadDeatils" time="0.914"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC033_VerifyLeadStage" time="0.611"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC007_AddBasicDetails" time="0.627"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC030_VerifyLeadStage" time="0.693"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC010_FetchLeadStage" time="0.659"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC029_FetchDynamicTncSanctionLetter" time="0.642"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC016_VerifyUploadedDocument" time="0.699"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC006_FetchTheCreatedLeadDeatils" time="0.678"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC014_VerifyUploadedDocument" time="0.774"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC027_FetchDynamicTnc" time="0.885"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostPaid" name="TC031_SubmitApplication" time="0.825"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostPaid -->
