<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="1" tests="10" name="OCL.PlanManagement.FetchPlanODSTest" time="2.685" errors="0" timestamp="2022-07-14T20:55:02 IST" skipped="9">
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_08_ODSFetchPlanWithNullPlanId" time="0.000">
    <skipped/>
  </testcase> <!-- TC_08_ODSFetchPlanWithNullPlanId -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_09_ODSFetchPlanWithInvalidServiceCategory" time="0.000">
    <skipped/>
  </testcase> <!-- TC_09_ODSFetchPlanWithInvalidServiceCategory -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_03_ODSFetchPlanWithInvalidServicePlanId" time="0.000">
    <skipped/>
  </testcase> <!-- TC_03_ODSFetchPlanWithInvalidServicePlanId -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_02_ODSFetchPlanWithInvalidMid" time="0.000">
    <skipped/>
  </testcase> <!-- TC_02_ODSFetchPlanWithInvalidMid -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_04_ODSFetchPlanWithInvalidCategory" time="0.000">
    <skipped/>
  </testcase> <!-- TC_04_ODSFetchPlanWithInvalidCategory -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_05_ODSFetchPlanWithInvalidSubCategory" time="0.000">
    <skipped/>
  </testcase> <!-- TC_05_ODSFetchPlanWithInvalidSubCategory -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_06_ODSFetchPlanWithoutMid" time="0.000">
    <skipped/>
  </testcase> <!-- TC_06_ODSFetchPlanWithoutMid -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_07_ODSFetchPlanWithNullMid" time="0.001">
    <skipped/>
  </testcase> <!-- TC_07_ODSFetchPlanWithNullMid -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_10_ODSFetchPlanWithInvalidBusinessWallet" time="0.000">
    <skipped/>
  </testcase> <!-- TC_10_ODSFetchPlanWithInvalidBusinessWallet -->
  <testcase classname="OCL.PlanManagement.FetchPlanODSTest" name="TC_01_ODSFetchPlan" time="2.684">
    <failure message="did not expect to find [200] but found [500]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.PlanManagement.FetchPlanODSTest.TC_01_ODSFetchPlan(FetchPlanODSTest.java:46)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:78)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:567)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC_01_ODSFetchPlan -->
</testsuite> <!-- OCL.PlanManagement.FetchPlanODSTest -->
