<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="4" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero" tests="41" failures="1" timestamp="2022-07-13T13:12:25 IST" time="39.738" errors="0">
  <testcase name="TC003_PLv3Hero_CreateLead" time="1.112" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC013_PLv3HERO_FetchDataPostBRE1Success" time="0.367" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC020_PLv3HERO_CKYCCallback" time="0.639" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC002_PLv3Hero_DeleteExistingLead" time="3.009" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC012_PLv3HERO_BRE1Callback" time="2.262" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC016_PLv3HERO_UploadCustomerPhoto" time="3.508" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC007_PLV3Hero_UpdateLeadOccupationDetails" time="0.884" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC001_PLv3Hero_fetchlLead" time="0.536" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC009_PLV3Hero_UpdateLeadDetailsinSAI" time="0.362" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC025_PLv3HERO_AdditionalIsRequiredorNot" time="0.414" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC026_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.383" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC027_PLv3HERO_AdditionalDataCapture" time="0.685" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC011_PLv3HERO_FetchCIR" time="4.345" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC040_PLv3HERO_PDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero">
    <skipped/>
  </testcase> <!-- TC040_PLv3HERO_PDCCallback -->
  <testcase name="TC015_PLv3HERO_FetchDataPostLoanOfferAccept" time="0.414" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC033_PLv3HERO_FetchLeadPostBankVerification" time="1.574" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="C036_PLv3HERO_GenerateLoanAgreement" time="1.234" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC035_PLv3HERO_FetchLeadPostEmandate" time="0.448" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC019_PLv3HERO_VerifyUploadedSelfie" time="3.319" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC038_PLv3HERO_SubmitApplication" time="0.353" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero.TC038_PLv3HERO_SubmitApplication(TestPersonalLoanV3Hero.java:1387)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC038_PLv3HERO_SubmitApplication -->
  <testcase name="TC031_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.390" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC023_PLv3HERO_SecondBRECallback" time="0.694" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC010_PLv3HERO_FetchDataPostSAIlUpdate" time="0.672" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC041_PLv3HERO_FetchLeadPostPDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero">
    <skipped/>
  </testcase> <!-- TC041_PLv3HERO_FetchLeadPostPDCCallback -->
  <testcase name="TC008_PLv3HERO_FetchDataPostOccupationDetailUpdate" time="0.741" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC037_PLv3HERO_GenerateSanctionLetter" time="0.743" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC018_PLv3HERO_UploadSelfie" time="2.812" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC042PLv3HERO_SubmitApplicationLMSApprovedCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero">
    <skipped/>
  </testcase> <!-- TC042PLv3HERO_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC034_PLv3HERO_EmandateCallback" time="0.478" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC014_PLv3HERO_LoanOfferAccept" time="0.532" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC004_PLV3Hero_FetchLeadAllData" time="0.728" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC006_PLv3HERO_FetchDataPostBasicDetailUpdate" time="0.488" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC030_PLv3HERO_UpdateKYCNameInSAI" time="0.936" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC017_PLv3HERO_VerifyUploadedCustomerPhoto" time="0.472" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC024_PLv3HERO_FetchDataAfterBRE2Success" time="0.369" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC029_PLv3HERO_BRE3Success" time="0.769" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC032_PLv3HERO_SaveBankDetails" time="1.203" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC039_PLv3HERO_FetchLeadPostSubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero">
    <skipped/>
  </testcase> <!-- TC039_PLv3HERO_FetchLeadPostSubmitApplication -->
  <testcase name="TC022_PLv3HERO_SecondBREInitiated" time="0.359" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC005_PLV3Hero_UpdateLeadBasicDetails" time="1.115" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
  <testcase name="TC021_PLv3HERO_FetchDataPostPanVerified" time="0.389" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero -->
