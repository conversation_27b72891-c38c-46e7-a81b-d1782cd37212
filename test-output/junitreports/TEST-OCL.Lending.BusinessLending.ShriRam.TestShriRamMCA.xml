<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="0" tests="7" name="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" time="5.863" errors="1" timestamp="2023-10-31T17:24:13 IST" skipped="1">
  <testcase classname="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" name="TC002_DeleteExistingLead" time="0.538"/>
  <testcase classname="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" name="TC005_FetchCIR_Async" time="0.561">
    <error message="Failed to parse the JSON document" type="io.restassured.path.json.exception.JsonPathException">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA.TC005_FetchCIR_Async(TestShriRamMCA.java:376)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 30 more
]]>
    </error>
  </testcase> <!-- TC005_FetchCIR_Async -->
  <testcase classname="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" name="TC001_FetchLeadDeatils" time="0.618"/>
  <testcase classname="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" name="TC006_FetchLeadAllData_ForBureauSuccess" time="0.000">
    <skipped/>
  </testcase> <!-- TC006_FetchLeadAllData_ForBureauSuccess -->
  <testcase classname="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" name="TC003_CreateShriRamLead" time="3.635"/>
  <testcase classname="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" name="test" time="0.187"/>
  <testcase classname="OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA" name="TC004_UpdateBureauDataSetInSAI" time="0.324"/>
</testsuite> <!-- OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA -->
