<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="10" hostname="197NODMB24984.local" name="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT" tests="19" failures="1" timestamp="2022-07-13T13:25:47 IST" time="5.362" errors="0">
  <testcase name="TC0019_PostiveRegisterEmail" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0019_PostiveRegisterEmail -->
  <testcase name="TC009_UpdatingChildLead" time="0.413" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT.TC009_UpdatingChildLead(FlowOrganisedOfflineMerchantWithCMT.java:339)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC009_UpdatingChildLead -->
  <testcase name="TC0010_UploadingDocs" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0010_UploadingDocs -->
  <testcase name="TC002_CompanyOnboard" time="1.528" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC008_PennyDrop" time="0.371" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC0011_PositiveFetchLeadPanel" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0011_PositiveFetchLeadPanel -->
  <testcase name="TC0023_PositiveSubmitCmtStaging" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0023_PositiveSubmitCmtStaging -->
  <testcase name="TC0020_AcceptingTnC" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0020_AcceptingTnC -->
  <testcase name="TC0013__PositiveSubmitLeadPanel" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0013__PositiveSubmitLeadPanel -->
  <testcase name="TC001_FetchPanDetails" time="0.518" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC0022_PositiveSubmitSap" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0022_PositiveSubmitSap -->
  <testcase name="TC007_CreateChildSolution" time="0.496" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC003_FetchBusinessProfile" time="0.402" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC006_UpdatingLead" time="0.635" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC005_VerifyGstin" time="0.318" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC0024_PositiveSubmitCmtProduction" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0024_PositiveSubmitCmtProduction -->
  <testcase name="TC0021_ManualPgCallBack" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0021_ManualPgCallBack -->
  <testcase name="TC004_CreateSolution" time="0.681" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT"/>
  <testcase name="TC0012__PositiveRejectedLeadPanel" time="0.000" classname="OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT">
    <skipped/>
  </testcase> <!-- TC0012__PositiveRejectedLeadPanel -->
</testsuite> <!-- OCL.Business.OrganisedMerchant.FlowOrganisedOfflineMerchantWithCMT -->
