<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.PiramalLAPSanity" tests="20" failures="0" timestamp="2022-04-28T19:31:48 IST" time="48.896" errors="0">
  <testcase name="TC018_FetchLeadAllData" time="1.258" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC004_FetchLeadAllData" time="1.483" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC019_LMSDataCallback" time="5.577" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.858" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC011_FetchLeadAllData" time="4.366" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC012_UpdateAdditionalDetailsWithoutCoApplicantDetails" time="0.795" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC017_LISCallbackToLoanDisbursedFromLoanSanctioned" time="1.057" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC002_DeleteExistingLead" time="1.078" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC010_UpdateExistingDetailsInSAI" time="0.674" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC003_CreateHLDistributionPiramalLead" time="3.387" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC001_FetchLeadDeatils" time="2.333" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC005_UpdateLeadBasicDetails" time="2.206" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC020_FetchLeadAllData" time="0.970" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC013_AcceptLoanOffer" time="3.721" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC008_FetchCIR" time="6.271" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC016_CallbackToLoanSanctioned" time="1.044" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC009_FetchLeadAllData" time="1.262" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC015_UpdateToAppointmentBookedSuccessStage" time="7.980" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC006_FetchLeadAllData" time="1.613" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
  <testcase name="TC014_UpdateToAppointmentBookedFailureNode" time="0.963" classname="OCL.Lending.ConsumerLending.PiramalLAPSanity"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.PiramalLAPSanity -->
