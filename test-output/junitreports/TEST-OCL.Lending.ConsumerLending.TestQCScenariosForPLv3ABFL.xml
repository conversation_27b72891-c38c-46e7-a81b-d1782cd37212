<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="0" tests="38" name="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" time="124.871" errors="1" timestamp="2022-09-22T19:14:20 IST" skipped="32">
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC014_PLv3ABFL_LoanOfferAccept" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_LoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC039_PLv3ABFL_FetchLeadAgentAllocationJob" time="0.000">
    <skipped/>
  </testcase> <!-- TC039_PLv3ABFL_FetchLeadAgentAllocationJob -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC025_PLv3ABFL_AdditionalIsRequiredorNot" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_PLv3ABFL_AdditionalIsRequiredorNot -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC002_PLv3ABFL_DeleteExistingLead" time="0.722"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC009_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC009_PLV3ABFL_UpdateLeadDetailsinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC027_PLv3ABFL_AdditionalDataCapture" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_PLv3ABFL_AdditionalDataCapture -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC028_PLv3ABFL_BRE3Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_PLv3ABFL_BRE3Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC017_PLv3ABFL_VerifyUploadedCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_PLv3ABFL_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC012_PLv3ABFL_BRE1Callback" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_PLv3ABFL_BRE1Callback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC024_PLv3ABFL_FetchDataAfterBRE2Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_PLv3ABFL_FetchDataAfterBRE2Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC031_PLv3ABFL_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_PLv3ABFL_SaveBankDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC010_PLv3ABFL_FetchDataPostSAIlUpdate" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_PLv3ABFL_FetchDataPostSAIlUpdate -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC032_PLv3ABFL_FetchLeadPostBankVerification" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_PLv3ABFL_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC008_PLv3ABFL_FetchDataPostOccupationDetailUpdate" time="0.000">
    <skipped/>
  </testcase> <!-- TC008_PLv3ABFL_FetchDataPostOccupationDetailUpdate -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC023_PLv3ABFL_SecondBRECallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_PLv3ABFL_SecondBRECallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC001_PLv3ABFL_fetchlLead" time="5.760"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC011_PLv3ABFL_FetchCIR" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_PLv3ABFL_FetchCIR -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC021_PLv3ABFL_FetchDataPostPanVerified" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_FetchDataPostPanVerified -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC007_PLV3ABFL_UpdateLeadOccupationDetails" time="0.001">
    <skipped/>
  </testcase> <!-- TC007_PLV3ABFL_UpdateLeadOccupationDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC016_PLv3ABFL_UploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_PLv3ABFL_UploadCustomerPhoto -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC030_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_PLv3ABFL_FetchLeadUpdateCKYCinSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC037_PLv3ABFL_SubmitApplication" time="0.000">
    <skipped/>
  </testcase> <!-- TC037_PLv3ABFL_SubmitApplication -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC003_PLv3ABFL_CreateLead" time="0.685"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC036_PLv3ABFL_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_PLv3ABFL_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC004_PLV3ABFL_FetchLeadAllData" time="1.251"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC018_PLv3ABFL_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC005_PLV3ABFL_UpdateLeadBasicDetails" time="2.841"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC015_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.001">
    <skipped/>
  </testcase> <!-- TC015_PLv3ABFL_FetchDataPostLoanOfferAccept -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC020_PLv3ABFL_CKYCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_CKYCCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC026_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_FetchLeadVerifyAdditionalData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC022_PLv3ABFL_SecondBREInitiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_SecondBREInitiated -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC013_PLv3ABFL_FetchDataPostBRE1Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_PLv3ABFL_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC006_PLv3ABFL_FetchDataPostBasicDetailUpdate" time="113.608">
    <error message="Remote host terminated the handshake" type="javax.net.ssl.SSLHandshakeException">
      <![CDATA[javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
at java.base/sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1709)
at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1508)
at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1415)
at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:450)
at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:421)
at org.apache.http.conn.ssl.SSLSocketFactory.createLayeredSocket(SSLSocketFactory.java:573)
at org.apache.http.conn.ssl.SSLSocketFactory.connectSocket(SSLSocketFactory.java:557)
at org.apache.http.conn.ssl.SSLSocketFactory.connectSocket(SSLSocketFactory.java:414)
at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
at org.apache.http.impl.conn.ManagedClientConnectionImpl.open(ManagedClientConnectionImpl.java:326)
at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:610)
at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:445)
at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
at org.apache.http.client.HttpClient$execute$0.call(Unknown Source)
at io.restassured.internal.RequestSpecificationImpl$RestAssuredHttpBuilder.doRequest(RequestSpecificationImpl.groovy:2146)
at io.restassured.internal.http.HTTPBuilder.doRequest(HTTPBuilder.java:494)
at io.restassured.internal.http.HTTPBuilder.request(HTTPBuilder.java:451)
at io.restassured.internal.http.HTTPBuilder$request$3.call(Unknown Source)
at io.restassured.internal.RequestSpecificationImpl.sendHttpRequest(RequestSpecificationImpl.groovy:1544)
at jdk.internal.reflect.GeneratedMethodAccessor176.invoke(Unknown Source)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at io.restassured.internal.RequestSpecificationImpl.sendRequest(RequestSpecificationImpl.groovy:1293)
at jdk.internal.reflect.GeneratedMethodAccessor93.invoke(Unknown Source)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:152)
at io.restassured.internal.filter.SendRequestFilter.filter(SendRequestFilter.groovy:30)
at io.restassured.filter.Filter$filter$0.call(Unknown Source)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at io.restassured.filter.Filter$filter.call(Unknown Source)
at io.restassured.internal.filter.FilterContextImpl.next(FilterContextImpl.groovy:72)
at io.restassured.filter.time.TimingFilter.filter(TimingFilter.java:56)
at io.restassured.filter.Filter$filter.call(Unknown Source)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at io.restassured.filter.Filter$filter.call(Unknown Source)
at io.restassured.internal.filter.FilterContextImpl.next(FilterContextImpl.groovy:72)
at io.restassured.filter.log.StatusCodeBasedLoggingFilter.filter(StatusCodeBasedLoggingFilter.java:93)
at io.restassured.filter.log.ResponseLoggingFilter.filter(ResponseLoggingFilter.java:31)
at io.restassured.filter.Filter$filter.call(Unknown Source)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at io.restassured.filter.Filter$filter.call(Unknown Source)
at io.restassured.internal.filter.FilterContextImpl.next(FilterContextImpl.groovy:72)
at io.restassured.filter.log.RequestLoggingFilter.filter(RequestLoggingFilter.java:122)
at io.restassured.filter.Filter$filter.call(Unknown Source)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at io.restassured.filter.Filter$filter$0.call(Unknown Source)
at io.restassured.internal.filter.FilterContextImpl.next(FilterContextImpl.groovy:72)
at io.restassured.filter.FilterContext$next.call(Unknown Source)
at io.restassured.internal.RequestSpecificationImpl.applyPathParamsAndSendRequest(RequestSpecificationImpl.groovy:1749)
at jdk.internal.reflect.GeneratedMethodAccessor87.invoke(Unknown Source)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.applyPathParamsAndSendRequest(RequestSpecificationImpl.groovy:1755)
at jdk.internal.reflect.GeneratedMethodAccessor86.invoke(Unknown Source)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.get(RequestSpecificationImpl.groovy:171)
at io.restassured.internal.RequestSpecificationImpl.get(RequestSpecificationImpl.groovy)
at com.paytm.apitools.core.http.HttpClient.send(HttpClient.java:86)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:224)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.LendingService.LendingBaseAPI.fetchLeadDetailsUsingWorkflowAPI(LendingBaseAPI.java:3009)
at Services.LendingService.LendingBaseAPI.fetchLeadDetails(LendingBaseAPI.java:2903)
at OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL.TC006_PLv3ABFL_FetchDataPostBasicDetailUpdate(TestQCScenariosForPLv3ABFL.java:341)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:483)
at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:472)
at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1500)
... 124 more
]]>
    </error>
  </testcase> <!-- TC006_PLv3ABFL_FetchDataPostBasicDetailUpdate -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC034_PLv3ABFL_FetchLeadPostEmandate" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_PLv3ABFL_FetchLeadPostEmandate -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="C035_PLv3ABFL_GenerateLoanAgreement" time="0.001">
    <skipped/>
  </testcase> <!-- C035_PLv3ABFL_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC033_PLv3ABFL_EmandateCallback" time="0.001">
    <skipped/>
  </testcase> <!-- TC033_PLv3ABFL_EmandateCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC029_PLv3ABFL_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL" name="TC019_PLv3ABFL_VerifyUploadedSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_VerifyUploadedSelfie -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestQCScenariosForPLv3ABFL -->
