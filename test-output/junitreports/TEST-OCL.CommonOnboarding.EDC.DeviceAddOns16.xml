<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="7" name="OCL.CommonOnboarding.EDC.DeviceAddOns16" time="119.037" errors="0" timestamp="2023-11-07T18:20:38 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.DeviceAddOns16" name="TC_4_DeviceAddOnsInvalidLeadID" time="17.182"/>
  <testcase classname="OCL.CommonOnboarding.EDC.DeviceAddOns16" name="TC_6_DeviceAddOnswithInvalidPlanID" time="21.104"/>
  <testcase classname="OCL.CommonOnboarding.EDC.DeviceAddOns16" name="TC_2_DeviceAddOnsWithoutSessiontoken" time="18.945"/>
  <testcase classname="OCL.CommonOnboarding.EDC.DeviceAddOns16" name="TC_3_DeviceAddOnsWithoutDeviceIdentifer" time="18.809"/>
  <testcase classname="OCL.CommonOnboarding.EDC.DeviceAddOns16" name="TC_1_DeviceAddons" time="22.671"/>
  <testcase classname="OCL.CommonOnboarding.EDC.DeviceAddOns16" name="TC_5_DeviceAddOnsWithoutLeadID" time="19.932"/>
  <testcase classname="OCL.CommonOnboarding.EDC.DeviceAddOns16" name="test" time="0.394"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.DeviceAddOns16 -->
