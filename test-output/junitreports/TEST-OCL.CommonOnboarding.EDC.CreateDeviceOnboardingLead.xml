<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="8" name="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" time="74.850" errors="0" timestamp="2023-11-07T16:59:09 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="TC_2_CreateDeviceOnboardingLeadWithoutSessiontoken" time="8.894"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="TC_3_CreateDeviceOnboardingLeadWithoutDeviceIdentifer" time="10.864"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="TC_6_CreateDeviceOnboardingLeadWithoutcustId" time="12.009"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="TC_5_CreateDeviceOnboardingLeadWithInvalidLeadID" time="10.716"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="TC_1_CreateDeviceOnboardingLead" time="11.130"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="TC_6_CreateDeviceOnboardingLeadWithoutLeadID" time="9.594"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="test" time="0.243"/>
  <testcase classname="OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead" name="TC_4_CreateDeviceOnboardingLeadWithoutVersion" time="11.400"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead -->
