<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="20" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowMerchantReactivation" tests="23" failures="0" timestamp="2022-07-13T13:25:47 IST" time="15.093" errors="1">
  <testcase name="TC005_1_GetLeadStatusWithEmptySolutionType" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC005_1_GetLeadStatusWithEmptySolutionType -->
  <testcase name="TC005_5_GetLeadStatusWithEmptyMID" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC005_5_GetLeadStatusWithEmptyMID -->
  <testcase name="TC013_GetCommisonMIDInActive" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC013_GetCommisonMIDInActive -->
  <testcase name="TC001_CreateApplicantOauth" time="1.547" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation"/>
  <testcase name="TC005_GetLeadStatus" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC005_GetLeadStatus -->
  <testcase name="TC005_4_GetLeadStatusWithEmptySessionToken" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC005_4_GetLeadStatusWithEmptySessionToken -->
  <testcase name="TC005_3_GetLeadStatusWithEmptySolutionSubtype" time="0.001" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC005_3_GetLeadStatusWithEmptySolutionSubtype -->
  <testcase name="TC015_GetCommisonWithEmptySolution" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC015_GetCommisonWithEmptySolution -->
  <testcase name="TC014_GetCommisonWithEmptySession" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC014_GetCommisonWithEmptySession -->
  <testcase name="TC005_2_GetLeadStatusWithEmptyEntityType" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC005_2_GetLeadStatusWithEmptyEntityType -->
  <testcase name="TC004_MakeMIDInactive" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC004_MakeMIDInactive -->
  <testcase name="TC018_GetCommisonMIDActive" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC018_GetCommisonMIDActive -->
  <testcase name="TC016_GetCommisonWithEmptyEntity" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC016_GetCommisonWithEmptyEntity -->
  <testcase name="TC003_GetMID" time="12.814" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.goldengate.common.BaseMethod.FetchMIDFromPG(BaseMethod.java:1442)
at OCL.Individual.ProfileUpdate.FlowMerchantReactivation.TC003_GetMID(FlowMerchantReactivation.java:89)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC003_GetMID -->
  <testcase name="TC017_GetCommisonWithEmptySolutionSubType" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC017_GetCommisonWithEmptySolutionSubType -->
  <testcase name="TC009_StopMerchantReactivationWithEmptySolutionType" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC009_StopMerchantReactivationWithEmptySolutionType -->
  <testcase name="TC002_createMerchantOnPG" time="0.731" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation"/>
  <testcase name="TC006_CreateLeadMerchantReactivation" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC006_CreateLeadMerchantReactivation -->
  <testcase name="TC010_StopMerchantReactivationWithEmptyEntity" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC010_StopMerchantReactivationWithEmptyEntity -->
  <testcase name="TC007_StopMerchantReactivationWithEmptyMID" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC007_StopMerchantReactivationWithEmptyMID -->
  <testcase name="TC012_StopMerchantReactivationWithSameRequestAgain" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC012_StopMerchantReactivationWithSameRequestAgain -->
  <testcase name="TC008_StopMerchantReactivationWithEmptySolutionSubtype" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC008_StopMerchantReactivationWithEmptySolutionSubtype -->
  <testcase name="TC011_StopMerchantReactivationWithEmptySessionToken" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowMerchantReactivation">
    <skipped/>
  </testcase> <!-- TC011_StopMerchantReactivationWithEmptySessionToken -->
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowMerchantReactivation -->
