<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="34" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix" tests="41" failures="1" timestamp="2022-07-13T13:12:25 IST" time="5.404" errors="0">
  <testcase name="TC002_PLv3CLIX_DeleteExistingLead" time="0.621" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix"/>
  <testcase name="TC028_PLv3CLIX_BRE3Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC028_PLv3CLIX_BRE3Success -->
  <testcase name="TC003_PLv3CLIX_CreateLead" time="0.697" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix"/>
  <testcase name="TC011_PLv3CLIX_FetchCIR" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC011_PLv3CLIX_FetchCIR -->
  <testcase name="TC004_PLV3CLIX_FetchLeadAllData" time="0.597" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix"/>
  <testcase name="TC037_PLv3CLIX_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC037_PLv3CLIX_SubmitApplication -->
  <testcase name="TC015_PLv3CLIX_FetchDataPostLoanOfferAccept" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC015_PLv3CLIX_FetchDataPostLoanOfferAccept -->
  <testcase name="TC027_PLv3CLIX_AdditionalDataCapture" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC027_PLv3CLIX_AdditionalDataCapture -->
  <testcase name="TC019_PLv3CLIX_VerifyUploadedSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC019_PLv3CLIX_VerifyUploadedSelfie -->
  <testcase name="TC040_PLv3CLIX_FetchLeadPostPDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC040_PLv3CLIX_FetchLeadPostPDCCallback -->
  <testcase name="TC016_PLv3CLIX_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC016_PLv3CLIX_UploadCustomerPhoto -->
  <testcase name="TC023_PLv3CLIX_SecondBRECallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC023_PLv3CLIX_SecondBRECallback -->
  <testcase name="TC001_PLv3CLIX_fetchlLead" time="0.535" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix"/>
  <testcase name="C035_PLv3CLIX_GenerateLoanAgreement" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- C035_PLv3CLIX_GenerateLoanAgreement -->
  <testcase name="TC034_PLv3CLIX_FetchLeadPostEmandate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC034_PLv3CLIX_FetchLeadPostEmandate -->
  <testcase name="TC036_PLv3CLIX_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC036_PLv3CLIX_GenerateSanctionLetter -->
  <testcase name="TC009_PLV3CLIX_UpdateLeadDetailsinSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC009_PLV3CLIX_UpdateLeadDetailsinSAI -->
  <testcase name="TC022_PLv3CLIX_SecondBREInitiated" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC022_PLv3CLIX_SecondBREInitiated -->
  <testcase name="TC024_PLv3CLIX_FetchDataAfterBRE2Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC024_PLv3CLIX_FetchDataAfterBRE2Success -->
  <testcase name="TC021_PLv3CLIX_FetchDataPostPanVerified" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC021_PLv3CLIX_FetchDataPostPanVerified -->
  <testcase name="TC030_PLv3CLIX_FetchLeadUpdateCKYCinSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC030_PLv3CLIX_FetchLeadUpdateCKYCinSAI -->
  <testcase name="TC005_PLV3CLIX_UpdateLeadBasicDetails" time="1.828" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix"/>
  <testcase name="TC012_PLv3CLIX_BRE1Callback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC012_PLv3CLIX_BRE1Callback -->
  <testcase name="TC010_PLv3CLIX_FetchDataPostSAIlUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC010_PLv3CLIX_FetchDataPostSAIlUpdate -->
  <testcase name="TC038_PLv3CLIX_FetchLeadPostSubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC038_PLv3CLIX_FetchLeadPostSubmitApplication -->
  <testcase name="TC013_PLv3CLIX_FetchDataPostBRE1Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC013_PLv3CLIX_FetchDataPostBRE1Success -->
  <testcase name="TC029_PLv3CLIX_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC029_PLv3CLIX_UpdateKYCNameInSAI -->
  <testcase name="TC020_PLv3CLIX_CKYCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC020_PLv3CLIX_CKYCCallback -->
  <testcase name="TC007_PLV3CLIX_UpdateLeadOccupationDetails" time="0.559" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <failure type="java.lang.AssertionError" message="did not expect to find [OCCUPATION_DETAILS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [OCCUPATION_DETAILS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix.TC007_PLV3CLIX_UpdateLeadOccupationDetails(TestPersonalLoanV3Clix.java:412)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC007_PLV3CLIX_UpdateLeadOccupationDetails -->
  <testcase name="TC039_PLv3CLIX_PDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC039_PLv3CLIX_PDCCallback -->
  <testcase name="TC033_PLv3CLIX_EmandateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC033_PLv3CLIX_EmandateCallback -->
  <testcase name="TC006_PLv3CLIX_FetchDataPostBasicDetailUpdate" time="0.566" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix"/>
  <testcase name="TC018_PLv3CLIX_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC018_PLv3CLIX_UploadSelfie -->
  <testcase name="TC014_PLv3CLIX_LoanOfferAccept" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC014_PLv3CLIX_LoanOfferAccept -->
  <testcase name="TC017_PLv3CLIX_VerifyUploadedCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC017_PLv3CLIX_VerifyUploadedCustomerPhoto -->
  <testcase name="TC032_PLv3CLIX_FetchLeadPostBankVerification" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC032_PLv3CLIX_FetchLeadPostBankVerification -->
  <testcase name="TC025_PLv3CLIX_AdditionalIsRequiredorNot" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC025_PLv3CLIX_AdditionalIsRequiredorNot -->
  <testcase name="TC041PLv3CLIX_SubmitApplicationLMSApprovedCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC041PLv3CLIX_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC031_PLv3CLIX_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC031_PLv3CLIX_SaveBankDetails -->
  <testcase name="TC008_PLv3CLIX_FetchDataPostOccupationDetailUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC008_PLv3CLIX_FetchDataPostOccupationDetailUpdate -->
  <testcase name="TC026_PLv3CLIX_FetchLeadVerifyAdditionalData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix">
    <skipped/>
  </testcase> <!-- TC026_PLv3CLIX_FetchLeadVerifyAdditionalData -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanV3Clix -->
