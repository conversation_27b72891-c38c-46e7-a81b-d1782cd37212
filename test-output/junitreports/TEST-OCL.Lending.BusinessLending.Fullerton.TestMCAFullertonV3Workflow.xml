<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="0" tests="35" name="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" time="7.643" errors="1" timestamp="2023-09-25T18:35:48 IST" skipped="26">
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC032_VerifyPDCCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC032_VerifyPDCCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC004_FetchLeadAllData" time="0.725"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC011_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC009_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC027_GenerateLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_GenerateLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC036_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC036_FetchLeadAllData -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC024_UpdateKYCNameInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_UpdateKYCNameInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC021_FetchLead_AfterBRE2" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_FetchLead_AfterBRE2 -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC035_LMSDataCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC035_LMSDataCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC015_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_UploadSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC005_UpdateLeadBasicDetails" time="2.566"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC030_AcceptLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC030_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="test" time="0.204"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC034_VerifyLISCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC034_VerifyLISCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC003_CreateFullertonLead" time="1.661"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC007_UpdateBureauDataSetInSAI" time="0.303"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC016_VerifyUploadedSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_VerifyUploadedSelfie -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC031_EmandateCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC031_EmandateCallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC028_GenerateSanctionLetter" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_GenerateSanctionLetter -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC001_FetchLeadDeatils" time="0.529"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC019_MCA_BRE2Requested" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_MCA_BRE2Requested -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC033_UploadSheetONPanel" time="0.000">
    <skipped/>
  </testcase> <!-- TC033_UploadSheetONPanel -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC002_DeleteExistingLead" time="0.326"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC017_InitiateKYC_UsingSearchByPan" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC008_FetchCIR" time="0.414">
    <error message="Unmatched closing &#039;)&#039; near index 11
&lt;&#039; (code 60)): expected a valid value (number, String, array, object, &#039;true&#039;, &#039;false&#039; or &#039;null&#039;)\n at [Source: (String)\&quot;&lt;html&gt;\r\n&lt;head&gt;&lt;title&gt;502 Bad Gateway&lt;/title&gt;&lt;/head&gt;\r\n&lt;body&gt;\r\n&lt;center&gt;&lt;h1&gt;502 Bad Gateway&lt;/h1&gt;&lt;/center&gt;\r\n&lt;hr&gt;&lt;center&gt;nginx&lt;/center&gt;\r\n&lt;/body&gt;\r\n&lt;/html&gt;
           ^" type="java.util.regex.PatternSyntaxException">
      <![CDATA[java.util.regex.PatternSyntaxException: Unmatched closing ')' near index 11
<' (code 60)): expected a valid value (number, String, array, object, 'true', 'false' or 'null')\n at [Source: (String)\"<html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>
           ^
at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
at java.base/java.util.regex.Pattern.compile(Pattern.java:1787)
at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
at java.base/java.lang.String.replaceFirst(String.java:2896)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.LendingService.LendingBaseAPI.v3FetchCIR(LendingBaseAPI.java:3923)
at OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow.TC008_FetchCIR(TestMCAFullertonV3Workflow.java:516)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- TC008_FetchCIR -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC012_LeadDataUpdateForKYC_InSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC010_UpdateExistingDetailsInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_UpdateExistingDetailsInSAI -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC018_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC026_FetchLeadPostBankVerification" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_FetchLeadPostBankVerification -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC014_VerifyUploadedCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC006_FetchLeadAllData" time="0.915"/>
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC013_uploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC020_MCA_Fullerton_SecondBRECallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_MCA_Fullerton_SecondBRECallback -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC025_SaveBankDetails" time="0.000">
    <skipped/>
  </testcase> <!-- TC025_SaveBankDetails -->
  <testcase classname="OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow" name="TC029_UpdateActualPanInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC029_UpdateActualPanInSAI -->
</testsuite> <!-- OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow -->
