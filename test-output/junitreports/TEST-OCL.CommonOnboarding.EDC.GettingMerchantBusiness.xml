<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="8" name="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" time="34.910" errors="0" timestamp="2023-11-07T14:06:06 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="TC_1_getExistingMerchantBusiness" time="18.611"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="TC_4_ErrorWithoutcustidGetBusiness" time="2.269"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="TC_6_ErrorWithoutVersion" time="1.999"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="TC_5_ErrorWithoutSessionToken" time="2.314"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="test" time="0.501"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="TC_3_MerchantNobusiness" time="2.578"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="TC_7_ErrorWithoutDeviceIdentifer" time="3.433"/>
  <testcase classname="OCL.CommonOnboarding.EDC.GettingMerchantBusiness" name="TC_2_getMultipleExistingMerchantBusiness" time="3.205"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.GettingMerchantBusiness -->
