<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="32" hostname="197NODMB24984.local" name="OCL.Individual.Merchant100K.Flow100K" tests="88" failures="16" timestamp="2022-07-13T13:25:47 IST" time="39.590" errors="8">
  <testcase name="emptyMobileSendOTP" time="0.351" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.emptyMobileSendOTP(Flow100K.java:125)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- emptyMobileSendOTP -->
  <testcase name="mobileAsNullSendOTP" time="0.266" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="mobileNoValidationSendOTP" time="0.360" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.mobileNoValidationSendOTP(Flow100K.java:151)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- mobileNoValidationSendOTP -->
  <testcase name="invalidUserTypeSendOTP" time="0.338" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.invalidUserTypeSendOTP(Flow100K.java:164)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- invalidUserTypeSendOTP -->
  <testcase name="diffUserTypeSendOTP" time="0.339" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidEntityTypeSendOTP" time="0.289" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptyEntityTypeSendOTP" time="0.382" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="diffEntityTypeSendOTP" time="0.389" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidSolutionTypeSendOTP" time="0.428" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.invalidSolutionTypeSendOTP(Flow100K.java:228)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- invalidSolutionTypeSendOTP -->
  <testcase name="diffSolutionTypeSendOTP" time="0.355" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.diffSolutionTypeSendOTP(Flow100K.java:245)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- diffSolutionTypeSendOTP -->
  <testcase name="PositiveSendOtpSendOTP" time="0.360" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.PositiveSendOtpSendOTP(Flow100K.java:267)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- PositiveSendOtpSendOTP -->
  <testcase name="invalidEntityGetTnc" time="0.949" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="diffEntityGetTnc" time="0.904" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptyEntityGetTnc" time="1.001" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidSolutionGetTnc" time="1.005" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="diffSolutionGetTnc" time="1.025" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptySolutionGetTnc" time="0.895" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidTncSetGetTnc" time="0.878" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptyTncSetGetTnc" time="0.955" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="positiveGetTnc" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positiveGetTnc -->
  <testcase name="invalidOtpValidateOTP" time="0.332" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.PositiveSendOtpSendOTP(Flow100K.java:267)
at OCL.Individual.Merchant100K.Flow100K.invalidOtpValidateOTP(Flow100K.java:360)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- invalidOtpValidateOTP -->
  <testcase name="fourDigitOtpValidateOTP" time="0.098" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at Services.MechantService.MiddlewareServices.v3ValidateOtp(MiddlewareServices.java:198)
at OCL.Individual.Merchant100K.Flow100K.fourDigitOtpValidateOTP(Flow100K.java:374)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fourDigitOtpValidateOTP -->
  <testcase name="emptyOtpValidateOTP" time="0.097" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at Services.MechantService.MiddlewareServices.v3ValidateOtp(MiddlewareServices.java:198)
at OCL.Individual.Merchant100K.Flow100K.emptyOtpValidateOTP(Flow100K.java:386)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- emptyOtpValidateOTP -->
  <testcase name="PositiveValidateOtp" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveValidateOtp -->
  <testcase name="diffEntityTypesGetMerchStatus" time="0.804" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Individual.Merchant100K.Flow100K.diffEntityTypesGetMerchStatus(Flow100K.java:574)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- diffEntityTypesGetMerchStatus -->
  <testcase name="invalidEntityTypesGetMerchStatus" time="1.177" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Individual.Merchant100K.Flow100K.invalidEntityTypesGetMerchStatus(Flow100K.java:588)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- invalidEntityTypesGetMerchStatus -->
  <testcase name="emptyEntityTypesGetMerchStatus" time="0.887" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Individual.Merchant100K.Flow100K.emptyEntityTypesGetMerchStatus(Flow100K.java:600)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- emptyEntityTypesGetMerchStatus -->
  <testcase name="custIdNotInOauthGetMerchStatus" time="1.406" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="custIdLeadNotPresentGetMerchStatus" time="2.061" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidCustIdGetMerchStatus" time="0.929" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidSolutionTypeGetMerchStatus" time="0.801" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Individual.Merchant100K.Flow100K.invalidSolutionTypeGetMerchStatus(Flow100K.java:663)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- invalidSolutionTypeGetMerchStatus -->
  <testcase name="diffSolutionTypeGetMerchStatus" time="0.888" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Individual.Merchant100K.Flow100K.diffSolutionTypeGetMerchStatus(Flow100K.java:676)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- diffSolutionTypeGetMerchStatus -->
  <testcase name="emptySolutionTypeGetMerchStatus" time="0.924" classname="OCL.Individual.Merchant100K.Flow100K">
    <error type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.Individual.Merchant100K.Flow100K.emptySolutionTypeGetMerchStatus(Flow100K.java:690)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </error>
  </testcase> <!-- emptySolutionTypeGetMerchStatus -->
  <testcase name="PositiveGetMerchantStatus" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveGetMerchantStatus -->
  <testcase name="diffEntityTypeGetCat" time="0.440" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [404]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [404]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.Merchant100K.Flow100K.diffEntityTypeGetCat(Flow100K.java:714)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- diffEntityTypeGetCat -->
  <testcase name="invalidEntityTypeGetCat" time="0.652" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptyEntityTypeGetCat" time="0.653" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="diffSolutionTypeGetCat" time="0.610" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [404]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [404]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.Merchant100K.Flow100K.diffSolutionTypeGetCat(Flow100K.java:750)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- diffSolutionTypeGetCat -->
  <testcase name="emptySolutionTypeGetCat" time="0.809" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidSolutionTypeGetCat" time="0.462" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="PositiveGetCategoryGetCat" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveGetCategoryGetCat -->
  <testcase name="diffEntityTypeGetSubCat" time="0.384" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [404]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [404]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.Merchant100K.Flow100K.diffEntityTypeGetSubCat(Flow100K.java:800)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- diffEntityTypeGetSubCat -->
  <testcase name="invalidEntityTypeGetSubCat" time="0.515" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptyEntityTypeGetSubCat" time="0.822" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="diffSolutionTypeGetSubCat" time="0.732" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [404]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [404]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.Merchant100K.Flow100K.diffSolutionTypeGetSubCat(Flow100K.java:836)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- diffSolutionTypeGetSubCat -->
  <testcase name="invalidSolutionTypeGetSubCat" time="0.451" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptySolutionTypeGetSubCat" time="0.411" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="invalidCategoryIdGetSubCat" time="0.356" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="diffCategoryIdGetSubCat" time="0.437" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="nullCategoryIdGetSubCat" time="0.360" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="PositiveGetSubCategory" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveGetSubCategory -->
  <testcase name="positiveGetDocStatus" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positiveGetDocStatus -->
  <testcase name="invalidPinCOdeGetPincode" time="0.945" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="PinCodeNotExistGetPincode" time="1.180" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="positiveGetPincode" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positiveGetPincode -->
  <testcase name="positiveGetLanguage" time="0.001" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positiveGetLanguage -->
  <testcase name="invalidBank" time="1.117" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="IfscNotExistBank" time="1.118" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="emptyIfscBank" time="0.870" classname="OCL.Individual.Merchant100K.Flow100K"/>
  <testcase name="positiveBank" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positiveBank -->
  <testcase name="invalidBankPennyDrop" time="0.870" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.invalidBankPennyDrop(Flow100K.java:1059)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- invalidBankPennyDrop -->
  <testcase name="emptyBankPennyDrop" time="0.880" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.emptyBankPennyDrop(Flow100K.java:1074)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- emptyBankPennyDrop -->
  <testcase name="diffBankPennyDrop" time="0.847" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.diffBankPennyDrop(Flow100K.java:1089)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- diffBankPennyDrop -->
  <testcase name="removeBankPennyDrop" time="0.897" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.removeBankPennyDrop(Flow100K.java:1103)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- removeBankPennyDrop -->
  <testcase name="invalidIfscPennyDrop" time="0.897" classname="OCL.Individual.Merchant100K.Flow100K">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100K.invalidIfscPennyDrop(Flow100K.java:1118)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- invalidIfscPennyDrop -->
  <testcase name="positivePennyDrop" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positivePennyDrop -->
  <testcase name="positiveSubmitMerchant" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positiveSubmitMerchant -->
  <testcase name="PositiveFetchDocs100K" time="0.001" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveFetchDocs100K -->
  <testcase name="PositiveCreateUserWallet100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveCreateUserWallet100K -->
  <testcase name="PositiveGetOEPanelCookie" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveGetOEPanelCookie -->
  <testcase name="PositiveFetchLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveFetchLeadPanel -->
  <testcase name="ReallocatingAgent100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- ReallocatingAgent100K -->
  <testcase name="PositiveRejectedLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveRejectedLeadPanel -->
  <testcase name="PositiveGetMerchantStatusAfterRejection" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveGetMerchantStatusAfterRejection -->
  <testcase name="positiveSubmitMerchantAfterRejection" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- positiveSubmitMerchantAfterRejection -->
  <testcase name="PositiveSubmitLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PositiveSubmitLeadPanel -->
  <testcase name="PgCallBack100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- PgCallBack100K -->
  <testcase name="emptyEntityTypeValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- emptyEntityTypeValidateOTP -->
  <testcase name="invalidEntityTypeValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- invalidEntityTypeValidateOTP -->
  <testcase name="emptyUserTypeValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- emptyUserTypeValidateOTP -->
  <testcase name="emptyCustIdGetMerchStatus" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- emptyCustIdGetMerchStatus -->
  <testcase name="emptyPinCOdeGetPincode" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- emptyPinCOdeGetPincode -->
  <testcase name="diffEntityTypeValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- diffEntityTypeValidateOTP -->
  <testcase name="invalidUserTypeValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- invalidUserTypeValidateOTP -->
  <testcase name="invalidStateValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- invalidStateValidateOTP -->
  <testcase name="diffUserTypeValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- diffUserTypeValidateOTP -->
  <testcase name="emptyMobileValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- emptyMobileValidateOTP -->
  <testcase name="emptyStateValidateOTP" classname="OCL.Individual.Merchant100K.Flow100K">
    <skipped/>
  </testcase> <!-- emptyStateValidateOTP -->
</testsuite> <!-- OCL.Individual.Merchant100K.Flow100K -->
