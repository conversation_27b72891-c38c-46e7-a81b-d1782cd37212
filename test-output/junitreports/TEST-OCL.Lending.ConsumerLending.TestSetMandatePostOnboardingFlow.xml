<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow" tests="6" failures="0" timestamp="2022-07-13T13:12:25 IST" time="4.823" errors="0">
  <testcase name="TC005_PLSetMandate_PostOnBoarding_SaveBankDetails" time="1.759" classname="OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow"/>
  <testcase name="TC003_PLSetMandate_PostOnBoarding_CreateLead" time="0.801" classname="OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow"/>
  <testcase name="TC001_PLSetMandate_PostOnBoarding_fetchlLead" time="0.669" classname="OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow"/>
  <testcase name="TC002_PLSetMandate_PostOnBoarding_DeleteExistingLead" time="0.620" classname="OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow"/>
  <testcase name="TC006_PLSetMandate_PostOnBoarding_Emandatecallback" time="0.590" classname="OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow"/>
  <testcase name="TC004_PLSetMandate_PostOnBoarding_FetchLeadUpdateCKYCinSAI" time="0.384" classname="OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow"/>
</testsuite> <!-- OCL.Lending.ConsumerLending.TestSetMandatePostOnboardingFlow -->
