<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="2" hostname="197NODMB24984.local" name="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated" tests="75" failures="2" timestamp="2022-07-13T13:25:47 IST" time="102.446" errors="0">
  <testcase name="TC0004_06_CreateLeadInvalidEntity" time="0.351" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0010_PGCallBackforInsatntMid" time="68.471" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_06_FetchDetailsDifferentSolutionDiy" time="0.653" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_05_CreateLeadDifferentEntity" time="0.325" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_03_CreateLeadDifferentSolutionAssisted" time="0.334" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_10_CreateLeadUMO" time="0.708" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_05_FetchDetailsDifferentSolutionAssisted" time="0.343" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_09_UpdateIdentityEmptyNameOfPan" time="0.342" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_05_ValidateBankDetailsDifferentSolutionDiy" time="0.337" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0012_PgCallBackUMO" time="0.000" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated">
    <skipped/>
  </testcase> <!-- TC0012_PgCallBackUMO -->
  <testcase name="TC0005_01_createBusinessEmptyCategoryAndSubCategory" time="0.329" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_12_UpdateIdentity" time="0.426" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_13_CreateBusinessNoChannel" time="0.325" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_08_FetchDetailsInvalidChannel" time="0.390" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_04_UpdateIdentityWrongPan" time="0.374" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_10_ValidateBankDetailsNoEntity" time="0.351" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0008_01_CreateAdditionalDetailsWithoutLineAdd" time="0.355" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_04_ValidateBankDetailsInvalidSolution" time="0.308" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_03_UpdateIdentityNoPan" time="0.319" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0007_03_UpdateBankDetailsWrongAccountNo" time="0.692" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_11_CreateBusinessNoEntity" time="0.335" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_11_ValidateBankDetails" time="0.907" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0011_01_CreateAccountWithoutLead" time="0.297" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_01_ValidateBankDetailsInvalidAccountNumber" time="0.394" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_05_CreateBusinessInvalidSolution" time="0.327" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_05_UpdateIdentityDifferentEntityPan" time="0.405" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_10_FetchDetailsNoChannel" time="0.310" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0008_02_CreateAdditionalDetailsWithoutCity" time="0.582" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_04_FetchDetailsInvalidSolution" time="0.301" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0007_02_UpdateBankDetailsNoAccountNo" time="0.311" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_03_FetchDetailsNoEntity" time="0.454" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_01_CreateLeadInvalidSolution" time="0.363" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_01_UpdateIdentityWithoutLead" time="0.338" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_07_FetchDetailsNoSolution" time="0.376" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_14_CreateBusinessEmptyName" time="0.366" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_04_createBusinessNoCatAndSubCat" time="0.388" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0002_GetApplicantToken" time="1.226" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_02_UpdateIdentityWrongLead" time="0.403" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0007_04_UpdateBankDetailsWrongBankAccName" time="0.481" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_02_createBusinessEmptyCategory" time="0.354" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_06_UpdateIdentityWrongEntity" time="0.471" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0008_03_CreateAdditionalDetails" time="0.420" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_02_CreateLeadDifferentSolutionDiy" time="0.322" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_07_ValidateBankDetailsNoSolution" time="0.293" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_08_ValidateBankDetailsInvalidEntity" time="0.352" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_01_FetchDetailsInvalidEntity" time="0.437" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_11_UpdateIdentityWrongNameOfPan" time="0.848" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_08_CreateBusinessNoSolution" time="1.046" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_09_CreateBusinessInvalidEntity" time="0.491" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated">
    <failure type="java.lang.AssertionError" message="did not expect to find [We could not save your details. Please try again.] but found [We noticed a problem with your entity type, please reach out to Merchant help desk for assistance.]">
      <![CDATA[java.lang.AssertionError: did not expect to find [We could not save your details. Please try again.] but found [We noticed a problem with your entity type, please reach out to Merchant help desk for assistance.]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated.TC0005_09_CreateBusinessInvalidEntity(FlowUinifiedPaymentIntegrated.java:762)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0005_09_CreateBusinessInvalidEntity -->
  <testcase name="TC0004_09_CreateLeadNoChannel" time="0.330" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_08_CreateLeadInvalidChannel" time="0.313" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_08_UpdateIdentityNoEntity" time="0.381" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_10_CreateBusinessDifferentEntity" time="0.334" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_11_FetchDetails" time="0.473" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_02_FetchDetailsDifferentEntity" time="0.347" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0003_09_FetchDetailsDifferentChannel" time="0.659" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_07_CreateLeadNoEntity" time="0.300" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_03_ValidateBankDetailsNoIfscAndAccNo" time="0.369" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_15_CreateBusinessOneCharName" time="0.383" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0004_04_CreateLeadNoSolutionAssisted" time="0.391" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_06_ValidateBankDetailsDifferentSolutionAssisted" time="0.405" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0001_CreateApplicantOauth" time="1.644" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_07_CreateBusinessDifferentSolutionAssisted" time="0.340" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0011_02_CreateAccountWrongLead" time="0.335" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0007_05_UpdateBankDetails" time="0.511" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0007_01_UpdateBankDetailsWrongAccountNo" time="0.477" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_16_CreateBusiness" time="0.344" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_12_CreateBusinessInvalidChannel" time="0.378" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_07_UpdateIdentityEmptyEntity" time="0.611" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0005_06_CreateBusinessDifferentSolutionDiy" time="0.388" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0011_03_CreateAccount" time="1.396" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated.TC0011_03_CreateAccount(FlowUinifiedPaymentIntegrated.java:1856)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0011_03_CreateAccount -->
  <testcase name="TC0005_03_createBusinessEmptySubCategory" time="0.347" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_09_ValidateBankDetailsDifferentEntity" time="0.317" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0006_02_ValidateBankDetailsInvalidIfsc" time="1.342" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated"/>
  <testcase name="TC0009_10_UpdateIdentityNoNameOfPan" classname="OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated">
    <skipped/>
  </testcase> <!-- TC0009_10_UpdateIdentityNoNameOfPan -->
</testsuite> <!-- OCL.Individual.UnifiedPaymentMerchant.FlowUinifiedPaymentIntegrated -->
