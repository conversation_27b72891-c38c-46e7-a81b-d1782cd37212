<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB24984" failures="1" tests="23" name="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" time="17.474" errors="0" timestamp="2023-07-31T18:48:01 IST" skipped="15">
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC006_FetchCIR" time="1.304"/>
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC001_EMI_FetchLeadDeatils" time="0.380"/>
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC023_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_ThirdAttempt" time="0.000">
    <skipped/>
  </testcase> <!-- TC023_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_ThirdAttempt -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC005_UpdateBureauDataSetInSAI" time="0.243"/>
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC011_VerifyUploadedCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_VerifyUploadedCustomerPhoto -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC008_LoanOfferAccept" time="0.000">
    <skipped/>
  </testcase> <!-- TC008_LoanOfferAccept -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC004_FetchLeadAllData" time="4.862"/>
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC014_InitiateKYC_UsingSearchByPan" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_InitiateKYC_UsingSearchByPan -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC015_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC009_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC010_uploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_uploadCustomerPhoto -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC003_CreateEMILead" time="1.342"/>
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC007_FetchLeadAllData" time="8.952">
    <failure message="did not expect to find [BRE1_SUCCESS] but found [null]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [BRE1_SUCCESS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode.TC007_FetchLeadAllData(EMIUserOnboardingOAInvalidShareCode.java:493)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC007_FetchLeadAllData -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC019_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_FirstAttempt" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_FirstAttempt -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC020_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="test" time="0.110"/>
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC022_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC018_LeadDataUpdateForKYC_InSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_LeadDataUpdateForKYC_InSAI -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC021_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_SecondAttempt" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_SecondAttempt -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC012_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_UploadSelfie -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC024_FetchDataPostKYCIntiated" time="0.000">
    <skipped/>
  </testcase> <!-- TC024_FetchDataPostKYCIntiated -->
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC002_DeleteExistingLead" time="0.281"/>
  <testcase classname="OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode" name="TC013_VerifyUploadedSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_VerifyUploadedSelfie -->
</testsuite> <!-- OCL.Lending.EMI.EMIUserOnboardingOAInvalidShareCode -->
