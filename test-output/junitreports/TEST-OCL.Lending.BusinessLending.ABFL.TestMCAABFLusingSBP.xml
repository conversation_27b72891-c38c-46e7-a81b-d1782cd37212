<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="24" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP" tests="31" failures="1" timestamp="2023-07-03T18:33:08 IST" time="10.053" errors="0">
  <testcase name="TC017_FetchDataKYCInitiate" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC017_FetchDataKYCInitiate -->
  <testcase name="TC031_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC031_FetchLeadAllData -->
  <testcase name="TC010_LeadDataUpdateForKYC_InSAI" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC010_LeadDataUpdateForKYC_InSAI -->
  <testcase name="TC019_FetchLead_AfterBRE2" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC019_FetchLead_AfterBRE2 -->
  <testcase name="TC027_EmandateCallback" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC027_EmandateCallback -->
  <testcase name="TC030_LMSDataCallback" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC030_LMSDataCallback -->
  <testcase name="TC011_uploadCustomerPhoto" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC011_uploadCustomerPhoto -->
  <testcase name="TC025_AcceptLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC025_AcceptLoanAgreement -->
  <testcase name="TC013_UploadSelfie" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC013_UploadSelfie -->
  <testcase name="TC020_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC020_UpdateKYCNameInSAI -->
  <testcase name="TC009_FetchLeadAllData" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC009_FetchLeadAllData -->
  <testcase name="TC023_GenerateLoanAgreement" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC023_GenerateLoanAgreement -->
  <testcase name="TC029_VerifyLeadStage_LMSSubmitApplicationJob" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC029_VerifyLeadStage_LMSSubmitApplicationJob -->
  <testcase name="TC026_UpdateActualPanInSAI" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC026_UpdateActualPanInSAI -->
  <testcase name="TC028_FetchLeadPostEmandate" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC028_FetchLeadPostEmandate -->
  <testcase name="TC001_FetchLeadDetails_MCA_Piramal" time="0.970" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
  <testcase name="TC024_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC024_GenerateSanctionLetter -->
  <testcase name="TC006_FetchLeadAllData" time="1.824" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.676" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at Services.LendingService.LendingBaseAPI.verifyResponseCodeAs200OK(LendingBaseAPI.java:3453)
at OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP.TC007_UpdateBureauDataSetInSAI(TestMCAABFLusingSBP.java:484)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC007_UpdateBureauDataSetInSAI -->
  <testcase name="TC014_VerifyUploadedSelfie" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC014_VerifyUploadedSelfie -->
  <testcase name="TC016_FetchDataPostKYCIntiated" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC016_FetchDataPostKYCIntiated -->
  <testcase name="TC005_UpdateLeadBasicDetails" time="2.199" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
  <testcase name="TC003_Create_MCA_V3_ABFL_Lead" time="2.815" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
  <testcase name="TC012_VerifyUploadedCustomerPhoto" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC012_VerifyUploadedCustomerPhoto -->
  <testcase name="TC002_DeleteExistingLead" time="0.489" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
  <testcase name="TC018_SecondBRECallback" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC018_SecondBRECallback -->
  <testcase name="TC021_SaveBankDetails" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC021_SaveBankDetails -->
  <testcase name="TC015_InitiateKYC_UsingSearchByPan" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC015_InitiateKYC_UsingSearchByPan -->
  <testcase name="TC022_FetchLeadPostBankVerification" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadPostBankVerification -->
  <testcase name="TC004_FetchLeadAllData" time="1.080" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP"/>
  <testcase name="TC008_FetchCIR" time="0.000" classname="OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP">
    <skipped/>
  </testcase> <!-- TC008_FetchCIR -->
</testsuite> <!-- OCL.Lending.BusinessLending.ABFL.TestMCAABFLusingSBP -->
