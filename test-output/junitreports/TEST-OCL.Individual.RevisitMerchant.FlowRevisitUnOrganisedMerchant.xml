<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="31" hostname="197NODMB24984.local" name="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant" tests="36" failures="2" timestamp="2022-07-13T13:25:47 IST" time="4.645" errors="1">
  <testcase name="TC003_05_SubmitRevisitUnOrganisedEmptyCustId" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_05_SubmitRevisitUnOrganisedEmptyCustId -->
  <testcase name="TC002_03_FetchQnaRevisitUnOrganisedEmptySolution" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_03_FetchQnaRevisitUnOrganisedEmptySolution -->
  <testcase name="TC002_01_FetchQnaRevisitUnOrganisedWithoutSolution" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_01_FetchQnaRevisitUnOrganisedWithoutSolution -->
  <testcase name="TC002_15_FetchQnaRevisitUnOrganisedNoQuestionType" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_15_FetchQnaRevisitUnOrganisedNoQuestionType -->
  <testcase name="TC002_13_FetchQnaRevisitUnOrganisedInvalidQuestionType" time="0.001" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_13_FetchQnaRevisitUnOrganisedInvalidQuestionType -->
  <testcase name="TC003_01_SubmitRevisitUnOrganisedEmptyBody" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_01_SubmitRevisitUnOrganisedEmptyBody -->
  <testcase name="TC003_03_SubmitRevisitUnOrganisedNoCustId" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_03_SubmitRevisitUnOrganisedNoCustId -->
  <testcase name="TC003_04_SubmitRevisitUnOrganisedEmptyCustId" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_04_SubmitRevisitUnOrganisedEmptyCustId -->
  <testcase name="TC001_02_FetchShopForEmptyNumber" time="1.125" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant"/>
  <testcase name="TC002_19_FetchQnaRevisitUnOrganisedNoSolLvl3" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_19_FetchQnaRevisitUnOrganisedNoSolLvl3 -->
  <testcase name="TC003_10_SubmitRevisitUnOrganised" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_10_SubmitRevisitUnOrganised -->
  <testcase name="TC002_08_FetchQnaRevisitUnOrganisedDifferentEntity" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_08_FetchQnaRevisitUnOrganisedDifferentEntity -->
  <testcase name="TC005_PositiveFetchLeadPanelRevisitUnOrganised" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC005_PositiveFetchLeadPanelRevisitUnOrganised -->
  <testcase name="TC002_10_FetchQnaRevisitUnOrganisedEmptySubSol" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_10_FetchQnaRevisitUnOrganisedEmptySubSol -->
  <testcase name="TC002_11_FetchQnaRevisitUnOrganisedNoSubSol" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_11_FetchQnaRevisitUnOrganisedNoSubSol -->
  <testcase name="TC002_06_FetchQnaRevisitUnOrganisedEmptyEntity" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_06_FetchQnaRevisitUnOrganisedEmptyEntity -->
  <testcase name="TC003_06_SubmitRevisitUnOrganisedNoMobile" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_06_SubmitRevisitUnOrganisedNoMobile -->
  <testcase name="TC001_01_FetchShopForInvalidNumber" time="0.780" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant.TC001_01_FetchShopForInvalidNumber(FlowRevisitUnOrganisedMerchant.java:67)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC001_01_FetchShopForInvalidNumber -->
  <testcase name="TC001_10_FetchShopDetailsRevisitUnOrganised" time="0.674" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant.TC001_10_FetchShopDetailsRevisitUnOrganised(FlowRevisitUnOrganisedMerchant.java:109)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC001_10_FetchShopDetailsRevisitUnOrganised -->
  <testcase name="TC002_07_FetchQnaRevisitUnOrganisedNoEntity" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_07_FetchQnaRevisitUnOrganisedNoEntity -->
  <testcase name="TC002_14_FetchQnaRevisitUnOrganisedEmptyQuestionType" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_14_FetchQnaRevisitUnOrganisedEmptyQuestionType -->
  <testcase name="TC002_09_FetchQnaRevisitUnOrganisedInvalidSubSol" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_09_FetchQnaRevisitUnOrganisedInvalidSubSol -->
  <testcase name="TC001_03_FetchShopForNonMerchantMobile" time="0.681" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant.TC001_03_FetchShopForNonMerchantMobile(FlowRevisitUnOrganisedMerchant.java:93)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC001_03_FetchShopForNonMerchantMobile -->
  <testcase name="TC002_17_FetchQnaRevisitUnOrganisedEmptySolLvl3" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_17_FetchQnaRevisitUnOrganisedEmptySolLvl3 -->
  <testcase name="TC002_05_FetchQnaRevisitUnOrganisedInvalidEntity" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_05_FetchQnaRevisitUnOrganisedInvalidEntity -->
  <testcase name="TC002_20_FetchQnaRevisitUnOrganised" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_20_FetchQnaRevisitUnOrganised -->
  <testcase name="TC002_12_FetchQnaRevisitUnOrganisedDifferentSubSol" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_12_FetchQnaRevisitUnOrganisedDifferentSubSol -->
  <testcase name="TC002_18_FetchQnaRevisitUnOrganisedDifferentSolLvl3" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_18_FetchQnaRevisitUnOrganisedDifferentSolLvl3 -->
  <testcase name="TC003_02_SubmitRevisitUnOrganisedWrongBody" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_02_SubmitRevisitUnOrganisedWrongBody -->
  <testcase name="TC003_04_SubmitRevisitUnOrganisedInvalidCustId" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC003_04_SubmitRevisitUnOrganisedInvalidCustId -->
  <testcase name="AgentLoginMapPos" time="1.384" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant"/>
  <testcase name="TC002_04_FetchQnaRevisitUnOrganisedDifferentSolution" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_04_FetchQnaRevisitUnOrganisedDifferentSolution -->
  <testcase name="TC002_02_FetchQnaRevisitUnOrganisedInvalidSolution" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_02_FetchQnaRevisitUnOrganisedInvalidSolution -->
  <testcase name="TC004_FetchAndSubmitDocRevisitUnOrganised" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC004_FetchAndSubmitDocRevisitUnOrganised -->
  <testcase name="TC002_16_FetchQnaRevisitUnOrganisedInvalidSolLvl3" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC002_16_FetchQnaRevisitUnOrganisedInvalidSolLvl3 -->
  <testcase name="TC006_PositiveSubmitLeadPanelUnOrganised" time="0.000" classname="OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant">
    <skipped/>
  </testcase> <!-- TC006_PositiveSubmitLeadPanelUnOrganised -->
</testsuite> <!-- OCL.Individual.RevisitMerchant.FlowRevisitUnOrganisedMerchant -->
