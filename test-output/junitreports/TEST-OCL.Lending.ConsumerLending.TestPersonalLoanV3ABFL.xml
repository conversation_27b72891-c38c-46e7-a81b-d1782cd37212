<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="25" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL" tests="41" failures="1" timestamp="2022-07-18T18:00:37 IST" time="83.488" errors="0">
  <testcase name="TC012_PLv3ABFL_BRE1Callback" time="4.934" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC041PLv3ABFL_SubmitApplicationLMSApprovedCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC041PLv3ABFL_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC004_PLV3ABFL_FetchLeadAllData" time="0.787" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC031_PLv3ABFL_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC031_PLv3ABFL_SaveBankDetails -->
  <testcase name="TC002_PLv3ABFL_DeleteExistingLead" time="0.288" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC040_PLv3ABFL_FetchLeadPostPDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC040_PLv3ABFL_FetchLeadPostPDCCallback -->
  <testcase name="TC001_PLv3ABFL_fetchlLead" time="0.963" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC025_PLv3ABFL_AdditionalIsRequiredorNot" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC025_PLv3ABFL_AdditionalIsRequiredorNot -->
  <testcase name="TC019_PLv3ABFL_VerifyUploadedSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_VerifyUploadedSelfie -->
  <testcase name="TC036_PLv3ABFL_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC036_PLv3ABFL_GenerateSanctionLetter -->
  <testcase name="TC010_PLv3ABFL_FetchDataPostSAIlUpdate" time="0.531" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC032_PLv3ABFL_FetchLeadPostBankVerification" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC032_PLv3ABFL_FetchLeadPostBankVerification -->
  <testcase name="TC014_PLv3ABFL_LoanOfferAccept" time="0.561" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC021_PLv3ABFL_FetchDataPostPanVerified" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_FetchDataPostPanVerified -->
  <testcase name="TC037_PLv3ABFL_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC037_PLv3ABFL_SubmitApplication -->
  <testcase name="TC039_PLv3ABFL_PDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC039_PLv3ABFL_PDCCallback -->
  <testcase name="TC026_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_FetchLeadVerifyAdditionalData -->
  <testcase name="C035_PLv3ABFL_GenerateLoanAgreement" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- C035_PLv3ABFL_GenerateLoanAgreement -->
  <testcase name="TC007_PLV3ABFL_UpdateLeadOccupationDetails" time="0.665" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC034_PLv3ABFL_FetchLeadPostEmandate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC034_PLv3ABFL_FetchLeadPostEmandate -->
  <testcase name="TC015_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.564" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC023_PLv3ABFL_SecondBRECallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC023_PLv3ABFL_SecondBRECallback -->
  <testcase name="TC013_PLv3ABFL_FetchDataPostBRE1Success" time="0.261" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC028_PLv3ABFL_BRE3Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC028_PLv3ABFL_BRE3Success -->
  <testcase name="TC006_PLv3ABFL_FetchDataPostBasicDetailUpdate" time="0.541" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC009_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.341" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC022_PLv3ABFL_SecondBREInitiated" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_SecondBREInitiated -->
  <testcase name="TC033_PLv3ABFL_EmandateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC033_PLv3ABFL_EmandateCallback -->
  <testcase name="TC008_PLv3ABFL_FetchDataPostOccupationDetailUpdate" time="0.642" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC024_PLv3ABFL_FetchDataAfterBRE2Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC024_PLv3ABFL_FetchDataAfterBRE2Success -->
  <testcase name="TC038_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC038_PLv3ABFL_FetchLeadPostSubmitApplication -->
  <testcase name="TC020_PLv3ABFL_CKYCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_CKYCCallback -->
  <testcase name="TC003_PLv3ABFL_CreateLead" time="0.973" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC005_PLV3ABFL_UpdateLeadBasicDetails" time="1.456" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC011_PLv3ABFL_FetchCIR" time="7.153" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL"/>
  <testcase name="TC027_PLv3ABFL_AdditionalDataCapture" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC027_PLv3ABFL_AdditionalDataCapture -->
  <testcase name="TC029_PLv3ABFL_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC029_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase name="TC016_PLv3ABFL_UploadCustomerPhoto" time="62.827" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [504]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [504]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at Services.LendingService.LendingBaseAPI.verifyResponseCodeAs200OK(LendingBaseAPI.java:3408)
at OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL.TC016_PLv3ABFL_UploadCustomerPhoto(TestPersonalLoanV3ABFL.java:773)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC016_PLv3ABFL_UploadCustomerPhoto -->
  <testcase name="TC017_PLv3ABFL_VerifyUploadedCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC017_PLv3ABFL_VerifyUploadedCustomerPhoto -->
  <testcase name="TC030_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC030_PLv3ABFL_FetchLeadUpdateCKYCinSAI -->
  <testcase name="TC018_PLv3ABFL_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFL_UploadSelfie -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanV3ABFL -->
