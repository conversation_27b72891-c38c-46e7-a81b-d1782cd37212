<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.DIY.MapEdcDIY.FlowMapEdcDIY" tests="15" failures="2" timestamp="2022-07-13T12:28:08 IST" time="35.561" errors="5">
  <testcase name="fetchPlanforEDCDIY" time="0.600" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="fetchProductIdforEDCDIY" time="0.595" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchProductIdforEDCDIY(FlowMapEdcDIY.java:82)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchProductIdforEDCDIY -->
  <testcase name="fetchPriceforEDCDIY" time="0.572" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchPriceforEDCDIY(FlowMapEdcDIY.java:99)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchPriceforEDCDIY -->
  <testcase name="fetchEdcPlanIdforEDCDIY" time="0.373" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchEdcPlanIdforEDCDIY(FlowMapEdcDIY.java:116)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchEdcPlanIdforEDCDIY -->
  <testcase name="AcceptTermsAndConditionsEdcDIY" time="0.346" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="invalidPriceforEdcDiy" time="0.613" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="invalidPlanIdforEdcDiy" time="0.390" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="validateOrderforEdcDiy" time="0.406" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="createAccessToken" time="0.441" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="createQRforEdcDiy" time="5.225" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="fetchPaymentStatusforEdcDiy" time="0.755" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="CreateLeadEdcDiy" time="0.512" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.CreateLeadEdcDiy(FlowMapEdcDIY.java:273)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- CreateLeadEdcDiy -->
  <testcase name="CallbackinprogressEdcDiy" time="0.529" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.CallbackinprogressEdcDiy(FlowMapEdcDIY.java:303)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- CallbackinprogressEdcDiy -->
  <testcase name="fetchLeadEdcDiy" time="2.314" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchLeadEdcDiy(FlowMapEdcDIY.java:317)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchLeadEdcDiy -->
  <testcase name="fetchStatusLeadEdcDiy" time="21.890" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchStatusLeadEdcDiy(FlowMapEdcDIY.java:329)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- fetchStatusLeadEdcDiy -->
</testsuite> <!-- OCL.DIY.MapEdcDIY.FlowMapEdcDIY -->
