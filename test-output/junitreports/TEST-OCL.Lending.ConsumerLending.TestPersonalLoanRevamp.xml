<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="42" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp" tests="44" failures="0" timestamp="2022-06-29T15:42:27 IST" time="0.318" errors="2">
  <testcase name="TC038_EmandateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC038_EmandateCallback -->
  <testcase name="TC037_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC037_VerifyLeadStage -->
  <testcase name="TC009_BasicDetailsUpdateCallback" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC009_BasicDetailsUpdateCallback -->
  <testcase name="TC025_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC025_UploadSelfie -->
  <testcase name="TC023_CheckCKYCStatus" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC023_CheckCKYCStatus -->
  <testcase name="TC002_FetchLeadDeatils" time="0.179" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.paytm.apitools.customreporter.CustomReporter.onTestSkipped(CustomReporter.java:93)
at org.testng.internal.TestListenerHelper.runTestListeners(TestListenerHelper.java:57)
at org.testng.internal.Invoker.runTestResultListener(Invoker.java:1516)
at org.testng.internal.Invoker.invokeListenersForSkippedTestResult(Invoker.java:1177)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:594)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC002_FetchLeadDeatils -->
  <testcase name="TC031_SecondBREInitiateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC031_SecondBREInitiateCallback -->
  <testcase name="TC010_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC010_FetchTheCreatedLeadDeatils -->
  <testcase name="TC022_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC022_VerifyLeadStage -->
  <testcase name="TC030_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC030_VerifyLeadStage -->
  <testcase name="TC007_AdiitionalInfoPANUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC007_AdiitionalInfoPANUpdate -->
  <testcase name="TC041_FetchDynamicTncSanctionLetter" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC041_FetchDynamicTncSanctionLetter -->
  <testcase name="TC008_AdiitionalInfoDOBUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC008_AdiitionalInfoDOBUpdate -->
  <testcase name="TC014_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC014_VerifyLeadStage -->
  <testcase name="TC005_PPBLOTPCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC005_PPBLOTPCallback -->
  <testcase name="TC029_CKYCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC029_CKYCCallback -->
  <testcase name="TC017_FetchBREResponse" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC017_FetchBREResponse -->
  <testcase name="TC045_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC045_VerifyLeadStage -->
  <testcase name="TC019_CheckBREResponse" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC019_CheckBREResponse -->
  <testcase name="TC028_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC028_VerifyUploadedDocument -->
  <testcase name="TC032_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC032_VerifyLeadStage -->
  <testcase name="TC024_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC024_VerifyLeadStage -->
  <testcase name="TC016_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC016_VerifyLeadStage -->
  <testcase name="TC039_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC039_VerifyLeadStage -->
  <testcase name="TC044_UploadSheetONPanel" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC044_UploadSheetONPanel -->
  <testcase name="TC012_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC012_FetchTheCreatedLeadDeatils -->
  <testcase name="TC036_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC036_SaveBankDetails -->
  <testcase name="TC042_SubmitApplication" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC042_SubmitApplication -->
  <testcase name="TC026_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC026_VerifyUploadedDocument -->
  <testcase name="TC011_OccupationDetailsCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC011_OccupationDetailsCallback -->
  <testcase name="TC013_BREOTPVerification" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC013_BREOTPVerification -->
  <testcase name="TC015_BREValidationPending" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC015_BREValidationPending -->
  <testcase name="TC020_VerifyLeadStage" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC020_VerifyLeadStage -->
  <testcase name="TC034_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC034_VerifyLeadStage -->
  <testcase name="TC006_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC006_FetchTheCreatedLeadDeatils -->
  <testcase name="TC040_FetchDynamicTnc" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC040_FetchDynamicTnc -->
  <testcase name="TC035_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC035_UpdateKYCNameInSAI -->
  <testcase name="TC003_CreatePersonalLoanLead" time="0.133" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.paytm.apitools.customreporter.CustomReporter.onTestSkipped(CustomReporter.java:93)
at org.testng.internal.TestListenerHelper.runTestListeners(TestListenerHelper.java:57)
at org.testng.internal.Invoker.runTestResultListener(Invoker.java:1516)
at org.testng.internal.Invoker.invokeListenersForSkippedTestResult(Invoker.java:1177)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:594)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC003_CreatePersonalLoanLead -->
  <testcase name="TC033_SecondBREStatusCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC033_SecondBREStatusCallback -->
  <testcase name="TC027_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC027_UploadCustomerPhoto -->
  <testcase name="TC021_AcceptLoanOffer" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC021_AcceptLoanOffer -->
  <testcase name="TC043_VerifyLeadStage" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC043_VerifyLeadStage -->
  <testcase name="TC004_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC004_FetchTheCreatedLeadDeatils -->
  <testcase name="TC018_VerifyLeadStage" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanRevamp">
    <skipped/>
  </testcase> <!-- TC018_VerifyLeadStage -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanRevamp -->
