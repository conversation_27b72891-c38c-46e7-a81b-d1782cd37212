<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="8" name="OCL.CommonOnboarding.EDC.ConfirmPlan15" time="106.376" errors="1" timestamp="2023-11-07T18:15:51 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="TC_2_ConfirmDevicePlanWithoutSessiontoken" time="18.229"/>
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="TC_6_ConfirmDevicePlanWithoutLeadID" time="15.871"/>
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="TC_7_ConfirmDevicePlanWithInvalidLeadID" time="17.592"/>
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="TC_3_ConfirmDevicePlanWithoutDeviceIdentifer" time="1.992">
    <error message="Cannot invoke &quot;Object.toString()&quot; because the return value of &quot;java.util.List.get(int)&quot; is null" type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.List.get(int)" is null
at OCL.CommonOnboarding.EDC.AddBank8.TC_5_addBank(AddBank8.java:91)
at OCL.CommonOnboarding.EDC.CreateDeviceOnboardingLead13.CreateDeviceOnboardingLead(CreateDeviceOnboardingLead13.java:76)
at OCL.CommonOnboarding.EDC.ConfirmPlan15.TC_3_ConfirmDevicePlanWithoutDeviceIdentifer(ConfirmPlan15.java:249)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:78)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:567)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- TC_3_ConfirmDevicePlanWithoutDeviceIdentifer -->
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="TC_4_ConfirmDevicePlanWithoutVersion" time="15.408"/>
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="test" time="0.442"/>
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="TC_5_ConfirmDevicePlanWithInvalidLeadID" time="18.350"/>
  <testcase classname="OCL.CommonOnboarding.EDC.ConfirmPlan15" name="TC_1_ConfirmDevicePlan" time="18.492"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.ConfirmPlan15 -->
