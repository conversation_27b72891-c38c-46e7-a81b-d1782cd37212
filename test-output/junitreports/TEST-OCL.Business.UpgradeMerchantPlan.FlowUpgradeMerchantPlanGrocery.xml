<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="7" hostname="197NODMB24984.local" name="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery" tests="10" failures="0" timestamp="2022-07-13T12:28:08 IST" time="2.117" errors="1">
  <testcase name="TC002_UpgradeMerchantPositiveGetBusiness" time="0.661" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery"/>
  <testcase name="TC003_UpgradeMerchantPositiveGetBusinessProfile" time="0.409" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery.TC003_UpgradeMerchantPositiveGetBusinessProfile(FlowUpgradeMerchantPlanGrocery.java:128)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC003_UpgradeMerchantPositiveGetBusinessProfile -->
  <testcase name="TC008_UpgradeMerchantPositiveValidateOtpCreate" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC008_UpgradeMerchantPositiveValidateOtpCreate -->
  <testcase name="TC006_UpgradeMerchantPositiveFetchTnC" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC006_UpgradeMerchantPositiveFetchTnC -->
  <testcase name="TC005_UpgradeMerchantPositiveFetchUpgradePlans" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC005_UpgradeMerchantPositiveFetchUpgradePlans -->
  <testcase name="TC009_UpgradeMerchantPositiveFetchDocs" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC009_UpgradeMerchantPositiveFetchDocs -->
  <testcase name="TC004_UpgradeMerchantPositiveFetchMID" time="0.001" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC004_UpgradeMerchantPositiveFetchMID -->
  <testcase name="TC0010_UpgradeMerchantPositivePanelSubmit" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC0010_UpgradeMerchantPositivePanelSubmit -->
  <testcase name="TC007_UpgradeMerchantPositiveSendOtpCreate" time="0.000" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC007_UpgradeMerchantPositiveSendOtpCreate -->
  <testcase name="TC001_UpgradeMerchantPositiveSendOtpBusiness" time="1.046" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery"/>
</testsuite> <!-- OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery -->
