<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="1" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate" tests="35" failures="3" timestamp="2022-07-13T13:25:47 IST" time="35.420" errors="1">
  <testcase name="TC006_02_FetchLeadStatusEmptySol" time="0.597" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_11_CreateLeadInstrumentUpdateDiffEntityPan" time="1.424" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC004_PgCallback" time="10.313" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_12_FetchLeadStatusInvalidInstrument" time="0.322" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_05_CreateLeadInstrumentUpdateNoInstrument" time="0.358" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC008_PgCallbackInstrumentUpdate" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate">
    <skipped/>
  </testcase> <!-- TC008_PgCallbackInstrumentUpdate -->
  <testcase name="TC007_13_CreateLeadInstrumentUpdateNoPan" time="0.312" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_13_FetchLeadStatusEmptyInstrument" time="0.375" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_01_FetchLeadStatusInvalidSol" time="0.482" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_04_CreateLeadInstrumentUpdateEmptyInstrument" time="0.300" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_08_FetchLeadStatusEmptySolSub" time="0.359" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_09_FetchLeadStatusDifferentSolSub" time="0.310" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_08_CreateLeadInstrumentUpdateEmptyBankName" time="0.382" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_06_FetchLeadStatusDifferentEntity" time="0.628" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_15_FetchLeadStatusInstrumentUpdate" time="0.342" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_09_CreateLeadInstrumentUpdateNoBankName" time="0.372" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_14_FetchLeadStatusDifferentInstrument" time="0.289" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC001_CreateApplicantOauth" time="4.227" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC002_createMerchantKyb" time="3.092" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC003_createMerchantOnPG" time="0.415" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_11_FetchLeadStatusEmptydMid" time="0.492" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_03_FetchLeadStatusDifferentSol" time="0.386" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_10_FetchLeadStatusInvalidMid" time="0.330" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_01_CreateLeadInstrumentUpdateEmptyMid" time="0.592" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_14_CreateLeadInstrumentUpdate" time="3.011" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowInstrumentUpdate.TC007_14_CreateLeadInstrumentUpdate(FlowInstrumentUpdate.java:1048)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC007_14_CreateLeadInstrumentUpdate -->
  <testcase name="TC006_04_FetchLeadStatusInvalidEntity" time="0.312" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowInstrumentUpdate.TC006_04_FetchLeadStatusInvalidEntity(FlowInstrumentUpdate.java:255)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC006_04_FetchLeadStatusInvalidEntity -->
  <testcase name="TC007_10_CreateLeadInstrumentUpdateInvalidPan" time="1.386" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_02_CreateLeadInstrumentUpdateNoMid" time="0.287" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_07_CreateLeadInstrumentUpdateDifferentInstrument" time="1.305" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_06_CreateLeadInstrumentUpdateInvalidInstrument" time="0.328" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC006_05_FetchLeadStatusEmptyEntity" time="0.336" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.ProfileUpdate.FlowInstrumentUpdate.TC006_05_FetchLeadStatusEmptyEntity(FlowInstrumentUpdate.java:277)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC006_05_FetchLeadStatusEmptyEntity -->
  <testcase name="TC007_12_CreateLeadInstrumentUpdateEmptyPan" time="0.307" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC005_EditMerchantKyb" time="0.089" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.KYB.KybServices.KybEdit(KybServices.java:41)
at OCL.Individual.ProfileUpdate.FlowInstrumentUpdate.TC005_EditMerchantKyb(FlowInstrumentUpdate.java:170)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC005_EditMerchantKyb -->
  <testcase name="TC006_07_FetchLeadStatusInvalidSolSub" time="0.315" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
  <testcase name="TC007_03_CreateLeadInstrumentUpdateWrongMid" time="1.045" classname="OCL.Individual.ProfileUpdate.FlowInstrumentUpdate"/>
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowInstrumentUpdate -->
