<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="16" hostname="197NODMB24984.local" name="OCL.Individual.Merchant100K.Flow100KTransportCategory" tests="17" failures="1" timestamp="2022-07-13T13:25:47 IST" time="0.427" errors="0">
  <testcase name="TC014_PositiveGetMerchantStatusAfterRejection" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC014_PositiveGetMerchantStatusAfterRejection -->
  <testcase name="TC006_positivePennyDrop" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC006_positivePennyDrop -->
  <testcase name="TC002_positiveGetTnc" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC002_positiveGetTnc -->
  <testcase name="TC012_ReallocatingAgent100KTransport" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC012_ReallocatingAgent100KTransport -->
  <testcase name="TC017_PgCallBack100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC017_PgCallBack100K -->
  <testcase name="TC010_PositiveGetOEPanelCookie" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC010_PositiveGetOEPanelCookie -->
  <testcase name="TC016_PositiveSubmitLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC016_PositiveSubmitLeadPanel -->
  <testcase name="TC009_PositiveCreateUserWallet100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC009_PositiveCreateUserWallet100K -->
  <testcase name="TC004_PositiveGetMerchantStatus" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC004_PositiveGetMerchantStatus -->
  <testcase name="TC007_positiveSubmitMerchant" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC007_positiveSubmitMerchant -->
  <testcase name="TC015_positiveSubmitMerchantAfterRejection" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC015_positiveSubmitMerchantAfterRejection -->
  <testcase name="TC008_PositiveFetchDocs100K" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC008_PositiveFetchDocs100K -->
  <testcase name="TC001_PositiveSendOtpSendOTP" time="0.427" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <failure type="java.lang.AssertionError" message="did not expect to find [true] but found [false]">
      <![CDATA[java.lang.AssertionError: did not expect to find [true] but found [false]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertTrue(Assert.java:43)
at org.testng.Assert.assertTrue(Assert.java:53)
at OCL.Individual.Merchant100K.Flow100KTransportCategory.TC001_PositiveSendOtpSendOTP(Flow100KTransportCategory.java:119)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC001_PositiveSendOtpSendOTP -->
  <testcase name="TC005_positiveGetDocStatus" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC005_positiveGetDocStatus -->
  <testcase name="TC003_PositiveValidateOtp" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC003_PositiveValidateOtp -->
  <testcase name="TC011_PositiveFetchLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC011_PositiveFetchLeadPanel -->
  <testcase name="TC013_PositiveRejectedLeadPanel" time="0.000" classname="OCL.Individual.Merchant100K.Flow100KTransportCategory">
    <skipped/>
  </testcase> <!-- TC013_PositiveRejectedLeadPanel -->
</testsuite> <!-- OCL.Individual.Merchant100K.Flow100KTransportCategory -->
