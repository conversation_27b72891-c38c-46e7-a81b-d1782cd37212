<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="ats.Barcode.BarcodeGenerateTest" tests="25" failures="0" timestamp="2022-07-13T13:28:49 IST" time="8.178" errors="25">
  <testcase name="generateIndividualBarcodeWithAllMandatoryAttributes" time="0.595" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithAllMandatoryAttributes(BarcodeGenerateTest.java:39)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithAllMandatoryAttributes -->
  <testcase name="generateIndividualBarcodeWithoutSupplierAttributes" time="0.305" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithoutSupplierAttributes(BarcodeGenerateTest.java:63)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithoutSupplierAttributes -->
  <testcase name="generateIndividualBarcodeWithoutSKUID" time="0.312" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithoutSKUID(BarcodeGenerateTest.java:88)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithoutSKUID -->
  <testcase name="generateIndividualBarcodeWithoutMandatoryAttributes" time="0.367" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithoutMandatoryAttributes(BarcodeGenerateTest.java:113)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithoutMandatoryAttributes -->
  <testcase name="generateIndividualBarcodeWithBarcodeCountZero" time="0.259" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithBarcodeCountZero(BarcodeGenerateTest.java:138)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithBarcodeCountZero -->
  <testcase name="generateIndividualBarcodeWithBarcodeCount_200" time="0.466" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithBarcodeCount_200(BarcodeGenerateTest.java:163)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithBarcodeCount_200 -->
  <testcase name="generateIndividualBarcodeWithBarcodeCount_Above200" time="0.270" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithBarcodeCount_Above200(BarcodeGenerateTest.java:188)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithBarcodeCount_Above200 -->
  <testcase name="generateChildBarcode" time="0.363" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcode(BarcodeGenerateTest.java:215)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcode -->
  <testcase name="generateChildBarcodeWhenMappingIsNotDone" time="0.366" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWhenMappingIsNotDone(BarcodeGenerateTest.java:242)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWhenMappingIsNotDone -->
  <testcase name="generateChildBarcodeWhenInputChildBarcode" time="0.289" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWhenInputChildBarcode(BarcodeGenerateTest.java:267)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWhenInputChildBarcode -->
  <testcase name="generateChildBarcodeWhenEnterChildSkuID" time="0.267" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWhenEnterChildSkuID(BarcodeGenerateTest.java:292)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWhenEnterChildSkuID -->
  <testcase name="generateChildBarcodeWithInvalidSkuID" time="0.270" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWithInvalidSkuID(BarcodeGenerateTest.java:318)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWithInvalidSkuID -->
  <testcase name="generateChildBarcodeWithAllMandatoryAttributes" time="0.277" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWithAllMandatoryAttributes(BarcodeGenerateTest.java:343)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWithAllMandatoryAttributes -->
  <testcase name="generateChildBarcodeWhichAlreadyGenerated" time="0.261" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWhichAlreadyGenerated(BarcodeGenerateTest.java:368)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWhichAlreadyGenerated -->
  <testcase name="generateIndividualBarcodeWithoutAcceptParam" time="0.271" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithoutAcceptParam(BarcodeGenerateTest.java:393)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithoutAcceptParam -->
  <testcase name="generateIndividualBarcodeWithoutAcceptlanguage" time="0.485" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithoutAcceptlanguage(BarcodeGenerateTest.java:417)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithoutAcceptlanguage -->
  <testcase name="generateIndividualBarcodeWithoutContentType" time="0.468" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithoutContentType(BarcodeGenerateTest.java:441)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithoutContentType -->
  <testcase name="generateIndividualBarcodeWithNegativeBarcodeCount" time="0.307" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithNegativeBarcodeCount(BarcodeGenerateTest.java:465)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithNegativeBarcodeCount -->
  <testcase name="generateIndividualBarcodeWithAplabetsInBarcodeCount" time="0.250" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithAplabetsInBarcodeCount(BarcodeGenerateTest.java:489)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithAplabetsInBarcodeCount -->
  <testcase name="generateIndividualBarcodeWithoutAnyAttribute" time="0.280" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithoutAnyAttribute(BarcodeGenerateTest.java:513)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithoutAnyAttribute -->
  <testcase name="generateIndividualBarcodeWithInvalidSupplierName" time="0.347" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithInvalidSupplierName(BarcodeGenerateTest.java:537)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithInvalidSupplierName -->
  <testcase name="generateIndividualBarcodeWithInvalidSKUId" time="0.249" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1IndividualBarcodeGenerate(MiddlewareServices.java:4501)
at ats.Barcode.BarcodeGenerateTest.generateIndividualBarcodeWithInvalidSKUId(BarcodeGenerateTest.java:560)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateIndividualBarcodeWithInvalidSKUId -->
  <testcase name="generateChildBarcodeWithNoAttributes" time="0.255" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWithNoAttributes(BarcodeGenerateTest.java:584)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWithNoAttributes -->
  <testcase name="generateChildBarcodeWithMissingSkuId" time="0.317" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWithMissingSkuId(BarcodeGenerateTest.java:609)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWithMissingSkuId -->
  <testcase name="generateChildBarcodeWithMissingBarcode" time="0.282" classname="ats.Barcode.BarcodeGenerateTest">
    <error type="java.util.regex.PatternSyntaxException" message="Illegal repetition near index 35
&lt;/title&gt;&lt;style type=&quot;text/css&quot;&gt;body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}&lt;/style&gt;&lt;/head&gt;&lt;body&gt;&lt;h1&gt;
                                   ^">
      <![CDATA[java.util.regex.PatternSyntaxException: Illegal repetition near index 35
</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>
                                   ^
at java.util.regex.Pattern.error(Pattern.java:1969)
at java.util.regex.Pattern.closure(Pattern.java:3171)
at java.util.regex.Pattern.sequence(Pattern.java:2148)
at java.util.regex.Pattern.expr(Pattern.java:2010)
at java.util.regex.Pattern.compile(Pattern.java:1702)
at java.util.regex.Pattern.<init>(Pattern.java:1352)
at java.util.regex.Pattern.compile(Pattern.java:1028)
at java.lang.String.replaceFirst(String.java:2178)
at com.paytm.apitools.util.LogPublisher.truncateLargerResponse(LogPublisher.java:123)
at com.paytm.apitools.util.LogPublisher.logResponse(LogPublisher.java:104)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:225)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.v1ChildBarcodeGenerate(MiddlewareServices.java:4526)
at ats.Barcode.BarcodeGenerateTest.generateChildBarcodeWithMissingBarcode(BarcodeGenerateTest.java:633)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- generateChildBarcodeWithMissingBarcode -->
</testsuite> <!-- ats.Barcode.BarcodeGenerateTest -->
