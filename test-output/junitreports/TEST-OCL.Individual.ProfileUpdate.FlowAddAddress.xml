<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="15" hostname="197NODMB24984.local" name="OCL.Individual.ProfileUpdate.FlowAddAddress" tests="20" failures="0" timestamp="2022-07-13T13:25:47 IST" time="15.337" errors="1">
  <testcase name="TC008_04_CreateLeadAddAddressEmptyAddress" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_04_CreateLeadAddAddressEmptyAddress -->
  <testcase name="TC008_06_CreateLeadAddAddressNullMid" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_06_CreateLeadAddAddressNullMid -->
  <testcase name="TC007_02_FetchLeadStatusAddAddressWrongToken" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC007_02_FetchLeadStatusAddAddressWrongToken -->
  <testcase name="TC003_createMerchantOnPG" time="0.392" classname="OCL.Individual.ProfileUpdate.FlowAddAddress"/>
  <testcase name="TC008_08_CreateLeadAddAddress" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_08_CreateLeadAddAddress -->
  <testcase name="TC007_05_FetchLeadStatusAddAddress" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC007_05_FetchLeadStatusAddAddress -->
  <testcase name="TC008_03_CreateLeadAddAddressEmptyKybId" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_03_CreateLeadAddAddressEmptyKybId -->
  <testcase name="TC007_03_FetchLeadStatusAddAddressEmptyMid" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC007_03_FetchLeadStatusAddAddressEmptyMid -->
  <testcase name="TC007_04_FetchLeadStatusAddAddressEmptyToken" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC007_04_FetchLeadStatusAddAddressEmptyToken -->
  <testcase name="TC008_01_CreateLeadAddAddressEmptyMid" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_01_CreateLeadAddAddressEmptyMid -->
  <testcase name="TC008_07_CreateLeadAddAddressNullAddress" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_07_CreateLeadAddAddressNullAddress -->
  <testcase name="TC008_05_CreateLeadAddAddressNullKybId" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_05_CreateLeadAddAddressNullKybId -->
  <testcase name="TC001_CreateApplicantOauth" time="1.864" classname="OCL.Individual.ProfileUpdate.FlowAddAddress"/>
  <testcase name="TC008_02_CreateLeadAddAddressWrongMid" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC008_02_CreateLeadAddAddressWrongMid -->
  <testcase name="TC009_PgCallbackAddAddress" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC009_PgCallbackAddAddress -->
  <testcase name="TC006_GetSessionToken" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC006_GetSessionToken -->
  <testcase name="TC007_01_FetchLeadStatusAddAddressWrongMid" time="0.000" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <skipped/>
  </testcase> <!-- TC007_01_FetchLeadStatusAddAddressWrongMid -->
  <testcase name="TC002_createMerchantKyb" time="2.954" classname="OCL.Individual.ProfileUpdate.FlowAddAddress"/>
  <testcase name="TC004_PgCallback" time="10.041" classname="OCL.Individual.ProfileUpdate.FlowAddAddress"/>
  <testcase name="TC005_EditMerchantKyb" time="0.086" classname="OCL.Individual.ProfileUpdate.FlowAddAddress">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.KYB.KybServices.KybEdit(KybServices.java:41)
at OCL.Individual.ProfileUpdate.FlowAddAddress.TC005_EditMerchantKyb(FlowAddAddress.java:162)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC005_EditMerchantKyb -->
</testsuite> <!-- OCL.Individual.ProfileUpdate.FlowAddAddress -->
