<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB28593.local" failures="2" tests="27" name="OCL.Lending.ConsumerLending.TestPostpaidV3OA" time="34.105" errors="0" timestamp="2022-07-12T16:56:21 IST" skipped="20">
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC011_FetchLeadAllData" time="0.001">
    <skipped/>
  </testcase> <!-- TC011_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC021_SelfieMatch" time="0.000">
    <skipped/>
  </testcase> <!-- TC021_SelfieMatch -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC027_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC027_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC003_CreatePostpaidLead" time="2.256"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC025_FetchDynamicTnc" time="0.001">
    <skipped/>
  </testcase> <!-- TC025_FetchDynamicTnc -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC007_UpdateLeadBasicDetails" time="21.218">
    <failure message="did not expect to find [BASIC_DETAILS] but found [PAN_DEDUPE_FAILED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [BASIC_DETAILS] but found [PAN_DEDUPE_FAILED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPostpaidV3OA.TC007_UpdateLeadBasicDetails(TestPostpaidV3OA.java:308)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC007_UpdateLeadBasicDetails -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC019_UploadCustomerPhoto" time="0.000">
    <skipped/>
  </testcase> <!-- TC019_UploadCustomerPhoto -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC020_VerifyUploadedDocument" time="0.000">
    <skipped/>
  </testcase> <!-- TC020_VerifyUploadedDocument -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC002_DeleteExistingLead" time="1.769"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC017_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC017_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC022_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC022_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC012_AcceptOffer" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_AcceptOffer -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC016_OfflineAadharValidationSuccess" time="0.000">
    <skipped/>
  </testcase> <!-- TC016_OfflineAadharValidationSuccess -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC011_BRE1Callback" time="0.000">
    <skipped/>
  </testcase> <!-- TC011_BRE1Callback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC009_UpdateBureauDataSetInSAI" time="2.756">
    <failure message="did not expect to find [BASIC_DETAILS] but found [PAN_DEDUPE_FAILED]" type="java.lang.AssertionError">
      <![CDATA[java.lang.AssertionError: did not expect to find [BASIC_DETAILS] but found [PAN_DEDUPE_FAILED]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPostpaidV3OA.TC009_UpdateBureauDataSetInSAI(TestPostpaidV3OA.java:384)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC009_UpdateBureauDataSetInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC008_FetchLeadAllData" time="1.686"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC023_OfferStage2Callback" time="0.001">
    <skipped/>
  </testcase> <!-- TC023_OfferStage2Callback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC015_UpdateBureauDataSetInSAI" time="0.000">
    <skipped/>
  </testcase> <!-- TC015_UpdateBureauDataSetInSAI -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC010_FetchCIR" time="0.000">
    <skipped/>
  </testcase> <!-- TC010_FetchCIR -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC001_FetchLeadDeatils" time="3.654"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC013_FetchLeadAllData" time="0.000">
    <skipped/>
  </testcase> <!-- TC013_FetchLeadAllData -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC004_FetchLeadAllData" time="0.763"/>
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC018_UploadSelfie" time="0.000">
    <skipped/>
  </testcase> <!-- TC018_UploadSelfie -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC026_AcceptLoanAgreement" time="0.000">
    <skipped/>
  </testcase> <!-- TC026_AcceptLoanAgreement -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC028_LMSCallback" time="0.000">
    <skipped/>
  </testcase> <!-- TC028_LMSCallback -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC012_FetchDataPostBRE1Success" time="0.000">
    <skipped/>
  </testcase> <!-- TC012_FetchDataPostBRE1Success -->
  <testcase classname="OCL.Lending.ConsumerLending.TestPostpaidV3OA" name="TC014_CKYCIDNotFound" time="0.000">
    <skipped/>
  </testcase> <!-- TC014_CKYCIDNotFound -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostpaidV3OA -->
