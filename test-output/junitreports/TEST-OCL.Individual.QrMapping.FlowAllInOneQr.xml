<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="3" hostname="197NODMB24984.local" name="OCL.Individual.QrMapping.FlowAllInOneQr" tests="4" failures="0" timestamp="2022-07-13T13:25:47 IST" time="1.011" errors="1">
  <testcase name="TC001_SendOtpMapQrAllInOne" time="1.011" classname="OCL.Individual.QrMapping.FlowAllInOneQr">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Individual.QrMapping.FlowAllInOneQr.TC001_SendOtpMapQrAllInOne(FlowAllInOneQr.java:62)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC001_SendOtpMapQrAllInOne -->
  <testcase name="TC002_FetchMidMapQrAllInOne" time="0.000" classname="OCL.Individual.QrMapping.FlowAllInOneQr">
    <skipped/>
  </testcase> <!-- TC002_FetchMidMapQrAllInOne -->
  <testcase name="TC003_ValidateOtpMapQrAllInOne" time="0.000" classname="OCL.Individual.QrMapping.FlowAllInOneQr">
    <skipped/>
  </testcase> <!-- TC003_ValidateOtpMapQrAllInOne -->
  <testcase name="TC004_SubmitMerchantQrAllInOne" time="0.000" classname="OCL.Individual.QrMapping.FlowAllInOneQr">
    <skipped/>
  </testcase> <!-- TC004_SubmitMerchantQrAllInOne -->
</testsuite> <!-- OCL.Individual.QrMapping.FlowAllInOneQr -->
