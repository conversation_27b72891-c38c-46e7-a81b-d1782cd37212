<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="14" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPostpaidMini" tests="17" failures="1" timestamp="2022-04-21T19:50:20 IST" time="1.905" errors="0">
  <testcase name="TC005_FetchCIR" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC005_FetchCIR -->
  <testcase name="TC016_FetchDynamicTnc" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC016_FetchDynamicTnc -->
  <testcase name="TC010_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC010_VerifyUploadedDocument -->
  <testcase name="TC007_UpdateLeadOFFERAccept" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC007_UpdateLeadOFFERAccept -->
  <testcase name="TC018_AcceptLoanAgreement" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC018_AcceptLoanAgreement -->
  <testcase name="TC008_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC008_UploadSelfie -->
  <testcase name="TC009_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC009_UploadCustomerPhoto -->
  <testcase name="TC004_UpdateBureauDataSetInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC004_UpdateBureauDataSetInSAI -->
  <testcase name="TC001_FetchLeadDeatils" time="0.753" classname="OCL.Lending.ConsumerLending.TestPostpaidMini"/>
  <testcase name="TC014_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC014_FetchLeadAllData -->
  <testcase name="TC017_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC017_FetchLeadAllData -->
  <testcase name="TC013_VeridyAddressDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC013_VeridyAddressDetails -->
  <testcase name="TC015_OfferStage2Callback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC015_OfferStage2Callback -->
  <testcase name="TC003_CreatePostpaidLead" time="0.828" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <failure type="java.lang.AssertionError" message="did not expect to find [BASIC_DETAILS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BASIC_DETAILS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPostpaidMini.TC003_CreatePostpaidLead(TestPostpaidMini.java:213)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC003_CreatePostpaidLead -->
  <testcase name="TC012_CKYCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC012_CKYCCallback -->
  <testcase name="TC002_DeleteExistingLead" time="0.324" classname="OCL.Lending.ConsumerLending.TestPostpaidMini"/>
  <testcase name="TC006_FetchLeadAllData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPostpaidMini">
    <skipped/>
  </testcase> <!-- TC006_FetchLeadAllData -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPostpaidMini -->
