<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="30" hostname="197NODMB24984.local" name="OCL.Lending.BusinessLending.TestAril" tests="33" failures="2" timestamp="2021-09-08T14:53:33 IST" time="15.726" errors="0">
  <testcase name="TC010_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC010_FetchTheCreatedLeadDeatils -->
  <testcase name="TC012_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC012_FetchTheCreatedLeadDeatils -->
  <testcase name="TC021_CheckCKYCStatus" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC021_CheckCKYCStatus -->
  <testcase name="TC027_CKYCCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC027_CKYCCallback -->
  <testcase name="TC001_DeleteExistingLead" time="4.166" classname="OCL.Lending.BusinessLending.TestAril"/>
  <testcase name="TC030_SaveBankDetails" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC030_SaveBankDetails -->
  <testcase name="TC007_UpdateArilLead" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC007_UpdateArilLead -->
  <testcase name="TC015_BRESuccesResponse" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC015_BRESuccesResponse -->
  <testcase name="TC017_LendingLeadOTPCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC017_LendingLeadOTPCallback -->
  <testcase name="TC033_EmandateCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC033_EmandateCallback -->
  <testcase name="TC014_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC014_FetchTheCreatedLeadDeatils -->
  <testcase name="TC005_PPBLOTPCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC005_PPBLOTPCallback -->
  <testcase name="TC029_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC029_UpdateKYCNameInSAI -->
  <testcase name="TC025_UploadCustomerPhoto" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC025_UploadCustomerPhoto -->
  <testcase name="TC003_CreateArilLead" time="10.184" classname="OCL.Lending.BusinessLending.TestAril">
    <failure type="java.lang.AssertionError" message="did not expect to find [Lead successfully created.] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.TestAril.TC003_CreateArilLead(TestAril.java:157)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC003_CreateArilLead -->
  <testcase name="TC020_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC020_FetchTheCreatedLeadDeatils -->
  <testcase name="TC002_FetchLeadDeatils" time="1.376" classname="OCL.Lending.BusinessLending.TestAril">
    <failure type="java.lang.AssertionError" message="did not expect to find [Data Not present for customer] but found [Oops. Something went wrong during the application process. Please try again later.]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Data Not present for customer] but found [Oops. Something went wrong during the application process. Please try again later.]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.BusinessLending.TestAril.TC002_FetchLeadDeatils(TestAril.java:110)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- TC002_FetchLeadDeatils -->
  <testcase name="TC09_BREValidationPending" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC09_BREValidationPending -->
  <testcase name="TC023_UploadSelfie" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC023_UploadSelfie -->
  <testcase name="TC019_ArilOTPCallback" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC019_ArilOTPCallback -->
  <testcase name="TC006_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC006_FetchTheCreatedLeadDeatils -->
  <testcase name="TC032_SubmitApplication" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC032_SubmitApplication -->
  <testcase name="TC008_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC008_FetchTheCreatedLeadDeatils -->
  <testcase name="TC024_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC024_VerifyUploadedDocument -->
  <testcase name="TC11_BREOtpVerified" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC11_BREOtpVerified -->
  <testcase name="TC028_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC028_VerifyLeadStage -->
  <testcase name="TC13_BRERequestInvoked" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC13_BRERequestInvoked -->
  <testcase name="TC018_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC018_FetchTheCreatedLeadDeatils -->
  <testcase name="TC031_FetchDynamicTnc" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC031_FetchDynamicTnc -->
  <testcase name="TC026_VerifyUploadedDocument" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC026_VerifyUploadedDocument -->
  <testcase name="TC016_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC016_FetchTheCreatedLeadDeatils -->
  <testcase name="TC022_VerifyLeadStage" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC022_VerifyLeadStage -->
  <testcase name="TC004_FetchTheCreatedLeadDeatils" time="0.000" classname="OCL.Lending.BusinessLending.TestAril">
    <skipped/>
  </testcase> <!-- TC004_FetchTheCreatedLeadDeatils -->
</testsuite> <!-- OCL.Lending.BusinessLending.TestAril -->
