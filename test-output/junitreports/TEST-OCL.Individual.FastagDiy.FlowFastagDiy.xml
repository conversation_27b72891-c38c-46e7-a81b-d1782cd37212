<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="197NODMB24984.local" name="OCL.Individual.FastagDiy.FlowFastagDiy" tests="2" failures="1" timestamp="2022-07-13T12:28:08 IST" time="6.471" errors="1">
  <testcase name="TC0001_CreateLead" time="1.244" classname="OCL.Individual.FastagDiy.FlowFastagDiy">
    <failure type="java.lang.AssertionError" message="did not expect to find [Lead successfully created.] but found [Mobile number not found against the mentioned custId]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [Mobile number not found against the mentioned custId]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.FastagDiy.FlowFastagDiy.TC0001_CreateLead(FlowFastagDiy.java:108)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0001_CreateLead -->
  <testcase name="TC0002_SubmitLeadPanel" time="5.227" classname="OCL.Individual.FastagDiy.FlowFastagDiy">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at com.goldengate.common.BaseMethod.ReallocatingAgent(BaseMethod.java:453)
at OCL.Individual.FastagDiy.FlowFastagDiy.TC0002_SubmitLeadPanel(FlowFastagDiy.java:118)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </error>
  </testcase> <!-- TC0002_SubmitLeadPanel -->
</testsuite> <!-- OCL.Individual.FastagDiy.FlowFastagDiy -->
