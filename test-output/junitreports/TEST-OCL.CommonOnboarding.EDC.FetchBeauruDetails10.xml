<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="197NODMB30184.local" failures="0" tests="20" name="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" time="13.134" errors="0" timestamp="2023-11-07T14:30:42 IST" skipped="0">
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.531"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.451"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_4_GetBeauruErrorWithoutDeviceIdentifer" time="0.288"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.394"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_1_getExistingLeadbeauru" time="1.636"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="1.273"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.338"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="2.952"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.330"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.588"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.556"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.363"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.337"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.384"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.363"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.611"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.382"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_2_ErrorWithoutSessionTokengetExistingLeadbeauru" time="0.626"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="test" time="0.283"/>
  <testcase classname="OCL.CommonOnboarding.EDC.FetchBeauruDetails10" name="TC_3_ErrorWithoutVersiongetExistingLeadbeauru" time="0.448"/>
</testsuite> <!-- OCL.CommonOnboarding.EDC.FetchBeauruDetails10 -->
