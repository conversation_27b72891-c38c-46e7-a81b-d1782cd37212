<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="41" hostname="197NODMB24984.local" name="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA" tests="46" failures="1" timestamp="2023-05-18T19:41:00 IST" time="4.882" errors="0">
  <testcase name="TC041_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC041_PLv3ABFL_FetchLeadPostSubmitApplication -->
  <testcase name="TC008_PLv3ABFL_FetchDataPostOccupationDetailUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC008_PLv3ABFL_FetchDataPostOccupationDetailUpdate -->
  <testcase name="TC027_PLv3ABFL_FetchDataAfterBRE2Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC027_PLv3ABFL_FetchDataAfterBRE2Success -->
  <testcase name="TC037_PLv3ABFL_FetchLeadPostEmandate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC037_PLv3ABFL_FetchLeadPostEmandate -->
  <testcase name="TC022_PLv3ABFL_UploadSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC022_PLv3ABFL_UploadSelfie -->
  <testcase name="TC028_PLv3ABFL_AdditionalIsRequiredorNot" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC028_PLv3ABFL_AdditionalIsRequiredorNot -->
  <testcase name="TC029_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC029_PLv3ABFL_FetchLeadVerifyAdditionalData -->
  <testcase name="TC036_PLv3ABFL_EmandateCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC036_PLv3ABFL_EmandateCallback -->
  <testcase name="TC007_PLV3ABFL_UpdateLeadOccupationDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC007_PLV3ABFL_UpdateLeadOccupationDetails -->
  <testcase name="TC011_PLv3ABFL_FetchCIR" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC011_PLv3ABFL_FetchCIR -->
  <testcase name="TC039_PLv3ABFL_GenerateSanctionLetter" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC039_PLv3ABFL_GenerateSanctionLetter -->
  <testcase name="TC033_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC033_PLv3ABFL_FetchLeadUpdateCKYCinSAI -->
  <testcase name="TC020_PLv3ABFL_UploadCustomerPhoto" time="0.001" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC020_PLv3ABFL_UploadCustomerPhoto -->
  <testcase name="TC044PLv3ABFL_SubmitApplicationLMSApprovedCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC044PLv3ABFL_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC012_PLv3ABFL_BRE1Callback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC012_PLv3ABFL_BRE1Callback -->
  <testcase name="TC026_PLv3ABFL_LocationCaptured" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_LocationCaptured -->
  <testcase name="TC004_PLV3ABFL_FetchLeadAllData" time="1.086" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA"/>
  <testcase name="TC032_PLv3ABFL_UpdateKYCNameInSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC032_PLv3ABFL_UpdateKYCNameInSAI -->
  <testcase name="TC043_PLv3ABFL_FetchLeadPostPDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC043_PLv3ABFL_FetchLeadPostPDCCallback -->
  <testcase name="TC025_PLv3ABFL_FetchDataPostSelfieUploaded" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC025_PLv3ABFL_FetchDataPostSelfieUploaded -->
  <testcase name="TC010_PLv3ABFL_FetchDataPostSAIlUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC010_PLv3ABFL_FetchDataPostSAIlUpdate -->
  <testcase name="TC040_PLv3ABFL_SubmitApplication" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC040_PLv3ABFL_SubmitApplication -->
  <testcase name="TC042_PLv3ABFL_PDCCallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC042_PLv3ABFL_PDCCallback -->
  <testcase name="TC017_PLv3ABFLNewKYC_FetchLeadPostKYCInitiate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC017_PLv3ABFLNewKYC_FetchLeadPostKYCInitiate -->
  <testcase name="TC013_PLv3ABFL_FetchDataPostBRE1Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC013_PLv3ABFL_FetchDataPostBRE1Success -->
  <testcase name="TC016_PLv3ABFLNewKYC_InitiateKYCSearchByPAN" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC016_PLv3ABFLNewKYC_InitiateKYCSearchByPAN -->
  <testcase name="TC031_PLv3ABFL_BRE3Success" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC031_PLv3ABFL_BRE3Success -->
  <testcase name="TC017_PLv3ABFLNewKYC_UpdateLeadDetailsinSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC017_PLv3ABFLNewKYC_UpdateLeadDetailsinSAI -->
  <testcase name="TC005_PLV3ABFL_UpdateLeadBasicDetails" time="1.140" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <failure type="java.lang.AssertionError" message="did not expect to find [BASIC_DETAILS] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [BASIC_DETAILS] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA.TC005_PLV3ABFL_UpdateLeadBasicDetails(TestPersonalLoanv3ABFLNewKYCviaSBA.java:322)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:392)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:354)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TC005_PLV3ABFL_UpdateLeadBasicDetails -->
  <testcase name="TC026_PLv3ABFL_SecondBRECallback" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC026_PLv3ABFL_SecondBRECallback -->
  <testcase name="TC015_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC015_PLv3ABFL_FetchDataPostLoanOfferAccept -->
  <testcase name="TC014_PLv3ABFL_LoanOfferAccept" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC014_PLv3ABFL_LoanOfferAccept -->
  <testcase name="TC024_PLv3ABFL_FetchDataPostKYCIntiated" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC024_PLv3ABFL_FetchDataPostKYCIntiated -->
  <testcase name="TC006_PLv3ABFL_FetchDataPostBasicDetailUpdate" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC006_PLv3ABFL_FetchDataPostBasicDetailUpdate -->
  <testcase name="TC035_PLv3ABFL_FetchLeadPostBankVerification" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC035_PLv3ABFL_FetchLeadPostBankVerification -->
  <testcase name="TC034_PLv3ABFL_SaveBankDetails" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC034_PLv3ABFL_SaveBankDetails -->
  <testcase name="TC001_PLv3ABFL_fetchlLead" time="1.182" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA"/>
  <testcase name="TC038_PLv3ABFL_GenerateLoanAgreement" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC038_PLv3ABFL_GenerateLoanAgreement -->
  <testcase name="TC018_PLv3ABFLNewKYC_InitiateKYCSearchByAadhar" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC018_PLv3ABFLNewKYC_InitiateKYCSearchByAadhar -->
  <testcase name="TC009_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC009_PLV3ABFL_UpdateLeadDetailsinSAI -->
  <testcase name="TC019_PLv3ABFL_FetchDataPostKYCInitiated" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC019_PLv3ABFL_FetchDataPostKYCInitiated -->
  <testcase name="TC002_PLv3ABFL_DeleteExistingLead" time="0.447" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA"/>
  <testcase name="TC030_PLv3ABFL_AdditionalDataCapture" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC030_PLv3ABFL_AdditionalDataCapture -->
  <testcase name="TC003_PLv3ABFL_CreateLead" time="1.026" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA"/>
  <testcase name="TC021_PLv3ABFL_VerifyUploadedCustomerPhoto" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC021_PLv3ABFL_VerifyUploadedCustomerPhoto -->
  <testcase name="TC023_PLv3ABFL_VerifyUploadedSelfie" time="0.000" classname="OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA">
    <skipped/>
  </testcase> <!-- TC023_PLv3ABFL_VerifyUploadedSelfie -->
</testsuite> <!-- OCL.Lending.ConsumerLending.TestPersonalLoanv3ABFLNewKYCviaSBA -->
