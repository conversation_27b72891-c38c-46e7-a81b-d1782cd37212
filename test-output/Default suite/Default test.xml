<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="14" hostname="197NODMB30184.local" failures="0" tests="1" name="Default test" time="26.085" errors="0" timestamp="2023-11-07T20:38:47 IST">
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_1_saveCartWithPrepaidMDRDisable" time="26.001"/>
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_1_saveOrderWithdinersDisable" time="0.0">
    <ignored/>
  </testcase> <!-- TC_1_saveOrderWithdinersDisable -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_2_saveCartSessiontoken" time="0.0">
    <ignored/>
  </testcase> <!-- TC_2_saveCartSessiontoken -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_3_saveCartWithoutDeviceIdentifer" time="0.0">
    <ignored/>
  </testcase> <!-- TC_3_saveCartWithoutDeviceIdentifer -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_1_saveOrderWithCorporateDisable" time="0.0">
    <ignored/>
  </testcase> <!-- TC_1_saveOrderWithCorporateDisable -->
  <testcase classname="com.goldengate.common.BaseMethod" name="test" time="0.0">
    <ignored/>
  </testcase> <!-- test -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_7_cartSaveWithInvalidAddOnsAMC" time="0.0">
    <ignored/>
  </testcase> <!-- TC_7_cartSaveWithInvalidAddOnsAMC -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_8_cartSaveWithInvalidAddOnsEMIRental" time="0.0">
    <ignored/>
  </testcase> <!-- TC_8_cartSaveWithInvalidAddOnsEMIRental -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_4_cartSaveInvalidLeadID" time="0.0">
    <ignored/>
  </testcase> <!-- TC_4_cartSaveInvalidLeadID -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_5_cartSaveWithoutLeadID" time="0.0">
    <ignored/>
  </testcase> <!-- TC_5_cartSaveWithoutLeadID -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_6_cartSaveWithoutPlanId" time="0.0">
    <ignored/>
  </testcase> <!-- TC_6_cartSaveWithoutPlanId -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_1_saveOrdertWithAMEXDisable" time="0.0">
    <ignored/>
  </testcase> <!-- TC_1_saveOrdertWithAMEXDisable -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_10_cartSaveWithInvalidQuantity" time="0.0">
    <ignored/>
  </testcase> <!-- TC_10_cartSaveWithInvalidQuantity -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_1_saveOrder" time="0.0">
    <ignored/>
  </testcase> <!-- TC_1_saveOrder -->
  <testcase classname="OCL.CommonOnboarding.EDC.saveOrder20" name="TC_9_cartSaveWithoutQuanity" time="0.0">
    <ignored/>
  </testcase> <!-- TC_9_cartSaveWithoutQuanity -->
</testsuite> <!-- Default test -->
