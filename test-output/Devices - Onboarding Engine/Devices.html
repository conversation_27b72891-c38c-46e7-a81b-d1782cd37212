<html>
<head>
<title>TestNG:  Devices</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Devices</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>39/6/2</td>
</tr><tr>
<td>Started on:</td><td>Fri Aug 23 20:09:45 IST 2024</td>
</tr>
<tr><td>Total time:</td><td>103 seconds (103683 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021()'><b>TC_021</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without device identifier<br/>
<a href="#Output-772175463" onClick='toggleBox("Output-772175463", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-772175463" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-772175463">
TC_021 = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace772175463", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace772175463'><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:926)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1127)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType()'><b>getStatusInCaseOfInvalidDeviceType</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-2062137507" onClick='toggleBox("Output-2062137507", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-2062137507" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-2062137507">
getStatusInCaseOfInvalidDeviceType = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2062137507", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2062137507'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>4</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel()'><b>getStatusInCaseOfInvalidModel</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1403908189" onClick='toggleBox("Output-1403908189", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1403908189" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1403908189">
getStatusInCaseOfInvalidModel = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1403908189", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1403908189'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS()'><b>getStatusInCaseOfInvalidOS</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-718050836" onClick='toggleBox("Output-718050836", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-718050836" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-718050836">
getStatusInCaseOfInvalidOS = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace718050836", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace718050836'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>4</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid()'><b>getStatusInCaseOfLeadIdISNotvalid</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-2087046199" onClick='toggleBox("Output-2087046199", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-2087046199" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-2087046199">
getStatusInCaseOfLeadIdISNotvalid = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [500] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2087046199", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2087046199'><pre>java.lang.AssertionError: did not expect to find [500] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation()'><b>getStatusInCaseOfSuccessfullQrValidation</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1876200622" onClick='toggleBox("Output-1876200622", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1876200622" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1876200622">
getStatusInCaseOfSuccessfullQrValidation = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1876200622", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1876200622'><pre>java.lang.AssertionError: did not expect to find [200] but found [400]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>4</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingMultiplePINSecondary()'><b>AddingMultiplePINSecondary</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Multiple PIN with Secondary details<br/>
<a href="#Output-528541748" onClick='toggleBox("Output-528541748", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-528541748" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-528541748">
AddingMultiplePINSecondary = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@6c977dcf</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingMultiplePINs()'><b>AddingMultiplePINs</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Multiple PIN with Primary details<br/>
<a href="#Output-262389491" onClick='toggleBox("Output-262389491", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-262389491" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-262389491">
AddingMultiplePINs = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@6c977dcf</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingSinglePINSecondary()'><b>AddingSinglePINSecondary</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Single PIN with Secondary details<br/>
<a href="#Output-84629220" onClick='toggleBox("Output-84629220", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-84629220" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-84629220">
AddingSinglePINSecondary = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@6c977dcf</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.AddingSinglePINs()'><b>AddingSinglePINs</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Fetching Single PIN with Primary details<br/>
<a href="#Output-58649956" onClick='toggleBox("Output-58649956", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-58649956" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-58649956">
AddingSinglePINs = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@6c977dcf</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.BlankPin()'><b>BlankPin</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Blank pincode<br/>
<a href="#Output-375916432" onClick='toggleBox("Output-375916432", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-375916432" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-375916432">
BlankPin = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@6c977dcf</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.InvalidPin()'><b>InvalidPin</b><br>Test class: OCL.UAD.AddUADPincode<br>Test method: Invalid pincode<br/>
<a href="#Output-590287367" onClick='toggleBox("Output-590287367", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-590287367" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-590287367">
InvalidPin = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.UAD.AddUADPincode@6c977dcf</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_001()'><b>TC_001</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with all valid details<br/>
<a href="#Output-1704733952" onClick='toggleBox("Output-1704733952", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1704733952" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1704733952">
TC_001 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_002()'><b>TC_002</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without token<br/>
<a href="#Output-1052519136" onClick='toggleBox("Output-1052519136", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1052519136" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1052519136">
TC_002 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_003()'><b>TC_003</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid token<br/>
<a href="#Output-93840122" onClick='toggleBox("Output-93840122", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-93840122" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-93840122">
TC_003 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_004()'><b>TC_004</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without param<br/>
<a href="#Output-184617408" onClick='toggleBox("Output-184617408", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-184617408" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-184617408">
TC_004 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_005()'><b>TC_005</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with inavlid param<br/>
<a href="#Output-827209051" onClick='toggleBox("Output-827209051", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-827209051" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-827209051">
TC_005 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_006()'><b>TC_006</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without entity type<br/>
<a href="#Output-75757764" onClick='toggleBox("Output-75757764", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-75757764" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-75757764">
TC_006 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_007()'><b>TC_007</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid entity type<br/>
<a href="#Output-30232202" onClick='toggleBox("Output-30232202", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-30232202" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-30232202">
TC_007 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_008()'><b>TC_008</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without user custid<br/>
<a href="#Output-664941033" onClick='toggleBox("Output-664941033", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-664941033" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-664941033">
TC_008 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_009()'><b>TC_009</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid custid<br/>
<a href="#Output-1163202926" onClick='toggleBox("Output-1163202926", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1163202926" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1163202926">
TC_009 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_010()'><b>TC_010</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without agent custid<br/>
<a href="#Output-1695926356" onClick='toggleBox("Output-1695926356", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1695926356" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1695926356">
TC_010 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_011()'><b>TC_011</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid agent custid<br/>
<a href="#Output-1233948622" onClick='toggleBox("Output-1233948622", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1233948622" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1233948622">
TC_011 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_012()'><b>TC_012</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without mobile number<br/>
<a href="#Output-1071945164" onClick='toggleBox("Output-1071945164", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1071945164" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1071945164">
TC_012 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_013()'><b>TC_013</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid mobile number<br/>
<a href="#Output-494985238" onClick='toggleBox("Output-494985238", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-494985238" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-494985238">
TC_013 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_014()'><b>TC_014</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with invalid MID<br/>
<a href="#Output-182031982" onClick='toggleBox("Output-182031982", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-182031982" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-182031982">
TC_014 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_015()'><b>TC_015</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without KYBID<br/>
<a href="#Output-1659597964" onClick='toggleBox("Output-1659597964", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1659597964" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1659597964">
TC_015 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_016()'><b>TC_016</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with INVALID KYBID<br/>
<a href="#Output-1802701877" onClick='toggleBox("Output-1802701877", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1802701877" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1802701877">
TC_016 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_017()'><b>TC_017</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead withOUT WORKFLOW VERSION<br/>
<a href="#Output-546342021" onClick='toggleBox("Output-546342021", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-546342021" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-546342021">
TC_017 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_018()'><b>TC_018</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead with INVALID WORKFLOW VERSION<br/>
<a href="#Output-1329644058" onClick='toggleBox("Output-1329644058", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1329644058" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1329644058">
TC_018 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_019()'><b>TC_019</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without service reason<br/>
<a href="#Output-1513730762" onClick='toggleBox("Output-1513730762", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1513730762" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1513730762">
TC_019 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_020()'><b>TC_020</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without version<br/>
<a href="#Output-1898103185" onClick='toggleBox("Output-1898103185", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1898103185" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1898103185">
TC_020 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_022()'><b>TC_022</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without checksum<br/>
<a href="#Output-52456642" onClick='toggleBox("Output-52456642", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-52456642" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-52456642">
TC_022 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceBindedWithDifferentMid()'><b>getStatusInCaseOfDeviceBindedWithDifferentMid</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1409462298" onClick='toggleBox("Output-1409462298", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1409462298" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1409462298">
getStatusInCaseOfDeviceBindedWithDifferentMid = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceBindingDoesNotExist()'><b>getStatusInCaseOfDeviceBindingDoesNotExist</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-356366675" onClick='toggleBox("Output-356366675", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-356366675" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-356366675">
getStatusInCaseOfDeviceBindingDoesNotExist = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfDeviceIDNotPassedInRequest()'><b>getStatusInCaseOfDeviceIDNotPassedInRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1628426415" onClick='toggleBox("Output-1628426415", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1628426415" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1628426415">
getStatusInCaseOfDeviceIDNotPassedInRequest = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceOEM()'><b>getStatusInCaseOfInvalidDeviceOEM</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1088974815" onClick='toggleBox("Output-1088974815", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1088974815" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1088974815">
getStatusInCaseOfInvalidDeviceOEM = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidTokenIsPassedInRequest()'><b>getStatusInCaseOfInvalidTokenIsPassedInRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-124283990" onClick='toggleBox("Output-124283990", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-124283990" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-124283990">
getStatusInCaseOfInvalidTokenIsPassedInRequest = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIsNotPassedINRequest()'><b>getStatusInCaseOfLeadIsNotPassedINRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1521377590" onClick='toggleBox("Output-1521377590", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1521377590" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1521377590">
getStatusInCaseOfLeadIsNotPassedINRequest = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader()'><b>getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-666790064" onClick='toggleBox("Output-666790064", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-666790064" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-666790064">
getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfTokenIsNotPassedInRequest()'><b>getStatusInCaseOfTokenIsNotPassedInRequest</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1710654813" onClick='toggleBox("Output-1710654813", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1710654813" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1710654813">
getStatusInCaseOfTokenIsNotPassedInRequest = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfVersionIsNotPassedInReq()'><b>getStatusInCaseOfVersionIsNotPassedInReq</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-626824310" onClick='toggleBox("Output-626824310", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-626824310" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-626824310">
getStatusInCaseOfVersionIsNotPassedInReq = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.test()'><b>test</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br/>
<a href="#Output-768325822" onClick='toggleBox("Output-768325822", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-768325822" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-768325822">
test = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.UAD.AddUADPincode.test()'><b>test</b><br>Test class: OCL.UAD.AddUADPincode<br/>
<a href="#Output-1975000951" onClick='toggleBox("Output-1975000951", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1975000951" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1975000951">
test = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.UAD.AddUADPincode@6c977dcf</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.test()'><b>test</b><br>Test class: OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr<br/>
<a href="#Output-1430743701" onClick='toggleBox("Output-1430743701", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1430743701" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1430743701">
test = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021()'><b>TC_021</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without device identifier<br/>
<a href="#Output-31248596" onClick='toggleBox("Output-31248596", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-31248596" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-31248596">
TC_021 = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace31248596", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace31248596'><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
<tr>
<td title='OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021()'><b>TC_021</b><br>Test class: OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade<br>Test method: Create a new lead without device identifier<br/>
<a href="#Output-1929576190" onClick='toggleBox("Output-1929576190", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1929576190" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1929576190">
TC_021 = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1929576190", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1929576190'><pre>java.lang.AssertionError: did not expect to find [410] but found [200]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:926)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1127)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
</pre></div></td>
<td>2</td>
<td>OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6</td></tr>
</table><p>
</body>
</html>