<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="197NODMB38994" ignored="0" tests="0" failures="0" timestamp="2022-11-11T11:13:49 IST" time="0.033" errors="0">
  <testcase name="@BeforeSuite BeforeSuiteLogin" time="7.325" classname="BeforeSuite.BeforeSuiteClass">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:460)
at java.util.Properties.setProperty(Properties.java:166)
at Services.MechantService.MiddlewareServices.v1Token(MiddlewareServices.java:1174)
at BeforeSuite.BeforeSuiteClass.BeforeSuiteLogin(BeforeSuiteClass.java:42)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:61)
at org.testng.internal.Invoker.invokeConfigurationMethod(Invoker.java:511)
at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:241)
at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:144)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:338)
at org.testng.SuiteRunner.run(SuiteRunner.java:302)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
at org.testng.TestNG.runSuites(TestNG.java:997)
at org.testng.TestNG.run(TestNG.java:965)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- @BeforeSuite BeforeSuiteLogin -->
</testsuite>
