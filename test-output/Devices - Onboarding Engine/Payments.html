<html>
<head>
<title>TestNG:  Payments</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Payments</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>0/0/0</td>
</tr><tr>
<td>Started on:</td><td>Fri Nov 11 11:13:49 IST 2022</td>
</tr>
<tr><td>Total time:</td><td>0 seconds (33 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED CONFIGURATIONS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='BeforeSuite.BeforeSuiteClass.BeforeSuiteLogin()'><b>BeforeSuiteLogin</b><br>Test class: BeforeSuite.BeforeSuiteClass</td>
<td><div><pre>java.lang.NullPointerException
	at java.util.Hashtable.put(Hashtable.java:460)
	at java.util.Properties.setProperty(Properties.java:166)
	at Services.MechantService.MiddlewareServices.v1Token(MiddlewareServices.java:1174)
	at BeforeSuite.BeforeSuiteClass.BeforeSuiteLogin(BeforeSuiteClass.java:42)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:61)
	at org.testng.internal.Invoker.invokeConfigurationMethod(Invoker.java:511)
	at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:241)
	at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:144)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:338)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace1593909368", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1593909368'><pre>java.lang.NullPointerException
	at java.util.Hashtable.put(Hashtable.java:460)
	at java.util.Properties.setProperty(Properties.java:166)
	at Services.MechantService.MiddlewareServices.v1Token(MiddlewareServices.java:1174)
	at BeforeSuite.BeforeSuiteClass.BeforeSuiteLogin(BeforeSuiteClass.java:42)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:61)
	at org.testng.internal.Invoker.invokeConfigurationMethod(Invoker.java:511)
	at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:241)
	at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:144)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:338)
	at org.testng.SuiteRunner.run(SuiteRunner.java:302)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1145)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1067)
	at org.testng.TestNG.runSuites(TestNG.java:997)
	at org.testng.TestNG.run(TestNG.java:965)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>7</td>
<td>BeforeSuite.BeforeSuiteClass@2a1debfa</td></tr>
</table><p>
</body>
</html>