<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery-1.7.1.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>
    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <br/>
      <span class="top-banner-font-1">1 suite, 6 failed tests</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" title="Collapse/expand all the suites" class="collapse-all-link">
          <img src="collapseall.gif" class="collapse-all-icon">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" panel-name="suite-Devices___Onboarding_Engine" class="navigator-link">
              <span class="suite-name border-failed">Devices - Onboarding Engine</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" panel-name="test-xml-Devices___Onboarding_Engine" class="navigator-link ">
                    <span>[unset file name]</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="testlist-Devices___Onboarding_Engine" class="navigator-link ">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="group-Devices___Onboarding_Engine" class="navigator-link ">
                    <span>1 group</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="times-Devices___Onboarding_Engine" class="navigator-link ">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="reporter-Devices___Onboarding_Engine" class="navigator-link ">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="ignored-methods-Devices___Onboarding_Engine" class="navigator-link ">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="chronological-Devices___Onboarding_Engine" class="navigator-link ">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">45 methods, 6 failed,   39 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title failed">Failed methods</span>
                    <span class="show-or-hide-methods failed">
                      <a href="#" panel-name="suite-Devices___Onboarding_Engine" class="hide-methods failed suite-Devices___Onboarding_Engine"> (hide)</a> <!-- hide-methods failed suite-Devices___Onboarding_Engine -->
                      <a href="#" panel-name="suite-Devices___Onboarding_Engine" class="show-methods failed suite-Devices___Onboarding_Engine"> (show)</a> <!-- show-methods failed suite-Devices___Onboarding_Engine -->
                    </span>
                    <div class="method-list-content failed suite-Devices___Onboarding_Engine">
                      <span>
                        <img src="failed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_021">TC_021</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="failed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfInvalidDeviceType">getStatusInCaseOfInvalidDeviceType</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="failed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfInvalidModel">getStatusInCaseOfInvalidModel</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="failed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfInvalidOS">getStatusInCaseOfInvalidOS</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="failed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfLeadIdISNotvalid">getStatusInCaseOfLeadIdISNotvalid</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="failed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfSuccessfullQrValidation">getStatusInCaseOfSuccessfullQrValidation</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content failed suite-Devices___Onboarding_Engine -->
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-Devices___Onboarding_Engine" class="hide-methods passed suite-Devices___Onboarding_Engine"> (hide)</a> <!-- hide-methods passed suite-Devices___Onboarding_Engine -->
                      <a href="#" panel-name="suite-Devices___Onboarding_Engine" class="show-methods passed suite-Devices___Onboarding_Engine"> (show)</a> <!-- show-methods passed suite-Devices___Onboarding_Engine -->
                    </span>
                    <div class="method-list-content passed suite-Devices___Onboarding_Engine">
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.UAD.AddUADPincode" class="method navigator-link" hash-for-method="AddingMultiplePINSecondary">AddingMultiplePINSecondary</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.UAD.AddUADPincode" class="method navigator-link" hash-for-method="AddingMultiplePINs">AddingMultiplePINs</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.UAD.AddUADPincode" class="method navigator-link" hash-for-method="AddingSinglePINSecondary">AddingSinglePINSecondary</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.UAD.AddUADPincode" class="method navigator-link" hash-for-method="AddingSinglePINs">AddingSinglePINs</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.UAD.AddUADPincode" class="method navigator-link" hash-for-method="BlankPin">BlankPin</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.UAD.AddUADPincode" class="method navigator-link" hash-for-method="InvalidPin">InvalidPin</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_001">TC_001</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_002">TC_002</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_003">TC_003</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_004">TC_004</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_005">TC_005</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_006">TC_006</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_007">TC_007</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_008">TC_008</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_009">TC_009</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_010">TC_010</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_011">TC_011</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_012">TC_012</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_013">TC_013</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_014">TC_014</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_015">TC_015</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_016">TC_016</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_017">TC_017</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_018">TC_018</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_019">TC_019</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_020">TC_020</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="TC_022">TC_022</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfDeviceBindedWithDifferentMid">getStatusInCaseOfDeviceBindedWithDifferentMid</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfDeviceBindingDoesNotExist">getStatusInCaseOfDeviceBindingDoesNotExist</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfDeviceIDNotPassedInRequest">getStatusInCaseOfDeviceIDNotPassedInRequest</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfInvalidDeviceOEM">getStatusInCaseOfInvalidDeviceOEM</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfInvalidTokenIsPassedInRequest">getStatusInCaseOfInvalidTokenIsPassedInRequest</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfLeadIsNotPassedINRequest">getStatusInCaseOfLeadIsNotPassedINRequest</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader">getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfTokenIsNotPassedInRequest">getStatusInCaseOfTokenIsNotPassedInRequest</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="getStatusInCaseOfVersionIsNotPassedInReq">getStatusInCaseOfVersionIsNotPassedInReq</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.UAD.AddUADPincode" class="method navigator-link" hash-for-method="test">test</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr" class="method navigator-link" hash-for-method="test">test</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Devices___Onboarding_Engine" title="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade" class="method navigator-link" hash-for-method="test">test</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-Devices___Onboarding_Engine -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-Devices___Onboarding_Engine" class="panel Devices___Onboarding_Engine">
          <div class="suite-Devices___Onboarding_Engine-class-failed">
            <div class="main-panel-header rounded-window-top">
              <img src="failed.png"/>
              <span class="class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfInvalidDeviceType">
                  </a> <!-- getStatusInCaseOfInvalidDeviceType -->
                  <span class="method-name">getStatusInCaseOfInvalidDeviceType</span>
                  <div class="stack-trace">java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfInvalidModel">
                  </a> <!-- getStatusInCaseOfInvalidModel -->
                  <span class="method-name">getStatusInCaseOfInvalidModel</span>
                  <div class="stack-trace">java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfInvalidOS">
                  </a> <!-- getStatusInCaseOfInvalidOS -->
                  <span class="method-name">getStatusInCaseOfInvalidOS</span>
                  <div class="stack-trace">java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfLeadIdISNotvalid">
                  </a> <!-- getStatusInCaseOfLeadIdISNotvalid -->
                  <span class="method-name">getStatusInCaseOfLeadIdISNotvalid</span>
                  <div class="stack-trace">java.lang.AssertionError: did not expect to find [500] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfSuccessfullQrValidation">
                  </a> <!-- getStatusInCaseOfSuccessfullQrValidation -->
                  <span class="method-name">getStatusInCaseOfSuccessfullQrValidation</span>
                  <div class="stack-trace">java.lang.AssertionError: did not expect to find [200] but found [400]
	at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Devices___Onboarding_Engine-class-failed -->
          <div class="suite-Devices___Onboarding_Engine-class-failed">
            <div class="main-panel-header rounded-window-top">
              <img src="failed.png"/>
              <span class="class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="TC_021">
                  </a> <!-- TC_021 -->
                  <span class="method-name">TC_021</span>
                  <div class="stack-trace">java.lang.AssertionError: did not expect to find [410] but found [200]
	at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
... Removed 16 stack frames
</div> <!-- stack-trace -->
                  <em>
(Create a new lead without device identifier)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Devices___Onboarding_Engine-class-failed -->
          <div class="suite-Devices___Onboarding_Engine-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfDeviceBindedWithDifferentMid">
                  </a> <!-- getStatusInCaseOfDeviceBindedWithDifferentMid -->
                  <span class="method-name">getStatusInCaseOfDeviceBindedWithDifferentMid</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfDeviceBindingDoesNotExist">
                  </a> <!-- getStatusInCaseOfDeviceBindingDoesNotExist -->
                  <span class="method-name">getStatusInCaseOfDeviceBindingDoesNotExist</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfDeviceIDNotPassedInRequest">
                  </a> <!-- getStatusInCaseOfDeviceIDNotPassedInRequest -->
                  <span class="method-name">getStatusInCaseOfDeviceIDNotPassedInRequest</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfInvalidDeviceOEM">
                  </a> <!-- getStatusInCaseOfInvalidDeviceOEM -->
                  <span class="method-name">getStatusInCaseOfInvalidDeviceOEM</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfInvalidTokenIsPassedInRequest">
                  </a> <!-- getStatusInCaseOfInvalidTokenIsPassedInRequest -->
                  <span class="method-name">getStatusInCaseOfInvalidTokenIsPassedInRequest</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfLeadIsNotPassedINRequest">
                  </a> <!-- getStatusInCaseOfLeadIsNotPassedINRequest -->
                  <span class="method-name">getStatusInCaseOfLeadIsNotPassedINRequest</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader">
                  </a> <!-- getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader -->
                  <span class="method-name">getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfTokenIsNotPassedInRequest">
                  </a> <!-- getStatusInCaseOfTokenIsNotPassedInRequest -->
                  <span class="method-name">getStatusInCaseOfTokenIsNotPassedInRequest</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="getStatusInCaseOfVersionIsNotPassedInReq">
                  </a> <!-- getStatusInCaseOfVersionIsNotPassedInReq -->
                  <span class="method-name">getStatusInCaseOfVersionIsNotPassedInReq</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="test">
                  </a> <!-- test -->
                  <span class="method-name">test</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Devices___Onboarding_Engine-class-passed -->
          <div class="suite-Devices___Onboarding_Engine-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="TC_001">
                  </a> <!-- TC_001 -->
                  <span class="method-name">TC_001</span>
                  <em>
(Create a new lead with all valid details)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_002">
                  </a> <!-- TC_002 -->
                  <span class="method-name">TC_002</span>
                  <em>
(Create a new lead without token)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_003">
                  </a> <!-- TC_003 -->
                  <span class="method-name">TC_003</span>
                  <em>
(Create a new lead with invalid token)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_004">
                  </a> <!-- TC_004 -->
                  <span class="method-name">TC_004</span>
                  <em>
(Create a new lead without param)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_005">
                  </a> <!-- TC_005 -->
                  <span class="method-name">TC_005</span>
                  <em>
(Create a new lead with inavlid param)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_006">
                  </a> <!-- TC_006 -->
                  <span class="method-name">TC_006</span>
                  <em>
(Create a new lead without entity type)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_007">
                  </a> <!-- TC_007 -->
                  <span class="method-name">TC_007</span>
                  <em>
(Create a new lead with invalid entity type)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_008">
                  </a> <!-- TC_008 -->
                  <span class="method-name">TC_008</span>
                  <em>
(Create a new lead without user custid)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_009">
                  </a> <!-- TC_009 -->
                  <span class="method-name">TC_009</span>
                  <em>
(Create a new lead with invalid custid)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_010">
                  </a> <!-- TC_010 -->
                  <span class="method-name">TC_010</span>
                  <em>
(Create a new lead without agent custid)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_011">
                  </a> <!-- TC_011 -->
                  <span class="method-name">TC_011</span>
                  <em>
(Create a new lead with invalid agent custid)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_012">
                  </a> <!-- TC_012 -->
                  <span class="method-name">TC_012</span>
                  <em>
(Create a new lead without mobile number)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_013">
                  </a> <!-- TC_013 -->
                  <span class="method-name">TC_013</span>
                  <em>
(Create a new lead with invalid mobile number)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_014">
                  </a> <!-- TC_014 -->
                  <span class="method-name">TC_014</span>
                  <em>
(Create a new lead with invalid MID)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_015">
                  </a> <!-- TC_015 -->
                  <span class="method-name">TC_015</span>
                  <em>
(Create a new lead without KYBID)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_016">
                  </a> <!-- TC_016 -->
                  <span class="method-name">TC_016</span>
                  <em>
(Create a new lead with INVALID KYBID)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_017">
                  </a> <!-- TC_017 -->
                  <span class="method-name">TC_017</span>
                  <em>
(Create a new lead withOUT WORKFLOW VERSION)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_018">
                  </a> <!-- TC_018 -->
                  <span class="method-name">TC_018</span>
                  <em>
(Create a new lead with INVALID WORKFLOW VERSION)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_019">
                  </a> <!-- TC_019 -->
                  <span class="method-name">TC_019</span>
                  <em>
(Create a new lead without service reason)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_020">
                  </a> <!-- TC_020 -->
                  <span class="method-name">TC_020</span>
                  <em>
(Create a new lead without version)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="TC_022">
                  </a> <!-- TC_022 -->
                  <span class="method-name">TC_022</span>
                  <em>
(Create a new lead without checksum)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="test">
                  </a> <!-- test -->
                  <span class="method-name">test</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Devices___Onboarding_Engine-class-passed -->
          <div class="suite-Devices___Onboarding_Engine-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">OCL.UAD.AddUADPincode</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="AddingMultiplePINSecondary">
                  </a> <!-- AddingMultiplePINSecondary -->
                  <span class="method-name">AddingMultiplePINSecondary</span>
                  <em>
(Fetching Multiple PIN with Secondary details)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="AddingMultiplePINs">
                  </a> <!-- AddingMultiplePINs -->
                  <span class="method-name">AddingMultiplePINs</span>
                  <em>
(Fetching Multiple PIN with Primary details)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="AddingSinglePINSecondary">
                  </a> <!-- AddingSinglePINSecondary -->
                  <span class="method-name">AddingSinglePINSecondary</span>
                  <em>
(Fetching Single PIN with Secondary details)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="AddingSinglePINs">
                  </a> <!-- AddingSinglePINs -->
                  <span class="method-name">AddingSinglePINs</span>
                  <em>
(Fetching Single PIN with Primary details)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="BlankPin">
                  </a> <!-- BlankPin -->
                  <span class="method-name">BlankPin</span>
                  <em>
(Blank pincode)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="InvalidPin">
                  </a> <!-- InvalidPin -->
                  <span class="method-name">InvalidPin</span>
                  <em>
(Invalid pincode)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="test">
                  </a> <!-- test -->
                  <span class="method-name">test</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Devices___Onboarding_Engine-class-passed -->
        </div> <!-- panel Devices___Onboarding_Engine -->
        <div panel-name="test-xml-Devices___Onboarding_Engine" class="panel">
          <div class="main-panel-header rounded-window-top">
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;http://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite thread-count=&quot;10&quot; parallel=&quot;classes&quot; name=&quot;Devices - Onboarding Engine&quot;&gt;
  &lt;test thread-count=&quot;10&quot; parallel=&quot;classes&quot; name=&quot;Devices&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;OCL.UAD.AddUADPincode&quot;/&gt;
      &lt;class name=&quot;OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr&quot;/&gt;
      &lt;class name=&quot;OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- Devices --&gt;
&lt;/suite&gt; &lt;!-- Devices - Onboarding Engine --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-Devices___Onboarding_Engine" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for Devices - Onboarding Engine</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">Devices (3 classes)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-Devices___Onboarding_Engine" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for Devices - Onboarding Engine</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="test-group">
              <span class="test-group-name">Regression</span>
              <br/>
              <div class="method-in-group">
                <span class="method-in-group-name">TC_001</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_002</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_003</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_004</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_005</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_006</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_007</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_008</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_009</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_010</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_011</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_012</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_013</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_014</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_015</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_016</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_017</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_018</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_019</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_020</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_021</span>
                <br/>
              </div> <!-- method-in-group -->
              <div class="method-in-group">
                <span class="method-in-group-name">TC_022</span>
                <br/>
              </div> <!-- method-in-group -->
            </div> <!-- test-group -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-Devices___Onboarding_Engine" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for Devices - Onboarding Engine</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_Devices___Onboarding_Engine');
function tableData_Devices___Onboarding_Engine() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(45);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'getStatusInCaseOfSuccessfullQrValidation')
data.setCell(0, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(0, 3, 4518);
data.setCell(1, 0, 1)
data.setCell(1, 1, 'getStatusInCaseOfInvalidOS')
data.setCell(1, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(1, 3, 4433);
data.setCell(2, 0, 2)
data.setCell(2, 1, 'getStatusInCaseOfInvalidDeviceType')
data.setCell(2, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(2, 3, 4276);
data.setCell(3, 0, 3)
data.setCell(3, 1, 'getStatusInCaseOfDeviceIDNotPassedInRequest')
data.setCell(3, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(3, 3, 3889);
data.setCell(4, 0, 4)
data.setCell(4, 1, 'getStatusInCaseOfDeviceBindingDoesNotExist')
data.setCell(4, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(4, 3, 3852);
data.setCell(5, 0, 5)
data.setCell(5, 1, 'getStatusInCaseOfDeviceBindedWithDifferentMid')
data.setCell(5, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(5, 3, 3728);
data.setCell(6, 0, 6)
data.setCell(6, 1, 'getStatusInCaseOfInvalidModel')
data.setCell(6, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(6, 3, 3698);
data.setCell(7, 0, 7)
data.setCell(7, 1, 'TC_001')
data.setCell(7, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(7, 3, 3578);
data.setCell(8, 0, 8)
data.setCell(8, 1, 'getStatusInCaseOfInvalidDeviceOEM')
data.setCell(8, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(8, 3, 3561);
data.setCell(9, 0, 9)
data.setCell(9, 1, 'getStatusInCaseOfLeadIdISNotvalid')
data.setCell(9, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(9, 3, 3507);
data.setCell(10, 0, 10)
data.setCell(10, 1, 'getStatusInCaseOfLeadIsNotPassedINRequest')
data.setCell(10, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(10, 3, 3399);
data.setCell(11, 0, 11)
data.setCell(11, 1, 'TC_015')
data.setCell(11, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(11, 3, 3108);
data.setCell(12, 0, 12)
data.setCell(12, 1, 'TC_021')
data.setCell(12, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(12, 3, 2993);
data.setCell(13, 0, 13)
data.setCell(13, 1, 'getStatusInCaseOfTokenIsNotPassedInRequest')
data.setCell(13, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(13, 3, 2972);
data.setCell(14, 0, 14)
data.setCell(14, 1, 'TC_008')
data.setCell(14, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(14, 3, 2967);
data.setCell(15, 0, 15)
data.setCell(15, 1, 'getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader')
data.setCell(15, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(15, 3, 2935);
data.setCell(16, 0, 16)
data.setCell(16, 1, 'TC_016')
data.setCell(16, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(16, 3, 2903);
data.setCell(17, 0, 17)
data.setCell(17, 1, 'getStatusInCaseOfVersionIsNotPassedInReq')
data.setCell(17, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(17, 3, 2808);
data.setCell(18, 0, 18)
data.setCell(18, 1, 'TC_014')
data.setCell(18, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(18, 3, 2523);
data.setCell(19, 0, 19)
data.setCell(19, 1, 'TC_020')
data.setCell(19, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(19, 3, 2445);
data.setCell(20, 0, 20)
data.setCell(20, 1, 'TC_009')
data.setCell(20, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(20, 3, 2290);
data.setCell(21, 0, 21)
data.setCell(21, 1, 'TC_005')
data.setCell(21, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(21, 3, 2232);
data.setCell(22, 0, 22)
data.setCell(22, 1, 'TC_012')
data.setCell(22, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(22, 3, 2232);
data.setCell(23, 0, 23)
data.setCell(23, 1, 'TC_013')
data.setCell(23, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(23, 3, 2224);
data.setCell(24, 0, 24)
data.setCell(24, 1, 'TC_004')
data.setCell(24, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(24, 3, 2220);
data.setCell(25, 0, 25)
data.setCell(25, 1, 'TC_017')
data.setCell(25, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(25, 3, 2194);
data.setCell(26, 0, 26)
data.setCell(26, 1, 'TC_010')
data.setCell(26, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(26, 3, 2132);
data.setCell(27, 0, 27)
data.setCell(27, 1, 'TC_011')
data.setCell(27, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(27, 3, 2126);
data.setCell(28, 0, 28)
data.setCell(28, 1, 'TC_018')
data.setCell(28, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(28, 3, 2123);
data.setCell(29, 0, 29)
data.setCell(29, 1, 'TC_019')
data.setCell(29, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(29, 3, 2039);
data.setCell(30, 0, 30)
data.setCell(30, 1, 'AddingMultiplePINs')
data.setCell(30, 2, 'OCL.UAD.AddUADPincode')
data.setCell(30, 3, 1968);
data.setCell(31, 0, 31)
data.setCell(31, 1, 'TC_007')
data.setCell(31, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(31, 3, 1896);
data.setCell(32, 0, 32)
data.setCell(32, 1, 'TC_006')
data.setCell(32, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(32, 3, 1853);
data.setCell(33, 0, 33)
data.setCell(33, 1, 'getStatusInCaseOfInvalidTokenIsPassedInRequest')
data.setCell(33, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(33, 3, 1754);
data.setCell(34, 0, 34)
data.setCell(34, 1, 'AddingSinglePINs')
data.setCell(34, 2, 'OCL.UAD.AddUADPincode')
data.setCell(34, 3, 1691);
data.setCell(35, 0, 35)
data.setCell(35, 1, 'AddingSinglePINSecondary')
data.setCell(35, 2, 'OCL.UAD.AddUADPincode')
data.setCell(35, 3, 1356);
data.setCell(36, 0, 36)
data.setCell(36, 1, 'TC_002')
data.setCell(36, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(36, 3, 1201);
data.setCell(37, 0, 37)
data.setCell(37, 1, 'InvalidPin')
data.setCell(37, 2, 'OCL.UAD.AddUADPincode')
data.setCell(37, 3, 1181);
data.setCell(38, 0, 38)
data.setCell(38, 1, 'AddingMultiplePINSecondary')
data.setCell(38, 2, 'OCL.UAD.AddUADPincode')
data.setCell(38, 3, 1164);
data.setCell(39, 0, 39)
data.setCell(39, 1, 'BlankPin')
data.setCell(39, 2, 'OCL.UAD.AddUADPincode')
data.setCell(39, 3, 1086);
data.setCell(40, 0, 40)
data.setCell(40, 1, 'TC_003')
data.setCell(40, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(40, 3, 1064);
data.setCell(41, 0, 41)
data.setCell(41, 1, 'TC_022')
data.setCell(41, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(41, 3, 910);
data.setCell(42, 0, 42)
data.setCell(42, 1, 'test')
data.setCell(42, 2, 'OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade')
data.setCell(42, 3, 344);
data.setCell(43, 0, 43)
data.setCell(43, 1, 'test')
data.setCell(43, 2, 'OCL.UAD.AddUADPincode')
data.setCell(43, 3, 343);
data.setCell(44, 0, 44)
data.setCell(44, 1, 'test')
data.setCell(44, 2, 'OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr')
data.setCell(44, 3, 5);
window.suiteTableData['Devices___Onboarding_Engine']= { tableData: data, tableDiv: 'times-div-Devices___Onboarding_Engine'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 1 minutes</span>
              <div id="times-div-Devices___Onboarding_Engine">
              </div> <!-- times-div-Devices___Onboarding_Engine -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-Devices___Onboarding_Engine" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for Devices - Onboarding Engine</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_004</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_004 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_010</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_010 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_011</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_011 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_016</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_016 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_007</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_007 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_006</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_006 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_008</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_008 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">test</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">test = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">AddingMultiplePINs</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">AddingMultiplePINs = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">test</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">test = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_005</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_005 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_017</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_017 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_013</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_013 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfDeviceBindedWithDifferentMid</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfDeviceBindedWithDifferentMid = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfLeadIsNotPassedINRequest</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfLeadIsNotPassedINRequest = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_018</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_018 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">AddingSinglePINs</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">AddingSinglePINs = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_001</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_001 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_002</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_002 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfDeviceIDNotPassedInRequest</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfDeviceIDNotPassedInRequest = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_022</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_022 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_015</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_015 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_012</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_012 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">InvalidPin</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">InvalidPin = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfVersionIsNotPassedInReq</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfVersionIsNotPassedInReq = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfTokenIsNotPassedInRequest</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfTokenIsNotPassedInRequest = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_003</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_003 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfDeviceBindingDoesNotExist</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfDeviceBindingDoesNotExist = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">AddingSinglePINSecondary</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">AddingSinglePINSecondary = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_019</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_019 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_020</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_020 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">AddingMultiplePINSecondary</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">AddingMultiplePINSecondary = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">test</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">test = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfInvalidDeviceOEM</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfInvalidDeviceOEM = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_014</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_014 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">BlankPin</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">BlankPin = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_009</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_009 = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfInvalidTokenIsPassedInRequest</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfInvalidTokenIsPassedInRequest = [Pass]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfInvalidModel</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfInvalidModel = [Fail]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">TC_021</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">TC_021 = [Fail]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfLeadIdISNotvalid</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfLeadIdISNotvalid = [Fail]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfInvalidOS</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfInvalidOS = [Fail]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfInvalidDeviceType</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfInvalidDeviceType = [Fail]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
            <div class="reporter-method-div">
              <span class="reporter-method-name">getStatusInCaseOfSuccessfullQrValidation</span>
              <div class="reporter-method-output-div">
                <span class="reporter-method-output">getStatusInCaseOfSuccessfullQrValidation = [Fail]<br></span>
              </div> <!-- reporter-method-output-div -->
            </div> <!-- reporter-method-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-Devices___Onboarding_Engine" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">0 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-Devices___Onboarding_Engine" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.UAD.AddUADPincode</div> <!-- chronological-class-name -->
              <div class="configuration-test before">
                <span class="method-name">AgentLogin</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-test before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="configuration-test before">
                <span class="method-name">AgentLogin</span>
                <span class="method-start">4753 ms</span>
              </div> <!-- configuration-test before -->
              <div class="test-method">
                <span class="method-name">test</span>
                <span class="method-start">10705 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">10706 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.UAD.AddUADPincode</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">test</span>
                <span class="method-start">10706 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_001</span>
                <span class="method-start">11079 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">test</span>
                <span class="method-start">14602 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">14617 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_002</span>
                <span class="method-start">14660 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">TC_003</span>
                <span class="method-start">15865 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfDeviceBindedWithDifferentMid</span>
                <span class="method-start">16818 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_004</span>
                <span class="method-start">16930 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">TC_005</span>
                <span class="method-start">19152 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">20548 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_006</span>
                <span class="method-start">21386 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfDeviceBindingDoesNotExist</span>
                <span class="method-start">22471 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_007</span>
                <span class="method-start">23241 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">TC_008</span>
                <span class="method-start">25140 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">26328 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_009</span>
                <span class="method-start">28111 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfDeviceIDNotPassedInRequest</span>
                <span class="method-start">29201 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_010</span>
                <span class="method-start">30403 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">TC_011</span>
                <span class="method-start">32537 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">33091 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_012</span>
                <span class="method-start">34664 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfInvalidDeviceOEM</span>
                <span class="method-start">34902 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_013</span>
                <span class="method-start">36897 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">38467 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_014</span>
                <span class="method-start">39123 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">TC_015</span>
                <span class="method-start">41647 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">getStatusInCaseOfInvalidDeviceType</span>
                <span class="method-start">42043 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_016</span>
                <span class="method-start">44762 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">46366 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_017</span>
                <span class="method-start">47666 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">getStatusInCaseOfInvalidModel</span>
                <span class="method-start">48131 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_018</span>
                <span class="method-start">49866 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">51839 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_019</span>
                <span class="method-start">51991 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">getStatusInCaseOfInvalidOS</span>
                <span class="method-start">53965 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_020</span>
                <span class="method-start">54032 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">TC_021</span>
                <span class="method-start">56479 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">58404 ms</span>
              </div> <!-- configuration-method before -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_021</span>
                <span class="method-start">58582 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfInvalidTokenIsPassedInRequest</span>
                <span class="method-start">60230 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">TC_021</span>
                <span class="method-start">60764 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">61986 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">getStatusInCaseOfLeadIdISNotvalid</span>
                <span class="method-start">63724 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">TC_022</span>
                <span class="method-start">63759 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">67258 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfLeadIsNotPassedINRequest</span>
                <span class="method-start">70707 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">74109 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader</span>
                <span class="method-start">76378 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">79317 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">getStatusInCaseOfSuccessfullQrValidation</span>
                <span class="method-start">81169 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">85696 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfTokenIsNotPassedInRequest</span>
                <span class="method-start">87636 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">agentToken</span>
                <span class="method-start">90610 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">getStatusInCaseOfVersionIsNotPassedInReq</span>
                <span class="method-start">92382 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">OCL.UAD.AddUADPincode</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">AddingMultiplePINs</span>
                <span class="method-start">95198 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">AddingSinglePINs</span>
                <span class="method-start">97175 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">AddingMultiplePINSecondary</span>
                <span class="method-start">98868 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">AddingSinglePINSecondary</span>
                <span class="method-start">100034 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">InvalidPin</span>
                <span class="method-start">101396 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">BlankPin</span>
                <span class="method-start">102583 ms</span>
              </div> <!-- test-method -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
</html>
