<?xml version="1.0" encoding="UTF-8"?>
<testng-results ignored="0" total="45" passed="39" failed="6" skipped="0">
  <reporter-output>
    <line>
      <![CDATA[test = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[test = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[test = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_001 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_002 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_003 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_004 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfDeviceBindedWithDifferentMid = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_005 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_006 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_007 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfDeviceBindingDoesNotExist = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_008 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_009 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_010 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfDeviceIDNotPassedInRequest = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_011 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_012 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfInvalidDeviceOEM = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_013 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_014 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_015 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfInvalidDeviceType = [Fail]<br>]]>
    </line>
    <line>
      <![CDATA[TC_016 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_017 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfInvalidModel = [Fail]<br>]]>
    </line>
    <line>
      <![CDATA[TC_018 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_019 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_020 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfInvalidOS = [Fail]<br>]]>
    </line>
    <line>
      <![CDATA[TC_021 = [Skip]<br>]]>
    </line>
    <line>
      <![CDATA[TC_021 = [Skip]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfInvalidTokenIsPassedInRequest = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[TC_021 = [Fail]<br>]]>
    </line>
    <line>
      <![CDATA[TC_022 = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfLeadIdISNotvalid = [Fail]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfLeadIsNotPassedINRequest = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfSuccessfullQrValidation = [Fail]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfTokenIsNotPassedInRequest = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[getStatusInCaseOfVersionIsNotPassedInReq = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[AddingMultiplePINs = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[AddingSinglePINs = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[AddingMultiplePINSecondary = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[AddingSinglePINSecondary = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[InvalidPin = [Pass]<br>]]>
    </line>
    <line>
      <![CDATA[BlankPin = [Pass]<br>]]>
    </line>
  </reporter-output>
  <suite started-at="2024-08-23T20:09:45 IST" name="Devices - Onboarding Engine" finished-at="2024-08-23T20:11:28 IST" duration-ms="103683">
    <groups>
      <group name="Regression">
        <method signature="V2EDCDeviceUpgrade.TC_001()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_001" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_002()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_002" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_003()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_003" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_004()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_004" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_005()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_005" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_006()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_006" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_007()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_007" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_008()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_008" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_009()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_009" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_010()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_010" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_011()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_011" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_012()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_012" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_013()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_013" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_014()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_014" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_015()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_015" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_016()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_016" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_017()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_017" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_018()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_018" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_019()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_019" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_020()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_020" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_021()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_021" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
        <method signature="V2EDCDeviceUpgrade.TC_022()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" name="TC_022" class="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade"/>
      </group> <!-- Regression -->
    </groups>
    <test started-at="2024-08-23T20:09:45 IST" name="Devices" finished-at="2024-08-23T20:11:28 IST" duration-ms="103683">
      <class name="OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr">
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:09:55 IST" name="agentToken" finished-at="2024-08-23T20:09:59 IST" duration-ms="3885" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="test()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:09:59 IST" name="test" finished-at="2024-08-23T20:09:59 IST" duration-ms="5" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[test = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- test -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:09:59 IST" name="agentToken" finished-at="2024-08-23T20:10:02 IST" duration-ms="2200" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfDeviceBindedWithDifferentMid()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:02 IST" name="getStatusInCaseOfDeviceBindedWithDifferentMid" finished-at="2024-08-23T20:10:05 IST" duration-ms="3728" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfDeviceBindedWithDifferentMid = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfDeviceBindedWithDifferentMid -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:05 IST" name="agentToken" finished-at="2024-08-23T20:10:07 IST" duration-ms="1922" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfDeviceBindingDoesNotExist()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:07 IST" name="getStatusInCaseOfDeviceBindingDoesNotExist" finished-at="2024-08-23T20:10:11 IST" duration-ms="3852" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfDeviceBindingDoesNotExist = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfDeviceBindingDoesNotExist -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:11 IST" name="agentToken" finished-at="2024-08-23T20:10:14 IST" duration-ms="2871" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfDeviceIDNotPassedInRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:14 IST" name="getStatusInCaseOfDeviceIDNotPassedInRequest" finished-at="2024-08-23T20:10:18 IST" duration-ms="3889" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfDeviceIDNotPassedInRequest = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfDeviceIDNotPassedInRequest -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:18 IST" name="agentToken" finished-at="2024-08-23T20:10:20 IST" duration-ms="1811" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfInvalidDeviceOEM()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:20 IST" name="getStatusInCaseOfInvalidDeviceOEM" finished-at="2024-08-23T20:10:23 IST" duration-ms="3561" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfInvalidDeviceOEM = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfInvalidDeviceOEM -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:23 IST" name="agentToken" finished-at="2024-08-23T20:10:27 IST" duration-ms="3575" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfInvalidDeviceType()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:27 IST" name="getStatusInCaseOfInvalidDeviceType" finished-at="2024-08-23T20:10:31 IST" duration-ms="4276" status="FAIL">
          <exception class="java.lang.AssertionError">
            <message>
              <![CDATA[did not expect to find [200] but found [400]]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidDeviceType(TestDeviceUpgradevalidateEDCQr.java:446)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.AssertionError -->
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfInvalidDeviceType = [Fail]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfInvalidDeviceType -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:31 IST" name="agentToken" finished-at="2024-08-23T20:10:33 IST" duration-ms="1765" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfInvalidModel()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:33 IST" name="getStatusInCaseOfInvalidModel" finished-at="2024-08-23T20:10:37 IST" duration-ms="3698" status="FAIL">
          <exception class="java.lang.AssertionError">
            <message>
              <![CDATA[did not expect to find [200] but found [400]]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidModel(TestDeviceUpgradevalidateEDCQr.java:387)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.AssertionError -->
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfInvalidModel = [Fail]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfInvalidModel -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:37 IST" name="agentToken" finished-at="2024-08-23T20:10:39 IST" duration-ms="2117" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfInvalidOS()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:39 IST" name="getStatusInCaseOfInvalidOS" finished-at="2024-08-23T20:10:43 IST" duration-ms="4433" status="FAIL">
          <exception class="java.lang.AssertionError">
            <message>
              <![CDATA[did not expect to find [200] but found [400]]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfInvalidOS(TestDeviceUpgradevalidateEDCQr.java:504)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.AssertionError -->
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfInvalidOS = [Fail]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfInvalidOS -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:43 IST" name="agentToken" finished-at="2024-08-23T20:10:45 IST" duration-ms="1821" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfInvalidTokenIsPassedInRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:45 IST" name="getStatusInCaseOfInvalidTokenIsPassedInRequest" finished-at="2024-08-23T20:10:47 IST" duration-ms="1754" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfInvalidTokenIsPassedInRequest = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfInvalidTokenIsPassedInRequest -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:47 IST" name="agentToken" finished-at="2024-08-23T20:10:48 IST" duration-ms="1734" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfLeadIdISNotvalid()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:48 IST" name="getStatusInCaseOfLeadIdISNotvalid" finished-at="2024-08-23T20:10:52 IST" duration-ms="3507" status="FAIL">
          <exception class="java.lang.AssertionError">
            <message>
              <![CDATA[did not expect to find [500] but found [400]]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfLeadIdISNotvalid(TestDeviceUpgradevalidateEDCQr.java:273)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.AssertionError -->
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfLeadIdISNotvalid = [Fail]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfLeadIdISNotvalid -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:52 IST" name="agentToken" finished-at="2024-08-23T20:10:55 IST" duration-ms="3449" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfLeadIsNotPassedINRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:55 IST" name="getStatusInCaseOfLeadIsNotPassedINRequest" finished-at="2024-08-23T20:10:59 IST" duration-ms="3399" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfLeadIsNotPassedINRequest = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfLeadIsNotPassedINRequest -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:10:59 IST" name="agentToken" finished-at="2024-08-23T20:11:01 IST" duration-ms="2266" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:11:01 IST" name="getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader" finished-at="2024-08-23T20:11:04 IST" duration-ms="2935" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfRequestIsTemperedWithoutAuthorizationHeader -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:11:04 IST" name="agentToken" finished-at="2024-08-23T20:11:06 IST" duration-ms="1850" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfSuccessfullQrValidation()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:11:06 IST" name="getStatusInCaseOfSuccessfullQrValidation" finished-at="2024-08-23T20:11:10 IST" duration-ms="4518" status="FAIL">
          <exception class="java.lang.AssertionError">
            <message>
              <![CDATA[did not expect to find [200] but found [400]]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr.getStatusInCaseOfSuccessfullQrValidation(TestDeviceUpgradevalidateEDCQr.java:97)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.AssertionError -->
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfSuccessfullQrValidation = [Fail]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfSuccessfullQrValidation -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:11:10 IST" name="agentToken" finished-at="2024-08-23T20:11:12 IST" duration-ms="1939" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfTokenIsNotPassedInRequest()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:11:12 IST" name="getStatusInCaseOfTokenIsNotPassedInRequest" finished-at="2024-08-23T20:11:15 IST" duration-ms="2972" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfTokenIsNotPassedInRequest = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfTokenIsNotPassedInRequest -->
        <test-method is-config="true" signature="agentToken()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:11:15 IST" name="agentToken" finished-at="2024-08-23T20:11:17 IST" duration-ms="1772" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- agentToken -->
        <test-method signature="getStatusInCaseOfVersionIsNotPassedInReq()[pri:0, instance:OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr@733fb462]" started-at="2024-08-23T20:11:17 IST" name="getStatusInCaseOfVersionIsNotPassedInReq" finished-at="2024-08-23T20:11:20 IST" duration-ms="2808" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[getStatusInCaseOfVersionIsNotPassedInReq = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- getStatusInCaseOfVersionIsNotPassedInReq -->
      </class> <!-- OCL.EDCDeviceUpgradeV2.TestDeviceUpgradevalidateEDCQr -->
      <class name="OCL.UAD.AddUADPincode">
        <test-method is-config="true" signature="AgentLogin()[pri:0, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:09:45 IST" name="AgentLogin" finished-at="2024-08-23T20:09:49 IST" duration-ms="4751" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- AgentLogin -->
        <test-method signature="test()[pri:0, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:09:55 IST" name="test" finished-at="2024-08-23T20:09:56 IST" duration-ms="343" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[test = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- test -->
        <test-method signature="AddingMultiplePINs()[pri:1, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:11:20 IST" name="AddingMultiplePINs" description="Fetching Multiple PIN with Primary details" finished-at="2024-08-23T20:11:22 IST" duration-ms="1968" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[AddingMultiplePINs = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- AddingMultiplePINs -->
        <test-method signature="AddingSinglePINs()[pri:2, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:11:22 IST" name="AddingSinglePINs" description="Fetching Single PIN with Primary details" finished-at="2024-08-23T20:11:24 IST" duration-ms="1691" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[AddingSinglePINs = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- AddingSinglePINs -->
        <test-method signature="AddingMultiplePINSecondary()[pri:3, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:11:24 IST" name="AddingMultiplePINSecondary" description="Fetching Multiple PIN with Secondary details" finished-at="2024-08-23T20:11:25 IST" duration-ms="1164" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[AddingMultiplePINSecondary = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- AddingMultiplePINSecondary -->
        <test-method signature="AddingSinglePINSecondary()[pri:4, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:11:25 IST" name="AddingSinglePINSecondary" description="Fetching Single PIN with Secondary details" finished-at="2024-08-23T20:11:26 IST" duration-ms="1356" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[AddingSinglePINSecondary = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- AddingSinglePINSecondary -->
        <test-method signature="InvalidPin()[pri:5, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:11:26 IST" name="InvalidPin" description="Invalid pincode" finished-at="2024-08-23T20:11:27 IST" duration-ms="1181" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[InvalidPin = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- InvalidPin -->
        <test-method signature="BlankPin()[pri:6, instance:OCL.UAD.AddUADPincode@6c977dcf]" started-at="2024-08-23T20:11:27 IST" name="BlankPin" description="Blank pincode" finished-at="2024-08-23T20:11:28 IST" duration-ms="1086" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[BlankPin = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- BlankPin -->
      </class> <!-- OCL.UAD.AddUADPincode -->
      <class name="OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade">
        <test-method is-config="true" signature="AgentLogin()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:09:49 IST" name="AgentLogin" finished-at="2024-08-23T20:09:55 IST" duration-ms="5899" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- AgentLogin -->
        <test-method signature="test()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:09:55 IST" name="test" finished-at="2024-08-23T20:09:56 IST" duration-ms="344" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[test = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- test -->
        <test-method signature="TC_001()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:09:56 IST" name="TC_001" description="Create a new lead with all valid details" finished-at="2024-08-23T20:09:59 IST" duration-ms="3578" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_001 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_001 -->
        <test-method signature="TC_002()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:09:59 IST" name="TC_002" description="Create a new lead without token" finished-at="2024-08-23T20:10:01 IST" duration-ms="1201" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_002 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_002 -->
        <test-method signature="TC_003()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:01 IST" name="TC_003" description="Create a new lead with invalid token" finished-at="2024-08-23T20:10:02 IST" duration-ms="1064" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_003 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_003 -->
        <test-method signature="TC_004()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:02 IST" name="TC_004" description="Create a new lead without param" finished-at="2024-08-23T20:10:04 IST" duration-ms="2220" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_004 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_004 -->
        <test-method signature="TC_005()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:04 IST" name="TC_005" description="Create a new lead with inavlid param" finished-at="2024-08-23T20:10:06 IST" duration-ms="2232" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_005 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_005 -->
        <test-method signature="TC_006()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:06 IST" name="TC_006" description="Create a new lead without entity type" finished-at="2024-08-23T20:10:08 IST" duration-ms="1853" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_006 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_006 -->
        <test-method signature="TC_007()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:08 IST" name="TC_007" description="Create a new lead with invalid entity type" finished-at="2024-08-23T20:10:10 IST" duration-ms="1896" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_007 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_007 -->
        <test-method signature="TC_008()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:10 IST" name="TC_008" description="Create a new lead without user custid" finished-at="2024-08-23T20:10:13 IST" duration-ms="2967" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_008 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_008 -->
        <test-method signature="TC_009()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:13 IST" name="TC_009" description="Create a new lead with invalid custid" finished-at="2024-08-23T20:10:15 IST" duration-ms="2290" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_009 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_009 -->
        <test-method signature="TC_010()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:15 IST" name="TC_010" description="Create a new lead without agent custid" finished-at="2024-08-23T20:10:17 IST" duration-ms="2132" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_010 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_010 -->
        <test-method signature="TC_011()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:17 IST" name="TC_011" description="Create a new lead with invalid agent custid" finished-at="2024-08-23T20:10:19 IST" duration-ms="2126" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_011 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_011 -->
        <test-method signature="TC_012()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:19 IST" name="TC_012" description="Create a new lead without mobile number" finished-at="2024-08-23T20:10:22 IST" duration-ms="2232" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_012 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_012 -->
        <test-method signature="TC_013()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:22 IST" name="TC_013" description="Create a new lead with invalid mobile number" finished-at="2024-08-23T20:10:24 IST" duration-ms="2224" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_013 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_013 -->
        <test-method signature="TC_014()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:24 IST" name="TC_014" description="Create a new lead with invalid MID" finished-at="2024-08-23T20:10:26 IST" duration-ms="2523" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_014 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_014 -->
        <test-method signature="TC_015()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:26 IST" name="TC_015" description="Create a new lead without KYBID" finished-at="2024-08-23T20:10:29 IST" duration-ms="3108" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_015 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_015 -->
        <test-method signature="TC_016()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:29 IST" name="TC_016" description="Create a new lead with INVALID KYBID" finished-at="2024-08-23T20:10:32 IST" duration-ms="2903" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_016 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_016 -->
        <test-method signature="TC_017()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:32 IST" name="TC_017" description="Create a new lead withOUT WORKFLOW VERSION" finished-at="2024-08-23T20:10:35 IST" duration-ms="2194" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_017 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_017 -->
        <test-method signature="TC_018()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:35 IST" name="TC_018" description="Create a new lead with INVALID WORKFLOW VERSION" finished-at="2024-08-23T20:10:37 IST" duration-ms="2123" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_018 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_018 -->
        <test-method signature="TC_019()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:37 IST" name="TC_019" description="Create a new lead without service reason" finished-at="2024-08-23T20:10:39 IST" duration-ms="2039" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_019 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_019 -->
        <test-method signature="TC_020()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:39 IST" name="TC_020" description="Create a new lead without version" finished-at="2024-08-23T20:10:41 IST" duration-ms="2445" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_020 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_020 -->
        <test-method signature="TC_021()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:45 IST" name="TC_021" description="Create a new lead without device identifier" finished-at="2024-08-23T20:10:48 IST" duration-ms="2993" status="FAIL">
          <exception class="java.lang.AssertionError">
            <message>
              <![CDATA[did not expect to find [410] but found [200]]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.AssertionError: did not expect to find [410] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade.TC_021(V2EDCDeviceUpgrade.java:903)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:568)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.retryFailed(Invoker.java:926)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1127)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
at java.base/java.lang.Thread.run(Thread.java:833)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.AssertionError -->
          <reporter-output>
            <line>
              <![CDATA[TC_021 = [Fail]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_021 -->
        <test-method signature="TC_022()[pri:0, instance:OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade@661d6bb6]" started-at="2024-08-23T20:10:48 IST" name="TC_022" description="Create a new lead without checksum" finished-at="2024-08-23T20:10:49 IST" duration-ms="910" status="PASS">
          <reporter-output>
            <line>
              <![CDATA[TC_022 = [Pass]<br>]]>
            </line>
          </reporter-output>
        </test-method> <!-- TC_022 -->
      </class> <!-- OCL.EDCDeviceUpgradeV2.V2EDCDeviceUpgrade -->
    </test> <!-- Devices -->
  </suite> <!-- Devices - Onboarding Engine -->
</testng-results>
