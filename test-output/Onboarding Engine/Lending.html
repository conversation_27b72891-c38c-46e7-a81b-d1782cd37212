<html>
<head>
<title>TestNG:  Lending</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Lending</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>160/3/14</td>
</tr><tr>
<td>Started on:</td><td>Wed Jul 27 18:50:16 IST 2022</td>
</tr>
<tr><td>Total time:</td><td>266 seconds (266704 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC021_SaveBankDetails()'><b>TC021_SaveBankDetails</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Enter the bank details<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC021_SaveBankDetails = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [200] but found [202]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC021_SaveBankDetails(TestMCAFullertonV3Workflow.java:1042)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace**********", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace**********'><pre>java.lang.AssertionError: did not expect to find [200] but found [202]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:839)
	at org.testng.Assert.assertEquals(Assert.java:849)
	at OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC021_SaveBankDetails(TestMCAFullertonV3Workflow.java:1042)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>1</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC029_PLv3HERO_PDCCallback()'><b>TC029_PLv3HERO_PDCCallback</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Hero Verify PDC Callback<br/>
<a href="#Output-1479192488" onClick='toggleBox("Output-1479192488", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1479192488" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1479192488">
TC029_PLv3HERO_PDCCallback = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [LOAN_AGREEMENT_SUCCESS]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:575)
	at org.testng.Assert.assertEquals(Assert.java:585)
	at OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC029_PLv3HERO_PDCCallback(PersonalLoanRenewal.java:1190)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace1479192488", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1479192488'><pre>java.lang.AssertionError: did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [LOAN_AGREEMENT_SUCCESS]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:575)
	at org.testng.Assert.assertEquals(Assert.java:585)
	at OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC029_PLv3HERO_PDCCallback(PersonalLoanRenewal.java:1190)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>8</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC042PLV3HERO_SubmitApplicationLMSApprovedCallback()'><b>TC042PLV3HERO_SubmitApplicationLMSApprovedCallback</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Hero Verify if LMS Approve Callback required<br/>
<a href="#Output-840218042" onClick='toggleBox("Output-840218042", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-840218042" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-840218042">
TC042PLV3HERO_SubmitApplicationLMSApprovedCallback = [Fail]<br><br/>
</div>
</td>
<td><div><pre>java.lang.AssertionError: did not expect to find [LMS_CALLBACK_SUCCESS] but found [LOAN_AGREEMENT_SUCCESS]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:575)
	at org.testng.Assert.assertEquals(Assert.java:585)
	at OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC042PLV3HERO_SubmitApplicationLMSApprovedCallback(TestPLABFLOneClick.java:1557)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace840218042", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace840218042'><pre>java.lang.AssertionError: did not expect to find [LMS_CALLBACK_SUCCESS] but found [LOAN_AGREEMENT_SUCCESS]
	at org.testng.Assert.fail(Assert.java:97)
	at org.testng.Assert.failNotEquals(Assert.java:969)
	at org.testng.Assert.assertEqualsImpl(Assert.java:136)
	at org.testng.Assert.assertEquals(Assert.java:118)
	at org.testng.Assert.assertEquals(Assert.java:575)
	at org.testng.Assert.assertEquals(Assert.java:585)
	at OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC042PLV3HERO_SubmitApplicationLMSApprovedCallback(TestPLABFLOneClick.java:1557)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC001_FetchLeadDeatils()'><b>TC001_FetchLeadDeatils</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify whether there is any existing Renewal lead present or not<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC001_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC001_FetchLeadDeatils()'><b>TC001_FetchLeadDeatils</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: Verify whether there is any existing stashfin lead present or not<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC001_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC001_FetchLeadDeatils()'><b>TC001_FetchLeadDeatils</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify whether there is any existing stashfin lead present or not<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC001_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC001_FetchLeadDeatils()'><b>TC001_FetchLeadDeatils</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Verify whether there is any existing Home First lead present or not<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC001_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC001_FetchLeadDeatils()'><b>TC001_FetchLeadDeatils</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify whether there is any existing stashfin lead present or not<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC001_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC001_PLv3ABFL_fetchlLead()'><b>TC001_PLv3ABFL_fetchlLead</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify if there is any existing Personal Loan Migration ABFL Lead<br/>
<a href="#Output-518532395" onClick='toggleBox("Output-518532395", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-518532395" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-518532395">
TC001_PLv3ABFL_fetchlLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC002_DeleteExistingLead()'><b>TC002_DeleteExistingLead</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC002_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC002_DeleteExistingLead()'><b>TC002_DeleteExistingLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC002_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC002_DeleteExistingLead()'><b>TC002_DeleteExistingLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC002_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC002_DeleteExistingLead()'><b>TC002_DeleteExistingLead</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC002_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC002_DeleteExistingLead()'><b>TC002_DeleteExistingLead</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC002_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC002_PLv3ABFL_DeleteExistingLead()'><b>TC002_PLv3ABFL_DeleteExistingLead</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify reseting existing Personal Loan Migration ABFL lead<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC002_PLv3ABFL_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC003_CreateBTDistributionPiramalLead()'><b>TC003_CreateBTDistributionPiramalLead</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC003_CreateBTDistributionPiramalLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC003_CreateCitiBankLead()'><b>TC003_CreateCitiBankLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC003_CreateCitiBankLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC003_CreateCitiBankLead_WithoutPassingBaseId()'><b>TC003_CreateCitiBankLead_WithoutPassingBaseId</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC003_CreateCitiBankLead_WithoutPassingBaseId = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC003_CreateHomeFirstLead()'><b>TC003_CreateHomeFirstLead</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC003_CreateHomeFirstLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC003_CreatePLRenewalLead()'><b>TC003_CreatePLRenewalLead</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Create PL Renewal Hero Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC003_CreatePLRenewalLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC003_PLv3ABFL_CreateLead()'><b>TC003_PLv3ABFL_CreateLead</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Create Lead for Personal Loan Migeration ABFL<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC003_PLv3ABFL_CreateLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC004_CreateCitiBankLead_WithoutPassingProductId()'><b>TC004_CreateCitiBankLead_WithoutPassingProductId</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC004_CreateCitiBankLead_WithoutPassingProductId = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC004_FetchLeadAllData()'><b>TC004_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC004_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC004_FetchLeadAllData()'><b>TC004_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC004_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC004_FetchLeadAllData()'><b>TC004_FetchLeadAllData</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC004_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC004_PLV3ABFL_FetchLeadAllData()'><b>TC004_PLV3ABFL_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PL v3 ABFL lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC004_PLV3ABFL_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC004_UpdateLeadBasicDetails()'><b>TC004_UpdateLeadBasicDetails</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Update lead basic details<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC004_UpdateLeadBasicDetails = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC005_CreateCitiBankLead_WithoutPassingProductVersion()'><b>TC005_CreateCitiBankLead_WithoutPassingProductVersion</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC005_CreateCitiBankLead_WithoutPassingProductVersion = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC005_LISCallbackToLoanAccepted()'><b>TC005_LISCallbackToLoanAccepted</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: LIS Callback to Loan Accepted-->243 Stage<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC005_LISCallbackToLoanAccepted = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC005_PLV3ABFL_UpdateLeadOccupationDetails()'><b>TC005_PLV3ABFL_UpdateLeadOccupationDetails</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Update lead occupation details<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC005_PLV3ABFL_UpdateLeadOccupationDetails = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC005_UpdateBureauDataSetInSAI()'><b>TC005_UpdateBureauDataSetInSAI</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC005_UpdateBureauDataSetInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC005_UpdateBureauDataSetInSAI()'><b>TC005_UpdateBureauDataSetInSAI</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-1438511884" onClick='toggleBox("Output-1438511884", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1438511884" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1438511884">
TC005_UpdateBureauDataSetInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC005_UpdateLeadBasicDetails()'><b>TC005_UpdateLeadBasicDetails</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Update lead basic details<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC005_UpdateLeadBasicDetails = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC006_CreateCitiBankLead_WithoutPassingProductType()'><b>TC006_CreateCitiBankLead_WithoutPassingProductType</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC006_CreateCitiBankLead_WithoutPassingProductType = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC006_FetchCIR()'><b>TC006_FetchCIR</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Fetch CIR<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC006_FetchCIR = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC006_FetchCIR()'><b>TC006_FetchCIR</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Fetch CIR<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC006_FetchCIR = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC006_FetchLeadAllData()'><b>TC006_FetchLeadAllData</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC006_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC006_LISCallbackToLoanProcessingError()'><b>TC006_LISCallbackToLoanProcessingError</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: LIS Callback to Loan Processing error from Loan Accepted-->243 Stage<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC006_LISCallbackToLoanProcessingError = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate()'><b>TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PLv3 lead data after updating Occupation Details<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC007_CreateCitiBankLead_WithoutPassingLenderId()'><b>TC007_CreateCitiBankLead_WithoutPassingLenderId</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC007_CreateCitiBankLead_WithoutPassingLenderId = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC007_FetchLeadAllData()'><b>TC007_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC007_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC007_FetchLeadAllData()'><b>TC007_FetchLeadAllData</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC007_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>7</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC007_LISCallbackToLoanDisbursedFromLoanProcessingError()'><b>TC007_LISCallbackToLoanDisbursedFromLoanProcessingError</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: LIS Callback to Loan Disbursed-->237 Stage<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC007_LISCallbackToLoanDisbursedFromLoanProcessingError = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC007_PLV3ABFL_UpdateLeadDetailsinSAI()'><b>TC007_PLV3ABFL_UpdateLeadDetailsinSAI</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Update lead details in SAI<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC007_PLV3ABFL_UpdateLeadDetailsinSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC007_UpdateBureauDataSetInSAI()'><b>TC007_UpdateBureauDataSetInSAI</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC007_UpdateBureauDataSetInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC008_CreateCitiBankLead_WithoutPassingRiskSegment()'><b>TC008_CreateCitiBankLead_WithoutPassingRiskSegment</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC008_CreateCitiBankLead_WithoutPassingRiskSegment = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC008_FetchCIR()'><b>TC008_FetchCIR</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Fetch CIR<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC008_FetchCIR = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC008_FetchLeadAllData()'><b>TC008_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC008_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC008_FetchLeadAllData()'><b>TC008_FetchLeadAllData</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC008_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC008_PLv3ABFL_FetchDataPostSAIlUpdate()'><b>TC008_PLv3ABFL_FetchDataPostSAIlUpdate</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PLv3 lead data after Updating details in SAI<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC008_PLv3ABFL_FetchDataPostSAIlUpdate = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC008_UpdateExistingDetailsInSAI()'><b>TC008_UpdateExistingDetailsInSAI</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC008_UpdateExistingDetailsInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC009_CreateCitiBankLead_WithoutPassingFlowType()'><b>TC009_CreateCitiBankLead_WithoutPassingFlowType</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC009_CreateCitiBankLead_WithoutPassingFlowType = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC009_FetchLeadAllData()'><b>TC009_FetchLeadAllData</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC009_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>4</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC009_LMSDataCallback()'><b>TC009_LMSDataCallback</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: LMS Callback to move to node 240<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC009_LMSDataCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC009_PLRenewal_LoanOfferAccept()'><b>TC009_PLRenewal_LoanOfferAccept</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify PL Renewal lead Loan Offer Accepted<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC009_PLRenewal_LoanOfferAccept = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC009_PLv3ABFL_FetchCIR()'><b>TC009_PLv3ABFL_FetchCIR</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify Fetch CIR for PLv3 lead<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC009_PLv3ABFL_FetchCIR = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC009_UpdateExistingDetailsInSAI()'><b>TC009_UpdateExistingDetailsInSAI</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC009_UpdateExistingDetailsInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount()'><b>TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC010_FetchLeadAllData()'><b>TC010_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC010_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankFlow.TC010_FetchLeadAllData()'><b>TC010_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC010_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankFlow@164a62bf</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC010_PLv3ABFL_BRE1Callback()'><b>TC010_PLv3ABFL_BRE1Callback</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify lead details after Fetch CIR<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC010_PLv3ABFL_BRE1Callback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>4</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC010_UpdateAdditionalDetails()'><b>TC010_UpdateAdditionalDetails</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Update lead basic details<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC010_UpdateAdditionalDetails = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC010_UpdateExistingDetailsInSAI()'><b>TC010_UpdateExistingDetailsInSAI</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC010_UpdateExistingDetailsInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest()'><b>TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC011_FetchLeadAllData()'><b>TC011_FetchLeadAllData</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC011_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>10</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC011_PLv3ABFL_FetchDataPostBRE1Success()'><b>TC011_PLv3ABFL_FetchDataPostBRE1Success</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PLv3 lead data after BRE1 Success<br/>
<a href="#Output-483912455" onClick='toggleBox("Output-483912455", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-483912455" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-483912455">
TC011_PLv3ABFL_FetchDataPostBRE1Success = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC011_PLv3HERO_UploadCustomerPhoto()'><b>TC011_PLv3HERO_UploadCustomerPhoto</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the PLv3 lead Upload customer photo<br/>
<a href="#Output-1894103486" onClick='toggleBox("Output-1894103486", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1894103486" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1894103486">
TC011_PLv3HERO_UploadCustomerPhoto = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.HomeFirst.TC011_VerifyLeadStage()'><b>TC011_VerifyLeadStage</b><br>Test class: OCL.Lending.BusinessLending.HomeFirst<br>Test method: Update lead details<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC011_VerifyLeadStage = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.Lending.BusinessLending.HomeFirst@aaee2a2</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName()'><b>TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC012_PLv3ABFL_LoanOfferAccept()'><b>TC012_PLv3ABFL_LoanOfferAccept</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 ABFL lead Loan Offer Accepted<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC012_PLv3ABFL_LoanOfferAccept = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC012_PLv3HERO_VerifyUploadedCustomerPhoto()'><b>TC012_PLv3HERO_VerifyUploadedCustomerPhoto</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the  details of Uploaded Customer Photo<br/>
<a href="#Output-469232293" onClick='toggleBox("Output-469232293", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-469232293" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-469232293">
TC012_PLv3HERO_VerifyUploadedCustomerPhoto = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC012_UploadSelfie()'><b>TC012_UploadSelfie</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Upload selfie<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC012_UploadSelfie = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName()'><b>TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC013_PLv3ABFL_FetchDataPostLoanOfferAccept()'><b>TC013_PLv3ABFL_FetchDataPostLoanOfferAccept</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PLv3 lead data after Loan offer accept<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC013_PLv3ABFL_FetchDataPostLoanOfferAccept = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC013_PLv3HERO_UploadSelfie()'><b>TC013_PLv3HERO_UploadSelfie</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the PLv3 lead Upload Selfie <br/>
<a href="#Output-1619447519" onClick='toggleBox("Output-1619447519", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1619447519" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1619447519">
TC013_PLv3HERO_UploadSelfie = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>5</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC013_UploadCustomerPhoto()'><b>TC013_UploadCustomerPhoto</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Upload Customer Photo<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC013_UploadCustomerPhoto = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC014_CreateCitiBankLead_WithoutPassingApplicationId()'><b>TC014_CreateCitiBankLead_WithoutPassingApplicationId</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC014_CreateCitiBankLead_WithoutPassingApplicationId = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC014_PLV3ABFL_UpdateLeadDetailsinSAI()'><b>TC014_PLV3ABFL_UpdateLeadDetailsinSAI</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Update lead details in SAI<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC014_PLV3ABFL_UpdateLeadDetailsinSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC014_PLv3HERO_VerifyUploadedSelfie()'><b>TC014_PLv3HERO_VerifyUploadedSelfie</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the details of Uploaded Selfie<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC014_PLv3HERO_VerifyUploadedSelfie = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC015_CKYCCallback()'><b>TC015_CKYCCallback</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: CKYC Callback<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC015_CKYCCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC015_CreateCitiBankLead_WithoutPassingOfferURL()'><b>TC015_CreateCitiBankLead_WithoutPassingOfferURL</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC015_CreateCitiBankLead_WithoutPassingOfferURL = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan()'><b>TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Update lead details in SAI<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC015_VerifyUploadedDocument()'><b>TC015_VerifyUploadedDocument</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify the  details of Uploaded Document<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC015_VerifyUploadedDocument = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC016_CreateCitiBankLead_PassingInvalidSolutionName()'><b>TC016_CreateCitiBankLead_PassingInvalidSolutionName</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC016_CreateCitiBankLead_PassingInvalidSolutionName = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC016_PLv3ABFL_FetchDataPostLoanOfferAccept()'><b>TC016_PLv3ABFL_FetchDataPostLoanOfferAccept</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PLv3 lead data after Loan offer accept<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC016_PLv3ABFL_FetchDataPostLoanOfferAccept = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>10</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC016_UpdateSAIWithLenderDetails()'><b>TC016_UpdateSAIWithLenderDetails</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-808371440" onClick='toggleBox("Output-808371440", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-808371440" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-808371440">
TC016_UpdateSAIWithLenderDetails = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC016_VerifyLeadStage()'><b>TC016_VerifyLeadStage</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Update lead details<br/>
<a href="#Output-765645248" onClick='toggleBox("Output-765645248", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-765645248" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-765645248">
TC016_VerifyLeadStage = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>14</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC017_CKYCCallback()'><b>TC017_CKYCCallback</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: CKYC Callback<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC017_CKYCCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC017_CreateCitiBankLead_PassingInvalidChannelName()'><b>TC017_CreateCitiBankLead_PassingInvalidChannelName</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC017_CreateCitiBankLead_PassingInvalidChannelName = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC017_PLv3ABFL_FetchDataPostKYCIntiated()'><b>TC017_PLv3ABFL_FetchDataPostKYCIntiated</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify lead details after Fetch CIR<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC017_PLv3ABFL_FetchDataPostKYCIntiated = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC017_PLv3HERO_AdditionalIsRequiredorNot()'><b>TC017_PLv3HERO_AdditionalIsRequiredorNot</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify PL v3 Lead If Additional Data is required<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC017_PLv3HERO_AdditionalIsRequiredorNot = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2()'><b>TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2 = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC018_PLv3HERO_FetchLeadVerifyAdditionalData()'><b>TC018_PLv3HERO_FetchLeadVerifyAdditionalData</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify PL v3 Lead Additional Data is required<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC018_PLv3HERO_FetchLeadVerifyAdditionalData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC018_PLv3HERO_UploadCustomerPhoto()'><b>TC018_PLv3HERO_UploadCustomerPhoto</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PLv3 lead Upload customer photo<br/>
<a href="#Output-845778188" onClick='toggleBox("Output-845778188", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-845778188" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-845778188">
TC018_PLv3HERO_UploadCustomerPhoto = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>4</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC018_VerifyLeadStage()'><b>TC018_VerifyLeadStage</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Update lead details<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC018_VerifyLeadStage = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>3</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC019_CreateCitiBankLead_PassingInvalidEntityType()'><b>TC019_CreateCitiBankLead_PassingInvalidEntityType</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC019_CreateCitiBankLead_PassingInvalidEntityType = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC019_PLv3HERO_UpdateKYCNameInSAI()'><b>TC019_PLv3HERO_UpdateKYCNameInSAI</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify for PL v3 CKYC name update in SAI<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC019_PLv3HERO_UpdateKYCNameInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC019_PLv3HERO_VerifyUploadedCustomerPhoto()'><b>TC019_PLv3HERO_VerifyUploadedCustomerPhoto</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the  details of Uploaded Customer Photo<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC019_PLv3HERO_VerifyUploadedCustomerPhoto = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC020_CreateCitiBankLead()'><b>TC020_CreateCitiBankLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC020_CreateCitiBankLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI()'><b>TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify for PL v3 CKYC name update in SAI<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC020_PLv3HERO_UploadSelfie()'><b>TC020_PLv3HERO_UploadSelfie</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the PLv3 lead Upload customer photo<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC020_PLv3HERO_UploadSelfie = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>4</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC020_UpdateKYCNameInSAI()'><b>TC020_UpdateKYCNameInSAI</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: update kyc name for bank details<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC020_UpdateKYCNameInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC021_FetchLeadAllData()'><b>TC021_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC021_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC021_PLv3HERO_SaveBankDetails()'><b>TC021_PLv3HERO_SaveBankDetails</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify PL v3 Lead the bank details<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC021_PLv3HERO_SaveBankDetails = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC021_PLv3HERO_VerifyUploadedSelfie()'><b>TC021_PLv3HERO_VerifyUploadedSelfie</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the details of Uploaded Selfie<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC021_PLv3HERO_VerifyUploadedSelfie = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC022_CreateLeadAgain()'><b>TC022_CreateLeadAgain</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC022_CreateLeadAgain = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC022_PLv3ABFL_FetchDataPostKYCIntiated()'><b>TC022_PLv3ABFL_FetchDataPostKYCIntiated</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify lead details after Fetch CIR<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC022_PLv3ABFL_FetchDataPostKYCIntiated = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>10</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC022_PLv3HERO_UpdateKYCNameInSAI()'><b>TC022_PLv3HERO_UpdateKYCNameInSAI</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify for PL v3 CKYC name update in SAI<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC022_PLv3HERO_UpdateKYCNameInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC023_LISCallbackToLoanRejection()'><b>TC023_LISCallbackToLoanRejection</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Rejection-->245 Node<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC023_LISCallbackToLoanRejection = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC023_PLv3ABFL_FetchDataPostSelfieUploaded()'><b>TC023_PLv3ABFL_FetchDataPostSelfieUploaded</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify lead details after Fetch CIR<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC023_PLv3ABFL_FetchDataPostSelfieUploaded = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>15</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC023_PLv3HERO_EmandateCallback()'><b>TC023_PLv3HERO_EmandateCallback</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify PL v3 Lead Emandate Callback<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC023_PLv3HERO_EmandateCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC024_FetchLeadDeatils()'><b>TC024_FetchLeadDeatils</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify whether there is any existing stashfin lead present or not<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC024_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC024_PLv3ABFL_SecondBRECallback()'><b>TC024_PLv3ABFL_SecondBRECallback</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead Second BRE callback<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC024_PLv3ABFL_SecondBRECallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC024_PLv3HERO_FetchLeadPostEmandate()'><b>TC024_PLv3HERO_FetchLeadPostEmandate</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Emandate Callback Stage Verification<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC024_PLv3HERO_FetchLeadPostEmandate = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC025_DeleteExistingLead()'><b>TC025_DeleteExistingLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC025_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC025_PLv3ABFL_FetchDataAfterBRE2Success()'><b>TC025_PLv3ABFL_FetchDataAfterBRE2Success</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead After BRE2 success Callback<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC025_PLv3ABFL_FetchDataAfterBRE2Success = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC025_PLv3HERO_GenerateLoanAgreement()'><b>TC025_PLv3HERO_GenerateLoanAgreement</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Hero Generate Loan Agreement<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC025_PLv3HERO_GenerateLoanAgreement = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC026_CreateCitiBankLead()'><b>TC026_CreateCitiBankLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC026_CreateCitiBankLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC026_PLv3ABFL_AdditionalIsRequiredorNot()'><b>TC026_PLv3ABFL_AdditionalIsRequiredorNot</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead If Additional Data is required<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC026_PLv3ABFL_AdditionalIsRequiredorNot = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC026_PLv3HERO_GenerateSanctionLetter()'><b>TC026_PLv3HERO_GenerateSanctionLetter</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Hero Generate Sanction Letter<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC026_PLv3HERO_GenerateSanctionLetter = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC027_LISCallbackToLoanAccepted()'><b>TC027_LISCallbackToLoanAccepted</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Accepted-->243 Stage<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC027_LISCallbackToLoanAccepted = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC027_PLv3ABFL_FetchLeadVerifyAdditionalData()'><b>TC027_PLv3ABFL_FetchLeadVerifyAdditionalData</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead Additional Data is required<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC027_PLv3ABFL_FetchLeadVerifyAdditionalData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC027_PLv3HERO_SubmitApplication()'><b>TC027_PLv3HERO_SubmitApplication</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Hero Verify submit application<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC027_PLv3HERO_SubmitApplication = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC028_LISCallbackToLoanRejection()'><b>TC028_LISCallbackToLoanRejection</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Rejection-->243-->245 Node<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC028_LISCallbackToLoanRejection = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC028_PLv3ABFL_AdditionalDataCapture()'><b>TC028_PLv3ABFL_AdditionalDataCapture</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead Additional Data is added<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC028_PLv3ABFL_AdditionalDataCapture = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC028_PLv3HERO_FetchLeadPostSubmitApplication()'><b>TC028_PLv3HERO_FetchLeadPostSubmitApplication</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Hero Verify Lead stage Post Submit Loan Application<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC028_PLv3HERO_FetchLeadPostSubmitApplication = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC029_FetchLeadDeatils()'><b>TC029_FetchLeadDeatils</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify whether there is any existing stashfin lead present or not<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC029_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC029_PLv3ABFL_BRE3Success()'><b>TC029_PLv3ABFL_BRE3Success</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead BRE3 Success<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC029_PLv3ABFL_BRE3Success = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC030_DeleteExistingLead()'><b>TC030_DeleteExistingLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC030_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC030_PLv3ABFL_UpdateKYCNameInSAI()'><b>TC030_PLv3ABFL_UpdateKYCNameInSAI</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify for PL v3 CKYC name update in SAI<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC030_PLv3ABFL_UpdateKYCNameInSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC031_CreateCitiBankLead()'><b>TC031_CreateCitiBankLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC031_CreateCitiBankLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI()'><b>TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify for PL v3 CKYC name update in SAI<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC032_FetchLeadAllData()'><b>TC032_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC032_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC032_PLv3ABFL_SaveBankDetails()'><b>TC032_PLv3ABFL_SaveBankDetails</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead the bank details<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC032_PLv3ABFL_SaveBankDetails = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC033_LISCallbackToLoanAccepted()'><b>TC033_LISCallbackToLoanAccepted</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Accepted-->243 Stage<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC033_LISCallbackToLoanAccepted = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC033_PLv3ABFL_FetchLeadPostBankVerification()'><b>TC033_PLv3ABFL_FetchLeadPostBankVerification</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Bank Verification<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC033_PLv3ABFL_FetchLeadPostBankVerification = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC034_LISCallbackToLoanProcessingError()'><b>TC034_LISCallbackToLoanProcessingError</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Processing error from Loan Accepted-->243 Stage<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC034_LISCallbackToLoanProcessingError = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC034_PLv3ABFL_EmandateCallback()'><b>TC034_PLv3ABFL_EmandateCallback</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify PL v3 Lead Emandate Callback<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC034_PLv3ABFL_EmandateCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC035_LISCallbackToLoanRejection()'><b>TC035_LISCallbackToLoanRejection</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Rejection-->248-->245 Node<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC035_LISCallbackToLoanRejection = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC035_PLv3ABFL_FetchLeadPostEmandate()'><b>TC035_PLv3ABFL_FetchLeadPostEmandate</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Emandate Callback Stage Verification<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC035_PLv3ABFL_FetchLeadPostEmandate = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC036_FetchLeadDeatils()'><b>TC036_FetchLeadDeatils</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify whether there is any existing stashfin lead present or not<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC036_FetchLeadDeatils = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC036_PLv3ABFL_GenerateLoanAgreement()'><b>TC036_PLv3ABFL_GenerateLoanAgreement</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Hero Generate Loan Agreement<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC036_PLv3ABFL_GenerateLoanAgreement = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC037_DeleteExistingLead()'><b>TC037_DeleteExistingLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Reset the existing lead of the number<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC037_DeleteExistingLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC037_PLv3ABFL_GenerateSanctionLetter()'><b>TC037_PLv3ABFL_GenerateSanctionLetter</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Hero Generate Sanction Letter<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC037_PLv3ABFL_GenerateSanctionLetter = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC038_CreateCitiBankLead()'><b>TC038_CreateCitiBankLead</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Create Stashfin Lead with all deatils<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC038_CreateCitiBankLead = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC038_PLv3ABFL_SubmitApplication()'><b>TC038_PLv3ABFL_SubmitApplication</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Hero Verify submit application<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC038_PLv3ABFL_SubmitApplication = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC039_FetchLeadAllData()'><b>TC039_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC039_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC039_PLv3ABFL_FetchLeadPostSubmitApplication()'><b>TC039_PLv3ABFL_FetchLeadPostSubmitApplication</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Hero Verify Lead stage Post Submit Loan Application<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC039_PLv3ABFL_FetchLeadPostSubmitApplication = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC040_LISCallbackWithInvalidSolutionName()'><b>TC040_LISCallbackWithInvalidSolutionName</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback <br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC040_LISCallbackWithInvalidSolutionName = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC040_PLv3ABFL_PDCCallback()'><b>TC040_PLv3ABFL_PDCCallback</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Hero Verify PDC Callback<br/>
<a href="#Output-********" onClick='toggleBox("Output-********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-********">
TC040_PLv3ABFL_PDCCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC041_LISCallbackWithInvalidLeadId()'><b>TC041_LISCallbackWithInvalidLeadId</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback <br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC041_LISCallbackWithInvalidLeadId = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC041_PLv3ABFL_FetchLeadPostPDCCallback()'><b>TC041_PLv3ABFL_FetchLeadPostPDCCallback</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: For PL v3 Hero Verify Lead stage After PDC Callback<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC041_PLv3ABFL_FetchLeadPostPDCCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC043_LISCallbackWithInvalidEntityType()'><b>TC043_LISCallbackWithInvalidEntityType</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback <br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC043_LISCallbackWithInvalidEntityType = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC044_LISCallbackToLoanAccepted()'><b>TC044_LISCallbackToLoanAccepted</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Accepted-->243 Stage<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC044_LISCallbackToLoanAccepted = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC045_LISCallbackToLoanDisbursedFromLoanAccepted()'><b>TC045_LISCallbackToLoanDisbursedFromLoanAccepted</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Disbursed-->237 Stage<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC045_LISCallbackToLoanDisbursedFromLoanAccepted = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC046_LISCallbackWithInvalidWorkflowOperation()'><b>TC046_LISCallbackWithInvalidWorkflowOperation</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LIS Callback to Loan Accepted-->243 Stage<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC046_LISCallbackWithInvalidWorkflowOperation = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC047_FetchLeadAllData()'><b>TC047_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC047_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC048_LMSDataCallbackWithInvalidSolutionName()'><b>TC048_LMSDataCallbackWithInvalidSolutionName</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LMS Callback to move to node 240<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC048_LMSDataCallbackWithInvalidSolutionName = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC049_LMSDataCallbackWithInvalidLeadId()'><b>TC049_LMSDataCallbackWithInvalidLeadId</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LMS Callback to move to node 240<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC049_LMSDataCallbackWithInvalidLeadId = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC050_LMSDataCallbackWithInvalidChannel()'><b>TC050_LMSDataCallbackWithInvalidChannel</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LMS Callback to move to node 240<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC050_LMSDataCallbackWithInvalidChannel = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC051_LMSDataCallbackWithInvalidEntityType()'><b>TC051_LMSDataCallbackWithInvalidEntityType</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LMS Callback to move to node 240<br/>
<a href="#Output-*********" onClick='toggleBox("Output-*********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-*********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-*********">
TC051_LMSDataCallbackWithInvalidEntityType = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC052_LMSDataCallback()'><b>TC052_LMSDataCallback</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: LMS Callback to move to node 240<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC052_LMSDataCallback = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>1</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow.TC053_FetchLeadAllData()'><b>TC053_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC053_FetchLeadAllData = [Pass]<br><br/>
</div>
</td>
<td></td>
<td>2</td>
<td>OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow@11ebb1b6</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC022_GenerateLoanAgreement()'><b>TC022_GenerateLoanAgreement</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Fetch Dynamic T and C<br/>
<a href="#Output-**********" onClick='toggleBox("Output-**********", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-**********" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-**********">
TC022_GenerateLoanAgreement = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC022_GenerateLoanAgreement()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace**********", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace**********'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC022_GenerateLoanAgreement()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC023_GenerateSanctionLetter()'><b>TC023_GenerateSanctionLetter</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Fetch Dynamic T and C<br/>
<a href="#Output-1873003971" onClick='toggleBox("Output-1873003971", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1873003971" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1873003971">
TC023_GenerateSanctionLetter = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC023_GenerateSanctionLetter()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace1873003971", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1873003971'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC023_GenerateSanctionLetter()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC024_UpdateSAI()'><b>TC024_UpdateSAI</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Update Bureau Pull data set in SAI Table<br/>
<a href="#Output-807358572" onClick='toggleBox("Output-807358572", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-807358572" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-807358572">
TC024_UpdateSAI = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC024_UpdateSAI()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace807358572", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace807358572'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC024_UpdateSAI()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC025_AcceptLoanAgreement()'><b>TC025_AcceptLoanAgreement</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Accept Loan Agreement<br/>
<a href="#Output-914421583" onClick='toggleBox("Output-914421583", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-914421583" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-914421583">
TC025_AcceptLoanAgreement = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC025_AcceptLoanAgreement()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace914421583", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace914421583'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC025_AcceptLoanAgreement()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC026_EmandateCallback()'><b>TC026_EmandateCallback</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify Emandate Callback<br/>
<a href="#Output-869706365" onClick='toggleBox("Output-869706365", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-869706365" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-869706365">
TC026_EmandateCallback = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC026_EmandateCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace869706365", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace869706365'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC026_EmandateCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC027_VerifyPDCCallback()'><b>TC027_VerifyPDCCallback</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: If PDC Callback is not received manually hit callback<br/>
<a href="#Output-2046039308" onClick='toggleBox("Output-2046039308", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-2046039308" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-2046039308">
TC027_VerifyPDCCallback = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC027_VerifyPDCCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace2046039308", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2046039308'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC027_VerifyPDCCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC028_UploadSheetONPanel()'><b>TC028_UploadSheetONPanel</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Sheet upload from panel<br/>
<a href="#Output-1377929849" onClick='toggleBox("Output-1377929849", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1377929849" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1377929849">
TC028_UploadSheetONPanel = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC028_UploadSheetONPanel()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace1377929849", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1377929849'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC028_UploadSheetONPanel()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC029_VerifyLISCallback()'><b>TC029_VerifyLISCallback</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: If LIS Callback is not received manually hit callback to move to node 818<br/>
<a href="#Output-1240929777" onClick='toggleBox("Output-1240929777", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1240929777" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1240929777">
TC029_VerifyLISCallback = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC029_VerifyLISCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace1240929777", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1240929777'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC029_VerifyLISCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC030_LMSDataCallback()'><b>TC030_LMSDataCallback</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: LMS Callback to move to node 240<br/>
<a href="#Output-891465236" onClick='toggleBox("Output-891465236", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-891465236" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-891465236">
TC030_LMSDataCallback = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC030_LMSDataCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace891465236", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace891465236'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC030_LMSDataCallback()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC030_PLv3HERO_FetchLeadPostPDCCallback()'><b>TC030_PLv3HERO_FetchLeadPostPDCCallback</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Hero Verify Lead stage After PDC Callback<br/>
<a href="#Output-60230247" onClick='toggleBox("Output-60230247", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-60230247" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-60230247">
TC030_PLv3HERO_FetchLeadPostPDCCallback = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method PersonalLoanRenewal.TC030_PLv3HERO_FetchLeadPostPDCCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace60230247", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace60230247'><pre>java.lang.Throwable: Method PersonalLoanRenewal.TC030_PLv3HERO_FetchLeadPostPDCCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC031PLv3HERO_SubmitApplicationLMSApprovedCallback()'><b>TC031PLv3HERO_SubmitApplicationLMSApprovedCallback</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: For PL v3 Hero Verify if LMS Approve Callback required<br/>
<a href="#Output-901120198" onClick='toggleBox("Output-901120198", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-901120198" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-901120198">
TC031PLv3HERO_SubmitApplicationLMSApprovedCallback = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method PersonalLoanRenewal.TC031PLv3HERO_SubmitApplicationLMSApprovedCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace901120198", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace901120198'><pre>java.lang.Throwable: Method PersonalLoanRenewal.TC031PLv3HERO_SubmitApplicationLMSApprovedCallback()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC031_FetchLeadAllData()'><b>TC031_FetchLeadAllData</b><br>Test class: OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-696989191" onClick='toggleBox("Output-696989191", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-696989191" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-696989191">
TC031_FetchLeadAllData = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC031_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace696989191", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace696989191'><pre>java.lang.Throwable: Method TestMCAFullertonV3Workflow.TC031_FetchLeadAllData()[pri:0, instance:OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow@f3021cb</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC032_FetchLeadAllData()'><b>TC032_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.PersonalLoanRenewal<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-1508054172" onClick='toggleBox("Output-1508054172", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-1508054172" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-1508054172">
TC032_FetchLeadAllData = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method PersonalLoanRenewal.TC032_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace1508054172", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1508054172'><pre>java.lang.Throwable: Method PersonalLoanRenewal.TC032_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.PersonalLoanRenewal@2a32fb6</td></tr>
<tr>
<td title='OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC043_FetchLeadAllData()'><b>TC043_FetchLeadAllData</b><br>Test class: OCL.Lending.ConsumerLending.TestPLABFLOneClick<br>Test method: Verify the lead data using fetch Stratgey ALL_DATA<br/>
<a href="#Output-101697772" onClick='toggleBox("Output-101697772", this, "Show output", "Hide output");'>Show output</a>

<a href="#Output-101697772" onClick="toggleAllBoxes();">Show all outputs</a>
<div class='log' id="Output-101697772">
TC043_FetchLeadAllData = [Skip]<br><br/>
</div>
</td>
<td><div><pre>java.lang.Throwable: Method TestPLABFLOneClick.TC043_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div><a href='#' onClick='toggleBox("stack-trace101697772", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace101697772'><pre>java.lang.Throwable: Method TestPLABFLOneClick.TC043_FetchLeadAllData()[pri:0, instance:OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165] depends on not successfully finished methods
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:987)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at org.testng.TestRunner.privateRun(TestRunner.java:739)
	at org.testng.TestRunner.run(TestRunner.java:589)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
	at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
	at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
</pre></div></td>
<td>0</td>
<td>OCL.Lending.ConsumerLending.TestPLABFLOneClick@6107165</td></tr>
</table><p>
</body>
</html>