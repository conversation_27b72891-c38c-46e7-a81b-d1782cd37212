<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="197NODMB24984.local" ignored="0" name="Lending" tests="177" failures="3" timestamp="2022-07-27T18:54:42 IST" time="266.704" errors="0">
  <testcase name="TC001_FetchLeadDeatils" time="0.692" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC001_PLv3ABFL_fetchlLead" time="0.476" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.458" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.511" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.573" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC001_FetchLeadDeatils" time="0.778" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC002_DeleteExistingLead" time="0.465" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC002_PLv3ABFL_DeleteExistingLead" time="0.404" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC002_DeleteExistingLead" time="0.428" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC002_DeleteExistingLead" time="0.485" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC002_DeleteExistingLead" time="0.375" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC002_DeleteExistingLead" time="0.42" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC003_CreatePLRenewalLead" time="1.02" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC003_PLv3ABFL_CreateLead" time="0.961" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC003_CreateCitiBankLead" time="1.147" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC003_CreateCitiBankLead_WithoutPassingBaseId" time="0.806" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC003_CreateHomeFirstLead" time="0.882" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC003_CreateBTDistributionPiramalLead" time="1.366" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC004_FetchLeadAllData" time="0.81" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC004_PLV3ABFL_FetchLeadAllData" time="0.952" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC004_FetchLeadAllData" time="0.781" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC004_CreateCitiBankLead_WithoutPassingProductId" time="0.76" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC004_UpdateLeadBasicDetails" time="0.636" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC004_FetchLeadAllData" time="0.67" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC005_UpdateBureauDataSetInSAI" time="0.446" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC005_PLV3ABFL_UpdateLeadOccupationDetails" time="0.766" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC005_LISCallbackToLoanAccepted" time="0.399" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC005_CreateCitiBankLead_WithoutPassingProductVersion" time="0.725" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC005_UpdateBureauDataSetInSAI" time="0.416" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC005_UpdateLeadBasicDetails" time="1.347" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC006_FetchCIR" time="0.746" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate" time="0.832" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC006_LISCallbackToLoanProcessingError" time="2.809" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC006_CreateCitiBankLead_WithoutPassingProductType" time="0.852" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC006_FetchCIR" time="0.853" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC006_FetchLeadAllData" time="0.785" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC007_FetchLeadAllData" time="2.423" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC007_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.519" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC007_LISCallbackToLoanDisbursedFromLoanProcessingError" time="0.575" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC007_CreateCitiBankLead_WithoutPassingLenderId" time="0.849" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC007_FetchLeadAllData" time="7.821" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC007_UpdateBureauDataSetInSAI" time="0.455" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC008_UpdateExistingDetailsInSAI" time="0.406" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC008_PLv3ABFL_FetchDataPostSAIlUpdate" time="0.508" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC008_FetchLeadAllData" time="0.849" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC008_CreateCitiBankLead_WithoutPassingRiskSegment" time="0.673" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC008_FetchLeadAllData" time="0.689" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC008_FetchCIR" time="0.838" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC009_PLRenewal_LoanOfferAccept" time="0.833" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC009_PLv3ABFL_FetchCIR" time="0.581" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC009_LMSDataCallback" time="2.186" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC009_CreateCitiBankLead_WithoutPassingFlowType" time="0.653" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC009_UpdateExistingDetailsInSAI" time="0.408" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC009_FetchLeadAllData" time="4.405" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC010_FetchLeadAllData" time="0.765" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC010_PLv3ABFL_BRE1Callback" time="4.597" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC010_FetchLeadAllData" time="0.395" classname="OCL.Lending.ConsumerLending.TestCitiBankFlow"/>
  <testcase name="TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount" time="1.641" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC010_UpdateAdditionalDetails" time="0.79" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC010_UpdateExistingDetailsInSAI" time="0.755" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC011_PLv3HERO_UploadCustomerPhoto" time="3.021" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC011_PLv3ABFL_FetchDataPostBRE1Success" time="0.534" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest" time="0.91" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC011_VerifyLeadStage" time="3.43" classname="OCL.Lending.BusinessLending.HomeFirst"/>
  <testcase name="TC011_FetchLeadAllData" time="10.483" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC012_PLv3HERO_VerifyUploadedCustomerPhoto" time="0.467" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC012_PLv3ABFL_LoanOfferAccept" time="0.69" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName" time="0.736" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC012_UploadSelfie" time="2.804" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC013_PLv3HERO_UploadSelfie" time="5.278" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC013_PLv3ABFL_FetchDataPostLoanOfferAccept" time="0.343" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName" time="0.628" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC013_UploadCustomerPhoto" time="2.771" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC014_PLv3HERO_VerifyUploadedSelfie" time="0.452" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC014_PLV3ABFL_UpdateLeadDetailsinSAI" time="0.378" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC014_CreateCitiBankLead_WithoutPassingApplicationId" time="0.732" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC015_VerifyUploadedDocument" time="0.481" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC015_CKYCCallback" time="0.621" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan" time="0.685" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC015_CreateCitiBankLead_WithoutPassingOfferURL" time="0.62" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC016_UpdateSAIWithLenderDetails" time="0.709" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC016_VerifyLeadStage" time="14.405" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC016_PLv3ABFL_FetchDataPostLoanOfferAccept" time="10.445" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC016_CreateCitiBankLead_PassingInvalidSolutionName" time="0.614" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC017_CKYCCallback" time="0.465" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC017_PLv3HERO_AdditionalIsRequiredorNot" time="0.478" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC017_PLv3ABFL_FetchDataPostKYCIntiated" time="0.421" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC017_CreateCitiBankLead_PassingInvalidChannelName" time="0.63" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC018_VerifyLeadStage" time="3.864" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC018_PLv3HERO_FetchLeadVerifyAdditionalData" time="0.423" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC018_PLv3HERO_UploadCustomerPhoto" time="4.698" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2" time="0.898" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC020_UpdateKYCNameInSAI" time="0.47" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow"/>
  <testcase name="TC019_PLv3HERO_UpdateKYCNameInSAI" time="0.926" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC019_PLv3HERO_VerifyUploadedCustomerPhoto" time="0.516" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC019_CreateCitiBankLead_PassingInvalidEntityType" time="0.65" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC021_SaveBankDetails" time="1.884" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [202]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [202]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow.TC021_SaveBankDetails(TestMCAFullertonV3Workflow.java:1042)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC021_SaveBankDetails -->
  <testcase name="TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI" time="0.453" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC020_PLv3HERO_UploadSelfie" time="4.821" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC020_CreateCitiBankLead" time="1.189" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC022_GenerateLoanAgreement" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC022_GenerateLoanAgreement -->
  <testcase name="TC021_PLv3HERO_SaveBankDetails" time="0.745" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC021_PLv3HERO_VerifyUploadedSelfie" time="0.428" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC021_FetchLeadAllData" time="0.646" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC023_GenerateSanctionLetter" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC023_GenerateSanctionLetter -->
  <testcase name="TC022_PLv3HERO_UpdateKYCNameInSAI" time="0.823" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC022_PLv3ABFL_FetchDataPostKYCIntiated" time="10.32" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC022_CreateLeadAgain" time="0.879" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC024_UpdateSAI" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC024_UpdateSAI -->
  <testcase name="TC023_PLv3HERO_EmandateCallback" time="0.49" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC023_PLv3ABFL_FetchDataPostSelfieUploaded" time="15.634" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC023_LISCallbackToLoanRejection" time="0.414" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC025_AcceptLoanAgreement" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC025_AcceptLoanAgreement -->
  <testcase name="TC024_PLv3HERO_FetchLeadPostEmandate" time="0.362" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC024_PLv3ABFL_SecondBRECallback" time="0.912" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC024_FetchLeadDeatils" time="0.376" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC026_EmandateCallback" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC026_EmandateCallback -->
  <testcase name="TC025_PLv3HERO_GenerateLoanAgreement" time="0.936" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC025_PLv3ABFL_FetchDataAfterBRE2Success" time="0.38" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC026_PLv3ABFL_AdditionalIsRequiredorNot" time="0.774" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC025_DeleteExistingLead" time="0.373" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC027_VerifyPDCCallback" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC027_VerifyPDCCallback -->
  <testcase name="TC026_PLv3HERO_GenerateSanctionLetter" time="0.725" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC027_PLv3ABFL_FetchLeadVerifyAdditionalData" time="0.395" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC026_CreateCitiBankLead" time="0.859" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC028_UploadSheetONPanel" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC028_UploadSheetONPanel -->
  <testcase name="TC027_PLv3HERO_SubmitApplication" time="2.874" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC028_PLv3ABFL_AdditionalDataCapture" time="0.373" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC027_LISCallbackToLoanAccepted" time="0.427" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC029_VerifyLISCallback" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC029_VerifyLISCallback -->
  <testcase name="TC028_PLv3HERO_FetchLeadPostSubmitApplication" time="0.467" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal"/>
  <testcase name="TC029_PLv3ABFL_BRE3Success" time="1.646" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC028_LISCallbackToLoanRejection" time="0.537" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC030_LMSDataCallback" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC030_LMSDataCallback -->
  <testcase name="TC029_PLv3HERO_PDCCallback" time="8.364" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <failure type="java.lang.AssertionError" message="did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [LOAN_AGREEMENT_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LOAN_APPLICATION_ACCEPTED] but found [LOAN_AGREEMENT_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.PersonalLoanRenewal.TC029_PLv3HERO_PDCCallback(PersonalLoanRenewal.java:1190)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC029_PLv3HERO_PDCCallback -->
  <testcase name="TC030_PLv3ABFL_UpdateKYCNameInSAI" time="0.83" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC029_FetchLeadDeatils" time="0.392" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC031_FetchLeadAllData" time="0.0" classname="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
    <skipped/>
  </testcase> <!-- TC031_FetchLeadAllData -->
  <testcase name="TC030_PLv3HERO_FetchLeadPostPDCCallback" time="0.0" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <skipped/>
  </testcase> <!-- TC030_PLv3HERO_FetchLeadPostPDCCallback -->
  <testcase name="TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI" time="0.428" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC030_DeleteExistingLead" time="0.381" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC031PLv3HERO_SubmitApplicationLMSApprovedCallback" time="0.0" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <skipped/>
  </testcase> <!-- TC031PLv3HERO_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC032_PLv3ABFL_SaveBankDetails" time="1.503" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC031_CreateCitiBankLead" time="2.126" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC032_FetchLeadAllData" time="0.0" classname="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
    <skipped/>
  </testcase> <!-- TC032_FetchLeadAllData -->
  <testcase name="TC033_PLv3ABFL_FetchLeadPostBankVerification" time="1.6" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC032_FetchLeadAllData" time="0.662" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC034_PLv3ABFL_EmandateCallback" time="0.373" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC033_LISCallbackToLoanAccepted" time="0.473" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC035_PLv3ABFL_FetchLeadPostEmandate" time="0.539" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC034_LISCallbackToLoanProcessingError" time="0.447" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC036_PLv3ABFL_GenerateLoanAgreement" time="1.149" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC035_LISCallbackToLoanRejection" time="0.847" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC037_PLv3ABFL_GenerateSanctionLetter" time="0.605" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC036_FetchLeadDeatils" time="0.413" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC038_PLv3ABFL_SubmitApplication" time="0.629" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC037_DeleteExistingLead" time="0.36" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC039_PLv3ABFL_FetchLeadPostSubmitApplication" time="0.418" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC038_CreateCitiBankLead" time="0.878" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC040_PLv3ABFL_PDCCallback" time="2.813" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC039_FetchLeadAllData" time="0.636" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC041_PLv3ABFL_FetchLeadPostPDCCallback" time="0.346" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick"/>
  <testcase name="TC040_LISCallbackWithInvalidSolutionName" time="0.427" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC042PLV3HERO_SubmitApplicationLMSApprovedCallback" time="2.6" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick">
    <failure type="java.lang.AssertionError" message="did not expect to find [LMS_CALLBACK_SUCCESS] but found [LOAN_AGREEMENT_SUCCESS]">
      <![CDATA[java.lang.AssertionError: did not expect to find [LMS_CALLBACK_SUCCESS] but found [LOAN_AGREEMENT_SUCCESS]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Lending.ConsumerLending.TestPLABFLOneClick.TC042PLV3HERO_SubmitApplicationLMSApprovedCallback(TestPLABFLOneClick.java:1557)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at org.testng.TestRunner.privateRun(TestRunner.java:739)
at org.testng.TestRunner.run(TestRunner.java:589)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:398)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:39)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:437)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:70)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC042PLV3HERO_SubmitApplicationLMSApprovedCallback -->
  <testcase name="TC041_LISCallbackWithInvalidLeadId" time="0.375" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC043_FetchLeadAllData" time="0.0" classname="OCL.Lending.ConsumerLending.TestPLABFLOneClick">
    <skipped/>
  </testcase> <!-- TC043_FetchLeadAllData -->
  <testcase name="TC043_LISCallbackWithInvalidEntityType" time="0.539" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC044_LISCallbackToLoanAccepted" time="0.445" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC045_LISCallbackToLoanDisbursedFromLoanAccepted" time="0.602" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC046_LISCallbackWithInvalidWorkflowOperation" time="0.388" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC047_FetchLeadAllData" time="0.846" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC048_LMSDataCallbackWithInvalidSolutionName" time="0.395" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC049_LMSDataCallbackWithInvalidLeadId" time="0.425" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC050_LMSDataCallbackWithInvalidChannel" time="0.447" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC051_LMSDataCallbackWithInvalidEntityType" time="0.403" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC052_LMSDataCallback" time="1.839" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
  <testcase name="TC053_FetchLeadAllData" time="2.106" classname="OCL.Lending.ConsumerLending.TestCitiBankRegressionFlow"/>
</testsuite> <!-- Lending -->
