<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite thread-count="10" name="Onboarding Engine" guice-stage="DEVELOPMENT" verbose="30">
  <listeners>
    <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
    <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
    <listener class-name="com.paytm.framework.reporting.listeners.RPTestListener"/>
    <listener class-name="AfterSuite.ListenerTest"/>
  </listeners>
  <test thread-count="10" name="Lending" verbose="30">
    <classes>
      <class name="OCL.Lending.ConsumerLending.TestPostpaidV3PANDedupe">
        <methods>
          <include name="TC003_CreatePostpaidLead"/>
          <include name="TC001_FetchLeadDeatils"/>
          <include name="TC004_FetchLeadAllData"/>
          <include name="TC002_DeleteExistingLead"/>
          <include name="intitializeInputData"/>
          <include name="TC005_UpdateLeadBasicDetails"/>
        </methods>
      </class> <!-- OCL.Lending.ConsumerLending.TestPostpaidV3PANDedupe -->
    </classes>
  </test> <!-- Lending -->
</suite> <!-- Onboarding Engine -->
