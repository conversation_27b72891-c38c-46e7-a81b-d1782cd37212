<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="197NODMB24984.local" ignored="27" name="Devices" tests="547" failures="67" timestamp="2022-07-13T12:27:48 IST" time="542.696" errors="0">
  <testcase name="TC0001_CreateLead" time="1.244" classname="OCL.Individual.FastagDiy.FlowFastagDiy">
    <failure type="java.lang.AssertionError" message="did not expect to find [Lead successfully created.] but found [Mobile number not found against the mentioned custId]">
      <![CDATA[java.lang.AssertionError: did not expect to find [Lead successfully created.] but found [Mobile number not found against the mentioned custId]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.FastagDiy.FlowFastagDiy.TC0001_CreateLead(FlowFastagDiy.java:108)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0001_CreateLead -->
  <testcase name="TC001_MapEdcPositiveSendOtpBusiness" time="1.561" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC"/>
  <testcase name="TC0001_FetchValidTags" time="0.217" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <failure type="java.net.MalformedURLException" message="For input string: &amp;quot;8084v1&amp;quot;">
      <![CDATA[java.net.MalformedURLException: For input string: "8084v1"
at java.net.URL.<init>(URL.java:644)
at java.net.URL.<init>(URL.java:507)
at java.net.URL.<init>(URL.java:456)
at sun.reflect.GeneratedConstructorAccessor25.newInstance(Unknown Source)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:250)
at io.restassured.internal.RequestSpecificationImpl.getTargetURI(RequestSpecificationImpl.groovy:1636)
at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:169)
at io.restassured.internal.RequestSpecificationImpl.partiallyApplyPathParams(RequestSpecificationImpl.groovy:1777)
at sun.reflect.GeneratedMethodAccessor24.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.newFilterContext(RequestSpecificationImpl.groovy:1156)
at sun.reflect.GeneratedMethodAccessor34.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.applyPathParamsAndSendRequest(RequestSpecificationImpl.groovy:1747)
at sun.reflect.GeneratedMethodAccessor86.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.applyPathParamsAndSendRequest(RequestSpecificationImpl.groovy:1755)
at sun.reflect.GeneratedMethodAccessor85.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:822)
at io.restassured.internal.RequestSpecificationImpl.invokeMethod(RequestSpecificationImpl.groovy)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.call(PogoInterceptableSite.java:47)
at org.codehaus.groovy.runtime.callsite.PogoInterceptableSite.callCurrent(PogoInterceptableSite.java:57)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallCurrent(CallSiteArray.java:51)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:157)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.RequestSpecificationImpl.get(RequestSpecificationImpl.groovy:171)
at io.restassured.internal.RequestSpecificationImpl.get(RequestSpecificationImpl.groovy)
at com.paytm.apitools.core.http.HttpClient.send(HttpClient.java:86)
at com.paytm.apitools.core.AbstractApi.callAPI(AbstractApi.java:224)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:139)
at com.paytm.apitools.core.AbstractApiV2.callAPI(AbstractApiV2.java:134)
at Services.MechantService.MiddlewareServices.FisFetchTags(MiddlewareServices.java:3159)
at OCL.Individual.FastagAssisted.FlowFastagAssistedBasic.TC0001_FetchValidTags(FlowFastagAssistedBasic.java:123)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NumberFormatException: For input string: "8084v1"
at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
at java.lang.Integer.parseInt(Integer.java:580)
at java.lang.Integer.parseInt(Integer.java:615)
at java.net.URLStreamHandler.parseURL(URLStreamHandler.java:222)
at java.net.URL.<init>(URL.java:639)
... 96 more
]]>
    </failure>
  </testcase> <!-- TC0001_FetchValidTags -->
  <testcase name="TC_001_MerchantLogin" time="2.11" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_01_AddDealerForBrandEMI" time="1.064" classname="OCL.DIY.BrandEmiDIY.AddDealerForBrandEMI"/>
  <testcase name="TC001_MapEdcPositiveSendOtpBusiness" time="0.857" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS"/>
  <testcase name="TC01_CreateProduct" time="0.961" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <failure type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.RestApiForEdcPlans.RestApiTest.TC01_CreateProduct(RestApiTest.java:57)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The JSON input text should neither be null nor empty.
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:238)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:250)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:80)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </failure>
  </testcase> <!-- TC01_CreateProduct -->
  <testcase name="TC01_CreateUser" time="1.635" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC02_CreateProduct" time="0.665" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <failure type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.RestApiForEdcPlans.RestApiTest.TC02_CreateProduct(RestApiTest.java:83)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The JSON input text should neither be null nor empty.
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:250)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:80)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </failure>
  </testcase> <!-- TC02_CreateProduct -->
  <testcase name="TC001_MapEdcPositiveSendOtpBusiness" time="1.148" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore"/>
  <testcase name="TC03_CreateProductExistingWithdeviceType1" time="0.785" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <failure type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.RestApiForEdcPlans.RestApiTest.TC03_CreateProductExistingWithdeviceType1(RestApiTest.java:107)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The JSON input text should neither be null nor empty.
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:250)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:80)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </failure>
  </testcase> <!-- TC03_CreateProductExistingWithdeviceType1 -->
  <testcase name="TC0002_SubmitLeadPanel" time="5.227" classname="OCL.Individual.FastagDiy.FlowFastagDiy">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at com.goldengate.common.BaseMethod.ReallocatingAgent(BaseMethod.java:453)
at OCL.Individual.FastagDiy.FlowFastagDiy.TC0002_SubmitLeadPanel(FlowFastagDiy.java:118)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC0002_SubmitLeadPanel -->
  <testcase name="TC04_CreateProductExistingWithdeviceType2" time="0.525" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <failure type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.get(JsonPath.java:202)
at io.restassured.path.json.JsonPath.getString(JsonPath.java:352)
at OCL.RestApiForEdcPlans.RestApiTest.TC04_CreateProductExistingWithdeviceType2(RestApiTest.java:130)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: The JSON input text should neither be null nor empty.
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:250)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:80)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 18 more
]]>
    </failure>
  </testcase> <!-- TC04_CreateProductExistingWithdeviceType2 -->
  <testcase name="TC05_CreateProductWithoutPermissionsWithdeviceType1" time="0.469" classname="OCL.RestApiForEdcPlans.RestApiTest"/>
  <testcase name="TC001_MapEdcPositiveSendOtpBusiness" time="0.867" classname="OCL.Business.MapEDC.FlowMapEDC"/>
  <testcase name="TC06_CreateProductWithoutpermissionsWithdeviceType2" time="0.517" classname="OCL.RestApiForEdcPlans.RestApiTest"/>
  <testcase name="TC08_CreateProductWithOutModelWithdeviceType1" time="0.459" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <failure type="java.lang.AssertionError" message="did not expect to find [404] but found [401]">
      <![CDATA[java.lang.AssertionError: did not expect to find [404] but found [401]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.RestApiForEdcPlans.RestApiBase.verifyResponseCodeAs404Notfound(RestApiBase.java:96)
at OCL.RestApiForEdcPlans.RestApiTest.TC08_CreateProductWithOutModelWithdeviceType1(RestApiTest.java:228)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC08_CreateProductWithOutModelWithdeviceType1 -->
  <testcase name="TC09_CreateProductWithOutModelWithdeviceType2" time="0.453" classname="OCL.RestApiForEdcPlans.RestApiTest"/>
  <testcase name="TC13_CreateProductWithOutxmwtokendeviceType1" time="0.478" classname="OCL.RestApiForEdcPlans.RestApiTest"/>
  <testcase name="TC14_CreateProductWithOutxmwtokendeviceType2" time="0.524" classname="OCL.RestApiForEdcPlans.RestApiTest"/>
  <testcase name="TC001_InternationalCardPositiveSendOtpBusiness" time="0.814" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan"/>
  <testcase name="TC002_MapEdcPositiveGetBusiness" time="0.819" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:238)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at OCL.Business.MapEDC.FlowMapEDCWithAMC.TC002_MapEdcPositiveGetBusiness(FlowMapEDCWithAMC.java:122)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 15 more
]]>
    </failure>
  </testcase> <!-- TC002_MapEdcPositiveGetBusiness -->
  <testcase name="TC0002_AddingExtraParamValidateTag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_AddingExtraParamValidateTag -->
  <testcase name="TC0002_EmptyBodyValidateTag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_EmptyBodyValidateTag -->
  <testcase name="TC_002_CreateTerminalInPGWithInvalidOSType" time="2.057" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC0002_EmptyVersionValidateTag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_EmptyVersionValidateTag -->
  <testcase name="TC001_UpgradeMerchantPositiveSendOtpBusiness" time="1.046" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery"/>
  <testcase name="TC0002_FetchEmptyTag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_FetchEmptyTag -->
  <testcase name="TC0002_FetchRandomTag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_FetchRandomTag -->
  <testcase name="TC002_MapEdcPositiveGetBusiness" time="0.463" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS.TC002_MapEdcPositiveGetBusiness(FlowMapEDCAndroidWithPOS.java:115)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 15 more
]]>
    </failure>
  </testcase> <!-- TC002_MapEdcPositiveGetBusiness -->
  <testcase name="TC0002_ValidateTags" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0002_ValidateTags -->
  <testcase name="TC_02_AddSameDealerForBrandEMI" time="0.923" classname="OCL.DIY.BrandEmiDIY.AddDealerForBrandEMI"/>
  <testcase name="TC02_FetchUserId" time="0.737" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC002_InternationalCardPositiveGetBusiness" time="0.529" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan"/>
  <testcase name="TC001_UpgradeMerchantPositiveSendOtpBusiness" time="0.76" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases"/>
  <testcase name="TC003_MapEdcPositiveGetBusinessProfile" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC003_MapEdcPositiveGetBusinessProfile -->
  <testcase name="TC002_MapEdcPositiveGetBusiness" time="1.52" classname="OCL.Business.MapEDC.FlowMapEDC">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at OCL.Business.MapEDC.FlowMapEDC.TC002_MapEdcPositiveGetBusiness(FlowMapEDC.java:117)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 15 more
]]>
    </failure>
  </testcase> <!-- TC002_MapEdcPositiveGetBusiness -->
  <testcase name="TC002_MapEdcPositiveGetBusiness" time="1.946" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at OCL.Business.MapEDC.FlowMapEDCWithRAScore.TC002_MapEdcPositiveGetBusiness(FlowMapEDCWithRAScore.java:122)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 15 more
]]>
    </failure>
  </testcase> <!-- TC002_MapEdcPositiveGetBusiness -->
  <testcase name="TC003_MapEdcPositiveGetBusinessProfile" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC003_MapEdcPositiveGetBusinessProfile -->
  <testcase name="TC0003_AddingParamDropDownList" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0003_AddingParamDropDownList -->
  <testcase name="TC002_UpgradeMerchantPositiveGetBusiness" time="0.661" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery"/>
  <testcase name="TC0003_EmptyVersionDropDownList" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0003_EmptyVersionDropDownList -->
  <testcase name="TC_03_FindBrandDealerData" time="0.442" classname="OCL.DIY.BrandEmiDIY.AddDealerForBrandEMI"/>
  <testcase name="TC0003_GettingDropDownList" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0003_GettingDropDownList -->
  <testcase name="TC003_InternationalCardPositiveGetBusinessProfile" time="0.436" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan.TC003_InternationalCardPositiveGetBusinessProfile(FlowUpgradeMerchantPlanInternationalCardPlan.java:118)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC003_InternationalCardPositiveGetBusinessProfile -->
  <testcase name="TC_003_CreateTerminalInPGWithInvalidVendorType" time="1.938" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC004_MapEdcPositiveFetchMID" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC004_MapEdcPositiveFetchMID -->
  <testcase name="TC002_UpgradeMerchantPositiveGetBusiness" time="0.44" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases"/>
  <testcase name="TC003_MapEdcPositiveGetBusinessProfile" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC003_MapEdcPositiveGetBusinessProfile -->
  <testcase name="TC003_MapEdcPositiveGetBusinessProfile" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC003_MapEdcPositiveGetBusinessProfile -->
  <testcase name="TC004_MapEdcPositiveFetchMID" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC004_MapEdcPositiveFetchMID -->
  <testcase name="TC12_CompanyOnboard" time="1.591" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC004_MapEdcPositiveFetchMID" time="0.001" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC004_MapEdcPositiveFetchMID -->
  <testcase name="TC004_InternationalCardPositiveFetchMID" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC004_InternationalCardPositiveFetchMID -->
  <testcase name="TC0004_EmptyVersionFetchIssuance" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_EmptyVersionFetchIssuance -->
  <testcase name="TC001_PlanAmexPositiveSendOtpBusiness" time="0.774" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex"/>
  <testcase name="TC003_UpgradeMerchantPositiveGetBusinessProfile" time="0.409" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery.TC003_UpgradeMerchantPositiveGetBusinessProfile(FlowUpgradeMerchantPlanGrocery.java:128)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC003_UpgradeMerchantPositiveGetBusinessProfile -->
  <testcase name="TC0004_EmptyVrnIssuance" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_EmptyVrnIssuance -->
  <testcase name="TC005_MapEdcPositiveSendOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC005_MapEdcPositiveSendOtpCreate -->
  <testcase name="TC0004_FetchIssuance" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_FetchIssuance -->
  <testcase name="TC0004_WrongParameterIssuance" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_WrongParameterIssuance -->
  <testcase name="TC006_MapEdcPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC006_MapEdcPositiveValidateOtpCreate -->
  <testcase name="TC003_UpgradeMerchantPositiveGetBusinessProfile" time="0.864" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases.TC003_UpgradeMerchantPositiveGetBusinessProfile(FlowUpgradeMerchantPlanNegativeCases.java:108)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC003_UpgradeMerchantPositiveGetBusinessProfile -->
  <testcase name="TC004_MapEdcPositiveFetchMID" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC004_MapEdcPositiveFetchMID -->
  <testcase name="TC0004_WrongVrnIssuance" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0004_WrongVrnIssuance -->
  <testcase name="TC005_MapEdcPositiveSendOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC005_MapEdcPositiveSendOtpCreate -->
  <testcase name="TC005_InternationalCardPositiveFetchUpgradePlans" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC005_InternationalCardPositiveFetchUpgradePlans -->
  <testcase name="TC004_UpgradeMerchantPositiveFetchMID" time="0.001" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC004_UpgradeMerchantPositiveFetchMID -->
  <testcase name="TC007_MapEdcFetchPlans" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC007_MapEdcFetchPlans -->
  <testcase name="TC002_PlanAmexPositiveGetBusiness" time="0.439" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex"/>
  <testcase name="TC005_MapEdcPositiveSendOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC005_MapEdcPositiveSendOtpCreate -->
  <testcase name="TC004_UpgradeMerchantPositiveFetchMID" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC004_UpgradeMerchantPositiveFetchMID -->
  <testcase name="TC005_MapEdcPositiveSendOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC005_MapEdcPositiveSendOtpCreate -->
  <testcase name="TC0005_01_InvalidSolutionCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_01_InvalidSolutionCreateLeadFastag -->
  <testcase name="TC006_MapEdcPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC006_MapEdcPositiveValidateOtpCreate -->
  <testcase name="TC0005_02_EmptySolutionCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_02_EmptySolutionCreateLeadFastag -->
  <testcase name="TC006_InternationalCardPositiveFetchTnC" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC006_InternationalCardPositiveFetchTnC -->
  <testcase name="TC_004_CreateTerminalInPGWithInvalidModelType" time="2.448" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC0005_03_NoSolutionCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_03_NoSolutionCreateLeadFastag -->
  <testcase name="TC005_UpgradeMerchantPositiveFetchUpgradePlans" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC005_UpgradeMerchantPositiveFetchUpgradePlans -->
  <testcase name="TC008_MapEdcFetchLeadDetails" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC008_MapEdcFetchLeadDetails -->
  <testcase name="TC0005_04_InvalidEntityCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_04_InvalidEntityCreateLeadFastag -->
  <testcase name="TC005_UpgradeMerchantPositiveFetchUpgradePlans" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC005_UpgradeMerchantPositiveFetchUpgradePlans -->
  <testcase name="TC006_MapEdcPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC006_MapEdcPositiveValidateOtpCreate -->
  <testcase name="TC003_PlanAmexPositiveGetBusinessProfile" time="0.628" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex.TC003_PlanAmexPositiveGetBusinessProfile(FlowUpgradeMechantPlanAmex.java:108)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC003_PlanAmexPositiveGetBusinessProfile -->
  <testcase name="TC006_MapEdcPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC006_MapEdcPositiveValidateOtpCreate -->
  <testcase name="TC007_MapEdcFetchPlans" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC007_MapEdcFetchPlans -->
  <testcase name="TC009_MapEdcUpdateLead" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC009_MapEdcUpdateLead -->
  <testcase name="TC007_InternationalCardPositiveSendOtpCreate" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC007_InternationalCardPositiveSendOtpCreate -->
  <testcase name="TC006_UpgradeMerchantPositiveFetchTnC" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC006_UpgradeMerchantPositiveFetchTnC -->
  <testcase name="TC006_UpgradeMerchantPositiveFetchTnC" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC006_UpgradeMerchantPositiveFetchTnC -->
  <testcase name="TC0005_05_EmptyEntityCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_05_EmptyEntityCreateLeadFastag -->
  <testcase name="TC007_MapEdcFetchPlans" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC007_MapEdcFetchPlans -->
  <testcase name="TC004_01_PlanAmexFetchMIDInvalidCustId" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_01_PlanAmexFetchMIDInvalidCustId -->
  <testcase name="TC0005_06_DifferentEntityCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_06_DifferentEntityCreateLeadFastag -->
  <testcase name="TC008_MapEdcFetchLeadDetails" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC008_MapEdcFetchLeadDetails -->
  <testcase name="TC004_02_PlanAmexFetchMIDemptyCustId" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_02_PlanAmexFetchMIDemptyCustId -->
  <testcase name="TC0005_07_NoEntityCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_07_NoEntityCreateLeadFastag -->
  <testcase name="TC004_03_PlanAmexFetchMIDWrongCustId" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_03_PlanAmexFetchMIDWrongCustId -->
  <testcase name="TC007_MapEdcFetchPlans" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC007_MapEdcFetchPlans -->
  <testcase name="TC0005_08_EmptySolLevel3CreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_08_EmptySolLevel3CreateLeadFastag -->
  <testcase name="TC008_InternationalCardPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC008_InternationalCardPositiveValidateOtpCreate -->
  <testcase name="TC004_04_PlanAmexFetchMIDEmptyReqType" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_04_PlanAmexFetchMIDEmptyReqType -->
  <testcase name="TC0005_09_NoSolLevel3CreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_09_NoSolLevel3CreateLeadFastag -->
  <testcase name="TC004_05_PlanAmexFetchMIDInvalidReqType" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_05_PlanAmexFetchMIDInvalidReqType -->
  <testcase name="TC0010_MapEdcFetchPaymentStatus" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0010_MapEdcFetchPaymentStatus -->
  <testcase name="TC0005_10_EmptyVehicleNumCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_EmptyVehicleNumCreateLeadFastag -->
  <testcase name="TC004_06_PlanAmexFetchMIDDIfferentReqType" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_06_PlanAmexFetchMIDDIfferentReqType -->
  <testcase name="TC007_UpgradeMerchantPositiveSendOtpCreate" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC007_UpgradeMerchantPositiveSendOtpCreate -->
  <testcase name="TC0005_10_InvalidSolLevel3CreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_InvalidSolLevel3CreateLeadFastag -->
  <testcase name="TC007_UpgradeMerchantPositiveSendOtpCreate" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC007_UpgradeMerchantPositiveSendOtpCreate -->
  <testcase name="TC0005_10_InvalidVehicleNumCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_InvalidVehicleNumCreateLeadFastag -->
  <testcase name="TC004_07_PlanAmexPositiveFetchMID" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC004_07_PlanAmexPositiveFetchMID -->
  <testcase name="TC008_MapEdcFetchLeadDetails" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC008_MapEdcFetchLeadDetails -->
  <testcase name="TC009_MapEdcUpdateLead" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC009_MapEdcUpdateLead -->
  <testcase name="TC0005_10_NoVehicleNumCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_10_NoVehicleNumCreateLeadFastag -->
  <testcase name="TC008_MapEdcFetchLeadDetails" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC008_MapEdcFetchLeadDetails -->
  <testcase name="TC009_InternationalCardPositiveFetchDocs" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC009_InternationalCardPositiveFetchDocs -->
  <testcase name="TC0005_11_EmptyTagCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_11_EmptyTagCreateLeadFastag -->
  <testcase name="TC0011_MapEdcExtractQrCodeId" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0011_MapEdcExtractQrCodeId -->
  <testcase name="TC008_GroceryPlanValidateOtp" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC008_GroceryPlanValidateOtp -->
  <testcase name="TC0005_12_InvalidTagCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_12_InvalidTagCreateLeadFastag -->
  <testcase name="TC008_UpgradeMerchantPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC008_UpgradeMerchantPositiveValidateOtpCreate -->
  <testcase name="TC0005_13_NoTagCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_13_NoTagCreateLeadFastag -->
  <testcase name="TC009_AmexPlanValidateOtp" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC009_AmexPlanValidateOtp -->
  <testcase name="TC005_01_PlanAmexFetchPlanEmptyMid" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_01_PlanAmexFetchPlanEmptyMid -->
  <testcase name="TC010_InternationCardPlanValidateOtp" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC010_InternationCardPlanValidateOtp -->
  <testcase name="TC0005_14_NoFlowCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_14_NoFlowCreateLeadFastag -->
  <testcase name="TC005_02_PlanAmexFetchPlanInvalidMid" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_02_PlanAmexFetchPlanInvalidMid -->
  <testcase name="TC0005_16_InvalidFlowCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_16_InvalidFlowCreateLeadFastag -->
  <testcase name="TC011_EmiOfferingPlanValidateOtp" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC011_EmiOfferingPlanValidateOtp -->
  <testcase name="TC005_03_PlanAmexFetchPlanNoMid" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_03_PlanAmexFetchPlanNoMid -->
  <testcase name="TC0005_17_EmptyFlowCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_17_EmptyFlowCreateLeadFastag -->
  <testcase name="TC012_EdcAmcPlanValidateOtp" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC012_EdcAmcPlanValidateOtp -->
  <testcase name="TC005_04_PlanAmexFetchPlanEmptyEntity" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_04_PlanAmexFetchPlanEmptyEntity -->
  <testcase name="TC013_CashAtPosPlanFetchTerminal" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC013_CashAtPosPlanFetchTerminal -->
  <testcase name="TC0005_18_NoIssuanceTypeCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_18_NoIssuanceTypeCreateLeadFastag -->
  <testcase name="TC005_05_PlanAmexFetchPlanInvalidEntity" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_05_PlanAmexFetchPlanInvalidEntity -->
  <testcase name="TC0005_19_EmptyIssuanceTypeCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_19_EmptyIssuanceTypeCreateLeadFastag -->
  <testcase name="TC014_PaymentConfirmationPlanFetchTerminal" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanNegativeCases">
    <skipped/>
  </testcase> <!-- TC014_PaymentConfirmationPlanFetchTerminal -->
  <testcase name="TC005_06_PlanAmexFetchPlanDifferentEntity" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_06_PlanAmexFetchPlanDifferentEntity -->
  <testcase name="TC005_07_PlanAmexFetchPlanNoEntity" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_07_PlanAmexFetchPlanNoEntity -->
  <testcase name="TC005_08_PlanAmexFetchPlanEmptyCustId" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_08_PlanAmexFetchPlanEmptyCustId -->
  <testcase name="TC0005_20_InvalidIssuanceTypeCreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_20_InvalidIssuanceTypeCreateLeadFastag -->
  <testcase name="TC009_MapEdcUpdateLead" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC009_MapEdcUpdateLead -->
  <testcase name="TC005_09_PlanAmexFetchPlanInvalidCustId" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_09_PlanAmexFetchPlanInvalidCustId -->
  <testcase name="TC0005_21_CreateLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0005_21_CreateLeadFastag -->
  <testcase name="TC0010_MapEdcFetchPaymentStatus" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0010_MapEdcFetchPaymentStatus -->
  <testcase name="TC005_10_PlanAmexFetchPlanDifferentCustId" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_10_PlanAmexFetchPlanDifferentCustId -->
  <testcase name="TC009_MapEdcUpdateLead" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC009_MapEdcUpdateLead -->
  <testcase name="TC005_11_PlanAmexFetchPlanNoCustId" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_11_PlanAmexFetchPlanNoCustId -->
  <testcase name="TC010_InternationalCardPositivePanelSubmit" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC010_InternationalCardPositivePanelSubmit -->
  <testcase name="TC005_12_PlanAmexFetchPlanEmptyPPI" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_12_PlanAmexFetchPlanEmptyPPI -->
  <testcase name="TC0012_MapEdcFetchQrDetails" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0012_MapEdcFetchQrDetails -->
  <testcase name="TC009_UpgradeMerchantPositiveFetchDocs" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC009_UpgradeMerchantPositiveFetchDocs -->
  <testcase name="TC005_13_PlanAmexFetchPlanInvalidPPI" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_13_PlanAmexFetchPlanInvalidPPI -->
  <testcase name="TC0010_MapEdcFetchPaymentStatus" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0010_MapEdcFetchPaymentStatus -->
  <testcase name="TC0006_01_EmptyLeadIdValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_01_EmptyLeadIdValidateFastag -->
  <testcase name="TC005_14_PlanAmexFetchPlanDifferentPPI" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_14_PlanAmexFetchPlanDifferentPPI -->
  <testcase name="TC0010_MapEdcFetchPaymentStatus" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0010_MapEdcFetchPaymentStatus -->
  <testcase name="TC0006_02_InvalidLeadIdValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_02_InvalidLeadIdValidateFastag -->
  <testcase name="TC005_15_PlanAmexFetchPlanNoPPI" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_15_PlanAmexFetchPlanNoPPI -->
  <testcase name="TC011_PGCallBackFromPanel" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanInternationalCardPlan">
    <skipped/>
  </testcase> <!-- TC011_PGCallBackFromPanel -->
  <testcase name="TC0006_03_NoLeadIdValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_03_NoLeadIdValidateFastag -->
  <testcase name="TC005_16_PlanAmexFetchPlanInvalidEdcFlag" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_16_PlanAmexFetchPlanInvalidEdcFlag -->
  <testcase name="TC0011_MapEdcExtractQrCodeId" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0011_MapEdcExtractQrCodeId -->
  <testcase name="TC0006_04_EmptyVehicleValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_04_EmptyVehicleValidateFastag -->
  <testcase name="TC005_17_PlanAmexFetchPlanEmptyEdcFlag" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_17_PlanAmexFetchPlanEmptyEdcFlag -->
  <testcase name="TC013_MapEdcPayment" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC013_MapEdcPayment -->
  <testcase name="TC0006_05_InvalidVehicleValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_05_InvalidVehicleValidateFastag -->
  <testcase name="TC005_18_PlanAmexFetchPlanDifferentEdcFlag" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_18_PlanAmexFetchPlanDifferentEdcFlag -->
  <testcase name="TC0010_UpgradeMerchantPositivePanelSubmit" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMerchantPlanGrocery">
    <skipped/>
  </testcase> <!-- TC0010_UpgradeMerchantPositivePanelSubmit -->
  <testcase name="TC0006_06_NoVehicleValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_06_NoVehicleValidateFastag -->
  <testcase name="TC005_19_PlanAmexFetchPlanNoEdcFlag" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_19_PlanAmexFetchPlanNoEdcFlag -->
  <testcase name="TC0011_MapEdcExtractQrCodeId" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0011_MapEdcExtractQrCodeId -->
  <testcase name="TC0006_07_EmptyTagValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_07_EmptyTagValidateFastag -->
  <testcase name="TC005_PlanAmexPositiveFetchUpgradePlans" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC005_PlanAmexPositiveFetchUpgradePlans -->
  <testcase name="TC0011_MapEdcExtractQrCodeId" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0011_MapEdcExtractQrCodeId -->
  <testcase name="TC0006_08_InvalidTagValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_08_InvalidTagValidateFastag -->
  <testcase name="TC0012_MapEdcFetchQrDetails" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0012_MapEdcFetchQrDetails -->
  <testcase name="TC014_MapEdcFetchLeadDetailsAfterPayment" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC014_MapEdcFetchLeadDetailsAfterPayment -->
  <testcase name="TC0006_09_NoTagValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_09_NoTagValidateFastag -->
  <testcase name="TC0012_MapEdcFetchQrDetails" time="0.001" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0012_MapEdcFetchQrDetails -->
  <testcase name="TC006_PlanAmexPositiveFetchTnC" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC006_PlanAmexPositiveFetchTnC -->
  <testcase name="TC0006_10_ValidateFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0006_10_ValidateFastag -->
  <testcase name="TC0012_MapEdcFetchQrDetails" time="0.001" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0012_MapEdcFetchQrDetails -->
  <testcase name="TC013_MapEdcPayment" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC013_MapEdcPayment -->
  <testcase name="TC015_MapEdcFetchLeadDetailsForPgReferenceId" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC015_MapEdcFetchLeadDetailsForPgReferenceId -->
  <testcase name="TC013_MapEdcPayment" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC013_MapEdcPayment -->
  <testcase name="TC007_PlanAmexPositiveSendOtpCreate" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC007_PlanAmexPositiveSendOtpCreate -->
  <testcase name="TC0007_01_InvalidSolutionFetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_01_InvalidSolutionFetchV3FastagDetails -->
  <testcase name="TC0013_MapEdcPayment" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0013_MapEdcPayment -->
  <testcase name="TC0013_PGCallbackForEdc" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0013_PGCallbackForEdc -->
  <testcase name="TC0007_02_EmptySolutionFetchV3FastagDetails" time="0.001" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_02_EmptySolutionFetchV3FastagDetails -->
  <testcase name="TC0016_ManualPGCallbackForEdc" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0016_ManualPGCallbackForEdc -->
  <testcase name="TC014_MapEdcFetchLeadDetailsAfterPayment" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC014_MapEdcFetchLeadDetailsAfterPayment -->
  <testcase name="TC0007_03_DifferentSolutionFetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_03_DifferentSolutionFetchV3FastagDetails -->
  <testcase name="TC008_PlanAmexPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC008_PlanAmexPositiveValidateOtpCreate -->
  <testcase name="TC0014_PGCallbackForEdc" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0014_PGCallbackForEdc -->
  <testcase name="TC16_CreateMerchantonPG" time="15.701" classname="OCL.BrandEMI.BrandEMITest"/>
  <testcase name="TC0007_04_NoSolutionFetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_04_NoSolutionFetchV3FastagDetails -->
  <testcase name="TC0014_MapEdcFetchQnA" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0014_MapEdcFetchQnA -->
  <testcase name="TC0017_MapEdcFetchQnA" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0017_MapEdcFetchQnA -->
  <testcase name="TC015_MapEdcFetchLeadDetailsForPgReferenceId" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC015_MapEdcFetchLeadDetailsForPgReferenceId -->
  <testcase name="TC0007_05_InvalidEntityFetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_05_InvalidEntityFetchV3FastagDetails -->
  <testcase name="TC019_PGCallBackFromPanel" time="0.0" classname="OCL.Business.UpgradeMerchantPlan.FlowUpgradeMechantPlanAmex">
    <skipped/>
  </testcase> <!-- TC019_PGCallBackFromPanel -->
  <testcase name="TC0015_MapEdcFetchQnA" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0015_MapEdcFetchQnA -->
  <testcase name="TC0015_MapEdcMachineMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0015_MapEdcMachineMapping -->
  <testcase name="TC0007_06_DifferentEntityFetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_06_DifferentEntityFetchV3FastagDetails -->
  <testcase name="TC0018_MapEdcMachineMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0018_MapEdcMachineMapping -->
  <testcase name="TC0016_ManualPGCallbackForEdc" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0016_ManualPGCallbackForEdc -->
  <testcase name="TC0016_MapEdcMachineMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0016_MapEdcMachineMapping -->
  <testcase name="TC0007_07_EmptyEntityFetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_07_EmptyEntityFetchV3FastagDetails -->
  <testcase name="TC0019_MapEdcResendOtp" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0019_MapEdcResendOtp -->
  <testcase name="TC0016_MapEdcResendOtp" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0016_MapEdcResendOtp -->
  <testcase name="TC0017_MapEdcFetchQnA" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0017_MapEdcFetchQnA -->
  <testcase name="TC0007_08_NoEntityFetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_08_NoEntityFetchV3FastagDetails -->
  <testcase name="TC0017_MapEdcResendOtp" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0017_MapEdcResendOtp -->
  <testcase name="TC0017_MapEdcFinishMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0017_MapEdcFinishMapping -->
  <testcase name="TC0020_MapEdcFinishMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC0020_MapEdcFinishMapping -->
  <testcase name="TC0007_09_FetchV3FastagDetails" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0007_09_FetchV3FastagDetails -->
  <testcase name="TC0018_MapEdcMachineMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0018_MapEdcMachineMapping -->
  <testcase name="TC0018_MapEdcFinishMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDC">
    <skipped/>
  </testcase> <!-- TC0018_MapEdcFinishMapping -->
  <testcase name="TC0018_MapEdcFetchBrandAssociation" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0018_MapEdcFetchBrandAssociation -->
  <testcase name="TC021_MapEdcFetchLeadDetailsAfterMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC021_MapEdcFetchLeadDetailsAfterMapping -->
  <testcase name="TC0008_SentOtpFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0008_SentOtpFastag -->
  <testcase name="TC0019MapAndroidWithPosFetchStoreCategory" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0019MapAndroidWithPosFetchStoreCategory -->
  <testcase name="TC0019_MapEdcResendOtp" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0019_MapEdcResendOtp -->
  <testcase name="TC022_AMCFileRejectedFromPanel" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC022_AMCFileRejectedFromPanel -->
  <testcase name="TC0009_ValidateOtpFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC0009_ValidateOtpFastag -->
  <testcase name="TC0020_MapEdcFinishMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC0020_MapEdcFinishMapping -->
  <testcase name="TC0020MapPosPositiveSubmitLead" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0020MapPosPositiveSubmitLead -->
  <testcase name="TC023_UploadAMCFileFromPanel" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC023_UploadAMCFileFromPanel -->
  <testcase name="TC00010_01_InvalidEntityFetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_01_InvalidEntityFetchMerchantFastag -->
  <testcase name="TC021_MapEdcFetchLeadDetailsAfterMapping" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC021_MapEdcFetchLeadDetailsAfterMapping -->
  <testcase name="TC0021MapPosNegativeSubmitLead" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCAndroidWithPOS">
    <skipped/>
  </testcase> <!-- TC0021MapPosNegativeSubmitLead -->
  <testcase name="TC024_MapEdcFetchLeadDetailsAfterAMCFileUpload" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithAMC">
    <skipped/>
  </testcase> <!-- TC024_MapEdcFetchLeadDetailsAfterAMCFileUpload -->
  <testcase name="TC00010_02_DifferentEntityFetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_02_DifferentEntityFetchMerchantFastag -->
  <testcase name="TC022_AMCFileRejectedFromPanel" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC022_AMCFileRejectedFromPanel -->
  <testcase name="TC00010_03_EmptyEntityFetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_03_EmptyEntityFetchMerchantFastag -->
  <testcase name="TC023_UploadAMCFileFromPanel" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC023_UploadAMCFileFromPanel -->
  <testcase name="TC00010_04_InvalidEntityFetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_04_InvalidEntityFetchMerchantFastag -->
  <testcase name="TC024_MapEdcFetchLeadDetailsAfterAMCFileUpload" time="0.0" classname="OCL.Business.MapEDC.FlowMapEDCWithRAScore">
    <skipped/>
  </testcase> <!-- TC024_MapEdcFetchLeadDetailsAfterAMCFileUpload -->
  <testcase name="TC00010_05_EmptyEntityFetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_05_EmptyEntityFetchMerchantFastag -->
  <testcase name="TC00010_06_DifferentEntityFetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_06_DifferentEntityFetchMerchantFastag -->
  <testcase name="TC00010_07_EmptySessionTokenFetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_07_EmptySessionTokenFetchMerchantFastag -->
  <testcase name="TC00010_08_FetchMerchantFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00010_08_FetchMerchantFastag -->
  <testcase name="TC00011_SubmitLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00011_SubmitLeadFastag -->
  <testcase name="TC00012_ValidateAfterSubmitFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00012_ValidateAfterSubmitFastag -->
  <testcase name="TC00012_UploadDocumentsFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00012_UploadDocumentsFastag -->
  <testcase name="TC00013_01_InvalidLeadFetchPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_01_InvalidLeadFetchPaymentStatusFastag -->
  <testcase name="TC00013_02_EmptyLeadFetchPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_02_EmptyLeadFetchPaymentStatusFastag -->
  <testcase name="TC00013_03_NoLeadFetchPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_03_NoLeadFetchPaymentStatusFastag -->
  <testcase name="TC00013_04_EmptyQrPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_04_EmptyQrPaymentStatusFastag -->
  <testcase name="TC00013_05_InvalidQrPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_05_InvalidQrPaymentStatusFastag -->
  <testcase name="TC00013_06_NoQrPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_06_NoQrPaymentStatusFastag -->
  <testcase name="TC00013_07_NoParamsPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_07_NoParamsPaymentStatusFastag -->
  <testcase name="TC00013_08_WrongParamsPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_08_WrongParamsPaymentStatusFastag -->
  <testcase name="TC00013_09_FetchPaymentStatusFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00013_09_FetchPaymentStatusFastag -->
  <testcase name="TC00014_MakePaymentFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00014_MakePaymentFastag -->
  <testcase name="TC00015_RejectLeadFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00015_RejectLeadFastag -->
  <testcase name="TC00016_UploadRejectedDocumentsFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00016_UploadRejectedDocumentsFastag -->
  <testcase name="TC00017_SubmitApproveDocFastag" time="0.0" classname="OCL.Individual.FastagAssisted.FlowFastagAssistedBasic">
    <skipped/>
  </testcase> <!-- TC00017_SubmitApproveDocFastag -->
  <testcase name="TC_005_CreateTerminalInPG" time="114.61" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_006_CreateTerminalInPGWithMappedSerialNumber" time="2.131" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_007_ActiveTerminalInPGWithInvalidSerialNumber" time="2.323" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_008_ActiveTerminalInPGWithInvalidTID" time="1.922" classname="OCL.PG.FlowCreateTerminalInPG"/>
  <testcase name="TC_009_ActiveTerminalInPGWithInvalidModelName" time="105.779" classname="OCL.PG.FlowCreateTerminalInPG">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.PG.FlowCreateTerminalInPG.TC_009_ActiveTerminalInPGWithInvalidModelName(FlowCreateTerminalInPG.java:238)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_009_ActiveTerminalInPGWithInvalidModelName -->
  <testcase name="TC_010_ActiveTerminalInPGWithInvalidMID" time="0.0" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_010_ActiveTerminalInPGWithInvalidMID -->
  <testcase name="TC_011_ActiveTerminalInPGWithInvalidToken" time="0.0" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_011_ActiveTerminalInPGWithInvalidToken -->
  <testcase name="TC_012_CreateTerminalInPGWithInvalidMID" time="0.0" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_012_CreateTerminalInPGWithInvalidMID -->
  <testcase name="TC_013_CreateTerminalInPGWithInvalidToken" time="0.0" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_013_CreateTerminalInPGWithInvalidToken -->
  <testcase name="TC_014_CreateTerminalInPGWithValidData" time="0.0" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_014_CreateTerminalInPGWithValidData -->
  <testcase name="TC_015_ActiveTerminalInPG" time="0.0" classname="OCL.PG.FlowCreateTerminalInPG">
    <skipped/>
  </testcase> <!-- TC_015_ActiveTerminalInPG -->
  <testcase name="TC_001_CheckEligibilityforBrandEmiDIYWithAllParametersPassed" time="0.537" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_001_CheckEligibilityforBrandEmiDIYWithAllParametersPassed(FlowBrandEmiWithDocUploadDIY.java:81)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_001_CheckEligibilityforBrandEmiDIYWithAllParametersPassed -->
  <testcase name="fetchPlanforEDCDIY" time="0.6" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="fetchPlanforMposDIY" time="0.651" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MposDIY.FlowMposDIY.fetchPlanforMposDIY(FlowMposDIY.java:88)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchPlanforMposDIY -->
  <testcase name="fetchPlanforCashAtPosDIY" time="0.681" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchPlanforEMIOfferingDIY" time="1.058" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="SoundBoxPositiveSendOtp" time="0.82" classname="OCL.Individual.SoundBox.FlowSoundBox"/>
  <testcase name="UnMapEdcReturnPositiveSendOtpBusiness" time="0.993" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
  <testcase name="UnMapEdcPositiveSendOtpBusiness" time="0.843" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return"/>
  <testcase name="FetchSoundBoxReplacementQNA" time="0.653" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_01_CheckEligibilityforBrandEmiDIYWithoutpassingCustID" time="0.569" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="UnMapEdcReplacePositiveSendOtpBusiness" time="0.565" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC"/>
  <testcase name="FetchTnCSoundBox" time="0.536" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [https://s3-ap-southeast-1.amazonaws.com/accounts-assets/test/2769.html] but found [https://cif-staging.paytm.in/kyc/tnc/get/*********]">
      <![CDATA[java.lang.AssertionError: did not expect to find [https://s3-ap-southeast-1.amazonaws.com/accounts-assets/test/2769.html] but found [https://cif-staging.paytm.in/kyc/tnc/get/*********]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.SoundBox.TestSoundBoxFlow.FetchTnCSoundBox(TestSoundBoxFlow.java:1410)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- FetchTnCSoundBox -->
  <testcase name="UnMapEdcPositiveSendOtpBusiness" time="0.596" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace"/>
  <testcase name="GetMerchantMID" time="0.825" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_002_GetAllBrandsWithCorrectMIDPassed" time="3.031" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="TC_02_CheckEligibilityforBrandEmiDIYWithoutpassingMID" time="0.452" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_03_CheckEligibilityforBrandEmiDIYWithoutpassingSolutionType" time="0.333" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_003_ValidateDealerWithInCorrectDealerPassed" time="0.469" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="GetPinCodeDetails" time="0.989" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_04_CheckEligibilityforBrandEmiDIYWithoutpassingChannel" time="0.339" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_004_ValidateDealerWithCorrectDealerPassed" time="0.432" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="GetSoundBoxDeviceType" time="0.52" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [18] but found [null]">
      <![CDATA[java.lang.AssertionError: did not expect to find [18] but found [null]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:131)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.SoundBox.TestSoundBoxFlow.GetSoundBoxDeviceType(TestSoundBoxFlow.java:780)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- GetSoundBoxDeviceType -->
  <testcase name="TC_05_CheckEligibilityforBrandEmiDIYWithoutpassingPlanType" time="0.283" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="GetSoundBoxLeadCount" time="0.581" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [1] but found [0]">
      <![CDATA[java.lang.AssertionError: did not expect to find [1] but found [0]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:575)
at org.testng.Assert.assertEquals(Assert.java:585)
at OCL.Individual.SoundBox.TestSoundBoxFlow.GetSoundBoxLeadCount(TestSoundBoxFlow.java:1000)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- GetSoundBoxLeadCount -->
  <testcase name="SoundBoxPositiveValidateOtp" time="3.83" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [400]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [400]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.SoundBox.FlowSoundBox.SoundBoxPositiveValidateOtp(FlowSoundBox.java:99)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- SoundBoxPositiveValidateOtp -->
  <testcase name="GetSoundBoxMorefunDetails" time="0.567" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_06_CheckEligibilityforBrandEmiDIYWithInvalidCustidPassed" time="1.644" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY.TC_06_CheckEligibilityforBrandEmiDIYWithInvalidCustidPassed(FlowBrandEmiDIY.java:148)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_06_CheckEligibilityforBrandEmiDIYWithInvalidCustidPassed -->
  <testcase name="NegativeCaseGetMerchantMIDwithInValidKeyForCustID" time="0.591" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_07_CheckEligibilityforBrandEmiDIYWithInvalidMidPassed" time="0.339" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="NegativeCaseGetMerchantMIDwithInvalidCustID" time="0.627" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="NegativeCaseGetMerchantMIDwithoutsendCustID" time="0.765" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_08_CheckEligibilityforBrandEmiDIYWithInvalidSolutionTypePassed" time="1.552" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="UnmapEDCPositiveValidateOtp" time="6.994" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at com.goldengate.common.BaseMethod.getOTPFromSellerPanel(BaseMethod.java:1868)
at OCL.Business.UnmapEDC.FlowUnmapEDC_Return.UnmapEDCPositiveValidateOtp(FlowUnmapEDC_Return.java:86)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 16 more
]]>
    </failure>
  </testcase> <!-- UnmapEDCPositiveValidateOtp -->
  <testcase name="TC_005_GetActiveBrandListFromKyb" time="4.79" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="SoundBoxPositiveFetchMID" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxPositiveFetchMID -->
  <testcase name="TC_09_CheckEligibilityforBrandEmiDIYWithInvalidChannelPassed" time="1.355" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="UnMapEdcPositiveGetBusiness" time="0.001" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveGetBusiness -->
  <testcase name="NegativecaseSoundboxLeadCreateInvalidMID" time="0.715" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_006_CreateLeadBrandEMIDIY" time="0.728" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_006_CreateLeadBrandEMIDIY(FlowBrandEmiWithDocUploadDIY.java:176)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_006_CreateLeadBrandEMIDIY -->
  <testcase name="SoundBoxCreateLead" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxCreateLead -->
  <testcase name="TC_007_FetchParentLeadBrandEMIDIY" time="0.129" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchleadBrandEmiDIY(MiddlewareServices.java:4128)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_007_FetchParentLeadBrandEMIDIY(FlowBrandEmiWithDocUploadDIY.java:201)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_007_FetchParentLeadBrandEMIDIY -->
  <testcase name="UnMapEdcPositiveGetBusinessProfile" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveGetBusinessProfile -->
  <testcase name="TC_10_CheckEligibilityforBrandEmiDIYWithInvalidPlanTypePassed" time="0.667" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_008_FetchChildLeadBrandEMIDIY" time="0.163" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchleadBrandEmiDIY(MiddlewareServices.java:4128)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_008_FetchChildLeadBrandEMIDIY(FlowBrandEmiWithDocUploadDIY.java:222)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_008_FetchChildLeadBrandEMIDIY -->
  <testcase name="NegativecaseSoundboxLeadCreateNoAgentCustID" time="0.595" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="SoundBoxFetchPlan" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxFetchPlan -->
  <testcase name="TC_11_CheckEligibilityforBrandEmiDIYWithEmptyCustIDPassed" time="0.292" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="NegativecaseSoundboxLeadCreateNoEntityType" time="0.504" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_12_CheckEligibilityforBrandEmiDIYWithEmptyMIDPassed" time="0.311" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="UnMapEdcPositiveFetchMID" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveFetchMID -->
  <testcase name="UnmapEDCPositiveValidateOtp" time="5.767" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at com.goldengate.common.BaseMethod.getOTPFromSellerPanel(BaseMethod.java:1868)
at OCL.Business.UnmapEDC.FlowUnmapEDC_Replace.UnmapEDCPositiveValidateOtp(FlowUnmapEDC_Replace.java:86)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 16 more
]]>
    </failure>
  </testcase> <!-- UnmapEDCPositiveValidateOtp -->
  <testcase name="PositiveGetMerchantStatus" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- PositiveGetMerchantStatus -->
  <testcase name="UnMapEdcCreateLead" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- UnMapEdcCreateLead -->
  <testcase name="NegativecaseSoundboxLeadCreateNoMerchantName" time="0.552" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_13_CheckEligibilityforBrandEmiDIYWithEmptyPlanTypePassed" time="0.582" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="SoundBoxChoosePlan" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxChoosePlan -->
  <testcase name="UnMapEdcPositiveGetBusiness" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveGetBusiness -->
  <testcase name="UnMapEdcPositiveGetMerchantDetails" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveGetMerchantDetails -->
  <testcase name="SoundBoxFetchPaymentStatus" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxFetchPaymentStatus -->
  <testcase name="NegativecaseSoundboxLeadCreateNoMid" time="0.663" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_14_CheckEligibilityforBrandEmiDIYWithEmptyChannelPassed" time="1.165" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="NegativecaseSoundboxLeadCreateNoSolutionSubType" time="0.629" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="UnMapEdcReturnReasons" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnReasons -->
  <testcase name="UnMapEdcPositiveGetBusinessProfile" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveGetBusinessProfile -->
  <testcase name="MapEdcFetchQnA" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- MapEdcFetchQnA -->
  <testcase name="SoundBoxExtractQrCodeId" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxExtractQrCodeId -->
  <testcase name="fetchAllTerminalFromPG" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- fetchAllTerminalFromPG -->
  <testcase name="UnMapEdcPositiveFetchMID" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveFetchMID -->
  <testcase name="SoundBoxFetchQrDetails" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxFetchQrDetails -->
  <testcase name="TC_15_CheckEligibilityforBrandEmiDIYWithEmptySolutionTypePassed" time="1.173" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="UnMapEdcCreateLead" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- UnMapEdcCreateLead -->
  <testcase name="FetchSoundBoxBindUrl" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- FetchSoundBoxBindUrl -->
  <testcase name="SoundBoxPayment" time="0.0" classname="OCL.Individual.SoundBox.FlowSoundBox">
    <skipped/>
  </testcase> <!-- SoundBoxPayment -->
  <testcase name="NegativecaseSoundboxLeadCreateNoUserCustID" time="1.763" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="ValidateEdcQrReturn" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- ValidateEdcQrReturn -->
  <testcase name="UnMapEdcPositiveGetMerchantDetails" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveGetMerchantDetails -->
  <testcase name="UnMapEdcReplaceReasons" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- UnMapEdcReplaceReasons -->
  <testcase name="TC_16_CheckEligibilityforBrandEmiDIYWithAllParametersPassed" time="1.239" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="UnmapEDCMachine_Return" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Return">
    <skipped/>
  </testcase> <!-- UnmapEDCMachine_Return -->
  <testcase name="MapEdcFetchQnA" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- MapEdcFetchQnA -->
  <testcase name="fetchAllTerminalFromPG" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- fetchAllTerminalFromPG -->
  <testcase name="ValidateEdcQrReplace" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- ValidateEdcQrReplace -->
  <testcase name="TC_17_GetAllBrandsWithInvalidMIDPassed" time="0.373" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="SoundboxLeadCreate" time="2.485" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.SoundBox.TestSoundBoxFlow.SoundboxLeadCreate(TestSoundBoxFlow.java:314)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- SoundboxLeadCreate -->
  <testcase name="ReplaceEdcMachineMapping" time="0.0" classname="OCL.Business.UnmapEDC.FlowUnmapEDC_Replace">
    <skipped/>
  </testcase> <!-- ReplaceEdcMachineMapping -->
  <testcase name="SoundboxReplacementLeadCreate" time="0.658" classname="OCL.Individual.SoundBox.TestSoundBoxFlow"/>
  <testcase name="TC_19_GetAllBrandsWithCorrectMIDPassed" time="2.396" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_18_GetAllBrandsWithEmptyMIDPassed" time="0.336" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_20_ValidateDealerWithInCorrectmidPassed" time="0.608" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="morefunLeadCreate" time="2.54" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.Individual.SoundBox.TestSoundBoxFlow.morefunLeadCreate(TestSoundBoxFlow.java:737)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- morefunLeadCreate -->
  <testcase name="FetchPlanSoundBox" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- FetchPlanSoundBox -->
  <testcase name="TC_21_ValidateDealerWithInCorrectDealerCodePassed" time="0.658" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="GetSoundBoxMerchantDetails" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- GetSoundBoxMerchantDetails -->
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailsIncorrectLeadIDKey" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailsIncorrectLeadIDKey -->
  <testcase name="TC_22_ValidateDealerWithInCorrectBrandIdPassed" time="0.358" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailsInvalidLeadID" time="0.001" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailsInvalidLeadID -->
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithInvalidSessionToken" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithInvalidSessionToken -->
  <testcase name="TC_009_FetchStatusLeadBrandEMIDiy" time="11.853" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_009_FetchStatusLeadBrandEMIDiy(FlowBrandEmiWithDocUploadDIY.java:238)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_009_FetchStatusLeadBrandEMIDiy -->
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithNoEntityType" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithNoEntityType -->
  <testcase name="TC_23_ValidateDealerWithInCorrectCustIdPassed" time="0.669" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_010_ManualPGCallback" time="0.086" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithNoLeadID" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithNoLeadID -->
  <testcase name="TC_24_ValidateDealerWithoutTokenPassed" time="0.264" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="NegativeCaseGetSoundBoxMerchantDetailswithoutLeadID" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativeCaseGetSoundBoxMerchantDetailswithoutLeadID -->
  <testcase name="TC_25_ValidateDealerWithNullMidPassed" time="0.41" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="NegativecaseFetchPlanSoundBoxwithoutLeadID" time="0.001" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- NegativecaseFetchPlanSoundBoxwithoutLeadID -->
  <testcase name="TC_26_ValidateDealerWithNullBrandIDPassed" time="0.614" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="FetchPlanMorefun" time="0.0" classname="OCL.Individual.SoundBox.TestSoundBoxFlow">
    <skipped/>
  </testcase> <!-- FetchPlanMorefun -->
  <testcase name="TC_27_ValidateDealerWithNullDealerCodePassed" time="0.694" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_28_ValidateDealerWithNullCustIdPassed" time="0.608" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_29_ValidateDealerWithAllParamsNullPassed" time="0.314" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_30_ValidateDealerWithCorrectDealerPassed" time="0.387" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_31_FetchPlanEDCDIYWithEmptySolutionType" time="0.336" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_32_FetchPlanEDCDIYWithEmptyPlanType" time="0.371" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_33_FetchPlanEDCDIYWithEmptyMID" time="0.346" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_34_FetchPlanEDCDIYWithEmptyToken" time="0.273" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY"/>
  <testcase name="TC_35_FetchPlanEDCDIY" time="0.381" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [500]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [500]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY.TC_35_FetchPlanEDCDIY(FlowBrandEmiDIY.java:620)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_35_FetchPlanEDCDIY -->
  <testcase name="TC_36_CreateLeadBrandEMIDIYWithInvalidSolution" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_36_CreateLeadBrandEMIDIYWithInvalidSolution -->
  <testcase name="TC_37_CreateLeadBrandEMIDIYWithInvalidChannel" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_37_CreateLeadBrandEMIDIYWithInvalidChannel -->
  <testcase name="TC_011_FetchChildLeadStagefromPanelBrandEMIDiy" time="6.916" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_011_FetchChildLeadStagefromPanelBrandEMIDiy(FlowBrandEmiWithDocUploadDIY.java:267)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_011_FetchChildLeadStagefromPanelBrandEMIDiy -->
  <testcase name="TC_38_CreateLeadBrandEMIDIYWithInvalidSolutionTypeLevel2" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_38_CreateLeadBrandEMIDIYWithInvalidSolutionTypeLevel2 -->
  <testcase name="TC_39_CreateLeadBrandEMIDIYWithInvalidEntity" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_39_CreateLeadBrandEMIDIYWithInvalidEntity -->
  <testcase name="TC_40_CreateLeadBrandEMIDIY" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_40_CreateLeadBrandEMIDIY -->
  <testcase name="TC_041_fetchPlanforBrandEMIDIYWithEmptySolutionType" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_041_fetchPlanforBrandEMIDIYWithEmptySolutionType -->
  <testcase name="TC_042_fetchPlanforBrandEMIDIYWithInvalidmid" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_042_fetchPlanforBrandEMIDIYWithInvalidmid -->
  <testcase name="TC_043_fetchPlanforBrandEMIDIYWithInvalidPlanType" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_043_fetchPlanforBrandEMIDIYWithInvalidPlanType -->
  <testcase name="TC_044_fetchPlanforBrandEMIDIY" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_044_fetchPlanforBrandEMIDIY -->
  <testcase name="TC_045_fetchLeadStatusBrandEMIDIY" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_045_fetchLeadStatusBrandEMIDIY -->
  <testcase name="TC_46_FetchParentLeadBrandEMIDIY" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_46_FetchParentLeadBrandEMIDIY -->
  <testcase name="TC_47_FetchChildLeadBrandEMIDIY" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_47_FetchChildLeadBrandEMIDIY -->
  <testcase name="TC_48_FetchParentLeadBrandEMIDIYWithInvalidSolution" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_48_FetchParentLeadBrandEMIDIYWithInvalidSolution -->
  <testcase name="TC_012_FetchParentLeadStagefromPanelBrandEMIDiy" time="6.955" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY.TC_012_FetchParentLeadStagefromPanelBrandEMIDiy(FlowBrandEmiWithDocUploadDIY.java:287)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- TC_012_FetchParentLeadStagefromPanelBrandEMIDiy -->
  <testcase name="TC_49_FetchParentLeadBrandEMIDIYWithoutParamAll" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_49_FetchParentLeadBrandEMIDIYWithoutParamAll -->
  <testcase name="TC_50_FetchNoOfLeadsBrandEMIDIY" time="0.0" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiDIY">
    <skipped/>
  </testcase> <!-- TC_50_FetchNoOfLeadsBrandEMIDIY -->
  <testcase name="TC_013_CheckBrandActiveORNotInKyb" time="1.813" classname="OCL.DIY.BrandEmiDIY.FlowBrandEmiWithDocUploadDIY"/>
  <testcase name="fetchProductIdforCashAtPosDIY" time="0.56" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchProductIdforEDCDIY" time="0.595" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchProductIdforEDCDIY(FlowMapEdcDIY.java:82)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchProductIdforEDCDIY -->
  <testcase name="fetchProductIdforEMIOfferingDIY" time="0.978" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchPlanforInvalidMidMposDIY" time="1.42" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="UnmapEDCReturnPositiveValidateOtp" time="6.009" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at com.goldengate.common.BaseMethod.getOTPFromSellerPanel(BaseMethod.java:1868)
at OCL.Business.UnmapEDC.FlowReturnWithClaimAMC.UnmapEDCReturnPositiveValidateOtp(FlowReturnWithClaimAMC.java:88)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 16 more
]]>
    </failure>
  </testcase> <!-- UnmapEDCReturnPositiveValidateOtp -->
  <testcase name="UnmapEDCReplacePositiveValidateOtp" time="5.927" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at com.goldengate.common.BaseMethod.getOTPFromSellerPanel(BaseMethod.java:1868)
at OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC.UnmapEDCReplacePositiveValidateOtp(FlowReplaceWithClaimAMC.java:84)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 16 more
]]>
    </failure>
  </testcase> <!-- UnmapEDCReplacePositiveValidateOtp -->
  <testcase name="UnMapEdcReturnPositiveGetBusiness" time="0.002" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnPositiveGetBusiness -->
  <testcase name="fetchRentalAmountforCashAtPosDIY" time="0.569" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchPriceforEDCDIY" time="0.572" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchPriceforEDCDIY(FlowMapEdcDIY.java:99)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchPriceforEDCDIY -->
  <testcase name="UnMapEdcReplacePositiveGetBusiness" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReplacePositiveGetBusiness -->
  <testcase name="fetchtotalPriceforEMIOfferingDIY" time="0.827" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchProductIdforEMIOfferingDIY" time="1.404" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="UnMapEdcPositiveFetchMID" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveFetchMID -->
  <testcase name="UnMapEdcPositiveFetchMID" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveFetchMID -->
  <testcase name="fetchEdcPlanIdforEDCDIY" time="0.373" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchEdcPlanIdforEDCDIY(FlowMapEdcDIY.java:116)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchEdcPlanIdforEDCDIY -->
  <testcase name="AcceptTermsAndConditionsCashAtPosDIY" time="0.378" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchRentalpriceforEMIOfferingDIY" time="0.789" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchPriceforMposDIY" time="1.095" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="UnMapEdcReturnWithClaimAMCReasons" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnWithClaimAMCReasons -->
  <testcase name="AcceptTermsAndConditionsEMIOfferingDIY" time="0.327" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="AcceptTermsAndConditionsEdcDIY" time="0.346" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="UnMapEdcReplaceWithClaimAMCReasons" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReplaceWithClaimAMCReasons -->
  <testcase name="CheckEligibilityforCashAtPosDIY" time="0.578" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchRentalAmountforMposDIY" time="1.127" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="UnMapEdcReturnWithClaimAMCCreateLead" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnWithClaimAMCCreateLead -->
  <testcase name="UnMapEdcReplaceWithClaimAMCCreateLead" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReplaceWithClaimAMCCreateLead -->
  <testcase name="invalidPriceforEdcDiy" time="0.613" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="CheckEligibilityWithInvalidMIDforCashAtPosDIY" time="0.954" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="CheckEligibilityforEMIOfferingDIY" time="1.149" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchMposPlanIdforMposDIY" time="1.62" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="UnMapEdcReturnPositiveGetMerchantDetails" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReturnPositiveGetMerchantDetails -->
  <testcase name="UnMapEdcReplacePositiveGetMerchantDetails" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcReplacePositiveGetMerchantDetails -->
  <testcase name="invalidPlanIdforEdcDiy" time="0.39" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="fetchPlanforInvalidSolutionTypeMposDIY" time="0.396" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="CheckEligibilityWithInvalidCustIdforCashAtPosDIY" time="0.426" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="invalidPriceforEmiOfferingDiy" time="0.743" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="UnMapEdcFetchQnA" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcFetchQnA -->
  <testcase name="validateOrderforEdcDiy" time="0.406" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="UnMapEdcFetchQnA" time="0.001" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcFetchQnA -->
  <testcase name="CheckEligibilityWithInvalidPlanTypeforCashAtPosDIY" time="0.654" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="invalidmidforEmiOfferingDiy" time="0.685" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchPlanWithAdditionalParamsforMposDIY" time="1.321" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPlanWithSmallMerchantEditDisableFlagMposDIY" time="1.146" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPlanWithSmallMerchantFlagMposDIY" time="1.028" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="UnmapEDCfetchAllTerminalFromPG" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnmapEDCfetchAllTerminalFromPG -->
  <testcase name="invalidPlanTypeforEmiOfferingDiy" time="0.35" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="AcceptTermsAndConditionsEdcDIY" time="0.357" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="invalidPriceforCashAtPosDiy" time="0.379" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="UnmapEDCfetchAllTerminalFromPG" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnmapEDCfetchAllTerminalFromPG -->
  <testcase name="createAccessToken" time="0.441" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="ValidateEdcQrReturnWithClaimAMC" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- ValidateEdcQrReturnWithClaimAMC -->
  <testcase name="validateOrderforMposDiy" time="0.31" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="invalidmidforCashAtPosDiy" time="0.313" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="invalidProductIdforEmiOfferingDiy" time="0.325" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="ValidateEdcQrReplace" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- ValidateEdcQrReplace -->
  <testcase name="createQRforEdcDiy" time="5.225" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="validateOrderforCashAtPosDiy" time="0.374" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="FetchTnCUnmapEDC" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- FetchTnCUnmapEDC -->
  <testcase name="invalidSolutionTypeforEmiOfferingDiy" time="0.499" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="ReplaceEdcMachineMappingWithClaimAMC" time="0.0" classname="OCL.Business.UnmapEDC.FlowReplaceWithClaimAMC">
    <skipped/>
  </testcase> <!-- ReplaceEdcMachineMappingWithClaimAMC -->
  <testcase name="invalidPriceforMposDiy" time="0.672" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPaymentStatusforEdcDiy" time="0.755" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY"/>
  <testcase name="UnMapEdcPositiveSendOtp" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveSendOtp -->
  <testcase name="failedToFetchedPlansForEMIOfferingDIY" time="0.407" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidPlanIdforMposDiy" time="0.412" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="CreateLeadEdcDiy" time="0.512" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.CreateLeadEdcDiy(FlowMapEdcDIY.java:273)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- CreateLeadEdcDiy -->
  <testcase name="createLeadforCashAtPosDiy" time="1.492" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.createLeadforCashAtPosDiy(FlowCashAtPosDIY.java:324)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- createLeadforCashAtPosDiy -->
  <testcase name="UnMapEdcPositiveValidateOtp" time="0.0" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC">
    <skipped/>
  </testcase> <!-- UnMapEdcPositiveValidateOtp -->
  <testcase name="invalidProductIdforMposDiy" time="0.394" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="CallbackinprogressEdcDiy" time="0.529" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.CallbackinprogressEdcDiy(FlowMapEdcDIY.java:303)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- CallbackinprogressEdcDiy -->
  <testcase name="invalidMidInFetchPlanForEMIOfferingDIY" time="0.546" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchLeadforCashAtPosDiy" time="1.52" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [400] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [400] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchLeadforCashAtPosDiy(FlowCashAtPosDIY.java:353)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchLeadforCashAtPosDiy -->
  <testcase name="fetchDocumentStatusforCashAtPosDiy" time="0.105" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.FetchDocumentStatusCashAtPosDIY(MiddlewareServices.java:4145)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchDocumentStatusforCashAtPosDiy(FlowCashAtPosDIY.java:374)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchDocumentStatusforCashAtPosDiy -->
  <testcase name="invalidSolutionTypeInFetchPlanForEMIOfferingDIY" time="0.334" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="invalidSolutionTypeforMposDiy" time="0.353" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchLeadEdcDiy" time="2.314" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchLeadEdcDiy(FlowMapEdcDIY.java:317)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchLeadEdcDiy -->
  <testcase name="createQRforUnmapEDCWithClaimAMC" time="3.8" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
  <testcase name="UploadDocforCashAtPosDiy" time="0.109" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.UploadDocCashAtPosDIY(MiddlewareServices.java:4162)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.UploadDocforCashAtPosDiy(FlowCashAtPosDIY.java:401)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- UploadDocforCashAtPosDiy -->
  <testcase name="createAccessTokenMposDIY" time="0.218" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="invalidCustIdInCheckEligibilityforEMIOfferingDIY" time="0.385" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="UnMapEdcPayMerchant" time="0.445" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
  <testcase name="fetchStatusLeadEdcDiy" time="21.89" classname="OCL.DIY.MapEdcDIY.FlowMapEdcDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MapEdcDIY.FlowMapEdcDIY.fetchStatusLeadEdcDiy(FlowMapEdcDIY.java:329)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchStatusLeadEdcDiy -->
  <testcase name="createAccessToken" time="0.206" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="UnmapEDCMachine_ReturnWithClaimAMC" time="0.359" classname="OCL.Business.UnmapEDC.FlowReturnWithClaimAMC"/>
  <testcase name="invalidMIdInCheckEligibilityforEMIOfferingDIY" time="0.653" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="createQRforMposDiy" time="3.851" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="createQRforCashAtPosDiy" time="0.207" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.OMS.CreateQR.createQRviaOMS(CreateQR.java:21)
at com.goldengate.common.BaseMethod.createQRviaOMS(BaseMethod.java:1543)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.createQRforCashAtPosDiy(FlowCashAtPosDIY.java:424)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- createQRforCashAtPosDiy -->
  <testcase name="fetchLeadMposDiyBeforeLeadCreation" time="0.743" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="validateOrderforEMIOfferingDiy" time="0.833" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchPaymentStatusforMposDiy" time="0.849" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="createLeadMposDiy" time="0.497" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="LeadAlreadyExistMposDiy" time="0.407" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchLeadMposDiyAfterLeadCreation" time="0.659" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchPaymentStatusforCashAtPosDiy" time="0.0" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <skipped/>
  </testcase> <!-- fetchPaymentStatusforCashAtPosDiy -->
  <testcase name="createAccessToken" time="0.229" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="OrderNotifyMposDiyWithoutOrderID" time="0.351" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MposDIY.FlowMposDIY.OrderNotifyMposDiyWithoutOrderID(FlowMposDIY.java:594)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- OrderNotifyMposDiyWithoutOrderID -->
  <testcase name="OrderNotifyMposDiy" time="0.0" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <skipped/>
  </testcase> <!-- OrderNotifyMposDiy -->
  <testcase name="CallbackinprogressMposDiy" time="0.461" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.MposDIY.FlowMposDIY.CallbackinprogressMposDiy(FlowMposDIY.java:652)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- CallbackinprogressMposDiy -->
  <testcase name="PaymentCashAtPosDiy" time="0.485" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [500] but found [200]">
      <![CDATA[java.lang.AssertionError: did not expect to find [500] but found [200]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.PaymentCashAtPosDiy(FlowCashAtPosDIY.java:462)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- PaymentCashAtPosDiy -->
  <testcase name="fetchLeadMposWithInvalidChannel" time="0.363" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="io.restassured.path.json.exception.JsonPathException" message="Failed to parse the JSON document">
      <![CDATA[io.restassured.path.json.exception.JsonPathException: Failed to parse the JSON document
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:986)
at io.restassured.path.json.JsonPath$4.doParseWith(JsonPath.java:951)
at io.restassured.path.json.JsonPath$JsonParser.parseWith(JsonPath.java:1031)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:993)
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadMposWithInvalidChannel(FlowMposDIY.java:694)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: groovy.json.JsonException: Lexing failed on line: 1, column: 1, while reading '<', no possible valid JSON value or punctuation could be recognized.
at groovy.json.JsonLexer.nextToken(JsonLexer.java:87)
at groovy.json.JsonLexer$nextToken.call(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parse(ConfigurableJsonSlurper.groovy:97)
at io.restassured.internal.path.json.ConfigurableJsonSlurper$parse.callCurrent(Unknown Source)
at io.restassured.internal.path.json.ConfigurableJsonSlurper.parseText(ConfigurableJsonSlurper.groovy:83)
at io.restassured.path.json.JsonPath$4$1.method(JsonPath.java:949)
at io.restassured.path.json.JsonPath$ExceptionCatcher.invoke(JsonPath.java:984)
... 17 more
]]>
    </failure>
  </testcase> <!-- fetchLeadMposWithInvalidChannel -->
  <testcase name="fetchLeadMposDiyAfterCallback" time="0.0" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <skipped/>
  </testcase> <!-- fetchLeadMposDiyAfterCallback -->
  <testcase name="createQRforEmiofferingDiy" time="3.536" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchPaymentStatusforEmiofferingDiy" time="1.029" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchLeadMposDiy" time="6.003" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at com.goldengate.common.BaseMethod.findXMWTokenforPanel(BaseMethod.java:1728)
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadMposDiy(FlowMposDIY.java:706)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchLeadMposDiy -->
  <testcase name="fetchStatusLeadCashAtPosDiy" time="7.037" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchStatusLeadCashAtPosDiy(FlowCashAtPosDIY.java:477)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchStatusLeadCashAtPosDiy -->
  <testcase name="assignAgentForQC" time="1.157" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="CreateLeadEmiofferingDiy" time="3.862" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="fetchStatusLeadMposDiy" time="11.837" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at java.util.Hashtable.put(Hashtable.java:461)
at java.util.Properties.setProperty(Properties.java:166)
at com.goldengate.common.BaseMethod.findXMWTokenforPanel(BaseMethod.java:1728)
at OCL.DIY.MposDIY.FlowMposDIY.fetchStatusLeadMposDiy(FlowMposDIY.java:718)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchStatusLeadMposDiy -->
  <testcase name="CallbackinprogressEmiofferingDiy" time="0.734" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="AddGSTForMpos" time="1.662" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MposDIY.FlowMposDIY.AddGSTForMpos(FlowMposDIY.java:745)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- AddGSTForMpos -->
  <testcase name="FetchDocidviaPanel" time="4.125" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.FetchDocidviaPanel(FlowCashAtPosDIY.java:512)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- FetchDocidviaPanel -->
  <testcase name="QCRejectionForCashAtPos" time="0.548" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [405]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [405]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.QCRejectionForCashAtPos(FlowCashAtPosDIY.java:533)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- QCRejectionForCashAtPos -->
  <testcase name="IsGSTValidForMpos" time="1.239" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchLeadEmiofferingDiy" time="3.07" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="UploadDocAfterQCRejectionforCashAtPosDiy" time="0.097" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at Services.MechantService.MiddlewareServices.UploadDocCashAtPosDIY(MiddlewareServices.java:4162)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.UploadDocAfterQCRejectionforCashAtPosDiy(FlowCashAtPosDIY.java:563)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- UploadDocAfterQCRejectionforCashAtPosDiy -->
  <testcase name="IsNameMatchSuccessForMpos" time="1.04" classname="OCL.DIY.MposDIY.FlowMposDIY"/>
  <testcase name="fetchStatusLeadEmiofferingDiy" time="22.44" classname="OCL.DIY.EMIOfferingDIY.FlowEMIOfferingDIY"/>
  <testcase name="FetchDocidviaPanelAfterAgainDocUpload" time="4.148" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.FetchDocidviaPanelAfterAgainDocUpload(FlowCashAtPosDIY.java:578)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- FetchDocidviaPanelAfterAgainDocUpload -->
  <testcase name="fetchLeadPgProfileLead" time="7.039" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadPgProfileLead(FlowMposDIY.java:810)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchLeadPgProfileLead -->
  <testcase name="QCApprovedForCashAtPos" time="0.373" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.AssertionError" message="did not expect to find [200] but found [405]">
      <![CDATA[java.lang.AssertionError: did not expect to find [200] but found [405]
at org.testng.Assert.fail(Assert.java:97)
at org.testng.Assert.failNotEquals(Assert.java:969)
at org.testng.Assert.assertEqualsImpl(Assert.java:136)
at org.testng.Assert.assertEquals(Assert.java:118)
at org.testng.Assert.assertEquals(Assert.java:839)
at org.testng.Assert.assertEquals(Assert.java:849)
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.QCApprovedForCashAtPos(FlowCashAtPosDIY.java:599)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- QCApprovedForCashAtPos -->
  <testcase name="fetchLeadPgProfileLeadAfterMaquette" time="12.673" classname="OCL.DIY.MposDIY.FlowMposDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.MposDIY.FlowMposDIY.fetchLeadPgProfileLeadAfterMaquette(FlowMposDIY.java:822)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchLeadPgProfileLeadAfterMaquette -->
  <testcase name="fetchStatusLeadCashAtPosDiyAfterQC" time="6.986" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchStatusLeadCashAtPosDiyAfterQC(FlowCashAtPosDIY.java:614)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchStatusLeadCashAtPosDiyAfterQC -->
  <testcase name="DIYCashAtPosFetchLeadDetailsForPgReferenceId" time="0.349" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="ManualPGCallbackForCashAtPos" time="2.395" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY"/>
  <testcase name="fetchStatusLeadCashAtPosDiyAfterPGCallback" time="7.105" classname="OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at OCL.DIY.CashAtPosDIY.FlowCashAtPosDIY.fetchStatusLeadCashAtPosDiyAfterPGCallback(FlowCashAtPosDIY.java:646)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- fetchStatusLeadCashAtPosDiyAfterPGCallback -->
  <testcase name="MapPosPositiveSendOtpBusiness" time="0.894" classname="OCL.Business.MapPOS.FlowMapPos"/>
  <testcase name="MapPosPositiveGetBusiness" time="0.368" classname="OCL.Business.MapPOS.FlowMapPos">
    <failure type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:83)
at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:258)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
at io.restassured.path.json.JsonPath.getJsonObject(JsonPath.java:994)
at OCL.Business.MapPOS.FlowMapPos.MapPosPositiveGetBusiness(FlowMapPos.java:96)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:131)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:658)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:792)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1103)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:140)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:91)
at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:47)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:34)
at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at Script1.run(Script1.groovy:1)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:574)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:612)
at groovy.lang.GroovyShell.evaluate(GroovyShell.java:583)
at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:190)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:71)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:128)
at io.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
at sun.reflect.GeneratedMethodAccessor145.invoke(Unknown Source)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:185)
at io.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:46)
... 15 more
]]>
    </failure>
  </testcase> <!-- MapPosPositiveGetBusiness -->
  <testcase name="MapPosPositiveGetBusinessProfile" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosPositiveGetBusinessProfile -->
  <testcase name="MapPosPositiveSendOtpCreate" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosPositiveSendOtpCreate -->
  <testcase name="MapPosPositiveValidateOtpCreate" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosPositiveValidateOtpCreate -->
  <testcase name="MapPosPositiveGetMerchant" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosPositiveGetMerchant -->
  <testcase name="MapPosPositiveSubmitLead" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosPositiveSubmitLead -->
  <testcase name="MapPosFetchPaymentStatus" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosFetchPaymentStatus -->
  <testcase name="MapPosExtractQrCodeId" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosExtractQrCodeId -->
  <testcase name="MapPosFetchQrDetails" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosFetchQrDetails -->
  <testcase name="MapPosPayMerchant" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosPayMerchant -->
  <testcase name="MapPosFetchBrandAssociation" time="0.0" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosFetchBrandAssociation -->
  <testcase name="MapPosFetchStoreCategory" time="0.001" classname="OCL.Business.MapPOS.FlowMapPos">
    <skipped/>
  </testcase> <!-- MapPosFetchStoreCategory -->
  <testcase name="TC18_CheckCreatedMerchantWithoutToken" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC18_CheckCreatedMerchantWithoutToken -->
  <testcase name="TC28_EditMerchantonPGWithoutCreatedBY" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC28_EditMerchantonPGWithoutCreatedBY -->
  <testcase name="TC22_EditMerchantonPGWithourSourceID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC22_EditMerchantonPGWithourSourceID -->
  <testcase name="TC13_CreateMerchantonPGWithoutRequestID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC13_CreateMerchantonPGWithoutRequestID -->
  <testcase name="TC04_CompanyOnboardWithoutPan" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC04_CompanyOnboardWithoutPan -->
  <testcase name="TC25_EditMerchantonPGWithoutCustID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC25_EditMerchantonPGWithoutCustID -->
  <testcase name="TC09_CompanyOnboardWithoutCompanyFlag" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC09_CompanyOnboardWithoutCompanyFlag -->
  <testcase name="TC17_CreateMerchantonPGWithoutKYB" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC17_CreateMerchantonPGWithoutKYB -->
  <testcase name="TC27_EditMerchantonPGWithoutBusinessName" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC27_EditMerchantonPGWithoutBusinessName -->
  <testcase name="TC07_CreateProductWithoutheaders" time="0.0" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <ignored/>
  </testcase> <!-- TC07_CreateProductWithoutheaders -->
  <testcase name="TC11_CreateProductWithOutProductIdWithdeviceType1" time="0.0" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <ignored/>
  </testcase> <!-- TC11_CreateProductWithOutProductIdWithdeviceType1 -->
  <testcase name="TC10_CreateProductWithOutdeviceType1" time="0.0" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <ignored/>
  </testcase> <!-- TC10_CreateProductWithOutdeviceType1 -->
  <testcase name="TC07_CompanyOnboardWithInvalidPan" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC07_CompanyOnboardWithInvalidPan -->
  <testcase name="TC26_EditMerchantonPGWithoutMID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC26_EditMerchantonPGWithoutMID -->
  <testcase name="TC15_CreateMerchantonPGWithoutRequestIDSourceID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC15_CreateMerchantonPGWithoutRequestIDSourceID -->
  <testcase name="TC14_CreateMerchantonPGWithoutRequestID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC14_CreateMerchantonPGWithoutRequestID -->
  <testcase name="TC23_EditMerchantonPGWithoutRequestID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC23_EditMerchantonPGWithoutRequestID -->
  <testcase name="TC05_CompanyOnboardWithoutDoc" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC05_CompanyOnboardWithoutDoc -->
  <testcase name="TC08_CompanyOnboardWithoutCompanyType" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC08_CompanyOnboardWithoutCompanyType -->
  <testcase name="TC06_CompanyOnboardWithoutRole" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC06_CompanyOnboardWithoutRole -->
  <testcase name="TC11_CompanyOnboardWithoutPANName" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC11_CompanyOnboardWithoutPANName -->
  <testcase name="TC10_CompanyOnboardWithoutEntityType" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC10_CompanyOnboardWithoutEntityType -->
  <testcase name="TC20_EditMerchantonPGWithoutHeaders" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC20_EditMerchantonPGWithoutHeaders -->
  <testcase name="TC03_CompanyOnboardWithoutCustId" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC03_CompanyOnboardWithoutCustId -->
  <testcase name="TC19_CheckCreatedMerchantonPG" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC19_CheckCreatedMerchantonPG -->
  <testcase name="TC24_EditMerchantonPGWithoutRequestSourceID" time="0.0" classname="OCL.BrandEMI.BrandEMITest">
    <ignored/>
  </testcase> <!-- TC24_EditMerchantonPGWithoutRequestSourceID -->
  <testcase name="TC12_CreateProductWithOutProductIdWithdeviceType2" time="0.0" classname="OCL.RestApiForEdcPlans.RestApiTest">
    <ignored/>
  </testcase> <!-- TC12_CreateProductWithOutProductIdWithdeviceType2 -->
</testsuite> <!-- Devices -->
