<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite thread-count="10" guice-stage="DEVELOPMENT" verbose="30" name="Failed suite [Onboarding Engine]" parallel="tests">
  <listeners>
    <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
    <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
    <listener class-name="com.paytm.framework.reporting.listeners.RPTestListener"/>
  </listeners>
  <test thread-count="10" verbose="30" name="Lending(failed)" parallel="tests">
    <classes>
      <class name="OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow">
        <methods>
          <include name="TC018_VerifyLeadStage"/>
          <include name="TC031_FetchLeadAllData"/>
          <include name="TC017_CKYCCallback"/>
          <include name="TC026_EmandateCallback"/>
          <include name="TC004_FetchLeadAllData"/>
          <include name="TC012_UploadSelfie"/>
          <include name="TC005_UpdateLeadBasicDetails"/>
          <include name="TC029_VerifyLISCallback"/>
          <include name="TC009_FetchLeadAllData"/>
          <include name="TC030_LMSDataCallback"/>
          <include name="TC006_FetchLeadAllData"/>
          <include name="TC007_UpdateBureauDataSetInSAI"/>
          <include name="TC024_UpdateSAI"/>
          <include name="TC013_UploadCustomerPhoto"/>
          <include name="TC008_FetchCIR"/>
          <include name="TC002_DeleteExistingLead"/>
          <include name="TC027_VerifyPDCCallback"/>
          <include name="TC025_AcceptLoanAgreement"/>
          <include name="TC023_GenerateSanctionLetter"/>
          <include name="intitializeInputData"/>
          <include name="TC001_FetchLeadDeatils"/>
          <include name="TC003_CreateBTDistributionPiramalLead"/>
          <include name="TC010_UpdateExistingDetailsInSAI"/>
          <include name="TC015_VerifyUploadedDocument"/>
          <include name="TC021_SaveBankDetails"/>
          <include name="TC022_GenerateLoanAgreement"/>
          <include name="TC011_FetchLeadAllData"/>
          <include name="TC020_UpdateKYCNameInSAI"/>
          <include name="TC016_UpdateSAIWithLenderDetails"/>
          <include name="TC028_UploadSheetONPanel"/>
        </methods>
      </class> <!-- OCL.Lending.BusinessLending.TestMCAFullertonV3Workflow -->
      <class name="OCL.Lending.ConsumerLending.TestPLABFLOneClick">
        <methods>
          <include name="TC041_PLv3ABFL_FetchLeadPostPDCCallback"/>
          <include name="TC038_PLv3ABFL_SubmitApplication"/>
          <include name="TC022_PLv3ABFL_FetchDataPostKYCIntiated"/>
          <include name="TC003_PLv3ABFL_CreateLead"/>
          <include name="TC027_PLv3ABFL_FetchLeadVerifyAdditionalData"/>
          <include name="TC005_PLV3ABFL_UpdateLeadOccupationDetails"/>
          <include name="TC026_PLv3ABFL_AdditionalIsRequiredorNot"/>
          <include name="TC011_PLv3ABFL_FetchDataPostBRE1Success"/>
          <include name="TC037_PLv3ABFL_GenerateSanctionLetter"/>
          <include name="TC004_PLV3ABFL_FetchLeadAllData"/>
          <include name="TC020_PLv3HERO_UploadSelfie"/>
          <include name="TC012_PLv3ABFL_LoanOfferAccept"/>
          <include name="TC017_PLv3ABFL_FetchDataPostKYCIntiated"/>
          <include name="TC023_PLv3ABFL_FetchDataPostSelfieUploaded"/>
          <include name="TC006_PLv3ABFL_FetchDataPostOccupationDetailUpdate"/>
          <include name="TC010_PLv3ABFL_BRE1Callback"/>
          <include name="TC009_PLv3ABFL_FetchCIR"/>
          <include name="TC043_FetchLeadAllData"/>
          <include name="TC029_PLv3ABFL_BRE3Success"/>
          <include name="TC033_PLv3ABFL_FetchLeadPostBankVerification"/>
          <include name="TC035_PLv3ABFL_FetchLeadPostEmandate"/>
          <include name="TC021_PLv3HERO_VerifyUploadedSelfie"/>
          <include name="TC002_PLv3ABFL_DeleteExistingLead"/>
          <include name="intitializeInputData"/>
          <include name="TC008_PLv3ABFL_FetchDataPostSAIlUpdate"/>
          <include name="TC015_PLV3ABFL_InitiateKYC_UsingSearchByPan"/>
          <include name="TC032_PLv3ABFL_SaveBankDetails"/>
          <include name="TC013_PLv3ABFL_FetchDataPostLoanOfferAccept"/>
          <include name="TC024_PLv3ABFL_SecondBRECallback"/>
          <include name="TC034_PLv3ABFL_EmandateCallback"/>
          <include name="TC030_PLv3ABFL_UpdateKYCNameInSAI"/>
          <include name="TC031_PLv3ABFL_FetchLeadUpdateCKYCinSAI"/>
          <include name="TC028_PLv3ABFL_AdditionalDataCapture"/>
          <include name="TC042PLV3HERO_SubmitApplicationLMSApprovedCallback"/>
          <include name="TC019_PLv3HERO_VerifyUploadedCustomerPhoto"/>
          <include name="TC036_PLv3ABFL_GenerateLoanAgreement"/>
          <include name="TC016_PLv3ABFL_FetchDataPostLoanOfferAccept"/>
          <include name="TC018_PLv3HERO_UploadCustomerPhoto"/>
          <include name="TC014_PLV3ABFL_UpdateLeadDetailsinSAI"/>
          <include name="TC039_PLv3ABFL_FetchLeadPostSubmitApplication"/>
          <include name="TC001_PLv3ABFL_fetchlLead"/>
          <include name="TC040_PLv3ABFL_PDCCallback"/>
          <include name="TC007_PLV3ABFL_UpdateLeadDetailsinSAI"/>
        </methods>
      </class> <!-- OCL.Lending.ConsumerLending.TestPLABFLOneClick -->
      <class name="OCL.Lending.ConsumerLending.PersonalLoanRenewal">
        <methods>
          <include name="TC026_PLv3HERO_GenerateSanctionLetter"/>
          <include name="TC004_FetchLeadAllData"/>
          <include name="TC028_PLv3HERO_FetchLeadPostSubmitApplication"/>
          <include name="TC010_FetchLeadAllData"/>
          <include name="TC019_PLv3HERO_UpdateKYCNameInSAI"/>
          <include name="TC015_CKYCCallback"/>
          <include name="TC029_PLv3HERO_PDCCallback"/>
          <include name="TC017_PLv3HERO_AdditionalIsRequiredorNot"/>
          <include name="TC012_PLv3HERO_VerifyUploadedCustomerPhoto"/>
          <include name="TC009_PLRenewal_LoanOfferAccept"/>
          <include name="TC024_PLv3HERO_FetchLeadPostEmandate"/>
          <include name="intitializeInputData"/>
          <include name="TC025_PLv3HERO_GenerateLoanAgreement"/>
          <include name="TC011_PLv3HERO_UploadCustomerPhoto"/>
          <include name="TC003_CreatePLRenewalLead"/>
          <include name="TC007_FetchLeadAllData"/>
          <include name="TC030_PLv3HERO_FetchLeadPostPDCCallback"/>
          <include name="TC002_DeleteExistingLead"/>
          <include name="TC014_PLv3HERO_VerifyUploadedSelfie"/>
          <include name="TC016_VerifyLeadStage"/>
          <include name="TC018_PLv3HERO_FetchLeadVerifyAdditionalData"/>
          <include name="TC001_FetchLeadDeatils"/>
          <include name="TC008_UpdateExistingDetailsInSAI"/>
          <include name="TC031PLv3HERO_SubmitApplicationLMSApprovedCallback"/>
          <include name="TC006_FetchCIR"/>
          <include name="TC023_PLv3HERO_EmandateCallback"/>
          <include name="TC013_PLv3HERO_UploadSelfie"/>
          <include name="TC005_UpdateBureauDataSetInSAI"/>
          <include name="TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI"/>
          <include name="TC022_PLv3HERO_UpdateKYCNameInSAI"/>
          <include name="TC027_PLv3HERO_SubmitApplication"/>
          <include name="TC021_PLv3HERO_SaveBankDetails"/>
          <include name="TC032_FetchLeadAllData"/>
        </methods>
      </class> <!-- OCL.Lending.ConsumerLending.PersonalLoanRenewal -->
    </classes>
  </test> <!-- Lending(failed) -->
</suite> <!-- Failed suite [Onboarding Engine] -->
