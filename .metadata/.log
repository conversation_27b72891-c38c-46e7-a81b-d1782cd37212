!SESSION 2022-10-07 18:41:09.867 -----------------------------------------------
eclipse.buildId=4.22.0.*********-1800
java.version=17.0.1
java.vendor=Eclipse Adoptium
BootLoader constants: OS=macosx, ARCH=aarch64, WS=cocoa, NL=en_IN
Framework arguments:  -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring
Command-line arguments:  -os macosx -ws cocoa -arch aarch64 -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring

!ENTRY org.eclipse.jface 2 0 2022-10-07 18:41:34.854
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-07 18:41:34.854
!MESSAGE A conflict occurred for COMMAND+SHIFT+T:
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.navigate.open.type,Open Type,
		Open a type in a Java editor,
		Category(org.eclipse.ui.category.navigate,Navigate,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@74f649a7,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.lsp4e.symbolinworkspace,Go to Symbol in Workspace,
		,
		Category(org.eclipse.lsp4e.category,Language Servers,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@308c65e0,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-07 18:41:34.854
!MESSAGE A conflict occurred for ALT+COMMAND+R:
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.ui.edit.rename,Rename,
		Rename the selected item,
		Category(org.eclipse.ui.category.file,File,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@50fa5938,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.edit.text.java.rename.element,Rename - Refactoring ,
		Rename the selected element,
		Category(org.eclipse.jdt.ui.category.refactoring,Refactor - Java,Java Refactoring Actions,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@1e482ecd,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
!SESSION 2022-10-17 14:42:53.814 -----------------------------------------------
eclipse.buildId=4.22.0.*********-1800
java.version=17.0.1
java.vendor=Eclipse Adoptium
BootLoader constants: OS=macosx, ARCH=x86_64, WS=cocoa, NL=en_IN
Framework arguments:  -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring
Command-line arguments:  -os macosx -ws cocoa -arch x86_64 -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring

!ENTRY org.eclipse.jface 2 0 2022-10-17 14:43:17.004
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:43:17.004
!MESSAGE A conflict occurred for COMMAND+SHIFT+T:
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.navigate.open.type,Open Type,
		Open a type in a Java editor,
		Category(org.eclipse.ui.category.navigate,Navigate,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@181098bf,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.lsp4e.symbolinworkspace,Go to Symbol in Workspace,
		,
		Category(org.eclipse.lsp4e.category,Language Servers,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@632b5c79,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:43:17.004
!MESSAGE A conflict occurred for ALT+COMMAND+R:
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.ui.edit.rename,Rename,
		Rename the selected item,
		Category(org.eclipse.ui.category.file,File,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@6a552721,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.edit.text.java.rename.element,Rename - Refactoring ,
		Rename the selected element,
		Category(org.eclipse.jdt.ui.category.refactoring,Refactor - Java,Java Refactoring Actions,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@3815a7d1,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)

!ENTRY org.eclipse.e4.ui.workbench 2 0 2022-10-17 14:43:21.582
!MESSAGE Removing PartDescriptorImpl with the "org.testng.eclipse.ResultView" id and the "TestNG" label.It points to the non available "bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" class. Bundle might have been uninstalled
!SESSION 2022-10-17 14:44:43.147 -----------------------------------------------
eclipse.buildId=4.22.0.*********-1800
java.version=17.0.1
java.vendor=Eclipse Adoptium
BootLoader constants: OS=macosx, ARCH=x86_64, WS=cocoa, NL=en_IN
Framework arguments:  -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring
Command-line arguments:  -os macosx -ws cocoa -arch x86_64 -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring

!ENTRY org.eclipse.jface 2 0 2022-10-17 14:44:53.721
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:44:53.721
!MESSAGE A conflict occurred for COMMAND+SHIFT+T:
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.navigate.open.type,Open Type,
		Open a type in a Java editor,
		Category(org.eclipse.ui.category.navigate,Navigate,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@1d9ff1e2,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.lsp4e.symbolinworkspace,Go to Symbol in Workspace,
		,
		Category(org.eclipse.lsp4e.category,Language Servers,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@722b3ba2,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:44:53.721
!MESSAGE A conflict occurred for ALT+COMMAND+R:
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.ui.edit.rename,Rename,
		Rename the selected item,
		Category(org.eclipse.ui.category.file,File,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@6487f7f8,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.edit.text.java.rename.element,Rename - Refactoring ,
		Rename the selected element,
		Category(org.eclipse.jdt.ui.category.refactoring,Refactor - Java,Java Refactoring Actions,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@2b62475a,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
!SESSION 2022-10-17 14:45:01.851 -----------------------------------------------
eclipse.buildId=4.22.0.*********-1800
java.version=17.0.1
java.vendor=Eclipse Adoptium
BootLoader constants: OS=macosx, ARCH=x86_64, WS=cocoa, NL=en_IN
Framework arguments:  -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring
Command-line arguments:  -os macosx -ws cocoa -arch x86_64 -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring

!ENTRY org.eclipse.jface 2 0 2022-10-17 14:45:18.230
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:45:18.230
!MESSAGE A conflict occurred for COMMAND+SHIFT+T:
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.navigate.open.type,Open Type,
		Open a type in a Java editor,
		Category(org.eclipse.ui.category.navigate,Navigate,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@41404aa2,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.lsp4e.symbolinworkspace,Go to Symbol in Workspace,
		,
		Category(org.eclipse.lsp4e.category,Language Servers,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@b267745,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:45:18.230
!MESSAGE A conflict occurred for ALT+COMMAND+R:
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.ui.edit.rename,Rename,
		Rename the selected item,
		Category(org.eclipse.ui.category.file,File,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@31e22365,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.edit.text.java.rename.element,Rename - Refactoring ,
		Rename the selected element,
		Category(org.eclipse.jdt.ui.category.refactoring,Refactor - Java,Java Refactoring Actions,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@2ebf524,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:39.430
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:41.822
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.11.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:43.786
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.11.0.r201704240320, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:44.904
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.12.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:46.719
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.12.0.r201709030044, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:47.784
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.13.1, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:49.753
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.13.1.r201712040202, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:50.814
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.13.1.r201712040515, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:51.853
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.14.2, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:53.701
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.14.2.r201802161450, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:54.748
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.14.3, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:56.600
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.14.3.r201802240500, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:57.631
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.0.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:46:59.505
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.0.0.r201908191551, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:00.511
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.1.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:02.481
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.1.0.r202001120626, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:04.832
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.2.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:06.484
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.2.0.r202003151902, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:07.517
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.3.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:09.378
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.3.0.r202008060316, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:11.536
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.4.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:13.174
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.4.0.r202105021533, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:15.566
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.5.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:17.087
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.5.0.r202201070709, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:18.985
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.6.1, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:20.170
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.6.1/content.xml.xz, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:20.565
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.6.1, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:47:23.383
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.6.1.r202207070546, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:48:21.139
!MESSAGE Using unsafe http transport to retrieve http://dl.bintray.com/testng-team/testng-p2-release/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:48:29.988
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:48:35.163
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/content.xml.xz, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:48:37.695
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:49:08.984
!MESSAGE Using unsafe http transport to retrieve http://dl.bintray.com/testng-team/testng-p2-release/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:49:18.380
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:49:23.461
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/content.xml.xz, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:49:25.981
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:44.038
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:46.111
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.11.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:47.960
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.11.0.r201704240320, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:49.177
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.12.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:50.998
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.12.0.r201709030044, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:52.282
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.13.1, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:54.222
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.13.1.r201712040202, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:55.252
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.13.1.r201712040515, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:56.307
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.14.2, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:58.241
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.14.2.r201802161450, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:50:59.275
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/6.14.3, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:01.028
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/6.14.3.r201802240500, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:02.162
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.0.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:04.041
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.0.0.r201908191551, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:05.104
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.1.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:07.518
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.1.0.r202001120626, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:08.683
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.2.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:17.240
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.2.0.r202003151902, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:19.587
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.3.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:22.149
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.3.0.r202008060316, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:23.329
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.4.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:25.316
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.4.0.r202105021533, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:26.391
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.5.0, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:28.233
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.5.0.r202201070709, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:29.297
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.6.1, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:30.343
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.6.1/artifacts.xml.xz, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:31.190
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/7.6.1, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:32.903
!MESSAGE Using unsafe http transport to retrieve http://testng.org/testng-p2-update-site/updatesites/7.6.1.r202207070546, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:40.492
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:45.904
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/artifacts.xml.xz, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:51:49.038
!MESSAGE Using unsafe http transport to retrieve http://download.eclipse.org/technology/m2e/releases/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:52:44.340
!MESSAGE Using unsafe http transport to retrieve http://dl.bintray.com/testng-team/testng-p2-release/, see CVE-2021-41033. Consider using https instead.

!ENTRY org.eclipse.equinox.p2.artifact.repository 2 0 2022-10-17 14:54:01.765
!MESSAGE No digest algorithm is available to verify download of osgi.bundle,com.google.guava,21.0.0.v20170206-1425.

!ENTRY org.eclipse.equinox.p2.artifact.repository 2 0 2022-10-17 14:54:02.106
!MESSAGE No digest algorithm is available to verify download of osgi.bundle,org.apache-extras.beanshell.bsh,2.0.0.b6.

!ENTRY org.eclipse.equinox.p2.artifact.repository 2 0 2022-10-17 14:54:02.140
!MESSAGE No digest algorithm is available to verify download of osgi.bundle,org.apache-extras.beanshell.bsh.source,2.0.0.b6.

!ENTRY org.eclipse.equinox.p2.artifact.repository 2 0 2022-10-17 14:54:02.153
!MESSAGE No digest algorithm is available to verify download of osgi.bundle,org.testng.eclipse,7.4.0.202106051955.

!ENTRY org.eclipse.equinox.p2.artifact.repository 2 0 2022-10-17 14:54:02.184
!MESSAGE No digest algorithm is available to verify download of org.eclipse.update.feature,org.testng.eclipse,7.4.0.202106051955.

!ENTRY org.eclipse.equinox.p2.artifact.repository 2 0 2022-10-17 14:54:02.249
!MESSAGE No digest algorithm is available to verify download of osgi.bundle,org.yaml.snakeyaml,1.21.0.

!ENTRY org.eclipse.equinox.p2.artifact.repository 2 0 2022-10-17 14:54:02.270
!MESSAGE No digest algorithm is available to verify download of osgi.bundle,org.yaml.snakeyaml.source,1.21.0.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:02.303
!MESSAGE The digest algorithms (md5) used to verify org.eclipse.update.feature,org.testng.p2.feature,7.6.1.r202207070546 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:02.306
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,org.testng,7.6.1.r202207070546 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:02.306
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,com.beust.jcommander,1.82.0 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:02.307
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,com.beust.jcommander.source,1.82.0 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:03.330
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,org.testng.source,7.6.1.r202207070546 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:04.259
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,org.webjars.jquery,3.6.0 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:04.480
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,org.webjars.jquery.source,3.6.0 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:04.848
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,slf4j.api,1.7.36 have severely compromised security. Please report this concern to the artifact provider.

!ENTRY org.eclipse.equinox.p2.repository 2 0 2022-10-17 14:54:05.272
!MESSAGE The digest algorithms (md5) used to verify osgi.bundle,slf4j.api.source,1.7.36 have severely compromised security. Please report this concern to the artifact provider.
!SESSION 2022-10-17 14:55:37.201 -----------------------------------------------
eclipse.buildId=4.22.0.*********-1800
java.version=17.0.1
java.vendor=Eclipse Adoptium
BootLoader constants: OS=macosx, ARCH=x86_64, WS=cocoa, NL=en_IN
Framework arguments:  -product org.eclipse.epp.package.java.product -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring
Command-line arguments:  -os macosx -ws cocoa -arch x86_64 -product org.eclipse.epp.package.java.product -data file:/Users/<USER>/oe-api-automation/ -product org.eclipse.epp.package.java.product -keyring /Users/<USER>/.eclipse_keyring

!ENTRY org.testng.eclipse 4 0 2022-10-17 14:55:45.700
!MESSAGE FrameworkEvent ERROR
!STACK 0
org.osgi.framework.BundleException: Could not resolve module: org.testng.eclipse [421]
  Unresolved requirement: Require-Bundle: org.testng; bundle-version="[6.9.12,8.0.0)"
    -> Bundle-SymbolicName: org.testng; bundle-version="7.6.1.r202207070546"
       org.testng [420]
         Unresolved requirement: Import-Package: com.google.inject; version="[1.2.0,5.0.0)"; resolution:="optional"
         Unresolved requirement: Require-Capability: osgi.ee; filter:="(osgi.ee=UNKNOWN)"

	at org.eclipse.osgi.container.Module.start(Module.java:463)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.lambda$1(ModuleContainer.java:1834)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1829)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ModuleContainer.java:1775)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(ModuleContainer.java:1739)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1661)
	at org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(ModuleContainer.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:228)
	at org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run(EventManager.java:339)

!ENTRY org.eclipse.jface 2 0 2022-10-17 14:55:54.596
!MESSAGE Keybinding conflicts occurred.  They may interfere with normal accelerator operation.
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:55:54.596
!MESSAGE A conflict occurred for COMMAND+SHIFT+T:
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.navigate.open.type,Open Type,
		Open a type in a Java editor,
		Category(org.eclipse.ui.category.navigate,Navigate,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@13b29b34,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
Binding(COMMAND+SHIFT+T,
	ParameterizedCommand(Command(org.eclipse.lsp4e.symbolinworkspace,Go to Symbol in Workspace,
		,
		Category(org.eclipse.lsp4e.category,Language Servers,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@25589735,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,,system)
!SUBENTRY 1 org.eclipse.jface 2 0 2022-10-17 14:55:54.596
!MESSAGE A conflict occurred for ALT+COMMAND+R:
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.ui.edit.rename,Rename,
		Rename the selected item,
		Category(org.eclipse.ui.category.file,File,null,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@46994f26,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
Binding(ALT+COMMAND+R,
	ParameterizedCommand(Command(org.eclipse.jdt.ui.edit.text.java.rename.element,Rename - Refactoring ,
		Rename the selected element,
		Category(org.eclipse.jdt.ui.category.refactoring,Refactor - Java,Java Refactoring Actions,true),
		org.eclipse.ui.internal.WorkbenchHandlerServiceHandler@1bb172dd,
		,,true),null),
	org.eclipse.ui.defaultAcceleratorConfiguration,
	org.eclipse.ui.contexts.window,,cocoa,system)
