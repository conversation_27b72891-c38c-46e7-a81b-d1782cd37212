<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_kQ3CUUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_kQ3CUkZBEe2rRMoXHnyVUQ" bindingContexts="_kQ3CW0ZBEe2rRMoXHnyVUQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList/>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_kQ3CUkZBEe2rRMoXHnyVUQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_ka4hA0ZBEe2rRMoXHnyVUQ" label="%trimmedwindow.label.eclipseSDK" x="58" y="78" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1665148295861"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_ka4hA0ZBEe2rRMoXHnyVUQ" selectedElement="_ka4hBEZBEe2rRMoXHnyVUQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_ka4hBEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_kgD78EZBEe2rRMoXHnyVUQ">
        <children xsi:type="advanced:Perspective" xmi:id="_kgD78EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_kgD78UZBEe2rRMoXHnyVUQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.actionSet:org.eclipse.eclemma.ui.CoverageActionSet</tags>
          <tags>persp.showIn:org.eclipse.eclemma.ui.CoverageView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_kgD78UZBEe2rRMoXHnyVUQ" selectedElement="_kgD7-kZBEe2rRMoXHnyVUQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_kgD78kZBEe2rRMoXHnyVUQ" toBeRendered="false" containerData="2500">
              <children xsi:type="basic:PartStack" xmi:id="_kgD780ZBEe2rRMoXHnyVUQ" elementId="left" toBeRendered="false" containerData="6000">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD79EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_kf9OQEZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD79UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_kf91UEZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD79kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_kf91UUZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD790ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_kgDU4kZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_kgD7-EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD7-UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_kgDU4EZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_kgD7-kZBEe2rRMoXHnyVUQ" containerData="7500" selectedElement="_kgD8AkZBEe2rRMoXHnyVUQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_kgD7-0ZBEe2rRMoXHnyVUQ" containerData="7500" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD7_EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_kf6K8EZBEe2rRMoXHnyVUQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_kgD7_UZBEe2rRMoXHnyVUQ" elementId="right" toBeRendered="false" containerData="2500">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_kgD7_kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_kf-cZ0ZBEe2rRMoXHnyVUQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_kgD7_0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_kf_DcEZBEe2rRMoXHnyVUQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_kgD8AEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_kgCt0EZBEe2rRMoXHnyVUQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_kgD8AUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_kgDU40ZBEe2rRMoXHnyVUQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Ant</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_kgD8AkZBEe2rRMoXHnyVUQ" elementId="bottom" containerData="2500" selectedElement="_kgD8A0ZBEe2rRMoXHnyVUQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <tags>active</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8A0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProblemView" ref="_kf-cYEZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8BEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_kf-cYUZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8BUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_kf-cYkZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8BkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_kf-cY0ZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8B0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_kf-cZEZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8CEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_kf-cZUZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8CUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_kf-cZkZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kgD8CkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_kgDU4UZBEe2rRMoXHnyVUQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_ka5IEEZBEe2rRMoXHnyVUQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_ka5IEUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_ka4hAEZBEe2rRMoXHnyVUQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_ka5IEkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_ka4hAUZBEe2rRMoXHnyVUQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_ka5IE0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_ka4hAkZBEe2rRMoXHnyVUQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_ka4hAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_ka4hAUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view>&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xA;&lt;standbyPart/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_k48WAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_k48WAUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_ka4hAkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_kf6K8EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_kf6K8UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf9OQEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;Aggregate for window 1665148295861&quot;>&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xA;&lt;xmlDefinedFilters>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/xmlDefinedFilters>&#xA;&lt;/customFilters>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_kiBDwEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_kiBDwUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf91UEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf91UUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cYEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_klsC0EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_klsC0UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cYUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cYkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cY0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cZEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cZUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cZkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf-cZ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_klICIEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_klICIUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kf_DcEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kgCt0EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kgDU4EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kgDU4UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kgDU4kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kgDU40ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <trimBars xmi:id="_kQ3CU0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_kbtncEZBEe2rRMoXHnyVUQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_kbtncUZBEe2rRMoXHnyVUQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOgEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_s3md8E39Ee2nLfAP04S0wA" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_kRQETkZBEe2rRMoXHnyVUQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOgUZBEe2rRMoXHnyVUQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_kbuOgkZBEe2rRMoXHnyVUQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOg0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.workbench.edit" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOhEZBEe2rRMoXHnyVUQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_kbuOhUZBEe2rRMoXHnyVUQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kgzi0EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kgfZwEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kgvRYEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOhkZBEe2rRMoXHnyVUQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_kbuOh0ZBEe2rRMoXHnyVUQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOiEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_s3qIUE39Ee2nLfAP04S0wA" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_kRPdFkZBEe2rRMoXHnyVUQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOiUZBEe2rRMoXHnyVUQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_kbuOikZBEe2rRMoXHnyVUQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOi0ZBEe2rRMoXHnyVUQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_kbuOjEZBEe2rRMoXHnyVUQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kbuOjUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_kcUEYEZBEe2rRMoXHnyVUQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_kcUrcEZBEe2rRMoXHnyVUQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_kQ3CVEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_kQ3CVUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_kQ3CVkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_kQ3CV0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_kQ3CWEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_k6ywIEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_kQ3CWUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <handlers xmi:id="_kTffkkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" command="_kTffkUZBEe2rRMoXHnyVUQ"/>
  <handlers xmi:id="_kTfflEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" command="_kTffk0ZBEe2rRMoXHnyVUQ"/>
  <handlers xmi:id="_kTgGoEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" command="_kTfflUZBEe2rRMoXHnyVUQ"/>
  <handlers xmi:id="_kTgGokZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" command="_kTgGoUZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kQ3CWkZBEe2rRMoXHnyVUQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_kQ3CW0ZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRhwwEZBEe2rRMoXHnyVUQ" keySequence="CTRL+SPACE" command="_kRQEBEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwyEZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+SPACE" command="_kRQD8kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwy0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+CTRL+F" command="_kRO1zkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiX1EZBEe2rRMoXHnyVUQ" keySequence="SHIFT+F10" command="_kRPdAUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiX2UZBEe2rRMoXHnyVUQ" keySequence="ALT+PAGE_UP" command="_kRRSUEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-0EZBEe2rRMoXHnyVUQ" keySequence="ALT+PAGE_DOWN" command="_kRO2CEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-5EZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F10" command="_kROOy0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkM9EZBEe2rRMoXHnyVUQ" keySequence="CTRL+PAGE_UP" command="_kRQrCUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM9UZBEe2rRMoXHnyVUQ" keySequence="CTRL+PAGE_DOWN" command="_kRPdF0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM9kZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+F3" command="_kRQq90ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkM-EZBEe2rRMoXHnyVUQ" keySequence="COMMAND+X" command="_kRQEM0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkM-UZBEe2rRMoXHnyVUQ" keySequence="COMMAND+Z" command="_kRQEKEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkNBkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+Z" command="_kRRSL0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRk0AUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+1" command="_kRPdCkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0HEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+I" command="_kRPc3UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbEUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+L" command="_kRQrOUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbJEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+D" command="_kRQrdkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCNUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+V" command="_kRNn2UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpREZBEe2rRMoXHnyVUQ" keySequence="COMMAND+A" command="_kRQrcUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpSUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+C" command="_kRNArkZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRfUcEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_kQ3CXEZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRgikEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q B" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJoEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_kRhJoUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q C" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJokZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_kRhJo0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q D" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJpEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_kRhJpUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q O" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJpkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_kRhJp0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q P" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJqEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_kRhJqUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q Q" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRhJqkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q S" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJq0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_kRhJrEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q T" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJrUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_kRhJrkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q V" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJr0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_kRhJsEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q H" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJsUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_kRhJskZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q J" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJs0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_kRhJtEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q L" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhJtUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_kRhJtkZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+SHIFT+T" command="_kROOykZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwsEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q X" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhwsUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_kRhwskZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q Y" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhws0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_kRhwtEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Q Z" command="_kRO18EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRhwtUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_kRhwu0ZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+B" command="_kRO1-kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwv0ZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+P" command="_kRO10kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwwkZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+T" command="_kRNApEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhww0ZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+H" command="_kROOv0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwxEZBEe2rRMoXHnyVUQ" keySequence="CTRL+Q" command="_kRQrAEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwxUZBEe2rRMoXHnyVUQ" keySequence="CTRL+H" command="_kRQEA0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwxkZBEe2rRMoXHnyVUQ" keySequence="CTRL+M" command="_kRQD_UZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRhwx0ZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+P" command="_kRO15EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwzEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+SHIFT+L" command="_kRNn8UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXxUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_kROO70ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXx0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_kRQD-EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXyUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F7" command="_kRQra0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXykZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F8" command="_kRQD9UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiX0kZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F10" command="_kRNntUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiX00ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+ARROW_LEFT" command="_kROO0UZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiX1UZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+ARROW_RIGHT" command="_kRQq9kZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiX1kZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F11" command="_kRQrHUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-1kZBEe2rRMoXHnyVUQ" keySequence="SHIFT+F2" command="_kROO30ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-10ZBEe2rRMoXHnyVUQ" keySequence="SHIFT+F5" command="_kRRSCkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-2EZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F6" command="_kRO1w0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-2UZBEe2rRMoXHnyVUQ" keySequence="ALT+F7" command="_kRNn2kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-3UZBEe2rRMoXHnyVUQ" keySequence="ALT+F5" command="_kRQraEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-4UZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F7" command="_kRNAr0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-40ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F8" command="_kRPdDkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-5kZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F11" command="_kRQrZkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-50ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F12" command="_kRQEB0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjl5kZBEe2rRMoXHnyVUQ" keySequence="F2" command="_kRNn5EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl6kZBEe2rRMoXHnyVUQ" keySequence="F3" command="_kRPdAEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl7UZBEe2rRMoXHnyVUQ" keySequence="F4" command="_kRNn9UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl8UZBEe2rRMoXHnyVUQ" keySequence="F5" command="_kRQrGEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl80ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F6" command="_kRO100ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl_EZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_kRQrAEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl_UZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_kRO17UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl_kZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+F11" command="_kRRSbUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjmAEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+X Q" command="_kRPc20ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjmAUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+X T" command="_kRQrbUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjmBUZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+X M" command="_kRQrf0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkM80ZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+F7" command="_kRO2H0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkM-kZBEe2rRMoXHnyVUQ" keySequence="COMMAND+[" command="_kROO0UZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM-0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+]" command="_kRQq9kZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkNAUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Z" command="_kRNAk0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkNAkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+X G" command="_kRQrXEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkNA0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+X J" command="_kRO2DUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkNB0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+[" command="_kRQD9kZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRkNCEZBEe2rRMoXHnyVUQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_kRkNCUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+X A" command="_kRNn3UZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkNC0ZBEe2rRMoXHnyVUQ" keySequence="DEL" command="_kRO120ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0BEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+R" command="_kRRSa0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0CUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+S" command="_kROO8EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0CkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+3" command="_kRPdGEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0C0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+C" command="_kRQEC0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRk0DEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+T" command="_kRQEMkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0DUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+T" command="_kRO17kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0D0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+U" command="_kRO2D0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0EkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+V" command="_kRQrIUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0FEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+F" command="_kRQrBEZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRk0GEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+G" command="_kRO13UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0GkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+W" command="_kRQEMUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0G0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+H" command="_kRNAm0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0HkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+K" command="_kRO14UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbFEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+N" command="_kRNntkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbFUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+." command="_kRRSGUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbGEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+O" command="_kRQrb0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbGkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+B" command="_kRO14kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbHUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+R" command="_kRNn5EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRlbHkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+R" command="_kRRSNEZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRlbI0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+S" command="_kRRSZkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRlbJkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+T" command="_kRNnykZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRlbJ0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+E" command="_kRO2BkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbLUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+V" command="_kRQrUUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRlbL0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+G" command="_kRRSMEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCIUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+W" command="_kRRSE0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmCI0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+I" command="_kROOzUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmCJEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+J" command="_kRQEFUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmCJkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+L" command="_kRPdA0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmCKUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+M" command="_kRQrfkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmCLEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+N" command="_kRQELUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmCL0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+P" command="_kRQETkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCMUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+S" command="_kRQrQEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCNEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+U" command="_kRRSKEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpMEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+W" command="_kRRSP0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpMkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+I" command="_kRQD5EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmpNEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+K" command="_kRO1_0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpOEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+-" command="_kRQD9kZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_kRmpOUZBEe2rRMoXHnyVUQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_kRmpO0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+N" command="_kRRSOEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpPUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+." command="_kRNn30ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmpRUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+B" command="_kRNn50ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpS0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+3" command="_kROOzEZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmpTUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+E" command="_kRQEHUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpTkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F" command="_kRO1xkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpUUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+G" command="_kRNAo0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpU0ZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E E" command="_kROO0kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpVEZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E G" command="_kRQEP0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQQEZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E J" command="_kRNn2EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQQ0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+CTRL+D A" command="_kRQEIUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRnQREZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E S" command="_kRQrckZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQRUZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E T" command="_kROOyUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQRkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+CTRL+D T" command="_kRNnokZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRnQR0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+CTRL+D J" command="_kRPdBkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRnQSUZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E L" command="_kRNn4EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQSkZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E N" command="_kRRSIUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQS0ZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E P" command="_kRNAmkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQTkZBEe2rRMoXHnyVUQ" keySequence="ALT+CR" command="_kRQD5EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQT0ZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+E R" command="_kRPc7kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQUEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+CTRL+D Q" command="_kRRSGEZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_kRhwt0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_kRhwtkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRhwuEZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+P" command="_kRQECEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwwUZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+D" command="_kRRSJ0ZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRhwuUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_kRTuQkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRhwukZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+Q" command="_kRPc6EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRhwvEZBEe2rRMoXHnyVUQ" keySequence="CTRL+." command="_kRQrVEZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiXwEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+NUMPAD_MULTIPLY" command="_kRPc50ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXwUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+NUMPAD_ADD" command="_kRQrfEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXwkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+NUMPAD_SUBTRACT" command="_kRQq80ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXw0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+NUMPAD_DIVIDE" command="_kRO16kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXxkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_kRPc-0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXyEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_kROOvkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXzUZBEe2rRMoXHnyVUQ" keySequence="ALT+ARROW_UP" command="_kRRSSUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXzkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+ARROW_UP" command="_kRRSFEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiXz0ZBEe2rRMoXHnyVUQ" keySequence="ALT+ARROW_DOWN" command="_kRO2F0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiX0EZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+ARROW_DOWN" command="_kRRSBEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiX0UZBEe2rRMoXHnyVUQ" keySequence="ALT+ARROW_LEFT" command="_kRNAokZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiX2EZBEe2rRMoXHnyVUQ" keySequence="ALT+ARROW_RIGHT" command="_kRPc40ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-0UZBEe2rRMoXHnyVUQ" keySequence="SHIFT+END" command="_kRNAnkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-00ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+INSERT" command="_kRO2EEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-2kZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+ARROW_LEFT" command="_kRQrI0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-20ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+ARROW_RIGHT" command="_kRQrSkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-3EZBEe2rRMoXHnyVUQ" keySequence="SHIFT+HOME" command="_kRQD8EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-5UZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F10" command="_kRQEUkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-6EZBEe2rRMoXHnyVUQ" keySequence="COMMAND+END" command="_kRO2HUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-6UZBEe2rRMoXHnyVUQ" keySequence="END" command="_kRO2HUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjl4EZBEe2rRMoXHnyVUQ" keySequence="INSERT" command="_kROOu0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl50ZBEe2rRMoXHnyVUQ" keySequence="F2" command="_kRPdGUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl9kZBEe2rRMoXHnyVUQ" keySequence="COMMAND+ARROW_LEFT" command="_kRQrTkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjl90ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+ARROW_RIGHT" command="_kRQrF0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjl-EZBEe2rRMoXHnyVUQ" keySequence="COMMAND+HOME" command="_kRNn10ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl-UZBEe2rRMoXHnyVUQ" keySequence="HOME" command="_kRNn10ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM8UZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_kRQrUkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM8kZBEe2rRMoXHnyVUQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_kRO2GUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM90ZBEe2rRMoXHnyVUQ" keySequence="ALT+DEL" command="_kRQEIEZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM_EZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+DEL" command="_kRQEDEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkNAEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+Y" command="_kRMZgkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkNBEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+X" command="_kRNnqEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkNBUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+Y" command="_kRQrO0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0AkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+A" command="_kRNnyUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRk0HUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+J" command="_kRPc10ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbEEZBEe2rRMoXHnyVUQ" keySequence="COMMAND++" command="_kRO1x0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbE0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+-" command="_kRQrPkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbK0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F" command="_kRQEAkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpM0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+J" command="_kROO1UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpN0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+L" command="_kRQEHkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpQUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+O" command="_kRNnpkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpTEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+D" command="_kROO6UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpUkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+=" command="_kRO1x0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQQUZBEe2rRMoXHnyVUQ" keySequence="SHIFT+CR" command="_kRQrTUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQQkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+CR" command="_kRQq9UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQSEZBEe2rRMoXHnyVUQ" keySequence="ALT+BS" command="_kRNAoUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_kRhwvUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_kRTuUUZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRhwvkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+CTRL+/" command="_kRRSKUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRiXxEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+CTRL+\" command="_kROO8UZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjl6EZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F3" command="_kRRSFkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl-kZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+ARROW_UP" command="_kRRSR0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjl-0ZBEe2rRMoXHnyVUQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_kRQrd0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjl_0ZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+END" command="_kROO40ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjmA0ZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+PAGE_UP" command="_kRRSF0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRjmBEZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+PAGE_DOWN" command="_kRO16EZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkM8EZBEe2rRMoXHnyVUQ" keySequence="CTRL+SHIFT+HOME" command="_kRQrCkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRkNCkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+\" command="_kROO8UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkNDEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+P" command="_kRO2HkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0B0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+B" command="_kRRST0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRk0FUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+7" command="_kRNnuUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbEkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+M" command="_kRPdGkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbFkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+/" command="_kRNnuUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbH0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+C" command="_kRNnuUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbKEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+U" command="_kRPdEUZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRlbKkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F" command="_kRQrTEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCLkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+O" command="_kRQEIkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmCM0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+T" command="_kRNnxUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpMUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+I" command="_kRQq8EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpQEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+O" command="_kRQrdUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpT0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+'" command="_kRNn5kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpVUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+2 F" command="_kRQre0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQUkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+2 R" command="_kRQD50ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQU0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+2 T" command="_kRNnuEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQVEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+2 L" command="_kROO2EZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRnQVkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+2 M" command="_kRQrG0ZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRhwyUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.console" bindingContext="_kRTuRUZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRhwykZBEe2rRMoXHnyVUQ" keySequence="CTRL+D" command="_kRRSEEZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRiXy0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_kRTuQEZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRiXzEZBEe2rRMoXHnyVUQ" keySequence="ALT+ARROW_UP" command="_kRNAn0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRiX10ZBEe2rRMoXHnyVUQ" keySequence="ALT+ARROW_RIGHT" command="_kRQrX0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-0kZBEe2rRMoXHnyVUQ" keySequence="SHIFT+INSERT" command="_kRQrLEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-6kZBEe2rRMoXHnyVUQ" keySequence="COMMAND+INSERT" command="_kRPdHkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0E0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+V" command="_kRQrLEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbIUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+C" command="_kRPdHkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCNkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+V" command="_kRQrLEZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRmpSkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+C" command="_kRPdHkZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_kRi-1EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_kRTuVkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRi-1UZBEe2rRMoXHnyVUQ" keySequence="SHIFT+F2" command="_kRNn60ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl6UZBEe2rRMoXHnyVUQ" keySequence="F3" command="_kRNnpUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbHEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+R" command="_kRNn80ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRlbKUZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+F" command="_kRQrTEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCLUZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+O" command="_kRNAskZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_kRi-3kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_kRTuTUZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRi-30ZBEe2rRMoXHnyVUQ" keySequence="ALT+F5" command="_kRQrW0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_kRi-4EZBEe2rRMoXHnyVUQ" keySequence="F7" command="_kRRSIkZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRi-4kZBEe2rRMoXHnyVUQ" keySequence="F8" command="_kROOtUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl40ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+F2" command="_kRQEEEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl8EZBEe2rRMoXHnyVUQ" keySequence="F5" command="_kROOt0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRjl8kZBEe2rRMoXHnyVUQ" keySequence="F6" command="_kRQrV0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCMEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+R" command="_kRNnoEZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRjl4UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_kRTuU0ZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRjl4kZBEe2rRMoXHnyVUQ" keySequence="F1" command="_kRNno0ZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRjl5EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_kRTuWkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRjl5UZBEe2rRMoXHnyVUQ" keySequence="F2" command="_kRO13kZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRjl60ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_kRTuS0ZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRjl7EZBEe2rRMoXHnyVUQ" keySequence="F3" command="_kRQrAUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCIEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+G" command="_kRQrFkZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRjl7kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_kRTuVUZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRjl70ZBEe2rRMoXHnyVUQ" keySequence="F5" command="_kRQrRkZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRjl9EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_kRTuWUZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRjl9UZBEe2rRMoXHnyVUQ" keySequence="COMMAND+ARROW_LEFT" command="_kROO5UZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpRkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+C" command="_kRPdJUZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRkM_kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_kRTuUkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRkM_0ZBEe2rRMoXHnyVUQ" keySequence="ALT+Y" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRkNDUZBEe2rRMoXHnyVUQ" keySequence="ALT+A" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0A0ZBEe2rRMoXHnyVUQ" keySequence="ALT+B" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0CEZBEe2rRMoXHnyVUQ" keySequence="ALT+C" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0DkZBEe2rRMoXHnyVUQ" keySequence="ALT+D" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0EEZBEe2rRMoXHnyVUQ" keySequence="ALT+E" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0EUZBEe2rRMoXHnyVUQ" keySequence="ALT+F" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRk0GUZBEe2rRMoXHnyVUQ" keySequence="ALT+G" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbGUZBEe2rRMoXHnyVUQ" keySequence="ALT+P" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbG0ZBEe2rRMoXHnyVUQ" keySequence="ALT+R" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbIkZBEe2rRMoXHnyVUQ" keySequence="ALT+S" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbJUZBEe2rRMoXHnyVUQ" keySequence="ALT+T" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbLEZBEe2rRMoXHnyVUQ" keySequence="ALT+V" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbLkZBEe2rRMoXHnyVUQ" keySequence="ALT+W" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCIkZBEe2rRMoXHnyVUQ" keySequence="ALT+H" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCJUZBEe2rRMoXHnyVUQ" keySequence="ALT+L" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCKkZBEe2rRMoXHnyVUQ" keySequence="ALT+N" command="_kRNnsUZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRkNDkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_kRTuTEZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRk0AEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+1" command="_kRRSK0ZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRk0BUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_kRTuUEZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRk0BkZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+B" command="_kRRST0ZBEe2rRMoXHnyVUQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_kRk0FkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_kRTuWEZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRk0F0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+7" command="_kRNnuUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbF0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+/" command="_kRNnuUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRlbIEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+C" command="_kRNnuUZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRmCJ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_kRTuSkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRmCKEZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+M" command="_kRQrbEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCK0ZBEe2rRMoXHnyVUQ" keySequence="ALT+COMMAND+N" command="_kRQrgUZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCMkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+T" command="_kRPdIEZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmCN0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+W" command="_kROOx0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpOkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+N" command="_kRO1z0ZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRmpNUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_kRTuTkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRmpNkZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+," command="_kRQrA0ZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpPEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+SHIFT+." command="_kRQD-kZBEe2rRMoXHnyVUQ"/>
    <bindings xmi:id="_kRmpUEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+G" command="_kRQD-0ZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRmpPkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_kRTuQ0ZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRmpP0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+O" command="_kRRSEkZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRmpQkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_kRTuVEZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRmpQ0ZBEe2rRMoXHnyVUQ" keySequence="COMMAND+O" command="_kRNn0EZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRmpR0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_kRTuT0ZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRmpSEZBEe2rRMoXHnyVUQ" keySequence="COMMAND+C" command="_kRO12EZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kRnQTEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_kRTuQUZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_kRnQTUZBEe2rRMoXHnyVUQ" keySequence="ALT+CR" command="_kRNAmEZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kTgGo0ZBEe2rRMoXHnyVUQ" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" bindingContext="_kQ3CXkZBEe2rRMoXHnyVUQ">
    <bindings xmi:id="_saCYME39Ee2nLfAP04S0wA" keySequence="M1+W" command="_kTgGoUZBEe2rRMoXHnyVUQ"/>
  </bindingTables>
  <bindingTables xmi:id="_kf6yAUZBEe2rRMoXHnyVUQ" bindingContext="_kf6yAEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf6yA0ZBEe2rRMoXHnyVUQ" bindingContext="_kf6yAkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf6yBUZBEe2rRMoXHnyVUQ" bindingContext="_kf6yBEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf6yB0ZBEe2rRMoXHnyVUQ" bindingContext="_kf6yBkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf6yCUZBEe2rRMoXHnyVUQ" bindingContext="_kf6yCEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZEUZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZEEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZE0ZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZEkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZFUZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZFEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZF0ZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZFkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZGUZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZGEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZG0ZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZGkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZHUZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZHEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZH0ZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZHkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZIUZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZIEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZI0ZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZIkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf7ZJUZBEe2rRMoXHnyVUQ" bindingContext="_kf7ZJEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AIUZBEe2rRMoXHnyVUQ" bindingContext="_kf8AIEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AI0ZBEe2rRMoXHnyVUQ" bindingContext="_kf8AIkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AJUZBEe2rRMoXHnyVUQ" bindingContext="_kf8AJEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AJ0ZBEe2rRMoXHnyVUQ" bindingContext="_kf8AJkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AKUZBEe2rRMoXHnyVUQ" bindingContext="_kf8AKEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AK0ZBEe2rRMoXHnyVUQ" bindingContext="_kf8AKkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8ALUZBEe2rRMoXHnyVUQ" bindingContext="_kf8ALEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AL0ZBEe2rRMoXHnyVUQ" bindingContext="_kf8ALkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8AMUZBEe2rRMoXHnyVUQ" bindingContext="_kf8AMEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8nMUZBEe2rRMoXHnyVUQ" bindingContext="_kf8nMEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8nM0ZBEe2rRMoXHnyVUQ" bindingContext="_kf8nMkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8nNUZBEe2rRMoXHnyVUQ" bindingContext="_kf8nNEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8nN0ZBEe2rRMoXHnyVUQ" bindingContext="_kf8nNkZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8nOUZBEe2rRMoXHnyVUQ" bindingContext="_kf8nOEZBEe2rRMoXHnyVUQ"/>
  <bindingTables xmi:id="_kf8nO0ZBEe2rRMoXHnyVUQ" bindingContext="_kf8nOkZBEe2rRMoXHnyVUQ"/>
  <rootContext xmi:id="_kQ3CW0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_kQ3CXEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_kQ3CXUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_kRTuQEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_kRTuQUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_kRTuQkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_kRTuQ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_kRTuS0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_kRTuTEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_kRTuUUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_kRTuU0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_kRTuVEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_kRTuVkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_kRTuWEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_kRTuRUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_kRTuRkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_kRTuR0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_kRTuSUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_kRTuSkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_kRTuTUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_kRTuTkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_kRTuV0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_kRTuT0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_kRTuUkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_kRTuVUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_kRTuWUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_kRTuWkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_kQ3CXkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_kRTuREZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_kRTuSEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_kRTuUEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_kRhwtkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_kf6yAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_kf6yAkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_kf6yBEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_kf6yBkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_kf6yCEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_kf7ZEEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.CoverageActionSet" name="Auto::org.eclipse.eclemma.ui.CoverageActionSet"/>
  <rootContext xmi:id="_kf7ZEkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_kf7ZFEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_kf7ZFkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_kf7ZGEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_kf7ZGkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_kf7ZHEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_kf7ZHkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_kf7ZIEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_kf7ZIkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_kf7ZJEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_kf8AIEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_kf8AIkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_kf8AJEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_kf8AJkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_kf8AKEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_kf8AKkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_kf8ALEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_kf8ALkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_kf8AMEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_kf8nMEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_kf8nMkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_kf8nNEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_kf8nNkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_kf8nOEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_kf8nOkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_kVBwoEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_kap3gEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_karFoEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_karFoUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_karssEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_karssUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kasTwEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kas60EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kas60UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kas60kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kath4EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kath4UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kath4kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.CoverageView" label="Coverage" iconURI="platform:/plugin/org.eclipse.eclemma.ui/icons/full/eview16/coverage.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.eclemma.internal.ui.coverageview.CoverageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.eclemma.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_kauI8EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_kauI8UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_kauI8kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_kauwAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_kauwAUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_kauwAkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_kauwA0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_kawlMEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_kaxMQEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_kaxMQUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_kaxMQkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_kaxzUEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_kaxzUUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_kaxzUkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_kaxzU0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_kayaYEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_kayaYUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_kayaYkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_kayaY0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_kayaZEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_kazBcEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_kazBcUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_kazogEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_kazogUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_ka0PkEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_ka0PkUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_ka0PkkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_ka0Pk0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka0PlEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka02oEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_ka02oUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka02okZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka02o0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka1dsEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka1dsUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka1dskZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka1ds0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka1dtEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka2EwEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka2EwUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka2EwkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_ka2r0EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_kRMZgEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRMZgUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRMZgkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAkEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRNAkUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_kRNAkkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_kRLyh0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAk0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAlEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAlUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAlkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAl0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAmEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAmUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAmkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.junitPluginShortcut.coverage" commandName="Coverage JUnit Plug-in Test" description="Coverage JUnit Plug-in Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAm0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAnEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAnUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_kRLydEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAnkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAn0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_kRLyg0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAoEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAoUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAokZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAo0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNApEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_kRLyfEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNApUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_kRLyf0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRNApkZBEe2rRMoXHnyVUQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_kRNAp0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAqEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAqUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAqkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAq0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNArEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRNArUZBEe2rRMoXHnyVUQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_kRNArkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAr0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAsEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAsUZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.createClassAction" commandName="Create TestNG class" description="Create a TestNG class to test this class" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAskZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAs0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNAtEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnoEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnoUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnokZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNno0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnpEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_kRLyiUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnpUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnpkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="_kRLyeUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnp0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnqEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnqUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnqkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnq0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnrEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnrUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnrkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnr0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.resetOnDump" commandName="Reset on Dump" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnsEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnsUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_kRLyg0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnskZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.shortcut.rerunLast" commandName="Rerun TestNG Test" description="Rerun the last TestNG Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNns0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNntEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNntUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNntkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnt0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnuEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnuUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnukZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnu0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnvEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnvUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnvkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnv0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnwEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnwUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnwkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnw0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnxEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnxUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnxkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnx0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.hideUnusedElements" commandName="Hide Unused Elements" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnyEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnyUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnykZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNny0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_kRLyeEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRNnzEZBEe2rRMoXHnyVUQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_kRNnzUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnzkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNnz0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn0EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn0UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn0kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn00ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn1EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn1UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn1kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn10ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn2EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.localJavaShortcut.coverage" commandName="Coverage Java Application" description="Coverage Java Application" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn2UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn2kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn20ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn3EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn3UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn3kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.exportSession" commandName="Export Session..." category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn30ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn4EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.scalaShortcut.coverage" commandName="Coverage Scala Application" description="Coverage Scala Application" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn4UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn4kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_kRLyiEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRNn40ZBEe2rRMoXHnyVUQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_kRNn5EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn5UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn5kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn50ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn6EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn6UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.dumpExecutionData" commandName="Dump Execution Data" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn6kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn60ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn7EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn7UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn7kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn70ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn8EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn8UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_kRLyfkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn8kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn80ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn9EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRNn9UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOsEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOsUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOskZBEe2rRMoXHnyVUQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOs0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOtEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_kRLyikZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOtUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOtkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOt0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOuEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOuUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOukZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOu0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOvEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOvUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOvkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOv0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOwEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOwUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint  " category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOwkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kROOw0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_kROOxEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOxUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kROOxkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_kROOx0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOyEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOyUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.junitShortcut.coverage" commandName="Coverage JUnit Test" description="Coverage JUnit Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOykZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_kRLyfEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOy0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOzEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOzUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOzkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROOz0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO0EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO0UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO0kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.workbenchShortcut.coverage" commandName="Coverage Eclipse Application" description="Coverage Eclipse Application" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO00ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO1EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO1UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO1kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO10ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO2EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO2UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO2kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.commands.OpenCoverageConfiguration" commandName="Coverage Configurations..." description="Coverage Configurations..." category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO20ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO3EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO3UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO3kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO30ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO4EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO4UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO4kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO40ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO5EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO5UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO5kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO50ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO6EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO6UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO6kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_kRLyiUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO60ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO7EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_kRLyckZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kROO7UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_kROO7kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO70ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO8EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO8UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO8kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO80ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO9EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO9UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO9kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO90ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.openSessionExecutionData" commandName="Open Execution Data" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO-EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_kRLyeEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kROO-UZBEe2rRMoXHnyVUQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_kROO-kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO-0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO_EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kROO_UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1wEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1wUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1wkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1w0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1xEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1xUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1xkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1x0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1yEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1yUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1ykZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRO1y0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_kRO1zEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1zUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1zkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1z0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO10EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO10UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_kRLycEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO10kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO100ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO11EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO11UZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.shortcut.debug" commandName="Debug TestNG Test" description="Debug TestNG Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO11kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO110ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO12EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO12UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO12kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO120ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO13EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO13UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO13kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO130ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO14EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO14UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO14kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO140ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO15EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO15UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO15kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO150ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO16EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO16UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO16kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO160ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO17EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO17UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO17kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="_kRLyeUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO170ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO18EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_kRLycUZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRO18UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_kRO18kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_kRO180ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_kRO19EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO19UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO19kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_kRLyjUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO190ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_kRLyeEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRO1-EZBEe2rRMoXHnyVUQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_kRO1-UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1-kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1-0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.selectRootElements" commandName="Select Root Elements" category="_kRLyekZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRO1_EZBEe2rRMoXHnyVUQ" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_kRO1_UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1_kZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.shortcut.testngSuite.run" commandName="Run TestNG Suite" description="Run TestNG Suite" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO1_0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2AEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2AUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2AkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_kRLye0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRO2A0ZBEe2rRMoXHnyVUQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_kRO2BEZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.shortcut.run" commandName="Run TestNG Test" description="Run TestNG Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2BUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2BkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2B0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2CEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2CUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2CkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2C0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2DEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.removeAllSessions" commandName="Remove All Sessions" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2DUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2DkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2D0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2EEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2EUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_kRLygUZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRO2EkZBEe2rRMoXHnyVUQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_kRO2E0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2FEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2FUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2FkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2F0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2GEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2GUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2GkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2G0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_kRLyiUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2HEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2HUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2HkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRO2H0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc0EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.relaunchSession" commandName="Relaunch Coverage Session" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc0UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc0kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc00ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc1EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc1UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc1kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc10ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc2EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRPc2UZBEe2rRMoXHnyVUQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_kRPc2kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc20ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc3EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc3UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc3kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc30ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc4EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc4UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc4kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc40ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc5EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc5UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.donate" commandName="Donate" description="Donate to the development of the Eclipse IDE" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc5kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_kRLyfEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc50ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc6EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc6UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc6kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc60ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc7EZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.convertToYamlAction" commandName="Convert to YAML" description="Convert the TestNG Suite XML file to YAML" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc7UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc7kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.junitRAPShortcut.coverage" commandName="Coverage RAP JUnit Test" description="Coverage RAP JUnit Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc70ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc8EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc8UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc8kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc80ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc9EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc9UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc9kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE&amp;apos;s most fiercely contested preferences" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc90ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc-EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc-UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_kRLygUZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRPc-kZBEe2rRMoXHnyVUQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_kRPc-0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc_EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc_UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc_kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPc_0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdAUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdAkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdA0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdBEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_kRLyh0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdBUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdBkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdB0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdCEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdCUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdCkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdC0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdDEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdDUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdDkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdD0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdEEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdEUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdEkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdE0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdFEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRPdFUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_kRPdFkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdF0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdGEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdGUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdGkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdG0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdHEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdHUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdHkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_kRLyg0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdH0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdIEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdIUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdIkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdI0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdJEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRPdJUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD4EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_kRLyikZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQD4UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_kRQD4kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD40ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD5EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD5UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD5kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD50ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD6EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_kRLyf0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQD6UZBEe2rRMoXHnyVUQ" elementId="url" name="URL"/>
    <parameters xmi:id="_kRQD6kZBEe2rRMoXHnyVUQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_kRQD60ZBEe2rRMoXHnyVUQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_kRQD7EZBEe2rRMoXHnyVUQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_kRQD7UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD7kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD70ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD8EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD8UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD8kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD80ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD9EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD9UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD9kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_kRLyf0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQD90ZBEe2rRMoXHnyVUQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_kRQD-EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD-UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD-kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD-0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD_EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="_kRLyeUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD_UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD_kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQD_0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEAUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEAkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_kRLyeUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEA0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEBEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEBUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEBkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEB0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQECEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQECUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQECkZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.shortcut.rerunFailed" commandName="Rerun Failed TestNG Test" description="Rerun the failed TestNG Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEC0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEDEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEDUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEDkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.mergeSessions" commandName="Merge Sessions" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQED0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEEEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEEUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEEkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEE0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEFEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEFUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEFkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEF0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEGEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEGUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEGkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_kRLyiUZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQEG0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_kRQEHEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_kRQEHUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEHkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEH0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEIEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEIUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEIkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEI0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEJEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEJUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEJkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEJ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEKEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEKUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEKkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEK0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQELEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQELUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQELkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEL0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEMEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEMUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEMkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEM0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQENEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQENUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQENkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEN0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEOEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEOUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEOkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEO0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEPEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.selectCounters" commandName="Select Counters" category="_kRLyekZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQEPUZBEe2rRMoXHnyVUQ" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_kRQEPkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEP0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.testNgSuiteShortcut.coverage" commandName="Coverage TestNG Suite" description="Coverage TestNG Suite" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEQEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEQUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEQkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEQ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEREZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQERUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQERkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQER0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQESEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_kRLye0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQESUZBEe2rRMoXHnyVUQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_kRQESkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQES0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQETEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQETUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_kRLyikZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQETkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQET0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEUEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEUUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_kRLyiUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEUkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQEU0ZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.shortcut.testngSuite.debug" commandName="Debug TestNG Suite" description="Debug TestNG Suite" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq8EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq8UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq8kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq80ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq9EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq9UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq9kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq90ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq-EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq-UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq-kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq-0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq_EZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq_UZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq_kZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQq_0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrAUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrAkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrA0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrBEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrBUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrBkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrB0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrCEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.importSession" commandName="Import Session..." category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrCUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrCkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrC0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort Outline" category="_kRLyeUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrDEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_kRLyhkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQrDUZBEe2rRMoXHnyVUQ" elementId="title" name="Title"/>
    <parameters xmi:id="_kRQrDkZBEe2rRMoXHnyVUQ" elementId="message" name="Message"/>
    <parameters xmi:id="_kRQrD0ZBEe2rRMoXHnyVUQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_kRQrEEZBEe2rRMoXHnyVUQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_kRQrEUZBEe2rRMoXHnyVUQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_kRQrEkZBEe2rRMoXHnyVUQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_kRQrE0ZBEe2rRMoXHnyVUQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_kRQrFEZBEe2rRMoXHnyVUQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_kRQrFUZBEe2rRMoXHnyVUQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_kRQrFkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrF0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrGEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrGUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrGkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrG0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrHEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_kRLyh0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrHUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrHkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrH0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrIEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrIUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrIkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrI0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrJEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrJUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrJkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrJ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrKEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrKUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrKkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_kRLyiUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrK0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrLEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_kRLyg0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrLUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrLkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrL0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrMEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrMUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_kRLyeEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQrMkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_kRQrM0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrNEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrNUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrNkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQrN0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_kRQrOEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_kRQrOUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrOkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrO0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrPEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrPUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrPkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrP0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrQEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrQUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_kRLyeEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQrQkZBEe2rRMoXHnyVUQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_kRQrQ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrREZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrRUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrRkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_kRLycUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrR0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_kRLycEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrSEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrSUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrSkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrS0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.removeActiveSession" commandName="Remove Active Session" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrTEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrTUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrTkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrT0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrUEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrUUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrUkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrU0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrVEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrVUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrVkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_kRLycEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrV0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrWEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrWUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrWkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrW0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrXEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrXUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrXkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrX0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_kRLyg0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrYEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_kRLye0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRQrYUZBEe2rRMoXHnyVUQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_kRQrYkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrY0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrZEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrZUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrZkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrZ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQraEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQraUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrakZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQra0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrbEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrbUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrbkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrb0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrcEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrcUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrckZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.swtBotJunitShortcut.coverage" commandName="Coverage SWTBot Test" description="Coverage SWTBot Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrc0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrdEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrdUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrdkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrd0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQreEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQreUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_kRLyfEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrekZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQre0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrfEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrfUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_kRLygkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrfkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrf0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrgEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.linkWithSelection" commandName="Link with Current Selection" category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRQrgUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSAEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSAUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSAkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSA0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSBEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSBUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSBkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSB0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_kRLydkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSCEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSCUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSCkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSC0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSDEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSDUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_kRLydEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSDkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSD0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.FetchGithubPR" commandName="Fetch Github Pull Request" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSEEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSEUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSEkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSE0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSFEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSFUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSFkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSF0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSGEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSGUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSGkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSG0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_kRLyf0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSHEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_kRRSHUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_kRLygUZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSHkZBEe2rRMoXHnyVUQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_kRRSH0ZBEe2rRMoXHnyVUQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_kRRSIEZBEe2rRMoXHnyVUQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_kRRSIUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.testNgShortcut.coverage" commandName="Coverage TestNG Test" description="Coverage TestNG Test" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSIkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSI0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_kRLyf0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSJEZBEe2rRMoXHnyVUQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_kRRSJUZBEe2rRMoXHnyVUQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_kRRSJkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSJ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSKEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSKUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSKkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_kRLygUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSK0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSLEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSLUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSLkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSL0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_kRLyckZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSMEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSMUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSMkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSM0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSNEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSNUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_kRLyfEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSNkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSN0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSOEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_kRLye0ZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSOUZBEe2rRMoXHnyVUQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_kRRSOkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSO0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSPEZBEe2rRMoXHnyVUQ" elementId="org.testng.eclipse.convertAction" commandName="Convert to TestNG" description="Convert the selection to TestNG" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSPUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="_kRLyeUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSPkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSP0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_kRLye0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSQEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.selectActiveSession" commandName="Select Active Session..." category="_kRLyekZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSQUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSQkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSQ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSREZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSRUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSRkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSR0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSSEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSSUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSSkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSS0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_kRRSTEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_kRLyjkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSTUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_kRRSTkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_kRRST0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSUEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_kRLyeEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSUUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_kRLygUZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSUkZBEe2rRMoXHnyVUQ" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_kRRSU0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.FetchGitlabMergeRequest" commandName="Fetch Gitlab Merge Request" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSVEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSVUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_kRLyeEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSVkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_kRRSV0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSWEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_kRLyhkZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSWUZBEe2rRMoXHnyVUQ" elementId="title" name="Title"/>
    <parameters xmi:id="_kRRSWkZBEe2rRMoXHnyVUQ" elementId="message" name="Message"/>
    <parameters xmi:id="_kRRSW0ZBEe2rRMoXHnyVUQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_kRRSXEZBEe2rRMoXHnyVUQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_kRRSXUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSXkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_kRLygEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSX0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSYEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSYUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSYkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSY0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSZEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSZUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSZkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_kRLyjEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSZ0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_kRLyhEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSaEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_kRLyd0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSaUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSakZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_kRLyiEZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSa0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_kRLyeEZBEe2rRMoXHnyVUQ">
    <parameters xmi:id="_kRRSbEZBEe2rRMoXHnyVUQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_kRRSbUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui.commands.CoverageLast" commandName="Coverage" description="Coverage" category="_kRLyhUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kRRSbkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_kRLyfUZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kTffkUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cocoa.arrangeWindowsInFront" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.arrangeWindows.name" description="%command.arrangeWindows.desc" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kTffk0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cocoa.minimizeWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.minimize.name" description="%command.minimize.desc" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kTfflUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cocoa.zoomWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.zoom.name" description="%command.zoom.desc" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kTgGoUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.cocoa.closeDialog" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.closeDialog.name" description="%command.closeDialog.desc" category="_kRLyf0ZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaQO4EZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaRdAEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaRdAUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaRdAkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSEEEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSEEUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSEEkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSEE0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSEFEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSEFUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSEFkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageDropDownAction" commandName="Coverage" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSrIEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageAsAction" commandName="Coverage As" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSrIUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageHistoryAction" commandName="Coverage History" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaSrIkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaUgUEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaUgUUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaUgUkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVHYEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVHYUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVHYkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVHY0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVHZEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVucEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVucUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVuckZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaVuc0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaWVgEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaWVgUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaWVgkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaWVg0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaW8kEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaW8kUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaW8kkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaW8k0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaW8lEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaW8lUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaW8lkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaXjoEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaXjoUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaXjokZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaXjo0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaXjpEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaXjpUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaXjpkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYKsEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYKsUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYKskZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYKs0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYKtEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYKtUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYKtkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYxwEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYxwUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYxwkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYxw0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaYxxEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZY0EZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZY0UZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZY0kZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZY00ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZY1EZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZY1UZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZY1kZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZ_4EZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZ_4UZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZ_4kZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZ_40ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZ_5EZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZ_5UZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaZ_5kZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaam8EZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaam8UZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaam8kZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaam80ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaam9EZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaam9UZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kaam9kZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kabOAEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kabOAUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kabOAkZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kabOA0ZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kabOBEZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <commands xmi:id="_kabOBUZBEe2rRMoXHnyVUQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_kRLyjkZBEe2rRMoXHnyVUQ"/>
  <addons xmi:id="_kQ3CX0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_kQ3CYEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_kQ3CYUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_kQ3CYkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_kQ3CY0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_kQ3CZEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_kQ3CZUZBEe2rRMoXHnyVUQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_kQ3CZkZBEe2rRMoXHnyVUQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_kQ3CZ0ZBEe2rRMoXHnyVUQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_kQ3CaEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_kQ7TwEZBEe2rRMoXHnyVUQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_IYS0IKimEeS11vbz3f9ezw" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_kTffkEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler"/>
  <categories xmi:id="_kRLycEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_kRLycUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_kRLyckZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_kRLyc0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_kRLydEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_kRLydUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_kRLydkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_kRLyd0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_kRLyeEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_kRLyeUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_kRLyekZBEe2rRMoXHnyVUQ" elementId="org.eclipse.eclemma.ui" name="EclEmma Code Coverage"/>
  <categories xmi:id="_kRLye0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_kRLyfEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_kRLyfUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_kRLyfkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_kRLyf0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_kRLygEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_kRLygUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_kRLygkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_kRLyg0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_kRLyhEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_kRLyhUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_kRLyhkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_kRLyh0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_kRLyiEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_kRLyiUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_kRLyikZBEe2rRMoXHnyVUQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_kRLyi0ZBEe2rRMoXHnyVUQ" elementId="org.eclipse.lsp4e.commandCategory" name="Command"/>
  <categories xmi:id="_kRLyjEZBEe2rRMoXHnyVUQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_kRLyjUZBEe2rRMoXHnyVUQ" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_kRLyjkZBEe2rRMoXHnyVUQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
</application:Application>
