<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<section name="ExternalProjectImportWizard">
		<item key="WizardProjectsImportPage.STORE_NESTED_PROJECTS" value="false"/>
		<item key="WizardProjectsImportPage.STORE_COPY_PROJECT_ID" value="false"/>
		<item key="WizardProjectsImportPage.STORE_ARCHIVE_SELECTED" value="false"/>
		<item key="WizardProjectsImportPage.STORE_CLOSE_CREATED_PROJECTS_ID" value="false"/>
		<item key="WizardProjectsImportPage.STORE_HIDE_CONFLICTING_PROJECTS_ID" value="false"/>
		<list key="WizardProjectsImportPage.STORE_DIRECTORIES">
			<item value="/Users/<USER>/oe-api-automation"/>
		</list>
		<list key="WizardProjectsImportPage.STORE_ARCHIVES">
			<item value=""/>
		</list>
	</section>
	<section name="SmartImportWizard">
		<item key="SmartImportRootWizardPage.STORE_HIDE_ALREADY_OPEN" value="false"/>
		<item key="SmartImportRootWizardPage.STORE_CLOSE_IMPORTED" value="false"/>
		<item key="SmartImportRootWizardPage.STORE_NESTED_PROJECTS" value="true"/>
		<item key="SmartImportRootWizardPage.STORE_CONFIGURE_NATURES" value="true"/>
	</section>
</section>
