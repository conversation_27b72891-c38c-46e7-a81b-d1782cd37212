<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">


<suite name="payment - Onboarding Engine" parallel="tests" thread-count="10" preserve-order="true" verbose="30">


    <!-- Parallel execution at the test level, meaning each <test> tag will run in parallel -->
    <test name="payment" preserve-order="true">
        <classes>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividual"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividualRejection"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingPrivateLimited"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingPrivateLimitedRejection"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingProprietorship"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingProprietorshipRejection"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividualWithForm60"/>

            <class name="OCL.CommonOnboarding.FlowCommonOnboardingRekyc_KycUpdate"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingRekycKyc_UpdateAutoQC_Individual_WithPAN"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingRekycKyc_UpdateAutoQC_Prop_With_GSTN"/>


            <class name="OCL.CommonOnboarding.FlowCommonOnboardingIndividualNegative"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingPrivateLimitedNegative"/>

            <class name="OCL.ManageAgentOEPanel.ManageAgent"/>

            <class name="OCL.CommonOnboarding.AdditionalDetailsCoAPI"/>
            <class name="OCL.CommonOnboarding.AudioDetailsCoAPI"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingGetallResources"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingLightWeightAPI"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingMerchantAgreementTnC"/>
            <class name="OCL.CommonOnboarding.FlowCommonOnboardingTnCAccept"/>

            <class name="OCL.DIY.MCO.CreateLead"/>
            <class name="OCL.DIY.MerchantLimitUpgrade.CreateLeadUpdated"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.BillingAddressUpdate"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.BusinessNameUpdate"/>
            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.GstUpdate"/>
            <class name="OCL.PGProfileUpdate.BankDetailUpdate.BankDetailUpdate"/>
            <class name="OCL.PGProfileUpdate.BankDetailUpdate.BankDetailUpdateNegativeCases"/>

            <class name="OCL.DIY.ProfileUpdateBossToOEDIYFlows.RegisteredAddressUpdate"/>
            <class name="OCL.SFtoOE2024.BusinessLeadCreation"/>
            <class name="OCL.SFtoOE2024.SolutionLeadCreation"/>

            <class name="OCL.FseOnboardingDiy.FseDiy"/>
            <class name="OCL.FseOnboardingDiy.FseDiyPositiveFlow"/>



        </classes>


    </test>

    <test name="Devices" preserve-order="true">


    <packages>
        <package name="OCL.Business.UnmapEDC.*"/>
        <package name="OCL.Business.UpgradeMerchantPlan.*"/>
        <package name="OCL.Business.UpgradeEDC.*"/>
        <package name="OCL.CommonOnboarding.EDC.*"/>
        <package name="OCL.DIY.CashAtPosDIY.*"/>
        <package name="OCL.DIY.EMIOfferingDIY.*"/>
        <package name="OCL.DIY.BrandEmiDIY.*"/>
        <package name="OCL.BrandEMI.*"/>
        <package name="OCL.PG.*"/>
        <package name="OCL.PlanManagement.*"/>
        <package name="OCL.ODS.*"/>
        <package name="OCL.IOT.*"/>
        <package name="OCL.Individual.SoundBox.*"/>
        <package name="OCL.Subscription.*"/>
        <package name="OCL.GenericTestsForApis.*"/>
        <package name="OCL.Individual.PSA.*"/>
        <package name="OCL.Individual.RegisterLead.*"/>
        <package name="OCL.UAD.*"/>
        <package name="OCL.KYB.*"/>
        <package name="OCL.Billing.*"/>
        <package name="ats.subpartbarcodes.*"/>
        <package name="OCL.EDCDIY.*"/>
        <package name="OCL.FSM.*"/>
        <package name="OCL.EGS.*"/>
        <package name="OCL.Business.ReplaceSubpartEDC.*"/>
        <package name="OCL.Business.DeploymentDetails.*"/>
        <package name="OCL.ATS.*"/>
        <package name="OCL.PGProfileUpdateAddAddress.*"/>
        <package name="OCL.UpdateDeviceAddress.*"/>
        <package name="OCL.CIF.*"/>
        <package name="OCL.ManageSim.*"/>
        <package name="OCL.SHOPinsurance.*"/>
        <package name="OCL.RetentionOfferEDC.*"/>
        <package name="OCL.OnboardBankV3.*"/>
        <package name="OCL.Individual.RevisitFlow"/>
        <package name="OCL.EmbeddedInsurance.*"/>
        <package name="OCL.Individual.RevisitFlow"/>
        <package name="OCL.EDCDeviceUpgradeV2.*"/>



    </packages>
    </test>


    <listeners>
        <listener class-name="com.paytm.apitools.listeners.SuiteListener"/>
        <listener class-name="com.paytm.apitools.customreporter.CustomReporter"/>
        <!--        <listener class-name="com.paytm.framework.reporting.listeners.RPTestListener"/>-->
        <listener class-name="AfterSuite.ListenerTest"/>
    </listeners>
</suite>

