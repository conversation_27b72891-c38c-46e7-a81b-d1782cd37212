
package DevicesUtils;
import Request.MerchantService.v1.merchant.fetchAllTerminal;
import Services.MechantService.MiddlewareServices;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


public class DevicesUtils extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    /*
      Get All Terminal Associated With a Mid
     */
    String VersionNumber="7.3.8";
    public Response GetAllTerminal(String UserMID)
    {
        String AgentToken = AgentSessionToken("7771216290", "paytm@123");
        fetchAllTerminal fetchAllTerminalObject = new fetchAllTerminal();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid",UserMID);
        Response fetchAllTerminalResponse = middlewareServicesObject.v1FetchallTerminal(fetchAllTerminalObject,queryParams,AgentToken,  VersionNumber);
        return fetchAllTerminalResponse;

    }
    /*
        Get Active Device
    */

    public String getActiveDevice(Response res) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            System.out.println(res.getBody().asString());
            JsonNode responseNode = objectMapper.readTree(res.getBody().asString()).findValue("response");
            System.out.println(responseNode);

            System.out.println(responseNode);
                for (JsonNode device : responseNode) {
                    if ("ACTIVE".equals(device.get("status").asText())
                            && !"CS70".equals(device.get("modelName").asText()))
                    {
                        System.out.println(device.get("serialNo").asText());
                        return device.get("serialNo").asText();
                    }
                }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return "";
    }



}
