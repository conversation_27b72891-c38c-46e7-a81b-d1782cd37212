package MerchantService.loan.lead.dynamicTNC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchDynamicTnc  extends AbstractApiV2{
	   
		public FetchDynamicTnc()
	    {
			   super("MerchantService/loan/lead/dynamicTnc/TncRequest.json", "MerchantService/loan/lead/dynamicTnc/TncResponse.json", "MerchantService/loan/lead/dynamicTnc/TncProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}	