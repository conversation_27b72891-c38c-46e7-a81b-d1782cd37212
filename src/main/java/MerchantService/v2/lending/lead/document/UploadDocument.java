package MerchantService.v2.lending.lead.document;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UploadDocument extends AbstractApiV2 {

	  public UploadDocument () {
	        super("MerchantService/v2/lending/lead/document/UploadDocumentRequest.json","MerchantService/v2/lending/lead/document/UploadDocumentResponse.json","MerchantService/v2/lending/lead/document/UploadDocumentProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	}