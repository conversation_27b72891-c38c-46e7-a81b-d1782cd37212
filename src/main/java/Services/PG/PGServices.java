package Services.PG;

import Request.PG.*;
import Request.Subscription.CreateSubscription;
import io.restassured.response.Response;

import java.util.Map;

public class PGServices {


    public Response v1FetchMID(String custId, String Mobile , String sessionToken) {

        GetMIDUsingCustID v1FetchMID = new GetMIDUsingCustID(custId);
        //v1FetchMID.addParameter("merchantCustId", CustId);
         v1FetchMID.addParameter("mobileNumber", Mobile);

       // v1FetchMID.setHeader("Content-Type", "application/json");
        v1FetchMID.setHeader("x-sso-token", sessionToken);
      //  v1FetchMID.setHeader("appLanguage", "en");
      //  v1FetchMID.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1fetchMidResp = v1FetchMID.callAPI();

        return v1fetchMidResp;
    }

    public Response createMerchantOnPG(CreateMerchantOnPG CreateMerchant, Map<String,String> Body) {


        for(Map.Entry m : Body.entrySet())
        {
            CreateMerchant.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createMerchantOnPGResponse = CreateMerchant.callAPI();
           return createMerchantOnPGResponse;



    }

    public Response MarkingMIDInactiveOnPG(MarkingMIDInactiveOnPG MarkMIDInactive, Map<String,String> Body) {


        for(Map.Entry m : Body.entrySet())
        {
            MarkMIDInactive.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        MarkMIDInactive.setHeader("x-sso-token", "2c909d08-6987-4474-b2c0-d798cc731200");


        Response markMidInactiveOnPGResponse = MarkMIDInactive.callAPI();
        return markMidInactiveOnPGResponse;



    }


    public Response getPreferenceFromPG(GetPreference GetPreferenceObj , Map<String,String> headers)
    {

        for(Map.Entry m : headers.entrySet())
        {
            GetPreferenceObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response GetPreferenceObjResp=GetPreferenceObj.callAPI();
        return GetPreferenceObjResp;

    }

    public Response editMerchantOnPG(EditMerchantOnPG EditMerchantOnPGObj, Map<String,String> Body) {


        for(Map.Entry m : Body.entrySet())
        {
            EditMerchantOnPGObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response EditMerchantOnPGObjResp = EditMerchantOnPGObj.callAPI();
        return EditMerchantOnPGObjResp;



    }



    public Response applyTieredMdr(ApplyTieredMdr ApplyTieredMdrObj,Map<String,String> headers,Map<String,String> params, Map<String,String> body) {


        for(Map.Entry m : headers.entrySet())
        {
            ApplyTieredMdrObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            ApplyTieredMdrObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : params.entrySet())
        {
            ApplyTieredMdrObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response ApplyTieredMdrObjResp = ApplyTieredMdrObj.callAPI();
        return ApplyTieredMdrObjResp;



    }

    public Response replaceEDCMachine(ReplaceTerminalInPG ReplaceTerminalInPGObj,Map<String,String> headers, Map<String,String> body) {


        for(Map.Entry m : headers.entrySet())
        {
            ReplaceTerminalInPGObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            ReplaceTerminalInPGObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response ReplaceTerminalInPGObjResp = ReplaceTerminalInPGObj.callAPI();
        return ReplaceTerminalInPGObjResp;



    }

    public Response onboardBanksOnPG(OnboardBanks OnboardBanksObj,Map<String,String> headers, Map<String,String> body) {


        for(Map.Entry m : headers.entrySet())
        {
            OnboardBanksObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            OnboardBanksObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response OnboardBanksObjResp = OnboardBanksObj.callAPI();
        return OnboardBanksObjResp;



    }

    public Response checkUPIEligibilityOnBoss(UPIEligibilityOnBoss UPIEligibilityOnBossObj,Map<String,String> headers,Map<String,String> params) {

        for(Map.Entry m : headers.entrySet())
        {
            UPIEligibilityOnBossObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : params.entrySet())
        {
            UPIEligibilityOnBossObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response UPIEligibilityOnBossObjResp = UPIEligibilityOnBossObj.callAPI();
        return UPIEligibilityOnBossObjResp;

    }

    public Response verifyUPIAddChannel(UPIAddChannel UPIAddChannelObj,Map<String,String> headers,Map<String,String> body) {

        for(Map.Entry m : headers.entrySet())
        {
            UPIAddChannelObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : body.entrySet())
        {
            UPIAddChannelObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response UPIAddChannelObjResp = UPIAddChannelObj.callAPI();
        return UPIAddChannelObjResp;

    }

    public Response configureMbidOnPG(ConfigureMbidOnPG ConfigureMbidObj,Map<String,String> headers, Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            ConfigureMbidObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            ConfigureMbidObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ConfigureMbidObjResp = ConfigureMbidObj.callAPI();
        return ConfigureMbidObjResp;
    }

    public Response onboardBanksRBDCOnPG(OnboardBanksRBDC OnboardBanksRBDCObj,Map<String,String> headers, Map<String,String> body) {


        for(Map.Entry m : headers.entrySet())
        {
            OnboardBanksRBDCObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            OnboardBanksRBDCObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response OnboardBanksRBDCObjResp = OnboardBanksRBDCObj.callAPI();
        return OnboardBanksRBDCObjResp;



    }


}

