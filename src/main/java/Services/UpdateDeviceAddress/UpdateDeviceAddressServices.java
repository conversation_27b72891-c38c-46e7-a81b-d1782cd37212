package Services.UpdateDeviceAddress;

import Request.Billing.BillingOnboard;
import Request.MerchantService.v1.IOT.IOTnotification;
import Request.UpdateDevideAddress.CreateLeadUpdateDeviceAddress;
import Services.IOT.IOTMiddlewareServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class UpdateDeviceAddressServices extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(UpdateDeviceAddressServices.class);

    public Response CreateLeadUpdateAddress(CreateLeadUpdateDeviceAddress CreateLeadUpdateDeviceAddress, Map<String, String> body,
                                            Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            CreateLeadUpdateDeviceAddress.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            CreateLeadUpdateDeviceAddress.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response CreateLeadUpdateDeviceAddressResp = CreateLeadUpdateDeviceAddress.callAPI();
        return CreateLeadUpdateDeviceAddressResp;
    }

}
