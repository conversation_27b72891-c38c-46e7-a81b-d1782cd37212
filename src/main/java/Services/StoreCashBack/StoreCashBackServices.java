package Services.StoreCashBack;

import Request.StoreCashBack.CreateStoreCashBack;
import Request.StoreCashBack.StoreCashBackTnc;
import io.restassured.response.Response;

import java.util.Map;

public class StoreCashBackServices {
	
	
	public Response StoreCashBackResponse(String requestBodyPath,Map<String,String> headers,Map<String,String> body,Map<String,String> params)
	{

		CreateStoreCashBack fetch = new CreateStoreCashBack(requestBodyPath);
		for(Map.Entry m : headers.entrySet())
		{
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}
		
		for(Map.Entry m : params.entrySet())
		{
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}
		
		
		Response resp=fetch.callAPI();
		return resp;
		
		
	}

	public Response StoreCashBackResponse(Map<String,String> headers,Map<String,String> body,Map<String,String> params)
	{

		StoreCashBackTnc fetch = new StoreCashBackTnc();
		for(Map.Entry m : headers.entrySet())
		{
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : params.entrySet())
		{
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}


		Response resp2=fetch.callAPI();
		return resp2;


	}


}
