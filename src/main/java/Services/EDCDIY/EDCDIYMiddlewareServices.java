package Services.EDCDIY;

import Request.EDCDIY.DeviceFraudcheck;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class EDCDIYMiddlewareServices extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(EDCDIYMiddlewareServices.class);

    public Response edcDiyMiddlewareServices(DeviceFraudcheck DeviceFraudcheck, Map<String, String> body,
                                             Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            DeviceFraudcheck.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            DeviceFraudcheck.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response DeviceFraudcheckResp = DeviceFraudcheck.callAPI();
        return DeviceFraudcheckResp;
    }
}
