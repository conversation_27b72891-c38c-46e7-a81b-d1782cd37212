package Services.FSM;

import Request.FSM.*;
import io.restassured.response.Response;

import java.util.Map;

public class FSMServices
{
    public Response fetchFseDetail(FetchFseDetail FetchFseDetailObj, Map<String,String> headers, Map<String,String> params)
    {
        for(Map.Entry m : headers.entrySet())
        {
            FetchFseDetailObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : params.entrySet())
        {
            FetchFseDetailObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response FetchFseDetailObjResp=FetchFseDetailObj.callAPI();
        return FetchFseDetailObjResp;


    }

    public Response fetchMerchantDetail(FetchMerchantDetail FetchMerchantDetailObj, Map<String,String> headers, Map<String,String> params,Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            FetchMerchantDetailObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : params.entrySet())
        {
            FetchMerchantDetailObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            FetchMerchantDetailObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response FetchMerchantDetailObjResp=FetchMerchantDetailObj.callAPI();
        return FetchMerchantDetailObjResp;


    }

    public Response fetchBeatDetail(FetchBeatDetail FetchBeatDetailObj, Map<String,String> headers,Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            FetchBeatDetailObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : body.entrySet())
        {
            FetchBeatDetailObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response FetchBeatDetailObjResp=FetchBeatDetailObj.callAPI();
        return FetchBeatDetailObjResp;


    }

    public Response updateTeamAndSubteam(UpdateTeamAndSubteam UpdateTeamAndSubteamObj, Map<String,String> headers, Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            UpdateTeamAndSubteamObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : body.entrySet())
        {
            UpdateTeamAndSubteamObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response UpdateTeamAndSubteamObjResp=UpdateTeamAndSubteamObj.callAPI();
        return UpdateTeamAndSubteamObjResp;


    }

    public Response enterpriseBeatCreation(EnterpriseBeatCreation EnterpriseBeatCreationObj, Map<String,String> headers, Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            EnterpriseBeatCreationObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : body.entrySet())
        {
            EnterpriseBeatCreationObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response EnterpriseBeatCreationObjResp=EnterpriseBeatCreationObj.callAPI();
        return EnterpriseBeatCreationObjResp;


    }
}
