package Services.oAuth;


import Request.oAuth.oAuthWormhole.CreateUser;
import Request.oAuth.oAuthWormhole.UserDetails;
import Request.oAuth.oauth2.Authorize;
import Request.oAuth.oauth2.Token;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;


public class oAuthServices {

    private static final Logger LOGGER = LogManager.getLogger(oAuthServices.class);


    public Response oAuthoauth2Autharize(Authorize authorizeRequestObject)
    {

        //Create API Object
        Authorize AuthorizeObject = new Authorize();

        //Adding Headers
        AuthorizeObject.setHeader("Authorization",authorizeRequestObject.getAuthorization());
        AuthorizeObject.setHeader("Content-Type",authorizeRequestObject.getContentType());


        //Adding mandatory Body Params
        AuthorizeObject.addFormParameter("response_type",authorizeRequestObject.getResponse_type());
        AuthorizeObject.addFormParameter("client_id",authorizeRequestObject.getClient_id());
        AuthorizeObject.addFormParameter("do_not_redirect",authorizeRequestObject.getDo_not_redirect());
        AuthorizeObject.addFormParameter("scope",authorizeRequestObject.getScope());
        AuthorizeObject.addFormParameter("username",authorizeRequestObject.getUsername());
        AuthorizeObject.addFormParameter("password",authorizeRequestObject.getPassword());


        LOGGER.info("Hitting Autharize API");
        //Calling the API
        Response AuthorizeResponse =AuthorizeObject.callAPI();
        LOGGER.info("response code : "+ AuthorizeResponse.getStatusCode());
        return AuthorizeResponse;
    }
    public Response oauthorizeGG(Authorize authorizeRequestObject) {

        //Create API Object
        Authorize AuthorizeObject = new Authorize();

        //Adding Headers
        AuthorizeObject.setHeader("Authorization",authorizeRequestObject.getAuthorization());
        AuthorizeObject.setHeader("Content-Type",authorizeRequestObject.getContentType());


        //Adding mandatory Body Params
        AuthorizeObject.addFormParameter("response_type",authorizeRequestObject.getResponse_type());
        AuthorizeObject.addFormParameter("client_id",authorizeRequestObject.getClient_id());
        AuthorizeObject.addFormParameter("do_not_redirect",authorizeRequestObject.getDo_not_redirect());
        AuthorizeObject.addFormParameter("scope",authorizeRequestObject.getScope());
        AuthorizeObject.addFormParameter("username",authorizeRequestObject.getUsername());
        AuthorizeObject.addFormParameter("password",authorizeRequestObject.getPassword());

        //Adding Params
        AuthorizeObject.addParameter("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        AuthorizeObject.addParameter("deviceManufacturer","OPPO");
        AuthorizeObject.addParameter("deviceName","CPH1859");
        AuthorizeObject.addParameter("client","androidapp");
        AuthorizeObject.addParameter("imei","869003037324211");
        AuthorizeObject.addParameter("osVersion","8.0");


        LOGGER.info("Hitting Autharize API");
        //Calling the API
        Response AuthorizeResponse =AuthorizeObject.callAPI();
        LOGGER.info("response code : "+ AuthorizeResponse.getStatusCode());
        return AuthorizeResponse;
    }
    public Response tokenGG(Token TokenRequestObject) {
        //Create API Object
        Token TokenObject = new Token();

        //Adding Headers
        TokenObject.setHeader("Content-Type",TokenRequestObject.getContentType());
        TokenObject.setHeader("Authorization",TokenRequestObject.getAuthorization());


        //Adding mandatory Body Params
        TokenObject.addFormParameter("code",TokenRequestObject.getCode());
        TokenObject.addFormParameter("grant_type",TokenRequestObject.getGrant_type());
        TokenObject.addFormParameter("client_id",TokenRequestObject.getClient_id());
        TokenObject.addFormParameter("scope",TokenRequestObject.getScope());

        //Adding Params
        TokenObject.addParameter("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        TokenObject.addParameter("deviceManufacturer","OPPO");
        TokenObject.addParameter("deviceName","CPH1859");
        TokenObject.addParameter("client","androidapp");
        TokenObject.addParameter("imei","869003037324211");
        TokenObject.addParameter("osVersion","8.0");

        LOGGER.info("Hitting Token API");
        //Calling the API
        Response tokenResponse =TokenObject.callAPI();
        LOGGER.info("response code : "+ tokenResponse.getStatusCode());
        return tokenResponse;
    }
    public Response oauthorizePaytmApp(Authorize authorizeRequestObject) {

        //Create API Object
        Authorize AuthorizeObject = new Authorize();

        //Adding Headers
        AuthorizeObject.setHeader("Authorization",authorizeRequestObject.getAuthorization());
        AuthorizeObject.setHeader("Content-Type",authorizeRequestObject.getContentType());


        //Adding mandatory Body Params
        AuthorizeObject.addFormParameter("response_type",authorizeRequestObject.getResponse_type());
        AuthorizeObject.addFormParameter("client_id",authorizeRequestObject.getClient_id());
        AuthorizeObject.addFormParameter("do_not_redirect",authorizeRequestObject.getDo_not_redirect());
        AuthorizeObject.addFormParameter("scope",authorizeRequestObject.getScope());
        AuthorizeObject.addFormParameter("username",authorizeRequestObject.getUsername());
        AuthorizeObject.addFormParameter("password",authorizeRequestObject.getPassword());

        //Adding Params
        AuthorizeObject.addParameter("deviceIdentifier","samsung-SM-J810GF-359211091044016");
        AuthorizeObject.addParameter("deviceManufacturer","samsung");
        AuthorizeObject.addParameter("deviceName","SM-J810GF");
        AuthorizeObject.addParameter("client","androidapp");
        AuthorizeObject.addParameter("imei","359211091044016");
        AuthorizeObject.addParameter("osVersion","9");


        LOGGER.info("Hitting Autharize API");
        //Calling the API
        Response AuthorizeResponse =AuthorizeObject.callAPI();
        LOGGER.info("response code : "+ AuthorizeResponse.getStatusCode());
        return AuthorizeResponse;
    }
    public Response tokenPaytmApp(Token TokenRequestObject) {
        //Create API Object
        Token TokenObject = new Token();

        //Adding Headers
        TokenObject.setHeader("Content-Type",TokenRequestObject.getContentType());
        TokenObject.setHeader("Authorization",TokenRequestObject.getAuthorization());


        //Adding mandatory Body Params
        TokenObject.addFormParameter("code",TokenRequestObject.getCode());
        TokenObject.addFormParameter("grant_type",TokenRequestObject.getGrant_type());
        TokenObject.addFormParameter("client_id",TokenRequestObject.getClient_id());
        TokenObject.addFormParameter("scope",TokenRequestObject.getScope());

        //Adding Params
        TokenObject.addParameter("deviceIdentifier","samsung-SM-J810GF-359211091044016");
        TokenObject.addParameter("deviceManufacturer","samsung");
        TokenObject.addParameter("deviceName","SM-J810GF");
        TokenObject.addParameter("client","androidapp");
        TokenObject.addParameter("imei","359211091044016");
        TokenObject.addParameter("osVersion","9");

        LOGGER.info("Hitting Token API");
        //Calling the API
        Response tokenResponse =TokenObject.callAPI();
        LOGGER.info("response code : "+ tokenResponse.getStatusCode());
        return tokenResponse;
    }

    public Response oAuthoauth2Token(Token TokenRequestObject) {
        //Create API Object
        Token TokenObject = new Token();

        //Adding Headers
        TokenObject.setHeader("Content-Type",TokenRequestObject.getContentType());
        TokenObject.setHeader("Authorization",TokenRequestObject.getAuthorization());


        //Adding mandatory Body Params
        TokenObject.addFormParameter("code",TokenRequestObject.getCode());
        TokenObject.addFormParameter("grant_type",TokenRequestObject.getGrant_type());
        TokenObject.addFormParameter("client_id",TokenRequestObject.getClient_id());
        TokenObject.addFormParameter("scope",TokenRequestObject.getScope());

        LOGGER.info("Hitting Token API");
        //Calling the API
        Response tokenResponse =TokenObject.callAPI();
        LOGGER.info("response code : "+ tokenResponse.getStatusCode());
        return tokenResponse;
    }

    public String getoAuthSessionTokenCustID(String username, String password,boolean tokenOrCustID) {

        Authorize authorizeRequestObject = new Authorize();
        Authorize authorizeResponseObject = new Authorize();
        authorizeRequestObject.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
        authorizeRequestObject.setContentType("application/x-www-form-urlencoded");
        authorizeRequestObject.setResponse_type("code");
        authorizeRequestObject.setClient_id("GG-OE-staging");
        authorizeRequestObject.setDo_not_redirect("true");
        authorizeRequestObject.setScope("paytm");
        authorizeRequestObject.setUsername(username);
        authorizeRequestObject.setPassword(password);

        Response responseObject = oAuthoauth2Autharize(authorizeRequestObject);

        authorizeResponseObject.setCode(responseObject.jsonPath().getString("code"));
        LOGGER.info("Code GEnerated : "+responseObject.jsonPath().getString("code"));
        Token tokenRequestObject = new Token();
        Token tokenResponseObject = new Token();

        tokenRequestObject.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
        tokenRequestObject.setContentType("application/x-www-form-urlencoded");
        tokenRequestObject.setCode(authorizeResponseObject.getCode());
        tokenRequestObject.setGrant_type("authorization_code");
        tokenRequestObject.setClient_id("GG-OE-staging");
        tokenRequestObject.setScope("paytm");

        responseObject= oAuthoauth2Token(tokenRequestObject);


        tokenResponseObject.setAccess_token(responseObject.jsonPath().getString("access_token"));
        tokenResponseObject.setResourceOwnerId(responseObject.jsonPath().getString("resourceOwnerId"));

        if(tokenOrCustID==true) {
            LOGGER.info("In IF");
            return tokenResponseObject.getAccess_token();
        }else {
            LOGGER.info("In Else");
            return tokenResponseObject.getResourceOwnerId();
        }
    }

    public Response OauthUserDetails(UserDetails oauthObj,Map<String,String>body)
    {
        //Add Body Params
        for(Map.Entry m:body.entrySet()){
            oauthObj.getProperties().setProperty(m.getKey().toString(),m.getValue().toString());
        }

        Response OauthUserDetailsResp = oauthObj.callAPI();

        return OauthUserDetailsResp;
    }
    
    public Response oAuthoAuthWormholeCreateUser(CreateUser CreateUserRequestObject) {

        //Create API Object
        CreateUser CreateUserObject = new CreateUser();
        System.out.println("Cookie : " + CreateUserRequestObject.getCookie());
        //Adding Headers
        CreateUserObject.setHeader("Cookie",CreateUserRequestObject.getCookie());
        CreateUserObject.setHeader("Origin",CreateUserRequestObject.getOrigin());
        CreateUserObject.setHeader("Accept-Encoding",CreateUserRequestObject.getAcceptEncoding());
        CreateUserObject.setHeader("Accept-Language",CreateUserRequestObject.getAcceptLanguage());
        CreateUserObject.setHeader("User-Agent",CreateUserRequestObject.getUserAgent());
        CreateUserObject.setHeader("Content-Type",CreateUserRequestObject.getContentType());
        CreateUserObject.setHeader("Accept",CreateUserRequestObject.getAccept());
        CreateUserObject.setHeader("Referer",CreateUserRequestObject.getReferer());
        CreateUserObject.setHeader("X-Requested-With",CreateUserRequestObject.getXRequestedWith());
        CreateUserObject.setHeader("Connection",CreateUserRequestObject.getConnection());

        //Adding mandatory Body Params
        CreateUserObject.getProperties().setProperty("mobile",CreateUserRequestObject.getMobile());
        CreateUserObject.getProperties().setProperty("loginPassword",CreateUserRequestObject.getLoginPassword());

        System.out.println("Hitting CreateUser API");
        //Calling the API
        Response CreateUserResponse =CreateUserObject.callAPI();
        System.out.println("response code : "+ CreateUserResponse.getStatusCode());
        return CreateUserResponse;
    }

    public String getoAuthCustId(String mobileNumber) {

        CreateUser userObject = new CreateUser();

        //Add Query Params
        userObject.addParameter("fetch_strategy","DEFAULT,USERID,USER_TYPE");
        userObject.addParameter("phone",mobileNumber);

        //Add Headers
        userObject.setHeader("verification_type","service_token");
        userObject.setHeader("data","af213a43-2399-4c50-ab3a-295e20eafd32");
        userObject.setHeader("Authorization","Basic QmFuay1PRS1TdGFnaW5nOlhzbmVDekFQVVR6VHZZTnYzckdRbmlESTZjMmRHRllW");
        
        Response responseObject = userObject.callAPI();

       String custID = responseObject.jsonPath().getString("userId");
        return custID;
    }

}
