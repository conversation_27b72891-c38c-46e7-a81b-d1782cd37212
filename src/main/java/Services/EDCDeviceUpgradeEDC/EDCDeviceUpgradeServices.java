package Services.EDCDeviceUpgradeEDC;

import Request.EDCDeviceUpgradeV2.EDCDeviceUpgradeV2;
import Request.EGS.AirtelSimActivation;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class EDCDeviceUpgradeServices extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(EDCDeviceUpgradeServices.class);

    public Response EDCDeviceUpgradenMiddleware(EDCDeviceUpgradeV2 EDCDeviceUpgradeV2,Map<String, String> queryParams, Map<String, String> body,
                                                Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            EDCDeviceUpgradeV2.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            EDCDeviceUpgradeV2 .setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            EDCDeviceUpgradeV2.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response EDCDeviceUpgradeV2Resp = EDCDeviceUpgradeV2.callAPI();
        return EDCDeviceUpgradeV2Resp;
    }
}
