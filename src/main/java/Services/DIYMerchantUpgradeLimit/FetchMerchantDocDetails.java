package Services.DIYMerchantUpgradeLimit;

import Request.DIYMerchantLimitUpgrade.FetchMerchantDocDetail;
import io.restassured.response.Response;

import java.util.Map;

public class FetchMerchantDocDetails {


    public Response FetchMerchantDocDetails(String requestPath, Map<String, String> headers, Map<String, String> params) {

        FetchMerchantDocDetail fetch = new FetchMerchantDocDetail(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchMerchantDocDetail = fetch.callAPI();


        return fetchMerchantDocDetail;
    }

}
