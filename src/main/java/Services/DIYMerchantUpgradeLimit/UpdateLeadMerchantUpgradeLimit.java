package Services.DIYMerchantUpgradeLimit;

import Request.DIYMerchantLimitUpgrade.UpdatePanManuallyUpgradeLimit;
import io.restassured.response.Response;

import java.util.Map;

public class UpdateLeadMerchantUpgradeLimit {

    public Response updateLeadMerchantUpgradeLimit(String requestPath, Map<String, String> headers, Map<String, String> params, Map<String, String> body) {

        UpdatePanManuallyUpgradeLimit fetch = new UpdatePanManuallyUpgradeLimit(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response updateLeadMerchantUpgradeLimit = fetch.callAPI();


        return updateLeadMerchantUpgradeLimit;
    }

}

