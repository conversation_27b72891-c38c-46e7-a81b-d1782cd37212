package Services.DIYMerchantUpgradeLimit;

import Request.DIYMerchantLimitUpgrade.FetchScreenDetailsLimitUpgrade;
import io.restassured.response.Response;

import java.util.Map;

public class FetchScreenDetailsUpgradeLimit {
    public Response FetchScreenDetailsUpgradeLimit(String requestPath, Map<String, String> headers, Map<String, String> params) {
        FetchScreenDetailsLimitUpgrade fetch = new FetchScreenDetailsLimitUpgrade(requestPath);
        
        // Safely handle headers
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                if (entry.getKey() != null && entry.getValue() != null) {
                    fetch.setHeader(entry.getKey(), entry.getValue());
                }
            }
        }

        // Safely handle params
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (entry.getKey() != null && entry.getValue() != null) {
                    fetch.addParameter(entry.getKey(), entry.getValue());
                }
            }
        }

        return fetch.callAPI();
    }
}
