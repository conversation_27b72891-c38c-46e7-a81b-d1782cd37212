package Services.Wallet;

import Request.Wallet.CreateUserWallet;
import Request.Wallet.GenerateUpiQr;
import Request.Wallet.GetQrDetails;
import Request.Wallet.WalletAddMoney;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

public class WalletServices extends BaseMethod
    {

        public Response createUserWallet(CreateUserWallet createUserObj)
        {
            createUserObj.setHeader("Cache-Control","no-cache");
            createUserObj.setHeader("Content-Type","application/json");
            createUserObj.setHeader("Postman-Token","a89db274-e374-4f43-b486-cb33515647f6,9c8b1607-5cb8-49da-af78-5f7add888145");
            createUserObj.setHeader("ssotoken","96160cd1-b86b-445c-85ab-4a4a5123d98c");
            createUserObj.setHeader("tokentype","OAUTH");

            Response createUserResp = createUserObj.callAPI();

            return createUserResp;
        }

        public Response fetchQrDetailsWallet(GetQrDetails fetchQrObj, String QrId, String SessionToken)
        {

            fetchQrObj.setHeader("ssotoken",SessionToken);
            fetchQrObj.setHeader("Content-Type","application/json");

            fetchQrObj.getProperties().setProperty("qrCodeId",QrId);

            Response fetchQrCodeDetails = fetchQrObj.callAPI();

            return fetchQrCodeDetails;
        }

        public Response GenerateUpiQr(GenerateUpiQr generateQr)
        {
            generateQr.setHeader("Content-Type", "application/json");
            generateQr.setHeader("cache-control", "no-cache");
            generateQr.setHeader("clientid", "a5516f104428408fb6051f833c9bb9e0");
            generateQr.setHeader("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");

            Response v1geenrateUpiQr = generateQr.callAPI();
            return v1geenrateUpiQr;
        }

        public Response WalletAddMoney(WalletAddMoney addMoney,String token)
        {
            addMoney.setHeader("Cache-Control","no-cache");
            addMoney.setHeader("Content-Type","application/json");
            addMoney.setHeader("Postman-Token","56027ade-75f2-fb12-e540-de1496d51c0b");
            addMoney.setHeader("ssotoken",token);

            Response addMoneyResp = addMoney.callAPI();

            return addMoneyResp;
        }
    }
