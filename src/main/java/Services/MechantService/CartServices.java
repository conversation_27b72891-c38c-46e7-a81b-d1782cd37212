package Services.MechantService;

import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import Request.MerchantService.v1.Cart.SaveCart;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CartServices extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(CartServices.class);
    private static final String XMW_CHECKSUM_BYPASS = "BabaBlackSheepWeAreInShitDeep";

    public Response saveCart(String leadId, String planId, String deviceIdentifier, String version) {
        SaveCart saveCartObj = new SaveCart();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("deviceIdentifier", deviceIdentifier);
        headers.put("version", version);
        headers.put("UncleScrooge", XMW_CHECKSUM_BYPASS);

        Map<String, String> body = new HashMap<>();
        body.put("leadId", leadId);
        body.put("planId", planId);
        body.put("quantity", "1");

        for (Map.Entry m : headers.entrySet()) {
            saveCartObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveCartObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response response = saveCartObj.callAPI();
        return response;
    }

    public Response saveCart(String leadId, List<Map<String, Object>> items, String deviceIdentifier, String version) {
        SaveCart saveCartObj = new SaveCart();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("deviceIdentifier", deviceIdentifier);
        headers.put("version", version);
        headers.put("UncleScrooge", XMW_CHECKSUM_BYPASS);

        Map<String, Object> body = new HashMap<>();
        body.put("leadId", leadId);
        body.put("items", items);

        for (Map.Entry m : headers.entrySet()) {
            saveCartObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveCartObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response response = saveCartObj.callAPI();
        return response;
    }

    public Response saveCartWithQuantity(String leadId, String planId, int quantity, String deviceIdentifier, String version) {
        SaveCart saveCartObj = new SaveCart();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("deviceIdentifier", deviceIdentifier);
        headers.put("version", version);
        headers.put("UncleScrooge", XMW_CHECKSUM_BYPASS);

        Map<String, Object> body = new HashMap<>();
        body.put("leadId", leadId);
        body.put("planId", planId);
        body.put("quantity", quantity);

        for (Map.Entry m : headers.entrySet()) {
            saveCartObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveCartObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response response = saveCartObj.callAPI();
        return response;
    }

    public Response saveCartWithAddOns(String leadId, String planId, int quantity, Map<String, Object> addOns, String deviceIdentifier, String version) {
        SaveCart saveCartObj = new SaveCart();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("deviceIdentifier", deviceIdentifier);
        headers.put("version", version);
        headers.put("UncleScrooge", XMW_CHECKSUM_BYPASS);

        Map<String, Object> body = new HashMap<>();
        body.put("leadId", leadId);
        body.put("planId", planId);
        body.put("quantity", quantity);
        body.put("addOns", addOns);

        for (Map.Entry m : headers.entrySet()) {
            saveCartObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveCartObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response response = saveCartObj.callAPI();
        return response;
    }
} 