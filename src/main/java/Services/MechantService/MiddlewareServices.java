package Services.MechantService;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import Request.MerchantService.LeadInfo.LeadInfo;
import Request.MerchantService.oe.panel.v1.editLead.BusinessDetails;
import Request.MerchantService.v1.Enterprise.*;
import Request.MerchantService.v1.fseDiy.*;
import Services.DIYProfileUpdate.addChannelCallback;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;

import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import io.restassured.RestAssured;
import io.restassured.specification.RequestSpecification;
import io.restassured.response.Response;

import Request.CIF.BrandActiveInKyb;
import Request.CIF.FetchVettedKeys;
import Request.CIF.TncAccept;
import Request.CIF.TncUser;
import Request.CommonOnboardingEDC.AddonShopInsurance;
import Request.CommonOnboardingEDC.addBank;
import Request.CommonOnboardingEDC.cKYC;
import Request.CommonOnboardingEDC.cartsave;
import Request.CommonOnboardingEDC.confirmdevicePlanEDC;
import Request.CommonOnboardingEDC.createDeviceLead;
import Request.CommonOnboardingEDC.createMCOLead;
import Request.CommonOnboardingEDC.deviceAddons;
import Request.CommonOnboardingEDC.deviceSummary;
import Request.CommonOnboardingEDC.devicedetailsfetch;
import Request.CommonOnboardingEDC.fetchBank;
import Request.CommonOnboardingEDC.fetchBeauruStatus;
import Request.CommonOnboardingEDC.fetchMCOLead;
import Request.CommonOnboardingEDC.fetchmerchantdevicedetails;
import Request.CommonOnboardingEDC.fetchqnadevices;
import Request.CommonOnboardingEDC.getBank;
import Request.CommonOnboardingEDC.getBusiness;
import Request.CommonOnboardingEDC.getLeadStatus;
import Request.CommonOnboardingEDC.getMerchantMIDStatus;
import Request.CommonOnboardingEDC.orderSave;
import Request.CommonOnboardingEDC.partialSaveCall;
import Request.CommonOnboardingEDC.sendOTPLead;
import Request.EDCAssetReplace.DeviceFetchTnc;
import Request.EDCAssetReplace.Devicepayment;
import Request.EDCAssetReplace.Edcassetreplacecreatelead;
import Request.EDCAssetReplace.ScanassetQR;
import Request.EDCAssetReplace.ValidateotpEDC;
import Request.EDCDeviceUpgradeV2.DeviceUpgradevalidateEDCQr;
import Request.EOS.AddDealer;
import Request.EOS.DeleteDealer;
import Request.EOS.GetBrandDealer;
import Request.EOS.GetBrandList;
import Request.MerchantService.Fastag.FastagDropDown;
import Request.MerchantService.Fastag.FetchFastagTnC;
import Request.MerchantService.Fastag.GetFastagTags;
import Request.MerchantService.Fastag.ValidateTag;
import Request.MerchantService.Request.MerchantService.v1.SoundBOX.IotDevice.SoundBOXDeviceType;
import Request.MerchantService.RevisitOrganized.GetmidReferenceNumber;
import Request.MerchantService.loan.lead.dynamicTNC;
import Request.MerchantService.loan.lead.dynamicTnC_LOS;
import Request.MerchantService.loan.lead.saveTncAndSubmitApplication_LOS;
import Request.MerchantService.loan.lead.submitApplication;
import Request.MerchantService.oe.V1.Payment.Order.NotifyCallback;
import Request.MerchantService.oe.panel.v1.agent;
import Request.MerchantService.oe.panel.v1.userInfo;
import Request.MerchantService.oe.panel.v1.ReallocateAgent.ReallocateAgent;
import Request.MerchantService.oe.panel.v1.document.multiPart.UploadDocumentFlow;
import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.fileProcess.fileUpload_BusinessStatus;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.oe.panel.v1.referenceData.getAllResources.GetAllResources;
import Request.MerchantService.panel.v1.business.CompanyOnboardPanel;
import Request.MerchantService.panel.v1.business.FetchPanPanel;
import Request.MerchantService.panel.v1.business.PanelPennyDrop;
import Request.MerchantService.panel.v1.business.doc.status.GetBusinessDoc;
import Request.MerchantService.panel.v1.diy.businessprofile.FetchBusinessProfilePanel;
import Request.MerchantService.panel.v1.solution.lead.CreateSolutionPanel;
import Request.MerchantService.panel.v1.solution.lead.GetLeadDetails;
import Request.MerchantService.panel.v1.solution.lead.UpdateSolutionPanel;
import Request.MerchantService.panel.v2.solution.VerifyDocument;
import Request.MerchantService.url.SoundBoxBindUrl;
import Request.MerchantService.v1.AssistedMerchant;
import Request.MerchantService.v1.Commission;
import Request.MerchantService.v1.FetchSubCategoryODS;
import Request.MerchantService.v1.GetCompany;
import Request.MerchantService.v1.GstExemptionList;
import Request.MerchantService.v1.PostCompany;
import Request.MerchantService.v1.TokenXMV;
import Request.MerchantService.v1.UpgradePlans;
import Request.MerchantService.v1.Cart.PinCode;
import Request.MerchantService.v1.EDC.CreateUnmapEdcLead;
import Request.MerchantService.v1.EDC.Edc;
import Request.MerchantService.v1.EDC.EdcPost;
import Request.MerchantService.v1.EDC.EdcPostAndroidWithPOS;
import Request.MerchantService.v1.EDC.FetchEdcUpgradePlans;
import Request.MerchantService.v1.EDC.FetchPayment;
import Request.MerchantService.v1.EDC.Fetchdeploymentdetails;
import Request.MerchantService.v1.EDC.ResendOtp;
import Request.MerchantService.v1.EDC.unmapEDCMachine;
import Request.MerchantService.v1.EDC.validateEDCQr;
import Request.MerchantService.v1.GamePind.FetchQuestion;
import Request.MerchantService.v1.GamePind.StartTest;
import Request.MerchantService.v1.GamePind.SubmitQuestion;
import Request.MerchantService.v1.MerchantCommonOnboard.AdditionalDetailsFetchScreen;
import Request.MerchantService.v1.MerchantCommonOnboard.AudioDetails;
import Request.MerchantService.v1.MerchantCommonOnboard.FetchScreenDetails;
import Request.MerchantService.v1.NCMC.GetNcmcProducts;
import Request.MerchantService.v1.NCMC.fetch_payment_status;
import Request.MerchantService.v1.NCMC.generate_qr_ncmc;
import Request.MerchantService.v1.NCMC.lead_state;
import Request.MerchantService.v1.NCMC.ncmcsendotp;
import Request.MerchantService.v1.NCMC.ncmcvalidateotp;
import Request.MerchantService.v1.NCMC.submitKyc;
import Request.MerchantService.v1.POS.FetchPosPlans;
import Request.MerchantService.v1.Payments.OrderFullfillment;
import Request.MerchantService.v1.Payments.leads.status.FetchLeadMposDIY;
import Request.MerchantService.v1.QnA.FetchQnA;
import Request.MerchantService.v1.Resources.GetallResources;
import Request.MerchantService.v1.Resources.LanguagePreference;
import Request.MerchantService.v1.Resources.GetMappedDataByType.UnmapEDCReplaceReasonsWithClaimAMC;
import Request.MerchantService.v1.Resources.GetMappedDataByType.UnmapEDCReturnReasonsWithClaimAMC;
import Request.MerchantService.v1.Resources.GetMappedDataByType.UnmapEDCUpgradeReasons;
import Request.MerchantService.v1.Resources.Values.BrandAssociation;
import Request.MerchantService.v1.Resources.Values.StoreCategory;
import Request.MerchantService.v1.Resources.Values.UnmapEDCReplaceReasons;
import Request.MerchantService.v1.Resources.Values.UnmapEDCReturnReasons;
// import org.apache.log4j.Logger;
import Request.MerchantService.v1.Revisit.Fetchquestions;
import Request.MerchantService.v1.Revisit.GetMidRevisit;
import Request.MerchantService.v1.Revisit.LoanIntentfetchDocumentDetails;
import Request.MerchantService.v1.Revisit.MPAaddress;
import Request.MerchantService.v1.Revisit.RevisitGetKybValidDocs;
import Request.MerchantService.v1.Revisit.RevisitOrganised;
import Request.MerchantService.v1.Revisit.RevisitSubmit;
import Request.MerchantService.v1.Revisit.RevisitSubmitDetails;
import Request.MerchantService.v1.Revisit.RevisitUnOrganised;
import Request.MerchantService.v1.Revisit.RevisiteBeatDetails;
import Request.MerchantService.v1.Revisit.RevistLoancapture;
import Request.MerchantService.v1.Revisit.SendOtpRevisit;
import Request.MerchantService.v1.Revisit.Submitquestions;
import Request.MerchantService.v1.Revisit.ValidateOtpRevisit;
import Request.MerchantService.v1.Revisit.createLeadRevisit;
import Request.MerchantService.v1.bankcp.BankChannelLeadCreation;
import Request.MerchantService.v1.brandEmi.getAllBrands.GetAllBrands;
import Request.MerchantService.v1.brandEmi.getAllBrands.dealer.validate.ValidateDealer;
import Request.MerchantService.v1.kyc.ivr.LeadRequest;
import Request.MerchantService.v1.leadManagement.RegisterLead;
import Request.MerchantService.v1.merchant.fetchAllTerminal;
import Request.MerchantService.v1.panel.leadManagement.registerLead.RegisterLeadPanel;
import Request.MerchantService.v1.profile.Update;
import Request.MerchantService.v1.profile.update.AddAddressPGProfileUpdate;
import Request.MerchantService.v1.profile.update.AddPan;
import Request.MerchantService.v1.profile.update.CreateVendor;
import Request.MerchantService.v1.profile.update.bank_fetchChild;
import Request.MerchantService.v1.profile.update.editBank;
import Request.MerchantService.v1.profile.update.fetchBankUpdate;
import Request.MerchantService.v1.profile.update.Instrument.CombinedLeadStatus;
import Request.MerchantService.v1.profile.update.doc.StatusDoc;
import Request.MerchantService.v1.profile.update.lead.Status;
import Request.MerchantService.v1.sdMerchant.AcceptTermsAndConditionsEdcDIY;
import Request.MerchantService.v1.sdMerchant.AdditionalDetails;
import Request.MerchantService.v1.sdMerchant.AdditionalDetails_UPM_V2;
import Request.MerchantService.v1.sdMerchant.ApplicationStatus;
import Request.MerchantService.v1.sdMerchant.Business;
import Request.MerchantService.v1.sdMerchant.CreateAccount;
import Request.MerchantService.v1.sdMerchant.CreateLeadDIYCashAtPos;
import Request.MerchantService.v1.sdMerchant.CreateLeadMposDIY;
import Request.MerchantService.v1.sdMerchant.CreateLead_UPM_V2;
import Request.MerchantService.v1.sdMerchant.Lead_create;
import Request.MerchantService.v1.sdMerchant.Lead_fetch;
import Request.MerchantService.v1.sdMerchant.UpdateBankDetails;
import Request.MerchantService.v1.sdMerchant.UpdateIdentity;
import Request.MerchantService.v1.sdMerchant.ValidateBankDetails;
import Request.MerchantService.v1.sdMerchant.addAddressPL_LOS;
import Request.MerchantService.v1.sdMerchant.bank;
import Request.MerchantService.v1.sdMerchant.createLeadPL_LOS;
import Request.MerchantService.v1.sdMerchant.createLead_LOS;
import Request.MerchantService.v1.sdMerchant.fetchDynamicTnc;
import Request.MerchantService.v1.sdMerchant.fetchLead_LOS;
import Request.MerchantService.v1.sdMerchant.saveDynamicTnc_posAgent;
import Request.MerchantService.v1.sdMerchant.sendOtp_posAgent;
import Request.MerchantService.v1.sdMerchant.updateLeadAddressPL_LOS;
import Request.MerchantService.v1.sdMerchant.updateLeadAddress_LOS;
import Request.MerchantService.v1.sdMerchant.updateLeadPL_LOS;
import Request.MerchantService.v1.sdMerchant.updateLead_LOS;
import Request.MerchantService.v1.sdMerchant.additionalDetails.SaveRefreeCodeWhatsapp;
import Request.MerchantService.v1.sdMerchant.lead.CreateLeadLending;
import Request.MerchantService.v1.sdMerchant.lead.FetchLeadBrandEMIDIY;
import Request.MerchantService.v1.sdMerchant.lms.merchant.wrapper.triggerotp;
import Request.MerchantService.v1.sdMerchant.lms.merchant.wrapper.validateotp;
import Request.MerchantService.v1.sdMerchant.savebank.SaveBankUPM;
import Request.MerchantService.v1.sfcrm.Lead;
import Request.MerchantService.v1.upgradeMid.doc.status.FetchDocumentDIYCashAtPos;
import Request.MerchantService.v1.upgradeMid.doc.status.UploadDocCashAtPosDIY;
import Request.MerchantService.v1.upgradeMid.lead.CreateLeadIndigoOnboarding;
import Request.MerchantService.v1.upgradeMid.lead.CreateLeadIndigoReseller;
import Request.MerchantService.v1.upgradeMid.lead.CreateLeadOfflineToOnline;
import Request.MerchantService.v1.upgradeMid.lead.CreateLeadPaymentLink;
import Request.MerchantService.v1.upgradeMid.lead.CreateLeadReseller;
import Request.MerchantService.v1.upgradeMid.lead.FetchAllBusiness;
import Request.MerchantService.v1.upgradeMid.lead.FetchLeadData;
import Request.MerchantService.v1.upgradeMid.lead.FetchLeadStatus;
import Request.MerchantService.v1.upgradeMid.lead.PaymentsLeadStatus;
import Request.MerchantService.v1.upgradeMid.lead.UpdateAdditionalDetailsAadhaarRegistered;
import Request.MerchantService.v1.upgradeMid.lead.UpdateAdditionalDetailsOwner;
import Request.MerchantService.v1.upgradeMid.lead.UpdateAddtionalDetails;
import Request.MerchantService.v1.upgradeMid.lead.UpdateAddtionalDetailsAddressIndigoOnboarding;
import Request.MerchantService.v1.upgradeMid.lead.UpdateAddtionalDetailsAdhaarIndigoOnboarding;
import Request.MerchantService.v1.upgradeMid.lead.UpdateAddtionalDetailsGSTIndigoOnboarding;
import Request.MerchantService.v1.upgradeMid.lead.UpdateBankDetailOnline;
import Request.MerchantService.v1.upgradeMid.lead.UpdateBusiness;
import Request.MerchantService.v1.upgradeMid.lead.UpdateBusinessIndividual;
import Request.MerchantService.v1.upgradeMid.lead.UpdateTncOnline;
import Request.MerchantService.v1.upgradeMid.lead.ValidateBankDetailsOnline;
import Request.MerchantService.v1.upgradeMid.tnc.MerchantAgreementTnCAccept;
import Request.MerchantService.v2.Banks;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v2.ICICI.CreateLeadWithGST;
import Request.MerchantService.v2.ICICI.CreateLeadWithOutGSTIN;
import Request.MerchantService.v2.ICICI.FetchMID;
import Request.MerchantService.v2.ICICI.FetchStatus;
import Request.MerchantService.v2.doc.upload.status.fetchDocStatus_posInsurance;
import Request.MerchantService.v2.doc.upload.status.uploadDoc_posAgent;
import Request.MerchantService.v2.edc.diyUpgradePlans.CheckEligibilityDIY;
import Request.MerchantService.v2.edc.plans.fetchPlanEdcDIY;
import Request.MerchantService.v2.edc.validateOrder.validateOrderEdcDIY;
import Request.MerchantService.v2.ekyc.biometric.SoundboxTnc;
import Request.MerchantService.v2.fastag.CreateFastag;
import Request.MerchantService.v2.fastag.FetchFastagPayment;
import Request.MerchantService.v2.fastag.FetchIssuance;
import Request.MerchantService.v2.fastag.FetchVirtualCart;
import Request.MerchantService.v2.fastag.ValidateFastag;
import Request.MerchantService.v2.kyc.ivr.SendIvrRequest;
import Request.MerchantService.v2.lead.v2FetchDynamicTnc;
import Request.MerchantService.v2.lead.v2SaveDynamicTnc;
import Request.MerchantService.v2.lending.checkBreStatus_LOS;
import Request.MerchantService.v2.lending.getBreStatus_LOS;
import Request.MerchantService.v2.lending.getKycStatus;
import Request.MerchantService.v2.lending.getKycStatus_LOS;
import Request.MerchantService.v2.lending.dataUpdate.dataUpdate_LOS;
import Request.MerchantService.v2.profile.update.commissiontncs.GetMerchantAgreementTnC;
import Request.MerchantService.v2.upgradeMid.doc.FetchDocumentStatus;
import Request.MerchantService.v2.upgradeMid.doc.uploadCancelledCheque;
import Request.MerchantService.v3.BusinessProfile;
import Request.MerchantService.v3.GetBusinessv3;
import Request.MerchantService.v3.GetDocStatus;
import Request.MerchantService.v3.GetMerchant;
import Request.MerchantService.v3.MID;
import Request.MerchantService.v3.MidMobile;
import Request.MerchantService.v3.PennyDrop;
import Request.MerchantService.v3.SendOtp;
import Request.MerchantService.v3.SoundboxLeadCount;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v3.SubmitMerchant;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v3.Pg.PgCallBack;
import Request.MerchantService.v3.QRMapping.QRMappingFetchEligibleBanks;
import Request.MerchantService.v3.QRMapping.QRMappingFetchPosDetails;
import Request.MerchantService.v3.QRMapping.QRMappingGetStaticPref;
import Request.MerchantService.v3.QRMapping.QrMappingSendOtp;
import Request.MerchantService.v3.QRMapping.QrMappingValidateOTPLeadCreation;
import Request.MerchantService.v3.kyc.ivr.ValidateIvrRequest;
import Request.MerchantService.v3.merchant.fetch.FetchV3Merchant;
import Request.MerchantService.v3.merchant.mid.Mobile;
import Request.MerchantService.v4.FetchDynamicDocs;
import Request.MerchantService.v4.PennyDropMultiNameMatch;
import Request.MerchantService.v5.callback.CallBackv5_posAgent;
import Request.MerchantService.v5.callback.alternateNoCallBack_LOS;
import Request.MerchantService.v5.callback.callBackSaveOTP;
import Request.MerchantService.v5.callback.emailCallBackPL_LOS;
import Request.MerchantService.v5.callback.lmsCallBack_LOS;
import Request.MerchantService.v5.callback.lmsCallBack_loanTap;
import Request.MerchantService.v5.callback.otpCallback_LOS;
import Request.MerchantServices.v1.sdMerchant.UpdateIndentityDetails.SaveIdentityAdhaarDetails;
import Request.MerchantServices.v1.sdMerchant.UpdateIndentityDetails.SaveIdentityGSTINDetails;
import Request.MerchantServices.v1.sdMerchant.UpdateIndentityDetails.SaveIdentityPANDetails;
import Request.MerchantServices.v1.sdMerchant.ValidateBankDetails.ValidateBank;
import Request.MerchantServices.v1.sdMerchant.business.SaveBusiness;
import Request.PG.ActiveTerminalInPG;
import Request.PG.CreateTerminalInPG;
import Request.PSA_Agent_Onboarding.BankDetailsAgentOnboarding;
import Request.PSA_Agent_Onboarding.PSAFetchScreenDetails;
import Request.PSA_Agent_Onboarding.PostAdhaardata;
import Request.PSA_Agent_Onboarding.PostBankDetailsAgentOnboarding;
import Request.Shopinsurance.Embeddedshopinsurance;
import Request.Shopinsurance.ShopInsuranceConfirmPolicy;
import Request.SoundBox.AddOnDetails;
import Request.SoundBox.BindDeviceByOTP;
import Request.SoundBox.CardSB_Create_Lead_And_Check_Existing_Subscription;
import Request.SoundBox.CreateDeviceUnbindLead;
import Request.SoundBox.CreateSoundboxReplacementLead;
import Request.SoundBox.CreatenewSBLead;
import Request.SoundBox.DIYFetchAllocated;
import Request.SoundBox.DIYSBQRUpdateAWBNumber;
import Request.SoundBox.DIYUpdateDeviceDetails;
import Request.SoundBox.DIYUpdateDeviceID;
import Request.SoundBox.DIYV2SBUpdateDeviceAndQR;
import Request.SoundBox.FetchDeviceQuestions;
import Request.SoundBox.FetchDevicedIot;
import Request.SoundBox.FetchNextScreenSB;
import Request.SoundBox.FetchPOSIdSB;
import Request.SoundBox.FetchPlans;
import Request.SoundBox.FetchQrDetails;
import Request.SoundBox.FetchSoundBoxPayment;
import Request.SoundBox.FetchSoundboxReplacementLead;
import Request.SoundBox.Fetchtnc;
import Request.SoundBox.GetMerchantBasicDetails;
import Request.SoundBox.GetMerchantid;
import Request.SoundBox.InitiateH2HLead;
import Request.SoundBox.InitiateNssLead;
import Request.SoundBox.Posid;
import Request.SoundBox.QrValidate;
import Request.SoundBox.SBCheckBTStatus;
import Request.SoundBox.SBDIYV2OrderDelivery;
import Request.SoundBox.SBDIYValidateMID;
import Request.SoundBox.SBDIYViewMerchantSpecificLead;
import Request.SoundBox.SBFetchLeadDetails;
import Request.SoundBox.SBFetchSelectedPlan;
import Request.SoundBox.SBFetchYoutubeLink;
import Request.SoundBox.SBMerchantDetails;
import Request.SoundBox.SBPinCode;
import Request.SoundBox.SBPlanUpgradeCreateLead;
import Request.SoundBox.SBPreValidatePaymentQRNew;
import Request.SoundBox.SBShopInsuranceUpdateLead;
import Request.SoundBox.SBValidateNewChargerInBindFlow;
import Request.SoundBox.SbFulfilmentBypass;
import Request.SoundBox.SbInsuranceEligibility;
import Request.SoundBox.SendOTPV1;
import Request.SoundBox.SentOTPSB;
import Request.SoundBox.SimActivation;
import Request.SoundBox.SimReplacementValidateOTP;
import Request.SoundBox.SoundBoxChoosePlan;
import Request.SoundBox.SoundBoxFetchPlan;
import Request.SoundBox.SoundBoxLead;
import Request.SoundBox.SoundboxByPassQnA;
import Request.SoundBox.SoundboxVASLeadCreation;
import Request.SoundBox.UpdateAssetAnswers;
import Request.SoundBox.ValidateAndSaveAssets;
import Request.SoundBox.ValidateBeatDetails;
import Request.SoundBox.ValidateOldDevice;
import Request.SoundBox.Validatedevice;
import Request.SoundBox.ValidateotpSB;
import Request.SoundBox.fetchtncotp;
import Request.SoundBox.soundbox;
import Request.SoundBox.AgentLogin.AgentLoginApi2;
import Request.SoundBox.AgentLogin.DeviceIdentifier;
import Request.SoundBox.AgentLogin.SBADDONUpdateLead;
import Request.SoundBox.AgentLogin.SBPayment;
import Request.SoundBox.AgentLogin.SBPlanSummary;
import Request.SoundBox.AgentLogin.SBUPIAutopay;
import Request.SoundBox.AgentLogin.SBUpdateLead;
import Request.SoundBox.AgentLogin.Save_Tnc;
import Request.SoundBox.AgentLogin.SbPinCode;
import Request.SoundBox.AgentLogin.Sound_Box_Validate_OTP;
import Request.ats.ACLFetchPermission;
import Request.ats.AssetCountForAllStates;
import Request.ats.AssetCountForAssigned;
import Request.ats.AssetCountForAvailable;
import Request.ats.AssetCountForDeployed;
import Request.ats.AssetCountForLost;
import Request.ats.AssetCountForPending_Acknowledgement;
import Request.ats.AssetCountForPending_Assign;
import Request.ats.AssetCountForReturned;
import Request.ats.AssetCountForUnmapped;
import Request.ats.AtsAssetUpdate;
import Request.ats.AtsAssetValidate;
import Request.ats.AtsCatagory;
import Request.ats.AtsCheckRequestStatus;
import Request.ats.AtsDownloadFile;
import Request.ats.AtsRequestDownload;
import Request.ats.AtsSkuGroup;
import Request.ats.CheckRemainingCapacity;
import Request.ats.CreateATSSupplier;
import Request.ats.CreateSKU;
import Request.ats.CreateSKUWithoutSubpart;
import Request.ats.CreateSubSku;
import Request.ats.DownloadBarcodeDetails;
import Request.ats.EmployeeProfile;
import Request.ats.FetchBarcodeDetails;
import Request.ats.FetchBarcodeDetailsCanAssign;
import Request.ats.FetchSKUDetails;
import Request.ats.FetchSkuGroups;
import Request.ats.GenerateBarcode;
import Request.ats.GenerateChildBarcode;
import Request.ats.GenerateQRCode;
import Request.ats.GetSKUListOfParams;
import Request.ats.ListOfAssetDetailsForAssigned;
import Request.ats.ListOfAssetDetailsForAvailable;
import Request.ats.ListOfAssetDetailsForDeployed;
import Request.ats.ListOfAssetDetailsForLost;
import Request.ats.ListOfAssetDetailsForPending_Acknowledgement;
import Request.ats.ListOfAssetDetailsForPending_Assign;
import Request.ats.ListOfAssetDetailsForReturned;
import Request.ats.ListOfAssetDetailsForUnmapped;
import Request.ats.OnboardBarcode;
import Request.ats.OnboardBarcodeInSeries;
import Request.ats.OnboardChildBarcode;
import Request.ats.SearchATSCatagory;
import Request.ats.SearchATSSupplier;
import Request.ats.SearchSKU;
import Request.ats.Subpartassetupdate;
import Request.ats.UpdateSKU;
import Request.ats.UserGetDetails;
import Request.ats.V2FetchBarcodeDetails;
import Request.ats.VerifiedQRCode;
import Request.chlbwp.subscription.v3.add.ChannelAdd;
import Request.managesim.FetchDevicesimdetails;
import Request.managesim.SimupdateLead;
import Services.oAuth.oAuthServices;
import Request.MerchantService.v1.Revisit.GetDeviceIds;

public class MiddlewareServices extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(MiddlewareServices.class);

    oAuthServices oAuthServicesObject = new oAuthServices();


    public Response v3SentOtp(SendOtp callV3Otp, String entityType, String solutionType, String session_token, String version, String mobile, String userType) {
        //SendOtp callV3Otp = new SendOtp();

        callV3Otp.addParameter("entityType", entityType);
        callV3Otp.addParameter("solutionType", solutionType);

        callV3Otp.getProperties().setProperty("mobile", mobile);
        callV3Otp.getProperties().setProperty("userType", userType);
        callV3Otp.setHeader("Host", "goldengate-staging6.paytm.com");
//        callV3Otp.setHeader("Host", "goldengate-staging6.paytm.com");

        callV3Otp.getProperties().setProperty("call", "false");
        callV3Otp.setHeader("X-SRC", "GGClient");
        callV3Otp.setHeader("version", version);
        callV3Otp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        callV3Otp.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        callV3Otp.setHeader("session_token", session_token);
        callV3Otp.setHeader("isDeviceRooted", "false");
//        callV3Otp.setHeader("deviceIdentifier", "Xiaomi-M2004J19C-131535fc93929702");
        callV3Otp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");

        callV3Otp.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        callV3Otp.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response OtpResponse = callV3Otp.callAPI();
        OtpResponse.prettyPrint();

        return OtpResponse;

    }

    public Response v3ValidateOtp(ValidateOtp callV3validateOtp, String entityType, String solutionType, String session_token, String version, String mobile, String userType, String state, String OTP) throws JSchException, IOException {


        callV3validateOtp.addParameter("entityType", entityType);
        callV3validateOtp.addParameter("solutionType", solutionType);

        callV3validateOtp.setHeader("Content-Type", "application/json");
        callV3validateOtp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        callV3validateOtp.setHeader("session_token", session_token);
        callV3validateOtp.setHeader("version", version);
        callV3validateOtp.setHeader("appLanguage", "en");
        callV3validateOtp.setHeader("deviceName", "CPH1859");
        callV3validateOtp.setHeader("client", "androidapp");
        callV3validateOtp.setHeader("imei", "***************");
        callV3validateOtp.setHeader("deviceManufacturer", "OPPO");
        callV3validateOtp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        callV3validateOtp.setHeader("Content-Type", "application/json; charset=UTF-8");
        callV3validateOtp.setHeader("latitude", "28.5913173");
        callV3validateOtp.setHeader("longitude", "77.3189828");


        callV3validateOtp.getProperties().setProperty("mobile", mobile);
        callV3validateOtp.getProperties().setProperty("userType", userType);
        callV3validateOtp.getProperties().setProperty("otp", OTP);
        callV3validateOtp.getProperties().setProperty("state", state);


        Response ValidateOtp = callV3validateOtp.callAPI();

        System.out.println(callV3validateOtp.toString());


        return ValidateOtp;
    }

    public Response v3GetMerchant(GetMerchant callv3GetMerchant, String entityType, String solutionType, String session_token, String version) {


        callv3GetMerchant.addParameter("entityType", entityType);
        callv3GetMerchant.addParameter("solutionType", solutionType);

        callv3GetMerchant.setHeader("Content-Type", "application/json");
        callv3GetMerchant.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        callv3GetMerchant.setHeader("session_token", session_token);
        callv3GetMerchant.setHeader("version", version);
        callv3GetMerchant.setHeader("appLanguage", "en");
        callv3GetMerchant.setHeader("deviceName", "CPH1859");
        callv3GetMerchant.setHeader("client", "androidapp");
        callv3GetMerchant.setHeader("imei", "***************");
        callv3GetMerchant.setHeader("deviceManufacturer", "OPPO");
        callv3GetMerchant.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        callv3GetMerchant.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response GetMerchant = callv3GetMerchant.callAPI();

        System.out.println(callv3GetMerchant.toString());

        return GetMerchant;
    }

    public Response postSdMerchantBusiness(Business businessResponseObject, Map<String, String> queryParams, Map<String, String> body, String session_token, String version) {

        businessResponseObject.setHeader("Content-Type", "application/json");
        businessResponseObject.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        businessResponseObject.setHeader("session_token", session_token);
        businessResponseObject.setHeader("version", version);
        businessResponseObject.setHeader("latitude", "28.4542");
        businessResponseObject.setHeader("longitude", "72.1242");
        businessResponseObject.setHeader("ipAddress", "************");
        businessResponseObject.setHeader("androidId", "AashitAndroid");

        for (Map.Entry m : queryParams.entrySet()) {
            businessResponseObject.addParameter(m.getKey().toString(), m.getValue().toString());


        }
        for (Map.Entry m : body.entrySet()) {
            businessResponseObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());


        }

        Response businessResponse = businessResponseObject.callAPI();

        return businessResponse;
    }


    public Response postSdMerchantValidateBank(ValidateBankDetails validateBankDetailsResponseObject, Map<String, String> queryParams, Map<String, String> body, String session_token, String version) {


        validateBankDetailsResponseObject.setHeader("Content-Type", "application/json");
        validateBankDetailsResponseObject.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        validateBankDetailsResponseObject.setHeader("session_token", session_token);
        validateBankDetailsResponseObject.setHeader("version", version);
        validateBankDetailsResponseObject.setHeader("latitude", "28.4542");
        validateBankDetailsResponseObject.setHeader("longitude", "72.1242");
        validateBankDetailsResponseObject.setHeader("ipAddress", "************");
        validateBankDetailsResponseObject.setHeader("androidId", "AashitAndroid");


        for (Map.Entry m : queryParams.entrySet()) {
            validateBankDetailsResponseObject.addParameter(m.getKey().toString(), m.getValue().toString());


        }
        for (Map.Entry m : body.entrySet()) {
            validateBankDetailsResponseObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());


        }

        // System.out.println("object is : "+validateBankDetailsResponseObject);
        Response validateBankDetailsResponse = validateBankDetailsResponseObject.callAPI();
        // System.out.println("response is : "+validateBankDetailsResponse);
        return validateBankDetailsResponse;
    }

    public Response v3getDocStatus(GetDocStatus getDocStat, String Custid, String Entity, String Solution, String session_token, String version) {
        getDocStat.getRequest().urlEncodingEnabled(false);
        getDocStat.addParameter("merchantCustId", Custid);
        getDocStat.addParameter("entityType", Entity);
        getDocStat.addParameter("solutionType", Solution);

        getDocStat.setHeader("Content-Type", "application/json");
        getDocStat.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getDocStat.setHeader("session_token", session_token);
        getDocStat.setHeader("version", version);
        getDocStat.setHeader("appLanguage", "en");
        getDocStat.setHeader("deviceName", "CPH1859");
        getDocStat.setHeader("client", "androidapp");
        getDocStat.setHeader("imei", "***************");
        getDocStat.setHeader("deviceManufacturer", "OPPO");
        getDocStat.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getDocStat.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v3getDocStatusResponse = getDocStat.callAPI();

        return v3getDocStatusResponse;
    }


    public Response v2GetTnC(TnC getTnC, String entity, String solution, String version, String session_token) {

        getTnC.addParameter("entityType", entity);
        getTnC.addParameter("solutionType", solution);

        getTnC.setHeader("Content-Type", "application/json");
        getTnC.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getTnC.setHeader("session_token", session_token);
        getTnC.setHeader("version", version);
        getTnC.setHeader("appLanguage", "en");
        getTnC.setHeader("deviceName", "CPH1859");
        getTnC.setHeader("client", "androidapp");
        getTnC.setHeader("imei", "***************");
        getTnC.setHeader("deviceManufacturer", "OPPO");
        getTnC.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getTnC.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v2TnCResponse = getTnC.callAPI();

        return v2TnCResponse;
    }

    public Response v1PinCode(PinCode getPinCode, String version, String session_token) {

        getPinCode.setHeader("Content-Type", "application/json");
        getPinCode.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getPinCode.setHeader("session_token", session_token);
        getPinCode.setHeader("version", version);
        getPinCode.setHeader("appLanguage", "en");
        getPinCode.setHeader("deviceName", "CPH1859");
        getPinCode.setHeader("client", "androidapp");
        getPinCode.setHeader("imei", "***************");
        getPinCode.setHeader("deviceManufacturer", "OPPO");
        getPinCode.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getPinCode.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1PinCodeResponse = getPinCode.callAPI();

        return v1PinCodeResponse;

    }

    public Response v1LanguagePrefrence(LanguagePreference getLanguage, String version, String session_token) {
        getLanguage.setHeader("Content-Type", "application/json");
        getLanguage.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getLanguage.setHeader("session_token", session_token);
        getLanguage.setHeader("version", version);
        getLanguage.setHeader("appLanguage", "en");
        getLanguage.setHeader("deviceName", "CPH1859");
        getLanguage.setHeader("client", "androidapp");
        getLanguage.setHeader("imei", "***************");
        getLanguage.setHeader("deviceManufacturer", "OPPO");
        getLanguage.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getLanguage.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1getLanguageResponse = getLanguage.callAPI();

        return v1getLanguageResponse;
    }

    public Response v2Banks(Banks getBank, String version, String session_token) {

        getBank.setHeader("Content-Type", "application/json");
        getBank.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getBank.setHeader("session_token", session_token);
        getBank.setHeader("version", version);
        getBank.setHeader("appLanguage", "en");
        getBank.setHeader("deviceName", "CPH1859");
        getBank.setHeader("client", "androidapp");
        getBank.setHeader("imei", "***************");
        getBank.setHeader("deviceManufacturer", "OPPO");
        getBank.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getBank.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1getBanks = getBank.callAPI();

        return v1getBanks;
    }

    public Response v3PennyDrop(PennyDrop doPennyDrop, String version, String KycName, String BankName, String custIdPenny, String AgentToken) {
        doPennyDrop.getRequest().urlEncodingEnabled(false);
        doPennyDrop.addParameter("kycName", KycName);
        doPennyDrop.addParameter("bankName", BankName);
        doPennyDrop.addParameter("merchantCustId", custIdPenny);


        doPennyDrop.setHeader("Content-Type", "application/json");
        doPennyDrop.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        doPennyDrop.setHeader("session_token", AgentToken);
        doPennyDrop.setHeader("version", version);
        doPennyDrop.setHeader("appLanguage", "en");
        doPennyDrop.setHeader("deviceName", "CPH1859");
        doPennyDrop.setHeader("client", "androidapp");
        doPennyDrop.setHeader("imei", "***************");
        doPennyDrop.setHeader("deviceManufacturer", "OPPO");
        doPennyDrop.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        doPennyDrop.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v3PennyDrop = doPennyDrop.callAPI();

        return v3PennyDrop;
    }

    public Response v3SubmitMerchant(SubmitMerchant v3SubMerch, String mobile, String bankac, String ifscSub, String version, String entity, String solution, String AgentToken) {
        v3SubMerch.addParameter("entityType", entity);
        v3SubMerch.addParameter("solutionType", solution);

        v3SubMerch.getProperties().setProperty("mobileNumberOfCustomer", mobile);
        v3SubMerch.getProperties().setProperty("bankAccountNumber", bankac);
        v3SubMerch.getProperties().setProperty("ifsc", ifscSub);


        v3SubMerch.setHeader("Content-Type", "application/json");
        v3SubMerch.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v3SubMerch.setHeader("session_token", AgentToken);
        v3SubMerch.setHeader("version", version);
        v3SubMerch.setHeader("appLanguage", "en");
        v3SubMerch.setHeader("deviceName", "CPH1859");
        v3SubMerch.setHeader("client", "androidapp");
        v3SubMerch.setHeader("imei", "***************");
        v3SubMerch.setHeader("deviceManufacturer", "OPPO");
        v3SubMerch.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v3SubMerch.setHeader("Content-Type", "application/json; charset=UTF-8");
        v3SubMerch.setHeader("latitude", "28.5913173");
        v3SubMerch.setHeader("longitude", "77.3189828");

        Response v3SubmitMerchResponse = v3SubMerch.callAPI();
        return v3SubmitMerchResponse;
    }


    public Response v2kycivrSendIvrRequest(SendIvrRequest SendIvrRequestRequestObject) {
        SendIvrRequest SendIvrRequestObject = new SendIvrRequest("");

        // Adding Query Paramas
        SendIvrRequestObject.addParameter("resendRequest", SendIvrRequestRequestObject.getResendRequest());

        //Adding Headers
        SendIvrRequestObject.setHeader("X-SRC", SendIvrRequestRequestObject.getXSRC());
        SendIvrRequestObject.setHeader("latitude", SendIvrRequestRequestObject.getLatitude());
        SendIvrRequestObject.setHeader("channel", SendIvrRequestRequestObject.getChannel());
        SendIvrRequestObject.setHeader("deviceName", SendIvrRequestRequestObject.getDeviceName());
        SendIvrRequestObject.setHeader("version", SendIvrRequestRequestObject.getVersion());
        SendIvrRequestObject.setHeader("session_token", SendIvrRequestRequestObject.getSession_token());
        SendIvrRequestObject.setHeader("deviceIdentifier", SendIvrRequestRequestObject.getDeviceIdentifier());
        SendIvrRequestObject.setHeader("osVersion", SendIvrRequestRequestObject.getOsVersion());
        SendIvrRequestObject.setHeader("client", SendIvrRequestRequestObject.getClient());
        SendIvrRequestObject.setHeader("imei", SendIvrRequestRequestObject.getImei());
        SendIvrRequestObject.setHeader("UncleScrooge", SendIvrRequestRequestObject.getUncleScrooge());
        SendIvrRequestObject.setHeader("deviceManufacturer", SendIvrRequestRequestObject.getDeviceManufacturer());
        SendIvrRequestObject.setHeader("appLanguage", SendIvrRequestRequestObject.getAppLanguage());
        SendIvrRequestObject.setHeader("longitude", SendIvrRequestRequestObject.getLongitude());
        SendIvrRequestObject.setHeader("Content-Type", SendIvrRequestRequestObject.getContentType());

        //Add Body Params
        SendIvrRequestObject.getProperties().setProperty("mobileNumber", SendIvrRequestRequestObject.getMobileNumber());
        SendIvrRequestObject.getProperties().setProperty("action", SendIvrRequestRequestObject.getAction());


        Response SendIvrRequestResponse = SendIvrRequestObject.callAPI();

        return SendIvrRequestResponse;
    }

    public Response v1kycivrLeadRequest(LeadRequest LeadRequestRequestObject) {
        LeadRequest LeadRequestObject = new LeadRequest();

        //Adding Headers
        LeadRequestObject.setHeader("Content-Type", LeadRequestRequestObject.getContentType());
        LeadRequestObject.setHeader("x-jwt-token", LeadRequestRequestObject.getXjwttoken());

        //Add Body Params
        LeadRequestObject.getProperties().setProperty("mobile", LeadRequestRequestObject.getMobile());
        LeadRequestObject.getProperties().setProperty("action", LeadRequestRequestObject.getAction());
        LeadRequestObject.getProperties().setProperty("biometricConsent", LeadRequestRequestObject.getBiometricConsent());
        LeadRequestObject.getProperties().setProperty("timestamp", LeadRequestRequestObject.getTimestamp());

        Response LeadRequestResponse = LeadRequestObject.callAPI();

        return LeadRequestResponse;
    }

    public Response v1ProfileUpdateLeadStatus(Status statusResponseobject, String solution, String entityType, String solutionSubType, String Content_Type, String version, String UncleScrooge, String session_token, String mid) {
        statusResponseobject.addParameter("solution", solution);
        statusResponseobject.addParameter("entityType", entityType);
        statusResponseobject.addParameter("solutionSubType", solutionSubType);
        statusResponseobject.setHeader("Content-Type", Content_Type);
        statusResponseobject.setHeader("version", version);
        statusResponseobject.setHeader("UncleScrooge", UncleScrooge);
        statusResponseobject.setHeader("session_token", session_token);
        statusResponseobject.getProperties().setProperty("mid", mid);
        Response statusResponse = statusResponseobject.callAPI();
        return statusResponse;


    }

    public Response v1ProfileUpdateLeadStatus(Status statusResponseobject, String solution, String entityType, String solutionSubType, String Content_Type, String UncleScrooge, String session_token, String mid) {
        statusResponseobject.addParameter("solution", solution);
        statusResponseobject.addParameter("entityType", entityType);
        statusResponseobject.addParameter("solutionSubType", solutionSubType);
        statusResponseobject.setHeader("Content-Type", Content_Type);
        //  statusResponseobject.setHeader("version", version);
        statusResponseobject.setHeader("UncleScrooge", UncleScrooge);
        statusResponseobject.setHeader("session_token", session_token);
        statusResponseobject.getProperties().setProperty("mid", mid);
        Response statusResponse = statusResponseobject.callAPI();
        return statusResponse;


    }


    public Response v1ProfileUpdate(Update updateRequestObject, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            updateRequestObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            updateRequestObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            updateRequestObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response UpdateRequestResponse = updateRequestObject.callAPI();

        return UpdateRequestResponse;

    }


    public Response v1ProfileUpdateForBankDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        Update updateRequestObject = new Update(P.TESTDATA.get("updateBankDetails"));

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            updateRequestObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            updateRequestObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            updateRequestObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response UpdateRequestResponse = updateRequestObject.callAPI();

        return UpdateRequestResponse;

    }

    public Response v3kycivrValidateIvrRequest(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        ValidateIvrRequest ValidateIvrRequestObject = new ValidateIvrRequest();
        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            ValidateIvrRequestObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            ValidateIvrRequestObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            ValidateIvrRequestObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response SendIvrRequestResponse = ValidateIvrRequestObject.callAPI();

        return SendIvrRequestResponse;
    }


    public Response triggerOtp(triggerotp otp, String sso_token) {
        otp.setHeader("sso_token", sso_token);
        Response otpResponse = otp.callAPI();
        return otpResponse;
    }


    public Response validateOtp(validateotp validateotp, String sso_token, String Content_Type, String leadId, String state, String otp) {
        validateotp.setHeader("sso_token", sso_token);
        validateotp.setHeader("Content_Type", Content_Type);
        //   validateotp.setHeader("leadId",leadId);
        validateotp.getProperties().setProperty("lead_id", leadId);
        validateotp.getProperties().setProperty("state", state);
        validateotp.getProperties().setProperty("otp", otp);


        Response otpResponse = validateotp.callAPI();
        return otpResponse;
    }

    public Response callBackSaveOTP(callBackSaveOTP callbackotp, String leadId, String solution, String Authorization, String cache_Control, String Content_Type, String PostmanToken, String channel, String custId) {
        //  callbackotp.getProperties().setProperty("leadId",leadId);
        callbackotp.addParameter("leadId", leadId);
        callbackotp.addParameter("solution", solution);


        callbackotp.setHeader("Authorization", Authorization);
        callbackotp.setHeader("cache-control", cache_Control);
        callbackotp.setHeader("Content-Type", Content_Type);
        callbackotp.setHeader("Postmen-Token", PostmanToken);
        callbackotp.setHeader("channel", channel);
        callbackotp.setHeader("custId", custId);
        //   callbackotp.getProperties().setProperty("custId",custId);

        Response otpCallbackResponse = callbackotp.callAPI();
        return otpCallbackResponse;

    }

    public Response getKycStatus(getKycStatus kycStatus, String entityType, String solutionTypeLevel2, String solutionTypeLevel3, String solution, String channel, String session_token, String Content_Type) {
        kycStatus.addParameter("entityType", entityType);
        kycStatus.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        kycStatus.addParameter("solutionTypeLevel3", solutionTypeLevel3);
        kycStatus.addParameter("solution", solution);
        kycStatus.addParameter("channel", channel);


        kycStatus.setHeader("session_token", session_token);
        kycStatus.setHeader("Content_Type", Content_Type);

        Response kycResponse = kycStatus.callAPI();
        return kycResponse;

    }

    public Response getBankDetails(bank getBank, String entityType, String solutionTypeLevel2, String solutionTypeLevel3, String solution, String channel, String session_token, String Content_Type, String bankAccountNumber, String ifsc) {
        getBank.addParameter("entityType", entityType);
        getBank.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        getBank.addParameter("solutionTypeLevel3", solutionTypeLevel3);
        getBank.addParameter("solution", solution);
        getBank.addParameter("channel", channel);


        getBank.setHeader("session_token", session_token);
        getBank.setHeader("Content_Type", Content_Type);

        getBank.getProperties().setProperty("bankAccountNumber", bankAccountNumber);
        getBank.getProperties().setProperty("ifsc", ifsc);

        Response bankResponse = getBank.callAPI();
        return bankResponse;

    }

    public Response fetchBankUpdate(fetchBankUpdate bankUpdateStatus, String entityType, String solutionSubType, String solution, String session_token, String Content_Type, String Postman_Token, String UncleScrooge, String mid, String solutionTypeLevel3) {


        bankUpdateStatus.addParameter("entityType", entityType);
        bankUpdateStatus.addParameter("solutionSubType", solutionSubType);
        bankUpdateStatus.addParameter("solutionTypeLevel3", solutionTypeLevel3);
        bankUpdateStatus.addParameter("solution", solution);


        bankUpdateStatus.setHeader("session_token", session_token);
        bankUpdateStatus.setHeader("Content_Type", Content_Type);
        bankUpdateStatus.setHeader("Postman_Token", Postman_Token);
        bankUpdateStatus.setHeader("UncleScrooge", UncleScrooge);
        bankUpdateStatus.getProperties().setProperty("mid", mid);


        Response bankUpdateResponse = bankUpdateStatus.callAPI();
        return bankUpdateResponse;

    }


    public Response fetchSavedBank(bank_fetchChild fetchSavedBankStatus, String entityType, String solution, String session_token) {


        fetchSavedBankStatus.addParameter("entityType", entityType);
        fetchSavedBankStatus.addParameter("solution", solution);
        fetchSavedBankStatus.setHeader("session_token", session_token);
        Response savedBankResponse = fetchSavedBankStatus.callAPI();
        return savedBankResponse;

    }


    public Response editBank(editBank editBankStatus, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {


        for (Map.Entry m : queryParams.entrySet()) {
            editBankStatus.addParameter(m.getKey().toString(), m.getValue().toString());


        }

        for (Map.Entry m : headers.entrySet()) {
            editBankStatus.setHeader(m.getKey().toString(), m.getValue().toString());


        }

        for (Map.Entry m : body.entrySet()) {
            editBankStatus.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());


        }

        Response editBankResponse = editBankStatus.callAPI();
        return editBankResponse;

    }


    public Response uploadCancelCheque(uploadCancelledCheque cancelCheque, Map<String, String> queryParams, Map<String, String> headers, File file) {

        cancelCheque.getRequest().urlEncodingEnabled(false);

        for (Map.Entry m : queryParams.entrySet()) {
            cancelCheque.addParameter(m.getKey().toString(), m.getValue().toString());


        }
        for (Map.Entry m : headers.entrySet()) {
            cancelCheque.setHeader(m.getKey().toString(), m.getValue().toString());


        }


        cancelCheque.addMultipartFormData("file", file, "image/jpeg");

        cancelCheque.setHeader("Content-Type", "multipart/form-data");


        Response chequeResponse = cancelCheque.callAPI();
        return chequeResponse;

    }


    public Response v1EditLeadOE(EditLead PGProfileLeadAction_Status, String action, String phonenumber, String ssoid, String Cookie, String Content_Type) {
        PGProfileLeadAction_Status.addParameter("action", action);


        PGProfileLeadAction_Status.setHeader("phonenumber", phonenumber);
        //  PGProfileLeadAction_Status.setHeader("Referer",Referer);
        PGProfileLeadAction_Status.setHeader("ssoid", ssoid);
        PGProfileLeadAction_Status.setHeader("Cookie", Cookie);
        PGProfileLeadAction_Status.setHeader("Content-Type", Content_Type);

        Response PGLeadactionStatus = PGProfileLeadAction_Status.callAPI();
        return PGLeadactionStatus;
    }

    /*
     * public Response fetchBREStatus(getBreStatus breStatus, String entityType,
     * String channel, String solutionSubTypeLevel2, String solutionTypeLevel3,
     * String solution, String session_token, String Content_Type) {
     *
     *
     * breStatus.addParameter("entityType", entityType);
     * breStatus.addParameter("channel", channel);
     * breStatus.addParameter("solutionSubTypeLevel2", solutionSubTypeLevel2);
     * breStatus.addParameter("solutionTypeLevel3", solutionTypeLevel3);
     * breStatus.addParameter("solution", solution);
     *
     *
     * breStatus.setHeader("session_token", session_token);
     * breStatus.setHeader("Content_Type", Content_Type);
     *
     *
     * Response BreResponse = breStatus.callAPI(); return BreResponse;
     *
     * }
     */

    public Response generateTNC(dynamicTNC generateTNC, String entityType, String profile, String session_token) {

        generateTNC.setHeader("session_token", session_token);
        generateTNC.setHeader("entityType", entityType);
        generateTNC.setHeader("profile", profile);


        Response generateTNCResponse = generateTNC.callAPI();
        return generateTNCResponse;

    }


    public Response saveTNC(submitApplication saveTNC, String entityType, String profile, String session_token, String Content_Type) {

        saveTNC.setHeader("session_token", session_token);
        saveTNC.setHeader("entityType", entityType);
        saveTNC.setHeader("profile", profile);
        saveTNC.setHeader("Content_Type", Content_Type);


        Response saveTNCResponse = saveTNC.callAPI();
        return saveTNCResponse;

    }


    public Response fileUpload_BusinessStatus(fileUpload_BusinessStatus file_businessStatus, String Content_Type, String Cookie, File file, String process) {
        //    file_businessStatus.addParameter("process",process);
        file_businessStatus.getRequest().urlEncodingEnabled(false);
        file_businessStatus.setHeader("Cookie", Cookie);
        file_businessStatus.addMultipartFormData("file", file, "text/csv");
        file_businessStatus.addMultipartFormData("process", process, "*/*");
        file_businessStatus.setHeader("Content-Type", Content_Type);
        Response fileUploadResponse = file_businessStatus.callAPI();
        return fileUploadResponse;

    }


    public Response postSdMerchantUpdateBank(UpdateBankDetails updateBankDetailsResponseObject, Map<String, String> queryParams, Map<String, String> body, String session_token, String version) {


        //Adding Headers
        updateBankDetailsResponseObject.setHeader("Content-Type", "application/json");
        updateBankDetailsResponseObject.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        updateBankDetailsResponseObject.setHeader("session_token", session_token);
        updateBankDetailsResponseObject.setHeader("version", version);
        updateBankDetailsResponseObject.setHeader("latitude", "28.4542");
        updateBankDetailsResponseObject.setHeader("longitude", "72.1242");
        updateBankDetailsResponseObject.setHeader("ipAddress", "************");
        updateBankDetailsResponseObject.setHeader("androidId", "AashitAndroid");


        for (Map.Entry m : queryParams.entrySet()) {
            updateBankDetailsResponseObject.addParameter(m.getKey().toString(), m.getValue().toString());


        }
        for (Map.Entry m : body.entrySet()) {
            updateBankDetailsResponseObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());


        }


        Response updateBankDetailsResponse = updateBankDetailsResponseObject.callAPI();
        return updateBankDetailsResponse;
    }

    public Response V3SubmitDocs(SubmitDocs v3DocObj, String session_token, String version, Map<String, String> queryParams) {
        v3DocObj.getRequest().urlEncodingEnabled(false);

        for (Map.Entry m : queryParams.entrySet()) {
            v3DocObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v3DocObj.setHeader("Content-Type", "multipart/form-data");
        v3DocObj.setHeader("Content-Type", "application/json");
        v3DocObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v3DocObj.setHeader("session_token", session_token);
        v3DocObj.setHeader("version", version);
        v3DocObj.setHeader("appLanguage", "en");
        v3DocObj.setHeader("deviceName", "CPH1859");
        v3DocObj.setHeader("client", "androidapp");
        v3DocObj.setHeader("imei", "***************");
        v3DocObj.setHeader("deviceManufacturer", "OPPO");
        v3DocObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v3DocObj.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v3SubmitDocResp = v3DocObj.callAPI();

        return v3SubmitDocResp;
    }

    public Response v1RegisterLead(RegisterLead v1reg, String solutionType, String Authorization) {
        v1reg.addParameter("solutionType", solutionType);

        v1reg.setHeader("Authorization", Authorization);

        Response v1RegLeadResp = v1reg.callAPI();
        return v1RegLeadResp;
    }


    public Response FetchLead(Lead_fetch fetchLeadStatus, Map<String, String> queryParams, Map<String, String> headers) {


        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchLeadStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response fetchLeadRequestResponse = fetchLeadStatus.callAPI();

        return fetchLeadRequestResponse;


    }

    public Response CreateLead(Lead_create createLead, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        //    createLead_loanTap createLead_loanTap=new createLead_loanTap();


        for (Map.Entry m : queryParams.entrySet()) {
            createLead.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            createLead.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            createLead.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = createLead.callAPI();

        return createLeadRequestResponse;


    }
    public Response CreateLeadMosDIY(CreateLeadMposDIY CreateLeadMposDIYObj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        for (Map.Entry m : queryParams.entrySet()) {
            CreateLeadMposDIYObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            CreateLeadMposDIYObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            CreateLeadMposDIYObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadMposResponse = CreateLeadMposDIYObj.callAPI();

        return createLeadMposResponse;


    }

    public Response AdditionalDetails(AdditionalDetails additionalDetails, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {


        for (Map.Entry m : queryParams.entrySet()) {
            additionalDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            additionalDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            additionalDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response loanOfferObjectResponse = additionalDetails.callAPI();

        return loanOfferObjectResponse;


    }


    public Response lmsCallback_loanTap(lmsCallBack_loanTap lmsCallbackObject, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {


        for (Map.Entry m : queryParams.entrySet()) {
            lmsCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            lmsCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            lmsCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response lmsCallBackObjectResponse = lmsCallbackObject.callAPI();

        return lmsCallBackObjectResponse;


    }


    public Response v3GetBusiness(GetBusinessv3 v3getBus, String session_token, String version, String merchCustid) {
        v3getBus.getRequest().urlEncodingEnabled(false);
        v3getBus.addParameter("merchantCustId", merchCustid);

        v3getBus.setHeader("Content-Type", "application/json");
        v3getBus.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v3getBus.setHeader("session_token", session_token);
        v3getBus.setHeader("version", version);
        v3getBus.setHeader("appLanguage", "en");
        v3getBus.setHeader("deviceName", "CPH1859");
        v3getBus.setHeader("client", "androidapp");
        v3getBus.setHeader("imei", "***************");
        v3getBus.setHeader("deviceManufacturer", "OPPO");
        v3getBus.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v3getBus.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v3GBusResp = v3getBus.callAPI();
        return v3GBusResp;
    }

    public Response v1GetCompany(GetCompany v1getComp, String session_token, String version, String merchCustid, String entity, String solution, String Mobile) {
        v1getComp.addParameter("custId", merchCustid);
        v1getComp.addParameter("entityType", entity);
        v1getComp.addParameter("solution", solution);
        v1getComp.addParameter("mobile", Mobile);


        v1getComp.setHeader("Content-Type", "application/json");
        v1getComp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1getComp.setHeader("session_token", session_token);
        v1getComp.setHeader("version", version);
        v1getComp.setHeader("appLanguage", "en");
        v1getComp.setHeader("deviceName", "CPH1859");
        v1getComp.setHeader("client", "androidapp");
        v1getComp.setHeader("imei", "***************");
        v1getComp.setHeader("deviceManufacturer", "OPPO");
        v1getComp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1getComp.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1GetCompaResp = v1getComp.callAPI();
        return v1GetCompaResp;
    }

    public Response v1PostCompany(PostCompany v1PostComp, String session_token, String version, String merchCustid, String entity, String solution, String Mobile, String name, String pancard) {
        v1PostComp.getRequest().urlEncodingEnabled(false);
        v1PostComp.addParameter("custId", merchCustid);
        v1PostComp.addParameter("proprietor", "true");
        v1PostComp.addParameter("entityType", entity);
        v1PostComp.addParameter("solution", solution);
        v1PostComp.addParameter("mobile", Mobile);
        v1PostComp.addParameter("name", name);
        v1PostComp.addParameter("pancard", pancard);

        v1PostComp.setHeader("Content-Type", "application/json");
        v1PostComp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1PostComp.setHeader("session_token", session_token);
        v1PostComp.setHeader("version", version);
        v1PostComp.setHeader("appLanguage", "en");
        v1PostComp.setHeader("deviceName", "CPH1859");
        v1PostComp.setHeader("client", "androidapp");
        v1PostComp.setHeader("imei", "***************");
        v1PostComp.setHeader("deviceManufacturer", "OPPO");
        v1PostComp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1PostComp.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1PostCompaResp = v1PostComp.callAPI();
        return v1PostCompaResp;
    }

    public Response v3BusinessProfile(BusinessProfile v3BusPro, String custId, String mobile, String kybBusinessId, String session_token, String version) {
        v3BusPro.addParameter("merchantCustId", custId);
        v3BusPro.addParameter("mobile", mobile);
        v3BusPro.addParameter("kybBusinessId", kybBusinessId);

        v3BusPro.setHeader("Content-Type", "application/json");
        v3BusPro.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v3BusPro.setHeader("session_token", session_token);
        v3BusPro.setHeader("version", version);
        v3BusPro.setHeader("appLanguage", "en");
        v3BusPro.setHeader("deviceName", "CPH1859");
        v3BusPro.setHeader("client", "androidapp");
        v3BusPro.setHeader("imei", "***************");
        v3BusPro.setHeader("deviceManufacturer", "OPPO");
        v3BusPro.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v3BusPro.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v3BusProRes = v3BusPro.callAPI();
        return v3BusProRes;
    }

    public Response v1GstExemption(GstExemptionList v1gstObj, String version, String session_token) {
        v1gstObj.setHeader("Content-Type", "application/json");
        v1gstObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1gstObj.setHeader("session_token", session_token);
        v1gstObj.setHeader("version", version);
        v1gstObj.setHeader("appLanguage", "en");
        v1gstObj.setHeader("deviceName", "CPH1859");
        v1gstObj.setHeader("client", "androidapp");
        v1gstObj.setHeader("imei", "***************");
        v1gstObj.setHeader("deviceManufacturer", "OPPO");
        v1gstObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1gstObj.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1GstExemRes = v1gstObj.callAPI();
        return v1GstExemRes;
    }

    public Response v4PennyDropMultiName(PennyDropMultiNameMatch doPennyDrop, String version, String names, String BankName, String custIdPenny, String AgentToken) {
        doPennyDrop.getRequest().urlEncodingEnabled(false);
        doPennyDrop.addParameter("names", names);
        doPennyDrop.addParameter("bankName", BankName);
        doPennyDrop.addParameter("merchantCustId", custIdPenny);


        doPennyDrop.setHeader("Content-Type", "application/json");
        doPennyDrop.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        doPennyDrop.setHeader("session_token", AgentToken);
        doPennyDrop.setHeader("version", version);
        doPennyDrop.setHeader("appLanguage", "en");
        doPennyDrop.setHeader("deviceName", "CPH1859");
        doPennyDrop.setHeader("client", "androidapp");
        doPennyDrop.setHeader("imei", "***************");
        doPennyDrop.setHeader("deviceManufacturer", "OPPO");
        doPennyDrop.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        doPennyDrop.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v4PennyDropResp = doPennyDrop.callAPI();

        return v4PennyDropResp;
    }

    public Response v1Token(String username, String password) {
        TokenXMV tokenXMVRequestObject = new TokenXMV();
        String oAuthToken = oAuthServicesObject.getoAuthSessionTokenCustID(username, password, true);
        //Add Body Params
        tokenXMVRequestObject.getProperties().setProperty("oAuthToken", oAuthToken);
        tokenXMVRequestObject.getProperties().setProperty("ipAddress", "::ffff:127.0.0.1");

        Response responseObject = tokenXMVRequestObject.callAPI();

        return responseObject;
    }

    public Response v1FetchLeadPanel(FetchLead v1FetchLeadObj, String Token) {
        v1FetchLeadObj.setHeader("Cookie", Token);
        v1FetchLeadObj.setHeader("Accept", "application/json, text/plain, */*");

        Response v1FetcLeadResp = v1FetchLeadObj.callAPI();

        return v1FetcLeadResp;
    }

    public Response v3FetchMID(MID v3FetchMID, String session_token, String version, String CustId, String ReqType) {
        v3FetchMID.addParameter("merchantCustId", CustId);
        v3FetchMID.addParameter("requestType", ReqType);

        v3FetchMID.setHeader("Content-Type", "application/json");
        v3FetchMID.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v3FetchMID.setHeader("session_token", session_token);
        v3FetchMID.setHeader("version", version);
        v3FetchMID.setHeader("appLanguage", "en");
        v3FetchMID.setHeader("deviceName", "CPH1859");
        v3FetchMID.setHeader("client", "androidapp");
        v3FetchMID.setHeader("imei", "***************");
        v3FetchMID.setHeader("deviceManufacturer", "OPPO");
        v3FetchMID.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v3FetchMID.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v3fetchMidResp = v3FetchMID.callAPI();

        return v3fetchMidResp;
    }

    public Response v1FetchUpgradePlans(UpgradePlans v1FetchPlans, String session_token, String version, Map<String, String> QueryParam) {

        v1FetchPlans.getRequest().urlEncodingEnabled(false);
        for (Map.Entry m : QueryParam.entrySet()) {
            v1FetchPlans.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1FetchPlans.setHeader("Content-Type", "application/json");
        v1FetchPlans.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1FetchPlans.setHeader("session_token", session_token);
        v1FetchPlans.setHeader("version", version);
        v1FetchPlans.setHeader("appLanguage", "en");
        v1FetchPlans.setHeader("deviceName", "CPH1859");
        v1FetchPlans.setHeader("client", "androidapp");
        v1FetchPlans.setHeader("imei", "***************");
        v1FetchPlans.setHeader("deviceManufacturer", "OPPO");
        v1FetchPlans.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1FetchPlans.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response v1FetchPlanResp = v1FetchPlans.callAPI();

        return v1FetchPlanResp;
    }

    public Response v4FetchDynamicDocs(FetchDynamicDocs v4FetchDoc, String session_token, String version, Map<String, String> queryParams) {
        v4FetchDoc.getRequest().urlEncodingEnabled(false);
        for (Map.Entry m : queryParams.entrySet()) {
            v4FetchDoc.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v4FetchDoc.setHeader("Content-Type", "application/json");
        v4FetchDoc.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v4FetchDoc.setHeader("session_token", session_token);
        v4FetchDoc.setHeader("version", version);
        v4FetchDoc.setHeader("appLanguage", "en");
        v4FetchDoc.setHeader("deviceName", "CPH1859");
        v4FetchDoc.setHeader("client", "androidapp");
        v4FetchDoc.setHeader("imei", "***************");
        v4FetchDoc.setHeader("deviceManufacturer", "OPPO");
        v4FetchDoc.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v4FetchDoc.setHeader("Content-Type", "application/json; charset=UTF-8");
        v4FetchDoc.setHeader("ipAddress", "************");
        v4FetchDoc.setHeader("isLocationMocked", "false");
        v4FetchDoc.setHeader("latitude", "28.5913173");
        v4FetchDoc.setHeader("longitude", "77.3189828");

        Response v4FetchDocsResp = v4FetchDoc.callAPI();

        return v4FetchDocsResp;
    }


    public Response fetchDocStatus_posInsurance(fetchDocStatus_posInsurance fetchDocStatusObject_posInsurance, Map<String, String> queryParams, Map<String, String> headers) {


        for (Map.Entry m : queryParams.entrySet()) {
            fetchDocStatusObject_posInsurance.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchDocStatusObject_posInsurance.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        //Hit API to get Response
        Response fetchDocStatusObjectResponse_posInsurance = fetchDocStatusObject_posInsurance.callAPI();

        return fetchDocStatusObjectResponse_posInsurance;


    }

    public Response fetchLeadMposDIY(FetchLeadMposDIY FetchLeadMposDIYObj, Map<String, String> queryParams, Map<String, String> headers) {


        for (Map.Entry m : queryParams.entrySet()) {
            FetchLeadMposDIYObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            FetchLeadMposDIYObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        //Hit API to get Response
        Response FetchLeadMposDIYObjResp = FetchLeadMposDIYObj.callAPI();

        return FetchLeadMposDIYObjResp;


    }


    public Response uploadDocStatus_posInsurance(uploadDoc_posAgent uploadDoc_posAgentObject, String entityType, String channel, String solutionType, String solutionLeadId, String docProvided, String docType, String pageNo, File file, String session_token) {
        //Adding Headers
        uploadDoc_posAgentObject.getRequest().urlEncodingEnabled(false);
        uploadDoc_posAgentObject.addParameter("entityType", entityType);
        uploadDoc_posAgentObject.addParameter("channel", channel);
        uploadDoc_posAgentObject.addParameter("solutionType", solutionType);
        uploadDoc_posAgentObject.addParameter("pageNo", pageNo);
        uploadDoc_posAgentObject.addParameter("solutionLeadId", solutionLeadId);
        uploadDoc_posAgentObject.addParameter("docProvided", docProvided);
        uploadDoc_posAgentObject.addParameter("docType", docType);
        uploadDoc_posAgentObject.addMultipartFormData("file", file, "application/pdf");


        uploadDoc_posAgentObject.setHeader("session_token", session_token);
        uploadDoc_posAgentObject.setHeader("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");


       /* for(Map.Entry m:headers.entrySet())
        {
            uploadDoc_posAgentObject.setHeader(m.getKey().toString(),m.getValue().toString());
        }
        for(Map.Entry m:queryParams.entrySet())
        {
            uploadDoc_posAgentObject.addParameter(m.getKey().toString(),m.getValue().toString());
        }

        uploadDoc_posAgentObject.addMultipartFormData("file",file,"application/pdf");*/

        //Hit API to get Response
        Response uploadDocStatusObjectResponse_posInsurance = uploadDoc_posAgentObject.callAPI();

        return uploadDocStatusObjectResponse_posInsurance;


    }

    public Response fetchDynamicTnc_posInsurance(fetchDynamicTnc fetchDynamicTncObject, Map<String, String> queryParams, Map<String, String> headers) {


        for (Map.Entry m : queryParams.entrySet()) {
            fetchDynamicTncObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchDynamicTncObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        //Hit API to get Response
        Response fetchDynamicTncResponseObject = fetchDynamicTncObject.callAPI();

        return fetchDynamicTncResponseObject;


    }

    public Response sendOtp_posInsurance(sendOtp_posAgent sendOtp_posAgentObject, Map<String, String> headers) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            sendOtp_posAgentObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        //Hit API to get Response
        Response sendOtp_posAgentResponseObject = sendOtp_posAgentObject.callAPI();

        return sendOtp_posAgentResponseObject;


    }

    public Response saveDynamicTnc_posInsurance(saveDynamicTnc_posAgent saveDynamicTncObject_posAgent, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        for (Map.Entry m : queryParams.entrySet()) {
            saveDynamicTncObject_posAgent.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveDynamicTncObject_posAgent.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            saveDynamicTncObject_posAgent.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response saveDynamicTncObjectResponse_posAgent = saveDynamicTncObject_posAgent.callAPI();

        return saveDynamicTncObjectResponse_posAgent;


    }

    public Response leadStatusBrandEMIDIY(Status statusObj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        for (Map.Entry m : queryParams.entrySet()) {
            statusObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            statusObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            statusObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response statusObjResp = statusObj.callAPI();

        return statusObjResp;


    }


    public Response callbackv5_posInsurance(CallBackv5_posAgent CallBackv5Object_posAgent, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {


        for (Map.Entry m : queryParams.entrySet()) {
            CallBackv5Object_posAgent.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            CallBackv5Object_posAgent.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            CallBackv5Object_posAgent.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response CallBackv5ObjectResponse_posAgent = CallBackv5Object_posAgent.callAPI();

        return CallBackv5ObjectResponse_posAgent;


    }

    public Response UpdateGSTMpos(Update updateGstMpos, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {


        for (Map.Entry m : queryParams.entrySet()) {
            updateGstMpos.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            updateGstMpos.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            updateGstMpos.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response updateGstMposObj = updateGstMpos.callAPI();

        return updateGstMposObj;


    }


    public Response ReallocateAgent(ReallocateAgent v1reallocateAgent, String XMWToken) {
        v1reallocateAgent.setHeader("cookie", XMWToken);

        Response v1reallocateAgentResp = v1reallocateAgent.callAPI();

        return v1reallocateAgentResp;
    }

    public Response FetchDocProfileUpdate(StatusDoc v1FetchDoc, Map<String, String> queryParams, Map<String, String> headers) {
        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1FetchDoc.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            v1FetchDoc.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response v1FetchDocResp = v1FetchDoc.callAPI();
        return v1FetchDocResp;
    }

    public Response v1Edc(Edc v1EdcObbj, Map<String, String> queryParams, String session_token, String version) {
        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1EdcObbj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1EdcObbj.setHeader("Content-Type", "application/json");
        v1EdcObbj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1EdcObbj.setHeader("session_token", session_token);
        v1EdcObbj.setHeader("version", version);
        v1EdcObbj.setHeader("appLanguage", "en");
        v1EdcObbj.setHeader("deviceName", "CPH1859");
        v1EdcObbj.setHeader("client", "androidapp");
        v1EdcObbj.setHeader("imei", "***************");
        v1EdcObbj.setHeader("deviceManufacturer", "OPPO");
        v1EdcObbj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1EdcObbj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1EdcObbj.setHeader("ipAddress", "************");
        v1EdcObbj.setHeader("isLocationMocked", "false");
        v1EdcObbj.setHeader("latitude", "28.5913173");
        v1EdcObbj.setHeader("longitude", "77.3189828");

        Response v1EdcPlans = v1EdcObbj.callAPI();

        return v1EdcPlans;
    }

    public Response v1UpdateEdc(EdcPost v1EdcObbj, Map<String, String> queryParams, Map<String, String> body, String session_token, String version) {
        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1EdcObbj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Body
        for (Map.Entry m : body.entrySet()) {
            v1EdcObbj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        v1EdcObbj.setHeader("Content-Type", "application/json");
        v1EdcObbj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1EdcObbj.setHeader("session_token", session_token);
        v1EdcObbj.setHeader("version", version);
        v1EdcObbj.setHeader("appLanguage", "en");
        v1EdcObbj.setHeader("deviceName", "CPH1859");
        v1EdcObbj.setHeader("client", "androidapp");
        v1EdcObbj.setHeader("imei", "***************");
        v1EdcObbj.setHeader("deviceManufacturer", "OPPO");
        v1EdcObbj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1EdcObbj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1EdcObbj.setHeader("isDeviceRooted", "false");
        v1EdcObbj.setHeader("ipAddress", DeviceIP);
        v1EdcObbj.setHeader("isLocationMocked", "false");
        v1EdcObbj.setHeader("latitude", "28.5913173");
        v1EdcObbj.setHeader("longitude", "77.3189828");
        v1EdcObbj.setHeader("osVersion", "9");

        Response v1UpdateEdc = v1EdcObbj.callAPI();

        return v1UpdateEdc;
    }

    public Response v1EdcPayment(FetchPayment v1EdcPaymentObbj, Map<String, String> queryParams, String session_token, String version) {
        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1EdcPaymentObbj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1EdcPaymentObbj.setHeader("Content-Type", "application/json");
        v1EdcPaymentObbj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1EdcPaymentObbj.setHeader("session_token", session_token);
        v1EdcPaymentObbj.setHeader("version", version);
        v1EdcPaymentObbj.setHeader("appLanguage", "en");
        v1EdcPaymentObbj.setHeader("deviceName", "CPH1859");
        v1EdcPaymentObbj.setHeader("client", "androidapp");
        v1EdcPaymentObbj.setHeader("imei", "***************");
        v1EdcPaymentObbj.setHeader("deviceManufacturer", "OPPO");
        v1EdcPaymentObbj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1EdcPaymentObbj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1EdcPaymentObbj.setHeader("ipAddress", "************");
        v1EdcPaymentObbj.setHeader("isLocationMocked", "false");
        v1EdcPaymentObbj.setHeader("latitude", "28.5913173");
        v1EdcPaymentObbj.setHeader("longitude", "77.3189828");

        Response v1EdcFetchPayment = v1EdcPaymentObbj.callAPI();

        return v1EdcFetchPayment;
    }

    public Response v1FetchQnA(FetchQnA v1FetchQnAobj, Map<String, String> queryParams, String session_token, String version) {

        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1FetchQnAobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1FetchQnAobj.setHeader("Content-Type", "application/json");
        v1FetchQnAobj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1FetchQnAobj.setHeader("session_token", session_token);
        v1FetchQnAobj.setHeader("version", version);
        v1FetchQnAobj.setHeader("appLanguage", "en");
        v1FetchQnAobj.setHeader("deviceName", "CPH1859");
        v1FetchQnAobj.setHeader("client", "androidapp");
        v1FetchQnAobj.setHeader("imei", "***************");
        v1FetchQnAobj.setHeader("deviceManufacturer", "OPPO");
        v1FetchQnAobj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1FetchQnAobj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1FetchQnAobj.setHeader("ipAddress", "************");
        v1FetchQnAobj.setHeader("isLocationMocked", "false");
        v1FetchQnAobj.setHeader("latitude", "28.5913173");
        v1FetchQnAobj.setHeader("longitude", "77.3189828");

        Response v1FetchQnAResp = v1FetchQnAobj.callAPI();

        return v1FetchQnAResp;
    }

    public Response v1ResendOtp(ResendOtp v1ResentOtpObj, Map<String, String> queryParams, Map<String, String> body, String Session_token, String version) {

        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1ResentOtpObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Body
        for (Map.Entry m : body.entrySet()) {

            v1ResentOtpObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        v1ResentOtpObj.setHeader("Content-Type", "application/json");
        v1ResentOtpObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1ResentOtpObj.setHeader("session_token", Session_token);
        v1ResentOtpObj.setHeader("version", version);
        v1ResentOtpObj.setHeader("appLanguage", "en");
        v1ResentOtpObj.setHeader("deviceName", "CPH1859");
        v1ResentOtpObj.setHeader("client", "androidapp");
        v1ResentOtpObj.setHeader("imei", "***************");
        v1ResentOtpObj.setHeader("deviceManufacturer", "OPPO");
        v1ResentOtpObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1ResentOtpObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1ResentOtpObj.setHeader("ipAddress", "************");
        v1ResentOtpObj.setHeader("isLocationMocked", "false");
        v1ResentOtpObj.setHeader("latitude", "28.5913173");
        v1ResentOtpObj.setHeader("longitude", "77.3189828");

        Response v1ResentOtpResp = v1ResentOtpObj.callAPI();

        return v1ResentOtpResp;
    }

    public Response v1FetchPosPlans(FetchPosPlans v1FetchPosObj, Map<String, String> queryParams, String Session_token, String version) {

        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1FetchPosObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1FetchPosObj.setHeader("Content-Type", "application/json");
        v1FetchPosObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1FetchPosObj.setHeader("session_token", Session_token);
        v1FetchPosObj.setHeader("version", version);
        v1FetchPosObj.setHeader("appLanguage", "en");
        v1FetchPosObj.setHeader("deviceName", "CPH1859");
        v1FetchPosObj.setHeader("client", "androidapp");
        v1FetchPosObj.setHeader("imei", "***************");
        v1FetchPosObj.setHeader("deviceManufacturer", "OPPO");
        v1FetchPosObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1FetchPosObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1FetchPosObj.setHeader("ipAddress", "************");
        v1FetchPosObj.setHeader("isLocationMocked", "false");
        v1FetchPosObj.setHeader("latitude", "28.5913173");
        v1FetchPosObj.setHeader("longitude", "77.3189828");

        Response v1FetchPosPlan = v1FetchPosObj.callAPI();

        return v1FetchPosPlan;
    }

    public Response v1FetchBrandAssociation(BrandAssociation v1FetchBrandAssociation, Map<String, String> queryParams, String Session_token, String version) {

        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1FetchBrandAssociation.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1FetchBrandAssociation.setHeader("Content-Type", "application/json");
        v1FetchBrandAssociation.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1FetchBrandAssociation.setHeader("session_token", Session_token);
        v1FetchBrandAssociation.setHeader("version", version);
        v1FetchBrandAssociation.setHeader("appLanguage", "en");
        v1FetchBrandAssociation.setHeader("deviceName", "CPH1859");
        v1FetchBrandAssociation.setHeader("client", "androidapp");
        v1FetchBrandAssociation.setHeader("imei", "***************");
        v1FetchBrandAssociation.setHeader("deviceManufacturer", "OPPO");
        v1FetchBrandAssociation.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1FetchBrandAssociation.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1FetchBrandAssociation.setHeader("ipAddress", "************");
        v1FetchBrandAssociation.setHeader("isLocationMocked", "false");
        v1FetchBrandAssociation.setHeader("latitude", "28.5913173");
        v1FetchBrandAssociation.setHeader("longitude", "77.3189828");

        Response v1FetchBrandResp = v1FetchBrandAssociation.callAPI();

        return v1FetchBrandResp;
    }

    public Response v1FetchStoreCategory(StoreCategory v1FetchStoreCategory, Map<String, String> queryParams, String Session_token, String version) {

        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1FetchStoreCategory.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1FetchStoreCategory.setHeader("Content-Type", "application/json");
        v1FetchStoreCategory.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1FetchStoreCategory.setHeader("session_token", Session_token);
        v1FetchStoreCategory.setHeader("version", version);
        v1FetchStoreCategory.setHeader("appLanguage", "en");
        v1FetchStoreCategory.setHeader("deviceName", "CPH1859");
        v1FetchStoreCategory.setHeader("client", "androidapp");
        v1FetchStoreCategory.setHeader("imei", "***************");
        v1FetchStoreCategory.setHeader("deviceManufacturer", "OPPO");
        v1FetchStoreCategory.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1FetchStoreCategory.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1FetchStoreCategory.setHeader("ipAddress", "************");
        v1FetchStoreCategory.setHeader("isLocationMocked", "false");
        v1FetchStoreCategory.setHeader("latitude", "28.5913173");
        v1FetchStoreCategory.setHeader("longitude", "77.3189828");

        Response v1FetchPosPlan = v1FetchStoreCategory.callAPI();

        return v1FetchPosPlan;
    }

    public Response FetchDocumentStatusv2(FetchDocumentStatus v2DocStatus, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            v2DocStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            v2DocStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response v2FetchDocStatus = v2DocStatus.callAPI();

        return v2FetchDocStatus;
    }

    public Response FetchApplicationStatus(ApplicationStatus fetchAppStatus, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            fetchAppStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchAppStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchApplication = fetchAppStatus.callAPI();

        return fetchApplication;
    }

    public Response GamePindStartTest(StartTest startTest, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            startTest.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            startTest.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response startTestResp = startTest.callAPI();

        return startTestResp;
    }

    public Response GamePindFetchQuestion(FetchQuestion fetchQuestion, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            fetchQuestion.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchQuestion.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchQues = fetchQuestion.callAPI();

        return fetchQues;
    }

    public Response GamePindSubmitQuestion(SubmitQuestion submitQuestion, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            submitQuestion.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitQuestion.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitQuestion.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response submitQues = submitQuestion.callAPI();

        return submitQues;
    }

    public Response OrderFullfillment(OrderFullfillment orderFull, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            orderFull.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            orderFull.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response OrderObj = orderFull.callAPI();

        return OrderObj;
    }

    public Response OrderNotify(NotifyCallback notObj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            notObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            notObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            notObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response NotyObj = notObj.callAPI();

        return NotyObj;
    }

    public Response fetchLead_LOS(fetchLead_LOS fetchLeadRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token) {
        fetchLeadRequest.addParameter("entityType", entityType);
        fetchLeadRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        fetchLeadRequest.addParameter("solution", solution);
        fetchLeadRequest.addParameter("channel", channel);
        fetchLeadRequest.setHeader("session_token", session_token);
        Response fetchLeadResponse = fetchLeadRequest.callAPI();

        return fetchLeadResponse;

    }

    public Response createLead_LOS(createLead_LOS createLeadRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token) {

        createLeadRequest.addParameter("entityType", entityType);
        createLeadRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        createLeadRequest.addParameter("solution", solution);
        createLeadRequest.addParameter("channel", channel);
        createLeadRequest.setHeader("session_token", session_token);
        Response createLeadResponse = createLeadRequest.callAPI();
        return createLeadResponse;

    }


    public Response updateLead_LOS(updateLead_LOS updateLeadRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token, String PAN) {

        updateLeadRequest.addParameter("entityType", entityType);
        updateLeadRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        updateLeadRequest.addParameter("solution", solution);
        updateLeadRequest.addParameter("channel", channel);
        updateLeadRequest.setHeader("session_token", session_token);
        updateLeadRequest.getProperties().setProperty("PAN", PAN);
        Response updateLeadResponse = updateLeadRequest.callAPI();
        return updateLeadResponse;

    }

    public Response callBackSaveOtp_LOS(otpCallback_LOS otpCallback, String leadId, String solution, String Authorization, String Content_Type, String channel, String custId) {
        otpCallback.addParameter("leadId", leadId);
        otpCallback.addParameter("solution", solution);

        otpCallback.setHeader("Authorization", Authorization);
        otpCallback.setHeader("Content-Type", Content_Type);
        otpCallback.setHeader("channel", channel);
        otpCallback.setHeader("custId", custId);
        Response otpCallbackResponse = otpCallback.callAPI();
        return otpCallbackResponse;

    }

    public Response getKycStatus_LOS(getKycStatus_LOS kycStatus, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token, String Content_Type) {
        kycStatus.addParameter("entityType", entityType);
        kycStatus.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        kycStatus.addParameter("solution", solution);
        kycStatus.addParameter("channel", channel);
        kycStatus.setHeader("session_token", session_token);
        kycStatus.setHeader("Content_Type", Content_Type);
        Response kycResponse = kycStatus.callAPI();
        return kycResponse;

    }


    public Response alternateNoOtpcallBack_LOS(alternateNoCallBack_LOS alternateNumberOtpCallback, String leadId, String solution, String Authorization, String Content_Type, String channel, String custId) {
        alternateNumberOtpCallback.addParameter("leadId", leadId);
        alternateNumberOtpCallback.addParameter("solution", solution);

        alternateNumberOtpCallback.setHeader("Authorization", Authorization);
        alternateNumberOtpCallback.setHeader("Content-Type", Content_Type);
        alternateNumberOtpCallback.setHeader("channel", channel);
        alternateNumberOtpCallback.setHeader("custId", custId);
        Response alternateNoOtpCallbackResponse = alternateNumberOtpCallback.callAPI();
        return alternateNoOtpCallbackResponse;

    }

    public Response getBreStatus_LOS(getBreStatus_LOS breStatus, String entityType, String solutionTypeLevel2, String solution, String channel, String email, String session_token, String Content_Type) {
        breStatus.addParameter("entityType", entityType);
        breStatus.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        breStatus.addParameter("solution", solution);
        breStatus.addParameter("channel", channel);
        breStatus.addParameter("email", email);
        breStatus.setHeader("session_token", session_token);
        breStatus.setHeader("Content_Type", Content_Type);
        Response breResponse = breStatus.callAPI();
        return breResponse;

    }

    public Response dataUpdateCallback_LOS(dataUpdate_LOS dataUpdateCallback, String solutionTypeLevel2, String entityType, String leadId, String solution, String Authorization, String Content_Type, String channel, String custId) {
        dataUpdateCallback.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        dataUpdateCallback.addParameter("entityType", entityType);
        dataUpdateCallback.addParameter("leadId", leadId);
        dataUpdateCallback.addParameter("solution", solution);
        dataUpdateCallback.setHeader("Authorization", Authorization);
        dataUpdateCallback.setHeader("Content-Type", Content_Type);
        dataUpdateCallback.addParameter("channel", channel);
        dataUpdateCallback.setHeader("custId", custId);
        Response dataUpdateCallbackResponse = dataUpdateCallback.callAPI();
        return dataUpdateCallbackResponse;

    }

    public Response updateLeadAddress_LOS(updateLeadAddress_LOS updateLeadAddressRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token) {

        updateLeadAddressRequest.addParameter("entityType", entityType);
        updateLeadAddressRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        updateLeadAddressRequest.addParameter("solution", solution);
        updateLeadAddressRequest.addParameter("channel", channel);
        updateLeadAddressRequest.setHeader("session_token", session_token);
        Response updateLeadAddressResponse = updateLeadAddressRequest.callAPI();
        return updateLeadAddressResponse;

    }

    public Response generateTNC_LOS(dynamicTnC_LOS dynamicTNC, String leadId, String session_token) {
        dynamicTNC.setHeader("session_token", session_token);
        dynamicTNC.addParameter("leadId", leadId);
        Response generateTNCResponse = dynamicTNC.callAPI();
        return generateTNCResponse;

    }

    public Response saveSubMitTnC_LOS(saveTncAndSubmitApplication_LOS saveSubmitTnC, String leadId, String session_token, String Content_Type, String tncName, String uniqueIdentifier, String md5) {
        saveSubmitTnC.setHeader("session_token", session_token);
        saveSubmitTnC.setHeader("Content_Type", Content_Type);
        saveSubmitTnC.addParameter("leadId", leadId);
        saveSubmitTnC.getProperties().setProperty("tncName", tncName);
        saveSubmitTnC.getProperties().setProperty("uniqueIdentifier", uniqueIdentifier);
        saveSubmitTnC.getProperties().setProperty("md5", md5);
        Response saveTNCResponse_LOS = saveSubmitTnC.callAPI();
        return saveTNCResponse_LOS;
    }

    public Response lmsCallbackAfterSubmitApplication_LOS(lmsCallBack_LOS lmsCallback, String leadId, String solution, String Authorization, String ContentType, String custId, String status) {
        lmsCallback.addParameter("leadId", leadId);
        lmsCallback.addParameter("solution", solution);
        lmsCallback.setHeader("Authorization", Authorization);
        lmsCallback.setHeader("Content-Type", ContentType);
        lmsCallback.setHeader("custId", custId);
        lmsCallback.getProperties().setProperty("status", status);
        Response lmsCallbackResponse = lmsCallback.callAPI();
        return lmsCallbackResponse;

    }

    public Response checkBreStatus_LOS(checkBreStatus_LOS breStatus, String solution, String entityType, String channel, String solutionTypeLevel2, String session_token) {
        breStatus.addParameter("solution", solution);
        breStatus.addParameter("entityType", entityType);
        breStatus.addParameter("channel", channel);
        breStatus.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        breStatus.setHeader("session_token", session_token);
        Response checkbreResponse = breStatus.callAPI();
        return checkbreResponse;

    }


    public Response createLeadPL_LOS(createLeadPL_LOS createLeadRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token) {

        createLeadRequest.addParameter("entityType", entityType);
        createLeadRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        createLeadRequest.addParameter("solution", solution);
        createLeadRequest.addParameter("channel", channel);
        createLeadRequest.setHeader("session_token", session_token);
        Response createLeadResponse = createLeadRequest.callAPI();
        return createLeadResponse;

    }


    public Response updateLeadPL_LOS(updateLeadPL_LOS updateLeadRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token, String PAN) {

        updateLeadRequest.addParameter("entityType", entityType);
        updateLeadRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        updateLeadRequest.addParameter("solution", solution);
        updateLeadRequest.addParameter("channel", channel);
        updateLeadRequest.setHeader("session_token", session_token);
        updateLeadRequest.getProperties().setProperty("PAN", PAN);
        Response updateLeadResponse = updateLeadRequest.callAPI();
        return updateLeadResponse;

    }

    public Response emailCallBackPL_LOS(emailCallBackPL_LOS otpCallback, String leadId, String solution, String Authorization, String Content_Type, String custId) {
        otpCallback.addParameter("leadId", leadId);
        otpCallback.addParameter("solution", solution);
        otpCallback.setHeader("Authorization", Authorization);
        otpCallback.setHeader("Content-Type", Content_Type);
        otpCallback.setHeader("custId", custId);
        Response otpCallbackResponse = otpCallback.callAPI();
        return otpCallbackResponse;

    }

    public Response updateLeadAdditionalDetailPL_LOS9(updateLeadAddressPL_LOS updateLeadRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String Content_Type, String session_token) {

        updateLeadRequest.addParameter("entityType", entityType);
        updateLeadRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        updateLeadRequest.addParameter("solution", solution);
        updateLeadRequest.addParameter("channel", channel);
        updateLeadRequest.setHeader("session_token", session_token);
        updateLeadRequest.setHeader("Content_Type", Content_Type);
        Response updateLeadResponse = updateLeadRequest.callAPI();
        return updateLeadResponse;

    }


    public Response checkBTStatus(SBCheckBTStatus obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        obj.getRequest().urlEncodingEnabled(false);
        return obj.callAPI();

    }


    public Response v1SoundBoxCreateLead(soundbox v1soundboxObj, Map<String, String> body, String session_token, String version) {

        for (Map.Entry m : body.entrySet()) {
            v1soundboxObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        v1soundboxObj.setHeader("Content-Type", "application/json");
        v1soundboxObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1soundboxObj.setHeader("session_token", session_token);
        v1soundboxObj.setHeader("version", version);
        v1soundboxObj.setHeader("appLanguage", "en");
        v1soundboxObj.setHeader("deviceName", "CPH1859");
        v1soundboxObj.setHeader("client", "androidapp");
        v1soundboxObj.setHeader("imei", "***************");
        v1soundboxObj.setHeader("deviceManufacturer", "OPPO");
        v1soundboxObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1soundboxObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1soundboxObj.setHeader("isDeviceRooted", "false");
        v1soundboxObj.setHeader("ipAddress", DeviceIP);
        v1soundboxObj.setHeader("isLocationMocked", "false");
        v1soundboxObj.setHeader("latitude", "28.5913173");
        v1soundboxObj.setHeader("longitude", "77.3189828");
        v1soundboxObj.setHeader("osVersion", "9");

        Response v1soundboxCreateLead = v1soundboxObj.callAPI();

        return v1soundboxCreateLead;
    }

    public Response v1SoundBoxFetchPlan(SoundBoxFetchPlan v1soundboxfetchplanObj, String session_token, String version) {


        v1soundboxfetchplanObj.setHeader("Content-Type", "application/json");
        v1soundboxfetchplanObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1soundboxfetchplanObj.setHeader("session_token", session_token);
        v1soundboxfetchplanObj.setHeader("version", version);
        v1soundboxfetchplanObj.setHeader("appLanguage", "en");
        v1soundboxfetchplanObj.setHeader("deviceName", "CPH1859");
        v1soundboxfetchplanObj.setHeader("client", "androidapp");
        v1soundboxfetchplanObj.setHeader("imei", "***************");
        v1soundboxfetchplanObj.setHeader("deviceManufacturer", "OPPO");
        v1soundboxfetchplanObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1soundboxfetchplanObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1soundboxfetchplanObj.setHeader("isDeviceRooted", "false");
        v1soundboxfetchplanObj.setHeader("ipAddress", DeviceIP);
        v1soundboxfetchplanObj.setHeader("isLocationMocked", "false");
        v1soundboxfetchplanObj.setHeader("latitude", "28.5913173");
        v1soundboxfetchplanObj.setHeader("longitude", "77.3189828");
        v1soundboxfetchplanObj.setHeader("osVersion", "9");

        Response v1soundboxFetchPlan = v1soundboxfetchplanObj.callAPI();

        return v1soundboxFetchPlan;
    }

    public Response v1SoundBoxChoosePlan(SoundBoxChoosePlan v1soundboxchooseplanObj, Map<String, String> body, String session_token, String version) {

        for (Map.Entry m : body.entrySet()) {
            v1soundboxchooseplanObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        v1soundboxchooseplanObj.setHeader("Content-Type", "application/json");
        v1soundboxchooseplanObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1soundboxchooseplanObj.setHeader("session_token", session_token);
        v1soundboxchooseplanObj.setHeader("version", version);
        v1soundboxchooseplanObj.setHeader("appLanguage", "en");
        v1soundboxchooseplanObj.setHeader("deviceName", "CPH1859");
        v1soundboxchooseplanObj.setHeader("client", "androidapp");
        v1soundboxchooseplanObj.setHeader("imei", "***************");
        v1soundboxchooseplanObj.setHeader("deviceManufacturer", "OPPO");
        v1soundboxchooseplanObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1soundboxchooseplanObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1soundboxchooseplanObj.setHeader("isDeviceRooted", "false");
        v1soundboxchooseplanObj.setHeader("ipAddress", DeviceIP);
        v1soundboxchooseplanObj.setHeader("isLocationMocked", "false");
        v1soundboxchooseplanObj.setHeader("latitude", "28.5913173");
        v1soundboxchooseplanObj.setHeader("longitude", "77.3189828");
        v1soundboxchooseplanObj.setHeader("osVersion", "9");

        Response v1soundboxChoosePlan = v1soundboxchooseplanObj.callAPI();

        return v1soundboxChoosePlan;
    }

    public Response v1SoundBoxPayment(FetchSoundBoxPayment v1SoundBoxPaymentObj, Map<String, String> queryParams, String session_token, String version) {
        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1SoundBoxPaymentObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1SoundBoxPaymentObj.setHeader("Content-Type", "application/json");
        v1SoundBoxPaymentObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1SoundBoxPaymentObj.setHeader("session_token", session_token);
        v1SoundBoxPaymentObj.setHeader("version", version);
        v1SoundBoxPaymentObj.setHeader("appLanguage", "en");
        v1SoundBoxPaymentObj.setHeader("deviceName", "CPH1859");
        v1SoundBoxPaymentObj.setHeader("client", "androidapp");
        v1SoundBoxPaymentObj.setHeader("imei", "***************");
        v1SoundBoxPaymentObj.setHeader("deviceManufacturer", "OPPO");
        v1SoundBoxPaymentObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1SoundBoxPaymentObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1SoundBoxPaymentObj.setHeader("ipAddress", "************");
        v1SoundBoxPaymentObj.setHeader("isLocationMocked", "false");
        v1SoundBoxPaymentObj.setHeader("latitude", "28.5913173");
        v1SoundBoxPaymentObj.setHeader("longitude", "77.3189828");

        Response v1SoundBoxFetchPayment = v1SoundBoxPaymentObj.callAPI();

        return v1SoundBoxFetchPayment;
    }

    public Response v1SoundBoxBindUrl(SoundBoxBindUrl v1SoundBoxBindUrlObj, String session_token, String version) {

        v1SoundBoxBindUrlObj.setHeader("Content-Type", "application/json");
        v1SoundBoxBindUrlObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1SoundBoxBindUrlObj.setHeader("session_token", session_token);
        v1SoundBoxBindUrlObj.setHeader("version", version);
        v1SoundBoxBindUrlObj.setHeader("appLanguage", "en");
        v1SoundBoxBindUrlObj.setHeader("deviceName", "CPH1859");
        v1SoundBoxBindUrlObj.setHeader("client", "androidapp");
        v1SoundBoxBindUrlObj.setHeader("imei", "***************");
        v1SoundBoxBindUrlObj.setHeader("deviceManufacturer", "OPPO");
        v1SoundBoxBindUrlObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1SoundBoxBindUrlObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1SoundBoxBindUrlObj.setHeader("ipAddress", "************");
        v1SoundBoxBindUrlObj.setHeader("isLocationMocked", "false");
        v1SoundBoxBindUrlObj.setHeader("latitude", "28.5913173");
        v1SoundBoxBindUrlObj.setHeader("longitude", "77.3189828");

        Response v1SoundBoxBindUrlResponse = v1SoundBoxBindUrlObj.callAPI();

        return v1SoundBoxBindUrlResponse;
    }

    public Response v3GetMerchantUnmapEDC(GetMerchant callv3GetMerchantUnmapEDC, String entityType, String solutionType, String session_token, String version, String leadID, String KybId) {


        callv3GetMerchantUnmapEDC.addParameter("entityType", entityType);
        callv3GetMerchantUnmapEDC.addParameter("solutionType", solutionType);
        callv3GetMerchantUnmapEDC.addParameter("leadId", leadID);
        callv3GetMerchantUnmapEDC.addParameter("kybBusinessId", KybId);

        callv3GetMerchantUnmapEDC.setHeader("Content-Type", "application/json");
        callv3GetMerchantUnmapEDC.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        callv3GetMerchantUnmapEDC.setHeader("session_token", session_token);
        callv3GetMerchantUnmapEDC.setHeader("version", version);
        callv3GetMerchantUnmapEDC.setHeader("appLanguage", "en");
        callv3GetMerchantUnmapEDC.setHeader("deviceName", "CPH1859");
        callv3GetMerchantUnmapEDC.setHeader("client", "androidapp");
        callv3GetMerchantUnmapEDC.setHeader("imei", "***************");
        callv3GetMerchantUnmapEDC.setHeader("deviceManufacturer", "OPPO");
        callv3GetMerchantUnmapEDC.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        callv3GetMerchantUnmapEDC.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response callv3GetMerchantUnmapEDCResponse = callv3GetMerchantUnmapEDC.callAPI();

        System.out.println(callv3GetMerchantUnmapEDCResponse.toString());
        return callv3GetMerchantUnmapEDCResponse;
    }

    public Response v1UnmapEdcCreateLead(CreateUnmapEdcLead CreateUnmapEdcLeadObj, Map<String, String> body, String session_token, String version) {

        for (Map.Entry m : body.entrySet()) {
            CreateUnmapEdcLeadObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        CreateUnmapEdcLeadObj.setHeader("Content-Type", "application/json");
        CreateUnmapEdcLeadObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        CreateUnmapEdcLeadObj.setHeader("session_token", session_token);
        CreateUnmapEdcLeadObj.setHeader("version", version);
        CreateUnmapEdcLeadObj.setHeader("appLanguage", "en");
        CreateUnmapEdcLeadObj.setHeader("deviceName", "CPH1859");
        CreateUnmapEdcLeadObj.setHeader("client", "androidapp");
        CreateUnmapEdcLeadObj.setHeader("imei", "***************");
        CreateUnmapEdcLeadObj.setHeader("deviceManufacturer", "OPPO");
        CreateUnmapEdcLeadObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        CreateUnmapEdcLeadObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        CreateUnmapEdcLeadObj.setHeader("ipAddress", "************");
        CreateUnmapEdcLeadObj.setHeader("isLocationMocked", "false");
        CreateUnmapEdcLeadObj.setHeader("latitude", "28.5913173");
        CreateUnmapEdcLeadObj.setHeader("longitude", "77.3189828");

        Response CreateUnmapEdcLeadResponse = CreateUnmapEdcLeadObj.callAPI();

        return CreateUnmapEdcLeadResponse;
    }


    public Response v1CreateUpgradeLead(CreateUnmapEdcLead CreateUnmapEdcLeadObj, Map<String, String> body,Map<String, String> QueryParams, String session_token, String version) {

        for (Map.Entry m : body.entrySet()) {
            CreateUnmapEdcLeadObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : QueryParams.entrySet()) {
            CreateUnmapEdcLeadObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        CreateUnmapEdcLeadObj.setHeader("Content-Type", "application/json");
        CreateUnmapEdcLeadObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        CreateUnmapEdcLeadObj.setHeader("session_token", session_token);
        CreateUnmapEdcLeadObj.setHeader("version", version);
        CreateUnmapEdcLeadObj.setHeader("appLanguage", "en");
        CreateUnmapEdcLeadObj.setHeader("deviceName", "CPH1859");
        CreateUnmapEdcLeadObj.setHeader("client", "androidapp");
        CreateUnmapEdcLeadObj.setHeader("imei", "***************");
        CreateUnmapEdcLeadObj.setHeader("deviceManufacturer", "OPPO");
        CreateUnmapEdcLeadObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        CreateUnmapEdcLeadObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        CreateUnmapEdcLeadObj.setHeader("ipAddress", "************");
        CreateUnmapEdcLeadObj.setHeader("isLocationMocked", "false");
        CreateUnmapEdcLeadObj.setHeader("latitude", "28.5913173");
        CreateUnmapEdcLeadObj.setHeader("longitude", "77.3189828");

        Response CreateUnmapEdcLeadResponse = CreateUnmapEdcLeadObj.callAPI();

        return CreateUnmapEdcLeadResponse;
    }

    public Response v1UnmapEdcReplaceReasons(UnmapEDCReplaceReasons UnmapEDCReplaceReasonsObj, String session_token, String version) {

        UnmapEDCReplaceReasonsObj.setHeader("Content-Type", "application/json");
        UnmapEDCReplaceReasonsObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        UnmapEDCReplaceReasonsObj.setHeader("session_token", session_token);
        UnmapEDCReplaceReasonsObj.setHeader("version", version);
        UnmapEDCReplaceReasonsObj.setHeader("appLanguage", "en");
        UnmapEDCReplaceReasonsObj.setHeader("deviceName", "CPH1859");
        UnmapEDCReplaceReasonsObj.setHeader("client", "androidapp");
        UnmapEDCReplaceReasonsObj.setHeader("imei", "***************");
        UnmapEDCReplaceReasonsObj.setHeader("deviceManufacturer", "OPPO");
        UnmapEDCReplaceReasonsObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        UnmapEDCReplaceReasonsObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        UnmapEDCReplaceReasonsObj.setHeader("ipAddress", "************");
        UnmapEDCReplaceReasonsObj.setHeader("isLocationMocked", "false");
        UnmapEDCReplaceReasonsObj.setHeader("latitude", "28.5913173");
        UnmapEDCReplaceReasonsObj.setHeader("longitude", "77.3189828");

        Response UnmapEDCReplaceReasonsResponse = UnmapEDCReplaceReasonsObj.callAPI();

        return UnmapEDCReplaceReasonsResponse;
    }

    public Response v1UnmapEdcReplaceReasonsWithClaimAMC(UnmapEDCReplaceReasonsWithClaimAMC UnmapEDCReplaceReasonsObj, String session_token, String version) {

        UnmapEDCReplaceReasonsObj.setHeader("Content-Type", "application/json");
        UnmapEDCReplaceReasonsObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        UnmapEDCReplaceReasonsObj.setHeader("session_token", session_token);
        UnmapEDCReplaceReasonsObj.setHeader("version", version);
        UnmapEDCReplaceReasonsObj.setHeader("appLanguage", "en");
        UnmapEDCReplaceReasonsObj.setHeader("deviceName", "CPH1859");
        UnmapEDCReplaceReasonsObj.setHeader("client", "androidapp");
        UnmapEDCReplaceReasonsObj.setHeader("imei", "***************");
        UnmapEDCReplaceReasonsObj.setHeader("deviceManufacturer", "OPPO");
        UnmapEDCReplaceReasonsObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        UnmapEDCReplaceReasonsObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        UnmapEDCReplaceReasonsObj.setHeader("ipAddress", "************");
        UnmapEDCReplaceReasonsObj.setHeader("isLocationMocked", "false");
        UnmapEDCReplaceReasonsObj.setHeader("latitude", "28.5913173");
        UnmapEDCReplaceReasonsObj.setHeader("longitude", "77.3189828");

        Response UnmapEDCReplaceReasonsResponse = UnmapEDCReplaceReasonsObj.callAPI();

        return UnmapEDCReplaceReasonsResponse;
    }


    public Response v1UnmapEdcReturnReasons(UnmapEDCReturnReasons UnmapEDCReturnReasonsObj, String session_token, String version) {

        UnmapEDCReturnReasonsObj.setHeader("Content-Type", "application/json");
        UnmapEDCReturnReasonsObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        UnmapEDCReturnReasonsObj.setHeader("session_token", session_token);
        UnmapEDCReturnReasonsObj.setHeader("version", version);
        UnmapEDCReturnReasonsObj.setHeader("appLanguage", "en");
        UnmapEDCReturnReasonsObj.setHeader("deviceName", "CPH1859");
        UnmapEDCReturnReasonsObj.setHeader("client", "androidapp");
        UnmapEDCReturnReasonsObj.setHeader("imei", "***************");
        UnmapEDCReturnReasonsObj.setHeader("deviceManufacturer", "OPPO");
        UnmapEDCReturnReasonsObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        UnmapEDCReturnReasonsObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        UnmapEDCReturnReasonsObj.setHeader("ipAddress", "************");
        UnmapEDCReturnReasonsObj.setHeader("isLocationMocked", "false");
        UnmapEDCReturnReasonsObj.setHeader("latitude", "28.5913173");
        UnmapEDCReturnReasonsObj.setHeader("longitude", "77.3189828");

        Response UnmapEDCReturnReasonsResponse = UnmapEDCReturnReasonsObj.callAPI();

        return UnmapEDCReturnReasonsResponse;
    }

    public Response v1UnmapEdcReturnReasonsWithClaimAMC(UnmapEDCReturnReasonsWithClaimAMC UnmapEDCReturnReasonsObj, String session_token, String version) {

        UnmapEDCReturnReasonsObj.setHeader("Content-Type", "application/json");
        UnmapEDCReturnReasonsObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        UnmapEDCReturnReasonsObj.setHeader("session_token", session_token);
        UnmapEDCReturnReasonsObj.setHeader("version", version);
        UnmapEDCReturnReasonsObj.setHeader("appLanguage", "en");
        UnmapEDCReturnReasonsObj.setHeader("deviceName", "CPH1859");
        UnmapEDCReturnReasonsObj.setHeader("client", "androidapp");
        UnmapEDCReturnReasonsObj.setHeader("imei", "***************");
        UnmapEDCReturnReasonsObj.setHeader("deviceManufacturer", "OPPO");
        UnmapEDCReturnReasonsObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        UnmapEDCReturnReasonsObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        UnmapEDCReturnReasonsObj.setHeader("ipAddress", "************");
        UnmapEDCReturnReasonsObj.setHeader("isLocationMocked", "false");
        UnmapEDCReturnReasonsObj.setHeader("latitude", "28.5913173");
        UnmapEDCReturnReasonsObj.setHeader("longitude", "77.3189828");

        Response UnmapEDCReturnReasonsResponse = UnmapEDCReturnReasonsObj.callAPI();

        return UnmapEDCReturnReasonsResponse;
    }

    public Response v1FetchallTerminal(fetchAllTerminal fetchAllTerminalObj, Map<String, String> queryParams, String session_token, String version) {

        for (Map.Entry m : queryParams.entrySet()) {
            fetchAllTerminalObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        fetchAllTerminalObj.setHeader("Content-Type", "application/json");
        fetchAllTerminalObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        fetchAllTerminalObj.setHeader("session_token", session_token);
        fetchAllTerminalObj.setHeader("version", version);
        fetchAllTerminalObj.setHeader("appLanguage", "en");
        fetchAllTerminalObj.setHeader("deviceName", "CPH1859");
        fetchAllTerminalObj.setHeader("client", "androidapp");
        fetchAllTerminalObj.setHeader("imei", "***************");
        fetchAllTerminalObj.setHeader("deviceManufacturer", "OPPO");
        fetchAllTerminalObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        fetchAllTerminalObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        fetchAllTerminalObj.setHeader("ipAddress", "************");
        fetchAllTerminalObj.setHeader("isLocationMocked", "false");
        fetchAllTerminalObj.setHeader("latitude", "28.5913173");
        fetchAllTerminalObj.setHeader("longitude", "77.3189828");

        Response fetchAllTerminalResponse = fetchAllTerminalObj.callAPI();

        return fetchAllTerminalResponse;
    }

    public Response validateEDCQr(validateEDCQr validateEDCQrOBj, Map<String, String> body, String session_token, String version) {

        for (Map.Entry m : body.entrySet()) {
            validateEDCQrOBj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        validateEDCQrOBj.setHeader("Content-Type", "application/json");
        validateEDCQrOBj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        validateEDCQrOBj.setHeader("session_token", session_token);
        validateEDCQrOBj.setHeader("version", version);
        validateEDCQrOBj.setHeader("appLanguage", "en");
        validateEDCQrOBj.setHeader("deviceName", "CPH1859");
        validateEDCQrOBj.setHeader("client", "androidapp");
        validateEDCQrOBj.setHeader("imei", "***************");
        validateEDCQrOBj.setHeader("deviceManufacturer", "OPPO");
        validateEDCQrOBj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        validateEDCQrOBj.setHeader("Content-Type", "application/json; charset=UTF-8");
        validateEDCQrOBj.setHeader("ipAddress", "************");
        validateEDCQrOBj.setHeader("isLocationMocked", "false");
        validateEDCQrOBj.setHeader("latitude", "28.5913173");
        validateEDCQrOBj.setHeader("longitude", "77.3189828");

        Response validateEDCQrOBjResponse = validateEDCQrOBj.callAPI();

        return validateEDCQrOBjResponse;
    }

    public Response UnmapEDCMachine(unmapEDCMachine unmapEDCMachineObj, Map<String, String> body, String session_token, String version) {

        for (Map.Entry m : body.entrySet()) {
            unmapEDCMachineObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        unmapEDCMachineObj.setHeader("Content-Type", "application/json");
        unmapEDCMachineObj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        unmapEDCMachineObj.setHeader("session_token", session_token);
        unmapEDCMachineObj.setHeader("version", version);
        unmapEDCMachineObj.setHeader("appLanguage", "en");
        unmapEDCMachineObj.setHeader("deviceName", "CPH1859");
        unmapEDCMachineObj.setHeader("client", "androidapp");
        unmapEDCMachineObj.setHeader("imei", "***************");
        unmapEDCMachineObj.setHeader("deviceManufacturer", "OPPO");
        unmapEDCMachineObj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        unmapEDCMachineObj.setHeader("Content-Type", "application/json; charset=UTF-8");
        unmapEDCMachineObj.setHeader("ipAddress", "************");
        unmapEDCMachineObj.setHeader("isLocationMocked", "false");
        unmapEDCMachineObj.setHeader("latitude", "28.5913173");
        unmapEDCMachineObj.setHeader("longitude", "77.3189828");

        Response unmapEDCMachineObjResponse = unmapEDCMachineObj.callAPI();

        return unmapEDCMachineObjResponse;
    }


    public Response addAddressPL_LOS(addAddressPL_LOS addLeadAddressRequest, String entityType, String solutionTypeLevel2, String solution, String channel, String session_token, String workflowSubOperation) {

        addLeadAddressRequest.addParameter("entityType", entityType);
        addLeadAddressRequest.addParameter("solutionTypeLevel2", solutionTypeLevel2);
        addLeadAddressRequest.addParameter("solution", solution);
        addLeadAddressRequest.addParameter("channel", channel);
        addLeadAddressRequest.setHeader("session_token", session_token);
        addLeadAddressRequest.getProperties().setProperty("workflowSubOperation", workflowSubOperation);
        Response addLeadAddressResponse = addLeadAddressRequest.callAPI();
        return addLeadAddressResponse;

    }


    public Response v1PanelRegisterLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        RegisterLeadPanel registerLeadPanelObject = new RegisterLeadPanel();

        for (Map.Entry m : queryParams.entrySet()) {
            registerLeadPanelObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            registerLeadPanelObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            registerLeadPanelObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response registerLeadPanelObjectResponse = registerLeadPanelObject.callAPI();

        try {

            ((AbstractApiV2) registerLeadPanelObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/panel/leadManagement/registerLead/registerLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return registerLeadPanelObjectResponse;
    }


    public Response v1PanelRegisterLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, boolean submit) {

        RegisterLeadPanel registerLeadPanelObject = new RegisterLeadPanel(submit);

        for (Map.Entry m : queryParams.entrySet()) {
            registerLeadPanelObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            registerLeadPanelObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            registerLeadPanelObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response registerLeadPanelObjectResponse = registerLeadPanelObject.callAPI();

        try {

            ((AbstractApiV2) registerLeadPanelObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/panel/leadManagement/registerLead/registerLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return registerLeadPanelObjectResponse;
    }

    public Response v1PanelRegisterLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String edc) {

        RegisterLeadPanel registerLeadPanelObject = new RegisterLeadPanel(edc);

        for (Map.Entry m : queryParams.entrySet()) {
            registerLeadPanelObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            registerLeadPanelObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            registerLeadPanelObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response registerLeadPanelObjectResponse = registerLeadPanelObject.callAPI();

        try {

            ((AbstractApiV2) registerLeadPanelObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/panel/leadManagement/registerLead/edcLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return registerLeadPanelObjectResponse;
    }


    public Response v1getLeadDetails(Map<String, String> queryParams, Map<String, String> headers) {

        GetLeadDetails getLeadDetailsObject = new GetLeadDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            getLeadDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getLeadDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response getLeadDetailsObjectResponse = getLeadDetailsObject.callAPI();

        try {

            ((AbstractApiV2) getLeadDetailsObjectResponse).validateResponseAgainstJSONSchema("MerchantService/panel/v1/solution/lead/LeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return getLeadDetailsObjectResponse;
    }

    public Response v1getBusinessDocDetails(Map<String, String> queryParams, Map<String, String> headers) {

        GetBusinessDoc getBusinessDocObject = new GetBusinessDoc();

        for (Map.Entry m : queryParams.entrySet()) {
            getBusinessDocObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getBusinessDocObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response getBusinessDocObjectResponse = getBusinessDocObject.callAPI();

     /*   try {

            ((AbstractApiV2) getBusinessDocObjectResponse).validateResponseAgainstJSONSchema("MerchantService/panel/v1/business/doc/status/GetBusinessDetailsResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }*/
        return getBusinessDocObjectResponse;
    }

    public void utilityForDocumentUpload(String solutionType, String entityType, String leadId, Map<String, String> headers) throws InterruptedException {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", solutionType);
        queryParams.put("entityType", entityType);
        queryParams.put("leadId", leadId);
        queryParams.put("merchantCustId", "");
        queryParams.put("docProvided", "registerLeadAdditionalDoc3");
        queryParams.put("lobId", "");
        queryParams.put("pageNo", "");

        File uploadFile = new File("src/test/resources/MerchantService/OE/Panel/v1/document/multiPart/test.jpeg");

        headers.put("Content-Type", "multipart/form-data");
        headers.put("email", "<EMAIL>");

        Response responseObject = UploadDocument(queryParams, headers, uploadFile);

        System.out.println("STEP 2- Verify Status Code ");
        verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getBoolean("agentTncStatus"), true);
        Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);

        queryParams.put("docProvided", "shopFrontPhoto");
        queryParams.put("lobId", "");
        queryParams.put("pageNo", "");

        responseObject = UploadDocument(queryParams, headers, uploadFile);

        Assert.assertEquals(responseObject.jsonPath().getBoolean("agentTncStatus"), true);
        Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);

        queryParams.put("docProvided", "shopFrontPhoto");
        queryParams.put("lobId", "");
        queryParams.put("pageNo", "");

        responseObject = UploadDocument(queryParams, headers, uploadFile);

        Assert.assertEquals(responseObject.jsonPath().getBoolean("agentTncStatus"), true);
        Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);

        queryParams.put("docProvided", "registerLeadPan");
        queryParams.put("lobId", "");
        queryParams.put("pageNo", "");

        responseObject = UploadDocument(queryParams, headers, uploadFile);

        Assert.assertEquals(responseObject.jsonPath().getBoolean("agentTncStatus"), true);
        Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);

        queryParams.put("docProvided", "majorCompetitorQrImage");
        queryParams.put("lobId", "");
        queryParams.put("pageNo", "");

        responseObject = UploadDocument(queryParams, headers, uploadFile);


        Assert.assertEquals(responseObject.jsonPath().getBoolean("agentTncStatus"), true);
        Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);

    }

    public Response UploadDocument(Map<String, String> queryParams, Map<String, String> headers, File uploadFile) throws InterruptedException {


        UploadDocumentFlow UploadDocumentObject = new UploadDocumentFlow();


        //Adding queryParams
        for (Map.Entry m : queryParams.entrySet()) {
            UploadDocumentObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            UploadDocumentObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        UploadDocumentObject.addMultipartFormData("file", uploadFile, "image/jpeg");
        Thread.sleep(2000);

        UploadDocumentObject.getRequest().urlEncodingEnabled(false);

        Response UploadDocumentObjectResponse = UploadDocumentObject.callAPI();

        try {
            if (UploadDocumentObjectResponse.jsonPath().getString("errorCode").equals("204")) {
                LOGGER.info("Inside Document upload json validation");
                UploadDocumentObject.validateResponseAgainstJSONSchema("MerchantService/OE/Panel/v1/document/multiPart/UploadDocumentResponseSchema.json");
            }

        } catch (Exception e) {
            LOGGER.info("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }


        return UploadDocumentObjectResponse;
    }

    /**
     * Verify  REsponse Code as 200 OK
     *
     * @param responseObject
     */
    public void verifyResponseCodeAs200OK(Response responseObject) {

        System.out.println("Status Code : " + responseObject.getStatusCode());
        Assert.assertEquals(responseObject.getStatusCode(), 200);

    }

    public Response v1getAllResouces(Map<String, String> queryParams, Map<String, String> headers) {

        GetAllResources getAllResourcesObject = new GetAllResources(P.TESTDATA.get("GetAllResourcesRequest"));

        for (Map.Entry m : queryParams.entrySet()) {
            getAllResourcesObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getAllResourcesObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response getAllResourcesObjectResponse = getAllResourcesObject.callAPI();

        try {

            ((AbstractApiV2) getAllResourcesObjectResponse).validateResponseAgainstJSONSchema("MerchantService/OE/panel/v1/referenceData/getAllResources/getAllResourcesResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return getAllResourcesObjectResponse;
    }

    public Response addAndUpdateAgentDetails(agent agentDetailsObject, Map<String, String> headers, Map<String, String> body) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            agentDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            agentDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response agentDetailsObjectResponse = agentDetailsObject.callAPI();

        return agentDetailsObjectResponse;


    }

    public Response addAndUpdateAgentDetails(agent agentDetailsObject, Map<String, String> headers, Map<String, String> body, Map<String, String> queryParams) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            agentDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : body.entrySet()) {
            agentDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : queryParams.entrySet()) {
            agentDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        //Hit API to get Response
        Response agentDetailsObjectResponse = agentDetailsObject.callAPI();

        return agentDetailsObjectResponse;


    }

    public Response searchAgentDetails(userInfo userInfoObject, Map<String, String> headers) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            userInfoObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response searchAgentDetailsObjectResponse = userInfoObject.callAPI();

        return searchAgentDetailsObjectResponse;


    }

    public Response searchMerchantDetails(Mobile MobileObject, Map<String, String> headers, Map<String, String> queryParams) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            MobileObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Params
        for (Map.Entry m : queryParams.entrySet()) {
            MobileObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response MobileObjectResponse = MobileObject.callAPI();

        return MobileObjectResponse;


    }


    public Response postChannelSubscription(ChannelAdd postChannelSubscriptionObject, Map<String, String> headers, Map<String, String> body) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            postChannelSubscriptionObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Body
        for (Map.Entry m : body.entrySet()) {
            postChannelSubscriptionObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response postChannelSubscriptionObjectResponse = postChannelSubscriptionObject.callAPI();

        return postChannelSubscriptionObjectResponse;
    }

    public Response postChannelSubscriptionGoogle(ChannelAdd postChannelSubscriptionObject, Map<String, Object> headers, Map<String, Object> body) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            postChannelSubscriptionObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Body
        for (Map.Entry m : body.entrySet()) {
            postChannelSubscriptionObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response postChannelSubscriptionObjectResponse = postChannelSubscriptionObject.callAPI();

        return postChannelSubscriptionObjectResponse;
    }

    public Response MidMobile(MidMobile getMidMobile, Map<String, String> query, String version, String token) {
        for (Map.Entry m : query.entrySet()) {
            getMidMobile.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        getMidMobile.setHeader("Content-Type", "application/json");
        getMidMobile.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getMidMobile.setHeader("session_token", token);
        getMidMobile.setHeader("version", version);
        getMidMobile.setHeader("appLanguage", "en");
        getMidMobile.setHeader("deviceName", "CPH1859");
        getMidMobile.setHeader("client", "androidapp");
        getMidMobile.setHeader("imei", "***************");
        getMidMobile.setHeader("deviceManufacturer", "OPPO");
        getMidMobile.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getMidMobile.setHeader("Content-Type", "application/json; charset=UTF-8");
        getMidMobile.setHeader("isDeviceRooted", "false");
        getMidMobile.setHeader("ipAddress", DeviceIP);
        getMidMobile.setHeader("isLocationMocked", "false");
        getMidMobile.setHeader("latitude", "28.5913173");
        getMidMobile.setHeader("longitude", "77.3189828");
        getMidMobile.setHeader("osVersion", "9");

        Response v3getMidMobile = getMidMobile.callAPI();

        return v3getMidMobile;

    }

    public Response RevisitOrgainsed(RevisitOrganised organisedRevisit, Map<String, String> query, String version, String token) {
        for (Map.Entry m : query.entrySet()) {
            organisedRevisit.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        organisedRevisit.setHeader("Content-Type", "application/json");
        organisedRevisit.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        organisedRevisit.setHeader("session_token", token);
        organisedRevisit.setHeader("version", version);
        organisedRevisit.setHeader("appLanguage", "en");
        organisedRevisit.setHeader("deviceName", "CPH1859");
        organisedRevisit.setHeader("client", "androidapp");
        organisedRevisit.setHeader("imei", "***************");
        organisedRevisit.setHeader("deviceManufacturer", "OPPO");
        organisedRevisit.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        organisedRevisit.setHeader("Content-Type", "application/json; charset=UTF-8");
        organisedRevisit.setHeader("isDeviceRooted", "false");
        organisedRevisit.setHeader("ipAddress", DeviceIP);
        organisedRevisit.setHeader("isLocationMocked", "false");
        organisedRevisit.setHeader("latitude", "28.5913173");
        organisedRevisit.setHeader("longitude", "77.3189828");
        organisedRevisit.setHeader("osVersion", "9");

        Response organisedRevisitResp = organisedRevisit.callAPI();

        return organisedRevisitResp;

    }

    public Response RevisitSubmit(RevisitSubmit submitRevisit, Map<String, String> query, Map<String, String> body, String version, String token) {
        for (Map.Entry m : query.entrySet()) {
            submitRevisit.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitRevisit.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        submitRevisit.setHeader("Content-Type", "application/json");
        submitRevisit.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        submitRevisit.setHeader("session_token", token);
        submitRevisit.setHeader("version", version);
        submitRevisit.setHeader("appLanguage", "en");
        submitRevisit.setHeader("deviceName", "CPH1859");
        submitRevisit.setHeader("client", "androidapp");
        submitRevisit.setHeader("imei", "***************");
        submitRevisit.setHeader("deviceManufacturer", "OPPO");
        submitRevisit.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        submitRevisit.setHeader("Content-Type", "application/json; charset=UTF-8");
        submitRevisit.setHeader("isDeviceRooted", "false");
        submitRevisit.setHeader("ipAddress", DeviceIP);
        submitRevisit.setHeader("isLocationMocked", "false");
        submitRevisit.setHeader("latitude", "28.5913173");
        submitRevisit.setHeader("longitude", "77.3189828");
        submitRevisit.setHeader("osVersion", "9");

        Response submitRevisitResp = submitRevisit.callAPI();

        return submitRevisitResp;

    }

    public Response PgCallBack(PgCallBack pgCallback, Map<String, String> query, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : query.entrySet()) {
            pgCallback.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            pgCallback.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            pgCallback.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response PgCallBackResp = pgCallback.callAPI();

        return PgCallBackResp;

    }

    public Response CombinedLeadStatus(CombinedLeadStatus objLead, Map<String, String> query, Map<String, String> headers) {
        for (Map.Entry m : query.entrySet()) {
            objLead.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            objLead.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response CombinedLead = objLead.callAPI();

        return CombinedLead;


    }

    public Response CompanyOnboardPanel(CompanyOnboardPanel getBus, Map<String, String> query, String XMWToken) {
        getBus.setHeader("cookie", XMWToken);
        getBus.setHeader("phonenumber", "9953828631");
        getBus.setHeader("ssoid", "1107234087");
        getBus.setHeader("accept", "application/json, text/plain, */*");
        getBus.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            getBus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetBusResp = getBus.callAPI();

        return GetBusResp;

    }

    public Response FetchPanPanel(FetchPanPanel getBus, Map<String, String> query, String XMWToken) {
        getBus.setHeader("cookie", XMWToken);
        getBus.setHeader("phonenumber", "9953828631");
        getBus.setHeader("ssoid", "1107234087");
        getBus.setHeader("accept", "application/json, text/plain, */*");
        getBus.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            getBus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetBusResp = getBus.callAPI();

        return GetBusResp;

    }

    public Response FetchBusinessProfilePanel(FetchBusinessProfilePanel getBus, Map<String, String> query, String XMWToken) {
        getBus.setHeader("cookie", XMWToken);
        getBus.setHeader("phonenumber", "9953828631");
        getBus.setHeader("ssoid", "1107234087");
        getBus.setHeader("accept", "application/json, text/plain, */*");
        getBus.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            getBus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetBusResp = getBus.callAPI();

        return GetBusResp;

    }

    public Response CreateSolutionPanel(CreateSolutionPanel getBus, Map<String, String> query, String XMWToken) {
        getBus.setHeader("cookie", XMWToken);
        getBus.setHeader("phonenumber", "9953828631");
        getBus.setHeader("ssoid", "1107234087");
        getBus.setHeader("accept", "application/json, text/plain, */*");
        getBus.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            getBus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetBusResp = getBus.callAPI();

        return GetBusResp;

    }

    public Response UpdateSolutionPanel(UpdateSolutionPanel getBus, Map<String, String> query, String XMWToken) {
        getBus.setHeader("cookie", XMWToken);
        getBus.setHeader("phonenumber", "9953828631");
        getBus.setHeader("ssoid", "1107234087");
        getBus.setHeader("accept", "application/json, text/plain, */*");
        getBus.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            getBus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetBusResp = getBus.callAPI();

        return GetBusResp;

    }

    public Response VerifyDocumentPanel(VerifyDocument getBus, Map<String, String> query, String XMWToken) {
        getBus.setHeader("cookie", XMWToken);
        getBus.setHeader("phonenumber", "9953828631");
        getBus.setHeader("ssoid", "1107234087");
        getBus.setHeader("accept", "application/json, text/plain, */*");
        getBus.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            getBus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetBusResp = getBus.callAPI();

        return GetBusResp;

    }

    public Response PanelPennyDrop(PanelPennyDrop getBus, Map<String, String> query, String XMWToken) {
        getBus.setHeader("cookie", XMWToken);
        getBus.setHeader("phonenumber", "9953828631");
        getBus.setHeader("ssoid", "1107234087");
        getBus.setHeader("accept", "application/json, text/plain, */*");
        getBus.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            getBus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetBusResp = getBus.callAPI();

        return GetBusResp;

    }

    public Response v2FetchDynamicTnc(v2FetchDynamicTnc v2Tnc, Map<String, String> query, String XMWToken, String CustId) {
        v2Tnc.setHeader("cookie", XMWToken);
        v2Tnc.setHeader("ssoid", CustId);
        v2Tnc.setHeader("accept", "application/json, text/plain, */*");
        v2Tnc.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            v2Tnc.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetTnCResp = v2Tnc.callAPI();

        return GetTnCResp;
    }

    public Response v2SaveDynamicTnc(v2SaveDynamicTnc v2Tnc, Map<String, String> query, String XMWToken, String CustId) {
        v2Tnc.setHeader("cookie", XMWToken);
        v2Tnc.setHeader("ssoid", CustId);
        v2Tnc.setHeader("accept", "application/json, text/plain, */*");
        v2Tnc.setHeader("content-type", "application/json;charset=UTF-8");

        for (Map.Entry m : query.entrySet()) {
            v2Tnc.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response saveTnCResp = v2Tnc.callAPI();

        return saveTnCResp;
    }

    public Response v1sdMerchantLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadLending createLeadPostPaidObject = new CreateLeadLending();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();

        try {

            ((AbstractApiV2) createLeadPostPaidObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return createLeadPostPaidObjectResponse;
    }

    public Response v1AssistedMerchant(AssistedMerchant v1Assist, Map<String, String> query, String version, String session_token) {
        for (Map.Entry m : query.entrySet()) {
            v1Assist.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1Assist.setHeader("Content-Type", "application/json");
        v1Assist.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1Assist.setHeader("session_token", session_token);
        v1Assist.setHeader("version", version);
        v1Assist.setHeader("appLanguage", "en");
        v1Assist.setHeader("deviceName", "CPH1859");
        v1Assist.setHeader("client", "androidapp");
        v1Assist.setHeader("imei", "***************");
        v1Assist.setHeader("deviceManufacturer", "OPPO");
        v1Assist.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1Assist.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response GetAssistedMerchant = v1Assist.callAPI();

        System.out.println(v1Assist.toString());

        return GetAssistedMerchant;
    }

    public Response v1Commission(Commission v1Commission, Map<String, String> query, String version, String session_token) {
        for (Map.Entry m : query.entrySet()) {
            v1Commission.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        v1Commission.setHeader("Content-Type", "application/json");
        v1Commission.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1Commission.setHeader("session_token", session_token);
        v1Commission.setHeader("version", version);
        v1Commission.setHeader("appLanguage", "en");
        v1Commission.setHeader("deviceName", "CPH1859");
        v1Commission.setHeader("client", "androidapp");
        v1Commission.setHeader("imei", "***************");
        v1Commission.setHeader("deviceManufacturer", "OPPO");
        v1Commission.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1Commission.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response GetCommission = v1Commission.callAPI();

        System.out.println(v1Commission.toString());

        return GetCommission;
    }

    public Response FisFetchTags(GetFastagTags fetchTag, Map<String, String> query) {
        for (Map.Entry m : query.entrySet()) {
            fetchTag.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response GetTags = fetchTag.callAPI();

        return GetTags;
    }

    public Response ValidateTag(ValidateTag ValidateTag, String version, String session_token) {
        ValidateTag.setHeader("Content-Type", "application/json");
        ValidateTag.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        ValidateTag.setHeader("session_token", session_token);
        ValidateTag.setHeader("version", version);
        ValidateTag.setHeader("appLanguage", "en");
        ValidateTag.setHeader("deviceName", "CPH1859");
        ValidateTag.setHeader("client", "androidapp");
        ValidateTag.setHeader("imei", "***************");
        ValidateTag.setHeader("deviceManufacturer", "OPPO");
        ValidateTag.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        ValidateTag.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response RespValidateTag = ValidateTag.callAPI();


        return RespValidateTag;
    }

    public Response FastagDropDownList(FastagDropDown FastagDrop, String version, String session_token) {
        FastagDrop.setHeader("Content-Type", "application/json");
        FastagDrop.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        FastagDrop.setHeader("session_token", session_token);
        FastagDrop.setHeader("version", version);
        FastagDrop.setHeader("appLanguage", "en");
        FastagDrop.setHeader("deviceName", "CPH1859");
        FastagDrop.setHeader("client", "androidapp");
        FastagDrop.setHeader("imei", "***************");
        FastagDrop.setHeader("deviceManufacturer", "OPPO");
        FastagDrop.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        FastagDrop.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response fastagSropDownList = FastagDrop.callAPI();


        return fastagSropDownList;
    }

    public Response FastagFetchTnc(FetchFastagTnC FetchTnC, String version, String session_token) {
        FetchTnC.setHeader("Content-Type", "application/json");
        FetchTnC.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        FetchTnC.setHeader("session_token", session_token);
        FetchTnC.setHeader("version", version);
        FetchTnC.setHeader("appLanguage", "en");
        FetchTnC.setHeader("deviceName", "CPH1859");
        FetchTnC.setHeader("client", "androidapp");
        FetchTnC.setHeader("imei", "***************");
        FetchTnC.setHeader("deviceManufacturer", "OPPO");
        FetchTnC.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        FetchTnC.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response FetchTnCFastag = FetchTnC.callAPI();

        return FetchTnCFastag;
    }

    public Response FetchIssuanceFastag(FetchIssuance getIssuance, String version, String session_token) {

        getIssuance.setHeader("Content-Type", "application/json");
        getIssuance.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getIssuance.setHeader("session_token", session_token);
        getIssuance.setHeader("version", version);
        getIssuance.setHeader("appLanguage", "en");
        getIssuance.setHeader("deviceName", "CPH1859");
        getIssuance.setHeader("client", "androidapp");
        getIssuance.setHeader("imei", "***************");
        getIssuance.setHeader("deviceManufacturer", "OPPO");
        getIssuance.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getIssuance.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response FetchIssuance = getIssuance.callAPI();

        return FetchIssuance;
    }

    public Response FastagCreateLead(CreateFastag Createlead, Map<String, String> query, String version, String session_token) {
        for (Map.Entry m : query.entrySet()) {
            Createlead.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Createlead.setHeader("Content-Type", "application/json");
        Createlead.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        Createlead.setHeader("session_token", session_token);
        Createlead.setHeader("version", version);
        Createlead.setHeader("appLanguage", "en");
        Createlead.setHeader("deviceName", "CPH1859");
        Createlead.setHeader("client", "androidapp");
        Createlead.setHeader("imei", "***************");
        Createlead.setHeader("deviceManufacturer", "OPPO");
        Createlead.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        Createlead.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response LeadCreate = Createlead.callAPI();

        return LeadCreate;
    }

    public Response ValidateFastag(ValidateFastag ValFast, Map<String, String> query, String version, String session_token) {
        for (Map.Entry m : query.entrySet()) {
            ValFast.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        ValFast.setHeader("Content-Type", "application/json");
        ValFast.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        ValFast.setHeader("session_token", session_token);
        ValFast.setHeader("version", version);
        ValFast.setHeader("appLanguage", "en");
        ValFast.setHeader("deviceName", "CPH1859");
        ValFast.setHeader("client", "androidapp");
        ValFast.setHeader("imei", "***************");
        ValFast.setHeader("deviceManufacturer", "OPPO");
        ValFast.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        ValFast.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response validateFastag = ValFast.callAPI();

        return validateFastag;
    }

    public Response FetchFastagPayment(FetchFastagPayment getPayStatus, Map<String, String> query, String version, String session_token) {
        for (Map.Entry m : query.entrySet()) {
            getPayStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        getPayStatus.setHeader("Content-Type", "application/json");
        getPayStatus.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getPayStatus.setHeader("session_token", session_token);
        getPayStatus.setHeader("version", version);
        getPayStatus.setHeader("appLanguage", "en");
        getPayStatus.setHeader("deviceName", "CPH1859");
        getPayStatus.setHeader("client", "androidapp");
        getPayStatus.setHeader("imei", "***************");
        getPayStatus.setHeader("deviceManufacturer", "OPPO");
        getPayStatus.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getPayStatus.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response RespPayStatus = getPayStatus.callAPI();

        return RespPayStatus;

    }

    public Response FetchVirtualCartPay(FetchVirtualCart getVirtualPay, Map<String, String> query, String version, String session_token) {
        for (Map.Entry m : query.entrySet()) {
            getVirtualPay.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        getVirtualPay.setHeader("Content-Type", "application/json");
        getVirtualPay.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getVirtualPay.setHeader("session_token", session_token);
        getVirtualPay.setHeader("version", version);
        getVirtualPay.setHeader("appLanguage", "en");
        getVirtualPay.setHeader("deviceName", "CPH1859");
        getVirtualPay.setHeader("client", "androidapp");
        getVirtualPay.setHeader("imei", "***************");
        getVirtualPay.setHeader("deviceManufacturer", "OPPO");
        getVirtualPay.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        getVirtualPay.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response VirtualPay = getVirtualPay.callAPI();

        return VirtualPay;
    }

    public Response FetchV3MerchantLead(FetchV3Merchant v3Merch, Map<String, String> query, String version, String session_token) {
        for (Map.Entry m : query.entrySet()) {
            v3Merch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        v3Merch.setHeader("Content-Type", "application/json");
        v3Merch.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v3Merch.setHeader("session_token", session_token);
        v3Merch.setHeader("version", version);
        v3Merch.setHeader("appLanguage", "en");
        v3Merch.setHeader("deviceName", "CPH1859");
        v3Merch.setHeader("client", "androidapp");
        v3Merch.setHeader("imei", "***************");
        v3Merch.setHeader("deviceManufacturer", "OPPO");
        v3Merch.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v3Merch.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response getV3Merch = v3Merch.callAPI();

        return getV3Merch;
    }

    // SoundBox API

    public Response soundboxDeviceType(Map<String, String> queryParams, Map<String, String> headers) {

        SoundBOXDeviceType addBasicDetailsObject = new SoundBOXDeviceType();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }

    public Response soundboxLeadCount(Map<String, String> queryParams, Map<String, String> headers) {

        SoundboxLeadCount addBasicDetailsObject = new SoundboxLeadCount();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }


    public Response FetchTnCSoundBoxDetails(Map<String, String> queryParams, Map<String, String> headers) {

        SoundboxTnc addBasicDetailsObject = new SoundboxTnc();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }

    public Response FetchReplacementQNA(Map<String, String> queryParams, Map<String, String> headers) {

        FetchQnA addBasicDetailsObject = new FetchQnA();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }

    public Response soundboxMerchantDetails(Map<String, String> queryParams, Map<String, String> headers, String CustiD) {


        GetMerchant addBasicDetailsObject = new GetMerchant(CustiD);


        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }

    public Response PinCodeDetails(Map<String, String> queryParams, Map<String, String> headers, String Pincode) {


        PinCode addBasicDetailsObject = new PinCode(Pincode);


        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }

    public Response FetchMID(Map<String, String> queryParams, Map<String, String> headers) {

        MID addBasicDetailsObject = new MID();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }

    public Response FetchPlan(Map<String, String> queryParams, Map<String, String> headers) {

        SoundBoxFetchPlan addBasicDetailsObject = new SoundBoxFetchPlan();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }

    public Response Lead(SoundBoxLead addBasicDetailsObject, Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            addBasicDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        return addBasicDetailsObjectResponse;
    }


    public Response fetchPlanEdcDIY(fetchPlanEdcDIY fetchPlanEdcDIY, Map<String, String> queryParams, Map<String, String> headers) {

        fetchPlanEdcDIY.getRequest().urlEncodingEnabled(false);
        for (Map.Entry m : queryParams.entrySet()) {
            fetchPlanEdcDIY.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchPlanEdcDIY.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response fetchPlanEdcDIYResponse = fetchPlanEdcDIY.callAPI();

        return fetchPlanEdcDIYResponse;


    }

    public Response checkEligibilityDIY(CheckEligibilityDIY CheckEligibilityDIYObj, Map<String, String> queryParams) {

        CheckEligibilityDIYObj.getRequest().urlEncodingEnabled(false);
        for (Map.Entry m : queryParams.entrySet()) {
            CheckEligibilityDIYObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        //Hit API to get Response
        Response CheckEligibilityDIYObjResponse = CheckEligibilityDIYObj.callAPI();

        return CheckEligibilityDIYObjResponse;


    }

    public Response AcceptTermsAndConditionsEdcDIY(AcceptTermsAndConditionsEdcDIY AcceptTermsAndConditionsEdcDIYObj, Map<String, String> queryParams, Map<String, String> headers) {


        for (Map.Entry m : queryParams.entrySet()) {
            AcceptTermsAndConditionsEdcDIYObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            AcceptTermsAndConditionsEdcDIYObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response AcceptTermsAndConditionsEdcDIYResponse = AcceptTermsAndConditionsEdcDIYObj.callAPI();

        return AcceptTermsAndConditionsEdcDIYResponse;


    }

    public Response validateOrderEdcDIY(validateOrderEdcDIY validateOrderEdcDIYObj, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : body.entrySet()) {
            validateOrderEdcDIYObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            validateOrderEdcDIYObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response validateOrderEdcDIYResponse = validateOrderEdcDIYObj.callAPI();

        return validateOrderEdcDIYResponse;
    }

    public Response UpdateIdentity(UpdateIdentity updObj, Map<String, String> query, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : body.entrySet()) {
            updObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            updObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : query.entrySet()) {
            updObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateIdentityResp = updObj.callAPI();

        return UpdateIdentityResp;

    }

    public Response CreateAccount(CreateAccount AccObj, Map<String, String> query, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : body.entrySet()) {
            AccObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            AccObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : query.entrySet()) {
            AccObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response CreateAccResp = AccObj.callAPI();

        return CreateAccResp;

    }


    // Online Merchant API

    public Response onlinecreateLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        Request.MerchantService.v1.upgradeMid.lead.CreateLead createLeadObject = new Request.MerchantService.v1.upgradeMid.lead.CreateLead();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;
    }

    public Response sfoecreateLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        Request.SFtoOE.CreateBusinessLead createSfBusinessLeadObject = new Request.SFtoOE.CreateBusinessLead();

        for (Map.Entry m : queryParams.entrySet()) {
            createSfBusinessLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createSfBusinessLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createSfBusinessLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createSfBusinessLeadObject.callAPI();


        return createLeadObjectResponse;
    }


    public Response deviceFetchSubCategoryEDC(Map<String, String> headers, Map<String, String> queryParams) {

        FetchSubCategoryODS FetchSubCategoryODSObject = new FetchSubCategoryODS();

        for (Map.Entry m : headers.entrySet()) {
            FetchSubCategoryODSObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            FetchSubCategoryODSObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response FetchSubCategoryODSObjectResponse = FetchSubCategoryODSObject.callAPI();


        return FetchSubCategoryODSObjectResponse;
    }


    // Online Merchant Indigo Onboarding API

    public Response onlinecreateLeadIndigo(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadIndigoOnboarding CreateLeadIndigoOnboardingObject = new CreateLeadIndigoOnboarding();

        queryParams.forEach((key, value) -> CreateLeadIndigoOnboardingObject.addParameter(key.toString(), value.toString()));

        headers.forEach((key, value) -> CreateLeadIndigoOnboardingObject.setHeader(key.toString(), value.toString()));

        body.forEach((key, value) -> CreateLeadIndigoOnboardingObject.getProperties().setProperty(key.toString(), value.toString()));

        return CreateLeadIndigoOnboardingObject.callAPI();
    }


    // Fetch lead status (online merchant)

    public Response onlineFetchLead(Map<String, String> queryParams, Map<String, String> headers) {

        FetchLeadStatus fetchLeadObject = new FetchLeadStatus();

        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchLeadObjectResponse = fetchLeadObject.callAPI();


        return fetchLeadObjectResponse;
    }

// Fetch lead data (online merchant)

    public Response onlineFetchLeadData(Map<String, String> queryParams, Map<String, String> headers) {

        FetchLeadData fetchLeadDataObject = new FetchLeadData();

        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadDataObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchLeadDataObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchLeadDataObjectResponse = fetchLeadDataObject.callAPI();


        return fetchLeadDataObjectResponse;
    }


    //Payments lead status

    public Response onlinePaymentsLead(Map<String, String> queryParams, Map<String, String> headers) {

        PaymentsLeadStatus PaymentsLeadStatusObject = new PaymentsLeadStatus();

        for (Map.Entry m : queryParams.entrySet()) {
            PaymentsLeadStatusObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            PaymentsLeadStatusObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response PaymentsLeadStatusObjectResponse = PaymentsLeadStatusObject.callAPI();


        return PaymentsLeadStatusObjectResponse;
    }


    //Fetch All Business (online merchant)


    public Response onlineFetchAllBusiness(Map<String, String> queryParams, Map<String, String> headers) {

        FetchAllBusiness FetchAllBusinessObject = new FetchAllBusiness();

        for (Map.Entry m : queryParams.entrySet()) {
            FetchAllBusinessObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            FetchAllBusinessObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response FetchAllBusinessObjectResponse = FetchAllBusinessObject.callAPI();


        return FetchAllBusinessObjectResponse;
    }


    public Response onlineResellercreateLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadReseller createLeadResellerObject = new CreateLeadReseller();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadResellerObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadResellerObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadResellerObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadResellerObjectResponse = createLeadResellerObject.callAPI();


        return createLeadResellerObjectResponse;
    }

    public Response onlineIndigoResellerCreateLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadIndigoReseller createLeadResellerObject = new CreateLeadIndigoReseller();

        queryParams.forEach(createLeadResellerObject::addParameter);

        headers.forEach(createLeadResellerObject::setHeader);

        body.forEach((key, value) -> createLeadResellerObject.getProperties().setProperty(key.toString(), value.toString()));

        return createLeadResellerObject.callAPI();
    }

    public Response onlinePaymentLinkCreateLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadPaymentLink createLeadPaymentLinkObject = new CreateLeadPaymentLink();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPaymentLinkObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPaymentLinkObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPaymentLinkObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPaymentLinkObjectResponse = createLeadPaymentLinkObject.callAPI();


        return createLeadPaymentLinkObjectResponse;
    }


    public Response onlineUpdateBusiness(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateBusiness updateBusinessObject = new UpdateBusiness();

        for (Map.Entry m : queryParams.entrySet()) {
            updateBusinessObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : headers.entrySet()) {
            updateBusinessObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateBusinessObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateBusinessObjectResponse = updateBusinessObject.callAPI();


        return UpdateBusinessObjectResponse;
    }


    public Response onlineUpdateBusinessIndividual(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateBusinessIndividual updateBusinessIndividualObject = new UpdateBusinessIndividual();

        for (Map.Entry m : queryParams.entrySet()) {
            updateBusinessIndividualObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        for (Map.Entry m : headers.entrySet()) {
            updateBusinessIndividualObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateBusinessIndividualObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateBusinessIndividualObjectResponse = updateBusinessIndividualObject.callAPI();


        return UpdateBusinessIndividualObjectResponse;
    }

    public Response onlineUpdateAdditonalDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateAddtionalDetails updateAddtionalDetailsObject = new UpdateAddtionalDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            updateAddtionalDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateAddtionalDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateAddtionalDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateAddtionalDetailsObjectResponse = updateAddtionalDetailsObject.callAPI();


        return UpdateAddtionalDetailsObjectResponse;
    }

    public Response onlineUpdateAddtionalDetailsGSTIndigoOnboarding(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateAddtionalDetailsGSTIndigoOnboarding updateAddtionalDetailsGSTIndigoOnboardingObject = new UpdateAddtionalDetailsGSTIndigoOnboarding();

        for (Map.Entry m : queryParams.entrySet()) {
            updateAddtionalDetailsGSTIndigoOnboardingObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateAddtionalDetailsGSTIndigoOnboardingObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateAddtionalDetailsGSTIndigoOnboardingObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateAddtionalDetailsGSTIndigoOnboardingObjectResponse = updateAddtionalDetailsGSTIndigoOnboardingObject.callAPI();


        return UpdateAddtionalDetailsGSTIndigoOnboardingObjectResponse;
    }

    public Response onlineUpdateAddtionalDetailsAdhaarIndigoOnboarding(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateAddtionalDetailsAdhaarIndigoOnboarding updateAddtionalDetailsAdhaarIndigoOnboardingObject = new UpdateAddtionalDetailsAdhaarIndigoOnboarding();

        for (Map.Entry m : queryParams.entrySet()) {
            updateAddtionalDetailsAdhaarIndigoOnboardingObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateAddtionalDetailsAdhaarIndigoOnboardingObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateAddtionalDetailsAdhaarIndigoOnboardingObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateAddtionalDetailsAdhaarIndigoOnboardingObjectResponse = updateAddtionalDetailsAdhaarIndigoOnboardingObject.callAPI();


        return UpdateAddtionalDetailsAdhaarIndigoOnboardingObjectResponse;
    }


    public Response onlineUpdateAdditionalDetailsOwner(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateAdditionalDetailsOwner updateAdditionalDetailsOwnerObject = new UpdateAdditionalDetailsOwner();

        for (Map.Entry m : queryParams.entrySet()) {
            updateAdditionalDetailsOwnerObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateAdditionalDetailsOwnerObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateAdditionalDetailsOwnerObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateAdditionalDetailsOwnerObjectResponse = updateAdditionalDetailsOwnerObject.callAPI();

        return UpdateAdditionalDetailsOwnerObjectResponse;
    }

    public Response onlineUpdateAdditionalDetailsAadhaarRegistered(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateAdditionalDetailsAadhaarRegistered updateAdditionalDetailsAadhaarRegisteredObject = new UpdateAdditionalDetailsAadhaarRegistered();

        for (Map.Entry m : queryParams.entrySet()) {
            updateAdditionalDetailsAadhaarRegisteredObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateAdditionalDetailsAadhaarRegisteredObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateAdditionalDetailsAadhaarRegisteredObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateAdditionalDetailsAadhaarRegisteredObjectResponse = updateAdditionalDetailsAadhaarRegisteredObject.callAPI();

        return UpdateAdditionalDetailsAadhaarRegisteredObjectResponse;
    }

    public Response onlineUpdateAddtionalDetailsAddressIndigoOnboarding(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateAddtionalDetailsAddressIndigoOnboarding updateAddtionalDetailsAddressIndigoOnboardingObject = new UpdateAddtionalDetailsAddressIndigoOnboarding();

        for (Map.Entry m : queryParams.entrySet()) {
            updateAddtionalDetailsAddressIndigoOnboardingObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateAddtionalDetailsAddressIndigoOnboardingObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateAddtionalDetailsAddressIndigoOnboardingObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response UpdateAddtionalDetailsAddressIndigoOnboardingObjectResponse = updateAddtionalDetailsAddressIndigoOnboardingObject.callAPI();


        return UpdateAddtionalDetailsAddressIndigoOnboardingObjectResponse;
    }


    public Response onlineMerchantValidateBank(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {
        ValidateBankDetailsOnline validateBankDetailsResponseObject = new ValidateBankDetailsOnline();

        for (Map.Entry m : queryParams.entrySet()) {
            validateBankDetailsResponseObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            validateBankDetailsResponseObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            validateBankDetailsResponseObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // System.out.println("object is : "+validateBankDetailsResponseObject);
        Response validateBankDetailsResponse = validateBankDetailsResponseObject.callAPI();
        // System.out.println("response is : "+validateBankDetailsResponse);
        return validateBankDetailsResponse;


    }

    public Response onlineMerchantUpdateBank(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {
        UpdateBankDetailOnline updateBankDetailsResponseObject = new UpdateBankDetailOnline();

        for (Map.Entry m : queryParams.entrySet()) {
            updateBankDetailsResponseObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateBankDetailsResponseObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateBankDetailsResponseObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // System.out.println("object is : "+validateBankDetailsResponseObject);
        Response updateBankDetailsResponse = updateBankDetailsResponseObject.callAPI();
        // System.out.println("response is : "+validateBankDetailsResponse);
        return updateBankDetailsResponse;
    }


    public Response onlineMerchantUpdateTnc(Map<String, String> queryParams, Map<String, String> headers) {
        UpdateTncOnline updateTncResponseObject = new UpdateTncOnline();

        for (Map.Entry m : queryParams.entrySet()) {
            updateTncResponseObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateTncResponseObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        // System.out.println("object is : "+validateBankDetailsResponseObject);
        Response updateTncResponse = updateTncResponseObject.callAPI();
        // System.out.println("response is : "+validateBankDetailsResponse);
        return updateTncResponse;
    }

    public Response utilityForDocumentUpload(String docToUpload, String leadId, String custId, String entityType, String solutionType, String solutionTypeLevel2, String sessionToken) throws InterruptedException {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("docProvided", docToUpload);
        queryParams.put("extType", "png");
        queryParams.put("leadId", leadId);
        queryParams.put("custId", custId);
        queryParams.put("entityType", entityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
        queryParams.put("category", "NonTransport");

        if (solutionType.contentEquals("business_lending")) {

            queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");


        }
        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", sessionToken);
        headers.put("custId", custId);

        Response responseObject = UploadDocument(queryParams, headers, uploadFile);

        return responseObject;
    }


    public Response v1UpdateEdcAndroidWithPOS(EdcPostAndroidWithPOS v1EdcObbj, Map<String, String> queryParams, Map<String, String> body, String session_token, String version) {
        //Adding Query Param
        for (Map.Entry m : queryParams.entrySet()) {
            v1EdcObbj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Body
        for (Map.Entry m : body.entrySet()) {
            v1EdcObbj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        v1EdcObbj.setHeader("Content-Type", "application/json");
        v1EdcObbj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v1EdcObbj.setHeader("session_token", session_token);
        v1EdcObbj.setHeader("version", version);
        v1EdcObbj.setHeader("appLanguage", "en");
        v1EdcObbj.setHeader("deviceName", "CPH1859");
        v1EdcObbj.setHeader("client", "androidapp");
        v1EdcObbj.setHeader("imei", "***************");
        v1EdcObbj.setHeader("deviceManufacturer", "OPPO");
        v1EdcObbj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v1EdcObbj.setHeader("Content-Type", "application/json; charset=UTF-8");
        v1EdcObbj.setHeader("isDeviceRooted", "false");
        v1EdcObbj.setHeader("ipAddress", DeviceIP);
        v1EdcObbj.setHeader("isLocationMocked", "false");
        v1EdcObbj.setHeader("latitude", "28.5913173");
        v1EdcObbj.setHeader("longitude", "77.3189828");
        v1EdcObbj.setHeader("osVersion", "9");

        Response v1UpdateEdcAndroidWithPOS = v1EdcObbj.callAPI();

        return v1UpdateEdcAndroidWithPOS;
    }

    public Response v3SubmitMerchant(SubmitMerchant v3SubMerch, String version, String AgentToken) {

        v3SubMerch.setHeader("Content-Type", "application/json");
        v3SubMerch.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        v3SubMerch.setHeader("session_token", AgentToken);
        v3SubMerch.setHeader("version", version);
        v3SubMerch.setHeader("appLanguage", "en");
        v3SubMerch.setHeader("deviceName", "CPH1859");
        v3SubMerch.setHeader("client", "androidapp");
        v3SubMerch.setHeader("imei", "***************");
        v3SubMerch.setHeader("deviceManufacturer", "OPPO");
        v3SubMerch.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        v3SubMerch.setHeader("Content-Type", "application/json; charset=UTF-8");
        v3SubMerch.setHeader("latitude", "28.5913173");
        v3SubMerch.setHeader("longitude", "77.3189828");

        Response v3SubmitMerchResponse = v3SubMerch.callAPI();
        return v3SubmitMerchResponse;
    }


    public Response CreateLeadCashAtPosDIY(CreateLeadDIYCashAtPos CreateLeadDIYCashAtPosObj, Map<String, String> query, Map<String, String> headers, Map<String, String> body) {
        CreateLeadDIYCashAtPosObj.getRequest().urlEncodingEnabled(false);
        for (Map.Entry m : query.entrySet()) {
            CreateLeadDIYCashAtPosObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            CreateLeadDIYCashAtPosObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            CreateLeadDIYCashAtPosObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response CreateLeadDIYCashAtPosResp = CreateLeadDIYCashAtPosObj.callAPI();

        return CreateLeadDIYCashAtPosResp;

    }

    public Response FetchleadBrandEmiDIY(FetchLeadBrandEMIDIY FetchLeadBrandEMIDIYObj, Map<String, String> query, Map<String, String> headers) {
        FetchLeadBrandEMIDIYObj.getRequest().urlEncodingEnabled(false);
        for (Map.Entry m : query.entrySet()) {
            FetchLeadBrandEMIDIYObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            FetchLeadBrandEMIDIYObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response FetchLeadBrandEMIDIYObjResp = FetchLeadBrandEMIDIYObj.callAPI();

        return FetchLeadBrandEMIDIYObjResp;

    }


    public Response FetchDocumentStatusCashAtPosDIY(FetchDocumentDIYCashAtPos FetchDocumentDIYCashAtPosObj, Map<String, String> query, Map<String, String> headers) {

        for (Map.Entry m : query.entrySet()) {
            FetchDocumentDIYCashAtPosObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            FetchDocumentDIYCashAtPosObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response FetchDocumentDIYCashAtPosResp = FetchDocumentDIYCashAtPosObj.callAPI();

        return FetchDocumentDIYCashAtPosResp;

    }

    public Response UploadDocCashAtPosDIY(UploadDocCashAtPosDIY UploadDocCashAtPosDIYObj, Map<String, String> query, Map<String, String> headers) {
        UploadDocCashAtPosDIYObj.getRequest().urlEncodingEnabled(false);
        for (Map.Entry m : query.entrySet()) {
            UploadDocCashAtPosDIYObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            UploadDocCashAtPosDIYObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response UploadDocCashAtPosDIYResp = UploadDocCashAtPosDIYObj.callAPI();

        return UploadDocCashAtPosDIYResp;

    }

    public Response RevisitUnOrgainsed(RevisitUnOrganised organisedRevisit, Map<String, String> query, String version, String token) {
        for (Map.Entry m : query.entrySet()) {
            organisedRevisit.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        organisedRevisit.setHeader("Content-Type", "application/json");
        organisedRevisit.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        organisedRevisit.setHeader("session_token", token);
        organisedRevisit.setHeader("version", version);
        organisedRevisit.setHeader("appLanguage", "en");
        organisedRevisit.setHeader("deviceName", "CPH1859");
        organisedRevisit.setHeader("client", "androidapp");
        organisedRevisit.setHeader("imei", "***************");
        organisedRevisit.setHeader("deviceManufacturer", "OPPO");
        organisedRevisit.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        organisedRevisit.setHeader("Content-Type", "application/json; charset=UTF-8");
        organisedRevisit.setHeader("isDeviceRooted", "false");
        organisedRevisit.setHeader("ipAddress", DeviceIP);
        organisedRevisit.setHeader("isLocationMocked", "false");
        organisedRevisit.setHeader("latitude", "28.5913173");
        organisedRevisit.setHeader("longitude", "77.3189828");
        organisedRevisit.setHeader("osVersion", "9");

        Response organisedRevisitResp = organisedRevisit.callAPI();

        return organisedRevisitResp;

    }

    public Response v1ATSCatagoryRequest(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AtsCatagory atsCatagory = new AtsCatagory();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            atsCatagory.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            atsCatagory.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            atsCatagory.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response FetchAtsCatagoryResponse = atsCatagory.callAPI();

        return FetchAtsCatagoryResponse;
    }

    public Response v1ATSCatagoryFetchDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        SearchATSCatagory searchATSCatagory = new SearchATSCatagory();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            searchATSCatagory.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            searchATSCatagory.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            searchATSCatagory.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response FetchAtsCatagoryResponse = searchATSCatagory.callAPI();

        return FetchAtsCatagoryResponse;
    }

    public Response v1ATSSupplierRequest(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        CreateATSSupplier atsSupplier = new CreateATSSupplier();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            atsSupplier.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            atsSupplier.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            atsSupplier.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response FetchAtsCatagoryResponse = atsSupplier.callAPI();

        return FetchAtsCatagoryResponse;
    }

    public Response v1ATSSupplierFetchDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        SearchATSSupplier searchATSSupplier = new SearchATSSupplier();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            searchATSSupplier.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            searchATSSupplier.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            searchATSSupplier.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response FetchAtsCatagoryResponse = searchATSSupplier.callAPI();

        return FetchAtsCatagoryResponse;
    }

    public Response v1ATSCreateSKUWithoutSubPartRequest(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        CreateSKUWithoutSubpart createSKUWithoutSubpart = new CreateSKUWithoutSubpart();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            createSKUWithoutSubpart.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            createSKUWithoutSubpart.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            createSKUWithoutSubpart.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response createSKUResponse = createSKUWithoutSubpart.callAPI();

        return createSKUResponse;
    }

    public Response v1ATSCreateSKURequest(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        CreateSKU createSKU = new CreateSKU();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            createSKU.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            createSKU.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            createSKU.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response createSKUResponse = createSKU.callAPI();

        return createSKUResponse;
    }

    public Response v1ATSCreateSubSKURequest(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        CreateSubSku createSubSku = new CreateSubSku();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            createSubSku.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            createSubSku.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            createSubSku.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response createSKUResponse = createSubSku.callAPI();

        return createSKUResponse;
    }

    public Response v1ATSFetchSKUDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        FetchSKUDetails fetchSKUDetails = new FetchSKUDetails();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            fetchSKUDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchSKUDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            fetchSKUDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response fetchSKUDetailsResponse = fetchSKUDetails.callAPI();

        return fetchSKUDetailsResponse;
    }

    public Response v1ATSGetSKUListOfParams(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        GetSKUListOfParams getSKUListOfParams = new GetSKUListOfParams();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            getSKUListOfParams.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            getSKUListOfParams.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            getSKUListOfParams.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getSKUListOfParamsResponse = getSKUListOfParams.callAPI();

        return getSKUListOfParamsResponse;
    }

    public Response v1ATSSearchSKU(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        SearchSKU searchSKU = new SearchSKU();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            searchSKU.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            searchSKU.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            searchSKU.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getSearchSKUResponse = searchSKU.callAPI();

        return getSearchSKUResponse;
    }

    public Response v1ATSUpdateSKU(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        UpdateSKU updateSKU = new UpdateSKU();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            updateSKU.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            updateSKU.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            updateSKU.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getUpdateSKUResponse = updateSKU.callAPI();

        return getUpdateSKUResponse;
    }

    public Response v1IndividualBarcodeGenerate(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        GenerateBarcode generateBarcode = new GenerateBarcode();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            generateBarcode.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            generateBarcode.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            generateBarcode.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getGenerateBarcodeResponse = generateBarcode.callAPI();

        return getGenerateBarcodeResponse;
    }

    public Response v1ChildBarcodeGenerate(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        GenerateChildBarcode generateChildBarcode = new GenerateChildBarcode();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            generateChildBarcode.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            generateChildBarcode.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            generateChildBarcode.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getGenerateBarcodeResponse = generateChildBarcode.callAPI();

        return getGenerateBarcodeResponse;
    }

    public Response v1IndividualBarcodeOnboard(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        OnboardBarcode onboardBarcode = new OnboardBarcode();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            onboardBarcode.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            onboardBarcode.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            onboardBarcode.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getOnboardBarcodeResponse = onboardBarcode.callAPI();

        return getOnboardBarcodeResponse;
    }

    public Response v1ChildBarcodeOnboard(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        OnboardChildBarcode onboardChildBarcode = new OnboardChildBarcode();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            onboardChildBarcode.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            onboardChildBarcode.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            onboardChildBarcode.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getOnboardChildBarcodeResponse = onboardChildBarcode.callAPI();

        return getOnboardChildBarcodeResponse;
    }

    public Response v1IndividualBarcodeOnboardInSeries(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        OnboardBarcodeInSeries onboardBarcodeInSeries = new OnboardBarcodeInSeries();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            onboardBarcodeInSeries.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            onboardBarcodeInSeries.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            onboardBarcodeInSeries.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getOnboardBarcodeInSeriesResponse = onboardBarcodeInSeries.callAPI();

        return getOnboardBarcodeInSeriesResponse;
    }

    public Response v1ACLFetchPermission(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        ACLFetchPermission aclFetchPermission = new ACLFetchPermission();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            aclFetchPermission.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            aclFetchPermission.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            aclFetchPermission.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getAclFetchPermissionResponse = aclFetchPermission.callAPI();

        return getAclFetchPermissionResponse;
    }

    public Response v1GenerateQRCode(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        GenerateQRCode generateQRCode = new GenerateQRCode();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            generateQRCode.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            generateQRCode.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            generateQRCode.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response generateQRCodeDetails_Response = generateQRCode.callAPI();

        return generateQRCodeDetails_Response;
    }

    public Response v1GetUSerDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        UserGetDetails userGetDetails = new UserGetDetails();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            userGetDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            userGetDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            userGetDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getUserDetails_Response = userGetDetails.callAPI();

        return getUserDetails_Response;
    }

    public Response v1VerifiedQRCode(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        VerifiedQRCode verifiedQRCode = new VerifiedQRCode();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            verifiedQRCode.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            verifiedQRCode.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            verifiedQRCode.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyQRCodeDetails_Response = verifiedQRCode.callAPI();

        return verifyQRCodeDetails_Response;
    }

    public Response v1FetchBarcodeDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        FetchBarcodeDetails fetchBarcodeDetails = new FetchBarcodeDetails();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            fetchBarcodeDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchBarcodeDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            fetchBarcodeDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyFetchBarcodeDetails_Response = fetchBarcodeDetails.callAPI();

        return verifyFetchBarcodeDetails_Response;
    }

    public Response v1FetchBarcodeDetailsCanAssign(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        FetchBarcodeDetailsCanAssign fetchBarcodeDetailsCanAssign = new FetchBarcodeDetailsCanAssign();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            fetchBarcodeDetailsCanAssign.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchBarcodeDetailsCanAssign.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            fetchBarcodeDetailsCanAssign.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyFetchBarcodeDetailsCanAssign_Response = fetchBarcodeDetailsCanAssign.callAPI();

        return verifyFetchBarcodeDetailsCanAssign_Response;
    }

    public Response v1ValidateCharger(SBValidateNewChargerInBindFlow obj, Map<String, String> body, Map<String, String> headers) {


        for (Map.Entry m : headers.entrySet()) {

            obj.setHeader(m.getKey().toString(), m.getValue().toString());

        }

        for (Map.Entry m : body.entrySet()) {

            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());

        }

        Response fulfilmentBypass = obj.callAPI();

        return fulfilmentBypass;


    }

    public Response v1VasCreateLead(CardSB_Create_Lead_And_Check_Existing_Subscription obj, Map<String, String> body, Map<String, String> headers) {


        for (Map.Entry m : headers.entrySet()) {

            obj.setHeader(m.getKey().toString(), m.getValue().toString());

        }

        for (Map.Entry m : body.entrySet()) {

            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());

        }

        Response fulfilmentBypass = obj.callAPI();

        return fulfilmentBypass;


    }


    public Response v1SBPlanUpgradeCreateLead(SBPlanUpgradeCreateLead obj, Map<String, String> body, Map<String, String> headers) {


        for (Map.Entry m : headers.entrySet()) {

            obj.setHeader(m.getKey().toString(), m.getValue().toString());

        }

        for (Map.Entry m : body.entrySet()) {

            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());

        }

        Response fulfilmentBypass = obj.callAPI();

        return fulfilmentBypass;


    }


    public Response v2FetchBarcodeDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        V2FetchBarcodeDetails v2FetchBarcodeDetails = new V2FetchBarcodeDetails();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            v2FetchBarcodeDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            v2FetchBarcodeDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            v2FetchBarcodeDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyV2FetchBarcodeDetails_Response = v2FetchBarcodeDetails.callAPI();

        return verifyV2FetchBarcodeDetails_Response;
    }

    public Response v1DownloadBarcodeDetails(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        DownloadBarcodeDetails downloadBarcodeDetails = new DownloadBarcodeDetails();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            downloadBarcodeDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            downloadBarcodeDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            downloadBarcodeDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyV1DownloadBarcodeDetails_Response = downloadBarcodeDetails.callAPI();

        return verifyV1DownloadBarcodeDetails_Response;
    }

    public Response v1AssetCountForAvailable(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForAvailable assetCountForAvailable = new AssetCountForAvailable();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForAvailable.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForAvailable.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForAvailable.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForAvailable_Response = assetCountForAvailable.callAPI();

        return verifyAssetCountForAvailable_Response;
    }

    public Response v1AssetCountForAssigned(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForAssigned assetCountForAssigned = new AssetCountForAssigned();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForAssigned.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForAssigned.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForAssigned.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForAssigned_Response = assetCountForAssigned.callAPI();

        return verifyAssetCountForAssigned_Response;
    }

    public Response v1AssetCountForDeployed(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForDeployed assetCountForDeployed = new AssetCountForDeployed();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForDeployed.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForDeployed.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForDeployed.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForDeployed_Response = assetCountForDeployed.callAPI();

        return verifyAssetCountForDeployed_Response;
    }



    public Response v1AssetCountForPending_Assign(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForPending_Assign assetCountForPending_Assign = new AssetCountForPending_Assign();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForPending_Assign.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForPending_Assign.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForPending_Assign.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForPending_Assign_Response = assetCountForPending_Assign.callAPI();

        return verifyAssetCountForPending_Assign_Response;
    }

    public Response v1AssetCountForPending_Acknowledgement(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForPending_Acknowledgement assetCountForPending_Acknowledgement = new AssetCountForPending_Acknowledgement();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForPending_Acknowledgement.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForPending_Acknowledgement.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForPending_Acknowledgement.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForPending_Acknowledgement_Response = assetCountForPending_Acknowledgement.callAPI();

        return verifyAssetCountForPending_Acknowledgement_Response;
    }

    public Response v1AssetCountForUnmapped(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForUnmapped assetCountForUnmapped = new AssetCountForUnmapped();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForUnmapped.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForUnmapped.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForUnmapped.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForUnmapped_Response = assetCountForUnmapped.callAPI();

        return verifyAssetCountForUnmapped_Response;
    }

    public Response v1AssetCountForReturned(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForReturned assetCountForReturned = new AssetCountForReturned();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForReturned.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForReturned.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForReturned.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForReturned_Response = assetCountForReturned.callAPI();

        return verifyAssetCountForReturned_Response;
    }

    public Response v1ListOfAssetDetailsForLost(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        ListOfAssetDetailsForLost listOfAssetDetailsForLost = new ListOfAssetDetailsForLost();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForLost.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForLost.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForLost.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifylistOfAssetDetailsForLost_Response = listOfAssetDetailsForLost.callAPI();

        return verifylistOfAssetDetailsForLost_Response;
    }

    public Response v1ListOfAssetDetailsForAvailable(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        ListOfAssetDetailsForAvailable listOfAssetDetailsForAvailable = new ListOfAssetDetailsForAvailable();

        // Adding Query Params
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForAvailable.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForAvailable.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForAvailable.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response listOfAssetDetailsForAvailableResponse = listOfAssetDetailsForAvailable.callAPI();

        return listOfAssetDetailsForAvailableResponse;
    }

    public Response v1AssetCountForLost(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForLost assetCountForLost = new AssetCountForLost();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForLost.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForLost.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForLost.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForLost_Response = assetCountForLost.callAPI();

        return verifyAssetCountForLost_Response;
    }


    public Response v1ListOfAssetDetailsForAssigned(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        ListOfAssetDetailsForAssigned listOfAssetDetailsForAssigned = new ListOfAssetDetailsForAssigned();

        // Adding Query Params
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForAssigned.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForAssigned.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForAssigned.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response listOfAssetDetailsForAssignedResponse = listOfAssetDetailsForAssigned.callAPI();

        return listOfAssetDetailsForAssignedResponse;
    }

    public Response v1ListOfAssetDetailsForUnmapped(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        ListOfAssetDetailsForUnmapped listOfAssetDetailsForUnmapped = new ListOfAssetDetailsForUnmapped();

        // Adding Query Params
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForUnmapped.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForUnmapped.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForUnmapped.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response listOfAssetDetailsForUnmappedResponse = listOfAssetDetailsForUnmapped.callAPI();

        return listOfAssetDetailsForUnmappedResponse;
    }

    public Response v1ListOfAssetDetailsForReturned(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        ListOfAssetDetailsForReturned listOfAssetDetailsForReturned = new ListOfAssetDetailsForReturned();

        // Adding Query Params
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForReturned.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForReturned.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForReturned.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response listOfAssetDetailsForReturnedResponse = listOfAssetDetailsForReturned.callAPI();

        return listOfAssetDetailsForReturnedResponse;
    }

    public Response v1ListOfAssetDetailsForPending_Acknowledgement(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        ListOfAssetDetailsForPending_Acknowledgement listOfAssetDetailsForPending_Acknowledgement = new ListOfAssetDetailsForPending_Acknowledgement();

        // Adding Query Params
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForPending_Acknowledgement.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForPending_Acknowledgement.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForPending_Acknowledgement.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response listOfAssetDetailsForPending_AcknowledgementResponse = listOfAssetDetailsForPending_Acknowledgement.callAPI();

        return listOfAssetDetailsForPending_AcknowledgementResponse;
    }

    public Response v1ListOfAssetDetailsForPending_Assign(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        ListOfAssetDetailsForPending_Assign listOfAssetDetailsForPending_Assign = new ListOfAssetDetailsForPending_Assign();

        // Adding Query Params
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForPending_Assign.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForPending_Assign.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForPending_Assign.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response listOfAssetDetailsForPending_AssignResponse = listOfAssetDetailsForPending_Assign.callAPI();

        return listOfAssetDetailsForPending_AssignResponse;
    }

    public Response v1ListOfAssetDetailsForDeployed(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        ListOfAssetDetailsForDeployed listOfAssetDetailsForDeployed = new ListOfAssetDetailsForDeployed();

        // Adding Query Params
        for (Map.Entry m : queryParams.entrySet()) {
            listOfAssetDetailsForDeployed.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            listOfAssetDetailsForDeployed.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Add Body Params
        for (Map.Entry m : body.entrySet()) {
            listOfAssetDetailsForDeployed.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response listOfAssetDetailsForDeployedResponse = listOfAssetDetailsForDeployed.callAPI();

        return listOfAssetDetailsForDeployedResponse;
    }

    public Response v1AssetCountForAllStates(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AssetCountForAllStates assetCountForAllStates = new AssetCountForAllStates();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            assetCountForAllStates.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            assetCountForAllStates.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            assetCountForAllStates.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response verifyAssetCountForAllStates_Response = assetCountForAllStates.callAPI();

        return verifyAssetCountForAllStates_Response;
    }

    public Response v1DownloadRequest(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AtsRequestDownload RequestDownloadobj = new AtsRequestDownload();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            RequestDownloadobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            RequestDownloadobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            RequestDownloadobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response DownloadRequestResponse = RequestDownloadobj.callAPI();

        return DownloadRequestResponse;
    }

    public Response v1SkuGroup(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AtsSkuGroup SkuGroupobj = new AtsSkuGroup();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            SkuGroupobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            SkuGroupobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            SkuGroupobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response SkuGroupResponse = SkuGroupobj.callAPI();

        return SkuGroupResponse;
    }

    public Response v1SkuGroupFetch(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        FetchSkuGroups SkuGroupobj = new FetchSkuGroups();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            SkuGroupobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            SkuGroupobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            SkuGroupobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response SkuGroupResponse = SkuGroupobj.callAPI();

        return SkuGroupResponse;
    }

    public Response v1CheckDownloadRequests(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        AtsCheckRequestStatus SkuGroupobj = new AtsCheckRequestStatus();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            SkuGroupobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            SkuGroupobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            SkuGroupobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response SkuGroupResponse = SkuGroupobj.callAPI();

        return SkuGroupResponse;

    }

    public Response v1DownloadFile(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        AtsDownloadFile SkuGroupobj = new AtsDownloadFile();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            SkuGroupobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            SkuGroupobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            SkuGroupobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response SkuGroupResponse = SkuGroupobj.callAPI();

        return SkuGroupResponse;
    }

    public Response FetchLead_UPM(Lead_fetch fetchLeadStatus, Map<String, String> queryParams, Map<String, String> headers) {


        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchLeadStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response fetchLeadRequestResponse = fetchLeadStatus.callAPI();

        return fetchLeadRequestResponse;

    }

    public Response CreateLead(CreateLead_UPM_V2 createLead, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        //    createLead_loanTap createLead_loanTap=new createLead_loanTap();

        for (Map.Entry m : queryParams.entrySet()) {
            createLead.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            createLead.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLead.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = createLead.callAPI();

        return createLeadRequestResponse;

    }

    public Response SaveBusiness_UPM_V2(SaveBusiness saveBusiness, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        //    createLead_loanTap createLead_loanTap=new createLead_loanTap();

        for (Map.Entry m : queryParams.entrySet()) {
            saveBusiness.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveBusiness.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveBusiness.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = saveBusiness.callAPI();

        return createLeadRequestResponse;

    }

    public Response SaveAdditionalDetails_UPM_V2(AdditionalDetails_UPM_V2 saveAddtionalDetails, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        //    createLead_loanTap createLead_loanTap=new createLead_loanTap();

        for (Map.Entry m : queryParams.entrySet()) {
            saveAddtionalDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveAddtionalDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveAddtionalDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = saveAddtionalDetails.callAPI();

        return createLeadRequestResponse;

    }

    public Response SaveIdentityAdhaarDetails(SaveIdentityAdhaarDetails saveIdentityDetails, Map<String, String> queryParams, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            saveIdentityDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveIdentityDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveIdentityDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = saveIdentityDetails.callAPI();

        return createLeadRequestResponse;
    }

    public Response SaveIdentityPANDetails(SaveIdentityPANDetails saveIdentityDetails, Map<String, String> queryParams, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            saveIdentityDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveIdentityDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveIdentityDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = saveIdentityDetails.callAPI();

        return createLeadRequestResponse;
    }

    public Response SaveIdentityGSTINDetails(SaveIdentityGSTINDetails saveIdentityDetails, Map<String, String> queryParams, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            saveIdentityDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveIdentityDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveIdentityDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = saveIdentityDetails.callAPI();

        return createLeadRequestResponse;
    }

    public Response ValidateBankDetails(ValidateBank validateBankDetails, Map<String, String> queryParams, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            validateBankDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            validateBankDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            validateBankDetails.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = validateBankDetails.callAPI();

        return createLeadRequestResponse;
    }

    public Response SaveBankDetails(SaveBankUPM saveBankUPM, Map<String, String> queryParams, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            saveBankUPM.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveBankUPM.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveBankUPM.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = saveBankUPM.callAPI();

        return createLeadRequestResponse;
    }

    public Response SaveRefreeCodeAndWhatsapp(SaveRefreeCodeWhatsapp saveRefreeCodeWhatsapp, Map<String, String> queryParams, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            saveRefreeCodeWhatsapp.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            saveRefreeCodeWhatsapp.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveRefreeCodeWhatsapp.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadRequestResponse = saveRefreeCodeWhatsapp.callAPI();

        return createLeadRequestResponse;
    }

    public Response v1ProfileUpdateForAddVendor(CreateVendor updateRequestObject, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            updateRequestObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            updateRequestObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            updateRequestObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response UpdateRequestResponse = updateRequestObject.callAPI();

        return UpdateRequestResponse;

    }

    /**
     * Method to fetch instrument lead status
     *
     * @param combinedLeadStatus CombinedLeadStatus object
     * @param headers Request headers
     * @param params Query parameters
     * @return Response object
     */
    public Response v1ProfileUpdateInstrumentLeadStatus(CombinedLeadStatus combinedLeadStatus, Map<String, String> headers, Map<String, String> params) {
        // Adding Query Parameters
        for (Map.Entry m : params.entrySet()) {
            combinedLeadStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            combinedLeadStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response response = combinedLeadStatus.callAPI();

        return response;
    }

    /**
     * Method to fetch commission and TnC details
     *
     * @param headers Request headers
     * @param params Query parameters
     * @return Response object
     */
    public Response v1ProfileUpdateCommissionTncs(Map<String, String> headers, Map<String, String> params) {
        Request.MerchantService.v1.profile.update.Commissiontncs commissionTncs = new Request.MerchantService.v1.profile.update.Commissiontncs();

        // Adding Query Parameters
        for (Map.Entry m : params.entrySet()) {
            commissionTncs.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            commissionTncs.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response response = commissionTncs.callAPI();

        return response;
    }

    public Response GetAllBrandsDIY(GetAllBrands GetAllBrandsObj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            GetAllBrandsObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            GetAllBrandsObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response GetAllBrandsObjResp = GetAllBrandsObj.callAPI();

        return GetAllBrandsObjResp;
    }

    public Response ValidateDealerBrandsDIY(ValidateDealer ValidateDealerObj, Map<String, String> queryParams, String token) {
        for (Map.Entry m : queryParams.entrySet()) {
            ValidateDealerObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        ValidateDealerObj.setHeader("session_token", token);

        Response ValidateDealerObjResp = ValidateDealerObj.callAPI();

        return ValidateDealerObjResp;
    }

    public Response deleteDealerMiddlewareService(DeleteDealer DeleteDealer, Map<String, String> body,
                                                  Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            DeleteDealer.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            DeleteDealer.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response DeleteDealerResp = DeleteDealer.callAPI();
        return DeleteDealerResp;
    }

    public Response v1AssetValidate(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        AtsAssetValidate AssetValidateobj = new AtsAssetValidate();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            AssetValidateobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            AssetValidateobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            AssetValidateobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response AssetValidateResponse = AssetValidateobj.callAPI();

        return AssetValidateResponse;
    }

    public Response v1UserProfile(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {

        EmployeeProfile EmployeeProfileobj = new EmployeeProfile();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            EmployeeProfileobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            EmployeeProfileobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            EmployeeProfileobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response UserProfileResponse = EmployeeProfileobj.callAPI();
        return UserProfileResponse;
    }

    public Response merchnantReactivation(Update updateRequestObject, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            updateRequestObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            updateRequestObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            updateRequestObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response UpdateRequestResponse = updateRequestObject.callAPI();

        return UpdateRequestResponse;

    }

    public Response v1AssetUpdate(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        int x = body.size();

        AtsAssetUpdate AssetUpdateobj = null;
        if (x == 2) {
            AssetUpdateobj = new AtsAssetUpdate();
        }

        if (x == 3) {
            AssetUpdateobj = new AtsAssetUpdate(x);

        }

        if (x == 4) {
            char c = 0;
            AssetUpdateobj = new AtsAssetUpdate(c);

        }
        if (x == 5) {
            String s = "";
            AssetUpdateobj = new AtsAssetUpdate(s);

        }
        if (x == 8) {

            float s = (float) 4.9;

            AssetUpdateobj = new AtsAssetUpdate(s);


        }

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            AssetUpdateobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            AssetUpdateobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            AssetUpdateobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response AssetUpdateResponse = AssetUpdateobj.callAPI();

        return AssetUpdateResponse;
    }

    public Response AddPan(AddPan addPanRequest, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            addPanRequest.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            addPanRequest.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            addPanRequest.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response addPANResponse = addPanRequest.callAPI();

        return addPANResponse;
    }

    public Response GetActiveBrandListFromKyb(BrandActiveInKyb BrandActiveInKybObj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            BrandActiveInKybObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            BrandActiveInKybObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response BrandActiveInKybObjResp = BrandActiveInKybObj.callAPI();

        return BrandActiveInKybObjResp;
    }

    public Response FetchMIDICICI(FetchMID fetchMIDICIC, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            fetchMIDICIC.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchMIDICIC.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            fetchMIDICIC.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response fetchMIDResponse = fetchMIDICIC.callAPI();
        return fetchMIDResponse;

    }

    public Response FetchStatusICICI(FetchStatus fetchStatusICIC, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            fetchStatusICIC.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            fetchStatusICIC.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            fetchStatusICIC.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response fetchStatusResponse = fetchStatusICIC.callAPI();
        return fetchStatusResponse;

    }

    public Response CreateLeadWithOutGST(CreateLeadWithOutGSTIN createLeadWithoutGSTIN, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            createLeadWithoutGSTIN.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            createLeadWithoutGSTIN.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            createLeadWithoutGSTIN.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createleadICICI = createLeadWithoutGSTIN.callAPI();
        return createleadICICI;

    }

    public Response CreateLeadWithGST(CreateLeadWithGST createLeadWithGSTIN, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            createLeadWithGSTIN.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            createLeadWithGSTIN.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            createLeadWithGSTIN.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createleadICICI = createLeadWithGSTIN.callAPI();
        return createleadICICI;
    }

    public Response AddDealerBrandEmi(AddDealer AddDealerObj, Map<String, String> headers, Map<String, String> body) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            AddDealerObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            AddDealerObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response AddDealerObjResp = AddDealerObj.callAPI();
        return AddDealerObjResp;

    }

    public Response GetBrandDealerBrandEmi(GetBrandDealer GetBrandDealerObj, Map<String, String> headers, Map<String, String> body) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            GetBrandDealerObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            GetBrandDealerObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response GetBrandDealerObjResp = GetBrandDealerObj.callAPI();
        return GetBrandDealerObjResp;
    }

    public Response fetchScreenDetails(FetchScreenDetails fetchScreenDetails, Map<String, String> query, String version, String session_token) {

        for (Map.Entry m : query.entrySet()) {
            fetchScreenDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        fetchScreenDetails.setHeader("Content-Type", "application/json");
        fetchScreenDetails.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        fetchScreenDetails.setHeader("session_token", session_token);
        fetchScreenDetails.setHeader("version", version);
        fetchScreenDetails.setHeader("appLanguage", "en");
        fetchScreenDetails.setHeader("deviceName", "CPH1859");
        fetchScreenDetails.setHeader("client", "androidapp");
        fetchScreenDetails.setHeader("imei", "***************");
        fetchScreenDetails.setHeader("deviceManufacturer", "OPPO");
        fetchScreenDetails.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        fetchScreenDetails.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response GetAssistedMerchant = fetchScreenDetails.callAPI();

        System.out.println(fetchScreenDetails.toString());

        return GetAssistedMerchant;
    }

    public Response validateOtp(ValidateOtp ValidateOTPobj, Map<String, String> query, String version, String session_token, String content_type) {
        for (Map.Entry m : query.entrySet()) {
            ValidateOTPobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        ValidateOTPobj.setHeader("Content-Type", content_type);
        ValidateOTPobj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        ValidateOTPobj.setHeader("session_token", session_token);
        ValidateOTPobj.setHeader("version", version);
        ValidateOTPobj.setHeader("appLanguage", "en");
        ValidateOTPobj.setHeader("deviceName", "CPH1859");
        ValidateOTPobj.setHeader("client", "androidapp");
        ValidateOTPobj.setHeader("imei", "***************");
        ValidateOTPobj.setHeader("deviceManufacturer", "OPPO");
        ValidateOTPobj.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        ValidateOTPobj.setHeader("Content-Type", "application/json; charset=UTF-8");

        Response GetAssistedMerchant = ValidateOTPobj.callAPI();

        System.out.println(ValidateOTPobj.toString());

        return GetAssistedMerchant;
    }

    public Response CreateTerminalInPGMethod(CreateTerminalInPG CreateTerminalInPGObj, Map<String, String> headers, Map<String, String> body) {

        for (Map.Entry m : headers.entrySet()) {
            CreateTerminalInPGObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            CreateTerminalInPGObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response CreateTerminalInPGObjResp = CreateTerminalInPGObj.callAPI();
        return CreateTerminalInPGObjResp;
    }

    public Response ActiveTerminalInPGMethod(ActiveTerminalInPG ActiveTerminalInPGObj, Map<String, String> headers, Map<String, String> body) {

        for (Map.Entry m : headers.entrySet()) {
            ActiveTerminalInPGObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            ActiveTerminalInPGObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response ActiveTerminalInPGObjResp = ActiveTerminalInPGObj.callAPI();
        return ActiveTerminalInPGObjResp;
    }

    public Response getAllResources(GetallResources getallResources, Map<String, String> headers) {

        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            getallResources.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response fetchAllResources = getallResources.callAPI();
        return fetchAllResources;

    }

    public Response v1UnmapEdcUpgradeReasons(UnmapEDCUpgradeReasons UnmapEDCUpgradeReasonsObj, String session_token, String version) {

        UnmapEDCUpgradeReasonsObj.setHeader("Content-Type", "application/json");
        UnmapEDCUpgradeReasonsObj.setHeader("session_token", session_token);
        UnmapEDCUpgradeReasonsObj.setHeader("version", version);

        Response UnmapEDCUpgradeReasonsObjResp = UnmapEDCUpgradeReasonsObj.callAPI();

        return UnmapEDCUpgradeReasonsObjResp;
    }

    public Response EdcUpgradeCreateLead(CreateUnmapEdcLead CreateUnmapEdcLeadObj, Map<String, String> body, String session_token, String version, Map<String, String> query) {

        for (Map.Entry m : body.entrySet()) {
            CreateUnmapEdcLeadObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : query.entrySet()) {
            CreateUnmapEdcLeadObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        CreateUnmapEdcLeadObj.setHeader("Content-Type", "application/json");
        CreateUnmapEdcLeadObj.setHeader("session_token", session_token);
        CreateUnmapEdcLeadObj.setHeader("version", version);


        Response CreateUnmapEdcLeadResponse = CreateUnmapEdcLeadObj.callAPI();

        return CreateUnmapEdcLeadResponse;
    }

    public Response EdcUpgradeFetchPlan(FetchEdcUpgradePlans FetchEdcUpgradePlansObj, Map<String, String> headers, Map<String, String> query) {

        for (Map.Entry m : headers.entrySet()) {
            FetchEdcUpgradePlansObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : query.entrySet()) {
            FetchEdcUpgradePlansObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response FetchEdcUpgradePlansObjResp = FetchEdcUpgradePlansObj.callAPI();

        return FetchEdcUpgradePlansObjResp;
    }

    public Response v1fetchLeadStatus(Lead LeadObj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            LeadObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            LeadObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response v1LeadObj = LeadObj.callAPI();
        System.out.println(LeadObj.toString());

        return v1LeadObj;
    }

    public Response getMerchantAgreementTnC(GetMerchantAgreementTnC LeadObj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            LeadObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            LeadObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response v1LeadObj = LeadObj.callAPI();
        System.out.println(LeadObj.toString());

        return v1LeadObj;
    }

    public Response merchantAgreementTnCAccept(MerchantAgreementTnCAccept LeadObj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            LeadObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            LeadObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response v1LeadObj = LeadObj.callAPI();
        System.out.println(LeadObj.toString());

        return v1LeadObj;
    }

    public Response AudioDetails(AudioDetails audioDetails, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            audioDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            audioDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response Audiodetailskalsobj = audioDetails.callAPI();
        System.out.println(audioDetails.toString());

        return Audiodetailskalsobj;
    }

    public Response AdditionalDetails(AdditionalDetailsFetchScreen AdditionalDetailsFetchScreenObj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            AdditionalDetailsFetchScreenObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            AdditionalDetailsFetchScreenObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response Additionaldetailskalsobj = AdditionalDetailsFetchScreenObj.callAPI();
        System.out.println(AdditionalDetailsFetchScreenObj.toString());

        return Additionaldetailskalsobj;
    }


    public Response offlineToOnlinecreateV3Lead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadOfflineToOnline CreateLeadOfflineToOnlineObject = new CreateLeadOfflineToOnline();

        for (Map.Entry m : queryParams.entrySet()) {
            CreateLeadOfflineToOnlineObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            CreateLeadOfflineToOnlineObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            CreateLeadOfflineToOnlineObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response CreateLeadOfflineToOnlineObjectResponse = CreateLeadOfflineToOnlineObject.callAPI();


        return CreateLeadOfflineToOnlineObjectResponse;
    }

    public Response v1SoundBoxBypassQnA(Map<String, String> queryParams) {
        SoundboxByPassQnA SBobj = new SoundboxByPassQnA();

        for (Map.Entry m : queryParams.entrySet()) {
            SBobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response SBobjResponse = SBobj.callAPI();
        return SBobjResponse;
    }


    public Response v1SBMerchantDetails(SBMerchantDetails sbobj, Map<String, String> headers) {
        // String custid="1001891503" ;


        // Adding Query Paramas
        // for (Map.Entry m : queryParams.entrySet()) {
        // sbobj.addParameter(m.getKey().toString(), m.getValue().toString());
        // }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            sbobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        // for (Map.Entry m : body.entrySet()) {
        //  sbobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        // }

        //Hit API to get Response
        Response getSBFetchPermissionResponse = sbobj.callAPI();

        return getSBFetchPermissionResponse;
    }

    public Response v1SBPinCode(Map<String, String> headers, Map<String, String> body) {

        SBPinCode sbpincodeobj = new SBPinCode();

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            sbpincodeobj.setHeader(m.getKey().toString(), m.getValue().toString());

        }
        for (Map.Entry m : body.entrySet()) {
            sbpincodeobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response sbpincodeobjResponse = sbpincodeobj.callAPI();

        return sbpincodeobjResponse;

    }

    public Response v1SentOtp(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        SentOTPSB sentotpobj = new SentOTPSB();

        // Adding Query Paramas
        for (Map.Entry m : queryParams.entrySet()) {
            sentotpobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            sentotpobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            sentotpobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response getsentotpobjResponse = sentotpobj.callAPI();

        return getsentotpobjResponse;
    }

    public Response v1ValidateQr(QrValidate v1SoundboxQrValidateobj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : queryParams.entrySet()) {
            v1SoundboxQrValidateobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            v1SoundboxQrValidateobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response getvalidateotpobjResponse = v1SoundboxQrValidateobj.callAPI();
        return getvalidateotpobjResponse;
    }

    public Response fetchdevices(FetchDevicedIot FetchDevicedIotobj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            FetchDevicedIotobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response FetchDevicedIotResponse = FetchDevicedIotobj.callAPI();
        return FetchDevicedIotResponse;
    }

    public Response fetchtncsb(Fetchtnc fetchtncobj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            fetchtncobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            fetchtncobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response FetchTncResponse = fetchtncobj.callAPI();
        return FetchTncResponse;
    }

    public Response fetchmerchantbasicdetails(fetchmerchantdevicedetails fetchmerchantdevicedetails, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            fetchmerchantdevicedetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            fetchmerchantdevicedetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response FetchdevicedetailsResponse = fetchmerchantdevicedetails.callAPI();
        return FetchdevicedetailsResponse;
    }

    public Response validatedevice(Validatedevice validatedeviceobj, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            validatedeviceobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            validatedeviceobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response validatedeviceResponse = validatedeviceobj.callAPI();
        return validatedeviceResponse;
    }

    public Response validateEDCQr(DeviceUpgradevalidateEDCQr validatedeviceobj, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            validatedeviceobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            validatedeviceobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response validatedeviceResponse = validatedeviceobj.callAPI();
        return validatedeviceResponse;
    }


    public Response validateAndUpdateSimDetails(SimActivation simObj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            simObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            simObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response validatedeviceResponse = simObj.callAPI();
        return validatedeviceResponse;

    }

    public Response bindDeviceByOtp(BindDeviceByOTP otpObj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            otpObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            otpObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response validatedeviceResponse = otpObj.callAPI();
        return validatedeviceResponse;

    }

    public Response diyUpdateDevice(DIYUpdateDeviceID diyObj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            diyObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            diyObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response validatedeviceResponse = diyObj.callAPI();
        return validatedeviceResponse;

    }

    public Response QrSendOtp(QrMappingSendOtp obj, Map<String, String> body, Map<String, String> headers, Map<String, String> params) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : params.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = obj.callAPI();
        return validatedeviceResponse;

    }

    public Response QrValidateOtp(QrMappingValidateOTPLeadCreation obj, Map<String, String> body, Map<String, String> headers, Map<String, String> params) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : params.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = obj.callAPI();
        return validatedeviceResponse;

    }


    public Response validateBeat(ValidateBeatDetails beatObj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            beatObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            beatObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response validatedeviceResponse = beatObj.callAPI();
        return validatedeviceResponse;

    }

    public Response v1FulfilmentBypass(SbFulfilmentBypass obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response fulfilmentBypass = obj.callAPI();
        return fulfilmentBypass;

    }

    public Response updateDeviceNQRv2(DIYV2SBUpdateDeviceAndQR obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response fulfilmentBypass = obj.callAPI();
        return fulfilmentBypass;

    }


    public Response validateNSaveAsset(ValidateAndSaveAssets Obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = Obj.callAPI();
        return validatedeviceResponse;

    }

    public Response unbindDevice(CreateDeviceUnbindLead Obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = Obj.callAPI();
        return validatedeviceResponse;

    }


    public Response fetchDeviceQuestions(FetchDeviceQuestions obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        obj.getRequest().urlEncodingEnabled(false);
        return obj.callAPI();

    }

    public Response fetchEligibleBanks(QRMappingFetchEligibleBanks obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        obj.getRequest().urlEncodingEnabled(false);
        return obj.callAPI();

    }

    public Response fetchStaticPrefs(QRMappingGetStaticPref obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        obj.getRequest().urlEncodingEnabled(false);
        return obj.callAPI();

    }


    public Response fetchAddOnDetails(AddOnDetails obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        obj.getRequest().urlEncodingEnabled(false);
        return obj.callAPI();

    }

    public Response updateAssetsAnswer(UpdateAssetAnswers Obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = Obj.callAPI();
        return validatedeviceResponse;

    }

    public Response createSbVASLead(SoundboxVASLeadCreation Obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = Obj.callAPI();
        return validatedeviceResponse;

    }

    public Response createSimReplace(SimReplacementValidateOTP Obj, Map<String, String> body, Map<String, String> headers, Map<String, String> params) {

        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : params.entrySet()) {
            Obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = Obj.callAPI();
        return validatedeviceResponse;

    }

    public Response fetchLeadsForDIY(DIYFetchAllocated obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        obj.getRequest().urlEncodingEnabled(false);
        return obj.callAPI();

    }

    public Response getmerchant(GetMerchantid getmidobj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            getmidobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            getmidobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response getmidResponse = getmidobj.callAPI();
        return getmidResponse;
    }

    public Response updateSBShopInsurance(SBShopInsuranceUpdateLead Obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = Obj.callAPI();
        return validatedeviceResponse;

    }

    public Response prevalidatePaymentQRNew(SBPreValidatePaymentQRNew Obj, Map<String, String> body, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response validatedeviceResponse = Obj.callAPI();
        return validatedeviceResponse;

    }


    public Response fetchTncotp(fetchtncotp fetchtncobj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            fetchtncobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            fetchtncobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetchtncobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchTncotpResponse = fetchtncobj.callAPI();
        return fetchTncotpResponse;
    }

    public Response SBDIYMidValidation(SBDIYValidateMID MID, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            MID.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            MID.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchTncotpResponse = MID.callAPI();
        return fetchTncotpResponse;
    }

    public Response fetchNextScreen(FetchNextScreenSB obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchTncotpResponse = obj.callAPI();
        return fetchTncotpResponse;
    }

    public Response SBViewMerchantSpecificLead(SBDIYViewMerchantSpecificLead MID, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            MID.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            MID.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchTncotpResponse = MID.callAPI();
        return fetchTncotpResponse;
    }


    public Response InsuranceEligibility(SbInsuranceEligibility MID, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            MID.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            MID.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchTncotpResponse = MID.callAPI();
        return fetchTncotpResponse;
    }

    public Response v3SentOtp(SendOTPV1 callV3Otp, String entityType, String solutionType, String session_token, String version, String mobile, String userType) {
        //SendOtp callV3Otp = new SendOtp();

        callV3Otp.addParameter("entityType", entityType);
        callV3Otp.addParameter("solutionType", solutionType);

        callV3Otp.getProperties().setProperty("mobile", mobile);
        callV3Otp.getProperties().setProperty("userType", userType);
        callV3Otp.getProperties().setProperty("call", "false");
        callV3Otp.setHeader("X-SRC", "GGClient");
        callV3Otp.setHeader("version", version);
        callV3Otp.setHeader("X-MW-CHECKSUM-V3", "uXhKSnblZ1h/QfhKiNd99hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1KNCKHYvLBOZ/ncVs6JLj5cysSNmq6qfLg=");
        callV3Otp.setHeader("session_token", session_token);
        callV3Otp.setHeader("isDeviceRooted", "false");
        callV3Otp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        callV3Otp.setHeader("X-MW-URL-CHECKSUM-V3", "wytdcFjiazJKHY4QktpY9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1KNCKHYvLBPYDTBkxfifxvKSKWlLSyW4Q4=");
        callV3Otp.setHeader("Content-Type", "application/json; charset=UTF-8");
        callV3Otp.setHeader("Host", "goldengate-staging5.paytm.com");
        callV3Otp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        Response OtpResponse = callV3Otp.callAPI();
        return OtpResponse;

    }

    public Response CreatenewSBLead(CreatenewSBLead obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response CreateLeadResponse = obj.callAPI();
        return CreateLeadResponse;
    }

    public Response fetchdeploymentdetailsMethod(Map<String, String> headers, Map<String, String> queryparams) {
        Fetchdeploymentdetails fddobj = new Fetchdeploymentdetails();

        for (Map.Entry m : headers.entrySet()) {
            fddobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryparams.entrySet()) {
            fddobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response fddobjresp = fddobj.callAPI();
        return fddobjresp;
    }

    public Response createMCOLeadPlanMethod(createMCOLead createMCOLead, Map<String, String> queryParams,
                                            Map<String, String> headers, Map<Object, Object> body) {

        for (Map.Entry m : queryParams.entrySet()) {
            createMCOLead.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createMCOLead.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createMCOLead.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


// Hit API to get Response
        Response createMCOLeadResp = createMCOLead.callAPI();
        return createMCOLeadResp;
    }

    public Response addBankPlanmethod(addBank addbank, Map<String, String> queryParams,
                                      Map<String, String> headers, Map<String, Object> body) {

        for (Map.Entry m : queryParams.entrySet()) {
            addbank.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addbank.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        System.out.println(body.size());
        for (Map.Entry m : body.entrySet()) {
            addbank.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
            System.out.print(m.getKey().toString());
            System.out.println(m.getValue().toString());
        }


// Hit API to get Response
        Response addbankResp = addbank.callAPI();
        return addbankResp;
    }

    public Response fetchBankPlanmethod(fetchBank fetchBankobjNew, Map<String, String> queryParams,
                                        Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            fetchBankobjNew.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchBankobjNew.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        // Hit API to get Response
        Response fetchBankobjNewResp = fetchBankobjNew.callAPI();
        return fetchBankobjNewResp;
    }

    public Response getMIDStatusPlanMethod(getMerchantMIDStatus getMerchantMIDStatus, Map<String, String> queryParams,
                                           Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            getMerchantMIDStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getMerchantMIDStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }

// Hit API to get Response
        Response getMerchantMIDStatusResp = getMerchantMIDStatus.callAPI();
        return getMerchantMIDStatusResp;
    }


    public Response fetchMCOLeadPlanMethod(fetchMCOLead fetchMCOLead, Map<String, String> queryParams,
                                           Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            fetchMCOLead.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchMCOLead.setHeader(m.getKey().toString(), m.getValue().toString());
        }


// Hit API to get Response
        Response fetchMCOLeadResp = fetchMCOLead.callAPI();
        return fetchMCOLeadResp;
    }

    public Response sendOTPLeadPlanMethod(sendOTPLead sendOTPLead, Map<String, String> queryParams,
                                          Map<String, String> headers, Map<Object, Object> body) {

        for (Map.Entry m : queryParams.entrySet()) {
            sendOTPLead.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            sendOTPLead.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            sendOTPLead.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


// Hit API to get Response
        Response sendOTPLeadResp = sendOTPLead.callAPI();
        return sendOTPLeadResp;
    }

    public Response getBankPlanMethod(getBank getBank, Map<String, String> queryParams,
                                      Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            getBank.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getBank.setHeader(m.getKey().toString(), m.getValue().toString());
        }


// Hit API to get Response
        Response getBankResp = getBank.callAPI();
        return getBankResp;
    }

    public Response getCKYCMethod(cKYC cKYC, Map<String, String> queryParams,
                                  Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            cKYC.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            cKYC.setHeader(m.getKey().toString(), m.getValue().toString());
        }


// Hit API to get Response
        Response getBankResp = cKYC.callAPI();
        return getBankResp;
    }

    public Response getbeauruPlanMethod(fetchBeauruStatus fetchBeauruStatus, Map<String, String> queryParams,
                                        Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            fetchBeauruStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchBeauruStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }


// Hit API to get Response
        Response fetchbeauruResp = fetchBeauruStatus.callAPI();
        return fetchbeauruResp;
    }

    public Response subpartassetupdatetestmethod(Subpartassetupdate obj, Map<String, String> headers, Map<String, Object> body) {


        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response objresp = obj.callAPI();
        return objresp;
    }


    public Response getBusinessPlanMethod(getBusiness getBusiness, Map<String, String> queryParams,
                                          Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            getBusiness.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getBusiness.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        // Hit API to get Response
        Response getLeadStatusResp = getBusiness.callAPI();
        return getLeadStatusResp;
    }

    public Response fetchqnaPlanMethod(fetchqnadevices fetchqnadevices, Map<String, String> queryParams,
                                       Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            fetchqnadevices.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchqnadevices.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        // Hit API to get Response
        Response fetchqnadevicesResp = fetchqnadevices.callAPI();
        return fetchqnadevicesResp;
    }

    public Response getLeadStatusPlanMethod(getLeadStatus getLeadStatus, Map<String, String> queryParams,
                                            Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            getLeadStatus.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getLeadStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        // Hit API to get Response
        Response getLeadStatusResp = getLeadStatus.callAPI();
        return getLeadStatusResp;
    }

    public Response agentlogin1(DeviceIdentifier agentlogin1obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            agentlogin1obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            agentlogin1obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response agentlogin1objresp = agentlogin1obj.callAPI();
        return agentlogin1objresp;
    }

    public Response agentlogin2(AgentLoginApi2 agentlogin2obj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            agentlogin2obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            agentlogin2obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            agentlogin2obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response agentlogin2objresp = agentlogin2obj.callAPI();
        return agentlogin2objresp;
    }

    public Response validateOTPSB(ValidateotpSB validateotpsbobj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            validateotpsbobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            validateotpsbobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            validateotpsbobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response validateotpsbobjresp = validateotpsbobj.callAPI();
        return validateotpsbobjresp;
    }

    public Response posId(Posid posidobj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            posidobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            posidobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response posidobjResp = posidobj.callAPI();
        return posidobjResp;
    }


    public Response v3SentOtpFseDiy(SendOtpFseDiy callV3Otp, String entityType, String solutionType, String session_token, String version, String mobile, String userType, String custId) {
        //SendOtp callV3Otp = new SendOtp();

        callV3Otp.addParameter("entityType", entityType);
        callV3Otp.addParameter("solutionType", solutionType);

        callV3Otp.getProperties().setProperty("mobile", mobile);
        callV3Otp.getProperties().setProperty("custId", custId);
        callV3Otp.getProperties().setProperty("userType", userType);

        callV3Otp.setHeader("Host", "goldengate-staging6.paytm.com");
        callV3Otp.setHeader("X-SRC", "GGClient");
        callV3Otp.setHeader("version", version);
        callV3Otp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        callV3Otp.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        callV3Otp.setHeader("session_token", session_token);
        callV3Otp.setHeader("isDeviceRooted", "false");
        callV3Otp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        callV3Otp.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        callV3Otp.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response OtpResponse = callV3Otp.callAPI();
        return OtpResponse;

    }

    public Response fetchLeaddetails(SBFetchLeadDetails fetchleaddetailsobj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            fetchleaddetailsobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            fetchleaddetailsobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchleaddetailsResp = fetchleaddetailsobj.callAPI();
        return fetchleaddetailsResp;
    }

    public Response fetchPlansSB(FetchPlans fetchplansobj, Map<String, String> headers , Map<String, Object> body) {

        for (Map.Entry m : headers.entrySet()) {
            fetchplansobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetchplansobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchplansobjresp = fetchplansobj.callAPI();
        return fetchplansobjresp;
    }


    public Response partialSaving(partialSaveCall partialSaveCallobj, Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            partialSaveCallobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            partialSaveCallobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            partialSaveCallobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchleaddetailsResp = partialSaveCallobj.callAPI();
        return fetchleaddetailsResp;
    }


    public Response partialSavingNew(partialSaveCall partialSaveCallobj, Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            partialSaveCallobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            partialSaveCallobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            partialSaveCallobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchleaddetailsResp = partialSaveCallobj.callAPI();
        return fetchleaddetailsResp;
    }


    public Response createDeviceLeadPost(createDeviceLead createDeviceLeadobj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            createDeviceLeadobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            createDeviceLeadobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchleaddetailsResp = createDeviceLeadobj.callAPI();
        return fetchleaddetailsResp;
    }


    public Response fetchDeviceLeadGet(devicedetailsfetch devicedetailsfetchobj, Map<String, Object> queryParams, Map<String, Object> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            devicedetailsfetchobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            devicedetailsfetchobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchleaddetailsResp = devicedetailsfetchobj.callAPI();
        return fetchleaddetailsResp;
    }

    public Response fetchDeviceSummary(deviceSummary deviceSummaryobjnew, Map<String, Object> queryParams, Map<String, Object> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            deviceSummaryobjnew.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            deviceSummaryobjnew.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchDeviceSummaryResp = deviceSummaryobjnew.callAPI();
        return fetchDeviceSummaryResp;
    }

    public Response cartSummaryService(cartsave cartsaveobjnew, Map<String, Object> queryParams, Map<String, Object> headers, Map<String, Object> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            cartsaveobjnew.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            cartsaveobjnew.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            cartsaveobjnew.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response cartSummaryServiceResp = cartsaveobjnew.callAPI();
        return cartSummaryServiceResp;
    }

    public Response ordersaveResponse(orderSave orderSaveobj, Map<String, Object> queryParams, Map<String, Object> headers, Map<String, Object> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            orderSaveobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            orderSaveobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            orderSaveobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response orderSaveobjeResp = orderSaveobj.callAPI();
        return orderSaveobjeResp;
    }

    public Response fetchDeviceAddOns(deviceAddons deviceAddonsobjnew, Map<String, Object> queryParams, Map<String, Object> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            deviceAddonsobjnew.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            deviceAddonsobjnew.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response deviceAddonsobjnewResp = deviceAddonsobjnew.callAPI();
        return deviceAddonsobjnewResp;
    }

    public Response ConfirmPlanGet(confirmdevicePlanEDC confirmdevicePlanEDCnewobj, Map<String, Object> queryParams, Map<String, Object> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            confirmdevicePlanEDCnewobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            confirmdevicePlanEDCnewobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response confirmdevicePlanEDCnewobjResp = confirmdevicePlanEDCnewobj.callAPI();
        return confirmdevicePlanEDCnewobjResp;
    }

    public Response v1SubmitFseDiy(SubmitLeadDetail CallV1FseSubmit, String entityType, String solutionType, String leadId, String session_token, String version, String NAME_AS_PER_AADHAR, String merchant_dob, String gender, String aadhar_not_readable, String aadhar_ref_no, String screenName, String partialSave) {
        //SendOtp callV3Otp = new SendOtp();

        CallV1FseSubmit.addParameter("entityType", entityType);
        CallV1FseSubmit.addParameter("solutionType", solutionType);
        CallV1FseSubmit.addParameter("leadId", leadId);

        CallV1FseSubmit.getProperties().setProperty("leadID", leadId);
        CallV1FseSubmit.getProperties().setProperty("NAME_AS_PER_AADHAR", NAME_AS_PER_AADHAR);
        CallV1FseSubmit.getProperties().setProperty("MERCHANT_DOB", merchant_dob);
        CallV1FseSubmit.getProperties().setProperty("GENDER", gender);
        CallV1FseSubmit.getProperties().setProperty("AADHAR_NO_NOT_READABLE", aadhar_not_readable);
        CallV1FseSubmit.getProperties().setProperty("AADHAR_REF_NUMBER", aadhar_ref_no);
        CallV1FseSubmit.getProperties().setProperty("screenName", screenName);
        CallV1FseSubmit.getProperties().setProperty("partialSave", partialSave);


        CallV1FseSubmit.setHeader("Host", "goldengate-staging6.paytm.com");
        CallV1FseSubmit.setHeader("X-SRC", "GGClient");
        CallV1FseSubmit.setHeader("version", version);
        CallV1FseSubmit.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        CallV1FseSubmit.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        CallV1FseSubmit.setHeader("session_token", session_token);
        CallV1FseSubmit.setHeader("isDeviceRooted", "false");
        CallV1FseSubmit.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        CallV1FseSubmit.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        CallV1FseSubmit.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response LeadSubmitResponse = CallV1FseSubmit.callAPI();
        return LeadSubmitResponse;

    }

    public Response fetchVettedKeys(FetchVettedKeys FetchVettedKeysObj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            FetchVettedKeysObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            FetchVettedKeysObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response FetchVettedKeysObjResp = FetchVettedKeysObj.callAPI();
        return FetchVettedKeysObjResp;
    }

    public Response tncAcceptInKyb(TncAccept TncAcceptObj, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : body.entrySet()) {
            TncAcceptObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            TncAcceptObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response TncAcceptObjResp = TncAcceptObj.callAPI();
        return TncAcceptObjResp;
    }

    public Response tncUserInKyb(TncUser TncUserObj, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : body.entrySet()) {
            TncUserObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            TncUserObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response TncUserObjResp = TncUserObj.callAPI();
        return TncUserObjResp;
    }

    public Response v1ValidateOtpFseDiy(ValidateOtpFseDiy callV3validateOtp, String entityType, String solutionType, String session_token, String version, String mobile, String userType, String state, String OTP) throws JSchException, IOException {
        callV3validateOtp.addParameter("entityType", entityType);
        callV3validateOtp.addParameter("solutionType", solutionType);

        callV3validateOtp.setHeader("Content-Type", "application/json");
        callV3validateOtp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        callV3validateOtp.setHeader("session_token", session_token);
        callV3validateOtp.setHeader("version", version);
        callV3validateOtp.setHeader("appLanguage", "en");
        callV3validateOtp.setHeader("deviceName", "CPH1859");
        callV3validateOtp.setHeader("client", "androidapp");
        callV3validateOtp.setHeader("imei", "***************");
        callV3validateOtp.setHeader("deviceManufacturer", "OPPO");
        callV3validateOtp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        callV3validateOtp.setHeader("Content-Type", "application/json; charset=UTF-8");
        callV3validateOtp.setHeader("latitude", "28.5913173");
        callV3validateOtp.setHeader("longitude", "77.3189828");


        callV3validateOtp.getProperties().setProperty("mobile", mobile);
        callV3validateOtp.getProperties().setProperty("userType", userType);
        callV3validateOtp.getProperties().setProperty("otp", OTP);
        callV3validateOtp.getProperties().setProperty("state", state);


        Response ValidateOtp = callV3validateOtp.callAPI();

        System.out.println(callV3validateOtp.toString());


        return ValidateOtp;
    }


    public Response v1SubmitFseDiy(SubmitLeadDetail CallV1FseSubmit, String entityType, String solutionType, String leadId, String session_token, String version, String businessType, String businessEntity, String pan_number, String is_pan_skipped, String screenName, String partialSave) {
        //SendOtp callV3Otp = new SendOtp();

        CallV1FseSubmit.addParameter("entityType", entityType);
        CallV1FseSubmit.addParameter("solutionType", solutionType);
        CallV1FseSubmit.addParameter("leadId", leadId);


        CallV1FseSubmit.getProperties().setProperty("pan", pan_number);
        CallV1FseSubmit.getProperties().setProperty("PAN", pan_number);
        CallV1FseSubmit.getProperties().setProperty("BUSINESS_TYPE", businessType);
        CallV1FseSubmit.getProperties().setProperty("BUSINESS_ENTITY", businessEntity);
        CallV1FseSubmit.getProperties().setProperty("IS_PAN_SKIPPED", is_pan_skipped);
        //CallV1FseSubmit.getProperties().setProperty("screenName", screenName);
        CallV1FseSubmit.getProperties().setProperty("partialSave", partialSave);


        CallV1FseSubmit.setHeader("Host", "goldengate-staging6.paytm.com");
        CallV1FseSubmit.setHeader("X-SRC", "GGClient");
        CallV1FseSubmit.setHeader("version", version);
        CallV1FseSubmit.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        CallV1FseSubmit.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        CallV1FseSubmit.setHeader("session_token", session_token);
        CallV1FseSubmit.setHeader("isDeviceRooted", "false");
        CallV1FseSubmit.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        CallV1FseSubmit.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        CallV1FseSubmit.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response LeadSubmitResponse = CallV1FseSubmit.callAPI();
        return LeadSubmitResponse;

    }

    public Response sbFetchYoutubeLink(SBFetchYoutubeLink sbfetchyoutubelinkobj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            sbfetchyoutubelinkobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response sbfetchyoutubelinkobjresp = sbfetchyoutubelinkobj.callAPI();
        return sbfetchyoutubelinkobjresp;
    }


    public Response subpartassetupdatecreateleadmethod(Edcassetreplacecreatelead obj, Map<String, String> headers, Map<String, Object> body) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response objresp = obj.callAPI();
        return objresp;
    }


    public Response v1addBankFseDiy(AddBankDetail CallV1FseSubmit, String entityType, String solutionType, String leadId, String session_token, String version, String bank_AccountNumber, String ifsc, String only_ValidateBankDetails, String bankName, String partialSave) {
        //SendOtp callV3Otp = new SendOtp();

        CallV1FseSubmit.addParameter("entityType", entityType);
        CallV1FseSubmit.addParameter("solutionType", solutionType);
        CallV1FseSubmit.addParameter("leadId", leadId);


        CallV1FseSubmit.getProperties().setProperty("leadId", leadId);
        CallV1FseSubmit.getProperties().setProperty("onlyValidateBankDetails", only_ValidateBankDetails);
        CallV1FseSubmit.getProperties().setProperty("bankAccountNumber", bank_AccountNumber);
        CallV1FseSubmit.getProperties().setProperty("ifsc", ifsc);
        CallV1FseSubmit.getProperties().setProperty("bankName", bankName);
        CallV1FseSubmit.getProperties().setProperty("ifsc", ifsc);
        CallV1FseSubmit.getProperties().setProperty("bankAccountType", "");
        CallV1FseSubmit.getProperties().setProperty("bankAccountHolderName", "ANMOL JAIN");

        CallV1FseSubmit.getProperties().setProperty("partialSave", partialSave);


        CallV1FseSubmit.setHeader("Host", "goldengate-staging6.paytm.com");
        CallV1FseSubmit.setHeader("X-SRC", "GGClient");
        CallV1FseSubmit.setHeader("version", version);
        CallV1FseSubmit.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        CallV1FseSubmit.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        CallV1FseSubmit.setHeader("session_token", session_token);
        CallV1FseSubmit.setHeader("isDeviceRooted", "false");
        CallV1FseSubmit.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        CallV1FseSubmit.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        CallV1FseSubmit.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response LeadSubmitResponse = CallV1FseSubmit.callAPI();
        return LeadSubmitResponse;

    }

    public Response sbFetchSelectedPlan(SBFetchSelectedPlan FetchSelectedPlanobj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            FetchSelectedPlanobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            FetchSelectedPlanobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response FetchSelectedPlanrespobj = FetchSelectedPlanobj.callAPI();
        return FetchSelectedPlanrespobj;


    }

    public Response CheckRemainingCapacityMethod(CheckRemainingCapacity Capacityobj, Map<String, String> headers, Map<String, Object> body) {
        for (Map.Entry m : headers.entrySet()) {
            Capacityobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Capacityobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response Capacityrespobj = Capacityobj.callAPI();
        return Capacityrespobj;
    }

    public Response SBFetchPinCodeDetails(SbPinCode SBpincodeobj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            SBpincodeobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response SBpincodeobjresp = SBpincodeobj.callAPI();
        return SBpincodeobjresp;
    }

    public Response sbUpdateLead(SBUpdateLead UpdateLeadObj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            UpdateLeadObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            UpdateLeadObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response UpdateLeadObjResp = UpdateLeadObj.callAPI();
        return UpdateLeadObjResp;
    }

    public Response addAddressPgProfileUpdate(AddAddressPGProfileUpdate AddAddressObj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            AddAddressObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            AddAddressObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            AddAddressObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response AddAddressObjResp = AddAddressObj.callAPI();
        return AddAddressObjResp;
    }

    public Response v1SubmitFseHomeAddress(AddHomeAddress CallV1FseHomeAddress, String entityType, String solutionType, String leadId, String session_token, String version, String shopName, String landmark, String shopAddress, String areaOfEnrollment, String state, String cityOfEnrollment, String pincode, String latitudeOfShopVehicle, String longitudeOfShopVehicle, String district, String subDistrict, String village, String formattedAddress, String refreshLocationCount, String userAddressUUID, String partialSave) {
        //SendOtp callV3Otp = new SendOtp();

        CallV1FseHomeAddress.addParameter("entityType", entityType);
        CallV1FseHomeAddress.addParameter("solutionType", solutionType);
        CallV1FseHomeAddress.addParameter("leadId", leadId);


        CallV1FseHomeAddress.getProperties().setProperty("shopName", shopName);
        CallV1FseHomeAddress.getProperties().setProperty("landmark", landmark);
        CallV1FseHomeAddress.getProperties().setProperty("shopAddress", shopAddress);
        CallV1FseHomeAddress.getProperties().setProperty("areaOfEnrollment", areaOfEnrollment);
        CallV1FseHomeAddress.getProperties().setProperty("state", state);
        CallV1FseHomeAddress.getProperties().setProperty("cityOfEnrollment", cityOfEnrollment);
        CallV1FseHomeAddress.getProperties().setProperty("pincode", pincode);

        CallV1FseHomeAddress.getProperties().setProperty("latitudeOfShopVehicle", latitudeOfShopVehicle);
        CallV1FseHomeAddress.getProperties().setProperty("longitudeOfShopVehicle", longitudeOfShopVehicle);
        CallV1FseHomeAddress.getProperties().setProperty("district", district);
        CallV1FseHomeAddress.getProperties().setProperty("subDistrict", subDistrict);
        CallV1FseHomeAddress.getProperties().setProperty("village", village);
        CallV1FseHomeAddress.getProperties().setProperty("formattedAddress", formattedAddress);

        CallV1FseHomeAddress.getProperties().setProperty("refreshLocationCount", refreshLocationCount);
        CallV1FseHomeAddress.getProperties().setProperty("userAddressUUID", userAddressUUID);
        CallV1FseHomeAddress.getProperties().setProperty("partialSave", partialSave);

        CallV1FseHomeAddress.setHeader("Host", "goldengate-staging6.paytm.com");
        CallV1FseHomeAddress.setHeader("X-SRC", "GGClient");
        CallV1FseHomeAddress.setHeader("version", version);
        CallV1FseHomeAddress.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        CallV1FseHomeAddress.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        CallV1FseHomeAddress.setHeader("session_token", session_token);
        CallV1FseHomeAddress.setHeader("isDeviceRooted", "false");
        CallV1FseHomeAddress.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        CallV1FseHomeAddress.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        CallV1FseHomeAddress.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response LeadSubmitResponse = CallV1FseHomeAddress.callAPI();
        return LeadSubmitResponse;

    }

    public Response getBrandList(GetBrandList GetBrandListObj, Map<String, String> headers, Map<String, String> body) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            GetBrandListObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            GetBrandListObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response GetBrandListObjResp = GetBrandListObj.callAPI();
        return GetBrandListObjResp;

    }

    public Response UpdateLeadSbAddon(SBADDONUpdateLead SbaddonReqObj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            SbaddonReqObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            SbaddonReqObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response ADDONUpdateLeadObjResp = SbaddonReqObj.callAPI();
        return ADDONUpdateLeadObjResp;
    }

    public Response ScanassetQR(ScanassetQR obj, Map<String, String> headers, Map<String, String> body) {

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response objResp = obj.callAPI();
        return objResp;

    }

    public Response DeviceFetchTncMethod(DeviceFetchTnc obj, Map<String, String> queryParams, Map<String, String> headers) {   //adding query parameter
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response objResp = obj.callAPI();
        return objResp;

    }

    public Response ValidateotpEDCMethod(ValidateotpEDC obj, Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response objResp = obj.callAPI();
        return objResp;
    }

    public Response PaymentQRmethod(Devicepayment obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response objResp = obj.callAPI();
        return objResp;
    }

    public Response bankChannelLeadCreation(BankChannelLeadCreation BankChannelLeadCreationObj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            BankChannelLeadCreationObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            BankChannelLeadCreationObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            BankChannelLeadCreationObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response BankChannelLeadCreationObjResp = BankChannelLeadCreationObj.callAPI();
        return BankChannelLeadCreationObjResp;


    }

    public Response FetchDevicesimdetailsMethod(FetchDevicesimdetails Obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            Obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response ObjResp = Obj.callAPI();
        return ObjResp;

    }

    public Response SBPlansummary(SBPlanSummary SBPlansummary_obj1, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : queryParams.entrySet()) {
            SBPlansummary_obj1.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            SBPlansummary_obj1.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response SBPlansummary_obj1_Resp = SBPlansummary_obj1.callAPI();
        return SBPlansummary_obj1_Resp;
    }


    public Response SimupdateLeadMethod(SimupdateLead Obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ObjResp = Obj.callAPI();
        return ObjResp;

    }

    public Response Sound_Box_Validate_otp(Sound_Box_Validate_OTP Obj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            Obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }

    public Response Save_Tnc_SB(Save_Tnc Obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }


    // create a method v1FetchQuestions
    public Response v1FetchQuestions(FetchQuestions CallV1FseSubmit, String entityType, String solutionType, String session_token, String version) {
        //SendOtp callV3Otp = new SendOtp();

        CallV1FseSubmit.addParameter("entityType", entityType);
        CallV1FseSubmit.addParameter("solutionType", solutionType);

        CallV1FseSubmit.setHeader("Host", "goldengate-staging6.paytm.com");
        CallV1FseSubmit.setHeader("X-SRC", "GGClient");
        CallV1FseSubmit.setHeader("version", version);
        CallV1FseSubmit.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        CallV1FseSubmit.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        CallV1FseSubmit.setHeader("session_token", session_token);
        CallV1FseSubmit.setHeader("isDeviceRooted", "false");
        CallV1FseSubmit.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        CallV1FseSubmit.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        CallV1FseSubmit.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response v1FetchQuestions = CallV1FseSubmit.callAPI();
        return v1FetchQuestions;

    }

    public Response ShopInsuranceAddonMethod(AddonShopInsurance Obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            Obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }

    public Response EmbeddedshopinsuranceMethod(Embeddedshopinsurance Obj, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }


    public Response SBpayment(SBPayment Obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            Obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }

    public Response sbUpiautopay(SBUPIAutopay Obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }

    public Response ShopInsuranceConfirmPolicyMethod(ShopInsuranceConfirmPolicy Obj, Map<String, String> body, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }

    public Response getNcmcproducts(GetNcmcProducts productobj, Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            productobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response ObjResp = productobj.callAPI();
        return ObjResp;
    }

    public Response sendOTP(ncmcsendotp obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ObjResp = obj.callAPI();
        return ObjResp;
    }

    public Response validateOTP(ncmcvalidateotp obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ObjResp = obj.callAPI();
        return ObjResp;
    }


    public Response lead_state_ncmc(lead_state Obj, String solutionType, String entityType, String sessiontoken, String version) {

        Obj.addParameter("solutionType", solutionType);
        Obj.addParameter("entityType", entityType);

        Obj.setHeader("session_token", sessiontoken);
        Obj.setHeader("version", version);
        Obj.setHeader("Host", "goldengate-staging4.paytm.com");
        Obj.setHeader("X-SRC", "GGClient");
        Obj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        Obj.setHeader("devicemac", "60:6E:E8:D6:95:DB");
        Obj.setHeader("client", "androidapp");

        Response Objresp = Obj.callAPI();

        return Objresp;
    }

    public Response generate_qr_ncmc(generate_qr_ncmc Obj, String sessionToken, String version, String Cardcode, String lead_id, String sessionId) {

        //Adding Ide

        Obj.getProperties().setProperty("cardCode", Cardcode);
        Obj.getProperties().setProperty("leadId", lead_id);
        Obj.getProperties().setProperty("sessionId", sessionId);

        // Adding headers
        Obj.setHeader("session_token", sessionToken);
        Obj.setHeader("version", version);
        // Obj.setHeader("Host", "goldengate-staging5.paytm.com");
        Obj.setHeader("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        Obj.setHeader("devicemac", "60:6E:E8:D6:95:DB");
        Obj.setHeader("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        Obj.setHeader("client", "androidapp");
        Obj.setHeader("x-mw-url-checksum", "UWDvsxLWg4Nx49ABIcv3wC6pQabSmj5iWb66022uwmeI98QRerpKKmUZW5WWhzXqY/0HIUli0091yPkJxNgIaCGdfCRMGRrRGnZFrn1vNPI2jSX7rdYL4i8+oB7Psdy4");
        Obj.setHeader("androidid", "6db875100ed7d7a4");
        Obj.setHeader("osversion", "10");
        Obj.setHeader("x-src", "GGClient");
        Obj.setHeader("devicemanufacturer", "Xiaomi");
        Obj.setHeader("x-mw-checksum", "GqTfqbUQBlVLyXFwqdrEy/gt0L3DVSXEWsPDOlrgNDous9buXXCjBPd7dG0rqwr13I36O701MFah3N27BDBzIWIRxM92BO+0qD0F5M74f5Bis7lOV1bccHbkzZbSYnVV");
        Obj.setHeader("applanguage", "en");
        Obj.setHeader("sec-ch-ua-platform", "\"Android\"");
        Obj.setHeader("isdevicerooted", "false");
        Obj.setHeader("devicename", "M2004J19C");
        Obj.setHeader("sec-ch-ua-mobile", "?1");
        Obj.setHeader("ipaddress", "************");
        Obj.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        Obj.setHeader("content-type", "application/json; charset=UTF-8");
        Obj.setHeader("accept", "application/json, text/plain, */*");
        Obj.setHeader("isbusyboxfound", "false");
        //  Obj.setHeader("origin", "https://oe-staging5.paytm.com");
        Obj.setHeader("x-requested-with", "com.paytm.goldengate.debug");
        Obj.setHeader("sec-fetch-site", "same-site");
        Obj.setHeader("sec-fetch-mode", "cors");
        Obj.setHeader("sec-fetch-dest", "empty");
        //Obj.setHeader("referer", "https://oe-staging5.paytm.com/");
        Obj.setHeader("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");
        Obj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }

    public Response fetch_payment_status(fetch_payment_status Obj, String sessionToken, String version, String leadId, String cartOrderId) {
        Obj.getProperties().setProperty("leadId", leadId);
        Obj.getProperties().setProperty("orderId", cartOrderId);

        //Adding headers
        Obj.setHeader("session_token", sessionToken);
        Obj.setHeader("version", version);
        Obj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        // Obj.setHeader("Host", "goldengate-staging4.paytm.com");
        Obj.setHeader("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        Obj.setHeader("devicemac", "60:6E:E8:D6:95:DB");
        Obj.setHeader("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        Obj.setHeader("client", "androidapp");
        Obj.setHeader("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        Obj.setHeader("androidid", "6db875100ed7d7a4");
        Obj.setHeader("osversion", "10");
        Obj.setHeader("x-src", "GGClient");
        Obj.setHeader("devicemanufacturer", "Xiaomi");
        Obj.setHeader("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        Obj.setHeader("applanguage", "en");
        Obj.setHeader("sec-ch-ua-platform", "\"Android\"");
        Obj.setHeader("isdevicerooted", "false");
        Obj.setHeader("devicename", "M2004J19C");
        Obj.setHeader("sec-ch-ua-mobile", "?1");
        Obj.setHeader("ipaddress", "************");
        Obj.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        Obj.setHeader("content-type", "application/json; charset=UTF-8");
        Obj.setHeader("accept", "application/json, text/plain, */*");
        Obj.setHeader("isbusyboxfound", "false");
        // Obj.setHeader("origin", "https://oe-staging5.paytm.com");
        Obj.setHeader("x-requested-with", "com.paytm.goldengate.debug");
        Obj.setHeader("sec-fetch-site", "same-site");
        Obj.setHeader("sec-fetch-mode", "cors");
        Obj.setHeader("sec-fetch-dest", "empty");
        // Obj.setHeader("referer", "https://oe-staging5.paytm.com/");
        Obj.setHeader("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");


        Response ObjResp = Obj.callAPI();
        return ObjResp;
    }

    public Response submitKycomni(submitKyc Obj, String sessionToken, String version, String leadId, String sessionId, String docType, String docNo, String firstName, String lastName) {
        Obj.getProperties().setProperty("lead_id", leadId);
        Obj.getProperties().setProperty("sessionId", sessionId);
        Obj.getProperties().setProperty("docType", docType);
        Obj.getProperties().setProperty("docNo", docNo);
        Obj.getProperties().setProperty("firstName", firstName);
        Obj.getProperties().setProperty("lastName", lastName);

        //Adding headers
        Obj.setHeader("session_token", sessionToken);
        Obj.setHeader("version", version);
        Obj.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        // Obj.setHeader("Host", "goldengate-staging4.paytm.com");
        Obj.setHeader("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Android WebView\";v=\"120\"");
        Obj.setHeader("devicemac", "60:6E:E8:D6:95:DB");
        Obj.setHeader("deviceidentifier", "Xiaomi-M2004J19C-6db875100ed7d7a4");
        Obj.setHeader("client", "androidapp");
        Obj.setHeader("x-mw-url-checksum", "yeY3dUpg+E7Ek3gtB6BXRjOK8xLWxL/liuUXXHa3LO9cvaY7Bdm+SOlWEC7D/Eq5A28lFfKsPT4mxvAWI4rJamF+/1y/wccY7vJ7lQODTwFkRlo4Jio7O2auf2+1vR9x");
        Obj.setHeader("androidid", "6db875100ed7d7a4");
        Obj.setHeader("osversion", "10");
        Obj.setHeader("x-src", "GGClient");
        Obj.setHeader("devicemanufacturer", "Xiaomi");
        Obj.setHeader("x-mw-checksum", "baEhypt+euA6EQyNWD9dPTtbsFxIOMAaJtTD7NGeSrVZgbg1bPzFzJHFowr2tlbFPCtO3qjG10twiOl5wl5k3uNXDuTBUiEOMcqZvHHBIyL0ptLInh/YwuCpJhV3c+5d");
        Obj.setHeader("applanguage", "en");
        Obj.setHeader("sec-ch-ua-platform", "\"Android\"");
        Obj.setHeader("isdevicerooted", "false");
        Obj.setHeader("devicename", "M2004J19C");
        Obj.setHeader("sec-ch-ua-mobile", "?1");
        Obj.setHeader("ipaddress", "************");
        Obj.setHeader("user-agent", "Mozilla/5.0 (Linux; Android 10; M2004J19C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.210 Mobile Safari/537.36 AppContainer/10.5.10 AppContainer PhoenixContainer/1.0.52-10.34.0-RC1");
        Obj.setHeader("content-type", "application/json; charset=UTF-8");
        Obj.setHeader("accept", "application/json, text/plain, */*");
        Obj.setHeader("isbusyboxfound", "false");
        // Obj.setHeader("origin", "https://oe-staging5.paytm.com");
        Obj.setHeader("x-requested-with", "com.paytm.goldengate.debug");
        Obj.setHeader("sec-fetch-site", "same-site");
        Obj.setHeader("sec-fetch-mode", "cors");
        Obj.setHeader("sec-fetch-dest", "empty");
        // Obj.setHeader("referer", "https://oe-staging5.paytm.com/");
        Obj.setHeader("accept-language", "en-IN,en-US;q=0.9,en;q=0.8");

        Response RespObj = Obj.callAPI();
        return RespObj;
    }

    public Response GetMidRevisit(GetMidRevisit obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response RespObj = obj.callAPI();
        return RespObj;

    }

    public Response mpaAddressRevisit(MPAaddress obj, Map<String, String> headers, Map<String, String> queryParams) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response RespObj = obj.callAPI();
        return RespObj;

    }

    public Response revisitBeatDetails(RevisiteBeatDetails obj, Map<String, String> headers, Map<String, String> body) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response sendOtpRevisit(SendOtpRevisit obj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response validateotpResp(ValidateOtpRevisit obj, Map<String, String> queryParams, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response fetchquestionsRevisit(Fetchquestions obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response GetmidReferenceNumber(GetmidReferenceNumber obj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response createLeadRevisit(createLeadRevisit obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response SoundboxReplacementLead(CreateSoundboxReplacementLead createSoundboxReplacementLead, Map<String, String> headers, Map<String, String> body) {
        headers.forEach((key, value) -> createSoundboxReplacementLead.setHeader(key, value));
        body.forEach((key, value) -> createSoundboxReplacementLead.getProperties().setProperty(key, value));
        return createSoundboxReplacementLead.callAPI();
    }


    public Response psa_fetch_screen_details(PSAFetchScreenDetails obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response FetchSoundboxReplacementLead(FetchSoundboxReplacementLead fetchSoundboxReplacementLead, Map<String, String> headers, Map<String, String> queryParams) {
        headers.forEach((key, value) -> fetchSoundboxReplacementLead.setHeader(key, value));
        queryParams.forEach((key, value) -> fetchSoundboxReplacementLead.addParameter(key, value));
        return fetchSoundboxReplacementLead.callAPI();
    }

    public Response validateOldDevice(ValidateOldDevice validateOldDevice, Map<String, String> headers, Map<String, String> body) {
        headers.forEach((key, value) -> validateOldDevice.setHeader(key, value));
        body.forEach((key, value) -> validateOldDevice.getProperties().setProperty(key, value));
        return validateOldDevice.callAPI();
    }

    public Response post_adhaar_data_agent_onboarding(PostAdhaardata obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());

        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response fetch_bank_details_agent_onboarding(BankDetailsAgentOnboarding obj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response post_bank_details_agent_onboarding(PostBankDetailsAgentOnboarding obj, HashMap<String, String> headers, HashMap<String, String> queryParams, HashMap<String, Object> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());

        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response revisitLoancapture(RevistLoancapture obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response submitquestionsRevisit(Submitquestions obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response revisitLoanIntentFetchDoc(LoanIntentfetchDocumentDetails obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response respObj = obj.callAPI();
        return respObj;
    }

    public Response qrMappingFetchPosDetails(QRMappingFetchPosDetails obj, Map<String, String> queryParams, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        obj.getRequest().urlEncodingEnabled(false);
        return obj.callAPI();

    }

    public Response diyUpdateLeadDeivceDetails(DIYUpdateDeviceDetails obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response diyUpdateAwbNum(DIYSBQRUpdateAWBNumber obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response revisitSubmitDetails(RevisitSubmitDetails obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response notifyDelievery(SBDIYV2OrderDelivery obj, Map<String, String> headers, Map<String, String> params, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response getkybvaliddocsrevisit(RevisitGetKybValidDocs obj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response RespObj = obj.callAPI();
        return RespObj;
    }

    public Response CreateLead(ValidateOtp validateOtp, Map<String, String> queryParams, Map<String, String> headers) {
        //Add params in the request
        for (Map.Entry m : queryParams.entrySet()) {
            validateOtp.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            validateOtp.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadResponse = validateOtp.callAPI();
        return createLeadResponse;

    }

    public Response SendOtpMco(sendOTPLead sendOTPobj, Map<String, String> queryParams, Map<String, String> headers) {
        //Add params in the request
        for (Map.Entry m : queryParams.entrySet()) {
            sendOTPobj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            sendOTPobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response sendOTPResponse = sendOTPobj.callAPI();
        return sendOTPResponse;

    }


    public Response AddBankZDetail(SubmitMerchant submitMerchant, Map<String, String> queryParams, Map<String, String> headers) {
        //Add params in the request
        for (Map.Entry m : queryParams.entrySet()) {
            submitMerchant.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            submitMerchant.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        //Hit API to get Response
        Response createLeadResponse = submitMerchant.callAPI();
        return createLeadResponse;

    }

    public Response V3SubmitDocs(SubmitDocs v3DocObj, Map<String, String> queryParams, Map<String, String> headers) {
        v3DocObj.getRequest().urlEncodingEnabled(false);

        for (Map.Entry m : queryParams.entrySet()) {
            v3DocObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            v3DocObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response v3SubmitDocResp = v3DocObj.callAPI();

        return v3SubmitDocResp;
    }

    public Response FetchV3MerchantLead(FetchV3Merchant v3Merch, Map<String, String> query, Map<String, String> headers) {
        for (Map.Entry m : query.entrySet()) {
            v3Merch.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            v3Merch.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response getV3Merch = v3Merch.callAPI();

        return getV3Merch;
    }

    public Response v1EditLeadOEMco(EditLead QCMcoLead, Map<String, String> query, Map<String, String> headers) {
        for (Map.Entry m : query.entrySet()) {
            QCMcoLead.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            QCMcoLead.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response QcMco = QCMcoLead.callAPI();

        return QcMco;

    }

    public Response createLeadEnterprise(CreateLeadEnterprise obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response resp = obj.callAPI();
        return resp;
    }
    public Response createLeadEnterpriseOnUs(CreateLeadEnterprise_OnUs obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Add headers in the request
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response resp = obj.callAPI();
        return resp;
    }

    public Response fetchMerchantPosIds(FetchPOSIdSB obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response getLeadDataEnterprise(GetLeadDataEnterprise obj, Map<String, String> headers, Map<String, String> queryParams) {

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response enterpriseValidatePan(EnterpriseValidatePan obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response fetchAllResourcesEnterprise(EnterprisefetchAllResources obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response getCategorySubCategoryEnterprise(EnterpriseGetCategorySubCategory obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response getSubCategoryEnterprise(EnterpriseGetSubcategory obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response fetchGstFromPan(FetchGstFromPan obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response verifyGST(VerifyGST obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response fetchPinCodeDetailsEnterprise(EnterpriseFetchPinCodeDetails obj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response updateBusinessLeadEnterprise(EnterpriseUpdateBusinessLead obj, Map<String, String> headers, Map<String, String> body, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response fetchBusinessLeadInformation(FetchBusinessLead obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response addChannelCallback(String custId, String leadId) {
        String mid = FetchMID(custId);

        Map<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");

        Algorithm algorithm = Algorithm.HMAC256("e943ccc0-efc5-47e4-a701-bf9f3956bdaf");
        // Get current timestamp in the local timezone
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        String formattedTimestamp = now.format(formatter);
        // Build the JWT token with claims
        String jwtToken = JWT.create().withClaim("timestamp", formattedTimestamp).withClaim("clientId", "OE_INTERNAL").withClaim("iss", "OE").sign(algorithm);
        //Add JWT token to the headers
        headers.put("Authorization", jwtToken);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("leadId", leadId);
        body.put("bankName", "PTYES");
        body.put("responseCode", "000");
        body.put("responseMessage", "SUCCESS");

        addChannelCallback channelCallback = new addChannelCallback();

        String requestPath = "MerchantService/v1/boss/AddChannelCallback.json";
        Response response = channelCallback.addChannelCallback(requestPath, headers, body);

        return response;
    }

    public Response enterpriseFetchBasicInfo(EnterpriseFetchBasicInfo obj, HashMap<String, String> headers, HashMap<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response fetchMerchantBanksEnterprise(EnterpriseFetchMerchantBanks obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response fetchBankDetailsEnterprise(FetchBankDetailsEnterprise obj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response pennyDropVerification(EnterprisePennyDrop obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        return obj.callAPI();
    }

    public Response updateSolutionInstruments(EnterpriseSolutionInstruments obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response createSolutionLeadEnterprise(EnterpriseCreateSolutionLead obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response updateSolutionLeadEnterprise(EnterpriseUpdateSolutionLead obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        return obj.callAPI();
    }

    public Response fetchDocumentStatusEnterprise(EnterpriseDocumentStatus obj, Map<String, String> headers, Map<String, String> queryParams) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        return obj.callAPI();
    }

    public Response v1SubmitFseDiy1(SubmitLeadDetail SubmitLeadDetail, String entityType, String solutionType, String leadId, String session_token, String version) {
        //SendOtp callV3Otp = new SendOtp();

        SubmitLeadDetail.addParameter("entityType", entityType);
        SubmitLeadDetail.addParameter("solutionType", solutionType);
        SubmitLeadDetail.addParameter("leadId", leadId);


        SubmitLeadDetail.setHeader("Host", "goldengate-staging6.paytm.com");
        SubmitLeadDetail.setHeader("X-SRC", "GGClient");
        SubmitLeadDetail.setHeader("version", version);
        SubmitLeadDetail.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        SubmitLeadDetail.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        SubmitLeadDetail.setHeader("session_token", session_token);
        SubmitLeadDetail.setHeader("isDeviceRooted", "false");
        SubmitLeadDetail.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        SubmitLeadDetail.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        SubmitLeadDetail.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response LeadSubmitResponse = SubmitLeadDetail.callAPI();
        return LeadSubmitResponse;

    }


    public Response v1SaveTnc(FseDiySaveTnc savetnc, String session_token, String version) {
        //SendOtp callV3Otp = new SendOtp();


        savetnc.setHeader("Host", "goldengate-staging6.paytm.com");
        savetnc.setHeader("X-SRC", "GGClient");
        savetnc.setHeader("version", version);
        savetnc.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        savetnc.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        savetnc.setHeader("session_token", session_token);
        savetnc.setHeader("isDeviceRooted", "false");
        savetnc.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        savetnc.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        savetnc.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response LeadSubmitResponse = savetnc.callAPI();
        return LeadSubmitResponse;

    }

    public Response uploadDocumentsEnterprise(Map<String, String> headers, Map<String, String> queryParams, File file1) {
        UploadDocumentsEnterprise uploadDocumentsEnterprise = new UploadDocumentsEnterprise();
        for (Map.Entry m : headers.entrySet()) {
            uploadDocumentsEnterprise.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParams.entrySet()) {
            uploadDocumentsEnterprise.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        uploadDocumentsEnterprise.addMultipartFormData("file", file1, "image/jpeg");
        Response response = uploadDocumentsEnterprise.callAPI();
        return response;

    }


    public Response v1fetchFseInfo(FetchFseInfo callV3Otp, String entityType, String solutionType, String session_token, String version, String mobile, String userType, String custId) {
        //SendOtp callV3Otp = new SendOtp();

        callV3Otp.addParameter("entityType", entityType);
        callV3Otp.addParameter("solutionType", solutionType);


        callV3Otp.setHeader("Host", "goldengate-staging6.paytm.com");
        callV3Otp.setHeader("X-SRC", "GGClient");
        callV3Otp.setHeader("version", version);
        callV3Otp.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        callV3Otp.setHeader("X-MW-CHECKSUM-V3", "pUBmcEDJX0grKptLs6NQ9hVmYuKkSeIYkJwBWW6WoKTSr6ee3DOvKKvJJbhD6Lpz7NiOQsrU7MK1k1Q86Wzjqhi5IbB4Y81jw4cTP25zeGHWHzSncH1OPCq/esLZAadbN1M2QfAPYUH7pMDC9RgU=");
        callV3Otp.setHeader("session_token", session_token);
        callV3Otp.setHeader("isDeviceRooted", "false");
        callV3Otp.setHeader("deviceIdentifier", "OPPO-CPH1859-***************");
        callV3Otp.setHeader("X-MW-URL-CHECKSUM-V3", "x2JKNwbxSwY9EJx5le9x9hVlabX0S7UYks9XDmXH9/KFrKKfg2/9KaqePhPzLp3/NCfOsb5uO/Rj0QhrXWnt0C0bZRlFrgmk6MzP3MiISneHzSncH1OPCq/esLZBaBrrnR2c1cQDlCSajFgeXtQ=");
        callV3Otp.setHeader("Content-Type", "application/json; charset=UTF-8");
        Response OtpResponse = callV3Otp.callAPI();
        return OtpResponse;

    }

    public Response v1BusinessDetails(BusinessDetails QCMcoLead, Map<String, String> query, Map<String, String> headers) {

        for (Map.Entry m : query.entrySet()) {

            QCMcoLead.addParameter(m.getKey().toString(), m.getValue().toString());

        }


        //Add headers in the request

        for (Map.Entry m : headers.entrySet()) {

            QCMcoLead.setHeader(m.getKey().toString(), m.getValue().toString());

        }

        Response QcMco = QCMcoLead.callAPI();


        return QcMco;


    }

    public Response doingQCEnterprise(EnterpriseQC obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, Object> body) {
        for(Map.Entry m : headers.entrySet()){
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : queryParams.entrySet()){
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet()){
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response fetchQCDetailsEnterprise(FetchQCDetails obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, String> body) {
        for(Map.Entry m : headers.entrySet()){
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : queryParams.entrySet()){
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response doingEnterpriseCommercial(EnterpriseCommercial obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, Object> body) {
        for(Map.Entry m : headers.entrySet()){
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : queryParams.entrySet()){
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response reAllocateAgent(ReAllocateAgent obj, Map<String, String> headers, Map<String, String> queryParams, Map<String, Object> body) {
        for(Map.Entry m : headers.entrySet()){
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : queryParams.entrySet()){
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = obj.callAPI();
        return response;
    }

    public Response qrDetails(FetchQrDetails obj, Map<String, String> headers, Map<String, String> body) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response response = obj.callAPI();
        return response;
    }

    public Response getBasicDetails(GetMerchantBasicDetails obj, Map<String, String> headers, Map<String, String> queryParams) {
        for(Map.Entry m : headers.entrySet()){
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : queryParams.entrySet()){
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response response = obj.callAPI();
        return response;
    }

    public Response GetDeviceIds(GetDeviceIds obj, Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response RespObj = obj.callAPI();
        return RespObj;
    }

        // ... existing code ...
        public Response initiateNssLead(InitiateNssLead initiateNssLeadObj, Map<String, String> headers, Map<String, Object> body) {
            // Add headers to the request
            for (Map.Entry m : headers.entrySet()) {
                initiateNssLeadObj.setHeader(m.getKey().toString(), m.getValue().toString());
            }

            // Add body parameters
            for (Map.Entry<String, Object> entry : body.entrySet()) {
                initiateNssLeadObj.getProperties().setProperty(entry.getKey(), entry.getValue().toString());
            }

            Response response = initiateNssLeadObj.callAPI();
            return response;
        }

    public Response initiateH2HLead(InitiateH2HLead initiateH2HLeadObj, Map<String, String> headers, Map<String, Object> body) {
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            initiateH2HLeadObj.setHeader(entry.getKey(), entry.getValue());
        }

        for (Map.Entry<String, Object> entry : body.entrySet()) {
            initiateH2HLeadObj.getProperties().setProperty(entry.getKey(), entry.getValue().toString());
        }

        return initiateH2HLeadObj.callAPI();
    }



    public Response EdcUpgradeGetleadInfo(LeadInfo leadINfo, Map<String, String> headers, Map<String, String> query) {

        for (Map.Entry m : headers.entrySet()) {
            leadINfo.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : query.entrySet()) {
            leadINfo.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response res = leadINfo.callAPI();
        return res;
    }

    public Response PanelJobReplay(String leadId) throws Exception {
        String endpointTemplate = P.API.get("PanelJobReplay");
        if (endpointTemplate == null) {
            throw new Exception("API endpoint for PanelJobReplay is missing in configuration");
        }

        String url = endpointTemplate.replace("${leadId}", leadId);

        // Get XMW token for panel authentication
        String xmwToken = findXMWTokenforPanel("7771216290", "paytm@123");
        if (xmwToken == null) {
            throw new Exception("Failed to get XMW token for panel authentication");
        }

        RequestSpecification requestSpec = RestAssured.given()
            .header("Accept", "application/json")
            .header("Content-Type", "application/json")
            .header("Cookie", xmwToken);

        return requestSpec.get(url);
    }

}