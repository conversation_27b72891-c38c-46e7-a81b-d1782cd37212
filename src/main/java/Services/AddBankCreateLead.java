package Services;

import Request.PgProfileUpdate.BankDetailUpdate;
import io.restassured.response.Response;

import java.util.Map;

public class AddBankCreateLead {
    public Response addbankCreateLead(String requestPath, Map<String, String> headers, Map<String, String> params, Map<String, String> body) {
        BankDetailUpdate fetch = new BankDetailUpdate(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response addBankCreateLead = fetch.callAPI();
        return addBankCreateLead;
    }
}
