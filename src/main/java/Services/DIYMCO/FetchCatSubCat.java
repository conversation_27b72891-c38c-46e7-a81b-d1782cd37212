package Services.DIYMCO;

import Request.DIYMCO.FetchCat;
import Request.Subscription.CreateSubscription;
import Request.Subscription.FetchPlanSubscription;
import Request.Subscription.SubscriptionPlanOnboarding;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

import java.util.Map;

public class FetchCatSubCat {


	public Response FetchCatSubCatResponse( Map<String, String> headers, Map<String, String> params) {

		FetchCat fetch = new FetchCat();
		for (Map.Entry m : headers.entrySet()) {
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
//		for (Map.Entry m : body.entrySet()) {
//			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
//		}
		for (Map.Entry m : params.entrySet()) {
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}


		Response fetchsubsresponse = fetch.callAPI();
		return fetchsubsresponse;


	}
}
	
	
