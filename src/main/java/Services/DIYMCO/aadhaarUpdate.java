package Services.DIYMCO;

import Request.DIYMCO.AadhaarUpdate;
import io.restassured.response.Response;

import java.util.Map;

public class aadhaarUpdate {
    public Response aadhaarUpdateResponse(String RequestPathBody, Map<String, String> header, Map<String, String> params, Map<String,String> body) {

        AadhaarUpdate AU = new AadhaarUpdate(RequestPathBody);

        for (Map.Entry m : header.entrySet()) {
            AU.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            AU.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            AU.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response LeadResponse = AU.callAPI();
        return LeadResponse;
    }
}

