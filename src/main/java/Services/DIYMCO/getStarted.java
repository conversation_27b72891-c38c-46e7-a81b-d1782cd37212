package Services.DIYMCO;

import Request.DIYMCO.Getstarted;
import Request.DIYMCO.LeadCreate;
import io.restassured.response.Response;

import java.util.Map;

public class getStarted {

    public Response getStartedResponse(String RequestPathBody,Map<String, String> header, Map<String, String> params, Map<String,String> body) {

        Getstarted GS = new Getstarted(RequestPathBody);

        for (Map.Entry m : header.entrySet()) {
            GS.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            GS.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            GS.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response LeadResponse = GS.callAPI();
        return LeadResponse;
    }
}
