package Services.DIYMCO;

import Request.DIYMCO.FetchCat;
import Request.DIYMCO.LeadCreate;
import io.restassured.response.Response;

import java.util.Map;

public class CreationOfLead {

    public Response CreationOfLeadResponse(Map<String, String> headers, Map<String, String> params,Map<String,String> body) {
        LeadCreate LC = new LeadCreate();

        for (Map.Entry m : headers.entrySet()) {
            LC.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            LC.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            LC.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response LeadResponse = LC.callAPI();
        return LeadResponse;
    }

    public Response CreationOfLeadResponseWithParam(String RequestPathBody,Map<String, String> headers, Map<String, String> params,Map<String,String> body) {
                                //String RequestPathBody = paste before Map<String,String> headers as a param
        LeadCreate LC2 = new LeadCreate(RequestPathBody);
                                //RequestPathBody = paste as an param for the LeadCreate above
        for (Map.Entry m : headers.entrySet()) {
            LC2.setHeader(m.getKey().toString(), m.getValue().toString());
        }
		for (Map.Entry m : body.entrySet()) {
            LC2.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}
        for (Map.Entry m : params.entrySet()) {
            LC2.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response LeadResponse2 = LC2.callAPI();
        return LeadResponse2;
    }
}
