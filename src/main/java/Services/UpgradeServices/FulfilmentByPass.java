package Services.UpgradeServices;

import Request.QRcode.EditQRCodeDetailsRequest;
import Request.UpgradeEdcFulfilmentByPass.UpgradeEDCFulfilmentByPass;
import io.restassured.response.Response;

import java.util.Map;

public class FulfilmentByPass
{
    public Response FulfilmentBypass(UpgradeEDCFulfilmentByPass Obj, Map<String,String> headers, Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : body.entrySet())
        {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response res = Obj.callAPI();
        return res;
    }
}
