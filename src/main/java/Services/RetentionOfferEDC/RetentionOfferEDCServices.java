package Services.RetentionOfferEDC;


import Request.RetentionOfferEDC.RetentionOfferEDC;

import com.goldengate.common.BaseMethod;

import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
public class RetentionOfferEDCServices extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(RetentionOfferEDCServices.class);

    public Response CreateLeadRetentionOfferEDC(RetentionOfferEDC RetentionOfferEDC, Map<String, String> body,
                                                Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            RetentionOfferEDC.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            RetentionOfferEDC.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response CreateLeadRetentionOfferEDCResp = RetentionOfferEDC.callAPI();
        return CreateLeadRetentionOfferEDCResp;
    }
}