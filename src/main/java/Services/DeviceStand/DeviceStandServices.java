package Services.DeviceStand;

import Request.DeviceStand.CreateLeadDeviceStand;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class DeviceStandServices extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(DeviceStandServices.class);

    public Response CreateLeadDeviceStand(CreateLeadDeviceStand CreateLeadDeviceStand, Map<String, String> body,
                                          Map<String, String> headers) {

        for (Map.Entry m : headers.entrySet()) {
            CreateLeadDeviceStand.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            CreateLeadDeviceStand.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response CreateLeadDeviceStandResp = CreateLeadDeviceStand.callAPI();
        return CreateLeadDeviceStandResp;
    }
}
