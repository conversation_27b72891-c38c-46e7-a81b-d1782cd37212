package Services.EGS;

import Request.EGS.*;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class EGSMiddlewareServices extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(EGSMiddlewareServices.class);

    public Response egsMiddlewareServices(SimDetails SimDetails, Map<String, String> queryParams,
                                          Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            SimDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            SimDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response SimDetailsResp = SimDetails.callAPI();
        return SimDetailsResp;
    }
    public Response egssafecustodyMiddlewareServices(SafeCustody SafeCustody, Map<String, String> body,
                                                     Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            SafeCustody .setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            SafeCustody.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response SafeCustodyResp = SafeCustody.callAPI();
        return SafeCustodyResp;
    }

    public Response VISIMDetailsmethod(VISIMDetails obj, Map<String, String> queryParams, Map<String, String> headers)
    {

        for (Map.Entry m : queryParams.entrySet()) {
            obj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response ObjResp = obj.callAPI();
        return ObjResp;
    }

    public Response VISimActivationServices(VISimActivation VISimActivation, Map<String, String> body,
                                            Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            VISimActivation .setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            VISimActivation.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response VISimActivationResp = VISimActivation.callAPI();
        return VISimActivationResp;
    }

    public Response egsvisafecustodyMiddlewareServices(VISimSafeCustody VISimSafeCustody, Map<String, String> body,
                                                       Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            VISimSafeCustody .setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            VISimSafeCustody.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response VISimSafeCustodyResp = VISimSafeCustody.callAPI();
        return VISimSafeCustodyResp;
    }

    public Response VIOrderDetailssmethod(VIOrderDetails VIOrderDetails, Map<String, String> queryParams, Map<String, String> headers)
    {

        for (Map.Entry m : queryParams.entrySet()) {
            VIOrderDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            VIOrderDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response ObjResp = VIOrderDetails.callAPI();
        return ObjResp;
    }

    public Response AirtelSimActivationMiddleware(AirtelSimActivation AirtelSimActivation, Map<String, String> body,
                                                     Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            AirtelSimActivation .setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            AirtelSimActivation.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response AirtelSimActivationResp = AirtelSimActivation.callAPI();
        return AirtelSimActivationResp;
    }

    public Response AirtelReuseSimMiddleware(AirtelReuseSim AirtelReuseSim, Map<String, String> body,
                                                  Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            AirtelReuseSim .setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            AirtelReuseSim.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response AirtelReuseSimResp = AirtelReuseSim.callAPI();
        return AirtelReuseSimResp;
    }



}
