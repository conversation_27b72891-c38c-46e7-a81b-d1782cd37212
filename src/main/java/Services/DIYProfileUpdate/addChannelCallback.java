package Services.DIYProfileUpdate;

import Request.DIYProfileUpdate.AddChannelCallback;
import io.restassured.response.Response;

import java.util.Map;

public class addChannelCallback {

    public Response addChannelCallback(String requestPath, Map<String, String> headers, Map<String, String> body) {
        AddChannelCallback fetch = new AddChannelCallback(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = fetch.callAPI();
        return response;
    }
}
