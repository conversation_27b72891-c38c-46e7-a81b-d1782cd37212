package Services.DIYProfileUpdate;

import Request.MerchantService.v2.upgradeMid.doc.FetchDocumentStatus;
import io.restassured.response.Response;


import java.util.Map;

public class FetchDocStatus {
    public Response FetchDocumentStatus (String requestPath, Map<String, String> headers, Map<String, String> params) {
        FetchDocumentStatus fetch = new FetchDocumentStatus();
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response response = fetch.callAPI();
        return response;
    }
}
