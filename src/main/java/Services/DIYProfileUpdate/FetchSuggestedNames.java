package Services.DIYProfileUpdate;

import Request.DIYProfileUpdate.FetchSuggestedName;
import io.restassured.response.Response;

import java.util.Map;

public class FetchSuggestedNames {
    public Response FetchSuggestedNames(String requestPath, Map<String, String> headers, Map<String, String> params) {
        FetchSuggestedName fetch = new FetchSuggestedName(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response response = fetch.callAPI();
        return response;
    }
}
