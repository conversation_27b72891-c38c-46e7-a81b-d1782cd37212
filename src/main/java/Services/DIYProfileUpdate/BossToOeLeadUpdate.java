package Services.DIYProfileUpdate;

import Request.DIYProfileUpdate.LeadUpdate;
import io.restassured.response.Response;

import java.util.Map;

public class BossToOeLeadUpdate {
    public Response bossToOeLeadUpdate(String requestPath, Map<String, String> headers, Map<String, String> params, Map<String, Object> body) {
        LeadUpdate fetch = new LeadUpdate(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response response = fetch.callAPI();
        return response;
    }
}
