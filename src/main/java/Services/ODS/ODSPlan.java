package Services.ODS;

import Request.ODS.*;
import io.restassured.response.Response;

import java.util.HashMap;
import java.util.Map;

public class ODSPlan
{
    public Response odsFetchPlan(FetchPlanODS odsObj ,Map<String,String> headers, Map<String,String> body)
    {

        for(Map.Entry m : headers.entrySet())
        {
            odsObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            odsObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchOdsResponse=odsObj.callAPI();
        return fetchOdsResponse;

    }





    public Response odsCreatePlan(CreatePlanODS odsObj , Map<String,String> headers,  HashMap<String, Object> body)
    {

        for(Map.Entry m : headers.entrySet())
        {
            odsObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            odsObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response createOdsResponse=odsObj.callAPI();
        return createOdsResponse;

    }

    public Response odsViewPlan(ViewPlanODS odsObj , Map<String, String> queryParams, Map<String,String> headers, Map<String,String> body)
    {


        for(Map.Entry m : queryParams.entrySet())
        {
            odsObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : headers.entrySet())
        {
            odsObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            odsObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response viewOdsResponse=odsObj.callAPI();
        return viewOdsResponse;

    }

    public Response addonMappingPlanSearchAPIODSMiddlewareFunction(AddOnMappingPlanSearchAPIODS addOnMappingPlanSearchAPIODS,Map<String,String> headers, Map<String,String> body){

        for(Map.Entry m : headers.entrySet())
        {
            addOnMappingPlanSearchAPIODS.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            addOnMappingPlanSearchAPIODS.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addonMappingPlanSearchAPIResponse = addOnMappingPlanSearchAPIODS.callAPI();
        return addonMappingPlanSearchAPIResponse;


    }

    public Response addonMappingRequestSearchAPIMiddlewareFunction(AddonMappingRequestSearchAPI addonMappingRequestSearchAPI, Map<String,String> headers, Map<String,String> body){

        for(Map.Entry m : headers.entrySet())
        {
            addonMappingRequestSearchAPI.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            addonMappingRequestSearchAPI.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addonMappingRequestSearchAPIResponse = addonMappingRequestSearchAPI.callAPI();
        return addonMappingRequestSearchAPIResponse;


    }

    public Response bulkUpdateSearchPageAPIMiddlewareFunction( BulkUpdateSearchPageAPI bulkUpdateSearchPageAPI, Map<String,String> headers, Map<String,String> body){

        for(Map.Entry m : headers.entrySet())
        {
            bulkUpdateSearchPageAPI.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            bulkUpdateSearchPageAPI.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response bulkUpdateSearchAPIResponse = bulkUpdateSearchPageAPI.callAPI();
        return bulkUpdateSearchAPIResponse;


    }

    public Response fetchApplicableFiltersAPIMiddlewareFunction( FetchApplicableFiltersAPI fetchApplicableFiltersAPI, Map<String, String> queryParams, Map<String,String> headers){

        for(Map.Entry m : headers.entrySet())
        {
            fetchApplicableFiltersAPI.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : queryParams.entrySet())
        {
            fetchApplicableFiltersAPI.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchApplicableFiltersAPIResponse = fetchApplicableFiltersAPI.callAPI();
        return fetchApplicableFiltersAPIResponse;


    }

    public Response fetchFilterValuesAPIMiddlewareFunction(FetchFilterValuesAPI fetchFilterValuesAPI, Map<String, String> queryParams, Map<String,String> headers ){

        for(Map.Entry m : headers.entrySet())
        {
            fetchFilterValuesAPI.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : queryParams.entrySet())
        {
            fetchFilterValuesAPI.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchFilterValuesAPIResponse = fetchFilterValuesAPI.callAPI();
        return fetchFilterValuesAPIResponse;
    }

}
