package Services.ODS;

import Request.ODS.FetchActiveQuestionAnswers;
import Request.ODS.FetchDeviceDetailsAPI;
import Request.ODS.PlanDifferenceAPI;
import Request.ODS.TotalNumberOfRecords;
import Request.ODS.v1.*;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Map;
import java.util.*;

public class OdsMiddlewareServices extends BaseMethod {

	private static final Logger LOGGER = LogManager.getLogger(OdsMiddlewareServices.class);

	public Response odsfetchV1CategoriesMethod(OdsCategories OdsCategories, Map<String, String> headers) {

		for (Map.Entry m : headers.entrySet()) {
			OdsCategories.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		// Hit API to get Response
		Response OdsCategoriesResp = OdsCategories.callAPI();
		return OdsCategoriesResp;
	}
	public Response odsuserdetailsMethod(OdsUserInfo OdsUserInfo, Map<String, String> queryParams,
			Map<String, String> headers) {

		for (Map.Entry m : queryParams.entrySet()) {
			OdsUserInfo.addParameter(m.getKey().toString(), m.getValue().toString());
		}

		for (Map.Entry m : headers.entrySet()) {
			OdsUserInfo.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		// Hit API to get Response
		Response OdsUserInfoResp = OdsUserInfo.callAPI();
		return OdsUserInfoResp;
	}
	public Response odsPayModeMethod(OdsPayMode OdsPayMode, Map<String, String> headers,Map<String, String> queryParams) {

		for (Map.Entry m : headers.entrySet()) {
			OdsPayMode.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		for (Map.Entry m : queryParams.entrySet()) {
			OdsPayMode.addParameter(m.getKey().toString(), m.getValue().toString());
		}

		// Hit API to get Response
		Response OdsPayModeResp = OdsPayMode.callAPI();
		return OdsPayModeResp;
	}
	public Response odsAclServiceMethod(OdsAclService OdsAclService, Map<String, String> headers) {

		for (Map.Entry m : headers.entrySet()) {
			OdsAclService.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		// Hit API to get Response
		Response OdsAclServiceResp = OdsAclService.callAPI();
		return OdsAclServiceResp;
	}
	public Response odsSearchPlanMethod(OdsSearchPlan OdsSearchPlan, Map<String, String> queryParams,
										Map<String, String> headers) {

		for (Map.Entry m : queryParams.entrySet()) {
			OdsSearchPlan.addParameter(m.getKey().toString(), m.getValue().toString());
		}

		for (Map.Entry m : headers.entrySet()) {
			OdsSearchPlan.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		// Hit API to get Response
		Response OdsSearchPlanResp = OdsSearchPlan.callAPI();
		return OdsSearchPlanResp;
	}

	public Response fetchDeviceDetailsAPIMethod(FetchDeviceDetailsAPI FetchDeviceDetailsApi, Map<String, String> queryParams, Map<String, String> headers) {

		for(Map.Entry m : queryParams.entrySet()) {
			FetchDeviceDetailsApi.addParameter(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : headers.entrySet()) {
			FetchDeviceDetailsApi.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		Response resp = FetchDeviceDetailsApi.callAPI();
		return resp;
	}
	public Response odsNumberofRecordsmethod(Map<String,String> headers,  Map<String, String> params)
	{
		OdsNumberofRecords Odsnumofrec= new OdsNumberofRecords();

		for (Map.Entry m : params.entrySet()) {
			Odsnumofrec.addParameter(m.getKey().toString(), m.getValue().toString());
		}

		for (Map.Entry m : headers.entrySet()) {
			Odsnumofrec.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		// Hit API to get Response
		Response OdsNumberofRecordsResp = Odsnumofrec.callAPI();
		return OdsNumberofRecordsResp;

	}


	public Response fetchActiveQuestionAnswers(FetchActiveQuestionAnswers FetchActiveQuestionAnswers, Map<String, String> body, Map<String, String> headers) {

		for(Map.Entry m : headers.entrySet()) {
			FetchActiveQuestionAnswers.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : body.entrySet()) {
			FetchActiveQuestionAnswers.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
			}

		Response resp = FetchActiveQuestionAnswers.callAPI();
		return resp;
	}

	public Response totalNumberOfRecords(TotalNumberOfRecords TotalNumberOfRecords, Map<String, String> queryParams, Map<String, String> headers) {

		for(Map.Entry m : queryParams.entrySet()) {
			TotalNumberOfRecords.addParameter(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : headers.entrySet()) {
			TotalNumberOfRecords.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		Response resp = TotalNumberOfRecords.callAPI();
		return resp;
	}
	public Response planDifference(PlanDifferenceAPI Obj, Map<String, String> queryParams, Map<String, String> headers) {

		for(Map.Entry m : queryParams.entrySet()) {
			Obj.addParameter(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : headers.entrySet()) {
			Obj.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		Response resp = Obj.callAPI();
		return resp;
	}

}
