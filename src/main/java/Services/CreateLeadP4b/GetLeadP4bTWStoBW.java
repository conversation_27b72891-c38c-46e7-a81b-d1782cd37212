package Services.CreateLeadP4b;

import Request.CreateLeadTWSToBW.CreateLeadTWSToBW;
import io.restassured.response.Response;

import java.util.Map;

public class GetLeadP4bTWStoBW {
	
	
	public Response GetCreateLeadP4bResponse(Map<String,String> headers,Map<String,String> body,Map<String,String> params)
	{
		//FetchPlanSubscription fetch=new FetchPlanSubscription();
		CreateLeadTWSToBW fetch = new CreateLeadTWSToBW();
		for(Map.Entry m : headers.entrySet())
		{
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}
		
		for(Map.Entry m : params.entrySet())
		{
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}
		
		
		Response fetchsubsresponse=fetch.callAPI();
		return fetchsubsresponse;
		
		
	}
	
	

}
