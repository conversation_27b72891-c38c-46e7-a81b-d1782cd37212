package Services.CreateDeals;

import Request.CreateDeals.CreateDeals;
import Request.CreateDeals.DealsFetchTnc;
import io.restassured.response.Response;

import java.util.Map;

public class GetCreateLeadP4b {
	
	
	public Response GetCreateLeadP4bResponse(String requestBodyPath,Map<String,String> headers,Map<String,String> body,Map<String,String> params)
	{
		//FetchPlanSubscription fetch=new FetchPlanSubscription();

		CreateDeals fetch = new CreateDeals(requestBodyPath);
		for(Map.Entry m : headers.entrySet())
		{
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}
		
		for(Map.Entry m : params.entrySet())
		{
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}
		
		
		Response fetchDealsResponse=fetch.callAPI();
		return fetchDealsResponse;
		
		
	}

	public Response GetDealsTncResponse(Map<String,String> headers,Map<String,String> body,Map<String,String> params)
	{
		//FetchPlanSubscription fetch=new FetchPlanSubscription();

		DealsFetchTnc fetch = new DealsFetchTnc();
		for(Map.Entry m : headers.entrySet())
		{
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : params.entrySet())
		{
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}


		Response fetchDealsResponse=fetch.callAPI();
		return fetchDealsResponse;


	}



	
	

}
