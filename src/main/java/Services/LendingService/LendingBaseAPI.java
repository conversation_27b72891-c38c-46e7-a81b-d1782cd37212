package Services.LendingService;

import Rquest.DatabaseDetails;
import MerchantService.loan.lead.dynamicTNC.FetchDynamicTnc;
import Request.Los.v1.wrapper.bre.status.BREStatus;
import Request.Los.v1.wrapper.bre.status.ThirdBREStatus;
import Request.Los.v1.wrapper.bre2.BREInitiate;
import Request.Los.v1.wrapper.bre2.ThirdBREInitiate;
import Request.MerchantService.DeleteAllLeads.DeleteLeads;
import Request.MerchantService.oe.panel.v1.fileProcess.upload.SheetUpload;
import Request.MerchantService.v1.Consumer.Lead.AddAddress;
import Request.MerchantService.v1.Consumer.Lead.AddBasicDetails;
import Request.MerchantService.v1.Consumer.Lead.AdditionalDetails.AddAddressDetails;
import Request.MerchantService.v1.Consumer.Lead.AdditionalDetails.BasicDetails;
import Request.MerchantService.v1.Consumer.Lead.CreatePersonalLoanLead;
import Request.MerchantService.v1.Consumer.Lead.FetchLeadDetails;
import Request.MerchantService.v1.internal.workflow.lead.reset.LeadReset;
import Request.MerchantService.v1.sdMerchant.lead.*;
import Request.MerchantService.v1.sdMerchant.savebank.SaveBankDetails;
import Request.MerchantService.v1.workflow.lead.CreateLeadWorkflow;
import Request.MerchantService.v1.workflow.lead.ExperianPull;
import Request.MerchantService.v1.workflow.lead.FetchLeadWorkflow;
import Request.MerchantService.v1.workflow.lead.FetchWhitelistDetails;
import Request.MerchantService.v1.workflow.lead.bank.NewBankAPI;
import Request.MerchantService.v1.workflow.lead.detail.FetchLeadBackfillingAPI;
import Request.MerchantService.v1.workflow.lead.intiate.bureau.AsyncBureau;
import Request.MerchantService.v1.workflow.lead.intiate.kyc.InitiateKYC;
import Request.MerchantService.v1.workflow.lead.intiate.kyc.InitiateKYCV2Version;
import Request.MerchantService.v1.workflow.lead.upload.document.UploadDocumentNewAPI;
import Request.MerchantService.v2.lending.GetBREStatus;
import Request.MerchantService.v2.lending.GetCKYCFromPPBL;
import Request.MerchantService.v2.lending.dataUpdate.*;
import Request.MerchantService.v2.lending.fetchExistingDetail.FetchExistingLeadDetails;
import Request.MerchantService.v2.lending.lead.checkBreStatus.CheckBREStatus;
import Request.MerchantService.v2.lending.lead.checkBreStatus.UploadCancelledCheque;
import Request.MerchantService.v2.lending.lead.document.status.DocumentStatus;
import Request.MerchantService.v2.lending.lead.fetchCIR.FetchCIR;
import Request.MerchantService.v2.sdmerchant.bank.SaveBankDetailsV2;
import Request.MerchantService.v5.callback.*;
import Request.MerchnatService.loan.lead.submitApplication.SubmitApplication;
import Request.Stashfin.Lender.CreateToken;
import Request.Stashfin.Lender.LenderLeadReset;
import Request.Tools.UploadDoc.DocUpload;
import Request.v1.kyc.KYCStatus;
import Request.v1.kyc.KYCSubmit;
import Request.v1.kyc.KYCinitiate;
import Rquest.MerchantService.v1.workflow.lead.callback.*;
import Services.oAuth.oAuthServices;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.goldengate.common.BaseMethod;
import com.google.gson.Gson;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.ssh.SSHConnection;
import com.paytm.apitools.util.ssh.SshDetails;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;

import java.io.File;
//import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.time.format.DateTimeFormatter;

import static Rquest.DatabaseDetails.*;


public class LendingBaseAPI  extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(LendingBaseAPI.class);

    oAuthServices oAuthServicesObject = new oAuthServices();
    static Connection connection = null;
    static SSHConnection sshConnection = new SSHConnection();
    public static String DbName = "oe_lending_staging3_221103";
    String clientToken="";

    private static final String SSH_PRIVATE_KEY_PATH = System.getProperty("user.home") + "/.ssh/id_rsa";
    private static final String DB_USER = "michael";
    private static final String DB_PASSWORD = "Paytm@123";
    private static final String DB_HOST = "************";
    private static final int SSH_PORT = 22;
    private static final int DB_PORT = 3310;
    private static final String DB_NAME = "";


    /**
     * Method to create postpaid lead
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response v1sdMerchantLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadLending createLeadPostPaidObject = new CreateLeadLending();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();

        try {

            ((AbstractApiV2) createLeadPostPaidObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return createLeadPostPaidObjectResponse;
    }

    /**
     * Method to create postpaid lead
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response v1sdMerchantLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String merchantID) {

        CreateLeadLendingAril createLeadArilObject = new CreateLeadLendingAril(merchantID);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadArilObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadArilObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadArilObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadArilObjectResponse = createLeadArilObject.callAPI();

        try {

            ((AbstractApiV2) createLeadArilObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return createLeadArilObjectResponse;
    }


    public Response v1sdMerchantLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String merchantID, boolean isAril) {

        CreateLeadLendingAril createLeadArilObject = new CreateLeadLendingAril(merchantID, isAril);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadArilObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadArilObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadArilObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadArilObjectResponse = createLeadArilObject.callAPI();

        try {

            ((AbstractApiV2) createLeadArilObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return createLeadArilObjectResponse;
    }






    /**
     * Method to delete all the existing leads of given number
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response merchantServiceDeleteAllLeadsV2(Map<String, String> queryParams, Map<String, String> headers) {

        DeleteLeads deleteLeadsObject = new DeleteLeads();

        for (Map.Entry m : queryParams.entrySet()) {
            deleteLeadsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            deleteLeadsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response deleteLeadsObjectResponse = deleteLeadsObject.callAPI();


        return deleteLeadsObjectResponse;
    }
    /**
     * Method to fetch all the data of the lead
     * @param queryParams
     * @param headers
     * @return
     */
    public Response fetchLeadDetails(Map<String, String> queryParams, Map<String, String> headers) {

        FetchLeadDetails fetchLeadDetailsObject = new FetchLeadDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchLeadDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchLeadDetailsObjectResponse = fetchLeadDetailsObject.callAPI();


        return fetchLeadDetailsObjectResponse;
    }
    /**
     * Method to add Basic details PAN, DOB and EMAIL
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response addBasicDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        AddBasicDetails addBasicDetailsObject = new AddBasicDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            addBasicDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        try {

            ((AbstractApiV2) addBasicDetailsObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return addBasicDetailsObjectResponse;
    }

    public Response callbackOTP(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        callbackLending callbackObject = new callbackLending();

        for (Map.Entry m : queryParams.entrySet()) {
            callbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            callbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            callbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = callbackObject.callAPI();

        try {

            ((AbstractApiV2) callbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return callbackObjectResponse;
    }
    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response callbackPPBLOTP(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        PPBLOTPCallback callbackObject = new PPBLOTPCallback();

        for (Map.Entry m : queryParams.entrySet()) {
            callbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            callbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            callbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = callbackObject.callAPI();

        try {

            ((AbstractApiV2) callbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return callbackObjectResponse;
    }


    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @param isAril
     * @return
     */
    public Response callbackPPBLOTP(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, boolean isAril) {

        PPBLOTPCallback callbackObject = new PPBLOTPCallback(isAril);

        for (Map.Entry m : queryParams.entrySet()) {
            callbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            callbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            callbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = callbackObject.callAPI();

        try {

            ((AbstractApiV2) callbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return callbackObjectResponse;
    }


    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @param isAril
     * @param isTnc
     * @return
     */
    public Response callbackPPBLOTP(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, boolean isAril, boolean isTnc) {

        PPBLOTPCallback callbackObject = new PPBLOTPCallback(isAril,isTnc);

        for (Map.Entry m : queryParams.entrySet()) {
            callbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            callbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            callbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = callbackObject.callAPI();

        try {

            ((AbstractApiV2) callbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return callbackObjectResponse;
    }


    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @param solution
     * @return
     */
    public Response callbackPPBLOTP(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, String solution) {

        PPBLOTPCallback callbackObject = new PPBLOTPCallback(solution);

        for (Map.Entry m : queryParams.entrySet()) {
            callbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            callbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            callbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = callbackObject.callAPI();

        try {

            ((AbstractApiV2) callbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return callbackObjectResponse;
    }







    /**
     * Method to generate JWT token. This will add an extra 2 minutes to existing time and then generate the token
     * @param custId
     * @return
     */
    public String generateJwtToken(String issuer,String clientId,String custId,String clientSecret) {
        String token = "";
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30")).plusMinutes(2);
        String ts = localDateTime.toString();

        Algorithm buildAlgorithm = Algorithm.HMAC256(clientSecret);
        token = JWT.create().withIssuer(issuer)
                .withClaim("clientId", clientId)
                .withClaim("custId", custId)
                .withClaim("timestamp", ts + "+05:30").sign(buildAlgorithm);
        return token;
    }

    public String generateJwtTokenWithoutClientId(String issuer,String custId,String clientSecret) {
        String token = "";
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30")).plusMinutes(2);
        String ts = localDateTime.toString();

        Algorithm buildAlgorithm = Algorithm.HMAC256(clientSecret);
        token = JWT.create().withIssuer(issuer)

                .withClaim("customerId", custId)
                .withClaim("timestamp", ts + "+05:30").sign(buildAlgorithm);
        return token;
    }

    /**
     * Method to set headers which are used in callback request
     * @return
     */
    public Map<String, String> setCallbackHeaders(String auth, String custId,String channel) {


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization",auth);
        headers.put("Content-Type","application/json");
        headers.put("channel",channel);
        headers.put("custId",custId);

        return headers;
    }
    /**
     * Method to verify the current lead stage and return the response object
     */
    public Response fetchTheCurrentLeadStage(String entityType,String solution,String channel,String sessionToken,String expectedStage)
    {

        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams.put("entityType",entityType );
        queryParams.put("solution",solution);
        queryParams.put("channel",channel);

        Response responseObject=null;

        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token",sessionToken);

        if(solution.equals(LendingConstants.BUSINESS_LENDING_SOLUTION))
        {
            responseObject=fetchBusinessLendingLeads(queryParams, headers);
        }

        else
            responseObject= fetchLeadDetails(queryParams, headers);

        if(responseObject.getStatusCode()==200)
        {
            LOGGER.info("Verify Lead Stage "+responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),expectedStage);

        }

        return responseObject;

    }
    /**
     * Check CKYC status
     * @param queryParams
     * @param headers
     * @return
     */

    public Response checkCKYCStatus(Map<String, String> queryParams, Map<String, String> headers) {

        GetCKYCFromPPBL checkCKYCStatusObject = new GetCKYCFromPPBL();

        for (Map.Entry m : queryParams.entrySet()) {
            checkCKYCStatusObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            checkCKYCStatusObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response checkCKYCStatusObjectResponse = checkCKYCStatusObject.callAPI();

        try {

            ((AbstractApiV2) checkCKYCStatusObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return checkCKYCStatusObjectResponse;
    }
    /**
     * Method to upload any provided document. This method internally calls uploadDocument which finally uploads the image
     * @param docToUpload
     * @param leadId
     * @param custId
     * @param entityType
     * @param solutionType
     * @param solutionTypeLevel2
     * @param sessionToken
     * @return
     * @throws InterruptedException
     */
    public Response utilityForDocumentUpload(String docToUpload,String leadId,String custId, String entityType,String solutionType,String solutionTypeLevel2,String sessionToken) throws InterruptedException {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("docProvided", docToUpload);
        queryParams.put("extType", "png");
        queryParams.put("leadId", leadId);
        queryParams.put("custId", custId);
        queryParams.put("entityType", entityType);
        queryParams.put("solutionType", solutionType);
        queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
        queryParams.put("category", "NonTransport");

        if(solutionType.contentEquals("business_lending"))
        {

            queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");


        }
        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", sessionToken);
        headers.put("custId",custId);

        Response responseObject = UploadDocument(queryParams, headers, uploadFile);

        return responseObject;
    }
    /**
     *
     * @param path
     * @param leadId
     * @param custId
     * @param entityType
     * @param solutionType
     * @param docType
     * @param docProvided
     * @param channel
     * @return
     * @throws InterruptedException
     */
    public Response docUploadUsingToolsAPI(String path,String leadId,String custId, String entityType,String channel,String solutionType,String docProvided,String docType) throws InterruptedException {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-tags", "PL,OCL,LENDING");
        headers.put("x-user-id", custId);
        headers.put("Content-Type", "multipart/form-data");

        DocUpload UploadDocumentObject = new DocUpload();

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            UploadDocumentObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        File uploadFile = new File(path);
        UploadDocumentObject.addMultipartFormData("file", uploadFile,"application/pdf");
        UploadDocumentObject.addFormParameter("custId", custId);
        UploadDocumentObject.addFormParameter("docProvided", docProvided);
        UploadDocumentObject.addFormParameter("docType", docType);
        UploadDocumentObject.addFormParameter("solution", solutionType);
        UploadDocumentObject.addFormParameter("entityType", entityType);
        UploadDocumentObject.addFormParameter("channel", channel);
        UploadDocumentObject.addFormParameter("leadId", leadId);

        UploadDocumentObject.getRequest().urlEncodingEnabled(false);


        Response UploadDocumentObjectResponse = UploadDocumentObject.callAPI();

        return UploadDocumentObjectResponse;
    }

    /**
     * Method to upload the document
     * @param queryParams
     * @param headers
     * @param uploadFile
     * @return
     * @throws InterruptedException
     */
    public Response UploadDocument(Map<String, String> queryParams, Map<String, String> headers, File uploadFile) throws InterruptedException {


        MerchantService.v2.lending.lead.document.UploadDocument UploadDocumentObject = new MerchantService.v2.lending.lead.document.UploadDocument();


        //Adding queryParams
        for (Map.Entry m : queryParams.entrySet()) {
            UploadDocumentObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            UploadDocumentObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        UploadDocumentObject.addMultipartFormData("file", uploadFile,"image/jpeg");
        Thread.sleep(2000);

        UploadDocumentObject.getRequest().urlEncodingEnabled(false);

        Response UploadDocumentObjectResponse = UploadDocumentObject.callAPI();

        try {
            if (UploadDocumentObjectResponse.jsonPath().getString("errorCode").equals("204")) {
                LOGGER.info("Inside Document upload json validation");
                UploadDocumentObject.validateResponseAgainstJSONSchema("MerchantService/v2/lending/lead/document/UploadDocumentResponseSchema.json");
            }

        } catch (Exception e) {
            LOGGER.info("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }


        return UploadDocumentObjectResponse;
    }





    /**
     * To get KYC callback
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response ckycCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        CKYCCallBack ckycCallbackObject = new CKYCCallBack();

        for (Map.Entry m : queryParams.entrySet()) {
            ckycCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            ckycCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            ckycCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ckycCallbackObjectResponse = ckycCallbackObject.callAPI();

        try {

            ((AbstractApiV2) ckycCallbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v2/lending/dataUpdate/CkycCallbackResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return ckycCallbackObjectResponse;
    }


    /**
     * To get KYC callback
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response ckycCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String requestBodyPath,String solution) {

        CKYCCallBack ckycCallbackObject = new CKYCCallBack();

        for (Map.Entry m : queryParams.entrySet()) {
            ckycCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            ckycCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            ckycCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ckycCallbackObjectResponse = ckycCallbackObject.callAPI();

        try {

            ((AbstractApiV2) ckycCallbackObjectResponse).validateResponseAgainstJSONSchema(requestBodyPath);
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return ckycCallbackObjectResponse;
    }



    /**
     * Callback for BRE Validation pending
     * @param queryParams
     * @param headers
     * @param body
     * @param flag
     * @return
     */
    public Response callbackBREValiadtion(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,boolean flag) {

        callbackLending callbackObject = new callbackLending(flag);

        for (Map.Entry m : queryParams.entrySet()) {
            callbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            callbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            callbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = callbackObject.callAPI();

        try {

            ((AbstractApiV2) callbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return callbackObjectResponse;
    }
    /**
     * TO move to BRE Response awaited stage
     * @param queryParams
     * @param headers
     * @return
     */
    public Response getBREStatus(Map<String, String> queryParams, Map<String, String> headers) {

        GetBREStatus getBREStatusObject = new GetBREStatus();

        for (Map.Entry m : queryParams.entrySet()) {
            getBREStatusObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            getBREStatusObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        getBREStatusObject.getRequest().urlEncodingEnabled(false);
        Response getBREStatusObjectResponse = getBREStatusObject.callAPI();


        return getBREStatusObjectResponse;
    }

    /**
     * To check the BRE Status
     * @param queryParams
     * @param headers
     * @return
     */
    public Response checkBREStatus(Map<String, String> queryParams, Map<String, String> headers) {

        CheckBREStatus checkBREStatusObject = new CheckBREStatus();

        for (Map.Entry m : queryParams.entrySet()) {
            checkBREStatusObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            checkBREStatusObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        checkBREStatusObject.getRequest().urlEncodingEnabled(false);
        Response checkBREStatusObjectResponse = checkBREStatusObject.callAPI();


        return checkBREStatusObjectResponse;
    }

    /**
     * To get BRE Callback
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response breCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        BRECallback breCallbackObject = new BRECallback();

        for (Map.Entry m : queryParams.entrySet()) {
            breCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            breCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            breCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response breCallbackObjectResponse = breCallbackObject.callAPI();

        try {

            ((AbstractApiV2) breCallbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v2/lending/dataUpdate/BRECallbackResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return breCallbackObjectResponse;
    }

    /**
     * To add address of user
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response addAddress(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        AddAddress addAddressObject = new AddAddress();

        for (Map.Entry m : queryParams.entrySet()) {
            addAddressObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addAddressObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            addAddressObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addAddressObjectResponse = addAddressObject.callAPI();

        try {

            ((AbstractApiV2) addAddressObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v2/lending/dataUpdate/BRECallbackResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return addAddressObjectResponse;
    }

    /**
     * Method to fetch tnc
     * @param queryParams
     * @param headers
     * @return
     */

    public Response fetchDynamicTnc(Map<String, String> queryParams, Map<String, String> headers) {

        FetchDynamicTnc fetchDynamicTncObject = new FetchDynamicTnc();

        for (Map.Entry m : queryParams.entrySet()) {
            fetchDynamicTncObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchDynamicTncObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchDynamicTncObjectResponse = fetchDynamicTncObject.callAPI();

        try {

            ((AbstractApiV2) fetchDynamicTncObjectResponse).validateResponseAgainstJSONSchema("MerchantService/loan/lead/dynamicTnc/TncResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return fetchDynamicTncObjectResponse;
    }
    /**
     * Used to submit all tnc set
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response submitApplication(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        SubmitApplication submitApplicationObject = new SubmitApplication();

        for (Map.Entry m : queryParams.entrySet()) {
            submitApplicationObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitApplicationObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitApplicationObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = submitApplicationObject.callAPI();

        try {

            ((AbstractApiV2) submitApplicationObjectResponse).validateResponseAgainstJSONSchema("MerchantService/loan/lead/submitApplication/SubmitApplicationResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return submitApplicationObjectResponse;
    }


    /**
     * Used to submit all tnc set
     * @param queryParams
     * @param headers
     * @param body
     * @param isAril
     * @return
     */
    public Response submitApplication(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, boolean isAril) {

        SubmitApplication submitApplicationObject = new SubmitApplication(isAril);

        for (Map.Entry m : queryParams.entrySet()) {
            submitApplicationObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitApplicationObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitApplicationObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = submitApplicationObject.callAPI();

        try {

            ((AbstractApiV2) submitApplicationObjectResponse).validateResponseAgainstJSONSchema("MerchantService/loan/lead/submitApplication/SubmitApplicationResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return submitApplicationObjectResponse;
    }



    public Response submitApplication(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, boolean isNewLender,boolean onlyAgreement) {

        SubmitApplication submitApplicationObject = new SubmitApplication(isNewLender,onlyAgreement);

        for (Map.Entry m : queryParams.entrySet()) {
            submitApplicationObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitApplicationObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitApplicationObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = submitApplicationObject.callAPI();


        return submitApplicationObjectResponse;
    }















    /**
     * to get final LMS callback after  loan application is submitted
     */
    public Response loanApplicationCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String callback) {

        callbackLending loanApplicationCallbackObject = new callbackLending(callback);

        for (Map.Entry m : queryParams.entrySet()) {
            loanApplicationCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            loanApplicationCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            loanApplicationCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response loanApplicationCallbackObjectResponse = loanApplicationCallbackObject.callAPI();


        return loanApplicationCallbackObjectResponse;
    }

    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @param isBusinessLending
     * @return
     */
    public Response v1sdMerchantLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,boolean isBusinessLending) {

        CreateLeadLending createLeadPostPaidObject = new CreateLeadLending(isBusinessLending);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();

        try {

            ((AbstractApiV2) createLeadPostPaidObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/CreateLeadBusinessLendingResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return createLeadPostPaidObjectResponse;

    }

    /**
     * Method to add Basic details PAN, DOB and EMAIL
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response updatePanAndDOB(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdatePANAndDOB createLeadPostPaidObject = new UpdatePANAndDOB();


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();

        return createLeadPostPaidObjectResponse;

    }

    /**Method used to get callback for gender and pincode
     *
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response updateGenderAndPincode(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateGenderAndPincode createLeadPostPaidObject = new UpdateGenderAndPincode();


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;

    }
    /**
     * Method used to move to BRE OTP Verified stage
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response callbackBREOTP(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        BREOTPCallback callbackBREOTPObject = new BREOTPCallback();


        for (Map.Entry m : queryParams.entrySet()) {
            callbackBREOTPObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            callbackBREOTPObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            callbackBREOTPObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = callbackBREOTPObject.callAPI();


        return createLeadPostPaidObjectResponse;

    }

    /**Method used to get BRE success callback
     * Used only when BRE Status API gives error
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response breCallbackMCA(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        BRECallbackMCA breCallbackObject = new BRECallbackMCA();

        for (Map.Entry m : queryParams.entrySet()) {
            breCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            breCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            breCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response breCallbackObjectResponse = breCallbackObject.callAPI();

        try {

            ((AbstractApiV2) breCallbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v2/lending/dataUpdate/BRECallbackResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return breCallbackObjectResponse;
    }

    public Response breCallbackMCA(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, String requestJSON) {

        BRECallbackMCA breCallbackObject = new BRECallbackMCA(requestJSON);

        for (Map.Entry m : queryParams.entrySet()) {
            breCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            breCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            breCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response breCallbackObjectResponse = breCallbackObject.callAPI();

        try {

            ((AbstractApiV2) breCallbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v2/lending/dataUpdate/BRECallbackResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return breCallbackObjectResponse;
    }

    /**Method used to get BRE success callback for ARIL
     * Used only when BRE Status API gives error
     * @param queryParams
     * @param headers
     * @param body
     * @param isAril
     * @return
     */
    public Response breCallbackAril(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, boolean isAril) {

        BRECallbackMCA breCallbackObject = new BRECallbackMCA(isAril);

        for (Map.Entry m : queryParams.entrySet()) {
            breCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            breCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            breCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response breCallbackObjectResponse = breCallbackObject.callAPI();

        try {

            ((AbstractApiV2) breCallbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/V2/lending/dataUpdate/BRECallbackArilResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return breCallbackObjectResponse;
    }



    /**
     * Method used when we get Update loan offer stage in BRE status API response.
     * This moves the lead to BRE Success stage
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response updateLoanOffer(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        UpdateLoanOffer updateLoanOfferObject = new UpdateLoanOffer();

        for (Map.Entry m : queryParams.entrySet()) {
            updateLoanOfferObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateLoanOfferObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateLoanOfferObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        updateLoanOfferObject.getRequest().urlEncodingEnabled(false);
        Response updateLoanOfferObjectResponse = updateLoanOfferObject.callAPI();


        return updateLoanOfferObjectResponse;

    }
    /**
     * Used to save the bank details of user
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response saveBankDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        SaveBankDetails saveBankDetailsbject = new SaveBankDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            saveBankDetailsbject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            saveBankDetailsbject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveBankDetailsbject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        saveBankDetailsbject.getRequest().urlEncodingEnabled(false);
        Response saveBankDetailsbjectResponse = saveBankDetailsbject.callAPI();


        return saveBankDetailsbjectResponse;

    }

    public Response saveBankDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,boolean isEmandateTypePresent) {

        SaveBankDetails saveBankDetailsbject = new SaveBankDetails(isEmandateTypePresent);

        for (Map.Entry m : queryParams.entrySet()) {
            saveBankDetailsbject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            saveBankDetailsbject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveBankDetailsbject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        saveBankDetailsbject.getRequest().urlEncodingEnabled(false);
        Response saveBankDetailsbjectResponse = saveBankDetailsbject.callAPI();


        return saveBankDetailsbjectResponse;

    }

    public Response saveBankDetailsV2API(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,boolean isEmandateTypePresent) {

        SaveBankDetailsV2 saveBankDetailsbject = new SaveBankDetailsV2(isEmandateTypePresent);

        for (Map.Entry m : queryParams.entrySet()) {
            saveBankDetailsbject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            saveBankDetailsbject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveBankDetailsbject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        saveBankDetailsbject.getRequest().urlEncodingEnabled(false);
        Response saveBankDetailsbjectResponse = saveBankDetailsbject.callAPI();


        return saveBankDetailsbjectResponse;

    }

    public Response saveBankDetailsNewAPI(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        NewBankAPI saveBankDetailsbject = new NewBankAPI();

        for (Map.Entry m : queryParams.entrySet()) {
            saveBankDetailsbject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            saveBankDetailsbject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveBankDetailsbject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        saveBankDetailsbject.getRequest().urlEncodingEnabled(false);
        Response saveBankDetailsbjectResponse = saveBankDetailsbject.callAPI();


        return saveBankDetailsbjectResponse;

    }

    /**Used to upload cancelled cheque
     * This method is used when we get bank validation pending in save bank details API response.
     * @param queryParams
     * @param headers
     * @param uploadFile
     * @return
     * @throws InterruptedException
     */
    public Response uploadCancelledCheque(Map<String, String> queryParams, Map<String, String> headers,File uploadFile) throws InterruptedException {

        UploadCancelledCheque uploadCancelledChequeObject = new UploadCancelledCheque();

        //Adding queryParams
        for (Map.Entry m : queryParams.entrySet()) {
            uploadCancelledChequeObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            uploadCancelledChequeObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        uploadCancelledChequeObject.addMultipartFormData("file", uploadFile,"image/jpeg");
        Thread.sleep(2000);

        uploadCancelledChequeObject.getRequest().urlEncodingEnabled(false);

        Response UploadDocumentObjectResponse = uploadCancelledChequeObject.callAPI();




        return UploadDocumentObjectResponse;
    }
    /**
     * This is used to get Emandate callback
     * @param queryParams
     * @param headers
     * @param body
     * @return
     * @throws InterruptedException
     */

    public Response getEmandateCallback(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestBodyJsonPath) throws InterruptedException {

        EmandateCallback getEmandateCallbackObject = new EmandateCallback(requestBodyJsonPath);

        //Adding queryParams
        for (Map.Entry m : queryParams.entrySet()) {
            getEmandateCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            getEmandateCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            getEmandateCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response  getEmandateCallbackObjectResponse = getEmandateCallbackObject.callAPI();

        return getEmandateCallbackObjectResponse;
    }

    /**
     * This is used to get Emandate callback
     * @param queryParams
     * @param headers
     * @param body
     * @return
     * @throws InterruptedException
     */

    public Response getEmandateCallback(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) throws InterruptedException {

        EmandateCallback getEmandateCallbackObject = new EmandateCallback();

        //Adding queryParams
        for (Map.Entry m : queryParams.entrySet()) {
            getEmandateCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            getEmandateCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            getEmandateCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response  getEmandateCallbackObjectResponse = getEmandateCallbackObject.callAPI();

        return getEmandateCallbackObjectResponse;
    }


    /**
     * This method is used to update kyc name of user in solution additional info table
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response updateCKYCNameInSAI(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        UpdateSAI updateCKYCNameInSAIObject = new UpdateSAI();

        for (Map.Entry m : queryParams.entrySet()) {
            updateCKYCNameInSAIObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateCKYCNameInSAIObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            updateCKYCNameInSAIObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        updateCKYCNameInSAIObject.getRequest().urlEncodingEnabled(false);
        Response updateCKYCNameInSAIObjectResponse = updateCKYCNameInSAIObject.callAPI();


        return updateCKYCNameInSAIObjectResponse;
    }


    /**
     * This method is used to update kyc name of user in solution additional info table
     * @param queryParams
     * @param headers
     * @param body
     * @param requestjson, stagingUrl
     * @return
     */
    public Response updateCKYCNameInSAI(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestBodyJsonPath) {

        UpdateSAI updateCKYCNameInSAIObject = new UpdateSAI(requestBodyJsonPath);

        for (Map.Entry m : queryParams.entrySet()) {
            updateCKYCNameInSAIObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateCKYCNameInSAIObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            updateCKYCNameInSAIObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        updateCKYCNameInSAIObject.getRequest().urlEncodingEnabled(false);
        Response updateCKYCNameInSAIObjectResponse = updateCKYCNameInSAIObject.callAPI();


        return updateCKYCNameInSAIObjectResponse;
    }


    //------ ----------------DB utility-----------------------------------//

    /**
     * This method is used to make database connection
     */

    public static void MakeConnection() {
        try {
            SshDetails sshDetails = new SshDetails();
            sshDetails.setSshHost(P.CONFIG.get("sshHost"));
            sshDetails.setSshUser(P.CONFIG.get("sshUser2"));
            sshDetails.setSshKeyFilepath(System.getProperty("user.dir")+"/src/main/resources/id_rsa_nishant");
            sshDetails.setRemoteHost(P.CONFIG.get("remoteHost"));
            sshDetails.setDbUserName(P.CONFIG.get("dbUserName"));
            sshDetails.setDbPassword(P.CONFIG.get("dbPassword"));
            sshDetails.setLocalPort(Integer.parseInt(P.CONFIG.get("localPort")));
            sshDetails.setRemotePort(Integer.parseInt(P.CONFIG.get("remotePort")));
            connection = sshConnection.connectToServer(DbName, sshDetails);
        }
        catch (Exception e)
        {e.printStackTrace();}

    }

    /**
     * Method is used to run any update query, just pass the query as parameter in method call
     * @param Query
     * @return
     * @throws SQLException
     */
    public int UpdateQuery(String Query) throws SQLException {
        MakeConnection();
        int UpdateQueryResult = 0 ;
        LOGGER.info("Currently connected DB is  : " +DbName);

        try {
            LOGGER.info("Creating Connection ");
            LOGGER.info("Removing Foreign keys check ");
            int FK = sshConnection.executeUpdate("SET FOREIGN_KEY_CHECKS=0;",connection);
            LOGGER.info("Result of Foreign key query : " +FK);
            LOGGER.info("Foreign keys check Removed \n");

            LOGGER.info("This is generated Query : " + Query);
            int rs = sshConnection.executeUpdate(Query, connection);

            LOGGER.info("This is Result : " + rs);
            UpdateQueryResult = rs;
            sshConnection.closeConnections(connection);
            connection.close();

        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }

        return UpdateQueryResult;
    }

    /**
     * This method is used to get the workflow status id of the node which is passed in parameter
     * @param workflowNode
     * @param leadId
     * @return
     * @throws SQLException
     */

    public String getIdOfWorkflowNode(String workflowNode,String leadId) throws SQLException
    {
        MakeConnection();
        String workflowStatusId=null;

        LOGGER.info("Currently connected DB is  : " +DbName);
        String query = "select id from workflow_status where workflow_node_id="+workflowNode+" and user_business_mapping_id=(select id from user_business_mapping where lead_id='"+leadId+"')";
        LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
            ResultSet rs = sshConnection.executeQuery(query, connection);
            if (rs.next()) {
                LOGGER.info("This is Result : " + rs.getString("id"));
                workflowStatusId =(rs.getString("id"));

                sshConnection.closeConnections(connection);
                connection.close();
            }
        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }

        return workflowStatusId;



    }

    /**
     * This method creates query fro workflow status table and internally calls UpdateQuery method to run the query
     * @param isActive
     * @param id
     * @return
     * @throws SQLException
     */

    public int runUpdateQueryOnWorkflowStatus (int isActive,BigInteger id) throws SQLException
    {
        String query="update workflow_status set is_active="+isActive+" where id="+id;
        LOGGER.info("This is generated Query : " + query);
        int UpdateQueryResult=UpdateQuery(query);

        LOGGER.info("These are Updated Row/s : "+UpdateQueryResult);

        return UpdateQueryResult;


    }

    /**This method is used to get host name from api url present in api. properties file
     * This host is used as header in several callbacks
     * @return
     * @throws IOException
     */
//    public String getHostUrl() throws IOException
//    {
//        Properties prop = new Properties();
//        FileInputStream apiProperties = new FileInputStream("src/main/resources/_api.properties");
//        prop.load(apiProperties);
//        String stagingURL=prop.getProperty("api_url");
//        LOGGER.info("Staging environment is: "+stagingURL);
//        String host=stagingURL.substring(8,37);
//        LOGGER.info("Host URL is: "+host);
//        return host;
//

    // }

    /**This method is used to upload sheet from panel.
     * Used at KAFKA PUSH Stage, after this API, submit application job runs and lead gets closed
     */

    public Response uploadSheetFromPanel(Map<String, String> headers, File fileUpload, String process) throws InterruptedException {

        SheetUpload sheetUploadObject = new SheetUpload();

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            sheetUploadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        sheetUploadObject.addMultipartFormData("file", fileUpload,"text/csv");
        sheetUploadObject.addMultipartFormData("process", process,"*/*");
        Thread.sleep(2000);

        sheetUploadObject.getRequest().urlEncodingEnabled(false);

        Response sheetUploadObjectResponse = sheetUploadObject.callAPI();

        return sheetUploadObjectResponse;


    }

    /**
     * Method to add Basic details PAN, DOB and EMAIL
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response updateAdditionalDetailsTopUp(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        AdditionalDetailsTopup updateAdditionalDetailsTopUpObject = new AdditionalDetailsTopup();


        for (Map.Entry m : queryParams.entrySet()) {
            updateAdditionalDetailsTopUpObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateAdditionalDetailsTopUpObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            updateAdditionalDetailsTopUpObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = updateAdditionalDetailsTopUpObject.callAPI();

        return createLeadPostPaidObjectResponse;

    }

    /**
     * Ckyc Callback for top up
     * @param queryParams
     * @param headers
     * @param body
     * @param isTopup
     * @return
     */
    public Response ckycCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String isTopup) {

        CKYCCallBack ckycCallbackObject = new CKYCCallBack(isTopup);

        for (Map.Entry m : queryParams.entrySet()) {
            ckycCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            ckycCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            ckycCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ckycCallbackObjectResponse = ckycCallbackObject.callAPI();


        return ckycCallbackObjectResponse;
    }

    public Response ckycCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String RequestJsonPath, boolean isNewKycKeys) {

        CKYCCallBack ckycCallbackObject = new CKYCCallBack(RequestJsonPath,isNewKycKeys);

        for (Map.Entry m : queryParams.entrySet()) {
            ckycCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            ckycCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            ckycCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response ckycCallbackObjectResponse = ckycCallbackObject.callAPI();


        return ckycCallbackObjectResponse;
    }
    /**
     * Create lead for SSFB solution
     * @param queryParams
     * @param headers
     * @param body
     * @param solution
     * @param solutionTypeLevel2
     * @return
     */
    public Response v1sdMerchantLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String solution, String solutionTypeLevel2) {

        CreateLeadLending createLeadObject = new CreateLeadLending(solution,solutionTypeLevel2);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }
    /**
     * Fetch lead for business lending solutions
     * @param queryParams
     * @param headers
     * @param body
     * @param isFetchLead
     * @param isBusinessLending
     * @return
     */
    public Response fetchBusinessLendingLeads(Map<String, String> queryParams, Map<String, String> headers) {

        FetchLead_Lending createLeadObject = new FetchLead_Lending();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }



        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }
    /**Method used to get BRE success callback for SSFB solution
     * Used only when BRE Status API gives error
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response breCallbackMCA(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,Boolean isSSFB) {

        BRECallbackMCA breCallbackObject = new BRECallbackMCA(isSSFB);

        for (Map.Entry m : queryParams.entrySet()) {
            breCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            breCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            breCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response breCallbackObjectResponse = breCallbackObject.callAPI();

        try {

            ((AbstractApiV2) breCallbackObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v2/lending/dataUpdate/BRECallbackResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return breCallbackObjectResponse;
    }


    /**
     * Adds additional data to SSFB Lead(religion,marital status,gender)
     * @param queryParams
     * @param headers
     * @return
     */
    public Response additionalDataCaptureCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        AdditionalDataCapture additionalDataCaptureCallbackObject = new AdditionalDataCapture();

        for (Map.Entry m : queryParams.entrySet()) {
            additionalDataCaptureCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            additionalDataCaptureCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            additionalDataCaptureCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response additionalDataCaptureCallbackObjectResponse = additionalDataCaptureCallbackObject.callAPI();


        return additionalDataCaptureCallbackObjectResponse;
    }


    /**
     * Used to submit all tnc set
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response submitApplication(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, String waitForEmandate, String workflowVersion) {

        SubmitApplication submitApplicationObject = new SubmitApplication(waitForEmandate,workflowVersion);

        for (Map.Entry m : queryParams.entrySet()) {
            submitApplicationObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitApplicationObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitApplicationObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = submitApplicationObject.callAPI();


        return submitApplicationObjectResponse;
    }


    public Response submitApplication(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body, String waitForEmandate, String workflowVersion,boolean isOnlyAgreement) {

        SubmitApplication submitApplicationObject = new SubmitApplication(waitForEmandate,workflowVersion,isOnlyAgreement);

        for (Map.Entry m : queryParams.entrySet()) {
            submitApplicationObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitApplicationObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitApplicationObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = submitApplicationObject.callAPI();


        return submitApplicationObjectResponse;
    }


    /**
     * PDC Callback for SSFB
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response pdcLoanApplicationPending(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        PDCLoanApplicationCallback pdcLoanApplicationAcceptedObject = new PDCLoanApplicationCallback();

        for (Map.Entry m : queryParams.entrySet()) {
            pdcLoanApplicationAcceptedObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            pdcLoanApplicationAcceptedObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            pdcLoanApplicationAcceptedObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = pdcLoanApplicationAcceptedObject.callAPI();

        return submitApplicationObjectResponse;
    }

    /**
     * Create lead for Personal Loan solution
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response v1ConsumerLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreatePersonalLoanLead createLeadObject = new CreatePersonalLoanLead();


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }
    /**
     * Method to add Basic details PAN, DOB , EMAIL, Loan Purpose
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response addBasicDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String solution) {

        BasicDetails addBasicDetailsObject = new BasicDetails(solution);

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            addBasicDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        try {

            ((AbstractApiV2) addBasicDetailsObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return addBasicDetailsObjectResponse;
    }
    /**
     * Method for Email Callback
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response verifyEmailCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String solution) {

        EmailCallback verifyEmailCallbackObject = new EmailCallback();

        for (Map.Entry m : queryParams.entrySet()) {
            verifyEmailCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            verifyEmailCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            verifyEmailCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response verifyEmailCallbackObjectResponse = verifyEmailCallbackObject.callAPI();


        return verifyEmailCallbackObjectResponse;
    }


    /**
     * Method to add Basic details PAN, DOB , EMAIL, Loan Purpose
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response updateBasicDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        BasicDetails addBasicDetailsObject = new BasicDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            addBasicDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();


        return addBasicDetailsObjectResponse;
    }

    /**
     * To add address of user
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response addAddressDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        AddAddressDetails addAddressObject = new AddAddressDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            addAddressObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addAddressObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            addAddressObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addAddressObjectResponse = addAddressObject.callAPI();


        return addAddressObjectResponse;
    }

    /*
     * Method to add Basic details PAN, DOB and EMAIL for sd merchant
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response basicDetailsMCA(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        BasicDetailsMCA addBasicDetailsObject = new BasicDetailsMCA();

        for (Map.Entry m : queryParams.entrySet()) {
            addBasicDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            addBasicDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            addBasicDetailsObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response addBasicDetailsObjectResponse = addBasicDetailsObject.callAPI();

        try {

            ((AbstractApiV2) addBasicDetailsObjectResponse).validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLeadResponseSchema.json");
        } catch (Exception e) {

            System.out.println("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }
        return addBasicDetailsObjectResponse;
    }
    /**
     * Create Personal loan with hero lead
     * @param queryParams
     * @param headers
     * @param body
     * @param solutionType
     * @return
     */
    public Response v1ConsumerLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String solutionType) {

        CreatePersonalLoanLead createLeadObject = new CreatePersonalLoanLead(solutionType);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }
    /**
     * Update PAN details. This API can be called anywhere during workflow execution and details will be updated
     * @param queryParams
     * @param headers
     * @param body
     * @param solution
     * @return
     */

    public Response updateAdditionalPanDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        PANDOBDataUpdate panDataUpdateObject = new PANDOBDataUpdate();

        for (Map.Entry m : queryParams.entrySet()) {
            panDataUpdateObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            panDataUpdateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            panDataUpdateObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response panDataUpdateObjectResponse = panDataUpdateObject.callAPI();


        return panDataUpdateObjectResponse;
    }
    /**
     * Update DOB details. This API can be called anywhere during workflow execution and details will be updated
     * @param queryParams
     * @param headers
     * @param body
     * @param isDOB
     * @return
     */
    public Response updateAdditionalDOBDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,boolean isDOB) {

        PANDOBDataUpdate DOBDataUpdateObject = new PANDOBDataUpdate(isDOB);

        for (Map.Entry m : queryParams.entrySet()) {
            DOBDataUpdateObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            DOBDataUpdateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            DOBDataUpdateObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response DOBDataUpdateObjectResponse = DOBDataUpdateObject.callAPI();


        return DOBDataUpdateObjectResponse;
    }
    /**
     * Method to add Basic details For hero lender
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response addBasicDetailsHero(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String solution) {

        PANDOBDataUpdate basicDetailsUpdateObject = new PANDOBDataUpdate(solution);

        for (Map.Entry m : queryParams.entrySet()) {
            basicDetailsUpdateObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            basicDetailsUpdateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            basicDetailsUpdateObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response basicDetailsUpdateObjectResponse = basicDetailsUpdateObject.callAPI();



        return basicDetailsUpdateObjectResponse;
    }
    /**
     * Update lead call to add occupation details
     * @param queryParams
     * @param headers
     * @param body
     * @param isUpdate
     * @return
     */

    public Response v1ConsumerLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,boolean isUpdate) {

        BasicDetails createLeadObject = new BasicDetails(isUpdate);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }

    /**
     * Update lead call to accept loan offer
     * @param queryParams
     * @param headers
     * @param body
     * @param isUpdate
     * @return
     */

    public Response updateLoanOffer(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,boolean isUpdate) {

        CreatePersonalLoanLead createLeadObject = new CreatePersonalLoanLead(isUpdate);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }


    public Response updateLoanOffer(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,boolean isUpdate,boolean isNewLender) {

        CreatePersonalLoanLead createLeadObject = new CreatePersonalLoanLead(isUpdate,isNewLender);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }

    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response secondBREInitiateCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        SecondBREInitiateCallback secondBREInitiateCallbackObject = new SecondBREInitiateCallback();

        for (Map.Entry m : queryParams.entrySet()) {
            secondBREInitiateCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            secondBREInitiateCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            secondBREInitiateCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = secondBREInitiateCallbackObject.callAPI();


        return callbackObjectResponse;
    }



    /**
     *Second BRE initiate call using LOS API
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response secondBREInitiate( Map<String, String> headers) {

        BREInitiate secondBREInitiateObject = new BREInitiate();


        for (Map.Entry m : headers.entrySet()) {
            secondBREInitiateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response callbackObjectResponse = secondBREInitiateObject.callAPI();


        return callbackObjectResponse;
    }

    /**
     *Third BRE initiate call using LOS API
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response thirdBREInitiate( Map<String, String> headers) {

        ThirdBREInitiate secondBREInitiateObject = new ThirdBREInitiate();


        for (Map.Entry m : headers.entrySet()) {
            secondBREInitiateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response callbackObjectResponse = secondBREInitiateObject.callAPI();


        return callbackObjectResponse;
    }


    /**
     *Second BRE initiate call using LOS API
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response secondBREStatus( Map<String, String> headers) {

        BREStatus secondBREInitiateObject = new BREStatus();


        for (Map.Entry m : headers.entrySet()) {
            secondBREInitiateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response callbackObjectResponse = secondBREInitiateObject.callAPI();


        return callbackObjectResponse;
    }

    /**
     *Second BRE initiate call using LOS API
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response thirdBREStatus( Map<String, String> headers) {

        ThirdBREStatus secondBREInitiateObject = new ThirdBREStatus();


        for (Map.Entry m : headers.entrySet()) {
            secondBREInitiateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response callbackObjectResponse = secondBREInitiateObject.callAPI();


        return callbackObjectResponse;
    }
    /**
     * Update PAN details. This API can be called anywhere during workflow execution and details will be updated
     * @param queryParams
     * @param headers
     * @param body
     * @param solution
     * @return
     */

    public Response updateAdditionalPanDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String solution, boolean isBRECallbackUpdate) {

        PANDOBDataUpdate panDataUpdateObject = new PANDOBDataUpdate(solution,isBRECallbackUpdate);

        for (Map.Entry m : queryParams.entrySet()) {
            panDataUpdateObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            panDataUpdateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            panDataUpdateObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response panDataUpdateObjectResponse = panDataUpdateObject.callAPI();


        return panDataUpdateObjectResponse;
    }



    /**
     * Update PAN details. This API can be called anywhere during workflow execution and details will be updated
     * @param queryParams
     * @param headers
     * @param body
     * @param solution
     * @return
     */

    public Response updateAdditionalPanDetails(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,boolean isThirdBRE, boolean isBRECallbackUpdate) {

        PANDOBDataUpdate panDataUpdateObject = new PANDOBDataUpdate(isThirdBRE,isBRECallbackUpdate);

        for (Map.Entry m : queryParams.entrySet()) {
            panDataUpdateObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            panDataUpdateObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            panDataUpdateObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response panDataUpdateObjectResponse = panDataUpdateObject.callAPI();


        return panDataUpdateObjectResponse;
    }

    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response secondBREInitiateCallback(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,boolean isAdditionalDataUpdate) {

        SecondBREInitiateCallback secondBREInitiateCallbackObject = new SecondBREInitiateCallback(isAdditionalDataUpdate);

        for (Map.Entry m : queryParams.entrySet()) {
            secondBREInitiateCallbackObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            secondBREInitiateCallbackObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            secondBREInitiateCallbackObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response callbackObjectResponse = secondBREInitiateCallbackObject.callAPI();


        return callbackObjectResponse;
    }


    /**
     * Used to submit all tnc set for personla loan v2
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response submitApplicationHero(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String solution) {

        SubmitApplication submitApplicationObject = new SubmitApplication(solution);

        for (Map.Entry m : queryParams.entrySet()) {
            submitApplicationObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitApplicationObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitApplicationObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = submitApplicationObject.callAPI();

        return submitApplicationObjectResponse;
    }


    /**
     * Used to submit all tnc set for personla loan v2
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response submitApplicationHerov3(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String solution,String requestJsonPath) {

        SubmitApplication submitApplicationObject = new SubmitApplication(requestJsonPath,true);

        for (Map.Entry m : queryParams.entrySet()) {
            submitApplicationObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            submitApplicationObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            submitApplicationObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = submitApplicationObject.callAPI();

        return submitApplicationObjectResponse;
    }



    /**
     * PDC Callback for HERO PL
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */
    public Response pdcLoanApplicationAccepted(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,boolean isPersonalLoan) {

        PDCLoanApplicationCallback pdcLoanApplicationAcceptedObject = new PDCLoanApplicationCallback(isPersonalLoan);

        for (Map.Entry m : queryParams.entrySet()) {
            pdcLoanApplicationAcceptedObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            pdcLoanApplicationAcceptedObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            pdcLoanApplicationAcceptedObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response submitApplicationObjectResponse = pdcLoanApplicationAcceptedObject.callAPI();

        return submitApplicationObjectResponse;
    }

    /**
     * Update lead call to accept loan offer  for clix revamp
     * @param queryParams
     * @param headers
     * @param body
     * @param isUpdate
     * @return
     */

    public Response updateLoanOffer(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String solution, String solutionTypeLevel2) {

        CreatePersonalLoanLead createLeadObject = new CreatePersonalLoanLead(solution, solutionTypeLevel2);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadObjectResponse = createLeadObject.callAPI();


        return createLeadObjectResponse;

    }



    /**
     * Method to create postpaid lead for new lenders
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response v1sdMerchantLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,boolean newLender,String solutionName) {

        CreateLeadLending createLeadPostPaidObject = new CreateLeadLending(newLender,solutionName);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }


    public Response updateAddressDetailsInDataUpdateCallback(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,boolean isSolutionMetaData, boolean isAddressDetailsRequired) {

        UpdatePANAndDOB createLeadPostPaidObject = new UpdatePANAndDOB(true,true);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();

        return createLeadPostPaidObjectResponse;

    }


    public Response updateAddressDetailsInDataUpdateCallback(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, boolean isAddressDetailsRequired) {

        UpdatePANAndDOB createLeadPostPaidObject = new UpdatePANAndDOB(true);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();

        return createLeadPostPaidObjectResponse;

    }



    public Response updateAddressDetailsInDataUpdateCallback(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, boolean isAddressDetailsRequired, String solutionTypeLevel2) {

        UpdatePANAndDOB createLeadPostPaidObject = new UpdatePANAndDOB(true,solutionTypeLevel2);


        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();

        return createLeadPostPaidObjectResponse;

    }


    /**
     * Method to hit fetch lead api and return the response object
     * This is using new Fetch lead workflow API
     */
    public Response fetchLeadDetails(String leadId,String solution,String solutionTypeLevel2,String entityType,String channel,String fetchStrategy, String sessionToken,String custId)
    {

        Map<String,String> queryParams=new HashMap<String,String>();

        Map<String,String>commonQueryparams=setcommonQueryParameters(leadId,solution,channel,entityType);

        queryParams.putAll(commonQueryparams);
        queryParams.put("fetchStrategy",fetchStrategy);

        Response responseObject=null;

        Map<String,String> headers=new HashMap<String,String>();
        if(solution.equals("pl_distribution") || solution.equals("personal_loan_v3")|| solution.equals("personal_loan_renewal")|| solution.equals("business_lending_v3"))
        {
            String token = generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers.put("Authorization",token);
            headers.put("custId",custId);
        }
        else
        {
            headers.put("session_token",sessionToken);
        }



        responseObject= fetchLeadDetailsUsingWorkflowAPI(queryParams, headers);

        verifyResponseCodeAs200OK(responseObject);
        return responseObject;

    }


    public Response fetchPLPOLeadDetails(String leadId,String solution,String entityType,String channel,String fetchStrategy, String sessionToken,String custId)
    {

        Map<String,String> queryParams=new HashMap<String,String>();

        Map<String,String>commonQueryparams=setcommonQueryParameters(leadId,solution,channel,entityType);

        queryParams.putAll(commonQueryparams);
        queryParams.put("fetchStrategy",fetchStrategy);

        Response responseObject=null;

        Map<String,String> headers=new HashMap<String,String>();

        String token = generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers.put("Authorization",token);
        headers.put("custId",custId);




        responseObject= fetchLeadDetailsUsingWorkflowAPI(queryParams, headers);

        verifyResponseCodeAs200OK(responseObject);
        return responseObject;

    }


    public Response fetchLeadDetailsBackFillingAPI(String leadId,String solution,String solutionTypeLevel2,String entityType,String channel,String fetchStrategy, String sessionToken,String custId,String stagingUrl)
    {

        Map<String,String> queryParams=new HashMap<String,String>();

        //Map<String,String>commonQueryparams=setcommonQueryParameters(leadId,solution,channel,entityType);

        //queryParams.putAll(commonQueryparams);
        queryParams.put("solution",solution);
        queryParams.put("entityType",entityType);
        queryParams.put("leadId",leadId);

        Response responseObject=null;

        Map<String,String> headers=new HashMap<String,String>();
        if(solution.equals("pl_distribution") || solution.equals("personal_loan_v3")|| solution.equals("personal_loan_renewal") ||solution.equals("business_lending_v3") )
        {
            String token = generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers.put("Authorization",token);
            headers.put("custId",custId);
        }
        else
        {
            headers.put("session_token",sessionToken);
        }

        FetchLeadBackfillingAPI fetchLeadDetailsObject = new FetchLeadBackfillingAPI(stagingUrl);

        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchLeadDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchLeadDetailsObjectResponse = fetchLeadDetailsObject.callAPI();



        // responseObject= fetchLeadDetailsUsingWorkflowAPI(queryParams, headers);

        verifyResponseCodeAs200OK(fetchLeadDetailsObjectResponse);
        return fetchLeadDetailsObjectResponse;

    }



    /**
     * Method to get the existing bank details
     * This is using new Fetch lead workflow API
     */
    public Response fetchExistingLeadData(String solution,String entityType,String channel,String solutionTypeLevel2,String sessionToken)
    {

        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
        queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

        Response responseObject=null;

        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token",sessionToken);


        responseObject= fetchExistingLeadDetails(queryParams, headers);

        if(responseObject.getStatusCode()==200)
            return responseObject;



        return null;

    }
    /**
     * Method to fetch all the data of the lead using new workflow API
     * @param queryParams
     * @param headers
     * @return
     */
    public Response fetchLeadDetailsUsingWorkflowAPI(Map<String, String> queryParams, Map<String, String> headers) {

        FetchLeadWorkflow fetchLeadDetailsObject = new FetchLeadWorkflow();

        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchLeadDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchLeadDetailsObjectResponse = fetchLeadDetailsObject.callAPI();


        return fetchLeadDetailsObjectResponse;
    }

    /**
     * Method to fetch the existing lead details
     * @param queryParams
     * @param headers
     * @return
     */
    public Response fetchExistingLeadDetails(Map<String, String> queryParams, Map<String, String> headers) {

        FetchExistingLeadDetails fetchLeadDetailsObject = new FetchExistingLeadDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            fetchLeadDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchLeadDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response fetchLeadDetailsObjectResponse = fetchLeadDetailsObject.callAPI();


        return fetchLeadDetailsObjectResponse;
    }

    /**
     * Method to create postpaid lead using  new workflow API
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response v1WorkflowLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        CreateLeadWorkflow createLeadPostPaidObject = new CreateLeadWorkflow();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }


    /**
     * Reset lending lead using leadid, custid and JWT token
     * @param queryParams
     * @param headers
     * @return
     */
    public Response resetLendingLeads(Map<String, String> queryParams, Map<String, String> headers) {

        LeadReset resetLendingLeadsObject = new LeadReset();

        for (Map.Entry m : queryParams.entrySet()) {
            resetLendingLeadsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            resetLendingLeadsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response resetLendingLeadsObjectResponse = resetLendingLeadsObject.callAPI();


        return resetLendingLeadsObjectResponse;
    }

    public Response updateBureauDataSetInSAI(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,boolean isBureauDataUpdate) {

        UpdateSAI updateCKYCNameInSAIObject = new UpdateSAI(isBureauDataUpdate);

        for (Map.Entry m : queryParams.entrySet()) {
            updateCKYCNameInSAIObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateCKYCNameInSAIObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            updateCKYCNameInSAIObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        updateCKYCNameInSAIObject.getRequest().urlEncodingEnabled(false);
        Response updateCKYCNameInSAIObjectResponse = updateCKYCNameInSAIObject.callAPI();


        return updateCKYCNameInSAIObjectResponse;
    }



//    /* Data Update in SAI for Reset Feature */
//    public Response updateBureauDataSetInSAIForReset(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body , String requestBodyJsonPath) {
//
//        UpdateSAIReset updateInSAIObjectReset = new UpdateSAIReset(requestBodyJsonPath);
//
//        for (Map.Entry m : queryParams.entrySet()) {
//            updateInSAIObjectReset.addParameter(m.getKey().toString(), m.getValue().toString());
//        }
//
//        for (Map.Entry m : headers.entrySet()) {
//            updateInSAIObjectReset.setHeader(m.getKey().toString(), m.getValue().toString());
//        }
//        for (Map.Entry m : body.entrySet()) {
//            updateInSAIObjectReset.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
//        }
//        updateInSAIObjectReset.getRequest().urlEncodingEnabled(false);
//        Response updateInSAIObjectResponseReset = updateInSAIObjectReset.callAPI();
//
//
//        return updateInSAIObjectResponseReset;
//    }
//
//    /* Data Update Method ENDS */



    public Response UpdateLead(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestJson ) {

        Request.MerchantService.v1.workflow.lead.UpdateLead updateCKYCNameInSAIObject = new Request.MerchantService.v1.workflow.lead.UpdateLead(requestJson);

        for (Map.Entry m : queryParams.entrySet()) {
            updateCKYCNameInSAIObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateCKYCNameInSAIObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            updateCKYCNameInSAIObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        updateCKYCNameInSAIObject.getRequest().urlEncodingEnabled(false);
        Response updateCKYCNameInSAIObjectResponse = updateCKYCNameInSAIObject.callAPI();


        return updateCKYCNameInSAIObjectResponse;
    }





    public Response updateLenderDataSetSetInSAI(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestData) {

        UpdateSAI updateCKYCNameInSAIObject = new UpdateSAI(requestData);

        for (Map.Entry m : queryParams.entrySet()) {
            updateCKYCNameInSAIObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateCKYCNameInSAIObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            updateCKYCNameInSAIObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        updateCKYCNameInSAIObject.getRequest().urlEncodingEnabled(false);
        Response updateCKYCNameInSAIObjectResponse = updateCKYCNameInSAIObject.callAPI();


        return updateCKYCNameInSAIObjectResponse;
    }

    public Response v2FetchCIR(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body) {

        FetchCIR createLeadPostPaidObject = new FetchCIR();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }


        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }


    public Response v1WorkflowLeadUpdateOffer(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, boolean isOfferAccepted) {

        CreateLeadWorkflow createLeadPostPaidObject = new CreateLeadWorkflow(isOfferAccepted);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }

    public Response v1WorkflowLeadLoanAgreementAccept(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String accept) {

        CreateLeadWorkflow createLeadPostPaidObject = new CreateLeadWorkflow(accept);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }

    public Response v1WorkflowLeadCallback(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body) {

        LeadWorkflowCallback createLeadPostPaidObject = new LeadWorkflowCallback();

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }

    /**
     * Method to create stashfin lead using  new workflow API
     * @param queryParams
     * @param headers
     * @param body
     * @return
     */

    public Response v1WorkflowLeadStashfin(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String requestBodyPath) {

        CreateLeadWorkflow createLeadPostPaidObject = new CreateLeadWorkflow(requestBodyPath,true);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }

    /**
     * Method for BRE1 Callback
     * @param queryParams
     * @param headers
     * @param body
     * @param Request json
     * @return
     */
    public Response v1WorkflowLeadCallbackStashfin(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestBodyPath) {

        LeadWorkflowCallback createLeadPostPaidObject = new LeadWorkflowCallback(requestBodyPath);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();


        return createLeadPostPaidObjectResponse;
    }


    /**
     * Method for BRE2 Callback
     * @param queryParams
     * @param headers
     * @param body
     * @param Request json
     * @param BREtype
     * @return
     */
    public Response v1WorkflowLeadCallbackStashfin(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestBodyPath,String breType) {

        LeadWorkflowCallback createLeadPostPaidObject = new LeadWorkflowCallback(requestBodyPath);

        for (Map.Entry m : queryParams.entrySet()) {
            createLeadPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            createLeadPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            createLeadPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response createLeadPostPaidObjectResponse = createLeadPostPaidObject.callAPI();
        return createLeadPostPaidObjectResponse;
    }


    public Response createTokenForStashfinLender() {

        CreateToken createTokenObject = new CreateToken();

        Map<String,String> headers=new HashMap<String,String>();

        headers.put("Content-Type", "application/json");


        for (Map.Entry m : headers.entrySet()) {
            createTokenObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Response createTokenObjectResponse = createTokenObject.callAPI();

        clientToken=createTokenObjectResponse.jsonPath().getString("results");

        return createTokenObjectResponse;

    }

    public Response resetLenderLead(String consumerNumber) {

        LenderLeadReset createTokenObject = new LenderLeadReset();


        Map<String,String> headers=new HashMap<String,String>();

        headers.put("Content-Type", "application/json");
        headers.put("client-token", clientToken);


        for (Map.Entry m : headers.entrySet()) {
            createTokenObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("phone_number", consumerNumber);


        for (Map.Entry m : queryParams.entrySet()) {
            createTokenObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response createTokenObjectResponse = createTokenObject.callAPI();

        Assert.assertEquals(createTokenObjectResponse.jsonPath().getString("results"),"request processed successfully");
        Assert.assertEquals(createTokenObjectResponse.jsonPath().getString("status"),"true");


        return createTokenObjectResponse;

    }

    /**
     *
     * Method to generate JWT token using epouch time
     * @param custId
     * @return
     */
    public String generateJwtTokenUsingEpochTime(String clientId,String custId) {
        String token = "";

        Date date = new Date();
        long unixTime = date.getTime();
        System.out.println(unixTime);

        Algorithm buildAlgorithm = Algorithm.HMAC256("29589d4d-9967-4851-8ba0-5984ab09a9ed");
        token = JWT.create().withClaim("custId", custId)
                .withClaim("clientId", clientId)
                .withIssuedAt(date).sign(buildAlgorithm);


        return token;
    }

    /**
     * Verify  Response Code as 200 OK
     * @param responseObject
     */
    public void verifyResponseCodeAs200OK(Response responseObject) {

        LOGGER.info("Http Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),200);

    }


    /**
     * Verify  Response Code as 400 Bad Request
     * @param responseObject
     */
    public void verifyResponseCodeAs400BadRequest(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),400);

    }

    /**
     * Verify  Response Code as 500 Internal Server error
     * @param responseObject
     */
    public void verifyResponseCodeAs500InternalServerError(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),500);

    }
    /**
     * Verify  Response Code as 415 unsupported media type
     * @param responseObject
     */
    public void verifyResponseCodeAs415UnsupportedMediaType(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),415);

    }

    /**
     * Verify  Response Code as 401 UnAuthorized
     * @param responseObject
     */
    public void verifyResponseCodeAs401Unauthorized(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),401);

    }


    /**
     * Verify  Response Code as 404 Not Found
     * @param responseObject
     */
    public void verifyResponseCodeAs404NotFound(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),404);

    }


    /**
     * Verify  Response Code as 403 Forbidden
     * @param responseObject
     */
    public void verifyResponseCodeAs403Forbidden(Response responseObject) {

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        Assert.assertEquals(responseObject.getStatusCode(),403);

    }

    /**
     * Method to set query parameters which are common in for all lending solutions
     * @return
     */
    public static Map<String, String> setcommonQueryParameters(String leadId, String solutionType,String channel, String entityType) {


        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId",leadId);
        queryParams.put("solution",solutionType);
        queryParams.put("channel",channel);
        queryParams.put("entityType",entityType);


        return queryParams;
    }


    public static HashMap<String, String> setHeadersReceivedFromFE() {


        HashMap<String, String> headers = new HashMap<String, String>();
        headers.put("longitude",LendingConstants.LONGITUDE);
        headers.put("latitude",LendingConstants.LATITUDE);
        headers.put("androidId",LendingConstants.androidId);
        headers.put("deviceIdentifier",LendingConstants.deviceIdentifier);
        headers.put("osVersion",LendingConstants.osVersion);
        headers.put("appVersion",LendingConstants.appVersion);
        headers.put("deviceManufacturer",LendingConstants.deviceManufacturer);
        headers.put("deviceName",LendingConstants.deviceName);

        return headers;



    }


    public  Response BRE1CallbackforMCA(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"bureau\":\"CIBIL\",\"bureauKicker\":false,\"creditScore\":801,\"field_investigation_needed\":false,\"isRenewal\":false,\"lastFetchDate\":1641945600000,\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":300000,\"maxTenure\":720,\"minLoanAmount\":10000,\"minTenure\":90,\"newOfferGenerated\":false,\"offerId\":\"mca_shivangi_v3_2_3988475c\",\"pfFeeRate\":1,\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"rateOfInterest\":20,\"rejectionReason\":\"\",\"skipMandate\":false,\"supplementaryOfferDetails\":[{\"action\":\"SKIP_KYC\",\"amountBand\":\"L4\",\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"city\":\"Kolkata\",\"createdAt\":\"2022-01-17T13:39:54.237+05:30\",\"customerId\":\"1001789105\",\"emandateType\":\"OPTIONAL\",\"gmvAbove5000And90d\":1,\"gst\":18,\"incentiveRate\":0,\"interest\":20,\"isAcceptanceAbove5000\":1,\"isActive\":1,\"isEmandateEligible\":0,\"isIncentiveAllowed\":0,\"isMigrationWhitelisted\":0,\"isPaytmVintageOlderThan90d\":1,\"isRestrictedMerchant\":0,\"isSiMandatory\":\"1\",\"isTopup\":0,\"lender\":7,\"maxTenure\":365,\"merchantId\":\"rhAFTB32462034913135\",\"minAmount\":10000,\"minTenure\":90,\"modelType\":\"Heterogeneous\",\"offerEndDate\":\"2022-11-10\",\"offerId\":\"mca_shivangi_v3_2_3988475c_SUPPLEMENTARY_OFFER_163a199\",\"offerStartDate\":\"2020-11-29\",\"processingFeePercentage\":1,\"productEnum\":\"MCA_WITH_SSFB\",\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"reasons\":\"[]\",\"riskGrade\":\"MCA|GHB64\",\"roi\":20,\"sourceOfWhitelist\":\"Risk\",\"status\":\"SUCCESS\",\"tenure\":720,\"transactionId\":\"3d9ac039-2e56-4f9d-93d0-18928e7815cf\",\"updatedAt\":\"2022-01-17T13:39:54.237+05:30\",\"whiteListMonth\":\"2021-07-07\"}],\"supplementaryOfferResponse\":{\"action\":\"SKIP_KYC\",\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"bureau\":\"CIBIL\",\"bureauKicker\":false,\"creditScore\":801,\"field_investigation_needed\":false,\"isRenewal\":false,\"lastFetchDate\":1641945600000,\"loanDownGradable\":false,\"loan_offered\":true,\"maxTenure\":365,\"minLoanAmount\":10000,\"minTenure\":90,\"newOfferGenerated\":false,\"offerId\":\"mca_shivangi_v3_2_3988475c_SUPPLEMENTARY_OFFER_163a199\",\"pfFeeRate\":1,\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"rateOfInterest\":20,\"skipMandate\":false}}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BRE_LAST_FETCH_DATE", "2022-01-19");
        body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
        body.put("OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/BRE1CallbackForMCARequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response BRE1CallbackforMCAABFL(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"avgGMVgrid\":\"V5\",\"baseId\":\"mca_abfl_OE_prefill_shivangi_ccce46d2\",\"behaviouralScore\":5.0,\"best6MonthsGmvGrid\":\"V51\",\"bureau\":\"CIBIL\",\"bureauDecile\":\"5\",\"bureauKicker\":false,\"creditScore\":838,\"field_investigation_needed\":false,\"gmvIndex\":2,\"incentiveRate\":0.0,\"isAcceptanceAbove5000\":1,\"isBSAOffer\":false,\"isBre2Required\":false,\"isEmandateEligible\":0,\"isIncentiveAllowed\":0,\"isPaytmVintageOlderThan90d\":1,\"isRenewal\":false,\"isRestrictedMerchant\":0,\"isSiMandatory\":\"1\",\"lastFetchDate\":1681862400000,\"latestmonthgmvgrid\":\"V51\",\"loanAmountCappingGrid\":\"L24\",\"loanDownGradable\":false,\"loan_offered\":true,\"lowestGmvgrid\":\"V11\",\"maxLoanAmount\":80000.0,\"maxTenure\":180,\"mcrsDecile\":5,\"minLoanAmount\":10000.0,\"minTenure\":180,\"newOfferGenerated\":false,\"offerEndDate\":\"Sun Jul 30 00:00:00 IST 2023\",\"offerId\":\"d9ce04e4-530b-411e-8fae-b9aa0e51c9b3\",\"offerStartDate\":\"Wed May 10 00:00:00 IST 2023\",\"pfFeeRate\":1.0,\"productId\":\"104\",\"productVersion\":1,\"rateOfInterest\":35.0,\"renewalEnhancement\":0,\"renewalLastLoanClosureGrid\":\"RN2\",\"riskGrade\":\"MCA|BWB64\",\"roiCode\":\"R7\",\"skipMandate\":false,\"sourceOfWhitelist\":\"Risk\",\"tenureCappingGrid\":\"T7\",\"vintageCode\":\"V3\"}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BASE_ID","mca_abfl_OE_prefill_shivangi_ccce46d2");
        body.put("BRE_LAST_FETCH_DATE", "Wed Apr 19 05:30:00 IST 2023");
        body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
        body.put("OFFER_DETAILS", new Gson().toJsonTree(offerDetails));
        body.put("LOAN_OFFER_ID","d9ce04e4-530b-411e-8fae-b9aa0e51c9b3");
        body.put("BRE1_SUCCESS","TRUE");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/mcaABFL/BRE1MockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }


    public  Response BRE1CallbackforMCAClix(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"bureau\":\"CIBIL\",\"bureauKicker\":false,\"creditScore\":801,\"field_investigation_needed\":false,\"isRenewal\":false,\"lastFetchDate\":1641945600000,\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":300000,\"maxTenure\":720,\"minLoanAmount\":10000,\"minTenure\":90,\"newOfferGenerated\":false,\"offerId\":\"mca_shivangi_v3_2_3988475c\",\"pfFeeRate\":1,\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"rateOfInterest\":20,\"rejectionReason\":\"\",\"skipMandate\":false,\"supplementaryOfferDetails\":[{\"action\":\"SKIP_KYC\",\"amountBand\":\"L4\",\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"city\":\"Kolkata\",\"createdAt\":\"2022-01-17T13:39:54.237+05:30\",\"customerId\":\"1001789105\",\"emandateType\":\"OPTIONAL\",\"gmvAbove5000And90d\":1,\"gst\":18,\"incentiveRate\":0,\"interest\":20,\"isAcceptanceAbove5000\":1,\"isActive\":1,\"isEmandateEligible\":0,\"isIncentiveAllowed\":0,\"isMigrationWhitelisted\":0,\"isPaytmVintageOlderThan90d\":1,\"isRestrictedMerchant\":0,\"isSiMandatory\":\"1\",\"isTopup\":0,\"lender\":7,\"maxTenure\":365,\"merchantId\":\"rhAFTB32462034913135\",\"minAmount\":10000,\"minTenure\":90,\"modelType\":\"Heterogeneous\",\"offerEndDate\":\"2022-11-10\",\"offerId\":\"mca_shivangi_v3_2_3988475c_SUPPLEMENTARY_OFFER_163a199\",\"offerStartDate\":\"2020-11-29\",\"processingFeePercentage\":1,\"productEnum\":\"MCA_WITH_SSFB\",\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"reasons\":\"[]\",\"riskGrade\":\"MCA|GHB64\",\"roi\":20,\"sourceOfWhitelist\":\"Risk\",\"status\":\"SUCCESS\",\"tenure\":720,\"transactionId\":\"3d9ac039-2e56-4f9d-93d0-18928e7815cf\",\"updatedAt\":\"2022-01-17T13:39:54.237+05:30\",\"whiteListMonth\":\"2021-07-07\"}],\"supplementaryOfferResponse\":{\"action\":\"SKIP_KYC\",\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"bureau\":\"CIBIL\",\"bureauKicker\":false,\"creditScore\":801,\"field_investigation_needed\":false,\"isRenewal\":false,\"lastFetchDate\":1641945600000,\"loanDownGradable\":false,\"loan_offered\":true,\"maxTenure\":365,\"minLoanAmount\":10000,\"minTenure\":90,\"newOfferGenerated\":false,\"offerId\":\"mca_shivangi_v3_2_3988475c_SUPPLEMENTARY_OFFER_163a199\",\"pfFeeRate\":1,\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"rateOfInterest\":20,\"skipMandate\":false}}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BASE_ID", "MCA_shivangi_STP1_65f13eea");
        body.put("LOAN_OFFER_ID", "MCA_shivangi_STP1_65f13eea_NEW_OFFER_MCA_931ac2f");
        body.put("BRE_LAST_FETCH_DATE", "2022-01-19");
        body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
        body.put("OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/BRE1CallbackForMCARequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response BRE2CallbackforMCA(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"bureau\":\"CIBIL\",\"bureauKicker\":false,\"creditScore\":801,\"field_investigation_needed\":false,\"isRenewal\":false,\"lastFetchDate\":1641945600000,\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":300000,\"maxTenure\":720,\"minLoanAmount\":10000,\"minTenure\":90,\"newOfferGenerated\":false,\"offerId\":\"mca_shivangi_v3_2_3988475c\",\"pfFeeRate\":1,\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"rateOfInterest\":20,\"rejectionReason\":\"\",\"skipMandate\":false,\"supplementaryOfferDetails\":[{\"action\":\"SKIP_KYC\",\"amountBand\":\"L4\",\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"city\":\"Kolkata\",\"createdAt\":\"2022-01-17T13:39:54.237+05:30\",\"customerId\":\"1001789105\",\"emandateType\":\"OPTIONAL\",\"gmvAbove5000And90d\":1,\"gst\":18,\"incentiveRate\":0,\"interest\":20,\"isAcceptanceAbove5000\":1,\"isActive\":1,\"isEmandateEligible\":0,\"isIncentiveAllowed\":0,\"isMigrationWhitelisted\":0,\"isPaytmVintageOlderThan90d\":1,\"isRestrictedMerchant\":0,\"isSiMandatory\":\"1\",\"isTopup\":0,\"lender\":7,\"maxTenure\":365,\"merchantId\":\"rhAFTB32462034913135\",\"minAmount\":10000,\"minTenure\":90,\"modelType\":\"Heterogeneous\",\"offerEndDate\":\"2022-11-10\",\"offerId\":\"mca_shivangi_v3_2_3988475c_SUPPLEMENTARY_OFFER_163a199\",\"offerStartDate\":\"2020-11-29\",\"processingFeePercentage\":1,\"productEnum\":\"MCA_WITH_SSFB\",\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"reasons\":\"[]\",\"riskGrade\":\"MCA|GHB64\",\"roi\":20,\"sourceOfWhitelist\":\"Risk\",\"status\":\"SUCCESS\",\"tenure\":720,\"transactionId\":\"3d9ac039-2e56-4f9d-93d0-18928e7815cf\",\"updatedAt\":\"2022-01-17T13:39:54.237+05:30\",\"whiteListMonth\":\"2021-07-07\"}],\"supplementaryOfferResponse\":{\"action\":\"SKIP_KYC\",\"baseId\":\"mca_shivangi_v3_2_3988475c\",\"bureau\":\"CIBIL\",\"bureauKicker\":false,\"creditScore\":801,\"field_investigation_needed\":false,\"isRenewal\":false,\"lastFetchDate\":1641945600000,\"loanDownGradable\":false,\"loan_offered\":true,\"maxTenure\":365,\"minLoanAmount\":10000,\"minTenure\":90,\"newOfferGenerated\":false,\"offerId\":\"mca_shivangi_v3_2_3988475c_SUPPLEMENTARY_OFFER_163a199\",\"pfFeeRate\":1,\"productId\":\"10000020\",\"productType\":\"mca\",\"productVersion\":1,\"rateOfInterest\":20,\"skipMandate\":false}}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE2_SUCCESS");
        body.put("BASE_ID", "mca_1001644263_3bd73505");
        body.put("LOAN_OFFER_ID", "mca_1001644263_3bd73505");
        body.put("BRE_LAST_FETCH_DATE", "2022-01-19");
        body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
        body.put("OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/BRE2CallbackForMCARequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response BRE2CallbackforFullerton(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"mca_fullerton_shiv_new2_0c450f4f\",\"bureau\":\"CIBIL\",\"bureauKicker\":false,\"creditScore\":796,\"field_investigation_needed\":false,\"incentiveRate\":0.0,\"isAcceptanceAbove5000\":1,\"isBre2Required\":false,\"isEmandateEligible\":0,\"isIncentiveAllowed\":0,\"isPaytmVintageOlderThan90d\":1,\"isRenewal\":false,\"isRestrictedMerchant\":0,\"isSiMandatory\":\"1\",\"lastFetchDate\":1646784000000,\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":600000.0,\"maxTenure\":360,\"minLoanAmount\":10000.0,\"minTenure\":360,\"newOfferGenerated\":false,\"offerEndDate\":\"Tue Feb 07 00:00:00 IST 2023\",\"offerId\":\"mca_fullerton_shiv_new2_0c450f4f\",\"offerStartDate\":\"Mon Feb 21 00:00:00 IST 2022\",\"pfFeeRate\":1.0,\"productId\":\"85\",\"productVersion\":1,\"rateOfInterest\":30.0,\"riskGrade\":\"MCA|DRB124\",\"skipMandate\":false,\"sourceOfWhitelist\":\"Risk\"}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE2_SUCCESS");
        body.put("BASE_ID", "mca_fullerton_shiv_new2_0c450f4f");
        body.put("LENDER_APPLICATION_ID", "11364303");
        body.put("LENDER_HUBBLE_ID", "813982");
        body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
        body.put("OFFER_DETAILS", new Gson().toJsonTree(offerDetails));
        body.put("LOAN_OFFER_ID", "mca_fullerton_shiv_new2_0c450f4f");
        body.put("BRE_LAST_FETCH_DATE", "Tue Mar 01 05: 30: 00 IST 2022");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/BRE2CallbackforFullertonRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }
    public  Response BRE2CallbackforMCAABFL(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"avgGMVgrid\":\"V5\",\"baseId\":\"mca_abfl_new_shivangi1_4732407d\",\"behaviouralScore\":5.0,\"best6MonthsGmvGrid\":\"V51\",\"bureau\":\"CIBIL\",\"bureauDecile\":\"5\",\"bureauKicker\":false,\"creditScore\":838,\"field_investigation_needed\":false,\"gmvIndex\":2,\"incentiveRate\":0.0,\"isAcceptanceAbove5000\":1,\"isBSAOffer\":false,\"isBre2Required\":false,\"isEmandateEligible\":0,\"isIncentiveAllowed\":0,\"isPaytmVintageOlderThan90d\":1,\"isRenewal\":false,\"isRestrictedMerchant\":0,\"isSiMandatory\":\"1\",\"lastFetchDate\":1681862400000,\"latestmonthgmvgrid\":\"V51\",\"loanAmountCappingGrid\":\"L24\",\"loanDownGradable\":false,\"loan_offered\":true,\"lowestGmvgrid\":\"V11\",\"maxLoanAmount\":80000.0,\"maxTenure\":180,\"mcrsDecile\":5,\"minLoanAmount\":10000.0,\"minTenure\":180,\"newOfferGenerated\":false,\"offerEndDate\":\"Fri Jun 30 00:00:00 IST 2023\",\"offerId\":\"a3bac5e5-eadd-4ded-a154-35d37eefd5c0\",\"offerStartDate\":\"Tue May 02 00:00:00 IST 2023\",\"pfFeeRate\":1.0,\"productId\":\"104\",\"productVersion\":1,\"rateOfInterest\":35.0,\"renewalEnhancement\":0,\"renewalLastLoanClosureGrid\":\"RN2\",\"riskGrade\":\"MCA|BWB64\",\"roiCode\":\"R7\",\"skipMandate\":false,\"sourceOfWhitelist\":\"Risk\",\"tenureCappingGrid\":\"T7\",\"vintageCode\":\"V3\"}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE2_SUCCESS");
        body.put("BASE_ID", "mca_abfl_OE_prefill_shivangi_ccce46d2");
        body.put("LENDER_APPLICATION_ID", "PTMMCA0000000309");
        body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
        body.put("OFFER_DETAILS", new Gson().toJsonTree(offerDetails));
        body.put("LOAN_OFFER_ID", "d9ce04e4-530b-411e-8fae-b9aa0e51c9b3");
        body.put("BRE_LAST_FETCH_DATE", "Wed Apr 19 05:30:00 IST 2023");
        body.put("BRE2_SUCCESS", "TRUE");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/mcaABFL/BRE2MockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public Response callbackWithOnlyWorkflowoperation(String custId, String solution, String workflowOperation) {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solution);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", workflowOperation);

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/BRE1RequestCallback.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;

    }

    public Response BRE2CallbackWithLoanOfferKeys(String custId, String solutionName) {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solutionName);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.HOME_FIRST);

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE2_SUCCESS");
        body.put("BASE_ID", "HL_1002149725_dabdbb6e");
        body.put("LOAN_OFFER_ID", "HL_1002149725_dabdbb6e");
        body.put("LOAN_OFFER_RATE_OF_INTEREST", "12.5");
        body.put("LOAN_OFFER_AMOUNT", "525000");
        body.put("LOAN_OFFER_TENURE_UNIT", "YEAR");
        body.put("LOAN_OFFER_TENURE", "9");
        body.put("LOAN_OFFER_TENURE_AMOUNT", "5724.0");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/BRE2RequestCallback.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;

    }

    public  Response BRE1CallbackforRenewal(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"4952fee2-9b8a-4e3a-b183-d05b9156cb6d\",\"bureauKicker\":false,\"bureauThick\":1,\"field_investigation_needed\":false,\"isBre2Required\":false,\"lenderSchemeId\":\"54105\",\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":165000,\"minLoanAmount\":10000,\"newOfferGenerated\":true,\"offerEndDate\":\"Tue Aug 30 00:00:00 IST 2022\",\"offerId\":\"3c8f83eb-35e1-45eb-a552-9043ba55a29a\",\"offerStartDate\":\"Wed Jun 01 00:00:00 IST 2022\",\"paytmThick\":0,\"productId\":\"95\",\"productVersion\":1,\"riskGrade\":\"VL\",\"riskSegment\":\"VL\",\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\"}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("LENDING_SCHEME_ID", "54105");
        body.put("BRE1_OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/BRE1CallbackForRenewalRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response BRE2CallbackforRenewal(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE2_SUCCESS");
        body.put("BASE_ID", "4952fee2-9b8a-4e3a-b183-d05b9156cb6d");
        body.put("SERVICEABLE_LONGITUDE", "76.850639");
        body.put("SKIP_EMANDATE_ELIGIBLE", "false");
        body.put("IS_EMAIL_VERIFICATION_MANDATORY", "FALSE");
        body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");
        body.put("SERVICEABLE_LATITUDE", "30.328274");
        body.put("LOAN_OFFER_ID", "3c8f83eb-35e1-45eb-a552-9043ba55a29a");

        String requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/BRE2CallbackForRenewalRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }
    public  Response BRE2CallbackforEMI(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"advanceEmi\":0,\"allowedTxnLocationCategory\":\"All\",\"baseId\":\"abc12345_285ec40e\",\"bureauKicker\":false,\"bureauThick\":1,\"coolOffPeriod\":0,\"eMandateType\":\"MANDATORY\",\"field_investigation_needed\":false,\"flow\":\"RISK\",\"irr\":19.0,\"isBSAOffer\":false,\"isBre2Required\":false,\"joiningFees\":0.0,\"loanDownGradable\":false,\"loan_offered\":true,\"maxEmiAmount\":18000.0,\"maxLimit\":60000.0,\"maxLoanAmount\":60000.0,\"maxTenure\":18,\"merchantCategory\":\"Red,Amber,Green,Online\",\"minDownpayment\":0.0,\"minLimit\":7500.0,\"newOfferGenerated\":false,\"oem\":\"\",\"offerEndDate\":\"Sun Feb 04 00:00:00 IST 2024\",\"offerId\":\"ea9b0a18-9ee6-4607-9b09-0de12595c924\",\"offerStartDate\":\"Mon Nov 06 00:00:00 IST 2023\",\"paytmThick\":1,\"pemiRiskSegment\":\"PP1_N\",\"permanentBlock\":35,\"pfFeeRate\":3.0,\"prevLenderLimit\":60000.0,\"processingFeeOverride\":true,\"productId\":\"900\",\"productVersion\":1,\"skipMandate\":true,\"sourceOfWhitelist\":\"RISK\",\"subIrr\":15.0,\"taxOnJoiningFees\":0.0,\"temporaryBlock\":1,\"totalJoiningFees\":0.0}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE2_SUCCESS");
        body.put("BASE_ID", "OE_EMI_Shivangi_PL_552cca9e");
        body.put("SKIP_EMANDATE_ELIGIBLE", "false");
        body.put("IS_EMAIL_VERIFICATION_MANDATORY", "null");
        body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");
        body.put("LOAN_OFFER_ID", "3c8f83eb-35e1-45eb-a552-9043ba55a29a");
        body.put("BRE2_OFFER_DETAILS",new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/BRE2CallbackForRenewalRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }


    public Response v3FetchCIR(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String requestjson) {
        fetchCIRv3 fetchCIRPostPaidObject = new fetchCIRv3(requestjson);
        for (Map.Entry m : queryParams.entrySet()) {
            fetchCIRPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            fetchCIRPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            fetchCIRPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchCIRPostPaidObjectResponse = fetchCIRPostPaidObject.callAPI();

        return fetchCIRPostPaidObjectResponse;
    }


    public Response v1InitiateBureau(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String requestjson) {
        AsyncBureau fetchCIRPostPaidObject = new AsyncBureau(requestjson);
        for (Map.Entry m : queryParams.entrySet()) {
            fetchCIRPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            fetchCIRPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            fetchCIRPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchCIRPostPaidObjectResponse = fetchCIRPostPaidObject.callAPI();

        return fetchCIRPostPaidObjectResponse;
    }





    public Response v3CKYCID(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String requestjsonckyc) {
        CKYCID cKYCPostPaidObject = new CKYCID(requestjsonckyc);
        for (Map.Entry m : queryParams.entrySet()) {
            cKYCPostPaidObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : headers.entrySet()) {
            cKYCPostPaidObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            cKYCPostPaidObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response cKYCPostPaidObjectResponse = cKYCPostPaidObject.callAPI();
        return cKYCPostPaidObjectResponse;
    }

    /**
     *
     * @param queryParams
     * @param headers
     * @param body
     * @param requestBodyPath
     * @return
     */
    public Response documentStatus(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String requestBodyPath) {

        DocumentStatus docUploadObject = new DocumentStatus(requestBodyPath);

        for (Map.Entry m : queryParams.entrySet()) {
            docUploadObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            docUploadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            docUploadObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response docUploadObjectResponse = docUploadObject.callAPI();


        return docUploadObjectResponse;
    }

    /**
     * KYC New stack API
     * @param queryParams
     * @param headers
     * @param body
     * @param requestData
     * @return
     */

    public Response initiateKYCUsingSearchByPan(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestData) {

        InitiateKYC initiateKYCObject = new InitiateKYC(requestData);

        for (Map.Entry m : queryParams.entrySet()) {
            initiateKYCObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            initiateKYCObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            initiateKYCObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        initiateKYCObject.getRequest().urlEncodingEnabled(false);
        Response initiateKYCObjectResponse = initiateKYCObject.callAPI();


        return initiateKYCObjectResponse;
    }

    public Response initiateKYCUsingSearchByPan(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestData,String version) {

        InitiateKYCV2Version initiateKYCObject = new InitiateKYCV2Version(requestData);

        for (Map.Entry m : queryParams.entrySet()) {
            initiateKYCObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            initiateKYCObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            initiateKYCObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        initiateKYCObject.getRequest().urlEncodingEnabled(false);
        Response initiateKYCObjectResponse = initiateKYCObject.callAPI();


        return initiateKYCObjectResponse;
    }
    /**
     *
     * @param queryParams
     * @param headers
     * @param uploadFile
     * @param requestBody
     * @return
     * @throws InterruptedException
     */
    public Response UploadDocumentUsingWorkflowAPI(Map<String, String> queryParams, Map<String, String> headers, File uploadFile) throws InterruptedException {


        UploadDocumentNewAPI  UploadDocumentObject = new UploadDocumentNewAPI();


        //Adding queryParams
        for (Map.Entry m : queryParams.entrySet()) {
            UploadDocumentObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            UploadDocumentObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        UploadDocumentObject.addMultipartFormData("file", uploadFile,"image/jpeg");
        Thread.sleep(2000);

        UploadDocumentObject.getRequest().urlEncodingEnabled(false);

        Response UploadDocumentObjectResponse = UploadDocumentObject.callAPI();

        try {
            if (UploadDocumentObjectResponse.jsonPath().getString("errorCode").equals("204")) {
                LOGGER.info("Inside Document upload json validation");
                UploadDocumentObject.validateResponseAgainstJSONSchema("MerchantService/v2/lending/lead/document/UploadDocumentResponseSchema.json");
            }

        } catch (Exception e) {
            LOGGER.info("Exception occurred: java.lang.RuntimeException: Validation against Json schema failed");
        }


        return UploadDocumentObjectResponse;
    }
    /**
     *
     * @param docToUpload
     * @param leadId
     * @param custId
     * @param entityType
     * @param solutionType
     * @param solutionTypeLevel2
     * @param sessionToken
     * @param docType
     * @param requestBody
     * @return
     * @throws InterruptedException
     */

    public Response utilityForDocumentUpload(String docToUpload,String leadId,String custId, String entityType,String solutionType,String solutionTypeLevel2,String sessionToken,String docType) throws InterruptedException {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("docProvided", docToUpload);
        queryParams.put("docType", docType);
        queryParams.put("leadId", leadId);
        queryParams.put("custId", custId);
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
        queryParams.put("channel", "PAYTM_APP");


        File uploadFile = new File("src/test/resources/MerchantService/V2/lending/lead/document/selfie.jpg");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", sessionToken);
        headers.put("custId",custId);

        Response responseObject = UploadDocumentUsingWorkflowAPI(queryParams, headers, uploadFile);

        return responseObject;
    }


    public Response updateDataSetInSAI(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String requestBodyPath) {

        UpdateSAI updateCKYCNameInSAIObject = new UpdateSAI(requestBodyPath);

        for (Map.Entry m : queryParams.entrySet()) {
            updateCKYCNameInSAIObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateCKYCNameInSAIObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            updateCKYCNameInSAIObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        updateCKYCNameInSAIObject.getRequest().urlEncodingEnabled(false);
        Response updateCKYCNameInSAIObjectResponse = updateCKYCNameInSAIObject.callAPI();


        return updateCKYCNameInSAIObjectResponse;
    }


    //OAuth Mobile Number Generator
    public Response GenerateNewUser(Map<String, String> headers, Map<String, Object> body) {

        GenerateMobileUser GenerateNewUserObject = new GenerateMobileUser();

        for (Map.Entry m : headers.entrySet()) {
            GenerateNewUserObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            GenerateNewUserObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        GenerateNewUserObject.getRequest().urlEncodingEnabled(false);
        Response GenerateUserMobileResponse = GenerateNewUserObject.callAPI();


        return GenerateUserMobileResponse;
    }

    //OAuth Mobile Number Fetch Cust ID
    public Response FetchMobileNumber(Map<String, String> headers, Map<String, Object> body) {

        FetchCustId FetchMobileNumberObject = new FetchCustId();

        for (Map.Entry m : headers.entrySet()) {
            FetchMobileNumberObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            FetchMobileNumberObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        FetchMobileNumberObject.getRequest().urlEncodingEnabled(false);
        Response FetchMobileNumberResponse = FetchMobileNumberObject.callAPI();


        return FetchMobileNumberResponse;
    }

    /**
     * BRE3 Callback
     * @param leadId
     * @param solutionType
     * @param channel
     * @param entityType
     * @param solutionTypeLevel2
     * @param custId
     * @return
     */
    public  Response BRE3Callback(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE3_SUCCESS");
        body.put("IS_EMANDATE_ELIGIBLE", "true");


        String requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/BRE3CallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public Response KYCImageUpload(String docToUpload,String leadId,String custId, String entityType,String solutionType,String solutionTypeLevel2,String sessionToken,String docType,String filename) throws InterruptedException {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("docProvided", docToUpload);
        queryParams.put("docType", docType);
        queryParams.put("leadId", leadId);
        queryParams.put("custId", custId);
        queryParams.put("entityType", entityType);
        queryParams.put("solution", solutionType);
        queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
        queryParams.put("channel", "PAYTM_APP");


        File uploadFile = new File("src/test/resources/MerchantService/V2/lending/lead/document/"+filename);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "multipart/form-data");
        headers.put("session_token", sessionToken);
        headers.put("custId",custId);

        Response responseObject = UploadDocumentUsingWorkflowAPI(queryParams, headers, uploadFile);

        return responseObject;
    }





    public Response updateBureauDataSetInSAI(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body,String requestData) {

        UpdateSAI updateCKYCNameInSAIObject = new UpdateSAI(requestData);

        for (Map.Entry m : queryParams.entrySet()) {
            updateCKYCNameInSAIObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            updateCKYCNameInSAIObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            updateCKYCNameInSAIObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        updateCKYCNameInSAIObject.getRequest().urlEncodingEnabled(false);
        Response updateCKYCNameInSAIObjectResponse = updateCKYCNameInSAIObject.callAPI();


        return updateCKYCNameInSAIObjectResponse;
    }


    public  Response KYCCallbackusingDigilocker(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "KYC_SUCCESS");
        body.put("KYC_STATUS","KYC_SUCCESS");
        body.put("CKYC_SUCCESS_MODE", "DIGILOCKER");
        body.put("KYC_MODE", "DIGILOCKER");
        body.put("CKYC_F_NAME", "Himanshu Sharma");
        body.put("CKYC_NAME","Himanshu Sharma");
        body.put("CKYC_GENDER","MALE");
        body.put("CKYC_DOB", "1989-11-19");
        body.put("FATHER_NAME", "Shikha Sharma");
        body.put("IS_PAN_SEARCH_CKYC_SUCCESS", "false");
        body.put("KYC_SELFIE_UPLOAD_REQUIRED","false");
        body.put("LENDING_PAN_NAME_QC_REQUIRED","FALSE")	;
        body.put("PAN_NAME_MATCH_PERCENTAGE", "100");
        body.put("PAN_NAME_MATCH_TIMESTAMP", "2023-05-09 14:44:33");
        body.put("KYC_REQUEST_ID", "32fc6267640340d797742d1dbf04413a");
        body.put("OKYC_REFERENCE_NUMBER","2852");
        body.put("LENDING_IMAGE_QC_REQUIRED","false")	;
        body.put("IS_KYC_DOC_SHARED", "false");
        body.put("DIGILOCKER_XML_RESPONSE_TIMESTAMP", "2023-05-09T14:43:59.486+0530");
        body.put("DIGILOCKER_CONSENT_PROVIDED_TIMESTAMP","2023-05-09T14:43:59.486+0530");
        body.put("DIGILOCKER_REFERENCE_NUMBER","2852");
        body.put("CKYC_PINCODE","121002")	;
        body.put("refId", "NVEh-38");
        body.put("statusCode", "0");
        body.put("pincode", "121002");
        body.put("postalCode","0");
        body.put("state","Haryana")	;
        body.put("city", "Kheri Kalan");
        body.put("line1", "C O Shikha Sharma, House No 409, Near Sector 18 Post Office");
        body.put("line2","Sector 18, Faridabad");
        body.put("line3","Kheri Kalan(113), Haryana, India - 121002");
        body.put("latitude", "0.0");
        body.put("longitude", "0.0");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType","PERMANENT");
        body.put("status","0");
        body.put("docType", "others");
        body.put("docProvided", "customerPhoto");
        body.put("docUUId","***************");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/mcaABFL/KYCMockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response KYCFailureCallbackusingDigilocker(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId,String kycMode){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "KYC_FAILURE");
        body.put("KYC_STATUS","FAILED");
        body.put("KYC_MODE", kycMode);
        body.put("KYC_FAILURE_REASON", "YOB Failed");


        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/mcaABFL/KYCDigilockerFailureCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response KYCCallbackusingOfflineAdhaar(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "KYC_SUCCESS");
        body.put("KYC_STATUS","KYC_SUCCESS");
        body.put("CKYC_SUCCESS_MODE", "OFFLINE_AADHAAR");
        body.put("KYC_MODE", "OFFLINE_AADHAAR");
        body.put("KYC_SELFIE_UPLOAD_REQUIRED", "false");
        body.put("CKYC_F_NAME", "Mohammad Kalim Ansari");
        body.put("CKYC_NAME","Mohammad Kalim Ansari");
        body.put("CKYC_GENDER","MALE");
        body.put("CKYC_DOB", "1992-05-01");
        body.put("FATHER_NAME", "S/O Mohammad Quaiyum Ansari");
        body.put("CKYC_OFFLINE_AADHAAR_XML_RESPONSE_TIMESTAMP", "2022-01-24T06:52:12.906+0530");
        body.put("CKYC_OFFLINE_AADHAAR_CONSENT_PROVIDED_TIMESTAMP","2022-01-24T06:52:12.906+0530");
        body.put("PAN_NAME_MATCH_PERCENTAGE","100")	;
        body.put("PAN_NAME_MATCH_TIMESTAMP", "2023-05-09 14:44:33");
        body.put("KYC_REQUEST_ID", "32fc6267640340d797742d1dbf04413a");
        body.put("LENDING_PAN_NAME_QC_REQUIRED","FALSE");
        body.put("OKYC_REFERENCE_NUMBER","1234");
        body.put("CKYC_PINCODE","121002")	;
        body.put("IS_PAN_SEARCH_CKYC_SUCCESS","false")	;
        body.put("IS_KYC_DOC_SHARED","false")	;
        body.put("LENDING_IMAGE_QC_REQUIRED","false")	;
        body.put("refId", "NVEh-38");
        body.put("statusCode", "0");
        body.put("pincode", "226022");
        body.put("postalCode","0");
        body.put("state","Haryana")	;
        body.put("city", "Kheri Kalan");
        body.put("line1", "C O Shikha Sharma, House No 409, Near Sector 18 Post Office");
        body.put("line2","Sector 18, Faridabad");
        body.put("line3","Kheri Kalan(113), Haryana, India - 121002");
        body.put("latitude", "0.0");
        body.put("longitude", "0.0");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType","PERMANENT");
        body.put("status","0");
        body.put("docType", "others");
        body.put("docProvided", "customerPhoto");
        body.put("docUUId","***************");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/mcaABFL/KYCOACallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response KYCCallbackusingOfflineAdhaar(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId,boolean locationServiceabilityCheck){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "KYC_SUCCESS");
        body.put("KYC_STATUS","KYC_SUCCESS");
        body.put("CKYC_SUCCESS_MODE", "OFFLINE_AADHAAR");
        body.put("KYC_MODE", "OFFLINE_AADHAAR");
        body.put("KYC_SELFIE_UPLOAD_REQUIRED", "false");
        body.put("CKYC_F_NAME", "Mohammad Kalim Ansari");
        body.put("CKYC_NAME","Mohammad Kalim Ansari");
        body.put("CKYC_GENDER","MALE");
        body.put("CKYC_DOB", "1992-05-01");
        body.put("FATHER_NAME", "S/O Mohammad Quaiyum Ansari");
        body.put("CKYC_OFFLINE_AADHAAR_XML_RESPONSE_TIMESTAMP", "2022-01-24T06:52:12.906+0530");
        body.put("CKYC_OFFLINE_AADHAAR_CONSENT_PROVIDED_TIMESTAMP","2022-01-24T06:52:12.906+0530");
        body.put("PAN_NAME_MATCH_PERCENTAGE","100")	;
        body.put("PAN_NAME_MATCH_TIMESTAMP", "2023-05-09 14:44:33");
        body.put("KYC_REQUEST_ID", "32fc6267640340d797742d1dbf04413a");
        body.put("LENDING_PAN_NAME_QC_REQUIRED","FALSE");
        body.put("OKYC_REFERENCE_NUMBER","1234");
        body.put("CKYC_PINCODE","121002")	;
        body.put("IS_PAN_SEARCH_CKYC_SUCCESS","false")	;
        body.put("IS_KYC_DOC_SHARED","false")	;
        body.put("LENDING_IMAGE_QC_REQUIRED","false")	;
        body.put("refId", "NVEh-38");
        body.put("statusCode", "0");
        body.put("pincode", "226022");
        body.put("postalCode","0");
        body.put("state","Haryana")	;
        body.put("city", "Kheri Kalan");
        body.put("line1", "C O Shikha Sharma, House No 409, Near Sector 18 Post Office");
        body.put("line2","Sector 18, Faridabad");
        body.put("line3","Kheri Kalan(113), Haryana, India - 121002");
        body.put("latitude", "0.0");
        body.put("longitude", "0.0");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType","PERMANENT");
        body.put("status","0");
        body.put("docType", "others");
        body.put("docProvided", "customerPhoto");
        body.put("docUUId","***************");

        if(locationServiceabilityCheck)
            body.put("pincode", "226002");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/mcaABFL/KYCOACallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response KYCCallbackusingSearchBYPan(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "KYC_SUCCESS");
        body.put("KYC_STATUS","KYC_SUCCESS");
        body.put("CKYC_SUCCESS_MODE", "SEARCH_BY_PAN");
        body.put("KYC_MODE", "SEARCH_BY_PAN");
        body.put("CKYC_F_NAME", "Nagu");
        body.put("CKYC_M_NAME", "K");
        body.put("CKYC_L_NAME", "Singh");
        body.put("CKYC_NAME","Nagu Singh");
        body.put("CKYC_GENDER","MALE");
        body.put("CKYC_DOB", "1992-05-01");
        body.put("CKYC_EMAIL", "<EMAIL>");
        body.put("CKYC_PAN", "**********");
        body.put("FATHER_NAME", "S/O Mohammad Quaiyum Ansari");
        body.put("MOTHER_NAME", "ABCD Ansari");
        body.put("CKYC_ID", "30041446613529");
        body.put("IS_PAN_SEARCH_CKYC_SUCCESS","true");
        body.put("KYC_SELFIE_UPLOAD_REQUIRED","false")	;
        body.put("LENDING_PAN_NAME_QC_REQUIRED", "FALSE");
        body.put("PAN_NAME_MATCH_PERCENTAGE", "100");
        body.put("PAN_NAME_MATCH_TIMESTAMP","2023-05-29 12:28:09");
        body.put("CKYC_ATTEMPT_TIMESTAMP","*************");
        body.put("CKYC_RESPONSE_TIMESTAMP","*************")	;
        body.put("KYC_VALIDATION_API_STATUS","YES")	;
        body.put("KYC_VALIDATION_STATUS","APPROVE")	;
        body.put("KYC_REQUEST_ID","3f5ad41657f04312991fb106cfcb390b")	;
        body.put("SELFIE_MATCH_PERCENTAGE","100")	;
        body.put("SELFIE_MATCH_VENDOR","CDO_INSIGHTS")	;
        body.put("LENDING_IMAGE_QC_REQUIRED","false")	;
        body.put("CKYC_BANK_ACCOUNT_NUMBER","**********")	;
        body.put("IS_KYC_DOC_SHARED","false")	;
        body.put("CKYC_PINCODE","683102")	;
        body.put("refId", "NVEh-38");
        body.put("statusCode", "0");
        body.put("pincode", "121002");
        body.put("postalCode","0");
        body.put("state","Haryana")	;
        body.put("city", "Kheri Kalan");
        body.put("line1", "C O Shikha Sharma, House No 409, Near Sector 18 Post Office");
        body.put("line2","Sector 18, Faridabad");
        body.put("line3","Kheri Kalan(113), Haryana, India - 121002");
        body.put("latitude", "0.0");
        body.put("longitude", "0.0");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType","PERMANENT");
        body.put("status","0");
        body.put("docType", "others");
        body.put("docProvided", "customerPhoto");
        body.put("docUUId","***************");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/KYCSBPCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }
    public  Response BRE1CallbackforTCL(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"PL_Tata1_Shivangi_2_a00fdc0a\",\"bureauKicker\":false,\"bureauThick\":1,\"field_investigation_needed\":false,\"flow\":\"RISK\",\"isBSAOffer\":false,\"isBre2Required\":false,\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":192000.0,\"minLoanAmount\":30000.0,\"newOfferGenerated\":true,\"offerEndDate\":\"Wed Sep 13 00:00:00 IST 2023\",\"offerId\":\"473bb766-b216-4519-be27-ba4a396f1950\",\"offerStartDate\":\"Thu Jun 15 00:00:00 IST 2023\",\"paytmThick\":0,\"productId\":\"107\",\"productVersion\":1,\"riskGrade\":\"VL\",\"riskSegment\":\"VL\",\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\"}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BUREAU_PULL_TIMESTAMP", "*************");
        body.put("OFFER_GENERATION_TIMESTAMP", "1685039400000");
        body.put("IS_BSA_FLOW_ENABLED", "false");
        body.put("BRE1_OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/BRE1MockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response BRE2CallbackforTCL(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"PL_Tata1_Shivangi_2_a00fdc0a\",\"bureauKicker\":false,\"bureauThick\":1,\"field_investigation_needed\":false,\"flow\":\"RISK\",\"isBSAOffer\":false,\"isBre2Required\":false,\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":192000.0,\"minLoanAmount\":30000.0,\"newOfferGenerated\":true,\"offerEndDate\":\"Wed Sep 13 00:00:00 IST 2023\",\"offerId\":\"473bb766-b216-4519-be27-ba4a396f1950\",\"offerStartDate\":\"Thu Jun 15 00:00:00 IST 2023\",\"paytmThick\":0,\"productId\":\"107\",\"productVersion\":1,\"riskGrade\":\"VL\",\"riskSegment\":\"VL\",\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\"}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE2_SUCCESS");
        body.put("BASE_ID", "MAQUETTE_PL_107_84cfcb98-ccfc-4d6e-88c8-118bfa5322b1");
        body.put("SERVICEABLE_LONGITUDE", "79.419761");
        body.put("SKIP_EMANDATE_ELIGIBLE", "false");
        body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");
        body.put("SERVICEABLE_LATITUDE", "28.345977");
        body.put("LOAN_OFFER_ID", "e6dd5a98-5a72-42a0-b620-224713bb77f1");
        String requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/BRE2MockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }


    public  Response BRE3CallbackforTCL(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"PL_Tata1_Shivangi_2_a00fdc0a\",\"bureauKicker\":false,\"bureauThick\":1,\"field_investigation_needed\":false,\"flow\":\"RISK\",\"isBSAOffer\":false,\"isBre2Required\":false,\"loanDownGradable\":false,\"loan_offered\":false,\"maxLoanAmount\":210000.0,\"minLoanAmount\":30000.0,\"newOfferGenerated\":false,\"offerEndDate\":\"Wed Sep 06 00:00:00 IST 2023\",\"offerId\":\"16fadc0d-145b-4032-a2d7-4055e60e5808\",\"offerStartDate\":\"Thu Jun 08 00:00:00 IST 2023\",\"paytmThick\":0,\"productId\":\"107\",\"productVersion\":1,\"riskGrade\":\"VL\",\"riskSegment\":\"VL\",\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\",\"stampDutyCharges\":750.0}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE3_SUCCESS");
        body.put("STAMP_DUTY_CHARGES", "750.0");
        body.put("SKIP_EMANDATE_ELIGIBLE", "false");
        body.put("IS_EMAIL_VERIFICATION_MANDATORY", "false");
        body.put("BRE1_OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/BRE3MockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }



    public  Response BRE1CallbackforLTFS(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        String offerDetails="{\"baseId\":\"PL_Tata1_Shivangi_2_a00fdc0a\",\"bureauKicker\":false,\"bureauThick\":1,\"field_investigation_needed\":false,\"flow\":\"RISK\",\"isBSAOffer\":false,\"isBre2Required\":false,\"loanDownGradable\":false,\"loan_offered\":true,\"maxLoanAmount\":192000.0,\"minLoanAmount\":30000.0,\"newOfferGenerated\":true,\"offerEndDate\":\"Wed Sep 13 00:00:00 IST 2023\",\"offerId\":\"473bb766-b216-4519-be27-ba4a396f1950\",\"offerStartDate\":\"Thu Jun 15 00:00:00 IST 2023\",\"paytmThick\":0,\"productId\":\"107\",\"productVersion\":1,\"riskGrade\":\"VL\",\"riskSegment\":\"VL\",\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\"}";

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BUREAU_PULL_TIMESTAMP", System.currentTimeMillis());
        body.put("OFFER_GENERATION_TIMESTAMP", System.currentTimeMillis());
        body.put("IS_BSA_FLOW_ENABLED", "false");
        body.put("BRE1_OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/BRE1MockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }



    public Response experianPull(Map<String, String> queryParams, Map<String, String> headers, Map<String, Object> body, String requestBodyPath) {

        ExperianPull experianPullObject = new ExperianPull(requestBodyPath);

        for (Map.Entry m : queryParams.entrySet()) {
            experianPullObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            experianPullObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            experianPullObject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response experianPullObjectResponse = experianPullObject.callAPI();


        return experianPullObjectResponse;
    }

    public Response fetchWhitelistDetails(Map<String, String> queryParams ,Map<String, String> headers) {

        FetchWhitelistDetails fetchDetailsObject = new FetchWhitelistDetails();

        for (Map.Entry m : queryParams.entrySet()) {
            fetchDetailsObject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            fetchDetailsObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response FetchDetailsObjectResponse = fetchDetailsObject.callAPI();

        return FetchDetailsObjectResponse;
    }

    public Response saveBankDetailsNewAPI(Map<String, String> queryParams, Map<String, String> headers,Map<String, Object> body,String requestJson) {

        NewBankAPI saveBankDetailsbject = new NewBankAPI(requestJson);

        for (Map.Entry m : queryParams.entrySet()) {
            saveBankDetailsbject.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            saveBankDetailsbject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            saveBankDetailsbject.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        saveBankDetailsbject.getRequest().urlEncodingEnabled(false);
        Response saveBankDetailsbjectResponse = saveBankDetailsbject.callAPI();


        return saveBankDetailsbjectResponse;

    }

    public  Response BRE1CallbackGeneric(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId,String offerDetails ){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BUREAU_PULL_TIMESTAMP", "*************");
        body.put("OFFER_GENERATION_TIMESTAMP", "1685039400000");
        body.put("IS_BSA_FLOW_ENABLED", "false");
        body.put("BRE1_OFFER_DETAILS", new Gson().toJsonTree(offerDetails));

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/BRE1MockCallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }


    public  Response lisCreateApplicationAtLenderEnd(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId,String workflowOperation ){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", workflowOperation);
        body.put("LENDER_APPLICATION_ID", solutionTypeLevel2+"LD202308225131150");
        body.put("USER_TYPE", "ETB");
        body.put("LENDER_APPLICATION_CREATED", "TRUE");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/LISCreateApplicationRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public  Response lenderBRECallback(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId,String workflowOperation ){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", workflowOperation);
        body.put("IS_EMANDATE_ELIGIBLE", "false");
        body.put("APPLICATION_EXPIRY_TIMESTAMP", "*************");
        body.put("LENDER_BRE_SUCCESS", "TRUE");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/LenderBRECallbackRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }

    public Response initiateKYCForETBUserAxisBank( Map<String, String> headers, Map<String, Object> body,String requestData) {

        KYCinitiate kycinitiate = new KYCinitiate(requestData);


        for (Map.Entry m : headers.entrySet()) {
            kycinitiate.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            kycinitiate.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        kycinitiate.getRequest().urlEncodingEnabled(false);
        Response initiateKYCObjectResponse = kycinitiate.callAPI();


        return initiateKYCObjectResponse;
    }

    public Response submitKYCForETBUserAxisBank(Map<String, String> headers, Map<String, Object> body,String requestData) {

        KYCSubmit kycSubmit = new KYCSubmit(requestData);


        for (Map.Entry m : headers.entrySet()) {
            kycSubmit.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            kycSubmit.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        kycSubmit.getRequest().urlEncodingEnabled(false);
        Response initiateKYCObjectResponse = kycSubmit.callAPI();


        return initiateKYCObjectResponse;
    }

    public Response getStatusKYCForETBUserAxisBank(Map<String, String> headers) {

        KYCStatus kycStatus = new KYCStatus();

        for (Map.Entry m : headers.entrySet()) {
            kycStatus.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        kycStatus.getRequest().urlEncodingEnabled(false);
        Response initiateKYCObjectResponse = kycStatus.callAPI();


        return initiateKYCObjectResponse;
    }


    public  Response lisCreateApplicationFibe(String leadId, String solutionType, String solutionTypeLevel2, String custId,String workflowOperation,String requestBodyJsonPath,Map<String, Object> callbackBody){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,"PAYTM_APP","INDIVIDUAL");

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", workflowOperation);
        body.putAll(callbackBody);


        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }
    public  Response callbackWithOnlyWorkflowOperation(String leadId, String solutionType, String solutionTypeLevel2, String custId,String workflowOperation,String requestPath){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,"PAYTM_APP","INDIVIDUAL");

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", workflowOperation);

        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestPath);

        return responseObject;
    }


    public static Connection establishConnection() throws Exception {
        JSch jsch = new JSch();
        jsch.addIdentity(SSH_PRIVATE_KEY_PATH);
        Session session = jsch.getSession(user_name, host_name, SSH_PORT);
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();
        int assigned_port = session.setPortForwardingL(DB_PORT, DB_HOST, DB_PORT);
        Class.forName("com.mysql.cj.jdbc.Driver");
        return DriverManager.getConnection("**********************:" + assigned_port + "/" + DB_NAME, DB_USER, DB_PASSWORD);
    }


    public static String SelectQueryUBMId(String leadId) throws Exception {
        String id = null;
        String query = "SELECT * FROM user_business_mapping WHERE lead_id = ?";
        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
       // statement.execute("use " + P.CONFIG.get("db_name"));

        statement.execute("use " + database_name);

        try (PreparedStatement preparedStatement = connection.prepareStatement(query)) {

            preparedStatement.setString(1, leadId);

            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    id = resultSet.getString("id");
                }
            }
        } catch (SQLException e) {
            System.out.println("Error executing SELECT query.");
            throw e;
        }
        return id;
    }


    public static void UpdateQueryToTriggerBatch(String leadId) throws Exception {
        String id = null;
        String jobId = null;

        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
        statement.execute("use " + P.CONFIG.get("db_name"));

        try (PreparedStatement statement1 = connection.prepareStatement("SELECT * FROM user_business_mapping WHERE lead_id = ?");
             PreparedStatement statement2 = connection.prepareStatement("SELECT * FROM job WHERE context_val = ? AND status = 0");
             PreparedStatement statement3 = connection.prepareStatement("UPDATE job SET retry_count = 0, status = 0, batch_identifier = NULL, thread_name = NULL, trigger_at = NOW() WHERE id = ?")) {

            // Execute the first query
            statement1.setString(1, leadId);
            try (ResultSet resultSet = statement1.executeQuery()) {
                if (resultSet.next()) {
                    id = resultSet.getString("id");
                    System.out.println("ID: " + id);
                }
            }

            // Execute the second query
            statement2.setString(1, id);
            try (ResultSet resultSet = statement2.executeQuery()) {
                if (resultSet.next()) {
                    jobId = resultSet.getString("id");
                    System.out.println("ID: " + jobId);
                }
            }

            // Execute the third query
            statement3.setString(1, jobId);
            int rowsAffected = statement3.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println("Update successful. Affected rows: " + rowsAffected);
            } else {
                System.out.println("Update unsuccessful. No rows were affected.");
            }
        }
    }

    public static void insertNodeQuery(String leadId, String requiredNodeId) throws Exception {
        String userBusinessMappingId;
        String workflowNodeId;
        String prevWorkflowNodeId;
        String workflowStatusId;
        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
        statement.execute("use " + P.CONFIG.get("db_name"));

        try (PreparedStatement selectUBM = connection.prepareStatement("SELECT * FROM user_business_mapping WHERE lead_id = ?");
             PreparedStatement selectWS = connection.prepareStatement("SELECT * FROM workflow_status WHERE user_business_mapping_id = ? AND is_active = 1");
             PreparedStatement updateWS = connection.prepareStatement("UPDATE workflow_status SET user_business_mapping_id = ?, is_active = 0, workflow_node_id = ? WHERE id = ?");
             PreparedStatement insertWS = connection.prepareStatement("INSERT INTO workflow_status(user_business_mapping_id, is_active, metadata, created_at, updated_at, workflow_node_id, prev_workflow_node_id) VALUES(?, 1, NULL, ?, ?, ?, ?)")) {

            LocalDateTime now = LocalDateTime.now();
            String formatDateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // Fetch user_business_mapping_id
            selectUBM.setString(1, leadId);
            try (ResultSet resultSet = selectUBM.executeQuery()) {
                if (!resultSet.next()) {
                    throw new SQLException("No matching user_business_mapping found for lead_id: " + leadId);
                }
                userBusinessMappingId = resultSet.getString("id");
            }

            // Fetch workflow_status details
            selectWS.setString(1, userBusinessMappingId);
            try (ResultSet resultSet = selectWS.executeQuery()) {
                if (!resultSet.next()) {
                    throw new SQLException("No active workflow_status found for user_business_mapping_id: " + userBusinessMappingId);
                }
                workflowStatusId = resultSet.getString("id");
                workflowNodeId = resultSet.getString("workflow_node_id");
                prevWorkflowNodeId = resultSet.getString("prev_workflow_node_id");
            }

            // Update workflow_status
            updateWS.setString(1, userBusinessMappingId);
            updateWS.setString(2, workflowNodeId);
            updateWS.setString(3, workflowStatusId);
            updateWS.executeUpdate();

            // Insert new workflow_status
            insertWS.setString(1, userBusinessMappingId);
            insertWS.setString(2, formatDateTime);
            insertWS.setString(3, formatDateTime);
            insertWS.setString(4, requiredNodeId);
            insertWS.setString(5, workflowNodeId);
            insertWS.executeUpdate();
        }
    }


    public static void backstageMovement(String leadId, String requiredNodeId) throws Exception {
        String userBusinessMappingId;
        String workflowStatusId1;
        String workflowStatusId2;

        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
        statement.execute("use " + P.CONFIG.get("db_name"));

        try (PreparedStatement selectUBM = connection.prepareStatement("SELECT * FROM user_business_mapping WHERE lead_id = ?");
             PreparedStatement selectWS1 = connection.prepareStatement("SELECT * FROM workflow_status WHERE user_business_mapping_id = ? AND is_active = 1");
             PreparedStatement updateWS1 = connection.prepareStatement("UPDATE workflow_status SET user_business_mapping_id = ?, is_active = 0 WHERE id = ?");
             PreparedStatement selectWS2 = connection.prepareStatement("SELECT * FROM workflow_status WHERE user_business_mapping_id = ? AND is_active = 0 AND workflow_node_id = ?");
             PreparedStatement updateWS2 = connection.prepareStatement("UPDATE workflow_status SET user_business_mapping_id = ?, is_active = 1 WHERE id = ?")) {

            // Fetch user_business_mapping_id
            selectUBM.setString(1, leadId);
            try (ResultSet resultSet = selectUBM.executeQuery()) {
                if (!resultSet.next()) {
                    throw new SQLException("No matching user_business_mapping found for lead_id: " + leadId);
                }
                userBusinessMappingId = resultSet.getString("id");
            }

            // Fetch workflow_status_id_1
            selectWS1.setString(1, userBusinessMappingId);
            try (ResultSet resultSet = selectWS1.executeQuery()) {
                if (!resultSet.next()) {
                    throw new SQLException("No active workflow_status found for user_business_mapping_id: " + userBusinessMappingId);
                }
                workflowStatusId1 = resultSet.getString("id");
            }

            // Update workflow_status_1
            updateWS1.setString(1, userBusinessMappingId);
            updateWS1.setString(2, workflowStatusId1);
            updateWS1.executeUpdate();

            // Fetch workflow_status_id_2
            selectWS2.setString(1, userBusinessMappingId);
            selectWS2.setString(2, requiredNodeId);
            try (ResultSet resultSet = selectWS2.executeQuery()) {
                if (!resultSet.next()) {
                    throw new SQLException("No inactive workflow_status found for user_business_mapping_id: " + userBusinessMappingId + " and workflow_node_id: " + requiredNodeId);
                }
                workflowStatusId2 = resultSet.getString("id");
            }

            // Update workflow_status_2
            updateWS2.setString(1, userBusinessMappingId);
            updateWS2.setString(2, workflowStatusId2);
            updateWS2.executeUpdate();
        }
    }

    public  Response lisCreateApplicationSuccess(String leadId, String solutionType,String channel, String entityType , String solutionTypeLevel2, String custId,String workflowOperation ){

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,solutionType,channel,entityType);

        queryParams.put("solutionTypeLevel2", solutionTypeLevel2)	;

        String token = generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", workflowOperation);
        body.put("LENDER_APPLICATION_ID", "AAMBR2310090008");

        String requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/LISCreateApplicationRequest.json";
        Response responseObject= v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;
    }


}

