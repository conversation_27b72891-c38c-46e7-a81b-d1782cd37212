package Services.LendingService;

public class LendingConstants {


	public static final String BUSINESS_LENDING_SOLUTION = "business_lending";
	public static final String CLIX_SOLUTION_TYPE_LEVEL_2 = "CLIX";
	public static final String SSFB_SOLUTION_TYPE_LEVEL_2 = "SSFB";
	public static final String FULLERTON_SOLUTION_TYPE_LEVEL_2 = "FULLERTON";
	public static final String PIRAMAL_SOLUTION_TYPE_LEVEL_2 = "PIRAMAL";
	public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
	public static final String INDIVIDUAL_ENTITY_TYPE = "INDIVIDUAL";
	public static final String DIY_P4B_APP_CHANNEL = "DIY_P4B_APP";
	public static final String PAN = "**********";
	public static final String DOB = "1989-04-21";
	public static final String EMAIL = "<EMAIL>";
	public static final String ISSUER = "OE";
	public static final String LMS_CLIENT_ID = "LMS";
	public static final String BSA_CLIENT_ID = "BSA";
	public static final String BSA_SECRET = "0bc35126-ddec-470f-bd84-349f83a04cef";
	public static final String OE_INTERNAL="OE_INTERNAL";
	public static final String V2_WORKFLOW_VERSION = "V2";
	public static final String V3_WORKFLOW_VERSION = "V3";
	public static final String PERSONAL_LOAN_V2_SOLUTION="personal_loan_v2";
	public static final String SOLUTION_TYPE_LEVEL2_HERO="HERO";
	public static final String PAYTM_APP_CHANNEL = "PAYTM_APP";
	public static final String PAN_HERO = "**********";
	public static final String DOB_HERO = "1996-01-12";
	public static final String EMAIL_HERO = "<EMAIL>";
	public static final String PAN_CLIX_REVAMP = "**********";
	public static final String DOB_CLIX_REVAMP = "1994-02-27";
	public static final String EMAIL_CLIX_REVAMP = "<EMAIL>";
	public static final String POSTPAID_V2_SOLUTION="postpaid_v2";
	public static final String POSTPAID_V3_SOLUTION="postpaid_v3";
    public static final String POSTPAID_LENDER_STATIC_TNC_SETNAME="postpaid_select_offer_consent_abfl";


	public static final String SOLUTION_TYPE_LEVEL2_ABFL="ABFL";
	public static final String PAN_ABFL_DELITE = "**********";
	public static final String DOB_ABFL_DELITE = "1989-04-21";
	public static final String EMAIL_ABFL_DELITE= "<EMAIL>";
	public static final String PAN_ABFL_LITE = "**********";
	public static final String DOB_ABFL_LITE = "1995-02-25";
	public static final String EMAIL_ABFL_LITE= "<EMAIL>";
	public static final String PAN_CONTACTIBILITY_CLIX = "**********";
	public static final String DOB_CONTACTIBILITY_CLIX = "1995-03-19";
	public static final String EMAIL_CONTACTIBILITY_CLIX = "<EMAIL>";
	public static final String PAN_CONTACTIBILITY_SSFB= "**********";
	public static final String DOB_CONTACTIBILITY_SSFB = "1989-04-21";
	public static final String EMAIL_CONTACTIBILITY_SSFB = "<EMAIL>";
	public static final String TCL="TATA_CAPITAL";
	public static final String AXIS_DISTRIBUTION="AXIS_DISTRIBUTION";

    public static final String FIBE_DISTRIBUTION="FIBE_DISTRIBUTION";
	public static final String KYC_SECRET="TYJH7262-I952-019G-98B3-01937HFF5787";

    public static final String FETCH_STRATEGY_CHECK_LEAD_EXISTS = "CHECK_LEAD_EXISTS";
    public static final String FETCH_STRATEGY_BASIC_DATA = "BASIC_DATA";
    public static final String FETCH_STRATEGY_ALL_DATA = "ALL_DATA";
    public static final String PAN_PP_MINI = "**********";
    public static final String DOB_PP_MINI = "1996-04-21";
    public static final String EMAIL_PP_MINI = "<EMAIL>";
    public static final String LMS_SECRET = "fd61f785-e867-471b-90c6-1447b4331712";
    public static final String OE_INTERNAL_SECRET = "e943ccc0-efc5-47e4-a701-bf9f3956bdaf";
    public static final String LONGITUDE = "77.4977";
    public static final String LATITUDE = "27.2046";
    public static final String androidId = "11";
    public static final String deviceIdentifier = "Xiaomi";
    public static final String osVersion = "10.1";
    public static final String appVersion = "4.4.6";
    public static final String deviceManufacturer = "Redmi";
    public static final String deviceName = "OELendingTestDevice";
    public static final String NSDL_NAME = "TOUCH WOOD LIMITED";
    public static final String F_NAME = "TOUCH";
    public static final String M_NAME = "WOOD";
    public static final String L_NAME = "LIMITED";


    public static final String PL_DISTRIBUTION_STASHFIN = "pl_distribution";
    public static final String PL_DISTRIBUTION_SOLUTIONTYPELEVEL2 = "STASHFIN";
    public static final String LENDING_QA_SECRET = "2c7c275b-fafa-4f84-a7c5-9241d548e6ce";
    public static final String LENDING_QA = "LENDING_QA";
    public static final String LENDING_BFF_SECRET = "9c676784-8254-4b7b-8fae-bb0a35ab526f";
    public static final String LENDING_BFF = "LENDING_BFF";
    public static final String PAN_STASHFIN = "**********";
    public static final String DOB_STASHFIN = "1973-01-01";
    public static final String EMAIL_STASHFIN = "<EMAIL>";
    public static final String LENDING_LIS_SECRET = "c9b27685-c656-490d-8696-cbb08610211b";
    public static final String LENDING_LIS = "LENDING_INTEGRATION_SERVICE";
    public static final String LENDING_LMS_DISTRIBUTION_SECRET = "7e22804c-e1ff-4a56-a1c3-7ab173182aa9";
    public static final String LENDING_LMS_DISTRIBUTION = "LMS_DISTRIBUTION";
    public static final String PL_DISTRIBUTION_CITIBANK = "pl_distribution";
    public static final String PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2 = "CITI_BANK";
    public static final String BT_DISTRIBUTION = "bt_distribution";
    public static final String HL_DISTRIBUTION = "hl_distribution";
    public static final String LAP_DISTRIBUTION = "lap_distribution";
    public static final String PIRAMAL_SOLUTION_TYPE_LEVEL2 = "PIRAMAL";
    public static final String BUSINESS_LENDING_V3 = "business_lending_v3";
    public static final String BSA_PL = "bsa_pl";
    public static final String PERSONAL_LOAN_RENEWAL = "personal_loan_renewal";
    public static final String PERSONAL_LOAN_RENEWAL_KYC_CONSENT = "pl_hero_ckyc_consent";

    //Personal loan Migration Hero
    public static final String PL_V3_SOLUTION = "personal_loan_v3";
    public static final String PL_V3_HERO_LENDING_DYNAMIC_TNC = "loan_agreement_pl_hero";
    public static final String PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC = "sanction_letter_pl_hero";
    public static final String PL_V3_STATIC_CONSENT = "personalloan_oclconsent_hero";
    public static final String DOB_PLv3_HERO = "1988-12-18";
    public static final String PLv3_OCCUPATION = "SALARIED";
    public static final String UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED = "Unsecured_Short_term_Loan_Simplified";
    public static final String JWT_LMS = "LMS";
    public static final String JWT_LMS_KEY = "fd61f785-e867-471b-90c6-1447b4331712";
    public static final String IP_ADDRESS = "10.0.0.0";
    public static final String PLv3_PAN = "**********";
    public static final String JWT_RISK = "RISK";
    public static final String JWT_RISK_KEY = "083e1fa8-f144-4d62-af65-a8728e3e132e";

    public static final String PL_V3_ABFL_LENDING_DYNAMIC_TNC = "loan_agreement_pl_risk_abfl";
    public static final String PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC = "sanction_letter_pl_risk_abfl";
    public static final String DOB_ABFL = "1979-10-05";

    public static final String SOLUTION_TYPE_LEVEL2_CLIX = "CLIX";
    public static final String PL_V3_CLIX_LENDING_DYNAMIC_TNC = "loan_agreement_PL_clix";
    public static final String PL_V3_CLIX_LENDING_DYNAMIC_SECONDARY_TNC = "sanction_letter_pl_clix";
    public static final String DOB_CLIX = "1994-12-02";

    public static final String PANEL_LOGIN_USERNAME = "7771216290";
    public static final String PANEL_LOGIN_PASSWORD = "paytm@123";
    public static final String CKYC_NAME = "RONIT ARYA";

    public static final String HOME_FIRST = "HOME_FIRST";


    public static final String LMS_JWT_Secret = "fd61f785-e867-471b-90c6-1447b4331712";
    public static final String BUREAU_PRIORITY = "CIBIL,EXPERIAN";
    public static final String LENDING_KYB_SOURCE = "lending-pp";
    public static final String USER_INPUT_F_NAME = "Nishant";
    public static final String USER_INPUT_M_NAME = "";
    public static final String USER_INPUT_L_NAME = "Sharma";
    public static final String POSTPAID_V3_SOLUTION_LEVEL2 = "ABFL";
    public static final String CKYC_ID = "12345678901234";
    public static final String PP_PAN_SBP_ABFL = "**********";
    public static final String PP_PAN_SBP_ABFL_RTO = "**********";
    public static final String PP_PAN_OA_ABFL = "**********";
    public static final String PP_PAN_BUREAU_EXPIRED = "**********";
    public static final String PP_PAN_KYCv1_ABFL = "**********";
    public static final String PP_PAN_DEDUPE = "**********";
    public static final String PP_PAN_KYC_REJECT_ABFL = "**********";
    public static final String PP_PAN_ABFL = "**********";

    public static final String CKYC_EMAIL = "<EMAIL>";

    public static final String CKYC_F_NAME = "TOUCH";
    public static final String CKYC_M_NAME = "WOOD";
    public static final String CKYC_L_NAME = "LIMITED";
    public static final String FATHER_NAME = "PRAVEEN SHARMA";
    public static final String MOTHER_NAME = "RASHMI SHARMA";
    public static final String MARITAL_STATUS = "MARRIED";
    public static final String CKYC_GENDER = "MALE";
    public static final String CKYC_SUCCESS_MODE_SBP = "SEARCH_BY_PAN";
    public static final String CKYC_SUCCESS_MODE_OA = "OFFLINE_AADHAAR";
    public static final String CKYC_DOB = "1982-04-11";
    public static final String addressType = "RESIDENTIAL";
    public static final String addressSubType = "PERMANENT";
    public static final String line1 = "8A-410/412, DDA JANTA FLATS";
    public static final String line2 = "MAYUR KUNJ";
    public static final String line3 = "MAYUR VIHAR PHASE 1";
    public static final String landmark = "Punjab National Bank";
    public static final String city = "DELHI";
    public static final String state = "EAST DELHI";
    public static final String country = "INDIA";
    public static final String pincode = "110096";
    public static final String SELFIE_MATCH_PERCENTAGE_100 = "100";
    public static final String SELFIE_MATCH_PERCENTAGE_10 = "10";
    public static final String PL_SET_MANDATE = "pl_set_mandate";
    public static final String JWT_LENDING = "LENDING";
    public static final String JWT_LENDING_KEY = "4f438a17-2436-4231-a172-c338ab9d40bb";

    public static final String CKYC_F_NAME_REJECT = "NISHANT";
    public static final String CKYC_M_NAME_REJECT = "KUMAR";
    public static final String CKYC_L_NAME_REJECT = "SHARMA";

    public static final String SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP = "ABFL_TOPUP";
    public static final String SOLUTION_TYPE_LEVEL_2_HERO_TOPUP = "HERO_TOPUP";
    public static final String SOLUTION_TYPE_LEVEL_2_CLIX_TOPUP = "CLIX_TOPUP";
    public static final String LENDING_DYNAMIC_TNC_CLIX_TOPUP = "loan_agreement_pl_topup_clix";
    public static final String LENDING_DYNAMIC_SECONDARY_TNC_CLIX_TOPUP = "sanction_letter_pl_topup_clix";
    public static final String STATIC_TNC_SETNAME_CLIX_TOPUP = "personalloan_oclconsent_hero";
    public static final String LENDER_STATIC_TNC_SETNAME_CLIX_TOPUP = "pl_hero_ckyc_consent";

    public static final String PP_PAN_KYCv1_Fullerton = "**********";
    public static final String PP_PAN_Fullerton = "";
    public static final String PP_PAN_SBP_FULLERTON = "**********";
    public static final String PP_PAN_OA_FULLERTON = "**********";
    public static final String PP_PAN_KYC_REJECT_FULLERTON = "**********";

    public static final String SOLUTION_TYPE_LEVEL2_FULLERTON = "FULLERTON";

    public static final String CREDIT_CARD = "credit_card";
    public static final String HDFC_SOLUTION_TYPE_LEVEL2 = "HDFC";
    public static final String HDFC_IP_ADDRESS = "***********";


    public static final String PP_PAN_ABFL_KYC_3_0 = "**********";


    public static final String BANK_NAME_STAGING3 = "BENE";
    public static final String BANK_NAME_STAGING10 = "testNameMatch";


    public static final boolean IS_MOCK_CALLBACK = true;


    public static final String PP_PAN_ABFL_KYC_3_2 = "**********";
    public static final String PP_PAN_FULLERTON_KYC_3_2 = "**********";
    public static final String CREDIT_CARD_TYPE_LEVEL2_HDFC = "HDFC";
    public static final String CREDIT_CARD_HDFC_PAN = "**********";
    public static final String CC_HDFC_ETB = "ETB";
    public static final String CC_HDFC_NTB = "NTB";
    public static final String SBI_SOLUTION_TYPE_LEVEL2 = "SBI";
    public static final String SBI_IP_ADDRESS = "***********";
    public static final String CREDIT_CARD_SBI_PAN = "**********";
    public static final String EMI = "paytm_emi";

    public static final String EMI_STATIC_TNC_SETNAME = "Paytm_EMI_static_TnC";
    public static final String EMI_LENDER_STATIC_TNC_SETNAME = "Paytm_EMI_Lender_static_TnC";
    public static final String EMI_LENDING_DYNAMIC_SECONDARY_TNC = "paytm_emi_dynamic_tnc";
    public static final String EMI_KYC_ADDRESS_LENDER_STATIC_TNC_SETNAME = "paytm_emi_address_tnc";
    public static final String SBI_SPRINT_SOLUTION_TYPE_LEVEL2 = "SBI_SPRINT";

    public static final String SOLUTION_TYPE_LEVEL2_HERO_DISTRIBUTION = "HERO_DISTRIBUTION";

    public static final String BUREAU_TYPE_EXPERIAN = "EXPERIAN";
    public static final String CREDIT_STATE_CPF = "COMPLETE_PROFILE_FETCHED";


    public static final String LTFS_PRODUCT_ID = "110";
    public static final String LTFS_LENDING_DYNAMIC_TNC = "ltfs_pl_loan_agreement";
    public static final String LTFS_LENDING_DYNAMIC_SECONDARY_TNC = "ltfs_pl_sanction_letter";
    public static final String LTFS_STATIC_TNC_SETNAME = "personalloan_oclconsent_linked";
    public static final String LTFS_LENDER_STATIC_TNC_SETNAME = "pl_consent_bureau_kyc_ltfs";
    public static final String LTFS_BASE_ID = "PL_LTFS_1702094180_fe52d907";
    public static final String LTFS_LOAN_OFFER_ID = "d1bf8548-ce17-4105-ad2a-74f5d20d2ce7";
    public static final String LTFS_LENDER_NAME = "LTFS";
    public static final String LTFS_PINCODE = "560076";
    public static final String TRUE = "TRUE";
    public static final String FALSE = "FALSE";
    public static final String WHITELISTING_SOURCE = "RISK";
    public static final String LTFS_LENDER_ID = "11";
    public static final String LTFS_IP_ADDRESS = "************";
    public static final String LTFS_SOLUTION_TYPE_LEVEL2 = "LTFS";
    public static final String LTFS_DOB = "1987-07-13";
    public static final String LTFS_PAN = "**********";
    public static final String LTFS_EMAIL = "<EMAIL>";
    public static final String LTFS_LOAN_PURPOSE = "Personal Use";
    public static final String LTFS_GENDER = "MALE";
    public static final String LTFS_BUSINESS_NAME = "Hirandani Estate";
    public static final String EMPLOYER_NAME = "Paytm";
    public static final String ANNUAL_INCOME = "200000";
    public static final String EMPLOYER_ID = "30099";

    public static final String PL_PO_CPV="pl_po_cpv";



    /* Update this for Shailesh Singh Rawat Data -> Search By Pan */
    public static final String SSR_PAN = "**********";
    public static final String SSR_DOB = "1987-07-13";
    public static final String SSR_GENDER = "MALE";
    public static final String SSR_NSDL_NAME = "SHAILESH SINGH RAWAT";
    /*--------------------------END-----------------------------------*/


    /* Update this for Nagu Singh Data -> Search By Pan */
	public static final String NS_PAN="**********";
	public static final String NS_DOB="1987-07-13";
	public static final String NS_GENDER="MALE";
	public static final String NS_NSDL_NAME="Nagu Singh";
    /*--------------------------END-----------------------------------*/


    /* Update this for Rohan Shivaji Sonawane Data -> Search By Pan */
    public static final String OA_KYC_PAN="**********";
    public static final String OA_NSDL_NAME="Rohan Shivaji Sonawane";
    public static final String OA_DOB="1998-09-12";

    public static final String KYC_FAILED_NODE_ID = "2002";
    public static final String KYC_INITIATE_NODE_ID="2000";
    /*--------------------------END-----------------------------------*/


}