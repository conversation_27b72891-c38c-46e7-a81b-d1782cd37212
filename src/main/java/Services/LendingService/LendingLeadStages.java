package Services.LendingService;



public enum LendingLeadStages {

	LEAD_NOT_PRESENT("LEAD_NOT_PRESENT"),
	LEAD_CREATED("LEAD_CREATED"),
	PPBL_TNC_VERIFIED("PPBL_TNC_VERIFIED"),
	BRE_OTP_VERIFIED("BRE_OTP_VERIFIED"),
	BRE_RESPONSE_AWAITED("BRE_RESPONSE_AWAITED"),
	BRE2_RESPONSE_AWAITED("BRE2_RESPONSE_AWAITED"),
	BRE_SUCCESS("BRE_SUCCESS"),
	BRE2_SUCCESS("BRE2_SUCCESS"),
	PAN_VERIFIED("PAN_VERIFIED"),
	BANKING_ACTION_DONE("BANKING_ACTION_DONE"),
	BANK_VALIDATION_PENDING("BANK_VALIDATION_PENDING"),
	LOAN_AGREEMENT_SUCCESS("LOAN_AGREEMENT_SUCCESS"),
	EMANDATE_SUCCESS("EMANDATE_SUCCESS"),
	APPLICATION_PENDING("APPLICATION_PENDING"),
	KAFKA_PUSH_SUCCESS("KAFKA_PUSH_SUCCESS"),
	LOAN_APPLICATION_ACCEPTED("LOAN_APPLICATION_ACCEPTED"),
	LMS_SUBMIT_APPLICATION_FAILED("LMS_SUBMIT_APPLICATION_FAILED"),
	LMS_SUBMIT_APPLICATION_SUCCESS("LMS_SUBMIT_APPLICATION_SUCCESS"),
	QC_ACTION_PENDING("QC_ACTION_PENDING"),
	ADDITIONAL_DATA_CAPTURED("ADDITIONAL_DATA_CAPTURED"),
	FRAUD_REJECT("FRAUD_REJECT"),
	BASIC_DETAILS("BASIC_DETAILS"),
	BRE_VALIDATION_PENDING("BRE_VALIDATION_PENDING"),
	OTP_VERIFIED("OTP_VERIFIED"),
	BUSINESS_EMAIL_VERIFIED("BUSINESS_EMAIL_VERIFIED"),
	ADDITIONAL_DETAILS("ADDITIONAL_DETAILS"),
	ADDRESS_DETAILS("ADDRESS_DETAILS"),
	CKYC_VERIFIED("CKYC_VERIFIED"),
	OCCUPATION_DETAILS("OCCUPATION_DETAILS"),
	LOAN_OFFER_ACCEPTED("LOAN_OFFER_ACCEPTED"),
	SECOND_BRE_INITIATED("SECOND_BRE_INITIATED"),
	SECOND_BRE_SUCCESS("SECOND_BRE_SUCCESS"),
	THIRD_BRE_INITIATED("THIRD_BRE_INITIATED"),
	THIRD_BRE_SUCCESS("THIRD_BRE_SUCCESS"),
	LEAD_POSTED("LEAD_POSTED"),
	BUREAU_SUCCESS("BUREAU_SUCCESS"),
	OFFER_GENERATED("OFFER_GENERATED"),
	KYC_COMPLETED("KYC_COMPLETED"),
	LENDER_OFFER_REQUESTED("LENDER_OFFER_REQUESTED"),
	LENDER_SUBMIT_SUCCESS("LENDER_SUBMIT_SUCCESS"),
	LENDER_APPLICATION_SUBMITTED("LENDER_APPLICATION_SUBMITTED"),
	LOAN_ACCEPTED("LOAN_ACCEPTED"),
	LOAN_SANCTIONED("LOAN_SANCTIONED"),
	LOAN_DISBURSED("LOAN_DISBURSED"),
	LOAN_ACCOUNT_ACKNOWLEDGED("LOAN_ACCOUNT_ACKNOWLEDGED"),
	LOAN_ACCOUNT_CREATED("LOAN_ACCOUNT_CREATED"),
	LENDER_APPLICATION_REQUEST_SUCCESS("LENDER_APPLICATION_REQUEST_SUCCESS"),
	LENDER_LOAN_ACCOUNT_ACKNOWLEDGED("LENDER_LOAN_ACCOUNT_ACKNOWLEDGED"),
	LOAN_PROCESSING_ERROR("LOAN_PROCESSING_ERROR"),
	LOAN_REJECTED("LOAN_REJECTED"),
	BRE1_REQUESTED("BRE1_REQUESTED"),
	BRE1_SUCCESS("BRE1_SUCCESS"),
	ADDITIONAL_DETAILS_SUBMITTED("ADDITIONAL_DETAILS_SUBMITTED"),
	ADDITIONAL_AND_CO_APPLICANT_DETAILS_SUBMITTED("ADDITIONAL_AND_CO_APPLICANT_DETAILS_SUBMITTED"),
	LENDER_APPLICATION_SUCCESS("LENDER_APPLICATION_SUCCESS"),
	LENDER_APPOINTMENT_REQUESTED("LENDER_APPOINTMENT_REQUESTED"),
	LENDER_APPOINTMENT_BOOKED_FAILURE("LENDER_APPOINTMENT_BOOKED_FAILURE"),
	LENDER_APPOINTMENT_BOOKED_SUCCESS("LENDER_APPOINTMENT_BOOKED_SUCCESS"),

	BRE_IN_PROGRESS("BRE_IN_PROGRESS"),
	OFFER_REQUESTED("OFFER_REQUESTED"),
	BRE_COMPLETED("BRE_COMPLETED"),
	KYC_IN_PROGRESS("KYC_IN_PROGRESS"),
	KYC_COMPLETE("KYC_COMPLETE"),
	ADDITIONAL_DATA_REQUIRED("ADDITIONAL_DATA_REQUIRED"),
	ADDITIONAL_DATA_NOT_REQUIRED("ADDITIONAL_DATA_NOT_REQUIRED"),
	BRE3_SUCCESS("BRE3_SUCCESS"),
	PREDISBURSAL_INITIATED("PREDISBURSAL_INITIATED"),
	EMANDATE_REQUIRED("EMANDATE_REQUIRED"),
	LUP_CREATION_SUCCESS("LUP_CREATION_SUCCESS"),
	LMS_CALLBACK_SUCCESS("LMS_CALLBACK_SUCCESS"),
	BRE2_REQUESTED("BRE2_REQUESTED"),
	LIS_SUBMIT_SUCCESS("LIS_SUBMIT_SUCCESS"),
	LIS_DATA_UPDATE_REQUEST_SUCCESS("LIS_DATA_UPDATE_REQUEST_SUCCESS"),

	BRE3_NOT_REQUIRED("BRE3_NOT_REQUIRED"),

	PRE_BASIC_DETAILS("PRE_BASIC_DETAILS"),
	SBP_CKYC_ID_FOUND("SBP_CKYC_ID_FOUND"),
	SBP_VALIDATION_SUCCESS("SBP_VALIDATION_SUCCESS"),
	DOCUMENT_UPLOADED("DOCUMENT_UPLOADED"),
	BSA_DOC_UPLOAD_SUCCESS("BSA_DOC_UPLOAD_SUCCESS"),
	BANK_STATEMENT_VERIFICATION_SUCCESS("BANK_STATEMENT_VERIFICATION_SUCCESS"),

	BSA_PROCESS_COMPLETED("BSA_PROCESS_COMPLETED"),
	LENDING_LIS_SUBMIT_APPLICATION_SUCCESS("LENDING_LIS_SUBMIT_APPLICATION_SUCCESS"),
	KYC_PENDING("KYC_PENDING"),
	DATA_PREFILL_SUCCESS("DATA_PREFILL_SUCCESS"),
	NAME_MISMATCH("NAME_MISMATCH"),
	LEAD_REJECTED("LEAD_REJECTED"),
	BANK_VERIFICATION_FAILURE("BANK_VERIFICATION_FAILURE"),
	SBP_CKYC_ID_NOT_FOUND("SBP_CKYC_ID_NOT_FOUND"),
	OFFLINE_AADHAR_VALIDATION_FAILED("OFFLINE_AADHAR_VALIDATION_FAILED"),
	KYC_REQUIRED("KYC_REQUIRED"),
	KYC_INITIATED("KYC_INITIATED"),
	OA_VALIDATION_SUCCESS("OA_VALIDATION_SUCCESS"),
	OA_REUPLOAD_SELFIE("OA_REUPLOAD_SELFIE"),
	LOAN_QC_REJECTED("LOAN_QC_REJECTED"),
	KYC_SELFIE_REQUIRED("KYC_SELFIE_REQUIRED"),
	KYC_SELFIE_UPLOADED("KYC_SELFIE_UPLOADED"),
	PAN_DEDUPE_FAILED("PAN_DEDUPE_FAILED"),
	BRE2_COMPLETED("BRE2_COMPLETED"),
	REQUEST_OFFER_VALIDATION_SUCCESS("REQUEST_OFFER_VALIDATION_SUCCESS"),
	LENDER_APPROVED_OFFER("LENDER_APPROVED_OFFER"),
	LOCATION_REQUIRED("LOCATION_REQUIRED"),
	LOCATION_CAPTURED("LOCATION_CAPTURED"),
	KYC_FAILED("KYC_FAILED"),
	BRE2_IN_PROGRESS("BRE2_IN_PROGRESS"),
	LENDER_KYC_DOC_SYNC_FAILURE("LENDER_KYC_DOC_SYNC_FAILURE"),
	LENDER_KYC_DOC_SYNC_SUCCESS("LENDER_KYC_DOC_SYNC_SUCCESS"),
	LENDER_KYC_DOC_SYNC_REQUESTED("LENDER_KYC_DOC_SYNC_REQUESTED"),
	LENDER_DOC_SYNC_IN_PROGRESS("LENDER_DOC_SYNC_IN_PROGRESS"),
	OFFER_LINKING_IN_PROGRESS("OFFER_LINKING_IN_PROGRESS"),
	BUREAU_EXPIRED("BUREAU_EXPIRED"),
	BRE3_REQUESTED("BRE3_REQUESTED"),
	APPLICATION_SUBMISSION_IN_PROGRESS("APPLICATION_SUBMISSION_IN_PROGRESS"),
	APPLICATION_SUBMISSION_SUCCESS("APPLICATION_SUBMISSION_SUCCESS"),
	BUREAU_INITIATED("BUREAU_INITIATED"),
	ADDRESS_CONFIRMED("ADDRESS_CONFIRMED"),
	BRE3_IN_PROGRESS("BRE3_IN_PROGRESS"),
	BASIC_DETAILS_REQUIRED("BASIC_DETAILS_REQUIRED"),
	KYC_ADDRESS_CONSENT_ACCEPTED("KYC_ADDRESS_CONSENT_ACCEPTED"),
	QDE_INITIATED("QDE_INITIATED"),
	QDE_SUBMITTED("QDE_SUBMITTED"),
	CUSTOMER_SOFT_APPROVED("CUSTOMER_SOFT_APPROVED"),
	BASIC_DETAILS_SUBMITTED("BASIC_DETAILS_SUBMITTED"),
	DDE_SUBMIT_SUCCESS("DDE_SUBMIT_SUCCESS"),
	NET_BANKING_VERIFICATION_SUCCESS("NET_BANKING_VERIFICATION_SUCCESS"),
	APPLICATION_APPROVED("APPLICATION_APPROVED"),
	APPLICATION_CLOSED("APPLICATION_CLOSED"),
	DDE_INITIATED("DDE_INITIATED"),
	SECOND_BRE_ERROR("SECOND_BRE_ERROR"),
	EKYC_VERIFICATION_SUCCESS("EKYC_VERIFICATION_SUCCESS"),
	VKYC_INITIATED("VKYC_INITIATED"),
	VKYC_COMPLETED("VKYC_COMPLETED"),
	VKYC_VERIFICATION_SUCCESS("VKYC_VERIFICATION_SUCCESS"),
	APPOINTMENT_BOOKING_SUCCESS("APPOINTMENT_BOOKING_SUCCESS"),
	KYC_SELFIE_REUPLOAD_REQUIRED("KYC_SELFIE_REUPLOAD_REQUIRED"),
	KYC_RETRYABLE_FAILURE("KYC_RETRYABLE_FAILURE"),
	PENNYDROP_CREDIT_SUCCESS("PENNYDROP_CREDIT_SUCCESS"),
	PENNYDROP_DEBIT_SUCCESS("PENNYDROP_DEBIT_SUCCESS"),
	MITC_COMPLETED("MITC_COMPLETED"),
	ECARD_GENERATED("ECARD_GENERATED"),
	PHOTO_MATCHED("PHOTO_MATCHED"),
	RE_INITIATE_KYC("RE_INITIATE_KYC"),
	EDI_SOLE_PROP_DECLARATION_REQUIRED("EDI_SOLE_PROP_DECLARATION_REQUIRED"),
	BRE1_COMPLETED("BRE1_COMPLETED"),

	LIS_CREATE_APPLICATION_INITIATED("LIS_CREATE_APPLICATION_INITIATED"),
	LIS_CREATE_APPLICATION_SUCCESS("LIS_CREATE_APPLICATION_SUCCESS"),
	LENDER_BRE_INITIATED("LENDER_BRE_INITIATED"),
	LENDER_BRE_SUCCESS("LENDER_BRE_SUCCESS"),
	EMANDATE_SKIPPED("EMANDATE_SKIPPED"),
	INITIATE_LENDER_SUBMIT("INITIATE_LENDER_SUBMIT"),
	LOAN_ONBOARDING_SUCCESS("LOAN_ONBOARDING_SUCCESS"),
	KYC_ADDRESS_CONSENT_OPTIONAL("KYC_ADDRESS_CONSENT_OPTIONAL"),
	CURRENT_ADDRESS_UPDATED("CURRENT_ADDRESS_UPDATED"),
	CURRENT_ADDRESS_OVD_UPLOADED("CURRENT_ADDRESS_OVD_UPLOADED"),
	LENDER_SUBMIT_INITIATED("LENDER_SUBMIT_INITIATED"),
	CURRENT_ADDRESS_UPDATE_IN_PROGRESS("CURRENT_ADDRESS_UPDATE_IN_PROGRESS"),
	PO_CURRENT_ADDRESS_UPDATED("PO_CURRENT_ADDRESS_UPDATED"),
	PO_CURRENT_ADDRESS_OVD_UPLOADED("PO_CURRENT_ADDRESS_OVD_UPLOADED"),
	CPV_INITIATED("CPV_INITIATED"),
	PO_APPLICATION_UNDER_REVIEW("PO_APPLICATION_UNDER_REVIEW"),
	LIS_DATA_UPDATE_SUCCESS("LIS_DATA_UPDATE_SUCCESS"),
	SERVICEABILITY_REJECTED("SERVICEABILITY_REJECTED"),
	QDE_REQUEST_SUCCESS("QDE_REQUEST_SUCCESS"),
	CPV_DATA_CAPTURED("CPV_DATA_CAPTURED"),

	LOCATION_NOT_REQUIRED("LOCATION_NOT_REQUIRED"),
	CPV_QC_ACTION_PENDING("CPV_QC_ACTION_PENDING"),
	DDE_REQUEST_SUCCESS("DDE_REQUEST_SUCCESS"),
	PO_BANKING_ACTION_DONE("PO_BANKING_ACTION_DONE"),
	PO_BANK_VALIDATION_PENDING("PO_BANK_VALIDATION_PENDING"),
	OFFER_LINKING_FAILED("OFFER_LINKING_FAILED"),
	KYC_DATA_PREFILL_REQUESTED("KYC_DATA_PREFILL_REQUESTED");







	private String stage;

	public String getStage() {
		return stage;
	}


	LendingLeadStages(String stage){

		this.stage=stage;

	}
}
