package Services.PGP;


import Request.PGP.AppPay;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

import java.util.Map;

public class PGPServices extends BaseMethod
{
    public Response payMerchant(AppPay payObj, String SessionToken, Map<String,String> body)
    {
        for(Map.Entry m:body.entrySet())
        {
            payObj.getProperties().setProperty(m.getKey().toString(),m.getValue().toString());
        }
        payObj.setHeader("ssotoken",SessionToken);
        payObj.setHeader("Content-Type","application/json");

        Response payMerchResp = payObj.callAPI();
        return  payMerchResp;
    }
}
