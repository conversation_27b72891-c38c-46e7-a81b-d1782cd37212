package Services.VMN;
import Request.VMN.*;
import io.restassured.response.Response;

import java.util.Map;

public class GetMerchantDetails{


        public Response MerchantDetails(String path, Map<String,String> headers, Map<String,String> body, Map<String,String> params)
        {

            MerchantDetailsURLPath fetch = new MerchantDetailsURLPath(path);


            for(Map.Entry m : headers.entrySet())
            {
                fetch.setHeader(m.getKey().toString(), m.getValue().toString());
            }
            for(Map.Entry m : body.entrySet())
            {
                fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
            }

            for(Map.Entry m : params.entrySet())
            {
                fetch.addParameter(m.getKey().toString(), m.getValue().toString());
            }


            Response fetchVMNresponse=fetch.callAPI();
            return fetchVMNresponse;


        }



}
