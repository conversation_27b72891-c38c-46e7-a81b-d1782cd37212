package Services.ATS;

import Request.ats.CreateOrderPackForPickUp;
import io.restassured.response.Response;

import java.util.Map;

public class ATSServices
{
    public static Response createPackForPickupOrder(CreateOrderPackForPickUp CreateOrderPackForPickUpObj, Map<String, String> headers, Map<String, String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            CreateOrderPackForPickUpObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            CreateOrderPackForPickUpObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response CreateOrderPackForPickUpObjResp = CreateOrderPackForPickUpObj.callAPI();
        return CreateOrderPackForPickUpObjResp;
    }
}
