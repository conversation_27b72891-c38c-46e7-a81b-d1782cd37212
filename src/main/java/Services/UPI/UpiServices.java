package Services.UPI;

import Request.UPI.*;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

public class UpiServices extends BaseMethod {


   public Response InitiateSms (UpiInitiateSmsV3 IniSms, String DeviceId, String ReqId,String SSO)
    {
        IniSms.addParameter("fetchAccounts","true");
        IniSms.addParameter("timestamp","4.8.0");
        IniSms.addParameter("carrier","airtel|AIRTEL");
        IniSms.addParameter("slotNumber","1|2");
        IniSms.addParameter("imeiNumber","***********|***********");
        IniSms.addParameter("iccId","********|3456789");
        IniSms.addParameter("simSubscriptionId","*********|********");
        IniSms.addParameter("dualSimSupported","true");
        IniSms.addParameter("deviceId",DeviceId);
        IniSms.addParameter("requestId",ReqId);

        IniSms.setHeader("Accept-Encoding","gzip, deflate");
        IniSms.setHeader("Cache-Control","no-cache");
        IniSms.setHeader("Connection","keep-alive");
        IniSms.setHeader("cache-control","no-cache");
        IniSms.setHeader("channel","paytm");
        IniSms.setHeader("Content-Type","application/json");
        IniSms.setHeader("channel-token",SSO);


        Response IniSmsResp = IniSms.callAPI();
        return IniSmsResp;

    }

    public Response StoreSms(UpiStoreSms StrSms,String Mobile,String Verification)
    {
        StrSms.setHeader("Accept-Encoding","gzip, deflate");
        StrSms.setHeader("Cache-Control","no-cache");
        StrSms.setHeader("Connection","keep-alive");
        StrSms.setHeader("cache-control","no-cache");
        StrSms.setHeader("channel","paytm");
        StrSms.setHeader("Content-Type","application/json");


        StrSms.getProperties().setProperty("mobile",Mobile);
        StrSms.getProperties().setProperty("Verification",Verification);

        Response StrSmsResp = StrSms.callAPI();
        return StrSmsResp;
    }

    public Response DeviceBind(UpiDeviceBinding DevBind,String SSO,String DeviceId, String ReqId,String Mobile,String Verify)
    {
        DevBind.setHeader("Content-Type","application/x-www-form-urlencoded");
        DevBind.setHeader("channel-token",SSO);

        DevBind.addFormParameter("device-id",DeviceId);
        DevBind.addFormParameter("mobile","91"+Mobile);
        DevBind.addFormParameter("seq-no",ReqId);
        DevBind.addFormParameter("channel-code","paytm");
        DevBind.addFormParameter("verification-data",Verify);
        DevBind.addFormParameter("customer-email-id",Mobile+"@gmail.com");
        DevBind.addFormParameter("restore-old-upiprofile","true");

        Response DevBindResp = DevBind.callAPI();
        return DevBindResp;
    }

    public Response AddVpa(UpiAddVpa AddVpa ,String SSO,String DeviceId, String ReqId,String Mobile)
    {
        AddVpa.setHeader("Content-Type","application/x-www-form-urlencoded");
        AddVpa.setHeader("channel-token",SSO);

        AddVpa.addFormParameter("device-id",DeviceId);
        AddVpa.addFormParameter("mobile","91"+Mobile);
        AddVpa.addFormParameter("seq-no",ReqId);
        AddVpa.addFormParameter("channel-code","paytm");
        AddVpa.addFormParameter("customer-name","AASHIT");
        AddVpa.addFormParameter("customer-vpa",Mobile+"AS@paytm");
        AddVpa.addFormParameter("customer-code","0000");
        AddVpa.addFormParameter("customer-type","PERSON");

        Response AddVpaResp = AddVpa.callAPI();
        return AddVpaResp;
    }

    public Response ListAccount(UpiListAccount ListAc,String SSO,String DeviceId, String ReqId,String Mobile)
    {
        ListAc.setHeader("Content-Type","application/x-www-form-urlencoded");
        ListAc.setHeader("channel-token",SSO);

        ListAc.addFormParameter("device-id",DeviceId);
        ListAc.addFormParameter("mobile","91"+Mobile);
        ListAc.addFormParameter("seq-no",ReqId);
        ListAc.addFormParameter("channel-code","paytm");
        ListAc.addFormParameter("payer-va",Mobile+"AS@paytm");
        ListAc.addFormParameter("payer-name","AASHIT");
        ListAc.addFormParameter("ifsc","AABE");

        Response ListAcResp = ListAc.callAPI();
        return ListAcResp;

    }

    public Response AddAccount(UpiAddAccount AddAcc,String SSO,String DeviceId, String ReqId,String Mobile,String AccNo,String IFSC)
    {
        AddAcc.setHeader("Content-Type","application/x-www-form-urlencoded");
        AddAcc.setHeader("channel-token",SSO);

        AddAcc.addFormParameter("device-id",DeviceId);
        AddAcc.addFormParameter("mobile","91"+Mobile);
        AddAcc.addFormParameter("seq-no",ReqId);
        AddAcc.addFormParameter("channel-code","paytm");
        AddAcc.addFormParameter("virtual-address",Mobile+"AS@paytm");
        AddAcc.addFormParameter("account-no",AccNo);
        AddAcc.addFormParameter("ifsc-code",IFSC);
        AddAcc.addFormParameter("cred-block","[{\"CredsAllowedType\":\"OTP\",\"CredsAllowedDType\":\"Numeric\",\"CredsAllowedSubType\":\"SMS\",\"CredsAllowedDLength\":\"6\",\"dLength\":\"6\"},{\"CredsAllowedType\":\"PIN\",\"CredsAllowedDType\":\"Numeric\",\"CredsAllowedSubType\":\"MPIN\",\"CredsAllowedDLength\":\"6\",\"dLength\":\"6\"},{\"CredsAllowedType\":\"PIN\",\"CredsAllowedDType\":\"Numeric\",\"CredsAllowedSubType\":\"ATMPIN\",\"CredsAllowedDLength\":\"6\",\"dLength\":\"6\"}]");
        AddAcc.addFormParameter("default-debit","D");
        AddAcc.addFormParameter("default-credit","D");
        AddAcc.addFormParameter("account-type","SAVINGS");

        Response AddAccResp = AddAcc.callAPI();
        return AddAccResp;
    }

    public static Response GetUPIAccountInfo(GetUPIAccountDetail GetUPIAccount, String Jwt_token, String custId)
    {
        GetUPIAccount.setHeader("Content-Type","application/json");
        GetUPIAccount.setHeader("channel","OE");
        GetUPIAccount.setHeader("Authorization",Jwt_token);
        GetUPIAccount.getProperties().setProperty("custId", custId);



        Response GetUPIAccResp = GetUPIAccount.callAPI();
        return GetUPIAccResp;
    }





}
