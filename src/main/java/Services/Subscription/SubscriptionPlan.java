package Services.Subscription;

import Request.Subscription.*;
import io.restassured.response.Response;

import java.util.Map;

public class SubscriptionPlan {
	
	
	public Response subscriptionfetchPlan(Map<String,String> headers,Map<String,String> body,Map<String,String> params)
	{
		FetchPlanSubscription fetch=new FetchPlanSubscription();
		for(Map.Entry m : headers.entrySet())
		{
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}
		
		for(Map.Entry m : params.entrySet())
		{
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}
		
		
		Response fetchsubsresponse=fetch.callAPI();
		return fetchsubsresponse;
		
		
	}
	
	
	public Response subscriptionOnboardPlan(Map<Object,Object> headers,Map<Object,Object> body)
	{
		SubscriptionPlanOnboarding onboard=new SubscriptionPlanOnboarding();
		for(Map.Entry m : headers.entrySet())
		{
			onboard.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			onboard.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}
		
		
		
		
		Response onboardsubsresponse=onboard.callAPI();
		return onboardsubsresponse;
		
		
	}

	public Response subcriptionCreatePlan(CreateSubscription CreateSubscriptionObj , Map<String,String> headers, Map<String,String> body)
	{

		for(Map.Entry m : headers.entrySet())
		{
			CreateSubscriptionObj.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			CreateSubscriptionObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}


		Response CreateSubscriptionObjResp=CreateSubscriptionObj.callAPI();
		return CreateSubscriptionObjResp;

	}

	public Response subcriptionStatus(SubscriptionStatus SubscriptionStatusObj , Map<String,String> headers, Map<String,String> body)
	{

		for(Map.Entry m : headers.entrySet())
		{
			SubscriptionStatusObj.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			SubscriptionStatusObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}


		Response SubscriptionStatusObjResp=SubscriptionStatusObj.callAPI();
		return SubscriptionStatusObjResp;

	}

	public Response replaceSubscription(ReplaceSubscriptionRequest replaceSubscriptionRequestObj, Map<String,String> headers, Map<String,String> body)
	{

		for(Map.Entry m : headers.entrySet())
		{
			replaceSubscriptionRequestObj.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			replaceSubscriptionRequestObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}


		Response ReplaceSubscriptionObjResp= replaceSubscriptionRequestObj.callAPI();
		return ReplaceSubscriptionObjResp;

	}

	public Response fetchNewUsnSubscription( FetchNewUsnSubscription FetchNewUsnSubscriptionObj,Map<String,String> headers,Map<String,String> params)
	{
		for(Map.Entry m : headers.entrySet())
		{
			FetchNewUsnSubscriptionObj.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : params.entrySet())
		{
			FetchNewUsnSubscriptionObj.addParameter(m.getKey().toString(), m.getValue().toString());
		}


		Response FetchNewUsnSubscriptionObjResp=FetchNewUsnSubscriptionObj.callAPI();
		return FetchNewUsnSubscriptionObjResp;

	}

	public Response fetchOldUsnSubscription( FetchOldUsnSubscription FetchOldUsnSubscriptionObj,Map<String,String> headers,Map<String,String> params)
	{
		for(Map.Entry m : headers.entrySet())
		{
			FetchOldUsnSubscriptionObj.setHeader(m.getKey().toString(), m.getValue().toString());
		}

		for(Map.Entry m : params.entrySet())
		{
			FetchOldUsnSubscriptionObj.addParameter(m.getKey().toString(), m.getValue().toString());
		}


		Response FetchOldUsnSubscriptionObjResp=FetchOldUsnSubscriptionObj.callAPI();
		return FetchOldUsnSubscriptionObjResp;

	}

}
