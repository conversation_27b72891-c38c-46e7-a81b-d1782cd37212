package Services.MakerChecker;

import Request.MakerChecker.ReviewService.*;
import Request.MakerChecker.WorkflowEngine.CreateWorkflow;
import Request.MakerChecker.WorkflowEngine.FetchWorkflow;
import Request.MakerChecker.WorkflowEngine.GetWorkflowDetail;
import Request.MakerChecker.WorkflowEngine.ReviewRequestCallback;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;

import java.util.Date;
import java.util.Map;

public class MakerCheckerMiddlewareServices extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(MakerCheckerMiddlewareServices.class);
    public Response fetchWorkflowResponse(FetchWorkflow fetchWorkflow, Map<String, String> headers,Map<String, String> queryParam,Map<String, String> requestBody){
        for (Map.Entry m : headers.entrySet()) {
            fetchWorkflow.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : requestBody.entrySet()) {
            fetchWorkflow.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParam.entrySet()) {
            fetchWorkflow.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response fetchWorkflowResponse = fetchWorkflow.callAPI();
        return fetchWorkflowResponse;

    }
    public Response fetchRejectionReasons(FetchRejectionReasons fetchRejectionReasons, Map<String, String> headers, Map<String, String> queryParam){
        for (Map.Entry m : headers.entrySet()) {
            fetchRejectionReasons.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParam.entrySet()) {
            fetchRejectionReasons.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response fetchRejectionReasonsResponse = fetchRejectionReasons.callAPI();
        return fetchRejectionReasonsResponse;

    }


    public static String generateJwtToken() {
        String secret = "827fd090-249d-4445-be67-3943c2f0c2a2";
        String client = "BOSS";
        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder =
                JWT.create().withIssuedAt(new Date()).withIssuer("WE").
                        withClaim("client", client).
                        withClaim("timestamp", (new DateTime()).toString());

        return builder.sign(buildAlgorithm);
    }

    public static String generateJwtTokenForWorkflowBossClient() {
        String secret = "827fd090-249d-4445-be67-3943c2f0c2a2";
        String client = "BOSS";

        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder =
                JWT.create().withIssuedAt(new Date()).withIssuer("WE").
                        withClaim("client", client).
                        withClaim("timestamp", (new DateTime()).toString());

        return builder.sign(buildAlgorithm);

    }

    public static String generateJwtTokenForWorkflowCMSClient() {
        String secret = "6d7740d2-cf3b-4ad2-a8c8-50acb8cf5273";
        String client = "CMS";

        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder =
                JWT.create().withIssuedAt(new Date()).withIssuer("WE").
                        withClaim("client", client).
                        withClaim("timestamp", (new DateTime()).toString());

        return builder.sign(buildAlgorithm);

    }

    public static String generateJwtTokenForReviewService() {
        String secret = "d374fc49-81a6-4539-af6f-06469e6e1e28";
        String client = "workflow-engine";

        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder =
                JWT.create().
                        withClaim("client", client).
                        withClaim("timestamp", (new DateTime()).toString());

        return builder.sign(buildAlgorithm);

    }
    public static String generateJwtTokenForReviewCallbackToWorkflow() {
        String secret = "cf691309-b563-48ff-b76b-805b260fbfde";
        String client = "Review-Service";

        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder =
                JWT.create().
                        withClaim("client", client).
                        withClaim("timestamp", (new DateTime()).toString());

        return builder.sign(buildAlgorithm);

    }
    public Response fetchDMSDocResponse(FetchDMSDoc fetchDMSDoc, Map<String, String> headers,Map<String, String> queryParam){

        for (Map.Entry m : headers.entrySet()) {
            fetchDMSDoc.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParam.entrySet()) {
            fetchDMSDoc.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchDMSDocResponse = fetchDMSDoc.callAPI();
        return fetchDMSDocResponse;
    }

   public Response dmsDocResponse(DownloadDMSDoc downloadDMSDoc,Map<String, String> headers,Map<String, String> queryParam){
       for (Map.Entry m : headers.entrySet()) {
           downloadDMSDoc.setHeader(m.getKey().toString(), m.getValue().toString());
       }
       for (Map.Entry m : queryParam.entrySet()) {
           downloadDMSDoc.addParameter(m.getKey().toString(), m.getValue().toString());
       }
       Response dmsDocResponse = downloadDMSDoc.callAPI();
      return dmsDocResponse;
   }

    public Response fetchAllcasesResponse(FetchAllCases fetchAllCases, Map<String, String> headers, Map<String, String> queryParam, Map<String, String> requestBody){
        for (Map.Entry m : headers.entrySet()) {
            fetchAllCases.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParam.entrySet()) {
            fetchAllCases.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response fetchAllcasesResponse = fetchAllCases.callAPI();
        return fetchAllcasesResponse;
    }

    public String getBossSessionToken(String userEmail, String password)  {

        //System.setProperty("webdriver.chrome.driver", "/Users/<USER>/Documents/chromedriver");
        String OsName = System.getProperty("os.name");

        if(OsName.contains("Linux"))
        {
            log.info("OS Name is : " +OsName);
            System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir")+"/src/main/resources/chromedriverLinux");
            System.setProperty("webdriver.gecko.driver",System.getProperty("user.dir")+"/src/main/resources/geckodriverLinux");

        }
        else if (OsName.contains("Mac"))
        {
            log.info("OS Name is : " +OsName);
            System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir")+"/src/main/resources/chromedriverV115");
            System.setProperty("webdriver.gecko.driver",System.getProperty("user.dir")+"/src/main/resources/geckodriverMac");

        }

        ChromeOptions options = new ChromeOptions();
        options.setHeadless(true);

        WebDriver driver = new ChromeDriver(options);
        driver.manage().window().maximize();
        driver.get("https://bo-staging.paytm.com/");
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        driver.switchTo().frame(0);
        //Thread.sleep(3000);
        driver.findElement(By.xpath("//*[@id=\"email_mobile_login\"]")).sendKeys(userEmail);
        driver.findElement(By.xpath("//*[@id=\"password_login\"]")).sendKeys(password);
        driver.findElement(By.xpath("//*[@id=\"app\"]/div/div/div/form/div[3]/div[1]/div/button")).click();

        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        driver.findElement(By.xpath("//*[@id=\"otp_login\"]")).sendKeys("888888");
        driver.findElement(By.xpath("//*[@id=\"app\"]/div/div/form/div[2]/button")).click();
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        driver.navigate().refresh();
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        String cookieVal = "BOSS_SESSION="+driver.manage().getCookieNamed("BOSS_SESSION").getValue();
        driver.quit();
        System.out.println(cookieVal);
        return cookieVal;

    }
    public Response CreateWorkflowResponse(CreateWorkflow createWorkflow, Map<String, String> headers, Map<String, String> queryParam, Map<String, String> requestBody){
        for (Map.Entry m : headers.entrySet()) {
            createWorkflow.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : requestBody.entrySet()) {
            createWorkflow.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParam.entrySet()) {
            createWorkflow.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response CreateWorkflowResponse = createWorkflow.callAPI();
        return CreateWorkflowResponse;

    }

    public Response getWorkflowDetailResponse(GetWorkflowDetail getWorkflowDetail, Map<String, String> headers, Map<String, String> queryParam){
        for (Map.Entry m : headers.entrySet()) {
            getWorkflowDetail.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : queryParam.entrySet()) {
            getWorkflowDetail.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response getWorkflowDetailResponse = getWorkflowDetail.callAPI();
        return getWorkflowDetailResponse;
    }

    public Response reviewPanelGetAllResourcesResponse(ReviewPanelGetAllResources reviewPanelGetAllResources, Map<String, String> headers, Map<String, String> queryParam){
        for (Map.Entry m : headers.entrySet()) {
            reviewPanelGetAllResources.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : queryParam.entrySet()) {
            reviewPanelGetAllResources.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response getallResourcesResponse = reviewPanelGetAllResources.callAPI();
        return getallResourcesResponse;
    }

    public Response CreateReviewCaseResponse(CreateReviewCase createReviewCase,Map<String, String> headers, Map<String, String> queryParam, Map<String, String> requestBody){
        for (Map.Entry m : headers.entrySet()){
            createReviewCase.setHeader(m.getKey().toString(),m.getValue().toString());
        }
        for (Map.Entry m : queryParam.entrySet()){
            createReviewCase.addParameter(m.getKey().toString(),m.getValue().toString());
        }
        for (Map.Entry m : requestBody.entrySet()) {
            createReviewCase.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response CreateReviewCaseResponse = createReviewCase.callAPI();
        return CreateReviewCaseResponse;
    }
    public Response approveRejectCaseResponse(ApproveRejectCase approveRejectCase,Map<String, String> headers,Map<String, String> requestBody){
        for (Map.Entry m : headers.entrySet()){
            approveRejectCase.setHeader(m.getKey().toString(),m.getValue().toString());
        }
        for (Map.Entry m : requestBody.entrySet()) {
            approveRejectCase.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response approveRejectCaseResponse = approveRejectCase.callAPI();
        return approveRejectCaseResponse;
    }
    public Response ReviewRequestCallbackResponse(ReviewRequestCallback reviewRequestCallback, Map<String, String> headers, Map<String, String> requestBody){
        for (Map.Entry m : headers.entrySet()){
            reviewRequestCallback.setHeader(m.getKey().toString(),m.getValue().toString());
        }
        for (Map.Entry m : requestBody.entrySet()) {
            reviewRequestCallback.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response ReviewRequestCallbackResponse = reviewRequestCallback.callAPI();
        return ReviewRequestCallbackResponse;
    }
    public Response GetReviewCaseDetailsResponse(GetReviewCaseDetails getReviewCaseDetails, Map<String, String> headers, Map<String, String> queryParam){
        for (Map.Entry m : headers.entrySet()) {
            getReviewCaseDetails.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : queryParam.entrySet()) {
            getReviewCaseDetails.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response GetReviewCaseDetailsResponse = getReviewCaseDetails.callAPI();
        return GetReviewCaseDetailsResponse;
    }
    public Response UpdateReviewCaseStageResponse(UpdateReviewCaseStage updateReviewCaseStage, Map<String, String> headers, Map<String, String> queryParam, Map<String, String> requestBody){
        for (Map.Entry m : headers.entrySet()) {
            updateReviewCaseStage.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : queryParam.entrySet()) {
            updateReviewCaseStage.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : requestBody.entrySet()) {
            updateReviewCaseStage.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response UpdateReviewCaseStageResponse = updateReviewCaseStage.callAPI();
        return UpdateReviewCaseStageResponse;
    }
    public Response FetchReviewStatusCountResponse(FetchReviewStatusCount fetchReviewStatusCount, Map<String, String> headers, Map<String, String> queryParam){
        for (Map.Entry m : headers.entrySet()) {
            fetchReviewStatusCount.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : queryParam.entrySet()) {
            fetchReviewStatusCount.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response FetchReviewStatusCountResponse = fetchReviewStatusCount.callAPI();
        return FetchReviewStatusCountResponse;
    }
    public Response FetchSubGroupCheckersResponse(FetchSubGroupCheckers fetchSubGroupCheckers, Map<String, String> headers, Map<String, String> queryParam){

        for (Map.Entry m : headers.entrySet()) {
            fetchSubGroupCheckers.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : queryParam.entrySet()) {
            fetchSubGroupCheckers.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        // Hit API to get Response
        Response FetchSubGroupCheckersResponse = fetchSubGroupCheckers.callAPI();
        return FetchSubGroupCheckersResponse;
    }

}



