package Services.QR;

import Request.PG.ConfigureMbidOnPG;
import Request.QRService.ActiveVPA;
import Request.QRService.GenerateVPA;
import Request.QRcode.EditQRCodeDetailsRequest;
import io.restassured.response.Response;

import java.util.Map;

public class QRServices
{
    public Response generateAndActivateVPA(GenerateVPA GenerateVPAObj, Map<String,String> headers, Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            GenerateVPAObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            GenerateVPAObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response GenerateVPAObjResp = GenerateVPAObj.callAPI();
        return GenerateVPAObjResp;
    }

    public Response activateVPA(ActiveVPA ActiveVPAObj, Map<String,String> headers, Map<String,String> params)
    {
        for(Map.Entry m : headers.entrySet())
        {
            ActiveVPAObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : params.entrySet())
        {
            ActiveVPAObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response ActiveVPAObjResp = ActiveVPAObj.callAPI();
        return ActiveVPAObjResp;
    }


    
    public Response EditQRCodeDetailsRequest(EditQRCodeDetailsRequest Obj, Map<String,String> headers, Map<String,String> body)
    {
        for(Map.Entry m : headers.entrySet())
        {
            Obj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : body.entrySet())
        {
            Obj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response res = Obj.callAPI();
        return res;
    }


}
