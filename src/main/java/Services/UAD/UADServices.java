package Services.UAD;

import Request.UAD.AddCatSubcatSol;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Request.UAD.pin;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

import java.util.Map;


public class UADServices extends BaseMethod{

    public Response getCategory(Category getCat,String entityType, String solutionName, String session_token, String version)
    {
        getCat.setHeader("Content-Type", "application/json");
        getCat.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getCat.setHeader("session_token", session_token);
        getCat.setHeader("version", version);
        getCat.setHeader("appLanguage", "en");
        getCat.setHeader("deviceName", "CPH1859");
        getCat.setHeader("client", "androidapp");
        getCat.setHeader("imei", "869003037324211");
        getCat.setHeader("deviceManufacturer", "OPPO");
        getCat.setHeader("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        getCat.setHeader("Content-Type", "application/json; charset=UTF-8");

        getCat.getProperties().setProperty("solutionName", solutionName);
        getCat.getProperties().setProperty("entityType", entityType);


        Response getCatResp = getCat.callAPI();


        return getCatResp;
    }

    public Response getSubCategory(SubCategory getSubCat,String entityType, String solutionName, String session_token, String version, Integer categoryId)
    {

        getSubCat.setHeader("Content-Type", "application/json");
        getSubCat.setHeader("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
        getSubCat.setHeader("session_token", session_token);
        getSubCat.setHeader("version", version);
        getSubCat.setHeader("appLanguage", "en");
        getSubCat.setHeader("deviceName", "CPH1859");
        getSubCat.setHeader("client", "androidapp");
        getSubCat.setHeader("imei", "869003037324211");
        getSubCat.setHeader("deviceManufacturer", "OPPO");
        getSubCat.setHeader("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        getSubCat.setHeader("Content-Type", "application/json; charset=UTF-8");

        getSubCat.getProperties().setProperty("solutionName", solutionName);
        getSubCat.getProperties().setProperty("entityType", entityType);
        getSubCat.getProperties().setProperty("categoryId", String.valueOf(categoryId));


        Response getSubCatResp = getSubCat.callAPI();

        return getSubCatResp;
    }

    public Response AddCatSubcatSol (AddCatSubcatSol v1Add, Map<String, String> headers,Map<String, String> body)
    {
        //Adding Headers
        for(Map.Entry m:headers.entrySet())
        {
            v1Add.setHeader(m.getKey().toString(),m.getValue().toString());
        }

        //Adding Body
        for(Map.Entry m:body.entrySet())
        {
            v1Add.getProperties().setProperty(m.getKey().toString(),m.getValue().toString());
        }

        Response Add = v1Add.callAPI();


        return Add;
    }
    

    public Response getCategory(Map <String,String> headers, Map<String, String> body) {

    	Category cat = new Category();
     
        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
        	cat.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
        	cat.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response CategoryResponse = cat.callAPI();

        return CategoryResponse;
    }
    
    
    public Response getPincode(Map <String,String> headers, Map<String, String> body) {

    	pin pn = new pin();
     
        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
        	pn.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Add Body Params
        for (Map.Entry m : body.entrySet()) {
        	pn.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        //Hit API to get Response
        Response PINResponse = pn.callAPI();

        return PINResponse;
    }
    
    
    
    
    
}
