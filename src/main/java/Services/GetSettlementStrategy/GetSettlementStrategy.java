package Services.GetSettlementStrategy;

import Request.SettlementStrategy.SettlementStrategyFetch;
import io.restassured.response.Response;

import java.util.Map;

public class GetSettlementStrategy {
	
	
	public Response GetSettlementStrategyplan(Map<String,String> headers,Map<String,String> body,Map<String,String> params)
	{
		//FetchPlanSubscription fetch=new FetchPlanSubscription();
		SettlementStrategyFetch fetch = new SettlementStrategyFetch();
		for(Map.Entry m : headers.entrySet())
		{
			fetch.setHeader(m.getKey().toString(), m.getValue().toString());
		}
		for(Map.Entry m : body.entrySet())
		{
			fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
		}
		
		for(Map.Entry m : params.entrySet())
		{
			fetch.addParameter(m.getKey().toString(), m.getValue().toString());
		}
		
		
		Response fetchsubsresponse=fetch.callAPI();
		return fetchsubsresponse;
		
		
	}
	
	

}
