package Services.Utilities;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.ssh.SSHConnection;
import com.paytm.apitools.util.ssh.SshDetails;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

public class TestBase extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(TestBase.class);

    static Connection connection = null;
    static SSHConnection sshConnection = new SSHConnection();



    public static String getUbmId = "";
    public static String getWorkflowId = "";
    public static String getWorkflowStatusId = "";
    public static String QrPgReqId = null;
    public static int UpdateQueryResult ;
    public static String ExpectedOutput ="";
    public static String PgReqId = null;



    private static void MakeConnection() {
        try {
            SshDetails sshDetails = new SshDetails();
            sshDetails.setSshHost(P.CONFIG.get("sshHost"));
            sshDetails.setSshUser(P.CONFIG.get("sshUser2"));
            sshDetails.setSshKeyFilepath(System.getProperty("user.dir")+"/src/main/resources/id_rsa_jyoti");
            sshDetails.setRemoteHost(P.CONFIG.get("remoteHost"));
            sshDetails.setDbUserName(P.CONFIG.get("dbUserName"));
            sshDetails.setDbPassword(P.CONFIG.get("dbPassword"));
            sshDetails.setLocalPort(Integer.parseInt(P.CONFIG.get("localPort")));
            sshDetails.setRemotePort(Integer.parseInt(P.CONFIG.get("remotePort")));
            connection = sshConnection.connectToServer(DbName, sshDetails);
        }
        catch (Exception e)
        {e.printStackTrace();}
    }

    public void getUbmId(String number, String solutionType) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query = "select * from user_business_mapping where mobile_number = " + number +
                " and solution_type = '"+solutionType+"' order by created_at desc limit 1;";
            LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
            ResultSet rs = sshConnection.executeQuery(query, connection);
            if (rs.next()) {
                LOGGER.info("This is Result : " + rs.getString("id"));
                getUbmId =(rs.getString("id"));
                sshConnection.closeConnections(connection);
                connection.close();
            }
        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }

    }

    public void getQrMerchantPgReqId(String CustId) throws SQLException {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query = "SELECT DISTINCT pg_request_id from pg_callback_info pci where cust_id = "+CustId+" ORDER BY created_at; ";
        LOGGER.info("This is generated Query : " + query);
        try {
            for (int i = 0;i<=3;i++)
            {
                if (QrPgReqId == null )
                {
                    waitForLoad(7000);
                    LOGGER.info("Creating Connection ");
                    ResultSet rs = sshConnection.executeQuery(query, connection);
                    for (int j = 0;rs.next();j++)
                    {
                        //LOGGER.info(" Next Result : " + rs.next() );
                        String N = "OE"+rs.getNString("pg_request_id");
                        LOGGER.info(" Fetch Result : " + N);
                        LOGGER.info("Value of J : " +j );

                        LOGGER.info("This is Result : " + QrPgReqId);
                        QrPgReqId = N;
                    }
                }
                else
                {
                    LOGGER.info("Got QR Merchant Pg Request Id : " +QrPgReqId);
                    sshConnection.closeConnections(connection);
                    connection.close();
                    break;
                }
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }

    }

    public void getRecentPgReqId(String CustId) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query = "SELECT DISTINCT pg_request_id from pg_callback_info where cust_id = " +CustId+ " ORDER BY created_at DESC LIMIT 1";
        LOGGER.info("This is generated Query : " + query);
        try {
            for (int i = 0; i <= 3; i++)
            {
                if (PgReqId == null)
                {
                    LOGGER.info("Creating Connection ");
                    waitForLoad(7000);
                    ResultSet rs = sshConnection.executeQuery(query, connection);
                    if (rs.next())
                    {
                        LOGGER.info("This is Result : " + rs.getString("pg_request_id"));
                        PgReqId = "OE"+(rs.getString("pg_request_id"));
                        sshConnection.closeConnections(connection);
                        connection.close();
                    }
                }
                else
                {
                    LOGGER.info("Got Pg Request Id : " +PgReqId);
                    sshConnection.closeConnections(connection);
                    connection.close();
                    break;
                }


            }
        }

            catch (Exception e)
                {
                e.printStackTrace();
                LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
                sshConnection.closeConnections(connection);
                connection.close();
            }

    }



    public void UpdateQuery(String Query) throws SQLException {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);

        try {
            LOGGER.info("Creating Connection ");
            LOGGER.info("Removing Foreign keys check ");
            int FK = sshConnection.executeUpdate("SET FOREIGN_KEY_CHECKS=0;",connection);
            LOGGER.info("Result of Foreign key query : " +FK);
            LOGGER.info("Foreign keys check Removed \n");

            LOGGER.info("This is generated Query : " + Query);
            int rs = sshConnection.executeUpdate(Query, connection);

                LOGGER.info("This is Result : " + rs);
                UpdateQueryResult = rs;
                sshConnection.closeConnections(connection);
                connection.close();

        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }
    }

    public String getResult(String Query, String ExpectedInput) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);

        LOGGER.info("This is generated Query : " + Query);
        LOGGER.info("Looking for : " +ExpectedInput);
        try {
            LOGGER.info("Creating Connection ");
            for (int i = 0;i < 6;i++)
            {
                LOGGER.info("Inside Query Execution Loop");
                waitForLoad(6000);
                ResultSet rs = sshConnection.executeQuery(Query, connection);
                if (!rs.next())
                {
                    LOGGER.info("Result is Empty : Retrying");
                }
                else
                {
                    LOGGER.info("This is Result : " + rs.getString(ExpectedInput));
                    ExpectedOutput =(rs.getString(ExpectedInput));
                    sshConnection.closeConnections(connection);
                    connection.close();
                    break;
                }

            }
            sshConnection.closeConnections(connection);
            connection.close();

        }catch (Exception e)
        {
            LOGGER.info("In Catch");
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }
        return ExpectedOutput;
    }


    /*@AfterSuite
    public void deleteDbData() {
        String ubmId = null;
        String query = "SET FOREIGN_KEY_CHECKS=0";


    }*/

    public String getUbmIdForUpgradeMerchantPlan(String number, String solutionType,String solutionTypeLevel2) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query = "select * from user_business_mapping where mobile_number = " + number +
                " and solution_type = '"+solutionType+"' and solution_type_level_2= '"+solutionTypeLevel2+"' order by created_at desc limit 1;";
        LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
            ResultSet rs = sshConnection.executeQuery(query, connection);
            if (rs.next()) {
                LOGGER.info("This is Result : " + rs.getString("id"));
                getUbmId =(rs.getString("id"));
                LOGGER.info("UBM id is : "+getUbmId);
                sshConnection.closeConnections(connection);
                connection.close();
            }
        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }
       return getUbmId;
    }

    public void assignAgentviaDB(String number, String solutionType,String solutionTypeLevel2) throws SQLException
    {
        TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        String ubmid=getUbmIdForUpgradeMerchantPlan(number,solutionType,solutionTypeLevel2);
        testBase.UpdateQuery("UPDATE agent_lead_allocation_mapper SET agent_id=1152 WHERE ubm_id= '" + ubmid + "' ;");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes);
    }

    public String executequeryForWorkFlowId(String ubmid,String WorkFlowNode) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query = "select * from workflow_status where user_business_mapping_id = " + ubmid + " and workflow_node_id=" + WorkFlowNode + " order by created_at desc limit 1;";
      //  String query = "select * from workflow_status where user_business_mapping_id = " + ubmid + " and workflow_node_id=181 order by created_at desc limit 1;";
        LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
            ResultSet rs = sshConnection.executeQuery(query, connection);
            if (rs.next()) {
                LOGGER.info("This is Result : " + rs.getString("id"));
                getWorkflowId =(rs.getString("id"));
                LOGGER.info("Workflow id is : "+getWorkflowId);
                sshConnection.closeConnections(connection);
                connection.close();
            }
        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }
        return getWorkflowId;
    }

    public void insertQueryInWorkflowStatus(String ubmid, String currentStage) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query="insert into workflow_status values(null," + ubmid + ",1,NULL,now(),now()," + currentStage + ",480);";
        LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
             sshConnection.executeUpdate(query, connection);
        }
        catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }

    }

    public void updateWorkflowStatus(String WorkFlowStatusId) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query="update workflow_status set is_active=0 where id=" + WorkFlowStatusId + ";";
        LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
            sshConnection.executeUpdate(query, connection);
        }
        catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }

    }

    public String ActiveWorkflowStatus(String ubmId) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query="select * from workflow_status where is_active=1 and user_business_mapping_id =" + ubmId + ";";
        LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
            ResultSet rs = sshConnection.executeQuery(query, connection);
            if (rs.next()) {
                LOGGER.info("This is Result : " + rs.getString("id"));
                getWorkflowStatusId =(rs.getString("id"));
                LOGGER.info("Workflow status id is : "+getWorkflowStatusId);
                sshConnection.closeConnections(connection);
                connection.close();
            }
        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }
        return getWorkflowStatusId;
    }

    public void insertJob(String ubmid, String jobName) throws SQLException
    {
        MakeConnection();
        LOGGER.info("Currently connected DB is  : " +DbName);
        String query="insert into job values(null," + ubmid + ",0," + jobName + ",null,now(),now(),now(),0,'ubm'," + ubmid + ",null);";
        LOGGER.info("This is generated Query : " + query);
        try {
            LOGGER.info("Creating Connection ");
            sshConnection.executeUpdate(query, connection);

        }
        catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Exception Caught at line no :" + e.getStackTrace()[0].getLineNumber());
            sshConnection.closeConnections(connection);
            connection.close();
        }

    }
}
