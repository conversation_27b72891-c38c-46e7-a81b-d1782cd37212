

package Services.Utilities;

import Request.oAuth.oAuthWormhole.CreateUser;
import Services.MechantService.MiddlewareServices;
import Services.oAuth.oAuthServices;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.paytm.apitools.core.P;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import io.restassured.specification.RequestSpecification;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import java.util.Random;
import java.util.regex.Pattern;

public class Utilities {
    private static final Logger LOGGER = LogManager.getLogger(Utilities.class);

    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    oAuthServices oAuthServicesObect = new oAuthServices();

    public String generateKycJwtToken(String mobile, String action, String time) {
        Algorithm buildAlgorithm = Algorithm.HMAC256("9a6c6434-9991-45c3-8f04-626335c2cc96");// MFI Satin

        //      System.out.println(timestamp);
        //MFI TOken status
//        String time = timestamp;
        String token = JWT.create().withIssuer("OE").withClaim("clientId", "IVR-KYC-CLIENT").withClaim("timestamp", time).withClaim("mobile", mobile).withClaim("action", action).sign(buildAlgorithm);
        //        System.out.println(token);
        return token;
    }

    public String randomMobileNumberGenerator() {
        String mobileNumber = "6" + randomNumberGenerator(9);
        return mobileNumber;
    }

    public String randomMobileNumberGeneratorStartWith(int StartsWith) {
        char FirstChar = String.valueOf(StartsWith).charAt(0);
        String mobileNumber = String.valueOf(FirstChar) + randomNumberGenerator(9);
        return mobileNumber;
    }

    public static Integer randomNumberGenerator(Integer numberOfDigits) {
        int minimum = (int) Math.pow(10, numberOfDigits - 1); // minimum value with 2 digits is 10 (10^1)
        int maximum = (int) Math.pow(10, numberOfDigits) - 1; // maximum value with 2 digits is 99 (10^2 - 1)
        Random random = new Random();
        return minimum + random.nextInt((maximum - minimum) + 1);
    }

    public String randomAlphaGenerator(Integer stringLength) {

        String AlphaNumericString = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder sb = new StringBuilder(stringLength);
        for (int i = 0; i < stringLength; i++) {
            int index = (int) (AlphaNumericString.length() * Math.random());
            sb.append(AlphaNumericString.charAt(index));
        }
        return sb.toString();
    }

    public String randomDLDocValueGenerator() {
        String randomDLDocValue = "DL" + randomAlphaGenerator(5) + randomNumberGenerator(8);
        return randomDLDocValue;
    }


    public String randomIndividualPANValueGenerator() {
        String randomIndividualPANValue = randomAlphaGenerator(3) + "P" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomIndividualPANValue;
    }

    public String randomPublicPANValueGenerator() {
        String randomPublicPANValueGenerator = randomAlphaGenerator(3) + "C" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomPublicPANValueGenerator;
    }

    public String randomTrustPANValueGenerator() {
        String randomTrustPANValueGenerator = randomAlphaGenerator(3) + "T" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomTrustPANValueGenerator;
    }

    public String randomSocietyPANValueGenerator() {
        String randomSocietyPANValueGenerator = randomAlphaGenerator(3) + "A" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomSocietyPANValueGenerator;
    }

    public String randomPartnershipPANValueGenerator() {
        String randomPartnershipPANValueGenerator = randomAlphaGenerator(3) + "F" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomPartnershipPANValueGenerator;
    }

    public String randomHUFPANValueGenerator() {
        String randomHUFPANValueGenerator = randomAlphaGenerator(3) + "H" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomHUFPANValueGenerator;
    }

    public String randomGovernmentPANValueGenerator() {
        String randomTrustPANValueGenerator = randomAlphaGenerator(3) + "G" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomTrustPANValueGenerator;
    }

    public String randomLocalAuthorityPANValueGenerator() {
        String randomTrustPANValueGenerator = randomAlphaGenerator(3) + "L" + randomAlphaGenerator(1) + randomNumberGenerator(4) + randomAlphaGenerator(1);
        return randomTrustPANValueGenerator;
    }

    public String getXMVToken(String username, String password) {
        Response responseObject = MiddlewareServicesObject.v1Token(username, password);
        String XMVToken = responseObject.getHeader("Set-Cookie").toString();
        //String XMWToken = responseObject.getCookie("X-MW-TOKEN").toString();
        LOGGER.info("XMVToken : " + XMVToken);
        return XMVToken;
    }

    public static long generateRandom(int length) {
        Random random = new Random();
        char[] digits = new char[length];
        digits[0] = (char) (random.nextInt(9) + '1');
        for (int i = 1; i < length; i++) {
            digits[i] = (char) (random.nextInt(10) + '0');
        }
        return Long.parseLong(new String(digits));
    }

    private static boolean validateAadharNumber(String aadharNumber) {
        Pattern aadharPattern = Pattern.compile("\\d{12}");
        boolean isValidAadhar = aadharPattern.matcher(aadharNumber).matches();
        if (isValidAadhar) {
            isValidAadhar = AadhaarAlgo.validateVerhoeff(aadharNumber);
        }
        return isValidAadhar;
    }

    public String ValidAadhaar() {
        boolean isValid = false;

        long Aadhaar;
        String AadhaarNo = "";
        for (int i = 1; i <= 100000; i++) {
            LOGGER.info("Inside Loop for Aadhaar and I : " + i);
            if (!isValid) {
                LOGGER.info("Inside If Condition of Aadhaar");
                Aadhaar = generateRandom(12);
                LOGGER.info("Aadhaar is : " + Aadhaar);
                AadhaarNo = Long.toString(Aadhaar);
                isValid = validateAadharNumber(AadhaarNo);
                LOGGER.info("Is aadhaar Valid : " + isValid);
            } else {
                LOGGER.info("Found Valid Aadhaar");
                break;
            }
        }
        return AadhaarNo;
    }


    public boolean createNewAuthUser(String mobile, String loginPassword) {
        CreateUser CreateUserRequestObject = new CreateUser();
        CreateUserRequestObject.setMobile(mobile);
        CreateUserRequestObject.setLoginPassword(loginPassword);
        System.out.println("CreateUserRequestObject");

        Response responseObject = oAuthServicesObect.oAuthoAuthWormholeCreateUser(CreateUserRequestObject);
        if (responseObject.getStatusCode() == 200) {
            System.out.println("New User Created !!!");
            return true;
        } else {
            System.out.println("New User Creation Failed");
            return false;
        }

    }


    public String randomPGRequestID() {
        String pgRequestID = "15" + randomNumberGenerator(15);
        return pgRequestID;
    }


    public static String randomLendingLoanAccountNumberGenerator() {
        String pgRequestID = "PLD" + randomNumberGenerator(9);
        return pgRequestID;
    }

    public static String randomEmailGeneration() {
        String pgRequestID = "AutomationTest" + randomNumberGenerator(5) + "@paytm.com";
        return pgRequestID;
    }

    public String randomIndividualGSTINGenerator(char character) {
        String individualGstin;
        if (character == 'Z' || character == 'C') {
            individualGstin = randomNumberGenerator(2) + randomIndividualPANValueGenerator() + randomAlphaGenerator(1) + character + randomAlphaGenerator(1);
            return individualGstin;
        } else
            return individualGstin = randomNumberGenerator(2) + randomIndividualPANValueGenerator() + randomAlphaGenerator(1) + "Z" + randomAlphaGenerator(1);

    }

    public static String generateJwtTokenForICICI() throws Exception {
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30"));
        String timeStamp = localDateTime.toString();
        Algorithm buildAlgorithm = Algorithm.HMAC256("e943ccc0-efc5-47e4-a701-d38fadcdf65b");
        String token = JWT.create()
                .withClaim("clientId", "ICICI")
                .withClaim("timestamp", timeStamp + "+05:30")
                .withClaim("additional", "9810618965")
                .withIssuer("OE")
                .sign(buildAlgorithm);
        return token;
    }

    public static String generateJwtTokenForMerchantCommonOnbaord(String CustId) throws Exception {
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30"));
        String timeStamp = localDateTime.toString();
        Algorithm buildAlgorithm = Algorithm.HMAC256("salesforce_secret");
        String token = JWT.create()
                .withClaim("clientId", "salesforce")
                .withClaim("timestamp", timeStamp + "+05:30")
                .withClaim("custId", CustId)
                .withIssuer("OE")
                .sign(buildAlgorithm);
        return token;
    }

    public static String generateRandomBankAccountNumber() {
        String pgRequestID = "" + randomNumberGenerator(10);
        return pgRequestID;
    }

    public static String generateRandomAadhaar() {
        Random random = new Random();
        StringBuilder aadhaar = new StringBuilder();

        // Generate 12 digits for the Aadhaar number
        for (int i = 0; i < 12; i++) {
            int digit = random.nextInt(10); // generates a digit between 0 and 9
            aadhaar.append(digit);
        }

        return aadhaar.toString();
    }

    public static String generateRandomPAN(String character) {
        String ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        Random random = new Random();
        StringBuilder pan = new StringBuilder();

        // Generate first 3 alphabetic characters
        for (int i = 0; i < 3; i++) {
            char letter = ALPHABET.charAt(random.nextInt(ALPHABET.length()));
            pan.append(letter);
        }

        // Set the 4th character to 'P' for individuals
        pan.append(character);

        // Generate the 5th alphabetic character
        char fifthLetter = ALPHABET.charAt(random.nextInt(ALPHABET.length()));
        pan.append(fifthLetter);

        // Generate 4 digits
        for (int i = 0; i < 4; i++) {
            int digit = random.nextInt(10); // generates a digit between 0 and 9
            pan.append(digit);
        }

        // Generate last alphabetic character
        char lastLetter = ALPHABET.charAt(random.nextInt(ALPHABET.length()));
        pan.append(lastLetter);

        return pan.toString();
    }

    public Response UploadDocInAPI(File uploadFile, Map<String, String> params, Map<String, String> header, String endPoint) throws Exception {

        Response resp = null;
        String baseURI = P.API.get("api_url");

        RequestSpecification spec;

        try {
            spec = new RequestSpecBuilder().setBaseUri(baseURI).build();
            resp = RestAssured.given().multiPart(uploadFile).spec(spec).relaxedHTTPSValidation().queryParams(params).headers(header)
                    .post(endPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resp;
    }


    
}