package Services.OMS;

import Request.OMS.AuthorizeOMS;
import Request.OMS.FetchOMSOrder;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

public class FetchOrderOMS extends BaseMethod
{
    public Response fetchPaymentOrderviaOMS(FetchOMSOrder FetchOMSOrderObj)
    {
        AuthorizeOMS authorizeOMSObj=new AuthorizeOMS();
        CheckoutAuthorizeOMS checkoutObj=new CheckoutAuthorizeOMS();
        String access_token= checkoutObj.CheckoutAuthorizeviaOMS(authorizeOMSObj).jsonPath().getJsonObject("access_token");
        FetchOMSOrderObj.addParameter("payment","1");
        FetchOMSOrderObj.addParameter("address","1");
        FetchOMSOrderObj.addParameter("metadata","1");

        FetchOMSOrderObj.setHeader("access_token",access_token);

        Response FetchOMSOrderObjResp = FetchOMSOrderObj.callAPI();
        return  FetchOMSOrderObjResp;
    }
}
