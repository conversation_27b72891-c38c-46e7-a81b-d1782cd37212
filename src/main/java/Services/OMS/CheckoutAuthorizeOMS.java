package Services.OMS;

import Request.OMS.AuthorizeOMS;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

public class CheckoutAuthorizeOMS extends BaseMethod
{
    public Response CheckoutAuthorizeviaOMS(AuthorizeOMS AuthorizeOMSObj)
    {
        AuthorizeOMSObj.setHeader("Content-Type","application/json");
        Response AuthorizeOMSObjResp = AuthorizeOMSObj.callAPI();
        //String access_token=AuthorizeOMSObjResp.jsonPath().getJsonObject("access_token").toString();
        return  AuthorizeOMSObjResp;
    }
}
