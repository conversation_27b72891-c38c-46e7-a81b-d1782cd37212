package Services.OMS;

import Request.OMS.AuthorizeOMS;
import Request.OMS.CreateQROMS;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

import java.util.Map;

public class CreateQR extends BaseMethod
{

    public Response createQRviaOMS(CreateQROMS CreateQROMSObj,String SessionToken, Map<String,String> body)
    {
        AuthorizeOMS authorizeOMSObj=new AuthorizeOMS();
        CheckoutAuthorizeOMS checkoutObj=new CheckoutAuthorizeOMS();
       String access_token= checkoutObj.CheckoutAuthorizeviaOMS(authorizeOMSObj).jsonPath().getJsonObject("access_token");
        for(Map.Entry m:body.entrySet())
        {
            CreateQROMSObj.getProperties().setProperty(m.getKey().toString(),m.getValue().toString());
        }
        CreateQROMSObj.addParameter("generate_qrcode","1");
        CreateQROMSObj.addParameter("client","web");
        CreateQROMSObj.addParameter("version","2");
        CreateQROMSObj.addParameter("site_id","2");
        CreateQROMSObj.addParameter("child_site_id","6");
        CreateQROMSObj.addParameter("feature_type","fastag");


        CreateQROMSObj.setHeader("sso_token",SessionToken);
        CreateQROMSObj.setHeader("access_token",access_token);
        CreateQROMSObj.setHeader("Content-Type","application/json");

        Response createQRResp = CreateQROMSObj.callAPI();
        return  createQRResp;
    }
}
