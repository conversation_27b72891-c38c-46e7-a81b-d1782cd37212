package Services.AddPanDIY;

import Request.PgProfileUpdate.AddPanDIY;
import io.restassured.response.Response;

import java.util.Map;

public class AddPanDiy {

    public Response addPanDIY(String requestPath, Map<String, String> headers, Map<String, String> params, Map<String, String> body) {
        AddPanDIY fetch = new AddPanDIY(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response addPanDIYIndividual = fetch.callAPI();
        return addPanDIYIndividual;
    }


}
