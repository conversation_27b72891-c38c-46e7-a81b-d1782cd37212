package Services.Billing;

import Request.Billing.BillingOnboard;
import Request.Billing.BillingReturn;
import Request.ODS.v1.OdsUserInfo;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class BillingMiddlewareServices extends BaseMethod{
    private static final Logger LOGGER = LogManager.getLogger(BillingMiddlewareServices.class);
    public Response billingonboardservices(BillingOnboard BillingOnboard, Map<String, String> body,
                                           Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            BillingOnboard.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            BillingOnboard.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response BillingOnboardResp = BillingOnboard.callAPI();
        return BillingOnboardResp;
    }
    public Response billingreturnservices(BillingReturn BillingReturn, Map<String, String> body,
                                          Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            BillingReturn.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            BillingReturn.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response BillingReturnResp = BillingReturn.callAPI();
        return BillingReturnResp;
    }
}
