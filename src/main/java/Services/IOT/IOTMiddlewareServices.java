package Services.IOT;


import Request.MerchantService.v1.IOT.*;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

public class IOTMiddlewareServices extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(IOTMiddlewareServices.class);

    public Response iotnotificationMethod( Map<String, String> headers, Map<String,Object>body) {
        IOTnotification iotobj= new IOTnotification();
        for (Map.Entry m : headers.entrySet()) {
            iotobj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : body.entrySet()) {
            iotobj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response iotobjresp  = iotobj.callAPI();
        return iotobjresp;
    }

    public Response IOTCreateLeadServices(CreateLeadIOTServiceFlow CreateLeadIOTServiceFlow, Map<String, String> body,
                                          Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            CreateLeadIOTServiceFlow .setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : body.entrySet())
        {
            CreateLeadIOTServiceFlow.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response CreateLeadIOTResponse = CreateLeadIOTServiceFlow.callAPI();
        return CreateLeadIOTResponse;
    }
}
