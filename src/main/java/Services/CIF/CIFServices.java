package Services.CIF;

import Request.CIF.EditBrandEMI;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class CIFServices extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(CIFServices.class);

    public Response BrandEMIEditServices(EditBrandEMI BrandEMIEdit, Map<String, String> body,
                                         Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            BrandEMIEdit.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            BrandEMIEdit.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response BrandEMIEditResp = BrandEMIEdit.callAPI();
        return BrandEMIEditResp;
    }
}
