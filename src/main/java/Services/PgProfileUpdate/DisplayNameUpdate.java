package Services.PgProfileUpdate;

import Request.DIYMCO.FetchCat;
import io.restassured.response.Response;

import java.util.Map;

public class DisplayNameUpdate {
    public Response displayNameUpdate(String requestPath, Map<String, String> headers, Map<String, String> params, Map<String, String> body) {
        Request.PgProfileUpdate.DisplayNameUpdate fetch = new Request.PgProfileUpdate.DisplayNameUpdate(requestPath);
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response response = fetch.callAPI();
        return response;


    }

    public Response fetchDocStatus(Map<String, String> headers, Map<String, String> params) {

        FetchCat fetch = new FetchCat();
        for (Map.Entry m : headers.entrySet()) {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : params.entrySet()) {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        Response fetchSubResponse = fetch.callAPI();
        return fetchSubResponse;

    }
}
