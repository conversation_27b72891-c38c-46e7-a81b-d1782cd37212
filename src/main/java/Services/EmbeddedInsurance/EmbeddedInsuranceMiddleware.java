package Services.EmbeddedInsurance;

import Request.EGS.SimDetails;
import Request.EmbeddedInsurance.MerchantDetailsAVS;
import Services.EGS.EGSMiddlewareServices;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class EmbeddedInsuranceMiddleware extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(EmbeddedInsuranceMiddleware.class);

    public Response AVSGetdDetails(MerchantDetailsAVS MerchantDetailsAVS, Map<String, String> queryParams,
                                   Map<String, String> headers) {

        for (Map.Entry m : queryParams.entrySet()) {
            MerchantDetailsAVS.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : headers.entrySet()) {
            MerchantDetailsAVS.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response MerchantDetailsAVSResp = MerchantDetailsAVS.callAPI();
        return MerchantDetailsAVSResp;
    }
}
