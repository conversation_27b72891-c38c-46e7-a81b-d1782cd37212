package Services.DIYEDC;

import Request.DIYEDC.CreateLead1;
import io.restassured.response.Response;

import java.util.Map;

public class Createlead1 {
    public Response CreateleadResponse(String RequestPathBody, Map<String, String> header, Map<String, String> params, Map<String, String> body) {

        CreateLead1 CL = new CreateLead1(RequestPathBody);

        for (Map.Entry m : header.entrySet()) {
            CL.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            CL.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            CL.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response LeadResponse = CL.callAPI();
        return LeadResponse;
    }
}

