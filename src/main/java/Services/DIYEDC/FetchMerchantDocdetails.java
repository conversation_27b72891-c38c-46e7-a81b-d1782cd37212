package Services.DIYEDC;


import Request.DIYEDC.CreateLead1;
import Request.DIYEDC.FetchMerchantDocDetails;
import io.restassured.response.Response;

import java.util.Map;

public class FetchMerchantDocdetails {
    public Response FetchMerchantDocdetailsResponse( Map<String, String> header, Map<String, String> params) {

        FetchMerchantDocDetails CL = new FetchMerchantDocDetails();

        for (Map.Entry m : header.entrySet()) {
            CL.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            CL.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response LeadResponse = CL.callAPI();
        return LeadResponse;
    }

    public Response FetchMerchantDocdetailsResponse(String RequestPathBody, Map<String, String> header, Map<String, String> params) {

        FetchMerchantDocDetails CL = new FetchMerchantDocDetails(RequestPathBody);

        for (Map.Entry m : header.entrySet()) {
            CL.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            CL.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response LeadResponse = CL.callAPI();
        return LeadResponse;
    }
}
