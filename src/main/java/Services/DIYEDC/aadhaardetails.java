package Services.DIYEDC;

import Request.DIYEDC.AadhaarDetails;
import io.restassured.response.Response;

import java.util.Map;

public class aadhaardetails {
    public Response aadhaardetails(String RequestPathBody, Map<String, String> header, Map<String, String> params,Map<String, String> body) {

        AadhaarDetails AD = new AadhaarDetails(RequestPathBody);

        for (Map.Entry m : header.entrySet()) {
            AD.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            AD.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : body.entrySet()) {
            AD.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }
        Response LeadResponse = AD.callAPI();
        return LeadResponse;
    }
}

