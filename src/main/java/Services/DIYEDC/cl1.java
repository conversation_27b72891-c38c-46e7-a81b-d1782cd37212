package Services.DIYEDC;

import Request.DIYEDC.CL1;
import io.restassured.response.Response;

import java.util.Map;

public class cl1 {
    public Response cl1Response(String RequestPathBody, Map<String, String> header, Map<String, String> params) {

        CL1 CL = new CL1(RequestPathBody);

        for (Map.Entry m : header.entrySet()) {
            CL.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            CL.addParameter(m.getKey().toString(), m.getValue().toString());
        }
        Response LeadResponse = CL.callAPI();
        return LeadResponse;
    }
}

