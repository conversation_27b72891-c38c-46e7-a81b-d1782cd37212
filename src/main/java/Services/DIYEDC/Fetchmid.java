package Services.DIYEDC;

import Request.DIYEDC.FetchMerchantDocDetails;
import Request.DIYEDC.FetchMid;
import io.restassured.response.Response;

import java.util.Map;

public class Fetchmid {
    public Response FetchmidResponse(Map<String, String> header, Map<String, String> params) {

        FetchMid FM = new FetchMid();

        for (Map.Entry m : header.entrySet()) {
            FM.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for (Map.Entry m : params.entrySet()) {
            FM.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response LeadResponse = FM.callAPI();
        return LeadResponse;
    }
}
