package Services.InstrumentsEnableDisable;

import Request.IntrumentEnableDisable.InstrumentEnableDisable;
import Request.StoreCashBack.CreateStoreCashBack;
import io.restassured.response.Response;

import java.util.Map;


public class InstrumentEnableDisableServices {

//    public Response InstrumentEnableDisableServices(String requestBodyPath, Map<String,String> headers, Map<String,String> body, Map<String,String> params)
//    {
//
//        InstrumentEnableDisable fetch = new InstrumentEnableDisable(requestBodyPath);
//        for(Map.Entry m : headers.entrySet())
//        {
//            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
//        }
//        for(Map.Entry m : body.entrySet())
//        {
//            fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
//        }
//
//        for(Map.Entry m : params.entrySet())
//        {
//            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
//        }
//
//
//        Response resp=fetch.callAPI();
//        return resp;
//
//
//    }

    public Response InstrumentEnableDisableServices(Map<String,String> headers, Map<String,String> body, Map<String,String> params)
    {

        InstrumentEnableDisable fetch = new InstrumentEnableDisable();
        for(Map.Entry m : headers.entrySet())
        {
            fetch.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            fetch.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        for(Map.Entry m : params.entrySet())
        {
            fetch.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response resp=fetch.callAPI();
        return resp;


    }
}
