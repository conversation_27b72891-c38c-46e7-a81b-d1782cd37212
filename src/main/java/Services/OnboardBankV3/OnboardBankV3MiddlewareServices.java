package Services.OnboardBankV3;

import Request.Billing.BillingOnboard;
import Request.OnboardBankV3.OnboardBankV3;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

public class OnboardBankV3MiddlewareServices extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(OnboardBankV3MiddlewareServices.class);

    public Response onboardBankV3MiddlewareServices(OnboardBankV3 OnboardBankV3, Map<String, String> body,
                                                    Map<String, String> headers) {
        for (Map.Entry m : headers.entrySet()) {
            OnboardBankV3.setHeader(m.getKey().toString(), m.getValue().toString());
        }
        for(Map.Entry m : body.entrySet())
        {
            OnboardBankV3.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        // Hit API to get Response
        Response OnboardBankV3Resp = OnboardBankV3.callAPI();
        return OnboardBankV3Resp;
    }
}
