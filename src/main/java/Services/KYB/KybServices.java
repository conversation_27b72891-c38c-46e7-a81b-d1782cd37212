package Services.KYB;

import Request.CIF.EditAddressInKyb;
import Request.CIF.KybGet;
import Request.KYB.GetAddressInKyb;
import Request.KYB.GetCompany;
import Request.KYB.KybEdit;
import Request.KYB.KybSave;
import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

import java.util.Map;

public class KybServices extends BaseMethod {

    public Response KybSave(KybSave kybSave, Map<String,String> Query, Map<String,String> body) {

        kybSave.setHeader("Content-Type","application/json");
        kybSave.setHeader("x-jwt-token","Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE1MTgwMDA4MTAwMDAiLCJjdXN0X2lkIjoiMTEwNzE5OTQwNyIsImNsaWVudF9pZCI6InRlc3RjbGllbnQifQ.J69gTrP1Ct6-C0rBuBUKl0Gsvo/4lDXBojYOzwhJN2o");
        //Adding Query Params
        for (Map.Entry m : Query.entrySet()) {
            kybSave.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding body
        for (Map.Entry m : body.entrySet()) {
            kybSave.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response KybSaveResp = kybSave.callAPI();
        return KybSaveResp;
    }

    public Response KybEdit(KybEdit kybEdit, Map<String,String> Query, Map<String,String> body) {

        kybEdit.setHeader("Content-Type","application/json");
        kybEdit.setHeader("x-jwt-token","Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE1MTgwMDA4MTAwMDAiLCJjdXN0X2lkIjoiMTEwNzE5OTQwNyIsImNsaWVudF9pZCI6InRlc3RjbGllbnQifQ.J69gTrP1Ct6-C0rBuBUKl0Gsvo/4lDXBojYOzwhJN2o");
        //Adding Query Params
        for (Map.Entry m : Query.entrySet()) {
            kybEdit.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        //Adding body
        for (Map.Entry m : body.entrySet()) {
            kybEdit.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response KybSaveResp = kybEdit.callAPI();
        return KybSaveResp;
    }

    public Response KybEditAddress(EditAddressInKyb KybEditAddressObj, Map<String,String> headers, Map<String,String> body) {

        //Adding Headers
        for(Map.Entry m : headers.entrySet())
        {
            KybEditAddressObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Adding body
        for (Map.Entry m : body.entrySet()) {
            KybEditAddressObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response KybEKybEditAddressResp = KybEditAddressObj.callAPI();
        return KybEKybEditAddressResp;
    }

    public Response KybGetAddress(GetAddressInKyb GetAddressInKybObj, Map<String,String> headers, Map<String,String> params) {

        //Adding Headers
        for(Map.Entry m : headers.entrySet())
        {
            GetAddressInKybObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        //Adding params
        for(Map.Entry m : params.entrySet())
        {
            GetAddressInKybObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response GetAddressInKybObjResp = GetAddressInKybObj.callAPI();
        return GetAddressInKybObjResp;
    }

    public Response KybGet(KybGet KybGetObj, Map<String,String> headers)
    {

        //Adding Headers
        for(Map.Entry m : headers.entrySet())
        {
            KybGetObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        
        Response KybGetObjResp = KybGetObj.callAPI();
        return KybGetObjResp;
    }
    public Response KybGetCompany(GetCompany GetCompanyObj, Map<String,String> headers)
    {

        //Adding Headers
        for(Map.Entry m : headers.entrySet())
        {
            GetCompanyObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }


        Response GetCompanyObjResp = GetCompanyObj.callAPI();
        return GetCompanyObjResp;
    }



}
