package Services.DBConnection;

import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.paytm.apitools.core.P;
import org.yaml.snakeyaml.Yaml;

import java.math.BigInteger;
import java.sql.*;

public class DBConnection extends BaseMethod {

    private static final String SSH_USER_NEW = P.CONFIG.get("sshUser2");
    private static final String SSH_HOST_NEW = P.CONFIG.get("sshHost");
    private static final int DB_PORT = Integer.parseInt(P.CONFIG.get("remotePort"));
    private static final String DB_HOST = P.CONFIG.get("remoteHost");
    private static final String DB_NAME = P.CONFIG.get("dbName");

    private static final String DB_USER = P.CONFIG.get("dbUserName");
    private static final String DB_PASSWORD = P.CONFIG.get("dbPassword");
    private static final String SSH_PRIVATE_KEY_PATH = System.getProperty("user.dir") + "/src/main/resources/id_rsa_jyoti";

    private static final int SSH_PORT = 22;

    private static final Yaml yaml = new Yaml();

    public static Connection establishConnection() throws Exception {
        JSch jsch = new JSch();
        // jsch.addIdentity("/Users/<USER>/Documents/id_rsa_jyoti");
        jsch.addIdentity(SSH_PRIVATE_KEY_PATH);
        Session session = jsch.getSession(SSH_USER_NEW, SSH_HOST_NEW, SSH_PORT);
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        int assigned_port = session.setPortForwardingL(0, DB_HOST, DB_PORT);

        Class.forName("com.mysql.cj.jdbc.Driver");
        return DriverManager.getConnection("**********************:" + assigned_port + "/" + DB_NAME, DB_USER, DB_PASSWORD);
    }

    public static void UpdateQueryToCloseLead(String mobileNo, String solution_type) throws Exception {
        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
        statement.execute("use " + DB_NAME);

        try (
                PreparedStatement statement1 = connection.prepareStatement("UPDATE user_business_mapping SET status=2 WHERE mobile_number= ? and status = '0' and solution_type= ?")
        ) {
            statement1.setString(1, mobileNo);
            statement1.setString(2, solution_type);
            int rowsAffected = statement1.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println("Update successful. Affected rows: " + rowsAffected);
            } else {
                System.out.println("Update unsuccessful. No rows were affected.");
            }

        }


    }

    public static void UpdateQueryToCloseLeadsolnlevel2(String mobileNo, String solution_type, String solution_type2) throws Exception {
        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
        statement.execute("use " + DB_NAME);

        try (
                PreparedStatement statement1 = connection.prepareStatement("UPDATE user_business_mapping SET status=2 WHERE mobile_number= ? and status = '0' and solution_type= ? and solution_type_level_2= ?")
        ) {
            statement1.setString(1, mobileNo);
            statement1.setString(2, solution_type);
            statement1.setString(3, solution_type2);

            int rowsAffected = statement1.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println("Update successful. Affected rows: " + rowsAffected);
            } else {
                System.out.println("Update unsuccessful. No rows were affected.");
            }

        }


    }

    public static String getPgRequestId(String leadId) throws Exception {
        Connection connection = establishConnection();
        String pgRequestId = null;

        String query = "SELECT sai.solution_key, sai.solution_value FROM solution_additional_info sai WHERE sai.solution_id = ( SELECT rbsm.solution_id FROM related_business_solution_mapping rbsm WHERE rbsm.id = ( SELECT ubm.related_business_solution_mapping_id FROM user_business_mapping ubm WHERE ubm.lead_id = ?))";

        try (PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setString(1, leadId);

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    String solutionKey = resultSet.getString("solution_key");
                    if ("PG_REQUEST_ID".equals(solutionKey)) {
                        pgRequestId = resultSet.getString("solution_value");
                        break;
                    }
                }
            }
        } finally {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        }

        return pgRequestId;
    }

    public static int getUserBusinessMappingId(String number, String solutionType, String solutionTypeLevel2) throws Exception {
        Connection connection = establishConnection();
        int id = -1;

        try (PreparedStatement statement = connection.prepareStatement("SELECT id FROM user_business_mapping WHERE mobile_number = ? AND solution_type = ? AND solution_type_level_2 = ? ORDER BY created_at DESC LIMIT 1")) {

            statement.setString(1, number);
            statement.setString(2, solutionType);
            statement.setString(3, solutionTypeLevel2);

            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {

                    id = resultSet.getInt("id");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } finally {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        }

        return id;
    }

    public static boolean assignAgentViaDB(String agentId, int ubmId) throws Exception {
        Connection connection = establishConnection();
        boolean isUpdated = false;

        String query = "UPDATE agent_lead_allocation_mapper SET agent_id = ? WHERE ubm_id = ?";

        try (PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setString(1, agentId);
            statement.setInt(2, ubmId);

            int rowsAffected = statement.executeUpdate();
            isUpdated = rowsAffected > 0;

            if (isUpdated) {
                System.out.println("Update successful. Rows affected: " + rowsAffected);
            } else {
                System.out.println("Update unsuccessful. No rows were affected.");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } finally {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        }

        return isUpdated;
    }


    public static String SELECTQUERYJOIN(String leadId, String pgRequestId) throws Exception {
        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
        statement.execute("use " + DB_NAME);

        try (
//                PreparedStatement statement1 = connection.prepareStatement("UPDATE user_business_mapping SET status=2 WHERE mobile_number= ? and status = '0' and solution_type= ?")
                PreparedStatement statement1 = connection.prepareStatement("SELECT sai.solution_value from user_business_mapping ubm join related_business_solution_mapping rbsm join solution_additional_info sai on rbsm.id = ubm.related_business_solution_mapping_id and sai.solution_id = rbsm.solution_id where ubm.lead_id= ? and sai.solution_key= ?")
        ) {
            statement1.setString(1, leadId);
            statement1.setString(2, pgRequestId);
            return statement1.executeQuery().toString();
//            int rowsAffected = statement1.executeUpdate();
//            if (rowsAffected > 0) {
//                System.out.println("Update successful. Affected rows: " + rowsAffected);
//            } else {
//                System.out.println("Update unsuccessful. No rows were affected.");
//            }

        }


    }


    public static long getWorkflowStatusID(int ubmId) throws Exception {
        Connection connection = establishConnection();
        long id = -1;

        String query = "select * from workflow_status where is_active=1 and user_business_mapping_id =?";

        try (PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, ubmId);

            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    id = resultSet.getLong("id");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } finally {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        }

        return id;
    }

    public static int getUserBusinessMappingId(String number, String solutionType) throws Exception {
        Connection connection = establishConnection();
        int id = -1;

        try (PreparedStatement statement = connection.prepareStatement("SELECT id FROM user_business_mapping WHERE mobile_number = ? AND solution_type = ? ORDER BY created_at DESC LIMIT 1")) {

            statement.setString(1, number);
            statement.setString(2, solutionType);

            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {

                    id = resultSet.getInt("id");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } finally {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        }

        return id;
    }

    public static void UpdateQueryToCloseLeadsolnlevel3(String mobileNo, String solution_type, String solution_type3) throws Exception {
        Connection connection = establishConnection();
        Statement statement = connection.createStatement();
        statement.execute("use " + DB_NAME);

        try (
                PreparedStatement statement1 = connection.prepareStatement("UPDATE user_business_mapping SET status=2 WHERE mobile_number= ? and status = '0' and solution_type= ? and solution_type_level_3= ?")
        ) {
            statement1.setString(1, mobileNo);
            statement1.setString(2, solution_type);
            statement1.setString(3, solution_type3);

            int rowsAffected = statement1.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println("Update successful. Affected rows: " + rowsAffected);
            } else {
                System.out.println("Update unsuccessful. No rows were affected.");
            }

        }


    }


    }