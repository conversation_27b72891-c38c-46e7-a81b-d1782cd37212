package Request.CIF;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class TncAccept extends AbstractApiV2
{
    public TncAccept(String Reqpath)
    {
        super(Reqpath, "CIF/BrandActiveInKyb/TncAcceptInKyb/TncAcceptInKybResponse.json", "CIF/BrandActiveInKyb/TncAcceptInKyb/TncAcceptInKybProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("cif_url"));
    }
}
