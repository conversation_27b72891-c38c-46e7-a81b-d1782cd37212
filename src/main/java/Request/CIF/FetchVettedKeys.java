package Request.CIF;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchVettedKeys extends AbstractApiV2
{
    public FetchVettedKeys()
    {
        super("CIF/BrandActiveInKyb/BrandActiveInKybRequest.json", "CIF/BrandActiveInKyb/BrandActiveInKybResponse.json", "CIF/BrandActiveInKyb/BrandActiveInKybProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("cif_url"));

    }
}
