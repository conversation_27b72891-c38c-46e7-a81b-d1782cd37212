package Request.CIF;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EditAddressInKyb extends AbstractApiV2
{
    public EditAddressInKyb(String ReqBody)

    {
        super(ReqBody, "CIF/BrandActiveInKyb/EditAddressInKyb/EditAddressInKybResponse.json", "CIF/BrandActiveInKyb/EditAddressInKyb/EditAddressInKybProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("cif_url"));

    }
}
