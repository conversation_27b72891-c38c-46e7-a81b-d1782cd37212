package Request.ODS.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class OdsNumberofRecords extends AbstractApiV2 {
    public OdsNumberofRecords()
    {
        super("ODS/V1/OdsNumberofRecordsRequest.json","ODS/V1/OdsNumberofRecordsResponse.json","ODS/V1/OdsNumberofRecordsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));
    }
}
