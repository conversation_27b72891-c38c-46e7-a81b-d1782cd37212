package Request.ODS;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchActiveQuestionAnswers extends AbstractApiV2 {
    public FetchActiveQuestionAnswers(String ReqPath) {
        super(ReqPath,"ODS/FetchActiveQuestionAnswers/FetchActiveQuestionAnswersResponse.json", "ODS/FetchActiveQuestionAnswers/FetchActiveQuestionAnswersProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));
    }
}