package Request.ODS;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchApplicableFiltersAPI extends AbstractApiV2 {

    public FetchApplicableFiltersAPI(String ReqPath){
        super(ReqPath,"ODS/FetchApplicableFiltersAPI/FetchApplicableFiltersAPIResponse.json","ODS/FetchApplicableFiltersAPI/FetchApplicableFiltersAPIProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));

    }

}
