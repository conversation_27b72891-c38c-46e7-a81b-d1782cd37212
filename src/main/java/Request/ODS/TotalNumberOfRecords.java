package Request.ODS;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class TotalNumberOfRecords extends AbstractApiV2 {
    public TotalNumberOfRecords(String ReqPath) {
        super(ReqPath, "ODS/TotalNumberOfRecords/TotalNumberOfRecordsResponse.json", "ODS/TotalNumberOfRecords/TotalNumberOfRecordsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));
    }
}
