package Request.ODS;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchFilterValuesAPI extends AbstractApiV2 {

    public FetchFilterValuesAPI(String ReqPath){
        super(ReqPath,"ODS/FetchFilterValuesAPI/FetchFilterValuesAPIResponse.json","ODS/FetchFilterValuesAPI/FetchFilterValuesAPIProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));

    }
}
