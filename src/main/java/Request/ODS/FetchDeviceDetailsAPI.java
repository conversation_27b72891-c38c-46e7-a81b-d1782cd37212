package Request.ODS;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchDeviceDetailsAPI extends AbstractApiV2 {
    public FetchDeviceDetailsAPI(String ReqPath) {
        super(ReqPath,"ODS/FetchDeviceDetails/FetchDeviceDetailsAPIProperties.properties", "ODS/FetchDeviceDetails/FetchDeviceDetailsAPIResponse.json");
        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));
    }
}