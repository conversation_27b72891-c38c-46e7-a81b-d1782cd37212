package Request.ODS;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class PlanDifferenceAPI extends AbstractApiV2 {
    public PlanDifferenceAPI(String ReqPath) {
        super(ReqPath, "ODS/PlanDifference/PlanDifferenceResponse.json", "ODS/PlanDifference/PlanDifferenceProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));
    }
}