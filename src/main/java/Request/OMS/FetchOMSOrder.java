package Request.OMS;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchOMSOrder extends AbstractApiV2
{
    public FetchOMSOrder(String orderId)
    {
        super("OMS/FetchOMSOrderRequest.json", "OMS/FetchOMSOrderResponse.json", "OMS/FetchOMSOrderProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("oms_url"));
        replaceUrlPlaceholder("orderId", orderId);




    }
}
