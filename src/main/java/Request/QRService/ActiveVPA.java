package Request.QRService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ActiveVPA extends AbstractApiV2
{
    public ActiveVPA()
    {
        super("QRService/ActivateVPARequest.json","QRService/GenerateVPAResponse.json", "QRService/GenerateVPAProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("qr_integration_url"));

    }
}
