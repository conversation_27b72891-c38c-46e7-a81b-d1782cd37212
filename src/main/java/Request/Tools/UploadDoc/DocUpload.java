package Request.Tools.UploadDoc;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DocUpload  extends AbstractApiV2 {

	  public DocUpload () {
		  
	        super("MerchantService/v2/lending/lead/document/UploadDocumentRequest.json","MerchantService/v2/lending/lead/document/UploadDocumentResponse.json","MerchantService/v2/lending/lead/document/UploadDocumentProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("tools_base_url"));
	    }
	}