package Request.UpdateDevideAddress;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadUpdateDeviceAddress extends AbstractApiV2 {

    public CreateLeadUpdateDeviceAddress(String ReqPath)
    {
        super(ReqPath,"UpdateDeviceAddress/CreateLeadUpdateDeviceAddressResponse.json","UpdateDeviceAddress/CreateLeadUpdateDeviceAddressProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_url"));
    }
}
