package Request.PG;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetPreference extends AbstractApiV2
{
    public GetPreference(String mid)
    {
        super("PG/GetMID/MIDRequest.json", "PG/GetMID/MIDResponse.json", "PG/GetMID/MID.properties");

        replaceUrlPlaceholder("base_url", P.API.get("bossPG_url"));
        replaceUrlPlaceholder("mid", mid);

    }
}
