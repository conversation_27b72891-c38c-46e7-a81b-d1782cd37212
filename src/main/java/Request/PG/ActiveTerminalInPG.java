package Request.PG;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ActiveTerminalInPG extends AbstractApiV2
{
    public ActiveTerminalInPG(String ReqPath)
    {
        super(ReqPath,"PG.CreateMID/ActiveTerminalONPGResponse.json", "PG.CreateMID/ActiveTerminalONPGProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("bossPG_url"));

    }
}
