package Request.PG;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ReplaceTerminalInPG extends AbstractApiV2
{
    public ReplaceTerminalInPG(String ReqPath)
    {
        super(ReqPath,"PG.CreateMID/ReplaceTerminalONPGResponse.json", "PG.CreateMID/ReplaceTerminalONPGProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("bossPG_url"));

    }
}
