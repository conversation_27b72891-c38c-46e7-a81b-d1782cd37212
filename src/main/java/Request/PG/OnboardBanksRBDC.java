package Request.PG;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class OnboardBanksRBDC extends AbstractApiV2
{
    public OnboardBanksRBDC(String ReqPath)
    {
        super(ReqPath,"PG.CreateMID/OnboardBanksRBDCResponse.json", "PG.CreateMID/OnboardBanksRBDCProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("bossPG_url"));

    }
}
