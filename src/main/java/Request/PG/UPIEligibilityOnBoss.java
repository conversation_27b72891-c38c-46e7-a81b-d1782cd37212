package Request.PG;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UPIEligibilityOnBoss extends AbstractApiV2
{
    public UPIEligibilityOnBoss()
    {
        super("PG.CreateMID/UPIEligibilityOnBossRequest.json","PG.CreateMID/UPIEligibilityOnBossResponse.json", "PG.CreateMID/UPIEligibilityOnBossProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("bossPG_url"));

    }
}
