package Request.PG;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ApplyTieredMdr extends AbstractApiV2
{
    public ApplyTieredMdr(String mid)
    {
        super("PG.CreateMID/ApplyTieredMdrOnPGRequest.json", "PG/GetMID/MIDResponse.json", "PG/GetMID/MID.properties");

        replaceUrlPlaceholder("base_url", P.API.get("bossPG_url"));
        replaceUrlPlaceholder("mid", mid);

    }
}
