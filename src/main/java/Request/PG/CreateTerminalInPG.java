package Request.PG;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateTerminalInPG extends AbstractApiV2
{
    public CreateTerminalInPG(String ReqPath)
    {
        super(ReqPath,"PG.CreateMID/CreateTerminalONPGResponse.json", "PG.CreateMID/CreateTerminalONPGProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("bossPG_url"));

    }
}
