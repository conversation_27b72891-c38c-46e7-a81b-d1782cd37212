package Request.DIYMerchantLimitUpgrade;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchMerchantDocDetail extends AbstractApiV2 {


    public FetchMerchantDocDetail(String requestPath) {
        super(requestPath, "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetailResponse", "MerchantService/v1/sdMerchant/lead/fetchMerchantDocDetailProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }

}
