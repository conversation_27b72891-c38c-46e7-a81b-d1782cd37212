package Request.DIYMerchantLimitUpgrade;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchScreenDetailsLimitUpgrade extends AbstractApiV2 {
    public FetchScreenDetailsLimitUpgrade(String requestPath) {
        super(requestPath, "MerchantService/v1/sdMerchant/lead/fetchScreenDetailsUpgradeLimitResponse.json", "MerchantService/v1/sdMerchant/lead/fetchScreenDetailsUpgradeLimitProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
