package Request.DIYMerchantLimitUpgrade;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdatePanManuallyUpgradeLimit extends AbstractApiV2 {

    public UpdatePanManuallyUpgradeLimit(String requestPath) {
        super(requestPath, "MerchantService/v1/sdMerchant/lead/updatePanLeadMerchantUpgradeLimitResponse.json", "MerchantService/v1/sdMerchant/lead/updatePanLeadMerchantUpgradeLimitProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
