package Request.DIYMerchantLimitUpgrade;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class CreateLeadMerchantLimitUpgrade extends AbstractApiV2 {
    public CreateLeadMerchantLimitUpgrade(String requestPath) {
        super(requestPath, "MerchantService/V1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeResponse.json", "MerchantService/V1/sdMerchant/lead/CreateLeadMerchantLimitUpgradeProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}