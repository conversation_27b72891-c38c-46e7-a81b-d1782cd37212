package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBValidateNewChargerInBindFlow extends AbstractApiV2 {

	public SBValidateNewChargerInBindFlow(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBValidateNewChargerInBindFlow/SBValidateNewChargerInBindFlowRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBValidateNewChargerInBindFlow/SBValidateNewChargerInBindFlowResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBValidateNewChargerInBindFlow/SBValidateNewChargerInBindFlowProperties.properties");
        replaceUrlPlaceholder("base_url",  P.API.get("goldengate_api_url"));
    }
}