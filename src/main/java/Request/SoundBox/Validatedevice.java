package Request.SoundBox;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class Validatedevice extends AbstractApiV2{
    public Validatedevice(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/Validatedevice/ValidatedeviceRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/Validatedevice/ValidatedeviceResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/Validatedevice/ValidatedeviceProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}