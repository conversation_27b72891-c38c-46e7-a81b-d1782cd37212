package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchPOSIdSB extends AbstractApiV2{
	public FetchPOSIdSB(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchPOSIdSB/FetchPOSIdSBRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchPOSIdSB/FetchPOSIdSBResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchPOSIdSB/FetchPOSIdSBProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));}

}
