package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DIYSBQRUpdateAWBNumber extends AbstractApiV2{

	public DIYSBQRUpdateAWBNumber(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYSBQRUpdateAWBNumber/DIYSBQRUpdateAWBNumberRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYSBQRUpdateAWBNumber/DIYSBQRUpdateAWBNumberResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYSBQRUpdateAWBNumber/DIYSBQRUpdateAWBNumberRProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
