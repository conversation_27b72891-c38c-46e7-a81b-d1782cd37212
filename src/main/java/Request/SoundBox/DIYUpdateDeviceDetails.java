package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DIYUpdateDeviceDetails extends AbstractApiV2{
	 
	public DIYUpdateDeviceDetails(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYUpdateDeviceDetails/DIYUpdateDeviceDetailsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYUpdateDeviceDetails/DIYUpdateDeviceDetailsResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYUpdateDeviceDetails/DIYUpdateDeviceDetailsProperties.properties");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
	    }
	
}
