package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SbFulfilmentBypass extends AbstractApiV2 {

	public SbFulfilmentBypass(){
		super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SbFulfilmentBypass/SbFulfilmentBypassRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SbFulfilmentBypass/SbFulfilmentBypassResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SbFulfilmentBypass/SbFulfilmentBypassProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }

}