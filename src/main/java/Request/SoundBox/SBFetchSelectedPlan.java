package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBFetchSelectedPlan extends AbstractApiV2 {

    public SBFetchSelectedPlan(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBFetchSelectedPlan/SBFetchSelectedPlanRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBFetchSelectedPlan/SBFetchSelectedPlanResponse.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBFetchSelectedPlan/SBFetchSelectedPlanproperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }

}