package Request.SoundBox;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBFetchLeadDetails extends AbstractApiV2 {
    public SBFetchLeadDetails(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchLeadDetails/FetchLeadDetailsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchLeadDetails/FetchLeadDetailsResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchLeadDetails/FetchLeadDetailsProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}