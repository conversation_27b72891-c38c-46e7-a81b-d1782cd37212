package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchNextScreenSB extends AbstractApiV2{

	 public FetchNextScreenSB(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchNextScreenSB/FetchNextScreenSBRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchNextScreenSB/FetchNextScreenSBResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchNextScreenSB/FetchNextScreenSBProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	    }
}
