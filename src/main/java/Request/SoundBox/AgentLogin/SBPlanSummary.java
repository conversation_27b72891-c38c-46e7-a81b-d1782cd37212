package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBPlanSummary extends AbstractApiV2 {
    public SBPlanSummary(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPlanSummary/SBPlanSummaryRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPlanSummary/SBPlanSummaryResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPlanSummary/SBPlanSummaryProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}