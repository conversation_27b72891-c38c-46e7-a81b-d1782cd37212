package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DeviceIdentifier extends AbstractApiV2 {
    public DeviceIdentifier(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/AgentLogin/AgentLoginApi1.Request","MerchantService/V1/SoundBox/SoundBoxCreateLead/AgentLogin/AgentLoginApi1.Response","MerchantService/V1/SoundBox/SoundBoxCreateLead/AgentLogin/AgentLoginApi1.properties");
        replaceUrlPlaceholder("base_url", P.API.get("oAuth_api_url"));
    }
}