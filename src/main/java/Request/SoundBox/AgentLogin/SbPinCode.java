package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SbPinCode extends AbstractApiV2 {
    public SbPinCode(String Pincode){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SbPinCode/SbPinCodeRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SbPinCode/SbPinCodeResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/SbPinCode/SbPinCodeProperties.properties");
        replaceUrlPlaceholder( "base_url" , P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("pincode" , Pincode);
    }
}