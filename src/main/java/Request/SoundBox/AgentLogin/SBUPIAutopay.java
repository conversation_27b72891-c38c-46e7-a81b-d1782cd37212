package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBUPIAutopay extends AbstractApiV2 {

    public SBUPIAutopay()
    {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBUPIAutopay/SBUPIAutopayRequest.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/SBUPIAutopay/SBUPIAutopayResponse.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBUPIAutopay/SBUPIAutopayProperties.Properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }

}