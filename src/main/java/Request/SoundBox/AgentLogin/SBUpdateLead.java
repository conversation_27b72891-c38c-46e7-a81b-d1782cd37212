package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBUpdateLead extends AbstractApiV2 {
    public SBUpdateLead(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBUpdateLead/SBUpdateLeadRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBUpdateLead/SBUpdateLeadResponse.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBUpdateLead/SBUpdateLeadProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }

}