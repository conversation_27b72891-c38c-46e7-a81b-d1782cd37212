package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Save_Tnc extends AbstractApiV2 {
    public Save_Tnc(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/Save_Tnc/Save_TncRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/Save_Tnc/Save_TncResponse.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/Save_Tnc/Save_TncProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}