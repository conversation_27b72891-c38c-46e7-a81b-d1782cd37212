package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBPayment extends AbstractApiV2 {
    public SBPayment(){

        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchSoundBoxPayment/FetchSoundBoxPaymentRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPayment/SBPaymentResponse.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPayment/SBPaymentProperties.Properties");


        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }
}