package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AgentLoginApi2 extends AbstractApiV2 {
    public AgentLoginApi2(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/AgentLogin/AgentLoginApi2Request.Request","MerchantService/V1/SoundBox/SoundBoxCreateLead/AgentLogin/AgentLoginApi2Response.Response","MerchantService/V1/SoundBox/SoundBoxCreateLead/AgentLogin/AgentLoginApi2Properties.Properties");
        replaceUrlPlaceholder("base_url", P.API.get("oAuth_api_url"));

    }
}