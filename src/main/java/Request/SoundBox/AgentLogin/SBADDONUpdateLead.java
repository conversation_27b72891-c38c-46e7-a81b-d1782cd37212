package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBADDONUpdateLead extends AbstractApiV2 {
    public SBADDONUpdateLead(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBADDONUpdateLead/SBADDONUpdateLeadRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBADDONUpdateLead/SBADDONUpdateLeadResponse.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBADDONUpdateLead/SBADDONUpdateLeadProperty.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}