package Request.SoundBox.AgentLogin;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Sound_Box_Validate_OTP extends AbstractApiV2 {

    public Sound_Box_Validate_OTP(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/Sound_Box_Validate_OTP/Sound_Box_Validate_OTPRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/Sound_Box_Validate_OTP/Sound_Box_Validate_OTPResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/Sound_Box_Validate_OTP/Sound_Box_Validate_OTPProperties.properties");


        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));


    }
}