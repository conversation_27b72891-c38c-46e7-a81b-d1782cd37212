package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class Fetchtnc extends AbstractApiV2{
    public Fetchtnc(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchTncSb/FetchTncRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchTncSb/FetchTncResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchTncSb/FetchTncProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}