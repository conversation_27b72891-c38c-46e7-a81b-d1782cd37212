package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class InitiateNssLead extends AbstractApiV2 {

    public InitiateNssLead() {
        super("MerchantService/V1/device/InitiateNssLead/InitiateNssLeadRequest.json",
              "MerchantService/V1/device/InitiateNssLead/InitiateNssLeadResponse.json",
              "MerchantService/V1/device/InitiateNssLead/InitiateNssLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 