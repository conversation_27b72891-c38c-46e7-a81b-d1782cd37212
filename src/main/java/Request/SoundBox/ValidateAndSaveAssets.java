package Request.SoundBox;

import org.apache.logging.log4j.core.appender.AbstractAppender;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateAndSaveAssets extends AbstractApiV2 {
	public ValidateAndSaveAssets(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/ValidateAndSaveAssets/ValidateAndSaveAssetsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/ValidateAndSaveAssets/ValidateAndSaveAssetsResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/ValidateAndSaveAssets/ValidateAndSaveAssetsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}