package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBCheckBTStatus extends AbstractApiV2{

	  public SBCheckBTStatus(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBCheckBTStatus/SBCheckBTStatusRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBCheckBTStatus/SBCheckBTStatusResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/SBCheckBTStatus/SBCheckBTStatusRequest.json");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));}
}