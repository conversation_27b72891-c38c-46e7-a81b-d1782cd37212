package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class QrValidate extends AbstractApiV2 {
    public QrValidate(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/soundboxprevalidatePaymentQ/prevalidateRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/soundboxprevalidatePaymentQ/prevalidateResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/soundboxprevalidatePaymentQ/prevalidatepropeties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }

}