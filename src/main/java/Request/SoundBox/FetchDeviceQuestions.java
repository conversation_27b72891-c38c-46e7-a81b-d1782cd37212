package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchDeviceQuestions extends AbstractApiV2{

	 public FetchDeviceQuestions(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchDeviceQuestions/FetchDeviceQuestionsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchDeviceQuestions/FetchDeviceQuestionsResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchDeviceQuestions/FetchDeviceQuestionsProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	    }
}