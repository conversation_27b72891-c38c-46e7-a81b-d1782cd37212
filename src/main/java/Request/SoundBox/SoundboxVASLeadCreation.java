package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundboxVASLeadCreation extends AbstractApiV2{

	public SoundboxVASLeadCreation(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxVASLeadCreation/SoundboxVASLeadCreationRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxVASLeadCreation/SoundboxVASLeadCreationResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxVASLeadCreation/SoundboxVASLeadCreationProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}