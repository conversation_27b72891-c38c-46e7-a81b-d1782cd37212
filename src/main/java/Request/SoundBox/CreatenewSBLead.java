package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreatenewSBLead extends AbstractApiV2 {
    public CreatenewSBLead(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/CreateLead/CreateLeadRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/CreateLead/CreateLeadResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/CreateLead/CreateLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}