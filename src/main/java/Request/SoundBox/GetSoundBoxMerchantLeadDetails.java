package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

/**
 * Request class for getSoundBoxMerchantLeadDetails API
 * This API retrieves merchant lead details for a sound box replacement
 */
public class GetSoundBoxMerchantLeadDetails extends AbstractApiV2 {
    
    /**
     * Constructor for GetSoundBoxMerchantLeadDetails
     */
    public GetSoundBoxMerchantLeadDetails() {
        super("MerchantService/v1/soundbox/GetSoundBoxMerchantLeadDetails/GetSoundBoxMerchantLeadDetailsRequest.json",
              "MerchantService/v1/soundbox/GetSoundBoxMerchantLeadDetails/GetSoundBoxMerchantLeadDetailsResponse.json",
              "MerchantService/v1/soundbox/GetSoundBoxMerchantLeadDetails/GetSoundBoxMerchantLeadDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 