package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SentOTPSB extends AbstractApiV2 {
    public SentOTPSB(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxChoosePlan/SentOtpRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxChoosePlan/SentOtpResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxChoosePlan/SentOtpProperties.properties");
        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}