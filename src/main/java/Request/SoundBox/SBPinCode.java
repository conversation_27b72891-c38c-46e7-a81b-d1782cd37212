package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class SBPinCode extends AbstractApiV2{

    public SBPinCode() {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPinCodeRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPinCodeResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPinCodeProperties.properties");

                replaceUrlPlaceholder("base_url", P.API.get("uad_url"));
    }

}