package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchSoundBoxPayment extends AbstractApiV2
{
    public  FetchSoundBoxPayment()
    {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchSoundBoxPayment/FetchSoundBoxPaymentRequest.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchSoundBoxPayment/FetchSoundBoxPaymentResponse.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchSoundBoxPayment/FetchSoundBoxPaymentProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }

}