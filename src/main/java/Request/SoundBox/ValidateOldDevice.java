package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateOldDevice extends AbstractApiV2 {

    public ValidateOldDevice(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxReplacement/ValidateOldDeviceRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/Validatedevice/ValidatedeviceResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/Validatedevice/ValidatedeviceProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }


}