package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DIYV2SBUpdateDeviceAndQR extends AbstractApiV2 {

	 public DIYV2SBUpdateDeviceAndQR(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYV2SBUpdateDeviceAndQR/DIYV2SBUpdateDeviceAndQRRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYV2SBUpdateDeviceAndQR/DIYV2SBUpdateDeviceAndQRResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYV2SBUpdateDeviceAndQR/DIYV2SBUpdateDeviceAndQRRProperties.properties");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
	    }
}