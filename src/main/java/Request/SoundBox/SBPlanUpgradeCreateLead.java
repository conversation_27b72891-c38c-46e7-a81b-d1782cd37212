package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBPlanUpgradeCreateLead  extends AbstractApiV2{
    public SBPlanUpgradeCreateLead (){
            super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPlanUpgradeCreateLead\n"
            		+ "/SBPlanUpgradeCreateLeadRequest.json","/MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPlanUpgradeCreateLead\n"
            				+ "/SBPlanUpgradeCreateLeadResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPlanUpgradeCreateLead\n"
            		+ "/SBPlanUpgradeCreateLeadProperties.properties");
            replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));

    }

}