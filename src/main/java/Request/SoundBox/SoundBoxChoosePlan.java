package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundBoxChoosePlan extends AbstractApiV2
{
    public  SoundBoxChoosePlan(String ReqPath)
    {
        super(ReqPath, "MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxChoosePlan/SoundBoxChoosePlanResponse.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxChoosePlan/SoundBoxChoosePlanProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}