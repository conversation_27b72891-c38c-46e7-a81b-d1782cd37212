package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DIYFetchAllocated extends AbstractApiV2 {

	 public DIYFetchAllocated(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYFetchAloocatedLeads/DIYFetchAloocatedLeadsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYFetchAloocatedLeads/DIYFetchAloocatedLeadsResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYFetchAloocatedLeads/DIYFetchAloocatedLeadsProperties.properties");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
	    }
}