package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBPreValidatePaymentQRNew extends AbstractApiV2 {

	public SBPreValidatePaymentQRNew(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPreValidatePaymentQRNew/SBPreValidatePaymentQRNewRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPreValidatePaymentQRNew/SBPreValidatePaymentQRNewResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBPreValidatePaymentQRNew/SBPreValidatePaymentQRNewProperties.properties");
        replaceUrlPlaceholder("base_url",  P.API.get("goldengate_api_url"));
    }

}
