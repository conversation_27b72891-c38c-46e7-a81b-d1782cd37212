package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SimReplacementValidateOTP extends AbstractApiV2{

	 public SimReplacementValidateOTP() {
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SimReplacementValidateOTP/SimReplacementValidateOTPRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SimReplacementValidateOTP/SimReplacementValidateOTPResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SimReplacementValidateOTP/SimReplacementValidateOTPProperties.properties");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
	    }
	}