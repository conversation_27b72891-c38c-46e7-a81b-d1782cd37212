package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SimActivation extends AbstractApiV2{

	public SimActivation() {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SimActivation/SimActivationRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SimActivation/SimActivationResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SimActivation/SimActivationProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));	}

}