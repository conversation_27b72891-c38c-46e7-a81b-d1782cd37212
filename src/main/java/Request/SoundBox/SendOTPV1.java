package Request.SoundBox;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class SendOTPV1 extends AbstractApiV2{
    public SendOTPV1(){
        super("MerchantService/V3/SendOtp/SendOtpRequest.json","MerchantService/V3/SendOtp/Sendotp1Response.json","MerchantService/V3/SendOtp/SendotpV1properties.properties");
        replaceUrlPlaceholder("base_url",  P.API.get("goldengate_api_url"));
    }
}