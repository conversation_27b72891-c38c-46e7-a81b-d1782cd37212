package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class InitiateH2HLead extends AbstractApiV2 {

    public InitiateH2HLead() {
        super("src/test/resources/MerchantService/V1/device/InitiateH2HLead/InitiateH2HLeadRequest.json",
              "src/test/resources/MerchantService/V1/device/InitiateH2HLead/InitiateH2HLeadResponse.json",
              "src/test/resources/MerchantService/V1/device/InitiateH2HLead/InitiateH2HLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 