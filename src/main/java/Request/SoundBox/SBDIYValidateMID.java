package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBDIYValidateMID extends AbstractApiV2{

    public SBDIYValidateMID(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYValidateMID/SBDIYValidateMIDRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYValidateMID/SBDIYValidateMIDResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYValidateMID/SBDIYValidateMIDProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}