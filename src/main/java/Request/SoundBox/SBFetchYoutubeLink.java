package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBFetchYoutubeLink extends AbstractApiV2 {
    public SBFetchYoutubeLink(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBFetchYoutubeLink/SBFetchYoutubeLinkRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBFetchYoutubeLink/SBFetchYoutubeLinkResponse.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/SBFetchYoutubeLink/SBFetchYoutubeLinkProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}