package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBDIYViewMerchantSpecificLead extends AbstractApiV2{
	
    public SBDIYViewMerchantSpecificLead(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYViewMerchantSpecificLead/SBDIYViewMerchantSpecificLeadRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYViewMerchantSpecificLead/SBDIYViewMerchantSpecificLead.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYViewMerchantSpecificLead/SBDIYViewMerchantSpecificLeadProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }

}