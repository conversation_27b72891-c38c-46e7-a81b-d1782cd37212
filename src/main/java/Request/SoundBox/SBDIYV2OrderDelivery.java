package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBDIYV2OrderDelivery extends AbstractApiV2 {

	public SBDIYV2OrderDelivery(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYV2OrderDelivery/SBDIYV2OrderDeliveryRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYV2OrderDelivery/SBDIYV2OrderDeliveryResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/SBDIYV2OrderDelivery/SBDIYV2OrderDeliveryProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        }
}
