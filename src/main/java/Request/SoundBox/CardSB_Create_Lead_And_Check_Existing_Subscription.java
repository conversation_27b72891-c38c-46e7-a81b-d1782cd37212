package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CardSB_Create_Lead_And_Check_Existing_Subscription  extends AbstractApiV2{

	public CardSB_Create_Lead_And_Check_Existing_Subscription(){
        super("MerchantService/V1/SoundBox/CardSB_Create_Lead_And_Check_Existing_Subscription/CardSB_Create_Lead_And_Check_Existing_SubscriptionRequest.json","MerchantService/V1/SoundBox/CardSB_Create_Lead_And_Check_Existing_Subscription/CardSB_Create_Lead_And_Check_Existing_SubscriptionResponse.json","MerchantService/V1/SoundBox/CardSB_Create_Lead_And_Check_Existing_Subscription/CardSB_Create_Lead_And_Check_Existing_Subscription.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}