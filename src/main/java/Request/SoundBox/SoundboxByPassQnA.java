package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundboxByPassQnA extends AbstractApiV2 {

    public SoundboxByPassQnA() {

        super("ATS.v1.BarcodeOnboard/barcodeOnboardRequest.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}