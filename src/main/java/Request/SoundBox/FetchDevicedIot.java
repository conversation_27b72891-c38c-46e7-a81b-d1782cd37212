package Request.SoundBox;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import com.paytm.apitools.core.AbstractApiV2;

public class FetchDevicedIot extends AbstractApiV2 {
    public FetchDevicedIot(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchDevicedIot/FetchDevicedIotRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchDevicedIot/FetchDevicedIotResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchDevicedIot/FetchDevicedIotProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}