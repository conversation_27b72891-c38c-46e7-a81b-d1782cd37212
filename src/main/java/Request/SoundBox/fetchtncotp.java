package Request.SoundBox;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class fetchtncotp extends AbstractApiV2{
    public fetchtncotp(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/fetchtncotp/fetchtncotpRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/fetchtncotp/fetchtncotpResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/fetchtncotp/fetchtncotpProprties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}