package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateAssetAnswers extends AbstractApiV2{

	public UpdateAssetAnswers(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/UpdateAssetAnswers/UpdateAssetAnswersRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/UpdateAssetAnswers/UpdateAssetAnswersResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/UpdateAssetAnswers/UpdateAssetAnswersProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}