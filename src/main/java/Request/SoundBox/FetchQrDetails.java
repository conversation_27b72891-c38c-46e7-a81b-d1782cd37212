package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchQrDetails extends AbstractApiV2 {

    public FetchQrDetails() {
        super("MerchantService/V1/SoundBox/FetchQrDetails/FetchQrDetailsRequest.json",
              "MerchantService/V1/SoundBox/FetchQrDetails/FetchQrDetailsResponse.json",
              "MerchantService/V1/SoundBox/FetchQrDetails/FetchQrDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }

  
}
