package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddOnDetails  extends AbstractApiV2{

	 public AddOnDetails(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/AddOnDetails/AddOnDetailsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/AddOnDetails/AddOnDetailsResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/AddOnDetails/AddOnDetails.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	    }
}