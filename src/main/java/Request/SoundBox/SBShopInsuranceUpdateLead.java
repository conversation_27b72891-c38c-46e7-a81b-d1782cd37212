package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBShopInsuranceUpdateLead extends AbstractApiV2{

	public SBShopInsuranceUpdateLead(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SBShopInsuranceUpdateLead/SBShopInsuranceUpdateLeadRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBShopInsuranceUpdateLead/SBShopInsuranceUpdateLeadResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SBShopInsuranceUpdateLead/SBShopInsuranceUpdateLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}