package Request.SoundBox;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class GetMerchantid extends AbstractApiV2 {
    public GetMerchantid(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/GetMid/GetMidRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/GetMid/GetMidResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/GetMid/GetMidProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}