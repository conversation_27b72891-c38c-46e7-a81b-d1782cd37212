package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BindDeviceByOTP extends AbstractApiV2 {

	 public BindDeviceByOTP(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/BindDeviceByOTP/BindDeviceByOTPRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/BindDeviceByOTP/BindDeviceByOTPResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/BindDeviceByOTP/BindDeviceByOTPProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	    }
}