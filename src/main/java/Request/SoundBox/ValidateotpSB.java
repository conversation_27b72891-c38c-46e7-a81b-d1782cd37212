package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateotpSB extends AbstractApiV2 {
    public ValidateotpSB(){
        super("MerchantService/V3/ValidateotpSB/ValidateotpSBRequest.json","MerchantService/V3/ValidateotpSB/ValidateotpSBResponse.json","MerchantService/V3/ValidateotpSB/ValidateotpSBProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}