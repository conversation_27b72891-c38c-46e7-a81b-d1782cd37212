package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetMerchantBasicDetails extends AbstractApiV2 {

    public GetMerchantBasicDetails() {
        super("MerchantService/V1/SoundBox/GetMerchantBasicDetails/GetMerchantBasicDetailsRequest.json",
              "MerchantService/V1/SoundBox/GetMerchantBasicDetails/GetMerchantBasicDetailsResponse.json",
              "MerchantService/V1/SoundBox/GetMerchantBasicDetails/GetMerchantBasicDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}
