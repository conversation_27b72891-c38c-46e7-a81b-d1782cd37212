package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundBoxFetchPlan extends AbstractApiV2
{
    public  SoundBoxFetchPlan()
    {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchPlanSoundBox/SoundBoxFetchPlanRequest.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchPlanSoundBox/SoundBoxFetchPlanResponse.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/FetchPlanSoundBox/SoundBoxFetchPlanProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}