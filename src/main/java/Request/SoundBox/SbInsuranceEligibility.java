package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SbInsuranceEligibility extends AbstractApiV2 {
	
	public SbInsuranceEligibility(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SbInsuranceEligibility/SbInsuranceEligibilityRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SbInsuranceEligibility/SbInsuranceEligibilityResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SbInsuranceEligibility/SbInsuranceEligibilityProperties.properties");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
	    }
}