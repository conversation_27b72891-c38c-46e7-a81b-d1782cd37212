package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchPlans extends AbstractApiV2 {
    public FetchPlans(){
        super("MerchantService/v1/device/fetchPlan/FetchPlansRequest.json" , "MerchantService/v1/device/fetchPlan/FetchPlansResponse.json" ,"MerchantService/v1/device/fetchPlan/FetchPlansProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));

    }
}