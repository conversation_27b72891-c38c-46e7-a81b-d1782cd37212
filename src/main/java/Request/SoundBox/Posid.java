package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Posid extends AbstractApiV2 {
    public Posid(){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/Posid/Posid.Request" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/Posid/Posid.Response","MerchantService/V1/SoundBox/SoundBoxCreateLead/Posid/Posid.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));

    }

}