package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateSoundboxReplacementLead extends AbstractApiV2 {

    public CreateSoundboxReplacementLead() {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxReplacement/CreateReplacementLeadRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxReplacement/CreateReplacementLeadResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxReplacement/CreateReplacementLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}