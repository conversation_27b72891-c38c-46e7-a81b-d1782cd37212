package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SBMerchantDetails extends AbstractApiV2 {
    public SBMerchantDetails (String custid){
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SbmerchantdetailsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SbmerchantdetailsResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SbmerchantdeatilsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("boss_api_url"));
        replaceUrlPlaceholder("custId" ,custid);
    }
}