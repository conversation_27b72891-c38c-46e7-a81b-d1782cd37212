package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DIYUpdateDeviceID extends AbstractApiV2{

	 public DIYUpdateDeviceID(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYUpdateDeviceID/DIYUpdateDeviceIDRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYUpdateDeviceID/DIYUpdateDeviceIDResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/DIYUpdateDeviceID/DIYUpdateDeviceIDProperties.properties");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
	    }
}