
package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundBoxLead extends AbstractApiV2 {

    public SoundBoxLead(String ReqPath) {
        super(ReqPath, "MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxCreateLeadResponse.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxCreateLeadProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}