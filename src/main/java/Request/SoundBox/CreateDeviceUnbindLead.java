package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateDeviceUnbindLead  extends AbstractApiV2{

	   public CreateDeviceUnbindLead(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/CreateDeviceUnbindLead/CreateDeviceUnbindLeadRequest.json" , "MerchantService/V1/SoundBox/SoundBoxCreateLead/CreateDeviceUnbindLead/CreateDeviceUnbindLeadResponse.json" ,"MerchantService/V1/SoundBox/SoundBoxCreateLead/CreateDeviceUnbindLead/CreateDeviceUnbindLeadProperties.properties");
	        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));

	    }
}