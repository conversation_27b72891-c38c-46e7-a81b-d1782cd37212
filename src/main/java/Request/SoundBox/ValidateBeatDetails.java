package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateBeatDetails extends AbstractApiV2{
	 public ValidateBeatDetails(){
	        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/ValidateBeatDetails/ValidateBeatDetailsRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/ValidateBeatDetails/ValidateBeatDetailsResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/ValidateBeatDetails/ValidateBeatDetailsProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	    }

}