package Request.SoundBox;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchSoundboxReplacementLead extends AbstractApiV2 {

    public FetchSoundboxReplacementLead(String custId) {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxReplacement/FetchReplacementLeadRequest.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxReplacement/FetchReplacementLeadResponse.json","MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundboxReplacement/FetchReplacementLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("custId", custId);
    }
}