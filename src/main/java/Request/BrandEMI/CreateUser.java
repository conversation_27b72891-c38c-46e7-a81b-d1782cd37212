package Request.BrandEMI;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateUser  extends AbstractApiV2 {
	
	public CreateUser(String RequestPath)
    {
		   super(RequestPath,"oAuth/oAuthWormhole/CreateUser/CreateUserResponse.json", "oAuth/oAuthWormhole/CreateUser/CreateUserProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("oAuth_api_url"));
    }


}
