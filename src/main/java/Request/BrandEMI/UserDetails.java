package Request.BrandEMI;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UserDetails  extends AbstractApiV2 {
	
	public UserDetails(String RequestPath)
    {
		   super(RequestPath,"oAuth/oAuthWormhole/UserDetails/UserDetailsResponse.json", "oAuth/oAuthWormhole/UserDetails/UserDetailsProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("oAuth_api_url"));
    }

}
