package Request.VMN;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class MerchantDetailsURLPath extends AbstractApiV2
{
    public MerchantDetailsURLPath(String path)


    {
        super( path, "VMN/fact_vmn_srs/search/_searchResponse.json", "VMN/fact_vmn_srs/search/_searchProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("VMN_MerchantDetails_Url"));

    }
}