package Request.UPI;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetUPIAccountDetail extends AbstractApiV2{
    public GetUPIAccountDetail()
    {
        super("UPI/GetUPIAccountDetailRequest.json", "UPI/UPIAccountDetailResponse.json", "UPI/UPIAccountDetailProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("UPI_Secure_url"));
    }
}