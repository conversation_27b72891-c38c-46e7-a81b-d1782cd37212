package Request.SFtoOE;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateBusinessLead extends AbstractApiV2 {
	   
		public CreateBusinessLead()
	    {
			   super("MerchantService/V1/SFtoOE/CreateBusinessLeadRequest.json","MerchantService/V1/SFtoOE/CreateBusinessLeadResponse.json", "MerchantService/V1/SFtoOE/CreateBusinessLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("sf_url"));
	    }
		
		
		

}
