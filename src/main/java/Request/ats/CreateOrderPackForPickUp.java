package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateOrderPackForPickUp extends AbstractApiV2
{

    public CreateOrderPackForPickUp (String Reqpath) {

        super(Reqpath, "ATS/CreateOrder/ATSPackForPickupResponse.json", "ATS/CreateOrder/ATSPackForPickupProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("sts_url"));

    }
}
