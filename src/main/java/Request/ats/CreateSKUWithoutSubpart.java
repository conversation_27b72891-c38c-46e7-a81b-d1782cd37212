package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateSKUWithoutSubpart extends AbstractApiV2 {

    public CreateSKUWithoutSubpart() {

        super("ATS.v1.sku/atsSKURequestWithoutSubpart.json", "ATS.v1.sku/atsSKUResponse.json", "ATS.v1.sku/atsSKUProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }

}
