package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Subpartassetupdate extends AbstractApiV2
{
    public Subpartassetupdate(String ReqPath)
    {
        super(ReqPath,"ATS.v1.subpart_assetupdate/subpartassetreplaceresponse.json","ATS.v1.subpart_assetupdate/subpartassetreplaceproperties.properties");
        replaceUrlPlaceholder("ats_base_url",P.API.get("ats_base_url"));
    }


}

