package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import net.bytebuddy.implementation.bind.annotation.Super;
import okhttp3.Request;

public class CheckRemainingCapacity extends AbstractApiV2
{
    public CheckRemainingCapacity(String RequestPath)
    {
        super(RequestPath,"ATS.v1.getThresholdCount/getThresholdCountResponse.json","ATS.v1.getThresholdCount/getThresholdCountProperties.properties");
        replaceUrlPlaceholder("base_url",P.API.get("ats_base_url"));
    }

}

