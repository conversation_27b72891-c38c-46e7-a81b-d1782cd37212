package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class GenerateBarcode extends AbstractApiV2  {

    public GenerateBarcode () {

        super("ATS.v1.BarcodeGenerate/barcodeGenerateRequest.json", "ATS.v1.BarcodeGenerate/barcodeGenerateResponse.json", "ATS.v1.BarcodeGenerate/barcodeGenerateProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
}
