package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AtsAssetUpdate extends AbstractApiV2 {

    public AtsAssetUpdate () {

        super("ATS.v1.AssetUpdate/AssetUpdateRequest.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
    public AtsAssetUpdate (int x) {

        super("ATS.v1.AssetUpdate/AssetUpdateRequest2.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
    public AtsAssetUpdate (char c) {

        super("ATS.v1.AssetUpdate/AssetUpdateRequest3.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
    public AtsAssetUpdate (String g) {

        super("ATS.v1.AssetUpdate/AssetUpdateRequest4.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
    public AtsAssetUpdate (float s) {


        super("ATS.v1.AssetUpdate/AssetUpdateRequest5.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));


    }
}
