package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class VerifiedQRCode extends AbstractApiV2 {

    public VerifiedQRCode() {

        super("ATS/v1/catagory/atsCategoryRequest.json", "ATS/v1/catagory/atsCategoryResponse.json", "ATS/v1/catagory/atsCategoryProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }

}
