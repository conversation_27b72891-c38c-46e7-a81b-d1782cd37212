package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class OnboardChildBarcode extends AbstractApiV2 {

    public OnboardChildBarcode () {

        super("ATS.v1.BarcodeOnboard/barcodeOnboard_ChildRequest.json", "ATS.v1.BarcodeOnboard/barcodeOnboard_ChildResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
}