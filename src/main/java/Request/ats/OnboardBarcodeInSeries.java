package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class OnboardBarcodeInSeries extends AbstractApiV2 {

    public OnboardBarcodeInSeries () {

        super("ATS.v1.BarcodeOnboard/barcodeOnboardInSeriesRequest.json", "ATS.v1.BarcodeOnboard/barcodeOnboardInSeriesResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
}