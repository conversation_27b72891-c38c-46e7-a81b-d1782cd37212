package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateATSSupplier extends AbstractApiV2 {

    public CreateATSSupplier () {

        super("ATS.v1.supplier/atsSupplierRequest.json", "ATS.v1.supplier/atsSupplierResponse.json", "ATS.v1.supplier/atsSupplierProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }

}
