package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class AtsAssetValidate extends AbstractApiV2 {

	    public AtsAssetValidate () {

	        super("ATS.v1.SkuGroup/SkuGroupRequest.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

	    }
}
