package Request.ats;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ListOfAssetDetailsForLost extends AbstractApiV2 {

    public ListOfAssetDetailsForLost() {

        super("ATS.v1.BarcodeOnboard/barcodeOnboardRequest.json", "ATS.v1.BarcodeOnboard/barcodeOnboardResponse.json", "ATS.v1.BarcodeOnboard/barcodeOnboardProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("ats_base_url"));

    }
}