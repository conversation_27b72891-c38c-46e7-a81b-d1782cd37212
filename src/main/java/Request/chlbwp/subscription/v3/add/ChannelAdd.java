package Request.chlbwp.subscription.v3.add;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ChannelAdd extends AbstractApiV2 {

    public ChannelAdd() {
        super("ChannelOnboarding/channelSubscription/channelSubscriptionReq.json", "ChannelOnboarding/channelSubscription/channelSubscriptionRes.json", "ChannelOnboarding/channelSubscription/channelSubscription.properties");
       replaceUrlPlaceholder("base_url", P.API.get("channel_url"));
    }
}
