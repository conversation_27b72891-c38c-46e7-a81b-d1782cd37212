package Request.FSM;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateTeamAndSubteam extends AbstractApiV2
{
    public UpdateTeamAndSubteam()
    {
        super("FSM/UpdateTeamAndSubteamRequest.json", "KYB/KybGet/KybGetResponse.json", "KYB/KybGet/KybGetProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("fsm_url"));
    }
}
