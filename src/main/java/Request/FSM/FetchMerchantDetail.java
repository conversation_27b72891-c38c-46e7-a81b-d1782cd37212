package Request.FSM;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchMerchantDetail extends AbstractApiV2
{
    public FetchMerchantDetail()

    {
        super("FSM/FetchMerchantDetailRequest.json", "KYB/KybGet/KybGetResponse.json", "KYB/KybGet/KybGetProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("fsm_url"));

    }
}
