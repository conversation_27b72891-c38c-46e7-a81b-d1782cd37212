package Request.PSA_Agent_Onboarding;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BankDetailsAgentOnboarding extends AbstractApiV2 {
    public BankDetailsAgentOnboarding(String ifsccode){
        super("MerchantService/V3/BankDetailsAgentOnboarding/BankDetailsAgentOnboardingRequest.json" , "MerchantService/V3/BankDetailsAgentOnboarding/BankDetailsAgentOnboardingResponse.json" , "MerchantService/V3/BankDetailsAgentOnboarding/BankDetailsAgentOnboardingProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("ifsccode" , ifsccode);
    }
}
