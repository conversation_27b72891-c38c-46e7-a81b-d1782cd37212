package Request.PSA_Agent_Onboarding;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PSAFetchScreenDetails extends AbstractApiV2 {
    public PSAFetchScreenDetails(){
        super("MerchantService/V3/PSAFetchScreenDetails/PSAFetchScreenDetailsRequest.json" , "MerchantService/V3/PSAFetchScreenDetails/PSAFetchScreenDetailsResponse.json" , "MerchantService/V3/PSAFetchScreenDetails/PSAFetchScreenDetailsProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
