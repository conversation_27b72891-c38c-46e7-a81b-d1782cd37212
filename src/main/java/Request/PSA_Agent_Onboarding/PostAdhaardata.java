package Request.PSA_Agent_Onboarding;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PostAdhaardata extends AbstractApiV2 {
    public PostAdhaardata(String cust_id){
        super("MerchantService/V3/PostAdhaardata/PostAdhaardataRequest.json" , "MerchantService/V3/PostAdhaardata/PostAdhaardataResponse.json" , "MerchantService/V3/PostAdhaardata/PostAdhaardataProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("cust_id" , cust_id);
    }

}
