package Request.PSA_Agent_Onboarding;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PostBankDetailsAgentOnboarding extends AbstractApiV2 {
    public PostBankDetailsAgentOnboarding(String custid){
        super("MerchantService/V3/PostBankDetailsAgentOnboarding/PostBankDetailsAgentOnboardingRequest.json" , "MerchantService/V3/PostBankDetailsAgentOnboarding/PostBankDetailsAgentOnboardingResponse.json" , "MerchantService/V3/PostBankDetailsAgentOnboarding/PostBankDetailsAgentOnboardingProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("cust_id" , custid);
    }
}
