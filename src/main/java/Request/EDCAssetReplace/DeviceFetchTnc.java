package Request.EDCAssetReplace;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DeviceFetchTnc extends AbstractApiV2
{
	public DeviceFetchTnc()
	{
		super("EDCreplacesubpart/assetdeviceTnCRequest.json","EDCreplacesubpart/assetdeviceTnCResponse.json","EDCreplacesubpart/assetdeviceTnCProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	}
}
