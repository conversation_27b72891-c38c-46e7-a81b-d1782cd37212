package Request.EDCAssetReplace;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Edcassetreplacecreatelead extends AbstractApiV2
{

    public Edcassetreplacecreatelead(String RequestPath)
    {
        super(RequestPath,"EDCreplacesubpart/assetreplacecreateleadresponse.json","EDCreplacesubpart/assetreplacecreateleadproperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}
