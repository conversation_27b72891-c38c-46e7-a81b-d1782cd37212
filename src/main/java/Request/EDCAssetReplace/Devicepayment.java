package Request.EDCAssetReplace;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Devicepayment extends AbstractApiV2
{
		public Devicepayment()
		{
			super("EDCreplacesubpart/devicepaymentRequest.json","EDCreplacesubpart/devicepaymentResponse.json","EDCreplacesubpart/devicepaymentProperties.properties");
			replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
		}
}
