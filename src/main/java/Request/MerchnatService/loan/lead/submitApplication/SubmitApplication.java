package Request.MerchnatService.loan.lead.submitApplication;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SubmitApplication extends AbstractApiV2{
	   
		public SubmitApplication()
	    {
			   super("MerchantService/loan/lead/submitApplication/SubmitApplicationRequest.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationResponse.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		

		public SubmitApplication(String waitForEmandate,String workflowVersion)
	    {
			   super("MerchantService/loan/lead/submitApplication/SubmitApplicationVersionRequest.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationVersionResponse.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationVersionProperties.properties");
			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));

	    }
		public SubmitApplication(boolean  flag)
	    {
			   super("MerchantService/loan/lead/submitApplication/SubmitApplicationArilRequest.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationResponse.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationProperties.properties");


			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public SubmitApplication(String solution)
	    {
			   super("MerchantService/loan/lead/submitApplication/SubmitApplicationHeroRequest.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationHeroResponse.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationHeroProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		public SubmitApplication(boolean isNewLender, boolean onlyAgreement)
	    {
			   super("MerchantService/loan/lead/submitApplication/SubmitWithAgreementOnlyRequest.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationResponse.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public SubmitApplication(String waitForEmandate,String workflowVersion,boolean isOnlyAgreement)
	    {
			   super("MerchantService/loan/lead/submitApplication/SubmitAgreementWithVersionRequest.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationVersionResponse.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationVersionProperties.properties");
			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));

	    }
		
		public SubmitApplication(String requestJsonPath,Boolean isPLMigration)
	    {
			   super(requestJsonPath, "MerchantService/loan/lead/submitApplication/SubmitApplicationVersionResponse.json", "MerchantService/loan/lead/submitApplication/SubmitApplicationVersionProperties.properties");
			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));

	    }
}	