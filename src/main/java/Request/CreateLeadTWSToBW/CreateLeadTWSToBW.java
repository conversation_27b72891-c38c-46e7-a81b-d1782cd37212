package Request.CreateLeadTWSToBW;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadTWSToBW extends AbstractApiV2 {

	
	public CreateLeadTWSToBW()
	{
		super("MerchantServicev1profileupdate/CreateLeadTWSToBWRequest.json","MerchantServicev1profileupdate/CreateLeadTWSToBWResponse.json","MerchantServicev1profileupdate/CreateLeadTWSToBWProperties.properties");
		replaceUrlPlaceholder("base_url",P.API.get("api_url"));
				
	}
	
	
}
