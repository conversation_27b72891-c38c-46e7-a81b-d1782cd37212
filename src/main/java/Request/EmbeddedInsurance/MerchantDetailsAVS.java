package Request.EmbeddedInsurance;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class MerchantDetailsAVS extends AbstractApiV2 {

    public MerchantDetailsAVS (String ReqPath)
    {
        super(ReqPath,"EmbeddedInsurance/GetMerchantDetailsResponse.json","EmbeddedInsurance/GetMerchantDetailsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("avs_url"));

    }
}
