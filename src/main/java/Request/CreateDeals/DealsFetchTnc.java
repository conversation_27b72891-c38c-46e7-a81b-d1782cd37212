package Request.CreateDeals;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DealsFetchTnc extends AbstractApiV2 {


	public DealsFetchTnc()
	{
		super("MerchantServicev1sdMerchantfetchDynamicTnC/DealsTncFetchRequest.json","MerchantServicev1sdMerchantfetchDynamicTnC/DealsTncFetchResponse.json","MerchantServicev1sdMerchantfetchDynamicTnC/DealsTncFetchProperties.properties");
		replaceUrlPlaceholder("base_url",P.API.get("api_url"));

	}


}
