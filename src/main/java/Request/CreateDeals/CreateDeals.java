package Request.CreateDeals;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateDeals extends AbstractApiV2 {


	public CreateDeals(String requestBodyPath)
	{
		super(requestBodyPath,"MerchantServicev1sdMerchantlead/CreateDealsResponse.json","MerchantServicev1sdMerchantlead/CreateDealsProperties.properties");
		replaceUrlPlaceholder("base_url",P.API.get("api_url"));

	}


}
