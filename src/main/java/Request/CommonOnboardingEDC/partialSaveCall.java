package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class partialSaveCall extends AbstractApiV2 {
	public partialSaveCall(String ReqPath,String custId)
    {
        super(ReqPath,"CommonOnboardingEDC/partialSave/partialSaveResponse.json","CommonOnboardingEDC/partialSave/partialSave.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("custId", custId);

    }
}
