package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddonShopInsurance extends AbstractApiV2
{
	public AddonShopInsurance(String ReqPath)
	{
		super(ReqPath,"MerchantService/V1/EDC/ShopInsurance/ShopInsuranceResponse.json","MerchantService/V1/EDC/ShopInsurance/ShopInsuranceProperties.properties");

		replaceUrlPlaceholder("base_url", P.API.get("goldengate_url"));

	}
}
