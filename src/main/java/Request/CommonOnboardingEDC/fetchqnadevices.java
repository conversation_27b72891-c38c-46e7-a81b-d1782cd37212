package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchqnadevices extends AbstractApiV2 {
	public fetchqnadevices(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/fetchqnaedc/fetchqnaResponse.json","CommonOnboardingEDC/fetchqnaedc/fetchqna.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}