package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class createMCOLead extends AbstractApiV2 {
	public createMCOLead(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/CreateMCOLead/CreateMCOLeadResponse.json","CommonOnboardingEDC/CreateMCOLead/CreateMCOLead.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }

}
