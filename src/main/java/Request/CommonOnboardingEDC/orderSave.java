package Request.CommonOnboardingEDC;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;



public class orderSave extends AbstractApiV2 {
    public orderSave(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/orderSave/orderSaveEDCResponse.json","CommonOnboardingEDC/orderSave/orderSaveEDC.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}
