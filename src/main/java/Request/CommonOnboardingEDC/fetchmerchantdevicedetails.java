package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchmerchantdevicedetails extends AbstractApiV2 {
	public fetchmerchantdevicedetails(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/fetchdevicedetailsedc/fetchdevicedetailsedcResponse.json","CommonOnboardingEDC/fetchdevicedetailsedc/fetchdevicedetailsedc.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}
