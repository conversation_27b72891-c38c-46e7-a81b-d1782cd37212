package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class sendOTPLead extends AbstractApiV2 {
	public sendOTPLead(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/sendOTPLead/sendOTPLeadResponse.json","CommonOnboardingEDC/sendOTPLead/sendOTPLead.properties");

        replaceUrlPlaceholder("base_url",P.API.get("api_url"));

    }
}
