package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class createDeviceLead extends AbstractApiV2 {
	public createDeviceLead(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/deviceOnboardingLead/deviceOnboardingLeadResponse.json","CommonOnboardingEDC/deviceOnboardingLead/deviceOnboardingLead.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}