package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class addBank extends AbstractApiV2 {
	public addBank(String ReqPath,String custId)
    {
        super(ReqPath,"CommonOnboardingEDC/addBank/addBankResponse.json","CommonOnboardingEDC/addBank/addBank.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("custId", custId);

    }
}

