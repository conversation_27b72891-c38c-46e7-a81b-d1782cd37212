package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class cartsave extends AbstractApiV2 {
	public cartsave(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/cartSave/cartSaveEDCResponse.json","CommonOnboardingEDC/cartSave/cartSaveEDC.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}
