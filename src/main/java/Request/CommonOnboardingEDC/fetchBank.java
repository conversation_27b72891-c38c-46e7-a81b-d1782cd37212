package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchBank  extends AbstractApiV2 {
	public fetchBank(String ReqPath,String custId)
    {
        super(ReqPath,"CommonOnboardingEDC/fetchBank/fetchBankResponse.json","CommonOnboardingEDC/fetchBank/fetchBank.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("custId", custId);

    }
}

