package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class devicedetailsfetch extends AbstractApiV2 {
	public devicedetailsfetch(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/fetchDeviceDetails/fetchDeviceDetailsResponse.json","CommonOnboardingEDC/fetchDeviceDetails/fetchDeviceDetails.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}