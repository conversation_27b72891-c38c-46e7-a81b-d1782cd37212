package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class getLeadStatus extends AbstractApiV2 {
	public getLeadStatus(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/GetLeadStatus/GetLeadStatusResponse.json","CommonOnboardingEDC/GetLeadStatus/GetLeadStatus.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    } 

}
