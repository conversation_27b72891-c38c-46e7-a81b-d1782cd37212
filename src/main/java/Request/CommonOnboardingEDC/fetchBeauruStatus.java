package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchBeauruStatus extends AbstractApiV2 {
	
	public fetchBeauruStatus(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/fetchBeauru/fetchBeauruResponse.json","CommonOnboardingEDC/fetchBeauru/fetchBeauru.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));


    }  
}