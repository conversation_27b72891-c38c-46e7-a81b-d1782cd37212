package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class getBank extends AbstractApiV2 {
	
	public getBank(String ReqPath,String custId)
    {
        super(ReqPath,"CommonOnboardingEDC/GetBank/GetBankResponse.json","CommonOnboardingEDC/GetBank/GetBank.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("custId", custId);


    } 
	

}
