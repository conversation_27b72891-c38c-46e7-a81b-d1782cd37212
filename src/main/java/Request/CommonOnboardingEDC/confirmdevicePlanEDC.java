package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class confirmdevicePlanEDC extends AbstractApiV2 {
	public confirmdevicePlanEDC(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/confirmPlanDevice/confirmPlanDevicePropertyResponse.json","CommonOnboardingEDC/confirmPlanDevice/confirmPlanDeviceProperty.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}