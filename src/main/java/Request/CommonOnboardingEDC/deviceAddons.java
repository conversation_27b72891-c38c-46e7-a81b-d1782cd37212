package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class deviceAddons extends AbstractApiV2 {
	public deviceAddons(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/deviceAddOns/deviceAddOnsResponse.json","CommonOnboardingEDC/deviceAddOns/deviceAddOns.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}

