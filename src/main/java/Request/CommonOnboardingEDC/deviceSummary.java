package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class deviceSummary  extends AbstractApiV2 {
	public deviceSummary(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/deviceSummaryEDC/deviceSummaryEDCResponse.json","CommonOnboardingEDC/deviceSummaryEDC/deviceSummaryEDC.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    }
}
