package Request.CommonOnboardingEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchMCOLead extends AbstractApiV2 {
	public fetchMCOLead(String ReqPath,String leadId)
    {
        super(ReqPath,"CommonOnboardingEDC/FetchMCOLead/FetchMCOLeadResponse.json","CommonOnboardingEDC/FetchMCOLead/FetchMCOLead.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("leadId", leadId);

    }
}


