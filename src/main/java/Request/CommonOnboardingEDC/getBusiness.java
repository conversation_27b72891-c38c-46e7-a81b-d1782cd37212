package Request.CommonOnboardingEDC;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class getBusiness extends AbstractApiV2 {
	
	public getBusiness(String ReqPath)
    {
        super(ReqPath,"CommonOnboardingEDC/GetBusiness/GetBusinessResponse.json","CommonOnboardingEDC/GetBusiness/GetBusiness.properties");

        replaceUrlPlaceholder("base_url",P.API.get("goldengate_api_url"));

    } 
	

}
