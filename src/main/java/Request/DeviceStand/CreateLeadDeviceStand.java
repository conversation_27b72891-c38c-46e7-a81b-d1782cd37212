package Request.DeviceStand;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadDeviceStand extends AbstractApiV2 {

    public CreateLeadDeviceStand(String ReqPath)
    {
        super(ReqPath,"DeviceStand/CreateLeadDeviceStandResponse.json","DeviceStand/CreateLeadDeviceStandProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("device_stand"));
    }
}
