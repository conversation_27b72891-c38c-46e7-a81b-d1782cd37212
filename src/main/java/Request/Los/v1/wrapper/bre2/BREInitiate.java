package Request.Los.v1.wrapper.bre2;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BREInitiate extends AbstractApiV2 {
	
	
	
	public BREInitiate()
    {
		   super("MerchantService/v5/callback/SecondBRERequest.json", "MerchantService/v5/callback/SecondBREInitaiteResponse.json", "MerchantService/v5/callback/SecondBREInitaiteProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("Los_url"));
    }

}
