package Request.Los.v1.wrapper.bre.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ThirdBREStatus extends AbstractApiV2 {
	
	public ThirdBREStatus()
    {
		   super("MerchantService/v5/callback/SecondBRERequest.json", "MerchantService/v5/callback/SecondBREInitaiteResponse.json", "MerchantService/v5/callback/SecondBREInitaiteProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("Los_url"));
    }

}
