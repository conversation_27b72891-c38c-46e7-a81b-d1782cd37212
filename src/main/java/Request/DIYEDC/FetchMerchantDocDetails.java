package Request.DIYEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchMerchantDocDetails extends AbstractApiV2 {
    public FetchMerchantDocDetails(String RequestPath){
        super(RequestPath,"MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCOResponse.json","MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCO.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
    public FetchMerchantDocDetails(){
        super("MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/FetchMid_DIYEDCRequest.json","MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCOResponse.json","MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCO.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Boss_url"));
    }
}
