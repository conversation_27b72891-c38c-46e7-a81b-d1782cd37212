package Request.DIYEDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CL1 extends AbstractApiV2 {
    public CL1(String RequestPath){
        super(RequestPath,"MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCOResponse.json","MerchantService/V1/sdMerchant/lead/CreateLead_DIYEDC/CreateLead_DIYMCO.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}

