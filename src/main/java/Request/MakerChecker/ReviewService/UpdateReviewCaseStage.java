package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateReviewCaseStage extends AbstractApiV2 {
    public UpdateReviewCaseStage(String ReqPath) {
        super(ReqPath, "MakerChecker/ReviewService/UpdateReviewCaseStage/UpdateReviewCaseStageResponse.json", "MakerChecker/ReviewService/UpdateReviewCaseStage/UpdateReviewCaseStageProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }
}
