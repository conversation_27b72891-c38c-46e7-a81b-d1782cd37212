package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ApproveRejectCase extends AbstractApiV2 {
    public ApproveRejectCase(String ReqPath) {
        super(ReqPath, "MakerChecker/ReviewService/ApproveRejectCase/ApproveRejectCaseResponse.json", "ODS/CreatePlanODS/CreatePlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }
}
