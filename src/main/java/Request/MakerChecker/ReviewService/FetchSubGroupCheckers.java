package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchSubGroupCheckers extends AbstractApiV2{

    public FetchSubGroupCheckers(String ReqPath)
    {
        super(ReqPath, "MakerChecker/ReviewService/FetchSubGroupCheckers/FetchSubGroupCheckersResponse.json", "MakerChecker/ReviewService/FetchSubGroupCheckers/FetchSubGroupCheckersProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("kyr_staging_url"));

    }
}
