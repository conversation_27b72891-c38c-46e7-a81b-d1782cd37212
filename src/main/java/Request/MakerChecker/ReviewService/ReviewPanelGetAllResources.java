package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ReviewPanelGetAllResources extends AbstractApiV2 {

    public ReviewPanelGetAllResources(String ReqPath){
        super(ReqPath, "MakerChecker/ReviewService/ReviewPanelGetAllResources/ReviewPanelGetAllResourcesResponse.json","MakerChecker/ReviewService/ReviewPanelGetAllResources/ReviewPanelGetAllResourcesProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }
}
