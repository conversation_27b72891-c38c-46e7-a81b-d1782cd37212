package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchRejectionReasons extends AbstractApiV2{

    public FetchRejectionReasons(String ReqPath){
        super(ReqPath, "MakerChecker/ReviewService/FetchRejectionReasons/FetchRejectionReasonsResponse.json","ODS/CreatePlanODS/CreatePlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }

}
