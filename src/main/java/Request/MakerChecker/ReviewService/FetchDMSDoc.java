package Request.MakerChecker.ReviewService;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchDMSDoc extends AbstractApiV2 {

    public FetchDMSDoc(String ReqPath){
        super(ReqPath, "MakerChecker/ReviewService/FetchDMSDoc/FetchDMSDocResponse.json","ODS/CreatePlanODS/CreatePlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }

}
