package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchReviewStatusCount extends AbstractApiV2 {
    public FetchReviewStatusCount(String ReqPath) {
        super(ReqPath, "MakerChecker/ReviewService/FetchReviewStatusCount/FetchReviewStatusCountResponse.json", "MakerChecker/ReviewService/FetchReviewStatusCount/FetchReviewStatusCountProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }
}
