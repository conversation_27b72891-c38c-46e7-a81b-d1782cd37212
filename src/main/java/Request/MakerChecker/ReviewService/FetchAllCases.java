package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchAllCases extends AbstractApiV2{


    public FetchAllCases(String ReqPath){

        super(ReqPath, "MakerChecker/ReviewService/FetchAllCases/FetchAllCasesResponse.json","ODS/CreatePlanODS/CreatePlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));

    }
}