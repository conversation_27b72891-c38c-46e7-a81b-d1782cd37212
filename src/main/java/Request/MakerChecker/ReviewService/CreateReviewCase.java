package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateReviewCase extends AbstractApiV2 {
    public CreateReviewCase(String ReqPath) {
        super(ReqPath, "MakerChecker/ReviewService/CreateReviewCase/CreateReviewCaseResponse.json", "ODS/CreatePlanODS/CreatePlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }
}
