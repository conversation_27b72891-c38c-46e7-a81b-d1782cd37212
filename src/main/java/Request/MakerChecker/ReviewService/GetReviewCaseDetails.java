package Request.MakerChecker.ReviewService;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetReviewCaseDetails extends AbstractApiV2 {
    public GetReviewCaseDetails(String ReqPath){
        super(ReqPath, "MakerChecker/ReviewService/GetReviewCaseDetails/GetReviewCaseDetailsResponse.json","MakerChecker/ReviewService/GetReviewCaseDetails/GetReviewCaseDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Review_service_url"));
    }
}
