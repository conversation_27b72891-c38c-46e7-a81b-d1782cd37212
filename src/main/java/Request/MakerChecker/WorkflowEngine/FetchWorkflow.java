package Request.MakerChecker.WorkflowEngine;


import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchWorkflow extends AbstractApiV2{
    public FetchWorkflow(String ReqPath)
    {
        super(ReqPath,"MakerChecker/WorkflowService/FetchWorkflow/FetchWorkflowResponse.json","ODS/CreatePlanODS/CreatePlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("workflow_engine_url"));
    }
}
