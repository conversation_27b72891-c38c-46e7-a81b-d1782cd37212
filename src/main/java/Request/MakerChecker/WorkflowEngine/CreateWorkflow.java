package Request.MakerChecker.WorkflowEngine;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateWorkflow extends AbstractApiV2 {

    public CreateWorkflow(String ReqPath)
    {
        super(ReqPath,"MakerChecker/WorkflowService/CreateWorkflow/CreateWorkflowResponse.json","ODS/FetchPlanODS/FetchPlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("workflow_engine_url"));
    }
}
