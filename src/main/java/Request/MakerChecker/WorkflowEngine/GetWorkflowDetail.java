package Request.MakerChecker.WorkflowEngine;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import net.bytebuddy.implementation.bind.annotation.Super;

public class GetWorkflowDetail extends AbstractApiV2 {

    public GetWorkflowDetail(String ReqPath){

        super(ReqPath,"MakerChecker/WorkflowService/GetWorkflowDetail/GetWorkflowDetailResponse.json","ODS/CreatePlanODS/CreatePlanODSProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("workflow_engine_url"));
    }


}
