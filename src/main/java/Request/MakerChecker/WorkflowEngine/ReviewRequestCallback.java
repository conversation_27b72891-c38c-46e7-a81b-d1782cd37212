package Request.MakerChecker.WorkflowEngine;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ReviewRequestCallback extends AbstractApiV2 {
    public ReviewRequestCallback(String ReqPath)
    {
        super(ReqPath,"MakerChecker/WorkflowService/ReviewRequestCallback/ReviewRequestCallbackResponse.json","MakerChecker/WorkflowService/ReviewRequestCallback/ReviewRequestCallbackProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("workflow_engine_url"));
    }
}
