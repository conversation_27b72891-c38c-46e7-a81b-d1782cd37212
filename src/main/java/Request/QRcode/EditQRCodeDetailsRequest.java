package Request.QRcode;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import org.testng.annotations.Test;

public class EditQRCodeDetailsRequest extends AbstractApiV2
{
    public EditQRCodeDetailsRequest(String RequestPath)
    {
        super(RequestPath, "QRService/EditQRCodeDetailsRequestResponse.json", "QRService/EditQRCodeDetailsRequestProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("qr_integration_url"));
    }
}
