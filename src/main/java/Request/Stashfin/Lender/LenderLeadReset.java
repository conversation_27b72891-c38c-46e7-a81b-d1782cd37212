package Request.Stashfin.Lender;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LenderLeadReset extends AbstractApiV2{
	   
		public LenderLeadReset()
	    {
			   super("Stashfin/Lender/LeadResetRequest.json", "MerchantService/v1/Consumer/Lead/FetchLeadDetailsResponse.json", "MerchantService/v1/Consumer/Lead/FetchLeadDetailsProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("stashfin_url"));
	    }

}
