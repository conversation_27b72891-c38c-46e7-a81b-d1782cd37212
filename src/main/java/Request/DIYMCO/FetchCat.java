package Request.DIYMCO;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import net.minidev.json.JSONUtil;

public class FetchCat extends AbstractApiV2 {
    public FetchCat()
    {
        super("MerchantService/V1/sdMerchant/allCategoryBySol/DIYMCOFetchCatRequest.json","","MerchantService/V1/sdMerchant/allCategoryBySol/DIYMCOFetchCatProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }
}
