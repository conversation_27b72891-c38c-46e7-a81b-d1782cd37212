package Request.DIYMCO;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LeadCreate extends AbstractApiV2 {

    public LeadCreate(String RequestPath){
        super(RequestPath,"MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateResponse.json","MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreate.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

    public LeadCreate(){
        super("MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateRequest.json","MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateResponse.json","MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreate.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
//class UpdateLeadAadhaar extends AbstractApiV2 {
//    public UpdateLeadAadhaar(String RequestPath){
//        super(RequestPath,"MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreateResponse.json","MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DIYMCOLeadCreate.properties");
//        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
//    }
//}
