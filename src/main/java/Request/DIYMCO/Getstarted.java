package Request.DIYMCO;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Getstarted extends AbstractApiV2 {

    public Getstarted(String RequestPath){
        super(RequestPath,"MerchantService/V1/sdMerchant/lead/UpdateLeadDIYMCO/UpdateLeadDIYMCOResponse.json","MerchantService/V1/sdMerchant/lead/UpdateLeadDIYMCO/UpdateLeadDIYMCO.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
