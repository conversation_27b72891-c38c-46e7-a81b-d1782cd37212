package Request.OnboardBankV3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class OnboardBankV3 extends AbstractApiV2
{
    public OnboardBankV3(String ReqPath)
    {
        super(ReqPath,"OnboardBank/OnboardBanksV3OnPGResponse.json", "OnboardBank/OnboardBanksV3OnPGProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("boss_url"));

    }
}
