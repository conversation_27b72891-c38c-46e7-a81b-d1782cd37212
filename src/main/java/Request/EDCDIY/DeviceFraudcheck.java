package Request.EDCDIY;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DeviceFraudcheck extends AbstractApiV2 {
    public DeviceFraudcheck(String ReqPath)
    {
        super(ReqPath,"EDCDIY/DeviceFraudcheckResponse.json","EDCDIY/DeviceFraudcheckProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("staging6_api_url"));

    }
}
