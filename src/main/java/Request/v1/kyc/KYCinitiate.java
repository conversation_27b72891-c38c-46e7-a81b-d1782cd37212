package Request.v1.kyc;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class KYCinitiate extends AbstractApiV2{
	   
	public KYCinitiate(String requestJson)
    {
		   super(requestJson, "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("kyc_base_url"));
    }
}
