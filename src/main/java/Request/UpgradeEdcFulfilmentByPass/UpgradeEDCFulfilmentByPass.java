package Request.UpgradeEdcFulfilmentByPass;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpgradeEDCFulfilmentByPass extends AbstractApiV2
{
    public UpgradeEDCFulfilmentByPass(String RequestPath)
    {
        super(RequestPath, "UpgradeFulfilmentBypass/FulfilmentByPassResponse.json", "UpgradeFulfilmentBypass/FulfilmentByPassProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
