package Request.PgProfileUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddPanDIY extends AbstractApiV2 {
    public AddPanDIY(String requestPath) {
        super(requestPath, "MerchantService/v1/profile/update/addPanResponse.json", "MerchantService/v1/profile/update/addPanProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
