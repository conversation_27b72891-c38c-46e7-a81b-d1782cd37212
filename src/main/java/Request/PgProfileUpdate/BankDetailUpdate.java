package Request.PgProfileUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BankDetailUpdate extends AbstractApiV2 {
    public BankDetailUpdate(String requestPath) {
        super(requestPath, "MerchantService/v1/profile/update/bankDetailUpdateResponse.json", "MerchantService/v1/profile/update/bankDetailUpdateProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
