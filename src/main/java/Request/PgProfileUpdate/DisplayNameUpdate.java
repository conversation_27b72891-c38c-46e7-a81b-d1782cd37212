package Request.PgProfileUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DisplayNameUpdate extends AbstractApiV2 {
    public DisplayNameUpdate(String requestPath) {
        super(requestPath, "MerchantService/v1/profile/update/DisplayNameUpdateResponse.json", "MerchantService/v1/profile/update/DisplayNameUpdateProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
