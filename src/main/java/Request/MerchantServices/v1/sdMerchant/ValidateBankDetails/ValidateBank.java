package Request.MerchantServices.v1.sdMerchant.ValidateBankDetails;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateBank extends AbstractApiV2
{
  
	public ValidateBank()
	{
		super("MerchantService/v1/sdMerchant/ValidateBank/ValidateBankRequest.json","MerchantService/v1/sdMerchant/ValidateBank/ValidateBankResponse.json","MerchantService/v1/sdMerchant/ValidateBank/ValidateBankProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
}
