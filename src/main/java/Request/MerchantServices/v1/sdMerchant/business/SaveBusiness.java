package Request.MerchantServices.v1.sdMerchant.business;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveBusiness extends AbstractApiV2{
	
	public SaveBusiness()
    {
        super("MerchantService/v1/sdMerchant/SaveBusiness_UPM_V2/SaveBusinessRequest.json","MerchantService/v1/sdMerchant/SaveBusiness_UPM_V2/SaveBusinessResponse.json","MerchantService/v1/sdMerchant/SaveBusiness_UPM_V2/SaveBusinessProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
