package Request.MerchantServices.v1.sdMerchant.UpdateIndentityDetails;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveIdentityPANDetails extends AbstractApiV2
{
	
public SaveIdentityPANDetails() 
{
	super("MerchantService/v1/sdMerchant/SaveIdentityPANDetails/SaveIdentityPANRequest.json","MerchantService/v1/sdMerchant/SaveIdentityPANDetails/SaveIdentityPANResponse.json","MerchantService/v1/sdMerchant/SaveIdentityPANDetails/SaveIdentityPANProperties.properties");
	replaceUrlPlaceholder("base_url", P.API.get("api_url"));
}
}
