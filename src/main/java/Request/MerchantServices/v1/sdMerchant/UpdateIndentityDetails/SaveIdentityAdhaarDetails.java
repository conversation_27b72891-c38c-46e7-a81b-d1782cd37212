package Request.MerchantServices.v1.sdMerchant.UpdateIndentityDetails;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveIdentityAdhaarDetails extends AbstractApiV2
{
	public SaveIdentityAdhaarDetails() {
		
	   super("MerchantService/v1/sdMerchant/updateIdentityDetails/UpdateIdentityRequest.json","MerchantService/v1/sdMerchant/updateIdentityDetails/UpdateIdentityResponse.json","MerchantService/v1/sdMerchant/updateIdentityDetails/UpdateIdentityProperties.properties");
	   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
}
