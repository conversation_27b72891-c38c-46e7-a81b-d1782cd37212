package Request.MerchantServices.v1.sdMerchant.UpdateIndentityDetails;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveIdentityGSTINDetails extends AbstractApiV2
{

	public SaveIdentityGSTINDetails() 
	{
		super("MerchantService/v1/sdMerchant/SaveGSTINDetails/SaveGSTINRequest.json","MerchantService/v1/sdMerchant/SaveGSTINDetails/SaveGSTINResponse.json","MerchantService/v1/sdMerchant/SaveGSTINDetails/SaveGSTINProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
}


