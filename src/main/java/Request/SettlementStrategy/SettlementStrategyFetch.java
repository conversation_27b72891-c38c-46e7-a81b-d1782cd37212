package Request.SettlementStrategy;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SettlementStrategyFetch extends AbstractApiV2 {

	
	public SettlementStrategyFetch()
	{
		super("TWSTOBW/GetSettlementStrategie/GetSettlementStrategieRequest.json","TWSTOBW/GetSettlementStrategie/GetSettlementStrategieResponse.json","TWSTOBW/GetSettlementStrategie/GetSettlementStrategieproperties.properties");
		replaceUrlPlaceholder("base_url",P.API.get("api_url"));
				
	}
	
	
}
