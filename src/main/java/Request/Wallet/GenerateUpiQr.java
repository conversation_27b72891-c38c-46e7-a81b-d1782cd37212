package Request.Wallet;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GenerateUpiQr extends AbstractApiV2
{
    public GenerateUpiQr()
    {
        super("Wallet/GenerateUpiQr/GenrateUpiQrWalletRequest.json", "Wallet/GenerateUpiQr/GenrateUpiQrWalletResponse.json", "Wallet/GenerateUpiQr/GenrateUpiQrWalletProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("wallet_url"));
    }
}
