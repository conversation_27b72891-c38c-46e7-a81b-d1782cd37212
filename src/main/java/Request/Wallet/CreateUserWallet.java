package Request.Wallet;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateUserWallet extends AbstractApiV2 {
    public CreateUserWallet()
    {
        super("Wallet/CreateUserWallet/CreateUserWalletRequest.json", "Wallet/CreateUserWallet/CreateUserWalletResponse.json", "Wallet/CreateUserWallet/CreateUserWalletProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("wallet_url"));
    }
}
