package Request.Wallet;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class WalletAddMoney extends AbstractApiV2 {
   public WalletAddMoney()
    {
        super("Wallet/WalletAddMOney/AddMoneyWalletRequest.json", "Wallet/WalletAddMOney/AddMoneyWalletResponse.json", "Wallet/WalletAddMOney/AddMoneyWalletProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("wallet_url"));
    }

}
