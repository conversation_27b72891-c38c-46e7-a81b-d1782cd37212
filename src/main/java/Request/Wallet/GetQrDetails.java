package Request.Wallet;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetQrDetails extends AbstractApiV2
{
    public GetQrDetails()
    {
        super("Wallet/GetQrDetails/GetQrDetailsWalletRequest.json", "Wallet/GetQrDetails/GetQrDetailsWalletResponse.json", "Wallet/GetQrDetails/GetQrDetailsWalletProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("wallet_url"));
    }
}
