package Request.UAD;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class SubCategory extends AbstractApiV2 {

    public SubCategory()
    {
        super("UAD/V1/SubCategory/SubCategoryRequest.json", "UAD/V1/SubCategory/SubCategoryResponse.json", "UAD/V1/SubCategory/SubCategoryProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("uad_url"));
    }
}