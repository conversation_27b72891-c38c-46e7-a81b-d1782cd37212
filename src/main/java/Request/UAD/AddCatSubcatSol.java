package Request.UAD;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddCatSubcatSol extends AbstractApiV2
{
    public AddCatSubcatSol()
    {
        super("UAD/V1/AddCatSubcatSol/AddRequest.json","UAD/V1/AddCatSubcatSol/AddResponse.json","UAD/V1/AddCatSubcatSol/AddResponseSchema.json");
        replaceUrlPlaceholder("base_url", P.API.get("uad_url"));
    }
}
