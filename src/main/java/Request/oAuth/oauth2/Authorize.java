
package Request.oAuth.oauth2;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


    public class Authorize extends AbstractApiV2 {

        public Authorize() {
            super("oAuth/oauth2/Autharize/AutharizeRequest.json","oAuth/oauth2/Autharize/AutharizeResponse.json","oAuth/oauth2/Autharize/AutharizeProperties.properties");
            replaceUrlPlaceholder("base_url", P.API.get("oAuth_api_url"));
        }

        //Headers
        String Authorization;
        String ContentType;

        //Request Body Fields
        String response_type;
        String client_id;
        String do_not_redirect;
        String scope;
        String username;
        String password;

        //Response Body Fields
        String code;
        String state;

        public String getAuthorization() {
            return Authorization;
        }

        public void setAuthorization(String authorization) {
            Authorization = authorization;
        }

        public String getContentType() {
            return ContentType;
        }

        public void setContentType(String contentType) {
            ContentType = contentType;
        }

        public String getResponse_type() {
            return response_type;
        }

        public void setResponse_type(String response_type) {
            this.response_type = response_type;
        }

        public String getClient_id() {
            return client_id;
        }

        public void setClient_id(String client_id) {
            this.client_id = client_id;
        }

        public String getDo_not_redirect() {
            return do_not_redirect;
        }

        public void setDo_not_redirect(String do_not_redirect) {
            this.do_not_redirect = do_not_redirect;
        }

        public String getScope() {
            return scope;
        }

        public void setScope(String scope) {
            this.scope = scope;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }
    }
