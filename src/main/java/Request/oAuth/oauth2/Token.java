
package Request.oAuth.oauth2;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class Token extends AbstractApiV2 {
    public Token() {
        super("oAuth/oauth2/Token/TokenRequest.json","oAuth/oauth2/Token/TokenResponse.json","oAuth/oauth2/Token/TokenProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("oAuth_api_url"));
    }

    //Headers
    String Authorization;

    public String getAuthorization() {
        return Authorization;
    }

    public void setAuthorization(String authorization) {
        Authorization = authorization;
    }

    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getGrant_type() {
        return grant_type;
    }

    public void setGrant_type(String grant_type) {
        this.grant_type = grant_type;
    }

    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getExpires() {
        return expires;
    }

    public void setExpires(String expires) {
        this.expires = expires;
    }

    public String getResourceOwnerId() {
        return resourceOwnerId;
    }

    public void setResourceOwnerId(String resourceOwnerId) {
        this.resourceOwnerId = resourceOwnerId;
    }

    public String getScopeResponse() {
        return scopeResponse;
    }

    public void setScopeResponse(String scopeResponse) {
        this.scopeResponse = scopeResponse;
    }

    String ContentType;

    //Request Body Fields
    String code;
    String grant_type;
    String client_id;
    String scope;

    //Response Body Fields
    String access_token;
    String expires;
    String resourceOwnerId;
    String scopeResponse;
}