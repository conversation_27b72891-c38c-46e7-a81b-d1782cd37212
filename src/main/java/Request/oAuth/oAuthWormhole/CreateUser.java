
package Request.oAuth.oAuthWormhole;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class CreateUser extends AbstractApiV2 {

    public CreateUser() {
        super("oAuth/oAuthWormhole/CreateUser/CreateUserRequest.json","oAuth/oAuthWormhole/CreateUser/CreateUserResponse.json","oAuth/oAuthWormhole/CreateUser/CreateUserProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("oAuth_api_url"));
    }

    public String getCookie() {
        return Cookie;
    }

    public void setCookie(String cookie) {
        Cookie = cookie;
    }

    public String getOrigin() {
        return Origin;
    }

    public void setOrigin(String origin) {
        Origin = origin;
    }

    public String getAcceptEncoding() {
        return AcceptEncoding;
    }

    public void setAcceptEncoding(String acceptEncoding) {
        AcceptEncoding = acceptEncoding;
    }

    public String getAcceptLanguage() {
        return AcceptLanguage;
    }

    public void setAcceptLanguage(String acceptLanguage) {
        AcceptLanguage = acceptLanguage;
    }

    public String getUserAgent() {
        return UserAgent;
    }

    public void setUserAgent(String userAgent) {
        UserAgent = userAgent;
    }

    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }

    public String getAccept() {
        return Accept;
    }

    public void setAccept(String accept) {
        Accept = accept;
    }

    public String getReferer() {
        return Referer;
    }

    public void setReferer(String referer) {
        Referer = referer;
    }

    public String getXRequestedWith() {
        return XRequestedWith;
    }

    public void setXRequestedWith(String XRequestedWith) {
        this.XRequestedWith = XRequestedWith;
    }

    public String getConnection() {
        return Connection;
    }

    public void setConnection(String connection) {
        Connection = connection;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLoginPassword() {
        return loginPassword;
    }

    public void setLoginPassword(String loginPassword) {
        this.loginPassword = loginPassword;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    //Headers
    String Cookie = "JSESSIONID=BACB3E60BE815DD6E0763C62696C0E8A; PT-BR=801d5e72-d0d7-563c-a096-34122e2cc6f7";
    String Origin = "https://accounts-staging.paytm.in";
    String AcceptEncoding = "gzip, deflate, br";
    String AcceptLanguage = "en-US,en;q=0.9";
    String UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36";
    String ContentType = "application/json";
    String Accept = "*/*";
    String Referer = "https://accounts-staging.paytm.in/oauth-wormhole/resetPassword";
    String XRequestedWith = "XMLHttpRequest";
    String Connection = "keep-alive";

    //Request Body Params
    String mobile;
    String loginPassword;

    //Response Body Params
    String status;
    String responseCode;
    String code;
    String state;
}
