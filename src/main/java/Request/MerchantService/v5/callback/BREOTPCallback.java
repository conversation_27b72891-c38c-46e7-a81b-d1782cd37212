package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BREOTPCallback extends AbstractApiV2{
	   
		public BREOTPCallback()
	    {
			   super("MerchantService/v5/callback/BREOTPVerificationRequest.json", "MerchantService/v5/callback/BREOTPVerificationResponse.json", "MerchantService/v5/callback/BREOTPVerificationProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}
