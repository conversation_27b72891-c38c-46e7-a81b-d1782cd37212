package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class lmsCallBack_LOS extends AbstractApiV2 {
	
	
	public lmsCallBack_LOS()
    {

        super("MerchantService.v5/callback/lmsCallBack_LOS/leadRequest.json",
        		"MerchantService.v5/callback/lmsCallBack_LOS/leadResponse.json",
        		"MerchantService.v5/callback/lmsCallBack_LOS/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	
	//Query Param
	String leadId;
	String solution;
	
	//Headers
	String Authorization;
	String custId;
	String ContentType;
	
	//Request body Paramter
	String displayMessage;
	String refId;
	Integer statusCode;
	
	public String getLeadId() {
		return leadId;
	}
	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}
	public String getSolution() {
		return solution;
	}
	public void setSolution(String solution) {
		this.solution = solution;
	}
	public String getAuthorization() {
		return Authorization;
	}
	public void setAuthorization(String authorization) {
		Authorization = authorization;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getContentType() {
		return ContentType;
	}
	public void setContentType(String contentType) {
		ContentType = contentType;
	}
	public String getDisplayMessage() {
		return displayMessage;
	}
	public void setDisplayMessage(String displayMessage) {
		this.displayMessage = displayMessage;
	}
	public String getRefId() {
		return refId;
	}
	public void setRefId(String refId) {
		this.refId = refId;
	}

	public Integer getStatusCode() {
		return statusCode;
	}
	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}
	@Override
	public String toString() {
		return "lmsCallBack_LOS [leadId=" + leadId + ", solution=" + solution + ", Authorization=" + Authorization
				+ ", custId=" + custId + ", ContentType=" + ContentType + ", displayMessage=" + displayMessage
				+ ", refId=" + refId + ", statusCode=" + statusCode + "]";
	}
	

}
