package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class callbackLending extends AbstractApiV2{
	   
		public callbackLending()
	    {
			   super("MerchantService/v5/callback/OTPCallbackRequest.json", "MerchantService/v5/callback/OTPCallbackResponse.json", "MerchantService/v5/callback/OTPCallbackProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public callbackLending(boolean flag)
	    {
			   super("MerchantService/v5/callback/BREValidationPendingRequest.json", "MerchantService/v5/callback/BREValidationPendingResponse.json", "MerchantService/v5/callback/BREValidationPendingProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public callbackLending(String status)
	    {
			   super("MerchantService/v5/callback/LoanStatusCallbackRequest.json", "MerchantService/v5/callback/LoanStatusCallbackResponse.json", "MerchantService/v5/callback/LoanStatusCallbackProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}	
