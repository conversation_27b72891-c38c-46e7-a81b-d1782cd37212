package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PDCLoanApplicationCallback  extends AbstractApiV2{
	   
		public PDCLoanApplicationCallback()
	    {
			 super("MerchantService/v5/callback/PDCCallbackRequest.json", "MerchantService/v5/callback/PDCCallbackResponse.json", "MerchantService/v5/callback/PDCCallbackProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public PDCLoanApplicationCallback(boolean isPersonalLoan)
	    {
			 super("MerchantService/v5/callback/PDCForHeroRequest.json", "MerchantService/v5/callback/PDCForHeroResponse.json", "MerchantService/v5/callback/PDCForHeroProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
}
