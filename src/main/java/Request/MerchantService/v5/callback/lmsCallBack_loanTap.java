package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class lmsCallBack_loanTap extends AbstractApiV2

{
    public lmsCallBack_loanTap()
    {
        super("MerchantService.v5/callback/lmsCallBack_loanTap/leadRequest.json","MerchantService.v5/callback/lmsCallBack_loanTap/leadResponse.json","MerchantService.v5/callback/lmsCallBack_loanTap/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}

