package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AdditionalDataCapture extends AbstractApiV2{
	   
		public AdditionalDataCapture()
	    {
			   super("MerchantService/v5/callback/AdditionalDataCaptureRequest.json", "MerchantService/v5/callback/AdditionalDataCaptureResponse.json", "MerchantService/v5/callback/AdditionalDataCaptureProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}
