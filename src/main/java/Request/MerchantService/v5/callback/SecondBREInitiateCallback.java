package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SecondBREInitiateCallback extends AbstractApiV2{
	
	
	public SecondBREInitiateCallback()
    {
		   super("MerchantService/v5/callback/SecondBREInitaiteRequest.json", "MerchantService/v5/callback/SecondBREInitaiteResponse.json", "MerchantService/v5/callback/SecondBREInitaiteProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	public SecondBREInitiateCallback(boolean isAdditionalUpdate)
    {
		   super("MerchantService/v5/callback/AdditionalDataRequest.json", "MerchantService/v5/callback/AdditionalDataResponse.json", "MerchantService/v5/callback/AdditionalDataProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	

}
