package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class alternateNoCallBack_LOS extends AbstractApiV2 {

	public alternateNoCallBack_LOS() {

		super("MerchantServicev5callbackforAlternateNumber/alternateNoCallBack_LOS/leadRequest.json",
				"MerchantServicev5callbackforAlternateNumber/alternateNoCallBack_LOS/leadResponse.json",
				"MerchantServicev5callbackforAlternateNumber/alternateNoCallBack_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}

	// QueryParams
	String solution;

	String leadId;

	// Headers
	String ContentType;
	String Authorization;
	String custId;
	String channel;

	// Response body fields
	Integer statusCode;
	String refId;
	String displayMessage;

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getLeadId() {
		return leadId;
	}

	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}

	public String getContentType() {
		return ContentType;
	}

	public void setContentType(String contentType) {
		ContentType = contentType;
	}

	public String getAuthorization() {
		return Authorization;
	}

	public void setAuthorization(String authorization) {
		Authorization = authorization;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public Integer getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	public String getDisplayMessage() {
		return displayMessage;
	}

	public void setDisplayMessage(String displayMessage) {
		this.displayMessage = displayMessage;
	}

	@Override
	public String toString() {
		return "alternateNoCallBack_LOS [solution=" + solution + ", leadId=" + leadId + ", ContentType=" + ContentType
				+ ", Authorization=" + Authorization + ", custId=" + custId + ", channel=" + channel + ", statusCode="
				+ statusCode + ", refId=" + refId + ", displayMessage=" + displayMessage + "]";
	}

}
