package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PPBLOTPCallback extends AbstractApiV2{
	   
		public PPBLOTPCallback()
	    {
			   super("MerchantService/v5/callback/PPBLOTPRequest.json", "MerchantService/v5/callback/PPBLOTPResponse.json", "MerchantService/v5/callback/PPBLOTPProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

		
		public PPBLOTPCallback(boolean flag)
	    {
			   super("MerchantService/v5/callback/ARILLendingOtpVerificationRequest.json", "MerchantService/v5/callback/PPBLOTPResponse.json", "MerchantService/v5/callback/PPBLOTPProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

		
		public PPBLOTPCallback(boolean flag,boolean tnc)
	    {
			   super("MerchantService/v5/callback/ARILLendingLeadTncRequest.json", "MerchantService/v5/callback/PPBLOTPResponse.json", "MerchantService/v5/callback/PPBLOTPProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

	
		
		public PPBLOTPCallback(String solution)
	    {
			   super("MerchantService/v5/callback/ARILendingOtpVerificationRequest.json", "MerchantService/v5/callback/PPBLOTPResponse.json", "MerchantService/v5/callback/PPBLOTPProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

		
		
}