package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EmandateCallback extends AbstractApiV2{
	   
		public EmandateCallback()
	    {
			   super("MerchantService/v5/callback/EmandateRequest.json", "MerchantService/v5/callback/EmandateResponse.json", "MerchantService/v5/callback/EmandateProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public EmandateCallback(String requestBodyJsonPath)
	    {
			   super(requestBodyJsonPath, "MerchantService/v5/callback/EmandateResponse.json", "MerchantService/v5/callback/EmandateProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

}