package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EmailCallback  extends AbstractApiV2{
	   
		public EmailCallback()
	    {
			   super("MerchantService/v5/callback/EmailCallbackRequest.json", "MerchantService/v5/callback/EmailCallbackResponse.json", "MerchantService/v5/callback/EmailCallbackProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

}
