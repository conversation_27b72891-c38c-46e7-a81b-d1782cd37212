package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CallBackv5_posAgent extends AbstractApiV2
{
    public CallBackv5_posAgent()
    {
        super("MerchantService/v5/callback_posAgent/leadRequest.json","MerchantService/v5/callback_posAgent/leadResponse.json","MerchantService/v5/callback_posAgent/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}
