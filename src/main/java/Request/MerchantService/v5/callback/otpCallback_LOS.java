package Request.MerchantService.v5.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class otpCallback_LOS extends AbstractApiV2 {
	

	public otpCallback_LOS()
    {

        super("MerchantServicev5callbackforSaveOTP/otpCallBack_LOS/leadRequest.json",
        		"MerchantServicev5callbackforSaveOTP/otpCallBack_LOS/leadResponse.json",
        		"MerchantServicev5callbackforSaveOTP/otpCallBack_LOS/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	
	
//QueryParams
 String solution;
 String leadId;

 
 //Headers
 String ContentType;
 String Authorization;
 String custId;
 String channel;
 
 // Response  body fields
 Integer statusCode;
 String refId;
 String displayMessage;
 
 public String getSolution() {
     return solution;
 }

 public void setSolution(String solution) {
     this.solution = solution;
 }
 
 public String getLeadId() {
     return leadId;
 }

 public void setLeadId(String leadId) {
     this.leadId = leadId;
 }
 
 public String getRefId() {
     return refId;
 }

 public void setRefId(String refId) {
     this.refId = refId;
 }
 
 public Integer getStatusCode() {
     return statusCode;
 }

 public void setStatusCode(Integer statusCode) {
     this.statusCode = statusCode;
 }
 
 public String getChannel() {
     return channel;
 }

 public void setChannel(String channel) {
     this.channel = channel;
 }

 
 public String getAuthourizatioin() {
     return Authorization;
 }

 public void setAuthourization(String Authorization) {
     this.Authorization = Authorization;
 }

 public String getCustomerId() {
     return custId;
 }

 public void setCustomerId(String custId) {
     this.custId = custId;
 }
 

 public String getDisplayMessage() {
     return displayMessage;
 }

 public void setDisplayMessage(String displayMessage) {
     this.displayMessage = displayMessage;
 }
 
 @Override
 public String toString()
 {
     return "lead{" +
             "solution='" + solution + '\'' +
             ", channel='" + channel + '\'' +
             ", ContentType='" + ContentType + '\'' +
             ", statusCode=" + statusCode +
             ", leadId='" + leadId + '\'' +
             ", refId='" + refId + '\'' +
             '}';
 }

	

}
