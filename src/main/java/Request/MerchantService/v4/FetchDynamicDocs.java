package Request.MerchantService.v4;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchDynamicDocs extends AbstractApiV2
{
    public FetchDynamicDocs()
    {
        super("MerchantService/V4/PennyDropMultiNameMatch/PennyDropMultiNameRequest.json", "MerchantService/V4/PennyDropMultiNameMatch/PennyDropMultiNameResponse.json", "MerchantService/V4/PennyDropMultiNameMatch/PennyDropMultiNameProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
