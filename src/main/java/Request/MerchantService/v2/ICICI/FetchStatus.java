package Request.MerchantService.v2.ICICI;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchStatus extends AbstractApiV2
{
    public FetchStatus()
    {
        super("MerchantService/V2/ICICI/FetchStatus/FetchStatusRequest.json","MerchantService/V2/ICICI/FetchStatus/FetchStatusResponse.json","MerchantService/V2/ICICI/FetchStatus/FetchStatusProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
