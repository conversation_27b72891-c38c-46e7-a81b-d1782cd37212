package Request.MerchantService.v2.ICICI;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadWithGST extends AbstractApiV2
{
    public CreateLeadWithGST()
    {
        super("MerchantService/V2/ICICI/CreateLeadWithGSTIN/CreateLeadWithGSTINRequest.json","MerchantService/V2/ICICI/CreateLeadWithGSTIN/CreateLeadWithGSTINResponse.json","MerchantService/V2/ICICI/CreateLeadWithGSTIN/CreateLeadWithGSTINProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
