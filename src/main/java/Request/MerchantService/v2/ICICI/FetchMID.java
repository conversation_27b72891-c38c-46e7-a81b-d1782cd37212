package Request.MerchantService.v2.ICICI;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchMID extends AbstractApiV2
{
    public FetchMID()
    {
        super("MerchantService/V2/ICICI/FetchMID/FetchMIDRequest.json","MerchantService/V2/ICICI/FetchMID/FetchMIDResponse.json","MerchantService/V2/ICICI/FetchMID/FetchMIDProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
