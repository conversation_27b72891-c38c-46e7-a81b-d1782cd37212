package Request.MerchantService.v2.ICICI;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadWithOutGSTIN extends AbstractApiV2
{
    public CreateLeadWithOutGSTIN()
    {
        super("MerchantService/V2/ICICI/CreateLeadWithoutGSTIN/CreateLeadWithoutGSTINRequest.json","MerchantService/V2/ICICI/CreateLeadWithoutGSTIN/CreateLeadWithoutGSTINResponse.json","MerchantService/V2/ICICI/CreateLeadWithoutGSTIN/CreateLeadWithoutGSTINProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
