package Request.MerchantService.v2.edc.plans;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchPlanEdcDIY extends AbstractApiV2
{
    public  fetchPlanEdcDIY()
    {

        super("MerchantService/V2/edc/plans/fetchPlanEdcDIYLeadRequest.json","MerchantService/V2/edc/plans/fetchPlanEdcDIYLeadResponse.json","MerchantService/V2/edc/plans/fetchPlanEdcDIYLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }
}
