package Request.MerchantService.v2.edc.validateOrder;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class validateOrderEdcDIY extends AbstractApiV2
{
    public  validateOrderEdcDIY(String ReqPath)
    {

        super(ReqPath, "MerchantService/V2/edc.validateOrder/validateOrderEdcDIYResponse.json","MerchantService/V2/edc.validateOrder/validateOrderEdcDIYProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }
}
