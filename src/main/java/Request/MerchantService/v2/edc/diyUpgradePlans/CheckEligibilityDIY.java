package Request.MerchantService.v2.edc.diyUpgradePlans;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CheckEligibilityDIY  extends AbstractApiV2
{
    public CheckEligibilityDIY()
    {
        super("MerchantService/V2/edc.checkEligibilityDIY/checkEligibiltyDIYRequest.json", "MerchantService/V2/edc.checkEligibilityDIY/checkEligibiltyDIYResponse.json", "MerchantService/V2/edc.checkEligibilityDIY/checkEligibiltyDIYProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}