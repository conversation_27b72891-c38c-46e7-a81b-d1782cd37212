package Request.MerchantService.v2;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Banks extends AbstractApiV2 {

    public Banks(String IFSC)
    {
        super("MerchantService/V2/Banks/BanksRequest.json", "MerchantService/V2/Banks/BanksResponse.json", "MerchantService/V2/Banks/BanksResponseSchema.json");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("ifsc", IFSC);
    }
}
