package Request.MerchantService.v2.upgradeMid.doc;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchDocumentStatus extends AbstractApiV2
{
    public FetchDocumentStatus()
    {
        super("MerchantService/V2/UpgradeMid/Doc/Status/DocStatusRequest.json","MerchantService/V2/UpgradeMid/Doc/Status/DocStatusResponse.json","MerchantService/V2/UpgradeMid/Doc/Status/DocStatusProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
