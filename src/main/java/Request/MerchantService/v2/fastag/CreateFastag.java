package Request.MerchantService.v2.fastag;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateFastag extends AbstractApiV2 {

    public CreateFastag()

    {
        super("MerchantService/V2/Fastag/PostFastag/PostFastagRequest.json", "MerchantService/V2/Fastag/PostFastag/PostFastagResponse.json", "MerchantService/V2/Fastag/PostFastag/PostFastagProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}