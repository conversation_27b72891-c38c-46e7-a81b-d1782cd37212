package Request.MerchantService.v2.fastag;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchIssuance extends AbstractApiV2 {

    public FetchIssuance()

    {
        super("MerchantService/V2/Fastag/FetchIssuance/FetchIssuanceRequest.json", "MerchantService/V2/Fastag/FetchIssuance/FetchIssuanceResponse.json", "MerchantService/V2/Fastag/FetchIssuance/FetchIssuanceProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
