package Request.MerchantService.v2.fastag;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchFastagPayment extends AbstractApiV2 {

    public FetchFastagPayment()

    {
        super("MerchantService/V2/Fastag/FetchIssuance/FetchIssuanceRequest.json", "MerchantService/V2/Fastag/FetchIssuance/FetchIssuanceResponse.json", "MerchantService/V2/Fastag/FetchIssuance/FetchIssuanceProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}