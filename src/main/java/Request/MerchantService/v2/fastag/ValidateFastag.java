package Request.MerchantService.v2.fastag;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateFastag extends AbstractApiV2 {

    public ValidateFastag()

    {
        super("MerchantService/V2/Fastag/ValidateFastag/ValidateFastagRequest.json", "MerchantService/V2/Fastag/ValidateFastag/ValidateFastagResponse.json", "MerchantService/V2/Fastag/ValidateFastag/ValidateFastagProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
