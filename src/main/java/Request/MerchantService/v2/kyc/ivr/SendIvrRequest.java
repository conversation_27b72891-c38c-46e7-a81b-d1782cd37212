package Request.MerchantService.v2.kyc.ivr;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SendIvrRequest extends AbstractApiV2 {

    public SendIvrRequest(String platform) {
        super("MerchantService/v2/kyc/ivr/SendIvrRequest/SendIvrRequestRequest.json","MerchantService/v2/kyc/ivr/SendIvrRequest/SendIvrRequestResponse.json","MerchantService/v2/kyc/ivr/SendIvrRequest/SendIvrRequestProperties.properties");
        if(platform.equals("PPBL")) {
            replaceUrlPlaceholder("base_url", P.API.get("bank_api_url"));
        }else
            replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

    public String getResendRequest() {
        return resendRequest;
    }

    public void setResendRequest(String resendRequest) {
        this.resendRequest = resendRequest;
    }

    public String getXSRC() {
        return XSRC;
    }

    public void setXSRC(String XSRC) {
        this.XSRC = XSRC;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSession_token() {
        return session_token;
    }

    public void setSession_token(String session_token) {
        this.session_token = session_token;
    }

    public String getDeviceIdentifier() {
        return deviceIdentifier;
    }

    public void setDeviceIdentifier(String deviceIdentifier) {
        this.deviceIdentifier = deviceIdentifier;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getUncleScrooge() {
        return UncleScrooge;
    }

    public void setUncleScrooge(String UncleScrooge) {
        this.UncleScrooge = UncleScrooge;
    }

    public String getDeviceManufacturer() {
        return deviceManufacturer;
    }

    public void setDeviceManufacturer(String deviceManufacturer) {
        this.deviceManufacturer = deviceManufacturer;
    }

    public String getAppLanguage() {
        return appLanguage;
    }

    public void setAppLanguage(String appLanguage) {
        this.appLanguage = appLanguage;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public boolean isAgentTncStatus() {
        return agentTncStatus;
    }

    public void setAgentTncStatus(boolean agentTncStatus) {
        this.agentTncStatus = agentTncStatus;
    }

    public boolean isAgentKycStatus() {
        return agentKycStatus;
    }

    public void setAgentKycStatus(boolean agentKycStatus) {
        this.agentKycStatus = agentKycStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isIvrFlow() {
        return ivrFlow;
    }

    public void setIvrFlow(boolean ivrFlow) {
        this.ivrFlow = ivrFlow;
    }

    public Integer getRequestTimeStamp() {
        return requestTimeStamp;
    }

    public void setRequestTimeStamp(Integer requestTimeStamp) {
        this.requestTimeStamp = requestTimeStamp;
    }

    public Integer getMinRequestLimit() {
        return minRequestLimit;
    }

    public void setMinRequestLimit(Integer minRequestLimit) {
        this.minRequestLimit = minRequestLimit;
    }

    public Integer getMaxRequestLimit() {
        return maxRequestLimit;
    }

    public void setMaxRequestLimit(Integer maxRequestLimit) {
        this.maxRequestLimit = maxRequestLimit;
    }

    public Integer getMaxRequestTime() {
        return maxRequestTime;
    }

    public void setMaxRequestTime(Integer maxRequestTime) {
        this.maxRequestTime = maxRequestTime;
    }

    public Integer getRequestWaitTime() {
        return requestWaitTime;
    }

    public void setRequestWaitTime(Integer requestWaitTime) {
        this.requestWaitTime = requestWaitTime;
    }

    public boolean isOtpEnable() {
        return otpEnable;
    }

    public void setOtpEnable(boolean otpEnable) {
        this.otpEnable = otpEnable;
    }

    public String getIvrNumber() {
        return ivrNumber;
    }

    public void setIvrNumber(String ivrNumber) {
        this.ivrNumber = ivrNumber;
    }

    public boolean isPrivilegeCustomer() {
        return privilegeCustomer;
    }

    public void setPrivilegeCustomer(boolean privilegeCustomer) {
        this.privilegeCustomer = privilegeCustomer;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    //Query Params
    String resendRequest;

    //Headers
    String XSRC;
    String latitude;
    String channel;
    String deviceName;
    String version;
    String session_token;
    String deviceIdentifier;
    String osVersion;
    String client;
    String imei;
    String UncleScrooge;
    String deviceManufacturer;
    String appLanguage;
    String longitude;
    String ContentType;

    // Request Body Params
    String mobileNumber;
    String action;

    //Response Body Params
    boolean agentTncStatus;
    boolean agentKycStatus;
    String status;
    boolean ivrFlow;
    Integer requestTimeStamp;
    Integer minRequestLimit;
    Integer maxRequestLimit;
    Integer maxRequestTime;
    Integer requestWaitTime;
    boolean otpEnable;
    String ivrNumber;
    boolean privilegeCustomer;
    String message;
    String errorCode;

}
