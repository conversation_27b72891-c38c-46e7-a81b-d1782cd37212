package Request.MerchantService.v2.lending.lead.fetchCIR;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchCIR extends AbstractApiV2{
	   
	public FetchCIR()
{
		   super("MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json", "MerchantService/v2/lending/lead/fetchCIR/FetchCIRResponse.json", "MerchantService/V2/lending/lead/fetchCIR/FetchCIRProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
}
}
