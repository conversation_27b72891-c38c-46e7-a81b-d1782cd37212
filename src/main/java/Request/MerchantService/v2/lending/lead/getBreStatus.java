package Request.MerchantService.v2.lending.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class getBreStatus extends AbstractApiV2
{
	   
		public getBreStatus()
	    {
			   super("MerchantService/v2/lending/getBREStatus/GetBREStatusRequest.json", "MerchantService/v2/lending/getBREStatus/GetBREStatusResponse.json", "MerchantService/v2/lending/getBREStatus/GetBREStatusProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}