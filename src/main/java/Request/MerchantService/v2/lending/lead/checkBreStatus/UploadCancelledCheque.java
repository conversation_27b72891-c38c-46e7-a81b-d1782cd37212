package Request.MerchantService.v2.lending.lead.checkBreStatus;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UploadCancelledCheque extends AbstractApiV2{
	   
		public UploadCancelledCheque()
	    {
			   super("MerchantService/v2/upgradeMid/doc/UploadCCRequest.json", "MerchantService/v2/upgradeMid/doc/UploadCCResponse.json", "MerchantService/v2/upgradeMid/doc/UploadCCProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

}