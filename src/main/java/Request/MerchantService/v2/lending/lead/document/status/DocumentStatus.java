package Request.MerchantService.v2.lending.lead.document.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DocumentStatus extends AbstractApiV2 {

	  public DocumentStatus (String jsonPath) {
		  
	        super(jsonPath,"MerchantService/v2/lending/lead/document/UploadDocumentResponse.json","MerchantService/v2/lending/lead/document/UploadDocumentProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	}