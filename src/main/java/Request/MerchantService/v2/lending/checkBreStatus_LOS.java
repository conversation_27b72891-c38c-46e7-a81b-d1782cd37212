package Request.MerchantService.v2.lending;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class checkBreStatus_LOS extends AbstractApiV2{
	
	public checkBreStatus_LOS() {
		super("MerchantServicev2LendingLead/checkBreStatus_LOS/leadRequest.json",
				"MerchantServicev2LendingLead/checkBreStatus_LOS/leadResponse.json",
				"MerchantServicev2LendingLead/checkBreStatus_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
	
	
	//Query Param
	String solution;
	String entityType;
	String channel;
	String solutionTypeLevel2;
	
	//headers
	String session_token;
	
	
	//Response body hearders
	String refId;
	Integer statusCode;
	String stage;
	Integer creditScore;
	Long lastFetchDate;
	boolean loanOffered;
	String maxLoanAmount;
	String minLoanAmount;
	String bureau;
	
	
	public String getSolution() {
		return solution;
	}
	public void setSolution(String solution) {
		this.solution = solution;
	}
	public String getEntityType() {
		return entityType;
	}
	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public String getSolutionTypeLevel2() {
		return solutionTypeLevel2;
	}
	public void setSolutionTypeLevel2(String solutionTypeLevel2) {
		this.solutionTypeLevel2 = solutionTypeLevel2;
	}
	public String getSession_token() {
		return session_token;
	}
	public void setSession_token(String session_token) {
		this.session_token = session_token;
	}
	public String getRefId() {
		return refId;
	}
	public void setRefId(String refId) {
		this.refId = refId;
	}
	public Integer getStatusCode() {
		return statusCode;
	}
	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}
	public String getStage() {
		return stage;
	}
	public void setStage(String stage) {
		this.stage = stage;
	}
	public Integer getCreditScore() {
		return creditScore;
	}
	public void setCreditScore(Integer creditScore) {
		this.creditScore = creditScore;
	}
	public Long getLastFetchDate() {
		return lastFetchDate;
	}
	public void setLastFetchDate(Long lastFetchDate) {
		this.lastFetchDate = lastFetchDate;
	}
	public boolean isLoanOffered() {
		return loanOffered;
	}
	public void setLoanOffered(boolean loanOffered) {
		this.loanOffered = loanOffered;
	}
	public String getMaxLoanAmount() {
		return maxLoanAmount;
	}
	public void setMaxLoanAmount(String maxLoanAmount) {
		this.maxLoanAmount = maxLoanAmount;
	}
	public String getMinLoanAmount() {
		return minLoanAmount;
	}
	public void setMinLoanAmount(String minLoanAmount) {
		this.minLoanAmount = minLoanAmount;
	}
	public String getBureau() {
		return bureau;
	}
	public void setBureau(String bureau) {
		this.bureau = bureau;
	}
	@Override
	public String toString() {
		return "checkBreStatus_LOS [solution=" + solution + ", entityType=" + entityType + ", channel=" + channel
				+ ", solutionTypeLevel2=" + solutionTypeLevel2 + ", session_token=" + session_token + ", refId=" + refId
				+ ", statusCode=" + statusCode + ", stage=" + stage + ", creditScore=" + creditScore
				+ ", lastFetchDate=" + lastFetchDate + ", loanOffered=" + loanOffered + ", maxLoanAmount="
				+ maxLoanAmount + ", minLoanAmount=" + minLoanAmount + ", bureau=" + bureau + "]";
	}
	
	
	
	
	
	
	
	
	

}
