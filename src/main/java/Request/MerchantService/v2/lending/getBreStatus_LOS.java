package Request.MerchantService.v2.lending;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class getBreStatus_LOS extends AbstractApiV2 {

	public getBreStatus_LOS() {
		super("MerchantServicev2lendingGetBreStatus/getBreStatus_LOS/leadRequest.json",
				"MerchantServicev2lendingGetBreStatus/getBreStatus_LOS/leadRequest.json",
				"MerchantServicev2lendingGetBreStatus/getBreStatus_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}

	// QueryParam

	String solution;
	String entityType;
	String channel;
	String solutionTypeLevel2;
	String email;

	// Headers
	String ContentType;
	String session_token;

	// Response body fields
	Integer statusCode;
	String refId;

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getEntityType() {
		return entityType;
	}

	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getSolutionTypeLevel2() {
		return solutionTypeLevel2;
	}

	public void setSolutionTypeLevel2(String solutionTypeLevel2) {
		this.solutionTypeLevel2 = solutionTypeLevel2;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getContentType() {
		return ContentType;
	}

	public void setContentType(String contentType) {
		ContentType = contentType;
	}

	public String getSession_token() {
		return session_token;
	}

	public void setSession_token(String session_token) {
		this.session_token = session_token;
	}

	public Integer getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	@Override
	public String toString() {
		return "getBreStatus_LOS [solution=" + solution + ", entityType=" + entityType + ", channel=" + channel
				+ ", solutionTypeLevel2=" + solutionTypeLevel2 + ", email=" + email + ", ContentType=" + ContentType
				+ ", session_token=" + session_token + ", statusCode=" + statusCode + ", refId=" + refId + "]";
	}

}
