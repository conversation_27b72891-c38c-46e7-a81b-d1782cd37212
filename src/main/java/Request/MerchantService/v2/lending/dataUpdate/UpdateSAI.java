package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateSAI extends AbstractApiV2{
	   
	public UpdateSAI()
 {
		   super("MerchantService/v2/lending/dataUpdate/UpdateSAIRequest.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIResponse.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
 }
	
	public UpdateSAI(boolean isBureauDataUpdate)
 {
		   super("MerchantService/V2/lending/dataUpdate/BureauPullDataSetRequest.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIResponse.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
 }

	public UpdateSAI(String requestData)
 {
		   super(requestData, "MerchantService/v2/lending/dataUpdate/UpdateSAIResponse.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
 }

	public UpdateSAI(String requestData , boolean isBureauDataUpdate )
	{
		super(requestData, "MerchantService/v2/lending/dataUpdate/UpdateSAIResponse.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIProperties.properties");

		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
}	


