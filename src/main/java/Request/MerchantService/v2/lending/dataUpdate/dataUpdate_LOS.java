package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class dataUpdate_LOS extends AbstractApiV2 {
	public dataUpdate_LOS() {
		super("MerchantServicev2LendingDataUpdate/dataUpdate_LOS/leadRequest.json",
				"MerchantServicev2LendingDataUpdate/dataUpdate_LOS/leadRequest.json",
				"MerchantServicev2LendingDataUpdate/dataUpdate_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));

	}

	// QueryParam

	String solution;
	String entityType;
	String channel;
	String solutionTypeLevel2;
	String leadId;

	// Headers
	String ContentType;
	String Authorization;
	String custId;

	// Response body fields
	Integer statusCode;
	String refId;
	String displayMessage;
	String oeStage;

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getEntityType() {
		return entityType;
	}

	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getSolutionTypeLevel2() {
		return solutionTypeLevel2;
	}

	public void setSolutionTypeLevel2(String solutionTypeLevel2) {
		this.solutionTypeLevel2 = solutionTypeLevel2;
	}

	public String getLeadId() {
		return leadId;
	}

	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}

	public String getContentType() {
		return ContentType;
	}

	public void setContentType(String contentType) {
		ContentType = contentType;
	}

	public String getAuthorization() {
		return Authorization;
	}

	public void setAuthorization(String authorization) {
		Authorization = authorization;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public Integer getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	public String getDisplayMessage() {
		return displayMessage;
	}

	public void setDisplayMessage(String displayMessage) {
		this.displayMessage = displayMessage;
	}
	
	public String getOeStage() {
		return oeStage;
	}

	public void setOeStage(String oeStage) {
		this.oeStage = oeStage;
	}

	@Override
	public String toString() {
		return "dataUpdate_LOS [solution=" + solution + ", entityType=" + entityType + ", channel=" + channel
				+ ", solutionTypeLevel2=" + solutionTypeLevel2 + ", leadId=" + leadId + ", ContentType=" + ContentType
				+ ", Authorization=" + Authorization + ", custId=" + custId + ", statusCode=" + statusCode + ", refId="
				+ refId + ", displayMessage=" + displayMessage + ", oeStage=" + oeStage + "]";
	}


}
