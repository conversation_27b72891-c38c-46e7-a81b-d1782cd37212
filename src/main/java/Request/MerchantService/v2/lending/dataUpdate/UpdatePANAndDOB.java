package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdatePANAndDOB extends AbstractApiV2{
	
	public UpdatePANAndDOB()
    {
		   super("MerchantService/v2/lending/dataUpdate/PanDOBUpdateRequest.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

	
	public UpdatePANAndDOB(boolean solutionData, boolean AddressDetails)
    {
		   super("MerchantService/v2/lending/dataUpdate/AdditionalInfoUpdateRequest.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	public UpdatePANAndDOB( boolean AddressDetails)
    {
		   super("MerchantService/v2/lending/dataUpdate/AdditionalDataCaptureAddressRequest.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	public UpdatePANAndDOB( boolean AddressDetails, String solutionTypeLevel2SSFB)
    {
		   super("MerchantService/v2/lending/dataUpdate/AddDataCaptureSSFBRequest.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/PanDOBUpdateProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	
}
