package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CKYCCallBack extends AbstractApiV2 {

	  public CKYCCallBack () {
	        super("MerchantService/v2/lending/dataUpdate/CkycCallbackRequest.json","MerchantService/v2/lending/dataUpdate/CkycCallbackResponse.json","MerchantService/v2/lending/dataUpdate/CkycCallbackProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	  

	  public CKYCCallBack(String requestBodyPath,String solution) {
		  	super(requestBodyPath,"MerchantService/v2/lending/dataUpdate/KYCCallbackTopupResponse.json","MerchantService/v2/lending/dataUpdate/KYCCallbackTopupProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	  }
	  public CKYCCallBack (String isTopup) {
	        super("MerchantService/v2/lending/dataUpdate/KYCCallbackTopupRequest.json","MerchantService/v2/lending/dataUpdate/KYCCallbackTopupResponse.json","MerchantService/v2/lending/dataUpdate/KYCCallbackTopupProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	  
	  
	  public CKYCCallBack (String requestJSON, boolean isNewKycKeys) {
		    super(requestJSON,"MerchantService/v2/lending/dataUpdate/CkycCallbackResponse.json","MerchantService/v2/lending/dataUpdate/CkycCallbackProperties.properties");
		    replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}


}
