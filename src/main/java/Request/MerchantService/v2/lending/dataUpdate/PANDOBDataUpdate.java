package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PANDOBDataUpdate extends AbstractApiV2{
	   
		public PANDOBDataUpdate()
	    {
			   super("MerchantService/v2/lending/dataUpdate/AdditionalPanUpdateRequest.json", "MerchantService/v2/lending/dataUpdate/AdditionalPanUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/AdditionalPanUpdateProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		

		public PANDOBDataUpdate(Boolean isDOBUpdate)
	    {
			   super("MerchantService/v2/lending/dataUpdate/AdditionalDobUpdateRequest.json", "MerchantService/v2/lending/dataUpdate/AdditionalDobUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/AdditionalDobUpdateProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public PANDOBDataUpdate(String solutionType)
	    {
			   super("MerchantService/v2/lending/dataUpdate/BasicDetailsHeroRequest.json", "MerchantService/v2/lending/dataUpdate/BasicDetailsHeroResponse.json", "MerchantService/v2/lending/dataUpdate/BasicDetailsHeroProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		public PANDOBDataUpdate(String solutionType,boolean isSecondBREUpdate)
	    {
			   super("MerchantService/v2/lending/dataUpdate/SecondBREStatusUpdateRequest.json", "MerchantService/v2/lending/dataUpdate/SecondBREStatusUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/SecondBREStatusUpdateProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public PANDOBDataUpdate(String solutionType,String requestBodyJsonPath)
	    {
			   super(requestBodyJsonPath, "MerchantService/v2/lending/dataUpdate/SecondBREStatusUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/SecondBREStatusUpdateProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		public PANDOBDataUpdate(boolean isThirdBRE,boolean isBRECallback)
	    {
			   super("MerchantService/v2/lending/dataUpdate/ThirdBREStatusCallbackRequest.jsonn", "MerchantService/v2/lending/dataUpdate/SecondBREStatusUpdateResponse.json", "MerchantService/v2/lending/dataUpdate/SecondBREStatusUpdateProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
}
