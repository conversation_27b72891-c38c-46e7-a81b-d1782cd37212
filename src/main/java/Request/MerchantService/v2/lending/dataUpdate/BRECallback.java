package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BRECallback extends AbstractApiV2{
	   
		public BRECallback()
	    {
			   super("MerchantService/v2/lending/dataUpdate/BRECallbackRequest.json", "MerchantService/v2/lending/dataUpdate/BRECallbackResponse.json", "MerchantService/v2/lending/dataUpdate/BRECallbackProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}