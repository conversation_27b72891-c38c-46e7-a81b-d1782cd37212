package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AdditionalDetailsTopup extends AbstractApiV2{
	
	public AdditionalDetailsTopup()
    {
		   super("MerchantService/v2/lending/dataUpdate/AdditionalDetailsTopupRequest.json", "MerchantService/v2/lending/dataUpdate/AdditionalDetailsTopupResponse.json", "MerchantService/v2/lending/dataUpdate/AdditionalDetailsTopupProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
