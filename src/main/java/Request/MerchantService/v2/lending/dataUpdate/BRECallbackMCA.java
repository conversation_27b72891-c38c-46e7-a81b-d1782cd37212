package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BRECallbackMCA extends AbstractApiV2{
	   
		public BRECallbackMCA()
	    {
			   super("MerchantService/v2/lending/dataUpdate/BRECallbackMCARequest.json", "MerchantService/v2/lending/dataUpdate/BRECallbackMCAResponse.json", "MerchantService/v2/lending/dataUpdate/BRECallbackMCAProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		

		public BRECallbackMCA(Boolean isSSFB)
	    {
			   super("MerchantService/v2/lending/dataUpdate/SSFBBRECallbackRequest.json", "MerchantService/v2/lending/dataUpdate/BRECallbackMCAResponse.json", "MerchantService/v2/lending/dataUpdate/BRECallbackMCAProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

	
		public BRECallbackMCA(boolean flag)
	    {
			   super("MerchantService/V2/lending/dataUpdate/BRECallbackARILRequest.json", 
					   "MerchantService/V2/lending/dataUpdate/BRECallbackArilResponse.json",
					    "MerchantService/V2/lending/dataUpdate/BRECallbackArilProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		public BRECallbackMCA(String requestJson)
	    {
			   super(requestJson, "MerchantService/v2/lending/dataUpdate/BRECallbackMCAResponse.json", "MerchantService/v2/lending/dataUpdate/BRECallbackMCAProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		


}