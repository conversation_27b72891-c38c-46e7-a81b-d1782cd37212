package Request.MerchantService.v2.lending.dataUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateGenderAndPincode extends AbstractApiV2{
	
	public UpdateGenderAndPincode()
    {
		   super("MerchantService/v2/lending/dataUpdate/GenderPincodeRequest.json", "MerchantService/v2/lending/dataUpdate/GenderPincodeResponse.json", "MerchantService/v2/lending/dataUpdate/GenderPincodeProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
