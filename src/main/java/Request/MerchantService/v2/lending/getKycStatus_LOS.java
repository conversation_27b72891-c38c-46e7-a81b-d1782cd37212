package Request.MerchantService.v2.lending;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class getKycStatus_LOS extends AbstractApiV2 {

	public getKycStatus_LOS() {
		super("MerchantServicev2LendinggetKycStatus/lending_LOS/leadRequest.json", "MerchantServicev2LendinggetKycStatus/lending_LOS/leadRequest.json",
				"MerchantServicev2LendinggetKycStatus/lending_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}

	// QueryParams
	String entityType;
	String channel;
	String solutionTypeLevel2;
	String dob;
	String solution;

	// Headers
	String ContentType;
	String session_token;

	// Response body fields
	Integer statusCode;
	String leadId;
	String refId;
	String stage;
	boolean panNameMatchSuccess;
	String kycStatus;

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getEntityType() {
		return entityType;
	}

	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getSolutionTypeLevel2() {
		return solutionTypeLevel2;
	}

	public void setSolutionTypeLevel2(String solutionTypeLevel2) {
		this.solutionTypeLevel2 = solutionTypeLevel2;
	}

	public String getDob() {
		return dob;
	}

	public void setDob(String dob) {
		this.dob = dob;
	}

	public String getContentType() {
		return ContentType;
	}

	public void setContentType(String contentType) {
		ContentType = contentType;
	}

	public String getSession_token() {
		return session_token;
	}

	public void setSession_token(String session_token) {
		this.session_token = session_token;
	}

	public Integer getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}

	public String getLeadId() {
		return leadId;
	}

	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	public String getStage() {
		return stage;
	}

	public void setStage(String stage) {
		this.stage = stage;
	}

	public boolean isPanNameMatchSuccess() {
		return panNameMatchSuccess;
	}

	public void setPanNameMatchSuccess(boolean panNameMatchSuccess) {
		this.panNameMatchSuccess = panNameMatchSuccess;
	}

	public String getKycStatus() {
		return kycStatus;
	}

	public void setKycStatus(String kycStatus) {
		this.kycStatus = kycStatus;
	}

	@Override
	public String toString() {
		return "getKycStatus_LOS [entityType=" + entityType + ", channel=" + channel + ", solutionTypeLevel2="
				+ solutionTypeLevel2 + ", dob=" + dob + ", solution=" + solution + ", ContentType=" + ContentType
				+ ", session_token=" + session_token + ", statusCode=" + statusCode + ", leadId=" + leadId + ", refId="
				+ refId + ", stage=" + stage + ", panNameMatchSuccess=" + panNameMatchSuccess + ", kycStatus="
				+ kycStatus + "]";
	}
}
