package Request.MerchantService.v2;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class TnC extends AbstractApiV2 {

    public  TnC()
    {
        super("MerchantService/V2/TnC/TnCRequest.json", "MerchantService/V2/TnC/TnCResponse.json", "MerchantService/V2/TnC/TnCProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
