package Request.MerchantService.v2.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class v2FetchDynamicTnc extends AbstractApiV2 {
    public  v2FetchDynamicTnc()
    {
        super("MerchantService/V2/lead/FetchDynamicTnC/SaveDynamicTnCRequest.json", "MerchantService/V2/lead/FetchDynamicTnC/SaveDynamicTnCResponse.json", "MerchantService/V2/lead/FetchDynamicTnC/SaveDynamicTnCProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
