package Request.MerchantService.v2.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class v2SaveDynamicTnc  extends AbstractApiV2 {
    public  v2SaveDynamicTnc()
    {
        super("MerchantService/V2/lead/SaveDynamicTnC/SaveDynamicTnCRequest.json", "MerchantService/V2/lead/SaveDynamicTnC/SaveDynamicTnCResponse.json", "MerchantService/V2/lead/SaveDynamicTnC/SaveDynamicTnCProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
