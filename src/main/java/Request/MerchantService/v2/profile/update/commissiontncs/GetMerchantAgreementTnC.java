package Request.MerchantService.v2.profile.update.commissiontncs;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetMerchantAgreementTnC extends AbstractApiV2 {
    public GetMerchantAgreementTnC()
    {
        super("MerchantService/V2/profile/update/commissiontncs/TnCRequest.json", "MerchantService/V2/profile/update/commissiontncs/TnCResponse.json", "MerchantService/V2/profile/update/commissiontncs/TnCProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}

