package Request.MerchantService.v2.doc.upload.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchDocStatus_posInsurance extends AbstractApiV2
{
   public  fetchDocStatus_posInsurance()
   {

       super("MerchantService/V2/doc/upload/status/fetchDocStatus/leadRequest.json","MerchantService/V2/doc/upload/status/fetchDocStatus/leadResponse.json","MerchantService/V2/doc/upload/status/fetchDocStatus/leadProperties.properties");
       replaceUrlPlaceholder("base_url", P.API.get("api_url"));


   }
}
