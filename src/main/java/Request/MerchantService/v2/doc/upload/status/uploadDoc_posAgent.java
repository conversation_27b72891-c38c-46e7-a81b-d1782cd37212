package Request.MerchantService.v2.doc.upload.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class uploadDoc_posAgent extends AbstractApiV2
{
    public uploadDoc_posAgent()
    {
        super("MerchantService/V2/doc/upload/status/fetchDocStatus/uploadDoc/leadRequest.json","MerchantService/V2/doc/upload/status/fetchDocStatus/uploadDoc/leadResponse.json","MerchantService/V2/doc/upload/status/fetchDocStatus/uploadDoc/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
