package Request.MerchantService.v2.ekyc.biometric;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundboxTnc extends AbstractApiV2
{
    public  SoundboxTnc()
    {

        super("MerchantService/V2/ekyc.biometric/fetchTnCSoundBoxRequest.json","MerchantService/V2/ekyc.biometric/fetchTnCSoundBoxResponse.json","MerchantService/V2/ekyc.biometric/fetchTnCSoundBoxProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }
}
