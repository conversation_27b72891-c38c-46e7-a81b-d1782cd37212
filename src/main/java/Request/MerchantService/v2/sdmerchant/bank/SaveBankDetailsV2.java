package Request.MerchantService.v2.sdmerchant.bank;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveBankDetailsV2  extends AbstractApiV2{
	   
	public SaveBankDetailsV2(boolean isEmandateTypePresent)
    {
		   super("MerchantService/V1/sdMerchant/Bank/SaveBankWithEmandateTypeRequest.json", "MerchantService/v1/sdMerchant/Bank/SaveBankResponse.json", "MerchantService/v1/sdMerchant/Bank/SaveBankProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
		
}
