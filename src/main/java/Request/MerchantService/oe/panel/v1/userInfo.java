package Request.MerchantService.oe.panel.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class userInfo extends AbstractApiV2
{
    public userInfo(String phoneNumber)
    {
        super("ManageAgent/searchAgent/searchAgentReq.json","ManageAgent/searchAgent/searchAgentRes.json","ManageAgent/searchAgent/searchAgentProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("phoneNumber", phoneNumber );
    }


    //Headers
/*
    String Accept;
    String Referer;
    String phonenumber;
    String ssoid;
    String Sec_Fetch_Mode;
    String User_Agent;
    String Origin;

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getSsoid() {
        return ssoid;
    }

    public void setSsoid(String ssoid) {
        this.ssoid = ssoid;
    }

    public String getSec_Fetch_Mode() {
        return Sec_Fetch_Mode;
    }

    public void setSec_Fetch_Mode(String sec_Fetch_Mode) {
        Sec_Fetch_Mode = sec_Fetch_Mode;
    }

    public String getUser_Agent() {
        return User_Agent;
    }

    public void setUser_Agent(String user_Agent) {
        User_Agent = user_Agent;
    }

    public String getOrigin() {
        return Origin;
    }

    public void setOrigin(String origin) {
        Origin = origin;
    }
   public String getAccept() {
        return Accept;
    }

    public void setAccept(String accept) {
        Accept = accept;
    }

    public String getReferer() {
        return Referer;
    }

    public void setReferer(String referer) {
        Referer = referer;
    }
*/




}
