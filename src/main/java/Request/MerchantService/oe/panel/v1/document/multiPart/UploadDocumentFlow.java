package Request.MerchantService.oe.panel.v1.document.multiPart;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UploadDocumentFlow extends AbstractApiV2 {

	  public UploadDocumentFlow () {
	        super("MerchantService/OE/Panel/v1/document/multiPart/UploadDocumentRequest.json","MerchantService/OE/Panel/v1/document/multiPart/UploadDocumentResponse.json","MerchantService/OE/Panel/v1/document/multiPart/UploadDocumentProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	}