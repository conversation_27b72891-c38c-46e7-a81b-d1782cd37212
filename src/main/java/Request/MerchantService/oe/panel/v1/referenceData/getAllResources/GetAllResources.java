package Request.MerchantService.oe.panel.v1.referenceData.getAllResources;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetAllResources extends AbstractApiV2{
	   
		public GetAllResources(String getAllResourcesRequest)
	    {
			   super("MakerChecker/ReviewService/GetallResources/GetallResourcesResponse.json", "MerchantService/OE/Panel/v1/referenceData/getAllResources/getAllResourcesResponse.json", "MerchantService/OE/Panel/v1/referenceData/getAllResources/getAllResourcesProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
}