package Request.MerchantService.oe.panel.v1.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchLead extends AbstractApiV2 {
    public FetchLead(String leadId)
    {
        super("MerchantService/OE/Panel/v1/FetchLeadRequest.json","MerchantService/OE/Panel/v1/FetchLeadResponse.json","MerchantService/OE/Panel/v1/FetchLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("leadId",leadId);
    }
}
