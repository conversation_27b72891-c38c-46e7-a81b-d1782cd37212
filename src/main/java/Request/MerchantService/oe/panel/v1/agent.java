package Request.MerchantService.oe.panel.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class agent extends AbstractApiV2 {

    public agent(){
        super("ManageAgent/addAgent/addAgentReq.json","ManageAgent/addAgent/addAgentRes.json","ManageAgent/addAgent/addAgentProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
