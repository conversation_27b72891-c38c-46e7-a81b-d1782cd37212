package Request.MerchantService.oe.panel.v1.editLead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EditLead extends AbstractApiV2
{
    public EditLead(String leadId, String ReqPath)
    {

        super(ReqPath,"MerchantServiceOEPanelV1EditLead/leadResponse.json","MerchantServiceOEPanelV1EditLead/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("leadId",leadId);
    }



    //QueryParams
    String solution;
    String entityType;
    String channel;

    //Headers
    String ContentType;
    String PostmanToken;
    String UncleScrooge;
    String session_token;
    String version;
    String cache_Control;

    //Request Body Fields
    String mobile;
    String pan;
    String nameAsPerPan;

    // Response  body fields
    Integer statusCode;
    String leadId;
    String refId;

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }

    public String getPostmanToken() {
        return PostmanToken;
    }

    public void setPostmanToken(String postmanToken) {
        PostmanToken = postmanToken;
    }

    public String getUncleScrooge() {
        return UncleScrooge;
    }

    public void setUncleScrooge(String uncleScrooge) {
        UncleScrooge = uncleScrooge;
    }

    public String getSession_token() {
        return session_token;
    }

    public void setSession_token(String session_token) {
        this.session_token = session_token;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getNameAsPerPan() {
        return nameAsPerPan;
    }

    public void setNameAsPerPan(String nameAsPerPan) {
        this.nameAsPerPan = nameAsPerPan;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public String getLeadId() {
        return leadId;
    }

    public void setLeadId(String leadId) {
        this.leadId = leadId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }


    public String getcacheControl()
    {
        return cache_Control;
    }

    public void setcacheControl(String cache_Control)
    {
        this.cache_Control = cache_Control;
    }
















    @Override
    public String toString()
    {
        return "lead{" +
                "solution='" + solution + '\'' +
                ", entityType='" + entityType + '\'' +
                ", channel='" + channel + '\'' +
                ", ContentType='" + ContentType + '\'' +
                ", PostmanToken='" + PostmanToken + '\'' +
                ", UncleScrooge='" + UncleScrooge + '\'' +
                ", session_token='" + session_token + '\'' +
                ", version='" + version + '\'' +
                ", mobile='" + mobile + '\'' +
                ", pan='" + pan + '\'' +
                ", nameAsPerPan='" + nameAsPerPan + '\'' +
                ", statusCode=" + statusCode +
                ", leadId='" + leadId + '\'' +
                ", refId='" + refId + '\'' +
                '}';
    }



}





