package Request.MerchantService.oe.panel.v1.fileProcess.upload;



import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SheetUpload extends AbstractApiV2
{
    public SheetUpload()
    {
        super("MerchantService/OE/Panel/v1/fileProcess/upload/UploadSheetRequest.json","MerchantService/OE/Panel/v1/fileProcess/upload/UploadSheetResponse.json","MerchantService/OE/Panel/v1/fileProcess/upload/UploadSheetProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}