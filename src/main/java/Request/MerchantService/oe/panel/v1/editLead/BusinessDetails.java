package Request.MerchantService.oe.panel.v1.editLead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BusinessDetails extends AbstractApiV2 {
    public BusinessDetails(String leadId, String ReqPath) {
        super(ReqPath, "MerchantServiceOEPanelV1EditLead/leadResponse.json", "MerchantServiceOEPanelV1EditLead/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("leadId", leadId);
    }
}
