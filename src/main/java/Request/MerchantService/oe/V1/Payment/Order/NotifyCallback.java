package Request.MerchantService.oe.V1.Payment.Order;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class NotifyCallback extends AbstractApiV2
{
    public NotifyCallback(String ReqPath)
    {
        super(ReqPath,"MerchantService/OE/V1/Payment/Order/Notify/NotifyOrderResponse.json","MerchantService/OE/V1/Payment/Order/Notify/NotifyOrderProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
