package Request.MerchantService.loan.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class dynamicTnC_LOS extends AbstractApiV2 {

	public dynamicTnC_LOS() {
		super("MerchantServiceloanLeadDynamicTNC/dynamicTnC_LOS/leadRequest.json",
				"MerchantServiceloanLeadDynamicTNC/dynamicTnC_LOS/leadResponse.json",
				"MerchantServiceloanLeadDynamicTNC/dynamicTnC_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));

	}
	
	// Query Param
	String leadId;

	// header
	String session_token;

	// Response body field
	String refId;
	String status;
	String code;
	Integer version;
	Integer accept;
	String uniqueIdentifier;
	String md5;
	String tncName;

	public String getLeadId() {
		return leadId;
	}
	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}
	public String getSession_token() {
		return session_token;
	}
	public void setSession_token(String session_token) {
		this.session_token = session_token;
	}
	public String getRefId() {
		return refId;
	}
	public void setRefId(String refId) {
		this.refId = refId;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public Integer getVersion() {
		return version;
	}
	public void setVersion(Integer version) {
		this.version = version;
	}
	public Integer getAccept() {
		return accept;
	}
	public void setAccept(Integer accept) {
		this.accept = accept;
	}
	public String getUniqueIdentifier() {
		return uniqueIdentifier;
	}
	public void setUniqueIdentifier(String uniqueIdentifier) {
		this.uniqueIdentifier = uniqueIdentifier;
	}
	public String getMd5() {
		return md5;
	}
	public void setMd5(String md5) {
		this.md5 = md5;
	}
	public String getTncName() {
		return tncName;
	}
	public void setTncName(String tncName) {
		this.tncName = tncName;
	}
	@Override
	public String toString() {
		return "dynamicTnC_LOS [leadId=" + leadId + ", session_token=" + session_token + ", refId=" + refId
				+ ", status=" + status + ", code=" + code + ", version=" + version + ", accept=" + accept
				+ ", uniqueIdentifier=" + uniqueIdentifier + ", md5=" + md5 + "]";
	}
	
	
	
	
	



}
