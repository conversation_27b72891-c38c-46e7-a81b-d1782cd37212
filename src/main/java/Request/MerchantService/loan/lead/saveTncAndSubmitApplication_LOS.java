package Request.MerchantService.loan.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class saveTncAndSubmitApplication_LOS extends AbstractApiV2 {
	
    public saveTncAndSubmitApplication_LOS()
    {
        super("MerchantServiceLoanLeadSubmitApplication/saveTncAndSubmitApplication_LOS/leadRequest.json",
        		"MerchantServiceLoanLeadSubmitApplication/saveTncAndSubmitApplication_LOS/leadResponse.json",
        		"MerchantServiceLoanLeadSubmitApplication/saveTncAndSubmitApplication_LOS/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
    
	// Query Param
	String leadId;

	// header
	String session_token;

	// Response body field
	String stage;
	String refId;
	String subStage;
	String status;
	String state;
	
	@Override
	public String toString() {
		return "saveTncAndSubmitApplication_LOS [leadId=" + leadId + ", session_token=" + session_token + ", stage="
				+ stage + ", refId=" + refId + ", subStage=" + subStage + ", status=" + status + ", state=" + state
				+ "]";
	}

	public String getLeadId() {
		return leadId;
	}

	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}

	public String getSession_token() {
		return session_token;
	}

	public void setSession_token(String session_token) {
		this.session_token = session_token;
	}

	public String getStage() {
		return stage;
	}

	public void setStage(String stage) {
		this.stage = stage;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	public String getSubStage() {
		return subStage;
	}

	public void setSubStage(String subStage) {
		this.subStage = subStage;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}
}
	
	
	
	
	
	
	


