package Request.MerchantService.RevisitOrganized;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetmidReferenceNumber extends AbstractApiV2 {
    public GetmidReferenceNumber(String shopReferenceNumber){
        super("MerchantService/V1/GetmidRefernceNumber/GetmidReferenceNumberRequest.json" , "src/test/resources/MerchantService/V1/GetmidRefernceNumber/GetmidReferenceNumberResponse.json" , "MerchantService/V1/GetmidRefernceNumber/GetmidReferenceNumberProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("shopReferenceNumber" , shopReferenceNumber);
    }
}
