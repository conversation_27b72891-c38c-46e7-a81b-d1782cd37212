package Request.MerchantService.v1.panel.leadManagement.registerLead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class RegisterLeadPanel extends AbstractApiV2{
   
	public RegisterLeadPanel()
    {
		   super("MerchantService/v1/panel/leadManagement/registerLead/registerLeadRequest.json", "MerchantService/v1/panel/leadManagement/registerLead/registerLeadResponse.json", "MerchantService/v1/panel/leadManagement/registerLead/registerLeadProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	public RegisterLeadPanel(boolean submit)
    {
		   super("MerchantService/v1/panel/leadManagement/registerLead/registerLeadSubmitRequest.json", "MerchantService/v1/panel/leadManagement/registerLead/registerLeadSubmitResponse.json", "MerchantService/v1/panel/leadManagement/registerLead/registerLeadSubmitProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	public RegisterLeadPanel(String edcLead)
    {
		   super("MerchantService/v1/panel/leadManagement/registerLead/edcLeadRequest.json", "MerchantService/v1/panel/leadManagement/registerLead/edcLeadResponse.json", "MerchantService/v1/panel/leadManagement/registerLead/edcLeadProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }



}
