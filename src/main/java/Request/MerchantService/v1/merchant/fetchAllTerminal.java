package Request.MerchantService.v1.merchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchAllTerminal extends AbstractApiV2
{
    public fetchAllTerminal()
    {
        super("MerchantService/V1/merchant/FetchAllTerminal/fetchAllTerminalRequest.json", "MerchantService/V1/merchant/FetchAllTerminal/fetchAllTerminalResponse.json", "MerchantService/V1/merchant/FetchAllTerminal/fetchAllTerminalProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
