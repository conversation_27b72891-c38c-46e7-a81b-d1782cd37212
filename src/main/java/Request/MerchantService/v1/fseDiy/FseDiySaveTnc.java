package Request.MerchantService.v1.fseDiy;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FseDiySaveTnc extends AbstractApiV2
{
    public FseDiySaveTnc(String requestJson)
    {
        super(requestJson,"MerchantService/V1/Fse/Diy/Request/FseDiySaveTncResponse.json","MerchantService/V1/Fse/Diy/Request/FseDiySaveTncProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}


