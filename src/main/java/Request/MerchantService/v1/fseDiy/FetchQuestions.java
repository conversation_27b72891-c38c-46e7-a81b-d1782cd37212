package Request.MerchantService.v1.fseDiy;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchQuestions extends AbstractApiV2
{
    public FetchQuestions()
    {
        super("MerchantService/V1/Fse/Diy/Request/QuestionsRequest.json","MerchantService/V1/Fse/Diy/Request/QuestionsResponse.json","MerchantService/V1/Fse/Diy/Request/QuestionsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}


