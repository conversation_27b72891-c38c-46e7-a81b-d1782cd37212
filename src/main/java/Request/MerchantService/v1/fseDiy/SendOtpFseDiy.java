package Request.MerchantService.v1.fseDiy;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SendOtpFseDiy extends AbstractApiV2 {

    public SendOtpFseDiy()
    {
        super("MerchantService/V1/Fse/Diy/Request/SendOtpRequest.json", "MerchantService/V1/Fse/Diy/Request/SendOtpResponse.json", "MerchantService/V1/Fse/Diy/Request/SendOtpProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

    // Response  body fields
    public static String httpStatus;
    public static String state;
    public static String refId;
    public static String status;
    public static String message;

    @Override
    public String toString() {
        return "SendOtp{" +
                "httpStatus=" + httpStatus +
                ", state='" + state + '\'' +
                ", refId='" + refId + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                '}';
    }

    public String getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(String httpStatus) {
        this.httpStatus = httpStatus;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}







