package Request.MerchantService.v1.fseDiy;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateOtpFseDiy extends AbstractApiV2 {

    public  ValidateOtpFseDiy()
    {
        super("MerchantService/V1/Fse/Diy/Request/ValidateOtpRequest.json", "MerchantService/V1/Fse/Diy/Request/ValidateOtpResponse.json", "MerchantService/V1/Fse/Diy/Request/ValidateOtpProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
