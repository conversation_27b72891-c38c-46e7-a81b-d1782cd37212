package Request.MerchantService.v1.fseDiy;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchFseInfo extends AbstractApiV2
{
    public FetchFseInfo(String CustID)
    {
        super("MerchantService/V1/Fse/Diy/Request/fetchfseinfo.json","MerchantService/V1/Fse/Diy/Request/QuestionsResponse.json","MerchantService/V1/Fse/Diy/Request/QuestionsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("custID",CustID);

    }
}


