package Request.MerchantService.v1.leadManagement;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class RegisterLead extends  AbstractApiV2{
    public RegisterLead(String ReqPath)
    {
        super(ReqPath, "MerchantService/V1/leadManagement/registerLead/RegisterLeadResponse.json", "MerchantService/V1/leadManagement/registerLead/RegisterLeadProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
