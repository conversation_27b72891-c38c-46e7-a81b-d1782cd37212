package Request.MerchantService.v1.profile.update;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddAddressPGProfileUpdate extends AbstractApiV2
{
    public AddAddressPGProfileUpdate()
    {
        super("MerchantService/V1/profile/update/AddAddress/AddAddressRequest.json","MerchantService/V1/profile/update/AddAddress/AddAddressResponse.json","MerchantService/V1/profile/update/AddAddress/AddAddressProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
