package Request.MerchantService.v1.profile.update;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateVendor extends AbstractApiV2 {

    public CreateVendor()
    {
        super("MerchantService/V1/profile/update/addVendor/createRequestAddVendor.json","MerchantService/V1/profile/update/addVendor/createResponseAddVendor.json","MerchantService/V1/profile/update/addVendor/createAddVendor.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
