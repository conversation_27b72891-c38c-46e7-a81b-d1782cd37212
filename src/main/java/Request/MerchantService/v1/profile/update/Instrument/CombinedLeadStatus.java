package Request.MerchantService.v1.profile.update.Instrument;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CombinedLeadStatus extends AbstractApiV2
{
    public CombinedLeadStatus()
    {
        super("MerchantService/V1/profile/update/Instrument/CombinedLeadStatus/CombinedLeadStatusRequest.json","MerchantService/V1/profile/update/Instrument/CombinedLeadStatus/CombinedLeadStatusResponse.json","MerchantService/V1/profile/update/Instrument/CombinedLeadStatus/CombinedLeadStatusProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
