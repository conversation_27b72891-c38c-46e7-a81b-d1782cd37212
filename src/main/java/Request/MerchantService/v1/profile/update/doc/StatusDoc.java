package Request.MerchantService.v1.profile.update.doc;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class StatusDoc extends AbstractApiV2
{
    public StatusDoc()
    {
        super("MerchantService/V1/profile/update/doc/FetchDocRequest.json","MerchantService/V1/profile/update/doc/FetchDocResponse.json","MerchantService/V1/profile/update/doc/FetchSchema.json");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
