package Request.MerchantService.v1.profile.update;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Commissiontncs extends AbstractApiV2{

    public Commissiontncs()
    {
        super("MerchantService/V1/profile/update/Commissiontncs/CommissiontncsRequest.json","MerchantService/V1/profile/update/Commissiontncs/CommissiontncsResponse.json","MerchantService/V1/profile/update/Commissiontncs/CommissiontncsRequest.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
