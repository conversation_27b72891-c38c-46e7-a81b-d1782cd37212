package Request.MerchantService.v1.profile.update;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddPan extends AbstractApiV2 {

    public AddPan()
    {
        super("MerchantService/V1/profile/update/AddPan/AddPanRequest.json","MerchantService/V1/profile/update/AddPan/AddPanResponse.json","MerchantService/V1/profile/update/AddPan/AddPanProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
