package Request.MerchantService.v1.profile.update.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Status extends AbstractApiV2 {

    public Status(String ReqPath)
    {
       super(ReqPath,"MerchantService/v1/profile/update/lead/Status/statusResponse.json","MerchantService/v1/profile/update/lead/Status/statusProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }

   // Adding Parameters
    String solution;
    String entityType;
    String solutionSubType;

    //Adding Headers

    String Content_Type;
    String session_token;
    String version;
    String UncleScrooge;

    //Adding Request Body

    String mid;
    String giftVoucher;
    String bankDetailsLeadFetch;
    String pgInstruments;

    //Adding Response body

    String refId;
    String statusCode;
    String leadStatus;
    String leadId;

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getSolutionSubType() {
        return solutionSubType;
    }

    public void setSolutionSubType(String solutionSubType) {
        this.solutionSubType = solutionSubType;
    }

    public String getContent_Type() {
        return Content_Type;
    }

    public void setContent_Type(String content_Type) {
        Content_Type = content_Type;
    }

    public String getSession_token() {
        return session_token;
    }

    public void setSession_token(String session_token) {
        this.session_token = session_token;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUncleScrooge() {
        return UncleScrooge;
    }

    public void setUncleScrooge(String uncleScrooge) {
        UncleScrooge = uncleScrooge;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getGiftVoucher() {
        return giftVoucher;
    }

    public void setGiftVoucher(String giftVoucher) {
        this.giftVoucher = giftVoucher;
    }

    public String getBankDetailsLeadFetch() {
        return bankDetailsLeadFetch;
    }

    public void setBankDetailsLeadFetch(String bankDetailsLeadFetch) {
        this.bankDetailsLeadFetch = bankDetailsLeadFetch;
    }

    public String getPgInstruments() {
        return pgInstruments;
    }

    public void setPgInstruments(String pgInstruments) {
        this.pgInstruments = pgInstruments;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getLeadStatus() {
        return leadStatus;
    }

    public void setLeadStatus(String leadStatus) {
        this.leadStatus = leadStatus;
    }

    public String getLeadId() {
        return leadId;
    }

    public void setLeadId(String leadId) {
        this.leadId = leadId;
    }






}
