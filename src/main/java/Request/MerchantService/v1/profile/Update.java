package Request.MerchantService.v1.profile;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Update extends AbstractApiV2 {

    public Update(String reqPath)
    {
        super(reqPath,"MerchantService/v1/profile/update/updateGiftVoucher/updateResponse.json","MerchantService/v1/profile/update/updateGiftVoucher/updateProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
