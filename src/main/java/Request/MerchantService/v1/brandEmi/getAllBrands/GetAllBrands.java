package Request.MerchantService.v1.brandEmi.getAllBrands;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetAllBrands extends AbstractApiV2
{
    public GetAllBrands()
    {
        super("MerchantService/V1/brandEmi/getAllBrands/GetAllBrandsRequest.json", "MerchantService/V1/brandEmi/getAllBrands/GetAllBrandsResponse.json", "MerchantService/V1/brandEmi/getAllBrands/GetAllBrandsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
