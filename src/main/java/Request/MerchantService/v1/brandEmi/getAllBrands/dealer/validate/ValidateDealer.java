package Request.MerchantService.v1.brandEmi.getAllBrands.dealer.validate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateDealer extends AbstractApiV2
{
    public ValidateDealer()
    {
        super("MerchantService/V1/brandEmi/getAllBrands/dealer/validate/ValidateDealerRequest.json", "MerchantService/V1/brandEmi/getAllBrands/dealer/validate/ValidateDealerResponse.json", "MerchantService/V1/brandEmi/getAllBrands/dealer/validate/ValidateDealerProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
