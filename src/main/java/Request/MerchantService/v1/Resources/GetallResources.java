package Request.MerchantService.v1.Resources;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetallResources extends AbstractApiV2
{
    public GetallResources()
    {
        super("MerchantService/V1/Resources/GetallResources/GetAllResourcesRequest.json","MerchantService/V1/Resources/GetAllResources/GetallResourcesResponse.json","MerchantService/V1/Resources/GetAllResources/GetallResourcesProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
