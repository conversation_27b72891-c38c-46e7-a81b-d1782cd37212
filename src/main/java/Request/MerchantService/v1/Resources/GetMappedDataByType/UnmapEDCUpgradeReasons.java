package Request.MerchantService.v1.Resources.GetMappedDataByType;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UnmapEDCUpgradeReasons extends AbstractApiV2
{
    public UnmapEDCUpgradeReasons()
    {
        super("MerchantService/V1/Resources/Values/UnmapEDCUpgradeReasons/UnmapEDCUpgradeReasonsRequest.json", "MerchantService/V1/Resources/Values/UnmapEDCUpgradeReasons/UnmapEDCUpgradeReasonsResponse.json", "MerchantService/V1/Resources/Values/UnmapEDCUpgradeReasons/UnmapEDCUpgradeReasonsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
