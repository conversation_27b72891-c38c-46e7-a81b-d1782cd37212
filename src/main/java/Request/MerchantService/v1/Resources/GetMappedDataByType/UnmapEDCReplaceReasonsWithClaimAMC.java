package Request.MerchantService.v1.Resources.GetMappedDataByType;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UnmapEDCReplaceReasonsWithClaimAMC extends AbstractApiV2
{
    public UnmapEDCReplaceReasonsWithClaimAMC()
    {
        super("MerchantService/V1/Resources/Values/UnmapEdcReplaceReasons/UnmapEdcReplaceReasonsRequest.json", "MerchantService/V1/Resources/Values/UnmapEdcReplaceReasons/UnmapEdcReplaceReasonsResponse.json", "MerchantService/V1/Resources/Values/UnmapEdcReplaceReasons/UnmapEdcReplaceReasonsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
