package Request.MerchantService.v1.Resources.GetMappedDataByType;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class UnmapEDCReturnReasonsWithClaimAMC extends AbstractApiV2
{
    private static final Logger LOGGER = LogManager.getLogger(UnmapEDCReturnReasonsWithClaimAMC.class);

    public UnmapEDCReturnReasonsWithClaimAMC()
    {
        super("MerchantService/V1/Resources/Values/UnmapEdcReturnReasons/UnmapEdcReturnReasonsRequest.json", "MerchantService/V1/Resources/Values/UnmapEdcReturnReasons/UnmapEdcReturnReasonsResponse.json", "MerchantService/V1/Resources/Values/UnmapEdcReturnReasons/UnmapEdcReturnReasonsProperties.properties");

        try {
            replaceUrlPlaceholder("base_url", P.API.get("api_url"));
            LOGGER.info("Successfully initialized UnmapEDCReturnReasonsWithClaimAMC");
        } catch (Exception e) {
            LOGGER.error("Error initializing UnmapEDCReturnReasonsWithClaimAMC: " + e.getMessage());
        }
    }
}
