package Request.MerchantService.v1.Resources;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LanguagePreference extends AbstractApiV2 {
    public LanguagePreference()
    {
        super("MerchantService/V1/Resources/LanguagePrefrence/LanguagePrefRequest.json", "MerchantService/V1/Resources/LanguagePrefrence/LanguagePrefResponse.json", "MerchantService/V1/Resources/LanguagePrefrence/LanguagePrefProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
