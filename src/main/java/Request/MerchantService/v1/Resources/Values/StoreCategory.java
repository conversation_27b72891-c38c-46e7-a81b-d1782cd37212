package Request.MerchantService.v1.Resources.Values;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class StoreCategory extends AbstractApiV2
{
    public StoreCategory()
    {
        super("MerchantService/V1/Resources/Values/StoreCategory/StoreCategoryRequest.json", "MerchantService/V1/Resources/Values/StoreCategory/StoreCategoryResponse.json", "MerchantService/V1/Resources/Values/StoreCategory/StoreCategoryProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
