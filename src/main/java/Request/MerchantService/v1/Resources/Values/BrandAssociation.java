package Request.MerchantService.v1.Resources.Values;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BrandAssociation extends AbstractApiV2
{
    public BrandAssociation()
    {
        super("MerchantService/V1/Resources/Values/BrandAssociation/BrandAssociationRequest.json", "MerchantService/V1/Resources/Values/BrandAssociation/BrandAssociationResponse.json", "MerchantService/V1/Resources/Values/BrandAssociation/BrandAssociationProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
