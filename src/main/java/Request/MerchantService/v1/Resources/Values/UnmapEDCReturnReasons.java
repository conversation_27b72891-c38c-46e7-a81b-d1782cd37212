package Request.MerchantService.v1.Resources.Values;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UnmapEDCReturnReasons extends AbstractApiV2
{
    public UnmapEDCReturnReasons()
    {
        super("MerchantService/V1/Resources/Values/UnmapEdcReturnReasons/UnmapEdcReturnReasonsRequest.json", "MerchantService/V1/Resources/Values/UnmapEdcReturnReasons/UnmapEdcReturnReasonsResponse.json", "MerchantService/V1/Resources/Values/UnmapEdcReturnReasons/UnmapEdcReturnReasonsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
