package Request.MerchantService.v1.sfcrm;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Lead extends AbstractApiV2{
    public Lead()
    {
        super("MerchantService/V1/sfcrm/lead/fetchLeadRequest.json", "MerchantService/V1/sfcrm/lead/fetchLeadResponse.json", "MerchantService/V1/sfcrm/lead/fetchLeadProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}