package Request.MerchantService.v1.NCMC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ncmcsendotp extends AbstractApiV2 {
    public ncmcsendotp(){
        super("MerchantService/V1/ncmcsendotp/ncmcsendotpRequest.json" ,"MerchantService/V1/ncmcsendotp/ncmcsendotpResponse.json" ,"MerchantService/V1/ncmcsendotp/ncmcsendotpProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
