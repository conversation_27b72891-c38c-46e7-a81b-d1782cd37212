package Request.MerchantService.v1.NCMC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class generate_qr_ncmc extends AbstractApiV2 {

    public generate_qr_ncmc(){

        super( "MerchantService/V1/generate_qr_ncmc/generate_qr_ncmcRequest.json" , "MerchantService/V1/generate_qr_ncmc/generate_qr_ncmcResponse.json", "MerchantService/V1/generate_qr_ncmc/generate_qr_ncmcProperties.Properties" );

        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));

    }
}
