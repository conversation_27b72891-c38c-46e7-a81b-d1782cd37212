package Request.MerchantService.v1.NCMC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class submitKyc extends AbstractApiV2 {
    public submitKyc(){
        super("MerchantService/V1/submitKyc/submitKycRequest.json" , "MerchantService/V1/submitKyc/submitKycResponse.json" , "MerchantService/V1/submitKyc/submitKycProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
