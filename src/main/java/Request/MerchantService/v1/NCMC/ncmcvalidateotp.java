package Request.MerchantService.v1.NCMC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ncmcvalidateotp extends  AbstractApiV2{
        public ncmcvalidateotp(){
            super("MerchantService/V1/ncmcvalidateotp/ncmcvalidateotpRequest.json" , "MerchantService/V1/ncmcvalidateotp/ncmcvalidateotpResponse.json" , "MerchantService/V1/ncmcvalidateotp/ncmcvalidateotpProperties.properties");
            replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        }
    }


