package Request.MerchantService.v1.NCMC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetch_payment_status extends AbstractApiV2 {
    public fetch_payment_status(){
        super("MerchantService/V1/fetch_payment_status/fetch_payment_statusRequest.json" , "MerchantService/V1/fetch_payment_status/fetch_payment_statusResponse.json" , "MerchantService/V1/fetch_payment_status/fetch_payment_statusProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
