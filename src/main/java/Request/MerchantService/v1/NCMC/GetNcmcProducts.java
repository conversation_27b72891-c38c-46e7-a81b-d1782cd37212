package Request.MerchantService.v1.NCMC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetNcmcProducts extends AbstractApiV2 {
    public  GetNcmcProducts(){
        super("MerchantService/V1/NCMC/GetNcmcProducts/GetNcmcProductSRequest.json" , "MerchantService/V1/NCMC/GetNcmcProducts/GetNcmcProductsResponse.json" , "MerchantService/V1/NCMC/GetNcmcProducts/GetNcmcProductsProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));

    }
}
