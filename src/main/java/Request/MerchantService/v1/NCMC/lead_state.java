package Request.MerchantService.v1.NCMC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class lead_state extends AbstractApiV2 {

    public lead_state(String lead_id){
        super("MerchantService/V1/lead_state/lead_stateRequests.json", "MerchantService/V1/lead_state/lead_stateResponse.json" , "MerchantService/V1/lead_state/lead_stateProperties.Properties");

        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));

        replaceUrlPlaceholder("lead_id" , lead_id);
    }

}
