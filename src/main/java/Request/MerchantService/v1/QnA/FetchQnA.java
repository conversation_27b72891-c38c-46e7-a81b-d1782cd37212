package Request.MerchantService.v1.QnA;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchQnA extends AbstractApiV2
{
    public FetchQnA()
    {
        super("MerchantService/V1/QnA/FetchQuestions/FetchQnARequest.json", "MerchantService/V1/QnA/FetchQuestions/FetchQnAResponse.json", "MerchantService/V1/QnA/FetchQuestions/FetchQnAProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
