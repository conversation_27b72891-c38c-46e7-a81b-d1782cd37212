package Request.MerchantService.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchSubCategoryODS extends AbstractApiV2 {
    public FetchSubCategoryODS() {

        super( "ODS/FetchSubCategory/FetchSubCategoryODSRequest.json","ODS/FetchSubCategory/FetchSubCategoryODSResponse.json", "ODS/FetchSubCategory/FetchSubCategoryODSProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("ods_url"));

    }
}
