package Request.MerchantService.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetCompany extends AbstractApiV2 {
    public GetCompany(String PAN)
    {
        super("MerchantService/V1/GetCompany/GetCompanyRequest.json", "MerchantService/V1/GetCompany/GetCompanyResponse.json", "MerchantService/V1/GetCompany/GetCompanyProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("PAN", PAN);
    }
}
