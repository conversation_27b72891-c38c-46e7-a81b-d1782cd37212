package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseUpdateSolutionLead extends AbstractApiV2 {
    public EnterpriseUpdateSolutionLead() {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseUpdateSolutionLead/EnterpriseUpdateSolutionLeadRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseUpdateSolutionLead/EnterpriseUpdateSolutionLeadResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseUpdateSolutionLead/EnterpriseUpdateSolutionLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 