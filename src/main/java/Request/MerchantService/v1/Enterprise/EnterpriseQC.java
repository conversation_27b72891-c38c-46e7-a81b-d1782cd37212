package Request.MerchantService.v1.Enterprise;
import com.paytm.apitools.core.P;
import com.paytm.apitools.core.AbstractApiV2;

public class EnterpriseQC extends AbstractApiV2 {
    public EnterpriseQC(String leadId) {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseQC/EnterpriseQCRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseQC/EnterpriseQCResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseQC/EnterpriseQCProperties.Properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("leadId" , leadId);
    }
}
