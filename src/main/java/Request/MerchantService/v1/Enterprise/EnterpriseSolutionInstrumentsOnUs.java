package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseSolutionInstrumentsOnUs extends AbstractApiV2 {
    public EnterpriseSolutionInstrumentsOnUs() {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseSolutionInstruments/EnterpriseSolutionInstrumentsOnUsRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseSolutionInstruments/EnterpriseSolutionInstrumentsResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseSolutionInstruments/EnterpriseSolutionInstrumentsOnUsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}
