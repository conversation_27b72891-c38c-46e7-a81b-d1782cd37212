package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseFetchMerchantBanks extends AbstractApiV2 {
    public EnterpriseFetchMerchantBanks() {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchMerchantBanks/EnterpriseFetchMerchantBanksRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchMerchantBanks/EnterpriseFetchMerchantBanksResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchMerchantBanks/EnterpriseFetchMerchantBanksProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
} 
