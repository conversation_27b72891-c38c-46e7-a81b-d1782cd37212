package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseFetchPinCodeDetails extends AbstractApiV2 {

    public EnterpriseFetchPinCodeDetails(String pincode) {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchPinCodeDetails/EnterpriseFetchPinCodeDetailsRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchPinCodeDetails/EnterpriseFetchPinCodeDetailsResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchPinCodeDetails/EnterpriseFetchPinCodeDetailsProperties.Properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("pincode", pincode);
    }
}
