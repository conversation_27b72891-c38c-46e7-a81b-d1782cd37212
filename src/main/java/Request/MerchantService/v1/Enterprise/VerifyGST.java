package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class VerifyGST extends AbstractApiV2 {
    public VerifyGST(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/VerifyGST/VerifyGSTRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/VerifyGST/VerifyGSTResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/VerifyGST/VerifyGSTProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
