package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class CreateLeadEnterprise_OnUs extends AbstractApiV2 {
    public CreateLeadEnterprise_OnUs(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/CreateLeadEnterpriseOnUsRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/CreateLeadEnterpriseResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/CreateLeadEnterpriseProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
