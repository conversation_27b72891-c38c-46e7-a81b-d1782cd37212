package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseFetchBasicInfo extends AbstractApiV2 {
    public EnterpriseFetchBasicInfo(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchBasicInfo/EnterpriseFetchBasicInfoRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchBasicInfo/EnterpriseFetchBasicInfoResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseFetchBasicInfo/EnterpriseFetchBasicInfoProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
