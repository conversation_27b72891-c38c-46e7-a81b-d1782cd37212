package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseValidatePan extends AbstractApiV2 {
    public EnterpriseValidatePan(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseValidatePan/EnterpriseValidatePanRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseValidatePan/EnterpriseValidatePanResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseValidatePan/EnterpriseValidatePanProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
