package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterprisePennyDrop extends AbstractApiV2 {
    public EnterprisePennyDrop() {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterprisePennyDrop/EnterprisePennyDropRequest.json", "MerchantService/Enterprise/CreateLeadEnterprise/EnterprisePennyDrop/EnterprisePennyDropResponse.json", "MerchantService/Enterprise/CreateLeadEnterprise/EnterprisePennyDrop/EnterprisePennyDropProperties.properties");
       replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 