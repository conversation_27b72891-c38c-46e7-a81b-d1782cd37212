package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseUpdateBusinessLead extends AbstractApiV2 {

        public EnterpriseUpdateBusinessLead() {
            super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseUpdateBusinessLead/EnterpriseUpdateBusinessLeadRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseUpdateBusinessLead/EnterpriseUpdateBusinessLeadResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseUpdateBusinessLead/EnterpriseUpdateBusinessLeadProperties.Properties");
            replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        }
}
