package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ReAllocateAgent extends AbstractApiV2 {
    public ReAllocateAgent (){
        super("MerchantService/Enterprise/CreateLeadEnterprise/ReAllocateAgent/ReAllocateAgentRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/ReAllocateAgent/ReAllocateAgentResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/ReAllocateAgent/ReAllocateAgentProperties.Properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}
