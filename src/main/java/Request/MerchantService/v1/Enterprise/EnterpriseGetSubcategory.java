package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseGetSubcategory extends AbstractApiV2 {
    public EnterpriseGetSubcategory(String CategoryId){
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseGetCategorySubCategory/EnterpriseGetCategorySubCategoryRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseGetCategorySubCategory/EnterpriseGetCategorySubCategoryResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseGetCategorySubCategory/EnterpriseGetCategorySubCategoryProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("category" , CategoryId);
    }
}
