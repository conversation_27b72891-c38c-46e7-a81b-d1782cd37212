package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseDocumentStatus extends AbstractApiV2 {
    public EnterpriseDocumentStatus() {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseDocumentStatus/EnterpriseDocumentStatusRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseDocumentStatus/EnterpriseDocumentStatusResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseDocumentStatus/EnterpriseDocumentStatusProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 