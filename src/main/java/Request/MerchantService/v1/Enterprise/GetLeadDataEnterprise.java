package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetLeadDataEnterprise extends AbstractApiV2 {
    public GetLeadDataEnterprise(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/GetLeadDataEnterprise/GetLeadDataEnterpriseRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/GetLeadDataEnterprise/GetLeadDataEnterpriseResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/GetLeadDataEnterprise/GetLeadDataEnterpriseProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
