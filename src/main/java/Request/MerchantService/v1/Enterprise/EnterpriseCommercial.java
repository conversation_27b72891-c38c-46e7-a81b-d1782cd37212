package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseCommercial extends AbstractApiV2 {
    public EnterpriseCommercial(String leadId) {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseCommercial/EnterpriseCommercialRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseCommercial/EnterpriseCommercialResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseCommercial/EnterpriseCommercialProperties.Properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("leadId" , leadId);
    }
}
