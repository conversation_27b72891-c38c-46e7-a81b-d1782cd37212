package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class CreateLeadEnterprise extends AbstractApiV2 {
    public CreateLeadEnterprise(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/CreateLeadEnterpriseRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/CreateLeadEnterpriseResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/CreateLeadEnterpriseProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
