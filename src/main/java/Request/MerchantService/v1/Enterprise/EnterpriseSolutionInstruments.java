package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseSolutionInstruments extends AbstractApiV2 {
    public EnterpriseSolutionInstruments() {
       super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseSolutionInstruments/EnterpriseSolutionInstrumentsRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseSolutionInstruments/EnterpriseSolutionInstrumentsResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseSolutionInstruments/EnterpriseSolutionInstrumentsProperties.properties");
       replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 
