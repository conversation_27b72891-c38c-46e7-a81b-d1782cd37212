package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchGstFromPan extends AbstractApiV2 {
    public FetchGstFromPan(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/FetchGstFromPan/FetchGstFromPanRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/FetchGstFromPan/FetchGstFromPanResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/FetchGstFromPan/FetchGstFromPanProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
