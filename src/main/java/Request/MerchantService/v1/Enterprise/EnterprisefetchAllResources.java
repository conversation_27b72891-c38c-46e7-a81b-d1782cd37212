package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterprisefetchAllResources extends AbstractApiV2 {
    public EnterprisefetchAllResources(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterprisefetchAllResources/EnterprisefetchAllResourcesRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterprisefetchAllResources/EnterprisefetchAllResourcesResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterprisefetchAllResources/EnterprisefetchAllResourcesProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
