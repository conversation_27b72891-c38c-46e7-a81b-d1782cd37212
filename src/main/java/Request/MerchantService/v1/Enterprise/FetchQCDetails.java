package Request.MerchantService.v1.Enterprise;
import com.paytm.apitools.core.P;
import com.paytm.apitools.core.AbstractApiV2;
public class FetchQCDetails extends AbstractApiV2 {
    public FetchQCDetails(String leadId) {
        super("MerchantService/Enterprise/CreateLeadEnterprise/FetchQCDetails/FetchQCDetailsRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/FetchQCDetails/FetchQCDetailsResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/FetchQCDetails/FetchQCDetailsProperties.Properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("leadId" , leadId);
    }
}
