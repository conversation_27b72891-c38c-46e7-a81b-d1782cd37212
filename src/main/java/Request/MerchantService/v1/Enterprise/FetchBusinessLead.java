package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchBusinessLead extends AbstractApiV2 {
    public FetchBusinessLead(){
        super("MerchantService/Enterprise/CreateLeadEnterprise/FetchBusinessLead/FetchBusinessLeadRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/FetchBusinessLead/FetchBusinessLeadResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/FetchBusinessLead/FetchBusinessLeadProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
