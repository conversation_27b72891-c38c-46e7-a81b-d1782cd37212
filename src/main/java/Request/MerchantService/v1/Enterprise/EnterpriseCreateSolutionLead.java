package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EnterpriseCreateSolutionLead extends AbstractApiV2 {
    public EnterpriseCreateSolutionLead() {
        super("MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseCreateSolutionLead/EnterpriseCreateSolutionLeadRequest.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseCreateSolutionLead/EnterpriseCreateSolutionLeadResponse.json" , "MerchantService/Enterprise/CreateLeadEnterprise/EnterpriseCreateSolutionLead/EnterpriseCreateSolutionLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
} 