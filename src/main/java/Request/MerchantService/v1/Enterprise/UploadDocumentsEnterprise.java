package Request.MerchantService.v1.Enterprise;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UploadDocumentsEnterprise extends AbstractApiV2 {
    public UploadDocumentsEnterprise() {
        super("MerchantService/Enterprise/CreateLeadEnterprise/UploadDocumentsEnterprise/UploadDocumentsEnterpriseRequest.json",
              "MerchantService/Enterprise/CreateLeadEnterprise/UploadDocumentsEnterprise/UploadDocumentsEnterpriseResponse.json",
              "MerchantService/Enterprise/CreateLeadEnterprise/UploadDocumentsEnterprise/UploadDocumentsEnterpriseProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
    }
}
