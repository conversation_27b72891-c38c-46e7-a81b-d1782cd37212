package Request.MerchantService.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GstExemptionList extends AbstractApiV2 {
    public GstExemptionList()
    {
        super("MerchantService/V1/GstExemptionList/GstExemptionListRequest.json", "MerchantService/V1/GstExemptionList/GstExemptionListResponse.json", "MerchantService/V1/GstExemptionList/GstExemptionListProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
