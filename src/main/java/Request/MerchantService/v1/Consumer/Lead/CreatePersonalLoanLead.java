package Request.MerchantService.v1.Consumer.Lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreatePersonalLoanLead extends AbstractApiV2{
	
	
	  public CreatePersonalLoanLead()
	    {
			   super("MerchantService/v1/Consumer/Lead/CreatePLRequest.json", "MerchantService/v1/Consumer/Lead/CreatePLResponse.json", "MerchantService/v1/Consumer/Lead/CreatePLProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	  
	  public CreatePersonalLoanLead(String solutionType)
	    {
			   super("MerchantService/v1/Consumer/Lead/CreatePLHeroRequest.json", "MerchantService/v1/Consumer/Lead/CreatePLHeroResponse.json", "MerchantService/v1/Consumer/Lead/CreatePLHeroProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	  
	  public CreatePersonalLoanLead(boolean solutionType)
	    {
			   super("MerchantService/v1/Consumer/Lead/LoanOfferAcceptedRequest.json", "MerchantService/v1/Consumer/Lead/LoanOfferAcceptedResponse.json", "MerchantService/v1/Consumer/Lead/LoanOfferAcceptedProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	  
	  public CreatePersonalLoanLead(String solutionType,String solutionTypeLevel2)
	    {
			   super("MerchantService/v1/Consumer/Lead/LoanOfferAcceptedClixRequest.json", "MerchantService/v1/Consumer/Lead/LoanOfferAcceptedResponse.json", "MerchantService/v1/Consumer/Lead/LoanOfferAcceptedProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	  
	  public CreatePersonalLoanLead(boolean solutionType, boolean isNewLender)
	    {
			   super("MerchantService/v1/Consumer/Lead/LoanOfferAcceptedHeroRequest.json", "MerchantService/v1/Consumer/Lead/LoanOfferAcceptedResponse.json", "MerchantService/v1/Consumer/Lead/LoanOfferAcceptedProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
	  
	  
	  
}
