package Request.MerchantService.v1.Consumer.Lead.AdditionalDetails;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BasicDetails extends AbstractApiV2{
	
	public BasicDetails (String SolutionType)
    {
		   super("MerchantService/v1/Consumer/Lead/additionalDetails/BasicDetailsRequest.json", "MerchantService/v1/Consumer/Lead/additionalDetails/BasicDetailsResponse.json", "MerchantService/v1/Consumer/Lead/additionalDetails/BasicDetailsProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

	
	public BasicDetails ()
    {
		   super("MerchantService/v1/Consumer/Lead/additionalDetails/UpdateAdditionalDetailsRequest.json", "MerchantService/v1/Consumer/Lead/additionalDetails/UpdateAdditionalDetailsResponse.json", "MerchantService/v1/Consumer/Lead/additionalDetails/UpdateAdditionalDetailsProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	 public BasicDetails(boolean solutionType)
	    {
			   super("MerchantService/v1/Consumer/Lead/additionalDetails/UpdatePLHeroRequest.json", "MerchantService/v1/Consumer/Lead/additionalDetails/UpdatePLHeroResponse.json", "MerchantService/v1/Consumer/Lead/additionalDetails/UpdatePLHeroProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}
