package Request.MerchantService.v1.Consumer.Lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddAddress extends AbstractApiV2{
	   
		public AddAddress()
	    {
			   super("MerchantService/v1/Consumer/Lead/AddAddressRequest.json", "MerchantService/v1/Consumer/Lead/AddAddressResponse.json", "MerchantService/v1/Consumer/Lead/AddAddressProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}	