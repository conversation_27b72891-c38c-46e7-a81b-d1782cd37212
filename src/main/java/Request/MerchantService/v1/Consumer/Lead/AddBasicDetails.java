package Request.MerchantService.v1.Consumer.Lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddBasicDetails extends AbstractApiV2{
	   
		public AddBasicDetails()
	    {
			   super("MerchantService/v1/Consumer/Lead/AddBasicDetailsRequest.json", "MerchantService/v1/Consumer/Lead/AddBasicDetailsResponse.json", "MerchantService/v1/Consumer/Lead/AddBasicDetailsProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		
}	