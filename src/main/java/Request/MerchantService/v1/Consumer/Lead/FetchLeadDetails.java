package Request.MerchantService.v1.Consumer.Lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchLeadDetails extends AbstractApiV2{
	   
		public FetchLeadDetails()
	    {
			   super("MerchantService/v1/Consumer/Lead/FetchLeadDetailsRequest.json", "MerchantService/v1/Consumer/Lead/FetchLeadDetailsResponse.json", "MerchantService/v1/Consumer/Lead/FetchLeadDetailsProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}	
