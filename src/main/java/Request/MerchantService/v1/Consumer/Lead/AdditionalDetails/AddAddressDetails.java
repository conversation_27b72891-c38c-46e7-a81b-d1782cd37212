package Request.MerchantService.v1.Consumer.Lead.AdditionalDetails;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddAddressDetails extends AbstractApiV2{
	   
		public AddAddressDetails()
	    {
			   super("MerchantService/v1/Consumer/Lead/additionalDetails/AddAddressRequest.json", "MerchantService/v1/Consumer/Lead/additionalDetails/AddAddressResponse.json", "MerchantService/v1/Consumer/Lead/additionalDetails/AddAddressProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}
