package Request.MerchantService.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpgradePlans extends AbstractApiV2
{
    public UpgradePlans()
    {
        super("MerchantService/V1/GetUpgradePlans/UpgradePlansRequest.json", "MerchantService/V1/GetUpgradePlans/UpgradePlansResponse.json", "MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
