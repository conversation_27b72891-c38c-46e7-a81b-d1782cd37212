


package Request.MerchantService.v1;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

    public class TokenXMV extends AbstractApiV2 {

        public TokenXMV() {
            super("MerchantService/v1/Token/TokenRequest.json","MerchantService/v1/Token/TokenResponse.json","MerchantService/v1/Token/TokenProperties.properties");
            replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        }
    }








