package Request.MerchantService.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Commission extends AbstractApiV2
{
    public Commission()

    {
        super("MerchantService/V3/GetMerchant/GetMerchantRequest.json", "MerchantService/V3/GetMerchant/GetMerchantResponse.json", "MerchantService/V3/GetMerchant/GetMerchantProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}


