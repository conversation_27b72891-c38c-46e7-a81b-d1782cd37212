package Request.MerchantService.v1.upgradeMid.doc.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UploadDocCashAtPosDIY extends AbstractApiV2
{
    public UploadDocCashAtPosDIY()
    {
        super("MerchantService/V1/upgradeMid/doc/status/FetchDocumentStatusDIYCashAtPos/doc.UploadDocCashAtPosDIY/UploadDocLeadRequest.json", "MerchantService/V1/upgradeMid/doc/status/FetchDocumentStatusDIYCashAtPos/doc.UploadDocCashAtPosDIY/UploadDocLeadResponse.json", "MerchantService/V1/upgradeMid/doc/status/FetchDocumentStatusDIYCashAtPos/doc.UploadDocCashAtPosDIY/UploadDocLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
