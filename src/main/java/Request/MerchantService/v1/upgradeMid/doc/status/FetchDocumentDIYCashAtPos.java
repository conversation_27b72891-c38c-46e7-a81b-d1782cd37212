package Request.MerchantService.v1.upgradeMid.doc.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchDocumentDIYCashAtPos extends AbstractApiV2
{
    public FetchDocumentDIYCashAtPos()
    {
        super("MerchantService/V1/upgradeMid/doc/status/FetchDocumentStatusDIYCashAtPos/fetchDocumentLeadRequest.json", "MerchantService/V1/upgradeMid/doc/status/FetchDocumentStatusDIYCashAtPos/fetchDocumentLeadResponse.json", "MerchantService/V1/upgradeMid/doc/status/FetchDocumentStatusDIYCashAtPos/fetchDocumentLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}