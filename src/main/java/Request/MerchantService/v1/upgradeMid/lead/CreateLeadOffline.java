package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadOffline extends AbstractApiV2 {
	   
		public CreateLeadOffline()
	    {
			   super("MerchantService/V1/upgradeMid/lead/CreateLeadRequest.json", "MerchantService/V1/upgradeMid/lead/CreateLeadResponse.json", "MerchantService/V1/upgradeMid/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		

}


