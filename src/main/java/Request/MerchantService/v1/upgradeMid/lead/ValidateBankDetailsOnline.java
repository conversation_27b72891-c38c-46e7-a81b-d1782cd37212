package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateBankDetailsOnline extends AbstractApiV2 {

    public ValidateBankDetailsOnline()
    {
        super("MerchantService/V1/upgradeMid/ValidateBankDetails/validateBankDetailsRequest.json","MerchantService/V1/upgradeMid/ValidateBankDetails/validateBankDetailsResponse.json","MerchantService/V1/upgradeMid/ValidateBankDetails/validateBankDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }


        String displayMessage;
        String refId;
        String    statusCode;
        String  bankAccountHolderName;
        String bankDetailsUuid;
        String  nameMatchStatus;

    public String getDisplayMessage() {
        return displayMessage;
    }

    public void setDisplayMessage(String displayMessage) {
        this.displayMessage = displayMessage;
    }

    public String getRefId() {
        return refId;
    }

    public  void setRefId(String refId) {
        this.refId = refId;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getBankAccountHolderName() {
        return bankAccountHolderName;
    }

    public void setBankAccountHolderName(String bankAccountHolderName) {
        this.bankAccountHolderName = bankAccountHolderName;
    }

    public String getBankDetailsUuid() {
        return bankDetailsUuid;
    }

    public void setBankDetailsUuid(String bankDetailsUuid) {
        this.bankDetailsUuid = bankDetailsUuid;
    }

    public String getNameMatchStatus() {
        return nameMatchStatus;
    }

    public void setNameMatchStatus(String nameMatchStatus) {
        this.nameMatchStatus = nameMatchStatus;
    }

    @Override
    public String toString() {
        return "ValidateBankDetails{" +
                "displayMessage='" + displayMessage + '\'' +
                ", refId='" + refId + '\'' +
                ", statusCode=" + statusCode +
                ", bankAccountHolderName='" + bankAccountHolderName + '\'' +
                ", bankDetailsUuid='" + bankDetailsUuid + '\'' +
                ", nameMatchStatus='" + nameMatchStatus + '\'' +
                '}';
    }
}
