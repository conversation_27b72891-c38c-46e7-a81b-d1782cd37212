package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchLeadData extends AbstractApiV2 {
	
	public FetchLeadData()
    {
		   super("MerchantService/V1/upgradeMid/lead/FetchLeadDataRequest.json", "MerchantService/V1/upgradeMid/lead/FetchLeadDataResponse.json", "MerchantService/V1/upgradeMid/lead/FetchLeadDataProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
