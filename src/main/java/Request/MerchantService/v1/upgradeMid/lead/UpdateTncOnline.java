package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateTncOnline extends AbstractApiV2 {
	

	    public UpdateTncOnline()
	    {
	        super("MerchantService/V1/upgradeMid/tnc/tncRequest.json","MerchantService/V1/upgradeMid/tnc/tncResponse.json","MerchantService/V1/upgradeMid/tnc/tncProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

	    }
	
}
