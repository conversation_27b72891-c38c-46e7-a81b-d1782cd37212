package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class UpdateBankDetailOnline extends AbstractApiV2 {



    public UpdateBankDetailOnline() {
		
		super("MerchantService/V1/upgradeMid/UpdateBankDetails/updateBankDetailsRequest.json","MerchantService/V1/upgradeMid/UpdateBankDetails/updateBankDetailsResponse.json","MerchantService/V1/upgradeMid/UpdateBankDetails/updateBankDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));// 
	}
    

    String required ;
    String  refId;
    String   statusCode;



    public String getRequired() {
        return required;
    }

    public void setRequired(String required) {
        this.required = required;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }
    @Override
    public String toString() {
        return "UpdateBankDetails{" +
                "required='" + required + '\'' +
                ", refId='" + refId + '\'' +
                ", statusCode='" + statusCode + '\'' +
                '}';
    }


}