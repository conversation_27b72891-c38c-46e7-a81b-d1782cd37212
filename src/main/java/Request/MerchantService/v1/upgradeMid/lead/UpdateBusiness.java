package Request.MerchantService.v1.upgradeMid.lead;


import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateBusiness extends AbstractApiV2 {
	   
		public UpdateBusiness()
	    {
			   super("MerchantService/V1/upgradeMid/business/UpdateBusinessRequest.json", "MerchantService/V1/upgradeMid/business/UpdateBusinessResponse.json", "MerchantService/V1/upgradeMid/business/UpdateBusinessProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		

}
