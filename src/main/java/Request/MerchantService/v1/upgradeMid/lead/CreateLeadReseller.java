package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadReseller extends AbstractApiV2 {
	   
		public CreateLeadReseller()
	    {
			   super("MerchantService/V1/upgradeMid/lead/CreateLeadResellerRequest.json", "MerchantService/V1/upgradeMid/lead/CreateLeadResponse.json", "MerchantService/V1/upgradeMid/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		

}


