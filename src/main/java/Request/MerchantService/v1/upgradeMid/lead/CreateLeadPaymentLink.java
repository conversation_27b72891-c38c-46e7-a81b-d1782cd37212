package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadPaymentLink extends AbstractApiV2 {
	   
		public CreateLeadPaymentLink()
	    {
			   super("MerchantService/V1/upgradeMid/lead/CreateLeadPaymentLinkRequest.json", "MerchantService/V1/upgradeMid/lead/CreateLeadResponse.json", "MerchantService/V1/upgradeMid/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		

}


