package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateAddtionalDetailsAddressIndigoOnboarding extends AbstractApiV2 {
	
	public UpdateAddtionalDetailsAddressIndigoOnboarding()
	
	{
		super("MerchantService/V1/upgradeMid/additionalDetails/UpdateAdditionalInfoAddressRequest.json", "MerchantService/V1/upgradeMid/additionalDetails/UpdateAdditonalDetailsResponse.json", "MerchantService/V1/upgradeMid/additionalDetails/UpdateAdditonalDetailsProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
	
	

}
