package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PaymentsLeadStatus extends AbstractApiV2 {
	
	public PaymentsLeadStatus()
    {
		   super("MerchantService/V1/upgradeMid/lead/PaymentsLeadStatusRequest.json", "MerchantService/V1/upgradeMid/lead/PaymentsLeadStatusResponse.json", "MerchantService/V1/upgradeMid/lead/PaymentsLeadStatusProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
