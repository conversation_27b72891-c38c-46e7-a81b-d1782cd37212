package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchLeadStatus extends AbstractApiV2 {
	
	public FetchLeadStatus()
    {
		   super("MerchantService/V1/upgradeMid/lead/FetchLeadStatusRequest.json", "MerchantService/V1/upgradeMid/lead/FetchLeadStatusResponse.json", "MerchantService/V1/upgradeMid/lead/FetchLeadStatusProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
