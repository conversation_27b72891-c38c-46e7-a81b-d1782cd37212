package Request.MerchantService.v1.upgradeMid.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchAllBusiness extends AbstractApiV2 {
	
	public FetchAllBusiness()
    {
		   super("MerchantService/V1/upgradeMid/lead/FetchAllBusinessRequest.json", "MerchantService/V1/upgradeMid/lead/FetchAllBusinessResponse.json", "MerchantService/V1/upgradeMid/lead/FetchAllBusinessProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
