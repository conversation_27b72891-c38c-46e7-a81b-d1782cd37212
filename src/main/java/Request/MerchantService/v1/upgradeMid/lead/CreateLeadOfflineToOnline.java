package Request.MerchantService.v1.upgradeMid.lead;
import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;
public class CreateLeadOfflineToOnline extends AbstractApiV2 {
	 
	public CreateLeadOfflineToOnline() {
			  super("MerchantService/V1/upgradeMid/lead/CreateLeadOfflineToOnlineRequest.json", "MerchantService/V1/upgradeMid/lead/CreateLeadOfflineToOnlineResponse.json", "MerchantService/V1/upgradeMid/lead/CreateLeadProperties.properties");
			  replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	  
}
}