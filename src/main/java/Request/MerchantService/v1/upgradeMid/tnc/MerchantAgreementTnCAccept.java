package Request.MerchantService.v1.upgradeMid.tnc;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class MerchantAgreementTnCAccept extends AbstractApiV2 {
    public MerchantAgreementTnCAccept()
    {
        super("MerchantService/V1/upgradeMid/tnc/tncRequest.json", "MerchantService/V1/upgradeMid/tnc/tncResponse.json", "MerchantService/V1/upgradeMid/tnc/tncProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
