package Request.MerchantService.v1.MerchantCommonOnboard;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AudioDetails extends AbstractApiV2 {

    public AudioDetails()
    {
        super("MerchantService/v1/common/fetchScreenDetails/AudioDetailsRequest.json", "MerchantService/v1/common/fetchScreenDetails/AudioDetailsResponse.json", "MerchantService/v1/common/fetchScreenDetails/AudioDetailsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
