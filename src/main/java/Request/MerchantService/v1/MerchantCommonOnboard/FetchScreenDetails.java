package Request.MerchantService.v1.MerchantCommonOnboard;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchScreenDetails extends AbstractApiV2
{
    public FetchScreenDetails()

    {
        super("MerchantService/V1/MerchantCommonOnbaord/fetchScreenDetails.json", "MerchantService/V1/MerchantCommonOnbaord/fetchScreenDetailsResponse.json", "MerchantService/V1/MerchantCommonOnbaord/fetchScreenDetails.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}
