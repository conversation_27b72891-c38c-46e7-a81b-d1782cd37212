package Request.MerchantService.v1.MerchantCommonOnboard;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AdditionalDetailsFetchScreen extends AbstractApiV2 {

    public AdditionalDetailsFetchScreen()
    {
        super("MerchantService/V1/MerchantCommonOnbaord/AdditionalDetailsRequest.json", "MerchantService/V1/MerchantCommonOnbaord/AdditionalDetailsResponse.json", "MerchantService/V1/MerchantCommonOnbaord/AdditionalDetailsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
