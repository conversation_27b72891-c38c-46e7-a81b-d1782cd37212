package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class RevisitUnOrganised extends AbstractApiV2 {
    public RevisitUnOrganised(String Input)
    {
        super("MerchantService/V1/Revisit/RevisitOrganised/RevisitOrganisedRequest.json", "MerchantService/V1/Revisit/RevisitOrganised/RevisitOrganisedResponse.json", "MerchantService/V1/Revisit/RevisitOrganised/RevisitOrganisedProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("input", Input);
    }
}
