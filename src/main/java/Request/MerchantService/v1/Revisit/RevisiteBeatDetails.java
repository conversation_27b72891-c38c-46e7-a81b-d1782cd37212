package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class RevisiteBeatDetails extends AbstractApiV2 {
    public RevisiteBeatDetails(){
        super("MerchantService/V1/RevisiteBeatDetails/RevisiteBeatDetailsRequest.json" , "MerchantService/V1/RevisiteBeatDetails/RevisiteBeatDetailsResponse.json" , "MerchantService/V1/RevisiteBeatDetails/RevisiteBeatDetailsProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
