package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SendOtpRevisit extends AbstractApiV2 {
    public SendOtpRevisit(){
        super("MerchantService/V1/SendOtpRevisit/SendOtpRevisitRequest.json" , "MerchantService/V1/SendOtpRevisit/SendOtpRevisitResponse.json" , "MerchantService/V1/SendOtpRevisit/SendOtpRevisitProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
