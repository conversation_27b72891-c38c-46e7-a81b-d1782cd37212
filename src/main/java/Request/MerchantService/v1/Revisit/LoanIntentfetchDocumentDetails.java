package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LoanIntentfetchDocumentDetails extends AbstractApiV2 {
    public LoanIntentfetchDocumentDetails(){
        super("MerchantService/V1/LoanIntentfetchDocumentDetails/LoanIntentfetchDocumentDetailsRequest.json" , "MerchantService/V1/LoanIntentfetchDocumentDetails/LoanIntentfetchDocumentDetailsResponse.json" , "MerchantService/V1/LoanIntentfetchDocumentDetails/LoanIntentfetchDocumentDetailsproperties.properties");
        replaceUrlPlaceholder("base_url" ,  P.API.get("api_url"));
    }
}
