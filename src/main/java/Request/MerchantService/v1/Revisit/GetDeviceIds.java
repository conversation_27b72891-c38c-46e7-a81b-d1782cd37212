package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetDeviceIds extends AbstractApiV2 {
    public GetDeviceIds(String mid) {
        super("MerchantService/V1/GetDeviceIds/GetDeviceIdsRequest.json",
              "MerchantService/V1/GetDeviceIds/GetDeviceIdsResponse.json",
              "MerchantService/V1/GetDeviceIds/GetDeviceIdsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("mid", mid);
    }
} 