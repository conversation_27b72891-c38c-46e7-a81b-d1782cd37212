package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateOtpRevisit extends AbstractApiV2 {
    public ValidateOtpRevisit(){
        super("MerchantService/V1/ValidateOtpRevisit/ValidateOtpRevisitRequest.json" , "MerchantService/V1/ValidateOtpRevisit/ValidateOtpRevisitResponse.json" , "MerchantService/V1/ValidateOtpRevisit/ValidateOtpRevisitProperties.Properties");
        replaceUrlPlaceholder("base_url" ,  P.API.get("goldengate_api_url"));
    }
}
