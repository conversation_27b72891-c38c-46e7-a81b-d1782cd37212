package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class RevisitGetKybValidDocs extends AbstractApiV2 {
    public RevisitGetKybValidDocs(String mid){
        super("MerchantService/V1/RevisitGetKybValidDocs/RevisitGetKybValidDocsRequest.json" , "MerchantService/V1/RevisitGetKybValidDocs/RevisitGetKybValidDocsResponse.json" , "MerchantService/V1/RevisitGetKybValidDocs/RevisitGetKybValidDocsProperties.properties");
        replaceUrlPlaceholder("base_url" ,  P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("mid" , mid);
    }
}
