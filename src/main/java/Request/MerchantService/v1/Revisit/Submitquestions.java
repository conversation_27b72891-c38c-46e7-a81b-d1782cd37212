package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Submitquestions extends AbstractApiV2 {
    public Submitquestions(){
        super("MerchantService/V1/Submitquestions/SubmitquestionsRequest.json" , "MerchantService/V1/Submitquestions/SubmitquestionsResponse.json" , "MerchantService/V1/Submitquestions/Submitquestionsproperties.properties");
        replaceUrlPlaceholder("base_url" ,  P.API.get("goldengate_api_url"));
    }
}
