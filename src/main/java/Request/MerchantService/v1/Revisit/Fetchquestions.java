package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Fetchquestions extends AbstractApiV2 {
    public Fetchquestions(){
        super("MerchantService/V1/Fetchquestions/FetchquestionsRequests.json" , "MerchantService/V1/Fetchquestions/FetchquestionsResponse.json" , "MerchantService/V1/Fetchquestions/FetchquestionsProperties.properties");
        replaceUrlPlaceholder("base_url" ,  P.API.get("goldengate_api_url"));
    }
}
