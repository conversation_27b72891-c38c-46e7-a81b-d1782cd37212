package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class RevisitSubmit extends AbstractApiV2
{
    public RevisitSubmit(String ReqPath)
    {
        super(ReqPath, "MerchantService/V1/Revisit/RevisitSubmit/RevisitSubmitResponse.json", "MerchantService/V1/Revisit/RevisitSubmit/RevisitSubmitProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
