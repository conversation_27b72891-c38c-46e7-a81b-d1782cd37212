package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetMidRevisit extends AbstractApiV2 {
    public GetMidRevisit(){

        super("MerchantService/V1/GetMidRevisit/GetMidRevisitRequest.json" , "MerchantService/V1/GetMidRevisit/GetMidRevisitResponse.json" , "MerchantService/V1/GetMidRevisit/GetMidRevisitProperties.properties");

        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));

    }
}
