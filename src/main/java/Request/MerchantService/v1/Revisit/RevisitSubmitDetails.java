package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class RevisitSubmitDetails extends AbstractApiV2 {
    public RevisitSubmitDetails(){
        super("MerchantService/V1/RevisitSubmitDetails/RevisitSubmitDetailsRequest.json" , "MerchantService/V1/RevisitSubmitDetails/RevisitSubmitDetailsResponse.json" , "MerchantService/V1/RevisitSubmitDetails/RevisitSubmitDetailsProperties.properties");
        replaceUrlPlaceholder("base_url" ,  P.API.get("goldengate_api_url"));
    }

}
