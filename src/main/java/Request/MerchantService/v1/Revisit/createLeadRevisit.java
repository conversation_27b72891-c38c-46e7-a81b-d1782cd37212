package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class createLeadRevisit extends AbstractApiV2 {
    public createLeadRevisit(){
        super("MerchantService/V1/createLeadRevisit/createLeadRevisitRequest.json" , "MerchantService/V1/createLeadRevisit/createLeadRevisitResponse.json" , "MerchantService/V1/createLeadRevisit/createLeadRevisitProperties.properties");
        replaceUrlPlaceholder("base_url" ,  P.API.get("goldengate_api_url"));

    }
}
