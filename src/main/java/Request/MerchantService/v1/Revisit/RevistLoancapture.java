package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class RevistLoancapture extends AbstractApiV2 {
    public RevistLoancapture(){
        super("MerchantService/V1/RevisiteBeatDetails/LoanIntentCaptureRequest.json" , "MerchantService/V1/RevisiteBeatDetails/RevisiteBeatDetailsResponse.json" , "MerchantService/V1/RevisiteBeatDetails/RevisiteBeatDetailsProperties.Properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
    }
}
