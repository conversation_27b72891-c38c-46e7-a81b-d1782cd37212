package Request.MerchantService.v1.Revisit;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class MPAaddress extends AbstractApiV2 {
    public MPAaddress(String mid){
        super("MerchantService/V1/MPAaddress/MPAaddressRequest.json" , "MerchantService/V1/MPAaddress/MPAaddressResponse.json" , "MerchantService/V1/MPAaddress/MPAaddressProperties.properties");
        replaceUrlPlaceholder("base_url" , P.API.get("goldengate_api_url"));
        replaceUrlPlaceholder("mid" , mid);
    }
}
