package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateAccount extends AbstractApiV2 {
    public CreateAccount()
    {
        super("MerchantService/V1/sdMerchant/CreateAccount/CreateAccountRequest.json","MerchantService/V1/sdMerchant/CreateAccount/CreateAccountResponse.json","MerchantService/V1/sdMerchant/CreateAccount/CreateAccountProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}
