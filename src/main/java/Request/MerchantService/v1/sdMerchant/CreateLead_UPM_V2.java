package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLead_UPM_V2 extends AbstractApiV2
{

	public CreateLead_UPM_V2()
    {
        super("MerchantService/v1/sdMerchant/lead/CreateLead_UPM_V2/CreateLeadRequest.json","MerchantService/v1/sdMerchant/lead/CreateLead_UPM_V2/CreateLeadResponse.json","MerchantService/v1/sdMerchant/lead/CreateLead_UPM_V2/CreateLeadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
