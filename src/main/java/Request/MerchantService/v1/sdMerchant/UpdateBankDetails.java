package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateBankDetails extends AbstractApiV2 {

    public UpdateBankDetails(){

        super("MerchantService/v1/sdMerchant/UpdateBankDetails/updateBankDetailsRequest.json","MerchantService/v1/sdMerchant/UpdateBankDetails/updateBankDetailsResponse.json","MerchantService/v1/sdMerchant/UpdateBankDetails/updateBankDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }

    String required ;
    String  refId;
    String   statusCode;



    public String getRequired() {
        return required;
    }

    public void setRequired(String required) {
        this.required = required;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }
    @Override
    public String toString() {
        return "UpdateBankDetails{" +
                "required='" + required + '\'' +
                ", refId='" + refId + '\'' +
                ", statusCode='" + statusCode + '\'' +
                '}';
    }


}
