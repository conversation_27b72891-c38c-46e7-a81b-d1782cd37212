package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AcceptTermsAndConditionsEdcDIY extends AbstractApiV2
{
    public AcceptTermsAndConditionsEdcDIY() {

        super("MerchantService/v1/sdMerchant/TermsAndConditionsEdcDIY/TermsAndConditionsEdcDIYRequest.json",
                "MerchantService/v1/sdMerchant/TermsAndConditionsEdcDIY/TermsAndConditionsEdcDIYResponse.json",
                "MerchantService/v1/sdMerchant/TermsAndConditionsEdcDIY/TermsAndConditionsEdcDIYProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
