package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class updateLeadAddressPL_LOS extends AbstractApiV2{
	
	
	public updateLeadAddressPL_LOS() {

		super("MerchantService/V1/sdMerchant/lead/updateLeadAddressPL_LOS/leadRequest.json",
				"MerchantService/V1/sdMerchant/lead/updateLeadAddressPL_LOS/leadResponse.json",
				"MerchantService/V1/sdMerchant/lead/updateLeadAddressPL_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
	//QueryParams
 String solution;
 String entityType;
 String channel;
 String solutionTypeLevel2; 
 

 //Headers
 String ContentType;
 String session_token;
 
 // Response  body fields
 Integer statusCode;
 String relatedBusinessUuid;
 String refId;
 String  firstNameAsPerPan;
 String lastNameAsPerPan;
 String middleNameAsPerPan;
 String mobileNumber;
 
 
public String getSolution() {
	return solution;
}
public void setSolution(String solution) {
	this.solution = solution;
}
public String getEntityType() {
	return entityType;
}
public void setEntityType(String entityType) {
	this.entityType = entityType;
}
public String getChannel() {
	return channel;
}
public void setChannel(String channel) {
	this.channel = channel;
}
public String getSolutionTypeLevel2() {
	return solutionTypeLevel2;
}
public void setSolutionTypeLevel2(String solutionTypeLevel2) {
	this.solutionTypeLevel2 = solutionTypeLevel2;
}
public String getContentType() {
	return ContentType;
}
public void setContentType(String contentType) {
	ContentType = contentType;
}
public String getSession_token() {
	return session_token;
}
public void setSession_token(String session_token) {
	this.session_token = session_token;
}
public Integer getStatusCode() {
	return statusCode;
}
public void setStatusCode(Integer statusCode) {
	this.statusCode = statusCode;
}
public String getRelatedBusinessUuid() {
	return relatedBusinessUuid;
}
public void setRelatedBusinessUuid(String relatedBusinessUuid) {
	this.relatedBusinessUuid = relatedBusinessUuid;
}
public String getRefId() {
	return refId;
}
public void setRefId(String refId) {
	this.refId = refId;
}
public String getFirstNameAsPerPan() {
	return firstNameAsPerPan;
}
public void setFirstNameAsPerPan(String firstNameAsPerPan) {
	this.firstNameAsPerPan = firstNameAsPerPan;
}
public String getLastNameAsPerPan() {
	return lastNameAsPerPan;
}
public void setLastNameAsPerPan(String lastNameAsPerPan) {
	this.lastNameAsPerPan = lastNameAsPerPan;
}
public String getMiddleNameAsPerPan() {
	return middleNameAsPerPan;
}
public void setMiddleNameAsPerPan(String middleNameAsPerPan) {
	this.middleNameAsPerPan = middleNameAsPerPan;
}
public String getMobileNumber() {
	return mobileNumber;
}
public void setMobileNumber(String mobileNumber) {
	this.mobileNumber = mobileNumber;
}
@Override
public String toString() {
	return "updateLeadAddressPL_LOS [solution=" + solution + ", entityType=" + entityType + ", channel=" + channel
			+ ", solutionTypeLevel2=" + solutionTypeLevel2 + ", ContentType=" + ContentType + ", session_token="
			+ session_token + ", statusCode=" + statusCode + ", relatedBusinessUuid=" + relatedBusinessUuid + ", refId="
			+ refId + ", firstNameAsPerPan=" + firstNameAsPerPan + ", lastNameAsPerPan=" + lastNameAsPerPan
			+ ", middleNameAsPerPan=" + middleNameAsPerPan + ", mobileNumber=" + mobileNumber + "]";
}

	
	


}
