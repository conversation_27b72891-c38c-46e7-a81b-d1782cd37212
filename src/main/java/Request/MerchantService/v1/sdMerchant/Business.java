package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Business extends AbstractApiV2 {

    public Business(){

        super("MerchantService/v1/sdMerchant/Business/businessRequest.json","MerchantService/v1/sdMerchant/Business/businessResponse.json","MerchantService/v1/sdMerchant/Business/businessProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));


    }
    Integer statusCode;
    String leadId;
    String refId;

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public String getLeadId() {
        return leadId;
    }

    public void setLeadId(String leadId) {
        this.leadId = leadId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    @Override
    public String toString() {
        return "Business{" +
                "statusCode=" + statusCode +
                ", leadId='" + leadId + '\'' +
                ", refId='" + refId + '\'' +
                '}';
    }
}
