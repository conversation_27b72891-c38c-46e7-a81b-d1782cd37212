package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateIdentity extends AbstractApiV2 {

    public UpdateIdentity()
    {
        super("MerchantService/V1/sdMerchant/UpdateIdentity/UpdateIdentityRequest.json","MerchantService/V1/sdMerchant/UpdateIdentity/UpdateIdentityResponse.json","MerchantService/V1/sdMerchant/UpdateIdentity/UpdateIdentityProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}
