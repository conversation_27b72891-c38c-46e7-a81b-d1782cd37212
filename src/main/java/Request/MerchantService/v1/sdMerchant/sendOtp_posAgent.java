package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class sendOtp_posAgent extends AbstractApiV2
{
    public sendOtp_posAgent()
    {
        super("MerchantService/v1/sdMerchant/sendOtp/leadRequest.json","MerchantService/v1/sdMerchant/sendOtp/leadResponse.json","MerchantService/v1/sdMerchant/sendOtp/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
