package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateBankDetails extends AbstractApiV2 {

    public ValidateBankDetails()
    {
        super("MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsRequest.json","MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsResponse.json","MerchantService/v1/sdMerchant/ValidateBankDetails/validateBankDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }


        String displayMessage;
        String refId;
        String    statusCode;
        String  bankAccountHolderName;
        String bankDetailsUuid;
        String  nameMatchStatus;

    public String getDisplayMessage() {
        return displayMessage;
    }

    public void setDisplayMessage(String displayMessage) {
        this.displayMessage = displayMessage;
    }

    public String getRefId() {
        return refId;
    }

    public  void setRefId(String refId) {
        this.refId = refId;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getBankAccountHolderName() {
        return bankAccountHolderName;
    }

    public void setBankAccountHolderName(String bankAccountHolderName) {
        this.bankAccountHolderName = bankAccountHolderName;
    }

    public String getBankDetailsUuid() {
        return bankDetailsUuid;
    }

    public void setBankDetailsUuid(String bankDetailsUuid) {
        this.bankDetailsUuid = bankDetailsUuid;
    }

    public String getNameMatchStatus() {
        return nameMatchStatus;
    }

    public void setNameMatchStatus(String nameMatchStatus) {
        this.nameMatchStatus = nameMatchStatus;
    }

    @Override
    public String toString() {
        return "ValidateBankDetails{" +
                "displayMessage='" + displayMessage + '\'' +
                ", refId='" + refId + '\'' +
                ", statusCode=" + statusCode +
                ", bankAccountHolderName='" + bankAccountHolderName + '\'' +
                ", bankDetailsUuid='" + bankDetailsUuid + '\'' +
                ", nameMatchStatus='" + nameMatchStatus + '\'' +
                '}';
    }


}
