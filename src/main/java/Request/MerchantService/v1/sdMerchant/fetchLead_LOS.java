package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchLead_LOS  extends AbstractApiV2{
	
	
	public fetchLead_LOS()
    {

        super("MerchantService/V1/sdMerchant/lead/fetchLead_LOS/leadRequest.json","MerchantService/V1/sdMerchant/lead/fetchLead_LOS/leadResponse.json","MerchantService/v1/sdMerchant/lead/fetchLead_LOS/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	
	//QueryParams
    String solution;
    String entityType;
    String channel;
    
    
    //Headers
    String session_token;
    
    // Response  body fields
    Integer statusCode;
    String leadId;
    String refId;
    String mobileNumber;
    String pan;
    String nameAsPerPan;
    String ContentType;


    
    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
    
    
    public String getSession_token() {
        return session_token;
    }

    public void setSession_token(String session_token) {
        this.session_token = session_token;
    }
    
    
    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public String getLeadId() {
        return leadId;
    }

    public void setLeadId(String leadId) {
        this.leadId = leadId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobile(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getNameAsPerPan() {
        return nameAsPerPan;
    }

    public void setNameAsPerPan(String nameAsPerPan) {
        this.nameAsPerPan = nameAsPerPan;
    }
    
    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }
    
    
    @Override
    public String toString()
    {
        return "lead{" +
                "solution='" + solution + '\'' +
                ", entityType='" + entityType + '\'' +
                ", channel='" + channel + '\'' +
                ", ContentType='" + ContentType + '\'' +
                ", session_token='" + session_token + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", pan='" + pan + '\'' +
                ", nameAsPerPan='" + nameAsPerPan + '\'' +
                ", statusCode=" + statusCode +
                ", leadId='" + leadId + '\'' +
                ", refId='" + refId + '\'' +
                '}';
    }
    
    
    
    
    
    
    

}
