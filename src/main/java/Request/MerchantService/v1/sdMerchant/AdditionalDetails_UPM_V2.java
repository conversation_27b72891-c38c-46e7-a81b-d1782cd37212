package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AdditionalDetails_UPM_V2 extends AbstractApiV2{
	
	public AdditionalDetails_UPM_V2()
    {

        super("MerchantService/v1/sdMerchant/AdditionalDetails/additionalDetailsRequest.json","MerchantService/v1/sdMerchant/AdditionalDetails/additionalDetailsResponse.json","MerchantService/v1/sdMerchant/AdditionalDetails/additionalDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
