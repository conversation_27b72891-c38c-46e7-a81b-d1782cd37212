package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ApplicationStatus extends AbstractApiV2
{
    public ApplicationStatus()
    {
        super("MerchantService/V1/sdMerchant/lead/ApplicationStatus/ApplicationStatusRequest.json","MerchantService/V1/sdMerchant/lead/ApplicationStatus/ApplicationStatusResponse.json","MerchantService/V1/sdMerchant/lead/ApplicationStatus/ApplicationStatusProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
