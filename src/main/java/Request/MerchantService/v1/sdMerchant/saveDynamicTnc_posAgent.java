package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class saveDynamicTnc_posAgent extends AbstractApiV2
{
    public saveDynamicTnc_posAgent()
    {
        super("MerchantService/v1/sdMerchant/saveDynamicTnc_posAgent/leadRequest.json","MerchantService/v1/sdMerchant/saveDynamicTnc_posAgent/leadResponse.json","MerchantService/v1/sdMerchant/saveDynamicTnc_posAgent/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
