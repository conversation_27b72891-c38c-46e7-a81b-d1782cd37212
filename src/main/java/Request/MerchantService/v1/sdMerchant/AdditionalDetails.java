package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AdditionalDetails extends AbstractApiV2
{
    public AdditionalDetails(String ReqPath)
    {
        super(ReqPath,"MerchantService/v1/sdMerchant/additionalDetails/loanOffer/leadResponse.json","MerchantService/V1/sdMerchant/AdditionalDetails/loanOffer/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
    Integer statusCode;
    String relatedBusinessUuid;
    String refId;

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public String getRelatedBusinessUuid() {
        return relatedBusinessUuid;
    }

    public void setRelatedBusinessUuid(String relatedBusinessUuid) {
        this.relatedBusinessUuid = relatedBusinessUuid;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    @Override
    public String toString() {
        return "AdditionalDetails{" +
                "statusCode=" + statusCode +
                ", relatedBusinessUuid='" + relatedBusinessUuid + '\'' +
                ", refId='" + refId + '\'' +
                '}';
    }
}
