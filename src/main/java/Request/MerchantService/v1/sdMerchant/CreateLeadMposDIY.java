package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadMposDIY extends AbstractApiV2
{
    public CreateLeadMposDIY()
    {

        super("MerchantService/V1/sdMerchant/lead/CreateLeadMposDIY/MposDIYLeadRequest.json",
                "MerchantService/V1/sdMerchant/lead/createLeadCashAtPosDIY/CashAtPosDIYleadResponse.json",
                "MerchantService/V1/sdMerchant/lead/createLeadCashAtPosDIY/CashAtPosDIYleadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
