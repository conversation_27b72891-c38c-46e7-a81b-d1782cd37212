package Request.MerchantService.v1.sdMerchant.savebank;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveBankDetails extends AbstractApiV2{
	   
		public SaveBankDetails()
	    {
			   super("MerchantService/v1/sdMerchant/Bank/SaveBankRequest.json", "MerchantService/v1/sdMerchant/Bank/SaveBankResponse.json", "MerchantService/v1/sdMerchant/Bank/SaveBankProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public SaveBankDetails(boolean isEmandateTypePresent)
	    {
			   super("MerchantService/V1/sdMerchant/Bank/SaveBankWithEmandateTypeRequest.json", "MerchantService/v1/sdMerchant/Bank/SaveBankResponse.json", "MerchantService/v1/sdMerchant/Bank/SaveBankProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
}
