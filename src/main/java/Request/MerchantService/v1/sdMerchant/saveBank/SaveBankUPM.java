package Request.MerchantService.v1.sdMerchant.savebank;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveBankUPM extends AbstractApiV2{
	
	public SaveBankUPM()
	{
	
	super("MerchantService/v1/sdMerchant/SaveBank/SaveBankRequest.json","MerchantService/v1/sdMerchant/SaveBank/SaveBankResponse.json","MerchantService/v1/sdMerchant/SaveBank/SaveBankProperties.properties");
	replaceUrlPlaceholder("base_url",P.API.get("api_url"));	
	}
}
