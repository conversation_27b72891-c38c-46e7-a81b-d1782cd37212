package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class fetchDynamicTnc extends AbstractApiV2
{
    public fetchDynamicTnc()
    {
        super("MerchantService/v1/sdMerchant/fetchDynamicTnc/leadRequest.json","MerchantService/v1/sdMerchant/fetchDynamicTnc/leadResponse.json","MerchantService/v1/sdMerchant/fetchDynamicTnc/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
