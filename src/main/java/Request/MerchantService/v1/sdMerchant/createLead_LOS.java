package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class createLead_LOS  extends AbstractApiV2{
	
	
	
	public createLead_LOS()
    {

        super("MerchantService/V1/sdMerchant/lead/createLead_LOS/leadRequest.json","MerchantService/V1/sdMerchant/lead/createLead_LOS/leadResponse.json","MerchantService/v1/sdMerchant/lead/createLead_LOS/leadProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
	
	   //QueryParams
    String solution;
    String entityType;
    String channel;
    String solutionTypeLevel2; 

    //Headers
    String ContentType;
    String session_token;
    
    // Response  body fields
    Integer statusCode;
    String leadId;
    String refId;
    String displayMessage;

	
    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }
    
    
    public String getSolutionTypeLevel2() {
        return solutionTypeLevel2;
    }

    public void setsolutionTypeLevel2(String solutionTypeLevel2) {
        this.solutionTypeLevel2 = solutionTypeLevel2;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }
    public String getSession_token() {
        return session_token;
    }

    public void setSession_token(String session_token) {
        this.session_token = session_token;
    }
    
    public Integer getStatusCode() {
        return statusCode;
    }

    public Integer setStatusCode(Integer statusCode) {
        return this.statusCode = statusCode;
    }

    public String getLeadId() {
        return leadId;
    }

    public void setLeadId(String leadId) {
        this.leadId = leadId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }


    public String getDisplayMessage() {
        return displayMessage;
    }

    public void setDisplayMessage(String displayMessage) {
        this.displayMessage = displayMessage;
    }

    @Override
    public String toString()
    {
        return "lead{" +
                "solution='" + solution + '\'' +
                ", entityType='" + entityType + '\'' +
                ", channel='" + channel + '\'' +
                ", ContentType='" + ContentType + '\'' +
                ", session_token='" + session_token + '\'' +
                ", statusCode=" + statusCode +
                ", leadId='" + leadId + '\'' +
                ", refId='" + refId + '\'' +
                '}';
    }



    
    
    
    
	
	

}
