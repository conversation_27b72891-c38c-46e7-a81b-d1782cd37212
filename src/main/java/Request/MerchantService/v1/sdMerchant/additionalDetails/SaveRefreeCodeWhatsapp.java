package Request.MerchantService.v1.sdMerchant.additionalDetails;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SaveRefreeCodeWhatsapp extends AbstractApiV2
{
    public SaveRefreeCodeWhatsapp()
    {
    	super("MerchantService/v1/sdMerchant/SaveReferrerCodeAndWhatsappDetails/SaveReferrerCodeAndWhatsappRequest.json","MerchantService/v1/sdMerchant/SaveReferrerCodeAndWhatsappDetails/SaveReferrerCodeAndWhatsappResponse.json","MerchantService/v1/sdMerchant/SaveReferrerCodeAndWhatsappDetails/SaveReferrerCodeAndWhatsappProperties.properties");
    	replaceUrlPlaceholder("base_url",P.API.get("api_url"));	
    }
	
}
