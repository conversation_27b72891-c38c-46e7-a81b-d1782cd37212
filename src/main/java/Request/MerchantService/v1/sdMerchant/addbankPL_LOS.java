package Request.MerchantService.v1.sdMerchant;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class addbankPL_LOS extends AbstractApiV2 {

	public addbankPL_LOS() {

		super("MerchantServicev1sdMerchantbank/addbankPL_LOS/leadRequest.json",
				"MerchantServicev1sdMerchantbank/addbankPL_LOS/leadResponse.json",
				"MerchantServicev1sdMerchantbank/addbankPL_LOS/leadProperties.properties");
		replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}

	// query param
	String solution;
	String entityType;
	String channel;
	String solutionTypeLevel2;

	// Header
	String ContentType;
	String session_token;

	// Response Body
	String displayMessage;
	String refId;
	Integer statusCode;
	String stage;
	boolean nameMatchSuccess;

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getEntityType() {
		return entityType;
	}

	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getSolutionTypeLevel2() {
		return solutionTypeLevel2;
	}

	public void setSolutionTypeLevel2(String solutionTypeLevel2) {
		this.solutionTypeLevel2 = solutionTypeLevel2;
	}

	public String getContentType() {
		return ContentType;
	}

	public void setContentType(String contentType) {
		ContentType = contentType;
	}

	public String getSession_token() {
		return session_token;
	}

	public void setSession_token(String session_token) {
		this.session_token = session_token;
	}

	public String getDisplayMessage() {
		return displayMessage;
	}

	public void setDisplayMessage(String displayMessage) {
		this.displayMessage = displayMessage;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	public Integer getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}

	public String getStage() {
		return stage;
	}

	public void setStage(String stage) {
		this.stage = stage;
	}

	public boolean isNameMatchSuccess() {
		return nameMatchSuccess;
	}

	public void setNameMatchSuccess(boolean nameMatchSuccess) {
		this.nameMatchSuccess = nameMatchSuccess;
	}

	@Override
	public String toString() {
		return "addbankPL_LOS [solution=" + solution + ", entityType=" + entityType + ", channel=" + channel
				+ ", solutionTypeLevel2=" + solutionTypeLevel2 + ", ContentType=" + ContentType + ", session_token="
				+ session_token + ", displayMessage=" + displayMessage + ", refId=" + refId + ", statusCode="
				+ statusCode + ", stage=" + stage + ", nameMatchSuccess=" + nameMatchSuccess + "]";
	}

}
