package Request.MerchantService.v1.sdMerchant.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadLending extends AbstractApiV2{
	
	
	  public CreateLeadLending()
	    {
			   super("MerchantService/v1/sdMerchant/lead/createLeadRequest.json", "MerchantService/v1/sdMerchant/lead/createLeadResponse.json", "MerchantService/v1/sdMerchant/lead/createLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public CreateLeadLending(boolean flag)
	    {
			   super("MerchantService/v1/sdMerchant/lead/CreateLeadBusinessLendingRequest.json", "MerchantService/v1/sdMerchant/lead/CreateLeadBusinessLendingResponse.json", "MerchantService/v1/sdMerchant/lead/CreateLeadBusinessLendingProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public CreateLeadLending(String Solution)
	    {
			   super("MerchantService/v1/sdMerchant/lead/CreateLeadArilRequest.json", "MerchantService/v1/sdMerchant/lead/CreateLeadArilResponse.json", "MerchantService/v1/sdMerchant/lead/CreateLeadArilProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		
		public CreateLeadLending(String Solution, String solutionTypeLevel2)
	    {
			   super("MerchantService/v1/sdMerchant/lead/CreateLeadSSFBRequest.json", "MerchantService/v1/sdMerchant/lead/CreateLeadSSFBResponse.json", "MerchantService/v1/sdMerchant/lead/CreateLeadSSFBProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		 public CreateLeadLending(boolean isNewLender,String solution)
		    {
				   super("MerchantService/v1/sdMerchant/lead/createLeadABFLRequest.json", "MerchantService/v1/sdMerchant/lead/createLeadResponse.json", "MerchantService/v1/sdMerchant/lead/createLeadProperties.properties");

				   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
		    }
		 
		
		
}	