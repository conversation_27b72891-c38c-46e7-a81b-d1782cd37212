package Request.MerchantService.v1.sdMerchant.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadLendingAril extends AbstractApiV2 {
	
	
	
	
	public CreateLeadLendingAril(String  merchant_cust_id)
    {
		   super("MerchantService/v1/sdMerchant/lead/CreateLeadArilRequest.json", "MerchantService/v1/sdMerchant/lead/CreateLeadArilResponse.json", "MerchantService/v1/sdMerchant/lead/CreateLeadArilProperties.properties");
		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
		   replaceUrlPlaceholder("merchant_cust_id", merchant_cust_id);
    }
	
	
	
	public CreateLeadLendingAril(String  merchant_cust_id, boolean flag)
    {
		   super("MerchantService/v1/sdMerchant/lead/UpdateLeadArilRequest.json", "MerchantService/v1/sdMerchant/lead/CreateLeadArilResponse.json", "MerchantService/v1/sdMerchant/lead/CreateLeadArilProperties.properties");
		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
		   replaceUrlPlaceholder("merchant_cust_id", merchant_cust_id);
    }
	
	

}
