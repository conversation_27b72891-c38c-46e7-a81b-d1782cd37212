package Request.MerchantService.v1.sdMerchant.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BasicDetailsMCA  extends AbstractApiV2 {
	   
		public BasicDetailsMCA()
	    {
			   super("MerchantService/v1/Consumer/Lead/AddBasicDetailsRequest.json", "MerchantService/v1/Consumer/Lead/AddBasicDetailsResponse.json", "MerchantService/v1/Consumer/Lead/AddBasicDetailsProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
}
