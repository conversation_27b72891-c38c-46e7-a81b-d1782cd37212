package Request.MerchantService.v1.sdMerchant.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateLoanOffer extends AbstractApiV2{
	   
		public UpdateLoanOffer()
	    {
			   super("MerchantService/v1/sdMerchant/lead/UpdateLoanOfferRequest.json", "MerchantService/v1/sdMerchant/lead/UpdateLoanOfferResponse.json", "MerchantService/v1/sdMerchant/lead/UpdateLoanOfferProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}