package Request.MerchantService.v1.POS;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchPosPlans extends AbstractApiV2
{
    public FetchPosPlans()
    {
        super("MerchantService/V1/POS/FetchPosPlans/FetchPosPlanRequest.json", "MerchantService/V1/POS/FetchPosPlans/FetchPosPlanResponse.json", "MerchantService/V1/POS/FetchPosPlans/FetchPosPlanProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
