package Request.MerchantService.v1.workflow.lead;

import com.paytm.apitools.core.P;
import com.paytm.apitools.core.AbstractApiV2;

public class ExperianPull extends AbstractApiV2 {

    public ExperianPull(String requestBodyJsonPath)
    {
        super(requestBodyJsonPath,"MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");


        replaceUrlPlaceholder("base_url", P.API.get("ExperianPullURL"));
    }
}
