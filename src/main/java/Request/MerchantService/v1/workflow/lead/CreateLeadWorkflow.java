package Request.MerchantService.v1.workflow.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadWorkflow extends AbstractApiV2{
	   
		public CreateLeadWorkflow()
	    {
			   super("MerchantService/v1/workflow/Lead/CreateLeadRequest.json", "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public CreateLeadWorkflow(boolean isOfferAccepted)
	    {
			   super("MerchantService/V1/workflow/lead/offerAcceptRequest.json", "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		

		public CreateLeadWorkflow(String isLoanAgreementAccepted)
	    {
			   super("MerchantService/V1/workflow/lead/LoanAgreementAcceptRequest.json", "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public CreateLeadWorkflow(String requestJson,boolean isGenericRequest)
	    {
			   super(requestJson, "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}
