package Request.MerchantService.v1.workflow.lead.bank;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class NewBankAPI extends AbstractApiV2{
	   
	public NewBankAPI()
{
		   super("MerchantService/V1/workflow/lead/bank/NewBankAPIRequest.json", "MerchantService/v2/lending/lead/fetchCIR/FetchCIRResponse.json", "MerchantService/V2/lending/lead/fetchCIR/FetchCIRProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
}
	
	public NewBankAPI(String requestJson)
	{
			   super(requestJson, "MerchantService/v2/lending/lead/fetchCIR/FetchCIRResponse.json", "MerchantService/V2/lending/lead/fetchCIR/FetchCIRProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	}
}
