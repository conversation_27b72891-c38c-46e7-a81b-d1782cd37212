package Request.MerchantService.v1.workflow.lead.intiate.bureau;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AsyncBureau extends AbstractApiV2{
	   
	
	
		public AsyncBureau(String requestJson)
	    {
			   super(requestJson, "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }


}
