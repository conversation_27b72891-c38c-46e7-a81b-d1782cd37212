package Request.MerchantService.v1.workflow.lead.upload.document;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UploadDocumentNewAPI extends AbstractApiV2
{
	
	public UploadDocumentNewAPI()
    {
		   super("MerchantService/v2/lending/lead/document/UploadDocumentRequest.json", "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

		   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }


}