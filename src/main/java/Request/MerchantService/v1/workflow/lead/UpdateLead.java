package Request.MerchantService.v1.workflow.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateLead extends AbstractApiV2{
	   
	
		
		public UpdateLead(String requestJson)
	    {
			   super(requestJson, "MerchantService/v1/workflow/Lead/CreateLeadResponse.json", "MerchantService/V1/workflow/lead/CreateLeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }

}
