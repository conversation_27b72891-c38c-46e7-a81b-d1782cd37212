package Request.MerchantService.v1.workflow.lead.detail;

import com.paytm.apitools.core.AbstractApiV2;

public class FetchLeadBackfillingAPI extends AbstractApiV2{


	public FetchLeadBackfillingAPI(String stagingUrl)
    {
		   super("MerchantService/v1/Consumer/Lead/FetchLeadDetailsRequest.json", "MerchantService/v1/Consumer/Lead/FetchLeadDetailsResponse.json", "MerchantService/v1/Consumer/Lead/FetchLeadDetailsProperties.properties");

		   replaceUrlPlaceholder("base_url", stagingUrl);
    }

}
