package Request.MerchantService.v1.workflow.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchWhitelistDetails extends AbstractApiV2 {

    public FetchWhitelistDetails()
    {
        super("MerchantService/V1/workflow/lead/FetchWhitelistDetails.json","MerchantService/v1/workflow/Lead/FetchWhitelistDetailsResponse.json", "MerchantService/V1/workflow/lead/FetchWhitelistDetailsProperties.properties");


        replaceUrlPlaceholder("base_url", P.API.get("FetchWhitelistDetailsURL"));
    }
}
