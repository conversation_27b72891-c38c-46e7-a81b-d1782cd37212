package Request.MerchantService.v1.IOT;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateLeadIOTServiceFlow extends AbstractApiV2 {
    public CreateLeadIOTServiceFlow(String ReqPath)
    {
        super(ReqPath,"IOT/CreateLeadIOTServiceFlowResponse.json","IOT/CreateLeadIOTServiceFlowProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("iot_url"));
    }
}
