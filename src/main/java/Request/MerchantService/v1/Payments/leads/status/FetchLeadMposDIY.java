package Request.MerchantService.v1.Payments.leads.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchLeadMposDIY extends AbstractApiV2
{
    public FetchLeadMposDIY()
    {
        super("MerchantService/V1/Payment/OrderFullfillment/OrderFullfillmentLeadRequest.json","MerchantService/V1/Payment/OrderFullfillment/OrderFullfillmentResponse.json","MerchantService/V1/Payment/OrderFullfillment/OrderFullfillmentProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
