package Request.MerchantService.v1.GamePind;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class StartTest extends AbstractApiV2
{
    public StartTest()
    {
        super("MerchantService/V1/GamePind/StartTest/StartTestRequest.json","MerchantService/V1/GamePind/StartTest/StartTestResponse.json","MerchantService/V1/GamePind/StartTest/StartTestProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
