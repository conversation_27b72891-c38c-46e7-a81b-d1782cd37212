package Request.MerchantService.v1.GamePind;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchQuestion extends AbstractApiV2
{
    public FetchQuestion()
    {
        super("MerchantService/V1/GamePind/FetchQuestion/FetchQuestionRequest.json","MerchantService/V1/GamePind/FetchQuestion/FetchQuestionResponse.json","MerchantService/V1/GamePind/FetchQuestion/FetchQuestionProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
