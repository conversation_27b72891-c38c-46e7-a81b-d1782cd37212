package Request.MerchantService.v1.GamePind;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SubmitQuestion extends AbstractApiV2
{
    public SubmitQuestion()
    {
        super("MerchantService/V1/GamePind/SubmitQuestion/SubmitQuestionRequest.json","MerchantService/V1/GamePind/SubmitQuestion/SubmitQuestionResponse.json","MerchantService/V1/GamePind/SubmitQuestion/SubmitQuestionProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
