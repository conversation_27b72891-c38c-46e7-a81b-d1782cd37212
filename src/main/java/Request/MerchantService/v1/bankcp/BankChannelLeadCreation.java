package Request.MerchantService.v1.bankcp;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BankChannelLeadCreation extends AbstractApiV2
{
    public BankChannelLeadCreation(String ReqBody)
    {
        super(ReqBody, "MerchantService/V1/bankcp/BankChannelLeadCreationResponse.json", "MerchantService/V1/bankcp/BankChannelLeadCreationProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
