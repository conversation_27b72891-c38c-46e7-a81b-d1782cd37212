package Request.MerchantService.v1;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PostCompany extends AbstractApiV2 {
    public PostCompany()
    {
        super("MerchantService/V1/PostCompany/PostCompanyRequest.json", "MerchantService/V1/PostCompany/PostCompanyResponse.json", "MerchantService/V1/PostCompany/PostCompanyProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
