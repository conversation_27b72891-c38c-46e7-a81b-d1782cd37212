package Request.MerchantService.v1.kyc.ivr;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LeadRequest  extends AbstractApiV2 {

    public LeadRequest() {
        super("MerchantService/v1/kyc/ivr/LeadRequest/LeadRequestRequest.json","MerchantService/v1/kyc/ivr/LeadRequest/LeadRequestResponse.json","MerchantService/v1/kyc/ivr/LeadRequest/LeadRequestProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("bank_api_url"));
    }

    //Headers
    String ContentType;
    String xjwttoken;

    public String getContentType() {
        return ContentType;
    }

    public void setContentType(String contentType) {
        ContentType = contentType;
    }

    public String getXjwttoken() {
        return xjwttoken;
    }

    public void setXjwttoken(String xjwttoken) {
        this.xjwttoken = xjwttoken;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getBiometricConsent() {
        return biometricConsent;
    }

    public void setBiometricConsent(String biometricConsent) {
        this.biometricConsent = biometricConsent;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    //Request Body Params
    String mobile;
    String action;
    String biometricConsent;
    String timestamp;

    //Response Body Params
    String status;

}
