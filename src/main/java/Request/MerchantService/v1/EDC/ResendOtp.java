package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ResendOtp extends AbstractApiV2
{
    public ResendOtp()
    {
        super("MerchantService/V1/EDC/ResendOtp/ResendOtpLeadRequest.json", "MerchantService/V1/EDC/ResendOtp/ResendOtpResponse.json", "MerchantService/V1/EDC/ResendOtp/ResendOtpProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
