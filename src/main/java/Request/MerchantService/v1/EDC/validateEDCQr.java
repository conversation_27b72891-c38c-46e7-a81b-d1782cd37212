package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class validateEDCQr extends AbstractApiV2
{
    public validateEDCQr()
    {
        super("MerchantService/V1/EDC/validateEDCQr/validateEDCQrRequest.json", "MerchantService/V1/EDC/validateEDCQr/validateEDCQrResponse.json", "MerchantService/V1/EDC/validateEDCQr/validateEDCQrProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
