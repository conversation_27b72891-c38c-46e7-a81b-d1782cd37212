package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Edc extends AbstractApiV2
{
    public Edc()
    {
        super("MerchantService/V1/EDC/GetEDCPlan/GetEdcPlanRequest.json", "MerchantService/V1/EDC/GetEDCPlan/GetEdcPlanResponse.json", "MerchantService/V1/EDC/GetEDCPlan/GetEdcPlanProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
