package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchPayment extends AbstractApiV2
{
    public FetchPayment()
    {
        super("MerchantService/V1/EDC/FetchPayment/FetchPaymentRequest.json", "MerchantService/V1/EDC/FetchPayment/FetchPaymentResponse.json", "MerchantService/V1/EDC/FetchPayment/FetchPaymentProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
