package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class EdcPostAndroidWithPOS extends AbstractApiV2
{
    public EdcPostAndroidWithPOS(String ReqPath)
    {
        super(ReqPath, "MerchantService/V1/EDC/UpdateEdcLeadAndroidWithPOS/UpdateEdcLeadResponse.json", "MerchantService/V1/EDC/UpdateEdcLead/UpdateEdcLeadProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
