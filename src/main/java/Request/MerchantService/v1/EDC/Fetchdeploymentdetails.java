package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Fetchdeploymentdetails extends AbstractApiV2{

    public Fetchdeploymentdetails()
    {
        super("MerchantService/V1/EDC/FetchDeploymentDetails/FetchDeploymentDetailsRequest.json","MerchantService/V1/EDC/FetchDeploymentDetails/FetchDeploymentDetailsResponse.json","MerchantService/V1/EDC/FetchDeploymentDetails/FetchDeploymentDetailsProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("Fetchdeploymentdetails_url"));
    }
}

