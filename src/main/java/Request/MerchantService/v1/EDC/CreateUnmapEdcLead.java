package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateUnmapEdcLead extends AbstractApiV2
{
    public CreateUnmapEdcLead(String ReqPath)
    {
        super(ReqPath, "MerchantService/V1/EDC/CreateUnmapEdcLead/CreateUnmapEdcLeadResponse.json", "MerchantService/V1/EDC/CreateUnmapEdcLead/CreateUnmapEdcLeadProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
