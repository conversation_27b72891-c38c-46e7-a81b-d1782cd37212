package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchEdcUpgradePlans extends AbstractApiV2
{
    public FetchEdcUpgradePlans()
    {
        super("MerchantService/V1/EDC/FetchEDCUpgradePlans/FetchEDCUpgradePlansRequest.json", "MerchantService/V1/EDC/FetchEDCUpgradePlans/FetchEDCUpgradePlansResponse.json", "MerchantService/V1/EDC/FetchEDCUpgradePlans/FetchEDCUpgradePlansProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
