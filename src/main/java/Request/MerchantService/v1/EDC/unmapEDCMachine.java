package Request.MerchantService.v1.EDC;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class unmapEDCMachine extends AbstractApiV2
{
    public unmapEDCMachine(String ReqPath)
    {
        super(ReqPath, "MerchantService/V1/EDC/unmapEDCMachine/unmapEDCMachineResponse.json", "MerchantService/V1/EDC/unmapEDCMachine/unmapEDCMachineProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
