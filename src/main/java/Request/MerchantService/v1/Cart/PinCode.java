package Request.MerchantService.v1.Cart;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PinCode extends AbstractApiV2{
    public PinCode(String pinCode)
    {
        super("MerchantService/V1/PinCode/PinCodeRequest.json", "MerchantService/V1/PinCode/PinCodeResponse.json", "MerchantService/V1/PinCode/PinCodeResponseSchema.json");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("pinCode", pinCode);
    }
}
