package Request.MerchantService.v1.internal.workflow.lead.reset;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LeadReset extends AbstractApiV2{
	   
		public LeadReset()
	    {
			   super("MerchantService/DeleteLeads/DeleteRequest.json", "MerchantService/DeleteLeads/DeleteResponse.json", "MerchantService/DeleteLeads/DeleteProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}
