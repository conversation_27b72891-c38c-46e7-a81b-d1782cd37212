package Request.MerchantService.Fastag;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateTag extends AbstractApiV2 {

    public ValidateTag()

    {
        super("MerchantService/Fastag/ValidateTag/ValidateTagRequest.json", "MerchantService/Fastag/ValidateTag/ValidateTagResponse.json", "MerchantService/Fastag/ValidateTag/ValidateTagProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}