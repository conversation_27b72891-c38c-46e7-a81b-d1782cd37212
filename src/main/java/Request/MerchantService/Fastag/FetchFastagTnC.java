package Request.MerchantService.Fastag;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchFastagTnC extends AbstractApiV2 {

    public FetchFastagTnC()

    {
        super("MerchantService/Fastag/ValidateTag/ValidateTagRequest.json", "MerchantService/Fastag/ValidateTag/ValidateTagResponse.json", "MerchantService/Fastag/ValidateTag/ValidateTagProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}