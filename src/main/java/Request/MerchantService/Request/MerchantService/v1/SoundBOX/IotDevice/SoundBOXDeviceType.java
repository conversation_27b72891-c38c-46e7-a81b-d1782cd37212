package Request.MerchantService.Request.MerchantService.v1.SoundBOX.IotDevice;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundBOXDeviceType extends AbstractApiV2 {

    public SoundBOXDeviceType() {
        super("MerchantService/V1/SoundBox/SoundBoxCreateLead/iotDevice/SoundBoxDeviceTypeRequest.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/iotDevice/SoundBoxDeviceTypeResponse.json", "MerchantService/V1/SoundBox/SoundBoxCreateLead/iotDevice/SoundBoxDeviceTypeProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}

