package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class MidMobile extends AbstractApiV2
{
    public MidMobile()
    {
        super("MerchantService/V3/MidMobile/GetMIDRequest.json", "MerchantService/V3/MidMobile/GetMIDResponse.json", "MerchantService/V3/MidMobile/GetMID.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
