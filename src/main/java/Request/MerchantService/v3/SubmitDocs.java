package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class SubmitDocs extends AbstractApiV2 {

    public SubmitDocs() {
        super("MerchantService/V3/SubmitDocs/SubmitDocsRequest.json", "MerchantService/V3/SubmitDocs/SubmitDocsResponse.json", "MerchantService/V3/SubmitDocs/SubmitDocsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
