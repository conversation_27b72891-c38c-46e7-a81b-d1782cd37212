package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetBusinessv3 extends AbstractApiV2 {
    public GetBusinessv3()
    {
        super("MerchantService/V3/GetBusiness/GetBusinessRequest.json", "MerchantService/V3/GetBusiness/GetBusinessResponse.json", "MerchantService/V3/GetBusiness/GetBusinessProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
