package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateOtp extends AbstractApiV2 {

    public  ValidateOtp(String ReqPath)
    {
        super(ReqPath, "MerchantService/V3/ValidateOtp/ValidateOtpResponse.json", "MerchantService/V3/ValidateOtp/ValidateOtpProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

}
