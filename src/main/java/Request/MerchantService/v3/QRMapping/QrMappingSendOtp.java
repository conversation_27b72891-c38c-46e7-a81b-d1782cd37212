package Request.MerchantService.v3.QRMapping;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class QrMappingSendOtp extends AbstractApiV2{

	 public QrMappingSendOtp(){
	        super("MerchantService/V3/QrMappingSendOtp/QrMappingSendOtpRequest.json","MerchantService/V3/QrMappingSendOtp/QrMappingSendOtpResponse.json","MerchantService/V3/QrMappingSendOtp/QrMappingSendOtpProperties.properties");
	        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	    }
}