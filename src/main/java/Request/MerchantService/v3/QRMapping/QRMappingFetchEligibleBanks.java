package Request.MerchantService.v3.QRMapping;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class QRMappingFetchEligibleBanks extends AbstractApiV2 {

	 public QRMappingFetchEligibleBanks(){
		 super("MerchantService/V3/QRMappingFetchEligibleBanks/QRMappingFetchEligibleBanksRequest.json","MerchantService/V3/QRMappingFetchEligibleBanks/QRMappingFetchEligibleBanksResponse.json","MerchantService/V3/QRMappingFetchEligibleBanks/QRMappingFetchEligibleBanksProperties.properties");
		 replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	 }
}