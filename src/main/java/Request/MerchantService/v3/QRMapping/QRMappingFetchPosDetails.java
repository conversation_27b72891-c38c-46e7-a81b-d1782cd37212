package Request.MerchantService.v3.QRMapping;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class QRMappingFetchPosDetails extends AbstractApiV2 {

 	 public QRMappingFetchPosDetails(){
	 super("MerchantService/V3/QRMappingFetchPosDetails/QRMappingFetchPosDetailsRequest.json","MerchantService/V3/QRMappingFetchPosDetails/QRMappingFetchPosDetailsResponse.json","MerchantService/V3/QRMappingFetchPosDetails/QRMappingFetchPosDetailsProperties.properties");
	 replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
}
}