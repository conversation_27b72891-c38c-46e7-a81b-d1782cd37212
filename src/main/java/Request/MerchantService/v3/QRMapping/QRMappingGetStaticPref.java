package Request.MerchantService.v3.QRMapping;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class QRMappingGetStaticPref extends AbstractApiV2 {

 	 public QRMappingGetStaticPref(){
	 super("MerchantService/V3/QRMappingGetStaticPref/QRMappingGetStaticPrefRequest.json","MerchantService/V3/QRMappingGetStaticPref/QRMappingGetStaticPrefResponse.json","MerchantService/V3/QRMappingGetStaticPref/QRMappingGetStaticPrefProperties.properties");
	 replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
}
}