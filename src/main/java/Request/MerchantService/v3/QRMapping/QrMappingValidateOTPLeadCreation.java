package Request.MerchantService.v3.QRMapping;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class QrMappingValidateOTPLeadCreation extends AbstractApiV2{

	 public QrMappingValidateOTPLeadCreation(){
		 super("MerchantService/V3/QrMappingValidateOTPLeadCreation/QrMappingValidateOTPLeadCreationRequest.json","MerchantService/V3/QrMappingValidateOTPLeadCreation/QrMappingValidateOTPLeadCreationResponse.json","MerchantService/V3/QrMappingValidateOTPLeadCreation/QrMappingValidateOTPLeadCreationProperties.properties");	        replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	     replaceUrlPlaceholder("base_url", P.API.get("goldengate_api_url"));
	 }
}