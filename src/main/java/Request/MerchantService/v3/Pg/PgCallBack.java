package Request.MerchantService.v3.Pg;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PgCallBack extends AbstractApiV2
{
   public PgCallBack(String ReqPath)
    {
        super(ReqPath, "MerchantService/V3/PG/PgCallBack/PgCallBackResponse.json", "MerchantService/V3/PG/PgCallBack/PgCallBackProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
