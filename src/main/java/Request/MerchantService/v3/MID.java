package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class MID extends AbstractApiV2 {
    public MID()
    {
        super("MerchantService/V3/GetMID/GetMIDRequest.json", "MerchantService/V3/GetMID/GetMIDResponse.json", "MerchantService/V3/GetMID/GetMID.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
