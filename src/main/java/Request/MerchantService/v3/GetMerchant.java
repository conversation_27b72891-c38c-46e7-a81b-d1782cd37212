package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetMerchant extends AbstractApiV2 {

    public GetMerchant(String cusId)

    {
        super("MerchantService/V3/GetMerchant/GetMerchantRequest.json", "MerchantService/V3/GetMerchant/GetMerchantResponse.json", "MerchantService/V3/GetMerchant/GetMerchantProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("custId", cusId);
    }
}
