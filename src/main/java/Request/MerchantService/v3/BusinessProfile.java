package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class BusinessProfile extends AbstractApiV2 {
    public BusinessProfile()
    {
        super("MerchantService/V3/GetBusinessProfile/GetBusinessProfileRequest.json", "MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponse.json", "MerchantService/V3/GetBusinessProfile/GetBusinessProfileProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
