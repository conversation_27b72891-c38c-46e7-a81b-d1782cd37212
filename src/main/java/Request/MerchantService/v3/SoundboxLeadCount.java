package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundboxLeadCount extends AbstractApiV2 {
    public SoundboxLeadCount()
    {
        super("MerchantService/V3/GetSoundBoxLeadCount/LeadCountRequest.json", "MerchantService/V3/GetSoundBoxLeadCount/LeadCountResponse.json", "MerchantService/V3/GetSoundBoxLeadCount/LeadCount.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}