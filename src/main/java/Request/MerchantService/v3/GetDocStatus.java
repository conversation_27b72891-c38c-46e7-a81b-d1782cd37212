package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetDocStatus extends AbstractApiV2 {

    public  GetDocStatus()
    {
        super("MerchantService/V3/GetDocStatus/GetDocStatusRequest.json", "MerchantService/V3/GetDocStatus/GetDocStatusResponse.json", "MerchantService/V3/GetDocStatus/GetDocStatus.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
