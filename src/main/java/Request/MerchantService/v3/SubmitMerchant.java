package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SubmitMerchant extends AbstractApiV2 {

   public SubmitMerchant(String cusId,String ReqPath)
    {
        super(ReqPath, "MerchantService/V3/SubmitMerchant/SubmitMerchantResponse.json", "MerchantService/V3/SubmitMerchant/SubmitMerchantProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("custId", cusId);
    }
}
