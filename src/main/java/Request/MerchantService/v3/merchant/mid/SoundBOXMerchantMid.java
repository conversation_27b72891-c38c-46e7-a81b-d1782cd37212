package Request.MerchantService.v3.merchant.mid;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class SoundBOXMerchantMid extends AbstractApiV2 {

    public SoundBOXMerchantMid() {
        super("MerchantService/V3/GetMID/GetMIDRequest.json", "MerchantService/V3/GetMID/GetMIDResponse.json", "MerchantService/V3/GetMID/GetMID.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}


