package Request.MerchantService.v3.merchant.mid;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class Mobile extends AbstractApiV2 {

    public Mobile()
    {
        super("ChannelOnboarding/getMerchant/getMerchantReq.json", "ChannelOnboarding/getMerchant/getMerchantRes.json","ChannelOnboarding/getMerchant/getMerchant.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}
