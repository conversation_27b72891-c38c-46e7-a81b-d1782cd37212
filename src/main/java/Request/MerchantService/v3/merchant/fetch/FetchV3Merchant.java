package Request.MerchantService.v3.merchant.fetch;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchV3Merchant extends AbstractApiV2 {

    public FetchV3Merchant(String leadId)

    {
        super("MerchantService/V3/GetMerchant/GetMerchantRequest.json", "MerchantService/V3/GetMerchant/GetMerchantResponse.json", "MerchantService/V3/GetMerchant/GetMerchantProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("leadId", leadId);
    }
}