package Request.MerchantService.v3.kyc.ivr;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ValidateIvrRequest extends AbstractApiV2 {

    public ValidateIvrRequest() {
        super("MerchantService/V3/kyc/ivr/ValidateIvrRequest/ValidateIvrRequestRequest.json","MerchantService/V3/kyc/ivr/ValidateIvrRequest/ValidateIvrRequestResponse.json","MerchantService/V3/kyc/ivr/ValidateIvrRequest/ValidateIvrRequestProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("bank_api_url"));
    }
}
