package Request.MerchantService.v3;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class PennyDrop extends AbstractApiV2 {
    public PennyDrop()
    {
        super("MerchantService/V3/PennyDrop/PennyDropRequest.json", "MerchantService/V3/PennyDrop/PennyDropResponse.json", "MerchantService/V3/PennyDrop/PennyDropResponseSchema.json");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
