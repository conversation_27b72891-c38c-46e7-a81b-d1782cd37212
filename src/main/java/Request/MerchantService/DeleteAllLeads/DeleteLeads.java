package Request.MerchantService.DeleteAllLeads;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DeleteLeads extends AbstractApiV2{
	   
		public DeleteLeads()
	    {
			   super("MerchantService/DeleteLeads/DeleteRequest.json", "MerchantService/DeleteLeads/DeleteResponse.json", "MerchantService/DeleteLeads/DeleteProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}	
