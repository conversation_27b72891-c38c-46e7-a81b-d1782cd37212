package Request.MerchantService.panel.v1.diy.businessprofile;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchBusinessProfilePanel extends AbstractApiV2
{
    public FetchBusinessProfilePanel()
    {
        super("MerchantService/panel/v1/business/businessprofile/GetBusinessDetailsRequest.json", "MerchantService/panel/v1/business/businessprofile/GetBusinessDetailsResponse.json", "MerchantService/panel/v1/business/businessprofile/GetBusinessDetailsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
