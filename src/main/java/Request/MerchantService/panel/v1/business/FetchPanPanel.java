package Request.MerchantService.panel.v1.business;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchPanPanel extends AbstractApiV2
{
    public FetchPanPanel()
    {
        super("MerchantService/panel/v1/business/pan/FetchPanRequest.json", "MerchantService/panel/v1/business/pan/FetchPanResponse.json", "MerchantService/panel/v1/business/pan/FetchPanProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
