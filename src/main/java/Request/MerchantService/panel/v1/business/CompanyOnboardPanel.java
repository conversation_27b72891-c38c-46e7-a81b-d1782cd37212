package Request.MerchantService.panel.v1.business;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CompanyOnboardPanel extends AbstractApiV2
{
    public CompanyOnboardPanel()
    {
        super("MerchantService/panel/v1/business/lead/GetBusinessDetailsRequest.json", "MerchantService/panel/v1/business/lead/GetBusinessDetailsResponse.json", "MerchantService/panel/v1/business/lead/GetBusinessDetailsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
