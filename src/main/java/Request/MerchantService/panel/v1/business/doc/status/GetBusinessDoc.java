package Request.MerchantService.panel.v1.business.doc.status;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetBusinessDoc extends AbstractApiV2{
	   
		public GetBusinessDoc()
	    {
			   super("MerchantService/panel/v1/business/doc/status/GetBusinessDetailsRequest.json", "MerchantService/panel/v1/business/doc/status/GetBusinessDetailsResponse.json", "MerchantService/panel/v1/business/doc/status/GetBusinessDetailsProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
  }