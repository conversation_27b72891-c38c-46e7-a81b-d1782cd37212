package Request.MerchantService.panel.v1.solution.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetLeadDetails extends AbstractApiV2{
	   
		public GetLeadDetails()
	    {
			   super("MerchantService/panel/v1/solution/lead/LeadRequest.json", "MerchantService/panel/v1/solution/lead/LeadResponse.json", "MerchantService/panel/v1/solution/lead/LeadProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }


}
