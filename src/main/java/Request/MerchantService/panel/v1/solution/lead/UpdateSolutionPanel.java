package Request.MerchantService.panel.v1.solution.lead;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class UpdateSolutionPanel extends AbstractApiV2
{
    public UpdateSolutionPanel(String reqObj)
    {
        super(reqObj, "MerchantService/panel/v1/business/lead/GetBusinessDetailsResponse.json", "MerchantService/panel/v1/business/lead/GetBusinessDetailsProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
