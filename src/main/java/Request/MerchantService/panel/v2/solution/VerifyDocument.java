package Request.MerchantService.panel.v2.solution;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class VerifyDocument extends AbstractApiV2
{
    public VerifyDocument(String solution)
    {
        super("MerchantService/panel/v2/solution/verify/VerifyRequest.json", "MerchantService/panel/v2/solution/verify/VerifyProperties.properties", "MerchantService/panel/v2/solution/verify/VerifyProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        replaceUrlPlaceholder("solution", solution);
    }
}
