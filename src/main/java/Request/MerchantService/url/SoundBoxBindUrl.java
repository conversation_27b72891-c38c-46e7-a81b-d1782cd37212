package Request.MerchantService.url;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SoundBoxBindUrl extends AbstractApiV2
{
    public  SoundBoxBindUrl()
    {
        super("MerchantService/URL/SoundBoxBindURL/SoundBoxBindUrlRequest.json", "MerchantService/URL/SoundBoxBindURL/SoundBoxBindUrlResponse.json", "MerchantService/URL/SoundBoxBindURL/SoundBoxBindUrlProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }
}


