package Request.Shopinsurance;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ShopInsuranceConfirmPolicy extends AbstractApiV2
	{
		public ShopInsuranceConfirmPolicy(String RequestPath)
		{
			super(RequestPath,"shop_insurance/ConfirmPolicyResponse.json", "shop_insurance/ConfirmPolicyProperties.properties");

			replaceUrlPlaceholder("base_url", P.API.get("insurance_url"));
		}
	}

