package Request.StoreCashBack;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class StoreCashBackTnc extends AbstractApiV2 {


	public StoreCashBackTnc()
	{
		super("MerchantServicev1sdMerchantfetchDynamicTnC/DealsTncFetchRequest.json","MerchantServicev1sdMerchantfetchDynamicTnC/DealsTncFetchResponse.json","MerchantServicev1sdMerchantfetchDynamicTnC/DealsTncFetchProperties.properties");
		replaceUrlPlaceholder("base_url",P.API.get("api_url"));

	}


}
