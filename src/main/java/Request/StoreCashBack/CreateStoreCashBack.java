package Request.StoreCashBack;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class CreateStoreCashBack extends AbstractApiV2 {

    public CreateStoreCashBack(String requestBodyPath)
    {
        super(requestBodyPath,"MerchantService/V1/profile/update/StoreCashBack/StoreCashBackResponse.json","MerchantService/V1/profile/update/StoreCashBack/StoreCashBackProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }

    public CreateStoreCashBack()
    {
        super("MerchantServicev1sdMerchantfetchDynamicTnC/DealsTncFetchRequest.json","MerchantService/V1/profile/update/StoreCashBack/StoreCashBackResponse.json","MerchantService/V1/profile/update/StoreCashBack/StoreCashBackProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }


}

