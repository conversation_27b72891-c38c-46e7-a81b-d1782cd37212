package Request.OTPSellerPanel;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GetOtpFromSellerPanel extends AbstractApiV2 {

    public GetOtpFromSellerPanel()
    {
        super("OTP_Seller_Panel/OTPRequest.json", "OTP_Seller_Panel/OTPResponse.json", "OTP_Seller_Panel/OTP.properties");

        replaceUrlPlaceholder("base_url", P.API.get("SellerPanelURL"));
    }
}
