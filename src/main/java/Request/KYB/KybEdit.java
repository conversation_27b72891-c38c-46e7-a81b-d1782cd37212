package Request.KYB;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class KybEdit extends AbstractApiV2 {
    public KybEdit(String CustID, String SolutionType)

    {
        super("KYB/KybEdit/KybEditRequest.json", "KYB/KybEdit/KybEditResponse.json", "KYB/KybEdit/KybEditProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("kyb_url"));
        replaceUrlPlaceholder("CustId", CustID);
        replaceUrlPlaceholder("SolutionType", SolutionType);

    }
}
