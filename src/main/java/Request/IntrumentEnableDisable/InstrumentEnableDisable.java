package Request.IntrumentEnableDisable;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class InstrumentEnableDisable extends AbstractApiV2 {

    public InstrumentEnableDisable()
    {
        super("MerchantService/V1/profile/EnableDisable/EnableRequest.json","MerchantService/V1/profile/EnableDisable/updateResponse.json","MerchantService/V1/profile/EnableDisable/EnableDisableProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));

    }


}

