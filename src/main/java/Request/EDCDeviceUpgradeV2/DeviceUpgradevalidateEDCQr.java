package Request.EDCDeviceUpgradeV2;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DeviceUpgradevalidateEDCQr extends AbstractApiV2{
	
	public DeviceUpgradevalidateEDCQr() {
		super("EDCDeviceUpgradeV2/DeviceUpgradevalidateEDCQr/DeviceUpgradevalidateEDCQrRequest.json","EDCDeviceUpgradeV2/DeviceUpgradevalidateEDCQr/DeviceUpgradevalidateEDCQrResponse.json","EDCDeviceUpgradeV2/DeviceUpgradevalidateEDCQr/DeviceUpgradevalidateEDCQrProperties.properties");        
		replaceUrlPlaceholder("base_url" , P.API.get("Staging27_url"));

	}

}
