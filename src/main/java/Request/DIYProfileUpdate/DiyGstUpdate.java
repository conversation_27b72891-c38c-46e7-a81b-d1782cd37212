package Request.DIYProfileUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class DiyGstUpdate extends AbstractApiV2 {
    public DiyGstUpdate(String requestPath) {
        super(requestPath, "MerchantService/V1/sdMerchant/lead/createLeadMerchantUpgradeLimitResponse.json", "MerchantService/V1/sdMerchant/lead/createLeadMerchantLimitUpgradeProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}