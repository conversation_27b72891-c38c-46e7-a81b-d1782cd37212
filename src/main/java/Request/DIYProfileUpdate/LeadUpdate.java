package Request.DIYProfileUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LeadUpdate extends AbstractApiV2 {

    public LeadUpdate(String RequestPath){
        super(RequestPath,"MerchantService/V1/sdMerchant/lead/updatePanLeadMerchantUpgradeLimitResponse.json","MerchantService/V1/sdMerchant/lead/updatePanLeadMerchantUpgradeLimitProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
