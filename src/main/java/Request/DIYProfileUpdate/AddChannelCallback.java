package Request.DIYProfileUpdate;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class AddChannelCallback extends AbstractApiV2 {

    public AddChannelCallback(String requestPath) {
        super(requestPath, "MerchantService/V1/boss/addChannelCallbackResponse.json", "MerchantService/V1/boss/addChannelCallbackProperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}
