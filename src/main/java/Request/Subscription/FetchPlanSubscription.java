package Request.Subscription;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchPlanSubscription extends AbstractApiV2 {

	
	public FetchPlanSubscription()
	{
		super("Subscription/Plan/PlanFetchRequest.json","Subscription/Plan/PlanFetchResponse.json","Subscription/Plan/PlanFetchproperties.properties");
		replaceUrlPlaceholder("base_url",P.API.get("subscription_url"));	
				
	}
	
	
}
