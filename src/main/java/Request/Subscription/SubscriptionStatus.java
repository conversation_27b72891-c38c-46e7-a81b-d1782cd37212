package Request.Subscription;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SubscriptionStatus extends AbstractApiV2
{
    public SubscriptionStatus(String ReqBody)
    {
        super(ReqBody,"Subscription/PlanOnboard/PlanOnboardResponse.json","Subscription/PlanOnboard/PlanOnboardproperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("subscription_url"));
    }
}
