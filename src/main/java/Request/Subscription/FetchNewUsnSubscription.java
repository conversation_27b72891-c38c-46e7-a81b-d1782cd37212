package Request.Subscription;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchNewUsnSubscription extends AbstractApiV2
{
    public FetchNewUsnSubscription()
    {
        super("Subscription/Plan/PlanFetchRequest.json","Subscription/Plan/PlanFetchResponse.json","Subscription/Plan/PlanFetchproperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("subscription_url"));

    }
}
