package Request.Subscription;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class SubscriptionPlanOnboarding extends AbstractApiV2 {

	
	public SubscriptionPlanOnboarding()
	{
		super("Subscription/PlanOnboard/PlanOnboardRequest.json","Subscription/PlanOnboard/PlanOnboardResponse.json","Subscription/PlanOnboard/PlanOnboardproperties.properties");
		replaceUrlPlaceholder("base_url",P.API.get("subscription_url"));	
				
	}
	
	
}
