package Request.Subscription;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class ReplaceSubscriptionRequest extends AbstractApiV2
{
    public ReplaceSubscriptionRequest(String ReqBody)
    {
        super(ReqBody,"Subscription/Plan/PlanFetchResponse.json","Subscription/Plan/PlanFetchproperties.properties");
        replaceUrlPlaceholder("base_url", P.API.get("subscription_url"));

    }
}
