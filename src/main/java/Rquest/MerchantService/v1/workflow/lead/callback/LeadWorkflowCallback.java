package Rquest.MerchantService.v1.workflow.lead.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class LeadWorkflowCallback extends AbstractApiV2{
	   
		public LeadWorkflowCallback()
	    {
			   super("MerchantService/v1/workflow/lead/callback/LeadWorkflowCallbackRequest.json", "MerchantService/v1/workflow/lead/callback/LeadWorkflowCallbackResponse.json", "MerchantService/V1/workflow/lead/callback/LeadWorkflowCallbackProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
		
		public LeadWorkflowCallback(String requestBodyPath)
	    {
			   super(requestBodyPath, "MerchantService/v1/workflow/lead/callback/LeadWorkflowCallbackResponse.json", "MerchantService/V1/workflow/lead/callback/LeadWorkflowCallbackProperties.properties");

			   replaceUrlPlaceholder("base_url", P.API.get("api_url"));
	    }
}
