package Rquest.MerchantService.v1.workflow.lead.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;


public class CKYCID extends AbstractApiV2{


    public CKYCID(String requestjsonckyc)
    {

        super(requestjsonckyc, "MerchantService/V1/workflow/lead/PostpaidV3CKYCIdFound.json", "MerchantService/V2/lending/lead/fetchCIR/FetchCIRProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }

    public CKYCID()
    {

        super("MerchantService/V1/workflow/lead/PostpaidV3CKYCIdFound.json", "MerchantService/V2/lending/lead/fetchCIR/FetchCIRProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("api_url"));
    }
}