package Rquest.MerchantService.v1.workflow.lead.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

    public class fetchCIRv3 extends AbstractApiV2 {


        public fetchCIRv3(String requestjson)
        {
            super(requestjson, "MerchantService/V2/lending/lead/fetchCIR/FetchCIRResponse.json", "MerchantService/V2/lending/lead/fetchCIR/FetchCIRProperties.properties");

            replaceUrlPlaceholder("base_url", P.API.get("api_url"));
        }
    }
