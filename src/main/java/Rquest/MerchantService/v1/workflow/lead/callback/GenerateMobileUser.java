package Rquest.MerchantService.v1.workflow.lead.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class GenerateMobileUser extends AbstractApiV2 {

    public GenerateMobileUser()
    {
        super("MerchantService/V1/workflow/lead/OAuthUserRandomNumber.json", "MerchantService/V1/workflow/lead/OAuthUserRandomNumberResponse.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("Oauth_url"));
    }
}
