package Rquest.MerchantService.v1.workflow.lead.callback;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.P;

public class FetchCustId extends AbstractApiV2 {

    public FetchCustId()
    {
        super("MerchantService/V1/workflow/lead/GetCustIdviaMobileNumber.json", "MerchantService/V1/workflow/lead/OAuthUserRandomNumberResponse.json", "MerchantService/v2/lending/dataUpdate/UpdateSAIProperties.properties");

        replaceUrlPlaceholder("base_url", P.API.get("Oauth_url"));
    }
}
