package Rquest;

import org.testng.annotations.BeforeSuite;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

public class DatabaseDetails {

    public static String user_name= "";
    public static String database_name= "";
    public static String host_name= "";


    @Parameters({"db_name_xml","ssh_host_xml","ssh_user_xml"})
    @Test()
    public void config(String db_name_xml , String ssh_host_xml , String ssh_user_xml ) {
        database_name = db_name_xml;
        host_name =ssh_host_xml;
        user_name = ssh_user_xml;
    }
}
