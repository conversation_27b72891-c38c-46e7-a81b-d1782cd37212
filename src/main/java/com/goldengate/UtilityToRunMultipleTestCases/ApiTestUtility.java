package com.goldengate.UtilityToRunMultipleTestCases;

import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.core.http.HttpMethodType;

public class ApiTestUtility extends AbstractApiV2 {

    public ApiTestUtility(String MethodName,String reqPath, String resPath, String prop,String Uri)
    {

        //String rqPath, String rsPath, Properties properties, String uri, HttpMethodType methodType
        super(reqPath,resPath,prop,Uri,HttpMethodType.valueOf(MethodName));
    }

    public ApiTestUtility(String Uri)
    {
        this("GET","ApiTestUtility/ApiTestUtilityRequest.json","ApiTestUtility/ApiTestUtilityResponse.json","ApiTestUtility/ApiTestUtilityProperties.properties",Uri);
    }

    public ApiTestUtility(String ReqBody,String Uri)
    {
        this("POST",ReqBody,"ApiTestUtility/ApiTestUtilityResponse.json","ApiTestUtility/ApiTestUtilityProperties.properties",Uri);
    }
}
