package com.goldengate.UtilityToRunMultipleTestCases;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestGetAPIs extends BaseMethod {

    BaseApiFormation CommonApi = new BaseApiFormation();
    public static Map<String,String> CommonApiHeaders = new HashMap<>() ;
    public static Map<String,String> CommonApiQueryParam = new HashMap<>();
    public static String ApiUrl = "";

    public static final Logger LOGGER = LogManager.getLogger(TestGetAPIs.class);

    @Test(description = "For Positive Fetch Request")
    @Owner(emailId = "<EMAIL>")
    public void GetPositive()
    {
        LOGGER.info(" This is API Path : " + ApiUrl);

        LOGGER.info(" For Positive Fetch Request : " );
        CommonApi.CallGetApi(ApiUrl,CommonApiQueryParam,CommonApiHeaders);

    }

    @Test(description = "Where Params are not present in Fetch Request")
    @Owner(emailId = "<EMAIL>")

    public void GetWithNoParams()
    {
        LOGGER.info(" Where Params are not present in Fetch Request : " );
        Map<String,String> QueryParam = new HashMap<>();
        CommonApi.CallGetApi(ApiUrl,QueryParam,CommonApiHeaders);
    }

    @Test(description = "Where Headers are not present in Fetch request")
    @Owner(emailId = "<EMAIL>")

    public void GetWithNoHeader()
    {
        LOGGER.info(" Where Headers are not present in Fetch request : " );
        Map<String,String> Headers = new HashMap<>();
        CommonApi.CallGetApi(ApiUrl,CommonApiQueryParam,Headers);
    }

    @Test(description = "Where Params are empty in Fetch request")
    @Owner(emailId = "<EMAIL>")

    public void GetWithEmptyParam()
    {
        LOGGER.info(" Where Params are empty in Fetch request : " );
        Map<String,String> Params = new HashMap<String, String>(CommonApiQueryParam);
        Params.put("entityType","");
        Params.put("solution","");

        LOGGER.info("This is Param Map : " +Params);
        CommonApi.CallGetApi(ApiUrl,Params,CommonApiHeaders);

    }

    @Test(description = "Where Entity is Individual in Fetch request")
    @Owner(emailId = "<EMAIL>")
    public void GetWithIndividualEntity()
    {
        LOGGER.info(" Where Entity is Individual in Fetch request: " );
        Map<String,String> Params = new HashMap<String, String>(CommonApiQueryParam);
        Params.put("entityType","INDIVIDUAL");

        LOGGER.info("This is Param Map : " +Params);
        CommonApi.CallGetApi(ApiUrl,Params,CommonApiHeaders);
    }

    @Test(description = "Where Entity is Public Limited in Fetch request")
    @Owner(emailId = "<EMAIL>")
    public void GetWithPublicLimitedEntity()
    {
        LOGGER.info(" Where Entity is Public Limited  in Fetch request: " );
        Map<String,String> Params = new HashMap<String, String>(CommonApiQueryParam);
        Params.put("entityType","PUBLIC_LIMITED");

        LOGGER.info("This is Param Map : " +Params);
        CommonApi.CallGetApi(ApiUrl,Params,CommonApiHeaders);
    }

    @Test(description = "Where Entity is Trust in Fetch request")
    @Owner(emailId = "<EMAIL>")
    public void GetWithTrustEntity()
    {
        LOGGER.info(" Where Entity is Trust  in Fetch request: " );
        Map<String,String> Params = new HashMap<String, String>(CommonApiQueryParam);
        Params.put("entityType","TRUST");

        LOGGER.info("This is Param Map : " +Params);
        CommonApi.CallGetApi(ApiUrl,Params,CommonApiHeaders);
    }

}
