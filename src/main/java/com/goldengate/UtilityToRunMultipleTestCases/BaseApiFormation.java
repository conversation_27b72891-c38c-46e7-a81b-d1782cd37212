package com.goldengate.UtilityToRunMultipleTestCases;

import com.goldengate.common.BaseMethod;
import io.restassured.response.Response;

import java.util.HashMap;
import java.util.Map;

public class BaseApiFormation extends BaseMethod {

    Map<String,String> Headers = new HashMap<>() ;
    Map<String,String> QueryParam = new HashMap<>();
    Map<String,String> Body= new HashMap<>() ;
    public static String URI = "";
    public static String RequestBody = "";

    Response Get()
    {
        ApiTestUtility ApiObj = new ApiTestUtility(URI);


        for (Map.Entry m : Headers.entrySet()) {
            ApiObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : QueryParam.entrySet()) {
            ApiObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }


        Response RespObj = ApiObj.callAPI();

        return RespObj;
    }

    Response Post()
    {
        ApiTestUtility ApiObj = new ApiTestUtility(RequestBody,URI);


        for (Map.Entry m : Headers.entrySet()) {
            ApiObj.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : QueryParam.entrySet()) {
            ApiObj.addParameter(m.getKey().toString(), m.getValue().toString());
        }

        for (Map.Entry m : Body.entrySet()) {
            ApiObj.getProperties().setProperty(m.getKey().toString(), m.getValue().toString());
        }

        Response RespObj = ApiObj.callAPI();

        return RespObj;
    }

    public Response CallGetApi(String Uri,Map<String ,String>queryParam,Map<String ,String>headers)
    {
        Headers.putAll(headers);
        QueryParam.putAll(queryParam);
        URI = Uri;
        Response GetResp = Get();
        return GetResp;
    }

    public Response CallPostpi(String Uri,String ReqBody,Map<String ,String>headers,Map<String ,String>queryParam,Map<String ,String>body)
    {
        Headers = headers;
        QueryParam = queryParam;
        Body = body;
        URI = Uri;
        RequestBody = ReqBody;
        Response PostResp = Post();
        return PostResp;
    }
}
