package com.goldengate.common;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class ExcelReader {

    XSSFWorkbook wb;
    XSSFSheet wbsheet;

    public ExcelReader(String filePath, String fileName) throws IOException {

        // Create a object of File class to open xlsx file

        File file = new File(filePath + "/" + fileName);

        // Create an object of FileInputStream class to read excel file

        FileInputStream inputStream = new FileInputStream(file);

        // Workbook wb = null;

        // Find the file extension by spliting file name in substring and getting only
        // extension name

        // String fileExtensionName = fileName.substring(fileName.indexOf("."));

        // Check condition if the file is xlsx file

        // if(fileExtensionName.equals(".xlsx")){

        // If it is xlsx file then create object of XSSFWorkbook class

        wb = new XSSFWorkbook(inputStream);

    }

    public String from(String sheetName, int rowName, int columnName)

    {
        // Read sheet inside the workbook by its name
        wbsheet=wb.getSheet(sheetName);
        //wbsheet = wb.getSheetAt(sheetName);
        System.out.println(wbsheet);
        // Create a loop over all the rows of excel file to read it
        Row row = wbsheet.getRow(rowName);
        //System.out.println(row);
        Cell cell1 = row.getCell(columnName);
       // System.out.println(cell1);
        DataFormatter formatter = new DataFormatter(); // creating formatter using the default locale
        String data = formatter.formatCellValue(cell1);
        return data;

    }

    public void from(String sheetName) {
        wbsheet = wb.getSheet(sheetName);
        DataFormatter formatter = new DataFormatter(); // creating formatter using the default locale

        try {
            Iterator<Row> itr = wbsheet.iterator();

            // Iterating over Excel file in Java
            while (itr.hasNext()) {
                Row row = itr.next();

                // Iterating over each column of Excel file
                Iterator<Cell> cellIterator = row.cellIterator();
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    String data = formatter.formatCellValue(cell);
                    System.out.println(data);

                }
                System.out.println(""+"\t");
            }

        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void fromColWise(String sheetName) {
        wbsheet = wb.getSheet(sheetName);
        DataFormatter formatter = new DataFormatter(); // creating formatter using the default locale

        try {

            Map<String, Integer> Column = new HashMap<String,Integer>(); //Create map
            XSSFRow row = wbsheet.getRow(0); //Get first row
            //following is boilerplate from the java doc
            int minColIx = row.getFirstCellNum(); //get the first column index for a row
            int maxColIx = row.getLastCellNum(); //get the last column index for a row

            for(int colIx=minColIx; colIx<maxColIx; colIx++) { //loop from first to last index
                XSSFCell cell = row.getCell(colIx); //get the cell

                Column.put(cell.getStringCellValue(),cell.getColumnIndex()); //add the cell contents (name of column) and cell index to the map
                System.out.println(Column);


            }


        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getRowCount(int sheetIndex)

    {

        System.out.println("inside getRowCount Method");

        int rowCount = wb.getSheetAt(sheetIndex).getLastRowNum();

        System.out.println("total no. of rows are" + rowCount);

        rowCount = rowCount + 1;

        return rowCount;
    }

}
