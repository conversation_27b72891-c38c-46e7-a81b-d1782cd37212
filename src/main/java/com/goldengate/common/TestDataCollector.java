package com.goldengate.common;

import java.util.HashMap;
import java.util.Map;

public class TestDataCollector {
    public Map<String, Object> collectTestData() {
        // In a real project, collect data from your VCS, CI/CD pipeline, or previous test results
        Map<String, Object> data = new HashMap<>();
        data.put("recentCommits", "Sample commit data");
        data.put("testHistory", "Sample test history data");
        return data;
    }
}