package com.goldengate.common;

import Request.MerchantService.oe.panel.v1.ReallocateAgent.ReallocateAgent;
import Request.MerchantService.oe.panel.v1.fileProcess.upload.SheetUpload;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.TokenXMV;
import Request.MerchantService.v2.upgradeMid.doc.FetchDocumentStatus;
import Request.MerchantService.v2.upgradeMid.doc.uploadCancelledCheque;
import Request.MerchantService.v3.MID;
import Request.MerchantService.v3.Pg.PgCallBack;
import Request.MerchantService.v3.SubmitDocs;
import Request.MerchantService.v4.FetchDynamicDocs;
import Request.OMS.CreateQROMS;
import Request.OMS.FetchOMSOrder;
import Request.OTPSellerPanel.GetOtpFromSellerPanel;
import Request.PG.ActiveTerminalInPG;
import Request.PG.CreateTerminalInPG;
import Request.PG.OnboardBanks;
import Request.PGP.AppPay;
import Request.Subscription.CreateSubscription;
import Request.UPI.*;
import Request.Wallet.GenerateUpiQr;
import Request.Wallet.GetQrDetails;
import Request.Wallet.WalletAddMoney;
import Request.oAuth.oAuthWormhole.CreateUser;
import Request.oAuth.oAuthWormhole.UserDetails;
import Request.oAuth.oauth2.Authorize;
import Request.oAuth.oauth2.Token;
import Services.MechantService.MiddlewareServices;
import Services.OMS.CreateQR;
import Services.OMS.FetchOrderOMS;
import Services.PG.PGServices;
import Services.PGP.PGPServices;
import Services.Subscription.SubscriptionPlan;
import Services.UPI.UpiServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import Services.oAuth.oAuthServices;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.UtilityToRunMultipleTestCases.TestGetAPIs;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.NotFoundException;
import com.google.zxing.Result;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import io.restassured.http.Headers;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.openqa.selenium.By;
import org.openqa.selenium.UnexpectedAlertBehaviour;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.remote.CapabilityType;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;
import redis.clients.jedis.Jedis;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import org.json.JSONException;
import io.restassured.path.json.JsonPath;

public class BaseMethod {
    private static final String SSH_PRIVATE_KEY_PATH = System.getProperty("user.dir") + "/src/main/resources/id_rsa_jyoti";
   private static final String SSH_USER_NEW = P.CONFIG.get("sshUser2");

   private static final String SSH_HOST_NEW = P.CONFIG.get("sshHost");

    private static final int SSH_PORT = 22;

    private static final String redisHost = "nonprod-gg-oe1redis-ro.p52jza.ng.0001.aps1.cache.amazonaws.com";
    private static final int redisPort = 6379;
    private static final int redisDatabase = 5;

    public static final Logger log = LogManager.getLogger(BaseMethod.class);

    public static String PinCode = "";
    public static String IFSC = "";
    public static String UPIAccountID = "";
    public static String accountNo = "";
    public static String ifscCode = "";
    public static String accountHolderName = "";
    public static String bankName = "";
    public static String PaymentToken = "";
    public static String PGToken = "";
    public static String SEClientId = "oe-subscription-client";
    public static String SEClientToken = "E7MYmjY7sZNONEGtcPbM5lYjk2sBG+RSsyJVeMqF8DQMd+ToQSJ3tU4ii3Sz/YD0/L6+CG4SoWccRgcuaKiasQ==";

    public String XMWCookie;
    public static String CommonAgentToken;
    public static String SellerPanelCookie = "market-merchant.sid=s%**********************************.yG1X%2B4IHQXp2%2Fcbz0UP%2FKeayafG8rhcHiBCKiFhpng8";
    //  public static String SellerPanelCookie = "X-MW-TOKEN=2c9aba29-33ed-4de9-b17d-1ef8a8ac4097";


    public static String DeviceIP = "************";
    public static String PayMID = "";
    public static String PayMGUID = "";
    public static String OrderId = "";
    public static String Amount = "";
    public static String SsoToken = "a68f958e-6072-4cd9-b38c-9ecd8de35600";
    // public static String SsoToken = "d6105923-f82a-4474-a267-ecf6defb4000";

//    public static String DbName = "sprint48_1";
//   // public static String DbName = "sprint35_1";
//    //TODO - DbStaging6 = Staging 6 DB(Where PG Callback is Received)
//    public static String DbStaging6 = "staging6_48_1"; // sprint23_2_staging6// TODO: Set PG Callback Staging Env DB
//    // TODO - DbStagingSprint = Current staging Environment DB
//    public static String DbStagingSprint = "sprint48_1"; //staging14_sprint25 TODO: Set Current Staging6 Env DB


  // public static String DbName = "staging6_30Dec24"; // Staging6
 public static String DbName = "staging6_30Dec24"; //Staging6

 //  public static String DbName = "releaseNovW2"; //Staging7
   // public static String DbName = "releaseAugW3_s1"; //Staging1


    // public static String DbName = "sprint35_1";
    //TODO - DbStaging6 = Staging 6 DB(Where PG Callback is Received)
    public static String DbStaging6 = "staging6_30Dec24"; // sprint23_2_staging6// TODO: Set PG Callback Staging Env DB
    // TODO - DbStagingSprint = Current staging Environment DB
    public static String DbStagingSprint = "new_release_17Oct24"; //staging14_sprint25 TODO: Set Current Staging6 Env DB
    // public static String DbStagingSprint = "sprint35_1"; //staging14_sprint25 TODO: Set Current Staging6 Env DB
//testing

    ConnectServer connectServer = new ConnectServer();
    public static String PGProxyHost = "*************";
    public static String PGProxyUser = "read";
    public static String PGProxyPassword = "read";
    public static String excelSheetName = "TestData.xlsx";
    public String currentFilePath = System.getProperty("user.dir") + "/src/main/resources";

    public static String fileName = "cancelCheque.pdf";

    protected Properties prop = new Properties();

    public Properties configProperties() {

        try {

            String FilePath = System.getProperty("user.dir") + "/src/main/resources/_config.properties";
            String displayMessageFilePath = System.getProperty("user.dir") + "/src/main/resources/LendingDisplayMessage.properties";
            String requestJsonPath = System.getProperty("user.dir") + "/src/main/resources/RequestJsonPath.properties";
            FileInputStream Locator = new FileInputStream(FilePath);
            FileInputStream messages = new FileInputStream(displayMessageFilePath);
            FileInputStream requestPath = new FileInputStream(requestJsonPath);
            prop.load(Locator);
            prop.load(messages);
            prop.load(requestPath);
            return prop;

        } catch (IOException e) {
            // TODO: handle exception
            e.printStackTrace();
            log.info(e.getMessage());
        }
        return null;
    }


    public ExcelReader getTestData() {

        try {

            //String currentFilePath = System.getProperty("user.dir") + "/src/main/resources";
            ExcelReader readData = new ExcelReader(currentFilePath, excelSheetName);
            //String readData1= readData.toString();
            return readData;

        } catch (Exception e) {
            // TODO: handle exception
        }
        return null;
    }

    //This method returns recent OTP by providing mobile number
    public String getOTP(String phoneNumber) throws JSchException, IOException {
        String otp = "";

        for (int i = 0; i <= 4; i++) {
            if (otp.equals("") || otp.equals(null)) {
                try {
                    ConnectServer.connectToServer(PGProxyHost, PGProxyUser, PGProxyPassword);
                    otp = ConnectServer.getOtp(phoneNumber);
                    ConnectServer.closeConnection();


                } catch (Exception e) {
                    log.info("Exp in get OTP : " + e);
                }
            } else {
                break;
            }
        }
        return otp;
    }

    //This starts the explicit wait
    public void waitForLoad(long miliSeconds) {
        log.info("Explicit Wait Started");
        try {
            synchronized (this) {
                this.wait(miliSeconds);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        log.info("Explicit Wait End");
    }

    //Lazy initialization for CommonAgentToken
    public static String getCommonAgentToken() {
        if (CommonAgentToken == null || CommonAgentToken.isEmpty()) {
            try {
                CommonAgentToken = AgentSessionToken("8010630022", "paytm@123");
                log.info("CommonAgentToken initialized: " + CommonAgentToken);
            } catch (Exception e) {
                log.error("Failed to initialize CommonAgentToken: " + e.getMessage(), e);
                CommonAgentToken = ""; // Set to empty string to avoid null pointer exceptions
            }
        }
        return CommonAgentToken;
    }

    //Lazy initialization for XMWCookie
    public String getXMWCookie() {
        if (XMWCookie == null || XMWCookie.isEmpty()) {
            try {
                XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");
                log.info("XMWCookie initialized: " + XMWCookie);
            } catch (Exception e) {
                log.error("Failed to initialize XMWCookie: " + e.getMessage(), e);
                XMWCookie = ""; // Set to empty string to avoid null pointer exceptions
            }
        }
        return XMWCookie;
    }

    //Generates GG/PSA agent token
    public static String AgentSessionToken(String Number, String Password) {

        String AgentToken = "";
        oAuthServices GetToken = new oAuthServices();
        Authorize Oauth = new Authorize();
        Token Token = new Token();
        Oauth.setResponse_type("code");
        Oauth.setDo_not_redirect("true");
        Oauth.setScope("paytm");
        Oauth.setClient_id("GG-OE-staging");
        Oauth.setUsername(Number);
        Oauth.setPassword(Password);
        Oauth.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
        Oauth.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
        Response getCode = GetToken.oauthorizeGG(Oauth);
        String code = getCode.jsonPath().getString("code");
        log.info("Code is " + code);
        if (code != null) {
            log.info("Inside If Condition");
            Token.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
            Token.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
            Token.setCode(code);
            Token.setGrant_type("authorization_code");
            Token.setClient_id("staging-golden-gate");
            Token.setScope("paytm");
            Response resToken = GetToken.tokenGG(Token);
            AgentToken = resToken.jsonPath().getString("access_token");

            log.info("This is Token " + AgentToken);
            log.info("This is Response Object : " + resToken.getBody().prettyPrint());

        } else {
            log.info("There must be some issue in Oauth ");
        }
        return AgentToken;
    }


    //Generates Applicant token for DIY users
    public static String ApplicantToken(String Number, String Password) {

        String ApplicantSessionToken = "";
        oAuthServices GetToken = new oAuthServices();
        Authorize Oauth = new Authorize();
        Token Token = new Token();
        Oauth.setResponse_type("code");
        Oauth.setDo_not_redirect("true");
        Oauth.setScope("paytm");
        Oauth.setClient_id("GG-OE-staging");
        Oauth.setUsername(Number);
        Oauth.setPassword(Password);
        Oauth.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
        Oauth.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
        Response getCode = GetToken.oAuthoauth2Autharize(Oauth);
        String code = getCode.jsonPath().getString("code");
        log.info("Code is " + code);
        if (code != null) {
            log.info("Inside If Condition");
            Token.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
            Token.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
            Token.setCode(code);
            Token.setGrant_type("authorization_code");
            Token.setClient_id("GG-OE-staging");
            Token.setScope("paytm");
            Response resToken = GetToken.oAuthoauth2Token(Token);
            ApplicantSessionToken = resToken.jsonPath().getString("access_token");
            log.info("This is Token " + ApplicantSessionToken);

        } else {
            log.info("There must be some issue in Oauth ");
        }
        return ApplicantSessionToken;
    }

    //Generate paytm App token for the registered user
    public static String PaytmAppSsoToken(String PaytmNumber, String PaytmPassword) {
        String PaytmAppToken = "";
        oAuthServices GetToken = new oAuthServices();
        Authorize Oauth = new Authorize();
        Token Token = new Token();
        Oauth.setResponse_type("code");
        Oauth.setDo_not_redirect("true");
        Oauth.setScope("paytm");
        Oauth.setClient_id("market-app-staging");
        Oauth.setUsername(PaytmNumber);
        Oauth.setPassword(PaytmPassword);
        Oauth.setAuthorization("Basic bWFya2V0LWFwcC1zdGFnaW5nOmE4M2Q1NDJhLWViNGQtNGI4ZS04ZWViLTkzMTZjYzBlNjgwOA==");
        Oauth.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
        Response getCode = GetToken.oauthorizePaytmApp(Oauth);
        String code = getCode.jsonPath().getString("code");
        log.info("Code is " + code);
        if (code != null) {
            log.info("Inside If Condition");
            Token.setAuthorization("Basic bWFya2V0LWFwcC1zdGFnaW5nOmE4M2Q1NDJhLWViNGQtNGI4ZS04ZWViLTkzMTZjYzBlNjgwOA==");
            Token.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
            Token.setCode(code);
            Token.setGrant_type("authorization_code");
            Token.setClient_id("market-app-staging");
            Token.setScope("paytm");
            Response resToken = GetToken.tokenPaytmApp(Token);
            PaytmAppToken = resToken.jsonPath().getString("access_token");
            log.info("This is Token " + PaytmAppToken);
            log.info("This is Response Object : " + resToken.getBody().prettyPrint());

        } else {
            log.info("There must be some issue in Oauth ");
        }
        return PaytmAppToken;
    }

    //This Method returns QR Code Id by providing base64 and image output path

    public static String QrCodeExtractor(String base64, String pathQrCode) throws IOException, NotFoundException {
        String QrCodeId = "";
        try {
            //Converting Base64
            String originalInput = base64;
            byte[] result = Base64.getDecoder().decode(originalInput);
            String Rev = Arrays.toString(result);

            log.info("This is Byte Array : " + Arrays.toString(result));
            log.info("This is Byte Array in String : " + Rev);

            //Converting Byte Array into Image
            ByteArrayInputStream bis = new ByteArrayInputStream(result);
            log.info("Object of Byte Array Input Stream : " + bis);
            BufferedImage bImage2 = ImageIO.read(bis);
            log.info("Reading from object of input stream of byte array  : " + bImage2);

            ImageIO.write(bImage2, "jpg", new File(pathQrCode));

            // Reading Image Data and storing its result
            BinaryBitmap binaryBitmap = new BinaryBitmap(new HybridBinarizer(
                    new BufferedImageLuminanceSource(
                            ImageIO.read(new FileInputStream(pathQrCode)))));
            Result qrCodeResult = new MultiFormatReader().decode(binaryBitmap);


            QrCodeId = qrCodeResult.toString();

            log.info("This is QR ID: " + qrCodeResult);
            log.info("This is QR Code ID in String : " + QrCodeId);

        } catch (Exception e) {
            log.info("This is Exception" + e);
            log.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());
        }
        return QrCodeId;
    }


    //This method returns current epoch time
    public static Long TimeEpoch() {
        Date today = Calendar.getInstance().getTime();

        // Constructs a SimpleDateFormat using the given pattern
        SimpleDateFormat crunchifyFormat = new SimpleDateFormat("MMM dd yyyy HH:mm:ss.SSS zzz");

        // format() formats a Date into a date/time string.
        String currentTime = crunchifyFormat.format(today);
        log.info("Current Time = " + currentTime);

        long epochTime = 0;

        try {

            // parse() parses text from the beginning of the given string to produce a date.
            Date date = crunchifyFormat.parse(currentTime);

            // getTime() returns the number of milliseconds since January 1, 1970, 00:00:00 GMT represented by this Date object.
            epochTime = date.getTime();

            log.info("Current Time in Epoch: " + epochTime);

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return epochTime;
    }

    // Wallet API to fetch merchant details from QR Code ID
    public static void FetchQrDetails(String QrCodeId) {
        WalletServices walletServicesObj = new WalletServices();

        GetQrDetails fetchQrObj = new GetQrDetails();

        Response fetchQrResponse = walletServicesObj.fetchQrDetailsWallet(fetchQrObj, QrCodeId, SsoToken);

        PayMID = fetchQrResponse.jsonPath().getJsonObject("response.mappingId").toString();
        log.info("MID to Pay : " + PayMID);
        PayMGUID = fetchQrResponse.jsonPath().getJsonObject("response.MERCHANT_GUID").toString();
        log.info("MGUID to Pay : " + PayMGUID);
        Amount = fetchQrResponse.jsonPath().getJsonObject("response.TXN_AMOUNT").toString();
        log.info("Amount to be Payed : " + Amount);
        OrderId = fetchQrResponse.jsonPath().getJsonObject("response.ORDER_ID").toString();
        log.info("OrderId Generated : " + OrderId);

    }


    //PG API to perform transaction for an orderId
    public static String PayMerchant(String OrderID, String PayAmount, String PayMerchantMID, String ReqBodyType) {
        PGPServices pgpServicesObj = new PGPServices();
        String EpochTime = TimeEpoch().toString();
        String StatusCode = "";
        AppPay payObj = new AppPay(P.TESTDATA.get(ReqBodyType));

        Map<String, String> body = new HashMap<String, String>();
        body.put("merchantUniqueReference", OrderID);
        body.put("orderId", OrderID);
        body.put("txnAmount", PayAmount);
        body.put("mid", PayMerchantMID);
        String SSOToken1 = ApplicantToken("5555508546", "paytm@123");
        body.put("token", SSOToken1);
        body.put("requestTimestamp", EpochTime);

        Response payResp = pgpServicesObj.payMerchant(payObj, SSOToken1, body);

        StatusCode = payResp.jsonPath().getJsonObject("body.resultInfo.resultCode").toString();
        log.info("Status Code from PGP is : " + StatusCode);

        return StatusCode;

    }

    //Reallocating Agent in OE Panel
    public void ReallocatingAgent(String LeadId, String agentId) {
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        log.info("Inside ReallocatingAgent Method ");
        ReallocateAgent v1Reallocate = new ReallocateAgent();

        v1Reallocate.addParameter("reallocationType", "ALLOCATED_AGENT");

        v1Reallocate.getProperties().setProperty("leadIds", LeadId);
        v1Reallocate.getProperties().setProperty("agentId", agentId);

        Response v1ReallocateAgentResp = middlewareServicesObject.ReallocateAgent(v1Reallocate, getXMWCookie());

        String Message = v1ReallocateAgentResp.jsonPath().getJsonObject("message");
        log.info("API Status message is : " + Message);
        Assert.assertTrue(Message.contains("SUCCESS"));

        int ErrorCode = v1ReallocateAgentResp.getStatusCode();
        Assert.assertEquals(ErrorCode, 200);

        v1Reallocate.validateResponseAgainstJSONSchema("MerchantService/OE/Panel/v1/ReallocateAgent/ReallocateAgentResponseSchema.json");


    }

    //This Method is use to create new user in Oauth
    protected void CreateApplicantOauth(String mobileNo) {
        int StatusCode = 0;

        for (int i = 0; i <= 3; i++) {
            log.info("Inside Oauth Register Loop & Value of I : " + i);
            if (StatusCode != 200) {
                log.info("Status Code is not 200");
                CreateUser OauthObj = new CreateUser();
                OauthObj.setHeader("Content-Type", "application/json");
                OauthObj.getProperties().setProperty("mobile", mobileNo);
                OauthObj.getProperties().setProperty("loginPassword", "paytm@123");
                Response OauthResp = OauthObj.callAPI();
                StatusCode = OauthResp.getStatusCode();
            } else {
                log.info("User Created");
                break;
            }
        }
    }

    //This Methods return user CustId by giving Phone or email by declaring in statergy
    public static String FetchUserDetails(String Data, String Stratergy) {
        String userId = "";
        for (int i = 0; i < 3; i++) {
            if (userId.isEmpty()) {
                oAuthServices OauthObj = new oAuthServices();

                UserDetails oauthFetchUser = new UserDetails();

                Map<String, String> body = new HashMap<String, String>();

                body.put("userData", Data);
                body.put("fetch_strategy", Stratergy);

                Response OauthFetchUserRes = OauthObj.OauthUserDetails(oauthFetchUser, body);

                int StatusCode = OauthFetchUserRes.getStatusCode();
                Assert.assertEquals(StatusCode, 200);

                userId = OauthFetchUserRes.jsonPath().getJsonObject("userId");

            } else {
                log.info("UserId Found : " + userId);
                break;
            }
        }
        return userId;
    }

    //This Method returns User MID by giving CustId
    public String FetchMID(String CustId) {
        MID v3FetchMid = new MID();
        String UserMID = "";
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();
//        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,CommonAgentToken,"3.9.1",CustId,"");
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid, getCommonAgentToken(), "5.4.4", CustId, "");

        UserMID = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString();
        log.info("Applicant's MID is : " + UserMID);
        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        return UserMID;

    }

    //This method is used to fetch and either REJECT or APPROVE all the document present in the lead
    public Map<String, String> FetchPanelLead(@NotNull Map<String, String> PanelBody) throws JsonProcessingException {
        List<Object> DMS = new ArrayList<>();
        List<Object> DMSforUUID = new ArrayList<>();
        List<Object> DocType = new ArrayList<>();
        List<Object> DocProvided = new ArrayList<>();
        List<Object> DocumentRequest = new ArrayList<>();
        List<Object> NameOfDoc = new ArrayList<>();
        List<Object> TypeOfDoc = new ArrayList<>();
        String OePanelDocStatus = PanelBody.get("docStatus");
        String RejectionReason = PanelBody.get("rejectionReason");
        String DocumetRequestDeserialised = "";
        List<Object> GetDocuments = new ArrayList<>();
        String WorkFlowId = "";
        Map<String, String> ReturnPanel = new HashMap<>();
        String GSTIN = "07" + PanelBody.get("pan") + "1Z1";

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(PanelBody.get("leadId"));

        waitForLoad(12000);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, getXMWCookie());
        WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();

        log.info("Before Capturing Document Array");
        GetDocuments = v1FetchLeadResp.jsonPath().getList("leadDetails.documents");
        log.info("This is Document Array serialized : " + GetDocuments);

        // log.info("This is Doc Array Deserialized" + mapper.writeValueAsString(v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents")));

        ObjectMapper mapper = new ObjectMapper();

        int sizeLop = GetDocuments.size() - 1;
        log.info("This is Size of Loop : " + sizeLop);


        for (int i = 0; i <= sizeLop; i++) {
            int iterarion = i + 1;
            log.info("Inside For Loop of Document and iteration is : " + iterarion);

            Map docPovided = (Map) GetDocuments.get(i);
            log.info("Current Document Map is : " + docPovided);

            log.info("Setting Values in array");

            DocType.add(i, docPovided.get("documentType"));
            log.info(" Doc Type is : " + DocType.get(i));

            DocProvided.add(i, docPovided.get("docProvided"));
            log.info(" Doc Provided is : " + DocProvided.get(i));

            NameOfDoc.add(i, docPovided.get("name"));
            log.info("Name of Doc is : " + NameOfDoc.get(i));

            TypeOfDoc.add(i, docPovided.get("type"));
            log.info("Type of Doc is : " + TypeOfDoc.get(i));


            if (docPovided.get("uuid") == null) {
                log.info("If UUIDS are there  ");
                DMS.add(i, docPovided.get("uuids"));
                DMSforUUID.add(i, null);

                Map<Object, Object> DocMapOne = new HashMap<>();
                DocMapOne.put("documentType", DocType.get(i));
                DocMapOne.put("docProvided", DocProvided.get(i));
                DocMapOne.put("uuids", DMS.get(i));
                DocMapOne.put("name", NameOfDoc.get(i));
                DocMapOne.put("type", TypeOfDoc.get(i));
                DocMapOne.put("status", OePanelDocStatus);
                DocMapOne.put("rejectionReason", RejectionReason);
                DocMapOne.put("osv", true);

                if (DocType.get(i).toString().equals("poi") && DocProvided.get(i).toString().equals("aadhaar") && OePanelDocStatus.equals("APPROVED")) {
                    Utilities accObj = new Utilities();
                    String aadhaar = "";
                    aadhaar = accObj.ValidAadhaar();
                    log.info("Valid Aadhaar No. is : " + aadhaar);
                    DocMapOne.put("docValue", aadhaar);
                }
                if (DocProvided.get(i).toString().equals("goiGstCertificate")) {
                    DocMapOne.put("docValue", GSTIN);
                }

                log.info("This is Doc Map : " + DocMapOne);

                DocumentRequest.add(i, DocMapOne);

            } else {
                log.info("If UUID is There ");
                DMS.add(i, docPovided.get("uuid"));
                DMSforUUID.add(i, docPovided.get("uuids"));
                log.info("Print DMS for UUID : " + DMSforUUID);

                Map<Object, Object> DocMapTwo = new HashMap<>();
                DocMapTwo.put("documentType", DocType.get(i));
                DocMapTwo.put("docProvided", DocProvided.get(i));
                DocMapTwo.put("uuid", DMS.get(i));
                DocMapTwo.put("uuids", DMSforUUID.get(i));
                DocMapTwo.put("name", NameOfDoc.get(i));
                DocMapTwo.put("type", TypeOfDoc.get(i));
                DocMapTwo.put("status", OePanelDocStatus);
                DocMapTwo.put("rejectionReason", RejectionReason);
                DocMapTwo.put("osv", true);

                if (DocProvided.get(i).toString().equals("goiGstCertificate")) {
                    DocMapTwo.put("docValue", GSTIN);
                }
                if (DocType.get(i).toString().equals("poi") && DocProvided.get(i).toString().equals("aadhaar") && OePanelDocStatus.equals("APPROVED")) {
                    Utilities accObj = new Utilities();
                    String aadhaar = "";
                    aadhaar = accObj.ValidAadhaar();
                    log.info("Valid Aadhaar No. is : " + aadhaar);
                    DocMapTwo.put("docValue", aadhaar);
                }

                log.info("This is Doc Map : " + DocMapTwo);

                DocumentRequest.add(i, DocMapTwo);
            }

            log.info("Print DMS for UUID : " + DMSforUUID);
            log.info("This is Doument Request ArrayList : " + DocumentRequest);

            DocumetRequestDeserialised = mapper.writeValueAsString(DocumentRequest);
            log.info("This is Doument Request Deserialised : " + DocumetRequestDeserialised);
        }
        String leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        log.info("This is Current Lead Stage : " + leadStage);
        //Assert.assertTrue(leadStage.contains(PanelBody.get("leadStage")));

        log.info("Clearing All ArrayList");
        DocType.clear();
        DocProvided.clear();
        NameOfDoc.clear();
        TypeOfDoc.clear();
        DMS.clear();
        DMSforUUID.clear();
        DocumentRequest.clear();

        ReturnPanel.put("DocumenstArray", DocumetRequestDeserialised);
        ReturnPanel.put("LeadStage", leadStage);
        ReturnPanel.put("WorkFlowId", WorkFlowId);
        return ReturnPanel;
    }

    //This Method can be used where "/panel/v1/business/doc/status" API is used for fetch Doc status
    // And "/oe/panel/v1/document/multiPart" API is Used for Doc Upload
    public void PanelMultipartDocUpload(Map<String, String> queryParams, Map<String, String> headers, Map<String, String> queryParamsDoc, Map<String, String> headersDoc, File UploadDoc) throws JsonProcessingException, InterruptedException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        Response responseObject = middlewareServicesObject.v1getBusinessDocDetails(queryParams, headers);

        List<Object> Documents = new ArrayList<>();
        int sizeOfList;
        int sizeForLoop;

        Documents = responseObject.jsonPath().getList("docDetailsSet");
        sizeOfList = Documents.size();
        sizeForLoop = sizeOfList - 1;
        log.info("The is size of list : " + sizeOfList);
        log.info("The is size of list for loop : " + sizeForLoop);
        log.info("The is list of Documents: " + Documents);

        ObjectMapper mapper = new ObjectMapper();
        String FetchedDocuments = "";
        FetchedDocuments = mapper.writeValueAsString(Documents);
        log.info("Deserialized Response is  : " + FetchedDocuments);

        if (FetchedDocuments != null) {
            log.info(" Inside IF condition for Document Upload ");
            for (int i = 0; i <= sizeForLoop; ++i) {
                if (i <= sizeForLoop) {
                    log.info(" Inside Loop for Document Upload ");
                    log.info("The value of I in loop : " + i);
                    Map docPovided = (Map) Documents.get(i);//Fetching current element of the docDetails set List
                    log.info("This is Serialized current docType : " + docPovided);

                    List possibleDocs = new ArrayList<>();//Fetching Posible docments array list
                    possibleDocs = (List) docPovided.get("possibleDocuments");
                    log.info("Possible Documents Lists are : " + possibleDocs);

                    Map getPossibleDocument = (Map) possibleDocs.get(0);//Fetching possibleDocuments Map
                    log.info("Possible Documents MAP for docsProvided " + getPossibleDocument);

                    String docsProvided = (String) getPossibleDocument.get("docProvided");//setting docProvided
                    log.info("This is possible document : " + docsProvided);

                    Map multiPageDocs = (Map) getPossibleDocument.get("multiPageDocDetails");//fetching multipage doc details
                    log.info("This is Multi page Docs details : " + multiPageDocs);

                    if (multiPageDocs != null) {
                        log.info(" Inside If section when Multipage Docs are not NULL ");
                        int pageNo;
                        pageNo = (int) multiPageDocs.get("minPages");
                        log.info("MultiDoc Page No. is : " + pageNo);

                        for (int j = 0; j <= pageNo - 1; ++j) {
                            log.info("Inside Loop of multipage doc upload & current value of loop is : " + j);
                            if (j <= pageNo - 1) {
                                Map<String, String> queryMultiPage = queryParamsDoc;
                                log.info("This is Multi Page map : " + queryMultiPage);

                                queryMultiPage.put("docProvided", docsProvided);
                                queryMultiPage.put("pageNo", String.valueOf(j + 1));
                                log.info("Multi Page map after putting docProvided : " + queryMultiPage);


                                Response UploadDocResp = middlewareServicesObject.UploadDocument(queryMultiPage, headersDoc, UploadDoc);

                            } else {
                                log.info("Else section of multipage doc upload");
                                break;
                            }
                        }
                    } else if (multiPageDocs == null) {
                        log.info(" Inside Else If section when Multipage Docs is NULL ");

                        Map<String, String> querySinglePage = queryParamsDoc;
                        log.info("This is Single Page map : " + querySinglePage);

                        querySinglePage.put("docProvided", docsProvided);
                        querySinglePage.put("pageNo", "1");
                        log.info("Single Page map after putting docProvided : " + querySinglePage);

                        Response UploadDocResp = middlewareServicesObject.UploadDocument(querySinglePage, headersDoc, UploadDoc);
                    }

                } else {
                    log.info(" Else Condition for Upload Documents ");
                    break;
                }

            }
        }
    }

    //This method calls /v4/merchant/doc/status to fetch dynamic docs <GG App>
    // ANd /v3/merchant/document to upload documents <GG App>
    public void GgAppDynamicDocUpload(String AgentToken, String version, String docId, Map<String, String> queryFetchDoc, Map<String, String> queryDocUpload) throws JsonProcessingException {
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchDynamicDocs v4Docs = new FetchDynamicDocs();
        Response v4FetchDocResp = middlewareServicesObject.v4FetchDynamicDocs(v4Docs, AgentToken, version, queryFetchDoc);
        int statusCode = v4FetchDocResp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        List<Object> Documents = new ArrayList<>();
        int sizeOfList;
        int sizeForLoop;

        Documents = v4FetchDocResp.jsonPath().getList("docDetailsSet");
        sizeOfList = Documents.size();
        sizeForLoop = sizeOfList - 1;
        log.info("The is size of list : " + sizeOfList);
        log.info("The is size of list for loop : " + sizeForLoop);
        log.info("The is list of Documents: " + Documents);

        ObjectMapper mapper = new ObjectMapper();
        String FetchedDocuments = "";
        FetchedDocuments = mapper.writeValueAsString(Documents);
        log.info("Deserialized Response is  : " + FetchedDocuments);

        if (FetchedDocuments != null) {
            log.info(" Inside IF condition for Document Upload ");
            for (int i = 0; i <= sizeForLoop; ++i) {
                if (i <= sizeForLoop) {
                    log.info(" Inside Loop for Document Upload ");
                    log.info("The value of I in loop : " + i);
                    Map docPovided = (Map) Documents.get(i);//Fetching current element of the docDetails set List
                    log.info("This is Serialized current docType : " + docPovided);

                    List possibleDocs = new ArrayList<>();//Fetching Posible docments array list
                    possibleDocs = (List) docPovided.get("possibleDocuments");
                    log.info("Possible Documents Lists are : " + possibleDocs);

                    Map getPossibleDocument = (Map) possibleDocs.get(0);//Fetching possibleDocuments Map
                    log.info("Possible Documents MAP for docsProvided " + getPossibleDocument);

                    String docsProvided = (String) getPossibleDocument.get("docProvided");//setting docProvided
                    log.info("This is possible document : " + docsProvided);

                    Map multiPageDocs = (Map) getPossibleDocument.get("multiPageDocDetails");//fetching multipage doc details
                    log.info("This is Multi page Docs details : " + multiPageDocs);

                    if (multiPageDocs != null) {
                        log.info(" Inside If section when Multipage Docs are not NULL ");
                        int pageNo;
                        pageNo = (int) multiPageDocs.get("minPages");
                        log.info("MultiDoc Page No. is : " + pageNo);

                        for (int j = 0; j <= pageNo - 1; ++j) {
                            log.info("Inside Loop of multipage doc upload & current value of loop is : " + j);
                            if (j <= pageNo - 1) {
                                SubmitDocs v3DocSubmit = new SubmitDocs();

                                Map<String, String> queryMultiPage = new HashMap<>();
                                queryMultiPage.putAll(queryDocUpload);
                                log.info("This is Multi Page map : " + queryMultiPage);

                                queryMultiPage.put("docId", docId);
                                queryMultiPage.put("docType", docsProvided);
                                queryMultiPage.put("pageNo", String.valueOf(j + 1));
                                queryMultiPage.put("docCount", String.valueOf(pageNo));
                                log.info("This is Multi Page map : " + queryMultiPage);

                                Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, AgentToken, version, queryMultiPage);
                                int errorCode = submitDocs.jsonPath().getInt("errorCode");
                                Assert.assertEquals(errorCode, 204);
                            } else {
                                log.info("Else section of multipage doc upload");
                                break;
                            }
                        }
                    } else if (multiPageDocs == null) {
                        log.info(" Inside Else If section when Multipage Docs is NULL ");
                        SubmitDocs v3DocSubmit = new SubmitDocs();

                        Map<String, String> querySinglePage = new HashMap<>();
                        querySinglePage.putAll(queryDocUpload);
                        log.info("This is Single Page map : " + querySinglePage);

                        querySinglePage.put("docId", docId);
                        querySinglePage.put("docType", docsProvided);
                        querySinglePage.put("pageNo", "0");
                        querySinglePage.put("docCount", "0");
                        log.info("This is Single Page map : " + querySinglePage);

                        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, AgentToken, version, querySinglePage);
                        int errorCode = submitDocs.jsonPath().getInt("errorCode");
                        Assert.assertEquals(errorCode, 204);
                    }

                } else {
                    log.info(" Else Condition for Upload Documents ");
                    break;
                }

            }
        }
    }

    //Fetching documents from /v2/upgradeMid/doc/status
    //Uploading documents from /v2/upgradeMid/doc
    public void FetchUploadDiyDoc(Map<String, String> queryParams, Map<String, String> queryParamsUpload, Map<String, String> headers, File DocumentPath) throws JsonProcessingException {
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();
        FetchDocumentStatus v2FetchDoc = new FetchDocumentStatus();

        Response v2FetchDocResp = middlewareServicesObject.FetchDocumentStatusv2(v2FetchDoc, queryParams, headers);

        int statusCode = v2FetchDocResp.getStatusCode();
        Assert.assertEquals(statusCode, 200);

        List<Object> Documents = new ArrayList<>();
        int sizeOfList;
        int sizeForLoop;

        Documents = v2FetchDocResp.jsonPath().getList("docDetailsSet");
        sizeOfList = Documents.size();
        sizeForLoop = sizeOfList - 1;
        log.info("The is size of list : " + sizeOfList);
        log.info("The is size of list for loop : " + sizeForLoop);
        log.info("The is list of Documents: " + Documents);

        ObjectMapper mapper = new ObjectMapper();
        String FetchedDocuments = "";
        FetchedDocuments = mapper.writeValueAsString(Documents);
        log.info("Deserialized Response is  : " + FetchedDocuments);

        if (FetchedDocuments != null) {
            log.info(" Inside IF condition for Document Upload ");
            for (int i = 0; i <= sizeForLoop; ++i) {
                if (i <= sizeForLoop) {
                    log.info(" Inside Loop for Document Upload ");
                    log.info("The value of I in loop : " + i);
                    Map docPovided = (Map) Documents.get(i);//Fetching current element of the docDetails set List
                    log.info("This is Serialized current DocType MAP : " + docPovided);
                    String DocType = docPovided.get("docType").toString();
                    log.info(" This is Doc Type : " + DocType);

                    List possibleDocs = new ArrayList<>();//Fetching Posible docments array list
                    possibleDocs = (List) docPovided.get("possibleDocuments");
                    log.info("Possible Documents Lists are : " + possibleDocs);

                    Map getPossibleDocument = (Map) possibleDocs.get(0);//Fetching possibleDocuments Map
                    log.info("Possible Documents MAP for docsProvided " + getPossibleDocument);

                    String docsProvided = (String) getPossibleDocument.get("docProvided");//setting docProvided
                    log.info("This is possible document : " + docsProvided);

                    Map multiPageDocs = (Map) getPossibleDocument.get("multiPageDocDetails");//fetching multipage doc details
                    log.info("This is Multi page Docs details : " + multiPageDocs);

                    if (multiPageDocs != null) {
                        log.info(" Inside If section when Multipage Docs are not NULL ");
                        int pageNo;
                        pageNo = (int) multiPageDocs.get("minPages");
                        log.info("MultiDoc Page No. is : " + pageNo);

                        for (int j = 0; j <= pageNo - 1; ++j) {
                            log.info("Inside Loop of multipage doc upload & current value of loop is : " + j);
                            if (j <= pageNo - 1) {
                                uploadCancelledCheque cancelledCheque = new uploadCancelledCheque();

                                Map<String, String> queryMultiPage = queryParamsUpload;

                                queryMultiPage.put("docProvided", docsProvided);
                                queryMultiPage.put("docType", DocType);
                                queryMultiPage.put("pageNo", String.valueOf(j + 1));
                                queryMultiPage.put("docCount", String.valueOf(pageNo));

                                Response responseObject = middlewareServicesObject.uploadCancelCheque(cancelledCheque, queryMultiPage, headers, DocumentPath);
                                int StatusCode = responseObject.getStatusCode();
                                Assert.assertEquals(StatusCode, 200);
                                log.info("Status Code is " + StatusCode);
                            } else {
                                log.info("Else section of multipage doc upload");
                                break;
                            }
                        }
                    } else if (multiPageDocs == null) {
                        log.info(" Inside Else If section when Multipage Docs is NULL ");
                        uploadCancelledCheque cancelledCheque = new uploadCancelledCheque();

                        Map<String, String> querySinglePage = queryParamsUpload;

                        querySinglePage.put("docProvided", docsProvided);
                        querySinglePage.put("docType", DocType);
                        querySinglePage.put("pageNo", "0");
                        querySinglePage.put("docCount", "0");


                        Response responseObject = middlewareServicesObject.uploadCancelCheque(cancelledCheque, querySinglePage, headers, DocumentPath);
                        int StatusCode = responseObject.getStatusCode();
                        Assert.assertEquals(StatusCode, 200);
                        log.info("Status Code is " + StatusCode);
                    }

                } else {
                    log.info(" Else Condition for Upload Documents ");
                    break;
                }

            }
        }
    }

    //This Method fetch and set question/answers present in Veto and RA from Panel
    public Map<String, String> FetchSetAnswers(@NotNull Map<String, String> PanelBody, String ObjPath) throws JsonProcessingException {
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(PanelBody.get("leadId"));

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, getXMWCookie());

        String WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();
        String leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();

        List<Object> questionList = v1FetchLeadResp.jsonPath().getList(ObjPath);

        List<Object> QuestionAlias = new ArrayList<>();
        List<Object> AnswerAlias = new ArrayList<>();
        List OptionAlias = new ArrayList<>();
        List<Object> FinalList = new ArrayList<>();
        String QnASerialized = "";

        for (int i = 0; i <= questionList.size() - 1; i++) {
            log.info("Current Iteration of QnA Loop : " + i);
            Map CurrentQuestion = (Map) questionList.get(i);
            QuestionAlias.add(i, CurrentQuestion.get("questionAlias"));//Inserting Question in List
            log.info("Current Question in List : " + QuestionAlias);

            OptionAlias = (List) CurrentQuestion.get("options");

            int sizeOfOptionList = OptionAlias.size();
            int lastOption = sizeOfOptionList - 1;

            String Score = "";
            Score = PanelBody.get("score");
            log.info("Printing Score from Map : " + Score);

            if (Score != null && Score.contains("low")) {
                log.info("Inside Low Score condition");
                Map InsideOption = (Map) OptionAlias.get(lastOption);
                AnswerAlias.add(i, InsideOption.get("optionAlias"));//Inserting Answer in List
                log.info("Current Answer in List : " + AnswerAlias);
            } else {
                log.info("Inside High Score condition");
                Map InsideOption = (Map) OptionAlias.get(0);
                AnswerAlias.add(i, InsideOption.get("optionAlias"));//Inserting Answer in List
                log.info("Current Answer in List : " + AnswerAlias);
            }


            List<Object> L1 = new ArrayList<>();
            L1.add(0, AnswerAlias.get(i));//Setting answer in Array List
            log.info("List Created out of curent answer L1 : " + L1);

            Map<Object, Object> IncrementalResult = new HashMap<>();
            IncrementalResult.put("questionAlias", QuestionAlias.get(i));//Setting currrent question in map
            IncrementalResult.put("answerAliasList", L1);//Setting current answer in map
            log.info("Map Created to be inserted in Final : " + IncrementalResult);

            FinalList.add(i, IncrementalResult);//setting current Map in Final List
            log.info("Final List Created : " + FinalList);

        }
        ObjectMapper mapper = new ObjectMapper();
        log.info("Desiaralized QnA Object : " + FinalList);
        QnASerialized = mapper.writeValueAsString(FinalList);
        log.info("Serialized QnA Object : " + QnASerialized);

        Map<String, String> ReturnAnswer = new HashMap<>();
        ReturnAnswer.put("AnswerArray", QnASerialized);
        ReturnAnswer.put("WorkFlowId", WorkFlowId);
        ReturnAnswer.put("LeadStage", leadStage);

        return ReturnAnswer;

    }

    //Registering Email only users in yop mail, accepting oauth registration request
    public void RegisterEmail(String emailId, String Driver) throws IOException, InterruptedException {
        String ExeptionMessage = "";
        Process InitializeHub = null;
        Process InitializeNode = null;
        Process getChrome = null;
        Process InstallChrome = null;
        Process getWget = null;
        String OsName = System.getProperty("os.name");

        //For Browser Stack Begin
        final String USERNAME = "aashitsharma2";
        final String AUTOMATE_KEY = "********************";
        final String BsURL = "https://" + USERNAME + ":" + AUTOMATE_KEY + "@hub-cloud.browserstack.com/wd/hub";
        final String LmdaURL = "https://aashit.sharma:<EMAIL>/wd/hub";
        DesiredCapabilities capsBS = new DesiredCapabilities();

        capsBS.setCapability("browser", "chrome");
        capsBS.setCapability("browser_version", "70.0");
        capsBS.setCapability("network", true);
        capsBS.setCapability("console", true);
        capsBS.setCapability("name", "aashitsharma's Email Registeration");
        //For Browser Stack End

        //for Local Standalone Server End
        ChromeOptions caps = new ChromeOptions();
        //caps.setBrowserName("chrome");

        String nodeURL1 = "http://localhost:4444/wd/hub";
        //for Local Standalone Server End


        if (OsName.contains("Linux")) {
            log.info("OS Name is : " + OsName);
            System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir") + "/src/main/resources/chromedriverLinux");
            System.setProperty("webdriver.gecko.driver", System.getProperty("user.dir") + "/src/main/resources/geckodriverLinux");

        } else if (OsName.contains("Mac")) {
            log.info("OS Name is : " + OsName);
            System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir") + "/src/main/resources/chromedriverMac");
            System.setProperty("webdriver.gecko.driver", System.getProperty("user.dir") + "/src/main/resources/geckodriverMac");

        }
        RemoteWebDriver driver;
        log.info("Selected Driver Param is : " + Driver);

        switch (Driver.toUpperCase()) {
            case "LOCALSTAND":
                log.info("Inside Local Standalone Case");
                //For Local StandAlone
                Runtime rt = Runtime.getRuntime();
                log.info("Executing Initializing HUB");
                InitializeHub = rt.exec("java -jar selenium-server-standalone-3.141.59.jar -role hub");
                InitializeHub.isAlive();
                log.info("Printing HUB : " + InitializeHub + "\n");
                if (InitializeHub.isAlive()) {
                    log.info("Hub is alive : " + InitializeHub.isAlive() + "\n");
                    log.info("Executing Initializing Node");
                    InitializeNode = rt.exec("java -Dwebdriver.chrome.driver=src/main/resources/chromedriverLinux  -jar selenium-server-standalone-3.141.59.jar -role webdriver -hub http://localhost:4444/grid/register -port 5566");
                    InitializeNode.isAlive();
                    log.info("Printing Node : " + InitializeNode);
                    log.info("Node is alive : " + InitializeNode.isAlive() + "\n");
                    waitForLoad(8000);
                    log.info("Setting Capabilities");
                    caps.setHeadless(true);
                    log.info("Browser name is : " + caps.getBrowserName());
                    log.info("Browser Version is : " + caps.getVersion());
                    caps.setCapability(CapabilityType.UNEXPECTED_ALERT_BEHAVIOUR,
                            UnexpectedAlertBehaviour.IGNORE);
                }
                log.info("Setting up Driver Parameters");
                driver = new RemoteWebDriver(new URL(nodeURL1), caps);
                log.info("Driver has been setup successfully for LOCAL Standalone");

                break;
            case "BS":
                log.info("Inside BrowserStack Case");
                //For BrowserStack
                capsBS.setCapability("os", "OS X");
                capsBS.setCapability("os_version", "Mojave");
                driver = new RemoteWebDriver(new URL(BsURL), capsBS);
                break;
            case "LAMDA":
                log.info("Inside Lamda Case");
                //For Lamda
                driver = new RemoteWebDriver(new URL(LmdaURL), capsBS);
                break;
            case "LOCAL":
                log.info("Inside Local Case");
                //Locally Execution
                driver = new ChromeDriver();
                break;
            default:
                log.info("Got into Default case");

                driver = new FirefoxDriver();
                break;
        }

        //Headless Browser
       /* ChromeOptions options = new ChromeOptions();
        options.addArguments("headless");
        ChromeDriver driver = new ChromeDriver(options);*/
        driver.navigate().to("http://www.yopmail.com/en/");

        try {
            waitForLoad(2000);
            log.info("Entering Email ID");
            driver.findElement(By.name("login")).sendKeys(emailId);
            waitForLoad(2000);

            waitForLoadForElement(driver, By.className("sbut"));
            log.info("Clicking on Proceed");
            driver.findElement(By.className("sbut")).click();
            waitForLoad(7000);

            log.info("Getting Inside I frame of Set Password");
            driver.switchTo().frame("ifmail");

            log.info("Searching for Set Password");
            waitForLoadForElement(driver, By.xpath("/html/body/div/div[3]/div[2]/div/table/tbody/tr[1]/td/table/tbody/tr[2]/td/p[5]/a"));
            log.info("Clicking on Set Password");
            driver.findElement(By.xpath("/html/body/div/div[3]/div[2]/div/table/tbody/tr[1]/td/table/tbody/tr[2]/td/p[5]/a")).click();

            for (String winHandle : driver.getWindowHandles()) {
                driver.switchTo().window(winHandle);
            }
            waitForLoad(9000);

            log.info("Printing URL" + driver.getCurrentUrl() + "\n");
            log.info("Searching for Password TextBox");
            waitForLoadForElement(driver, By.xpath("/html/body/div/div/div/form/div[1]/div/input"));
            driver.findElement(By.xpath("/html/body/div/div/div/form/div[1]/div/input")).sendKeys("paytm@123");
            driver.findElement(By.xpath("/html/body/div/div/div/form/div[2]/div/input")).sendKeys("paytm@123");
            log.info("Submitting Password to register user " + "\n");
            driver.findElement(By.xpath("/html/body/div/div/div/form/div[4]/div/button")).click();
            waitForLoad(12000);

            log.info("Switching to IFrame for Oauth Login");
            driver.switchTo().frame("myIframe");
            /*log.info("Searching for Paytm Image");
            waitForLoadForElement(driver,By.xpath("/html/body/div[1]/div/div/div[3]/logo-header/div/div/img"));

            log.info("Setting isValid Flag for assertion");
            boolean valid = driver.findElement(By.xpath("/html/body/div[1]/div/div/div[3]/logo-header/div/div/img")).isDisplayed();
            Assert.assertTrue(valid);
            log.info("Landed on Login Screen and Paytm Image Exist ");*/
            driver.quit();

        } catch (WebDriverException E) {
            log.info("Exception occured in Register Email" + E);
            ExeptionMessage = E.getMessage();
            log.info("Line no is : " + E.getStackTrace()[0].getLineNumber());

            driver.quit();
            Assert.fail();


        } finally {
            if (ExeptionMessage.contains("cannot find Chrome binary") && OsName.contains("Linux")) {
                log.info("Inside If conditon of Finally for Chrome");
                Runtime Chrt = Runtime.getRuntime();

                getWget = Chrt.exec("apt install wget");
                getWget.isAlive();
                log.info("Get Wget Process : " + getWget + "\n" + "Is alive : " + getWget.isAlive());
                getWget.waitFor();

                log.info("Downloading Chrome on machine");
                getChrome = Chrt.exec("wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb");
                getChrome.isAlive();
                log.info("Get Chrome Process : " + getChrome + "\n" + "Is alive : " + getChrome.isAlive());
                getChrome.waitFor();

                log.info("Installing Chrome on machine");
                InstallChrome = Chrt.exec("dpkg -i google-chrome-stable_current_amd64.deb");
                InstallChrome.isAlive();
                log.info("Install Chrome Process : " + InstallChrome + "\n" + "Is alive : " + InstallChrome.isAlive());
                InstallChrome.waitFor();

                log.info("Destroying Chrome Processes");
                DestroyProcess(getWget);
                DestroyProcess(getChrome);
                DestroyProcess(InstallChrome);
                log.info("All Chrome Processes are destroyed");

            }
            if (Driver.toUpperCase().contains("LOCALSTAND")) {
                log.info("Destroying LOCAL STAND Process");
                assert InitializeHub != null;
                DestroyProcess(InitializeHub);
                assert InitializeNode != null;
                DestroyProcess(InitializeNode);
            }
            driver.quit();
        }

    }


    public void DestroyProcess(Process ProcessName) {
        log.info("Destroying Process");
        ProcessName.destroy();
        waitForLoad(5000);
        log.info("Is Alive status of Process : " + ProcessName.isAlive());
        if (ProcessName.isAlive()) {
            log.info("Destroying Process Forcibly");
            ProcessName.destroyForcibly();
        } else {
            log.info("Process is destroyed, Forcibly Destruction is not needed");
        }

    }

    //Finding element by given locator
    public boolean isElememtPresent(RemoteWebDriver driver, By byLocator) {
        int trialCount = 1;
        while (trialCount <= 1) {
            try {
                if (driver.findElement(byLocator).isDisplayed()) {
                    log.info("Element : " + byLocator + "\n found at trial count : " + trialCount);
                    return true;
                }
            } catch (NoSuchElementException e) {
                log.info("Element : " + byLocator + "\n not found at trial count : " + trialCount);
                trialCount++;
            }
        }
        return false;
    }


    //Continuously Searching element by given locator
    public void waitForLoadForElement(RemoteWebDriver driver, By destinationLocator) {
        int tryCount = 0;
        while (tryCount < 5) {
            try {
                if (isElememtPresent(driver, destinationLocator)) {
                    return;
                } else {
                    waitForLoad(4000);
                    tryCount++;
                }
            } catch (Exception e) {
                waitForLoad(4000);
                tryCount++;
            }
        }
    }

    //Manual PG Callback API
    public void ManualPgCallBack(String CustId, String PgRequestId, String MID) {
        MiddlewareServices middlewareServices = new MiddlewareServices();
        Utilities Util = new Utilities();
        String ReMid = Util.randomDLDocValueGenerator();

        PgCallBack PgObj = new PgCallBack(P.TESTDATA.get("NewMerchant"));
        Map<String, String> query = new HashMap<>();

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Postman-Token", "a7fee9f1-55cf-4329-8529-bd647dcbf024,ee27893e-9c9c-4913-ac70-fa0d45d20c18");
        headers.put("cache-control", "no-cache,no-cache");
        headers.put("checksumhash", "ahjasfsnfefnewkfnwfnkf");

        Map<String, String> body = new HashMap<>();
        body.put("requestID", "OE" + PgRequestId);
        body.put("CustId", CustId);
        body.put("username", PgRequestId);

        if (MID.isEmpty()) {
            body.put("mid", ReMid);
        } else {
            body.put("mid", MID);
        }


        waitForLoad(2000);
        Response PgCallBackResp = middlewareServices.PgCallBack(PgObj, query, headers, body);

        String Response = PgCallBackResp.prettyPrint();

        /*String expectedMsg = "SUCCESS";
        String actualMsg = PgCallBackResp.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));*/
    }

    //This Method returns User MID by giving CustId
    public static String FetchMIDFromPG(String CustId, String MobileNo, String applicantToken) {
        PGServices pgServicesObject = new PGServices();
        String UserMID = "";
        Response v1FetchMIDResp = pgServicesObject.v1FetchMID(CustId, MobileNo, applicantToken);
        UserMID = v1FetchMIDResp.jsonPath().getJsonObject("mid").toString();
        String str = UserMID.replaceAll("\\[", "").replaceAll("\\]", "");
        log.info("Applicant's MID is : " + UserMID);
        int statusCode = v1FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        return str;

    }


    //Fetching PG Request ID from OE Panel and executing PG Callback API
    public void PgCallBackFromPanelRef(String leadId, String CustId, String MID) {
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();
        String PGRequestId = "";
        waitForLoad(12000);
        FetchLead v1FetchLeadObj = new FetchLead(leadId);
        XMWCookie = findXMWTokenforPanel("**********", "paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
        PGRequestId = v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.pgReferenceId");
        log.info("PG RequestId is : " + PGRequestId);
        for (int i = 0; i <= 4; i++) {
            log.info("Inside CallBack Loop & Itration is : " + (i + 1));
            if (PGRequestId == null) {
                log.info("Inside If Condition of Manual Callback ");
                waitForLoad(5000);
                FetchLead v1FetchLeadObjLoop = new FetchLead(leadId);
                Response v1FetchLeadRespLoop = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObjLoop, XMWCookie);
                PGRequestId = v1FetchLeadRespLoop.jsonPath().getString("leadDetails.additionalDetails.pgReferenceId");
                log.info("PG RequestId is : " + PGRequestId);

            } else {
                log.info("Got Value for PG Request Id : " + PGRequestId);
                break;
            }
        }

        if (MID.isEmpty()) {
            ManualPgCallBack(CustId, PGRequestId, "");
        } else {
            ManualPgCallBack(CustId, PGRequestId, MID);
        }

    }

    //Callback for Instant50K through CustId
    public String PG_CallBack_Insatnt50K(String CustId) throws SQLException, JsonProcessingException {
        String MID = "";
        try {
            TestBase testBase = new TestBase();
            Reporter.log("Test TestNg Logger " + " Cust ID is :" + CustId, true);

            waitForLoad(17000);
            //String query = "select sai.solution_value from user_business_mapping ubm join related_business_solution_mapping rbsm join solution_additional_info sai on ubm.related_business_solution_mapping_id = rbsm.id and rbsm.solution_id = sai.solution_id where ubm.mobile_number = '"+mobileNo+"' and sai.solution_key in ('PG_REQUEST_ID_50K')";
            String query = "SELECT DISTINCT callback_request from pg_callback_info where cust_id = " + CustId + " LIMIT 1";
            log.info("Extpected Quesry is : " + query);
            Reporter.log("Runing DB Query", true);
            String Result = testBase.getResult(query, "callback_request");
            Reporter.log("Output of DB Query : " + Result, true);
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> map = mapper.readValue(Result, new TypeReference<Map<String, String>>() {
            });
            log.info("Created Map is : " + map + "\n");

            String ReqId = "";
            ReqId = map.get("requestID");
            ReqId = ReqId.substring(2);
            log.info("Request ID is : " + ReqId);


            MID = map.get("mid");
            log.info("MID is : " + MID);
            waitForLoad(15000);
            ManualPgCallBack(CustId, ReqId, MID);

        } catch (Exception E) {
            log.info("Execption occured while Submitting callback ");
            log.info("This is Message : " + E.getMessage());
        }

        return MID;
    }

    //OMS API to generate QR via oms
    public static String createQRviaOMS(String leadId, String mobileNumber, String session_token) {
        String ORDER_ID = "";
        String paymentMid = "";
        String payAmount = "";
        CreateQR CreateQRObj = new CreateQR();
        CreateQROMS createQROMSObj = new CreateQROMS(P.TESTDATA.get("CreateQRviaOMS"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("leadId", leadId);
        body.put("mobile", mobileNumber);

        Response createQRResp = CreateQRObj.createQRviaOMS(createQROMSObj, session_token, body);

        ORDER_ID = createQRResp.jsonPath().getJsonObject("ORDER_ID").toString();
        paymentMid = createQRResp.jsonPath().getJsonObject("MID").toString();
        payAmount = createQRResp.jsonPath().getJsonObject("TXN_AMOUNT").toString();
        log.info("Order id from OMS is : " + ORDER_ID);
        log.info("Payment mid is  : " + paymentMid);
        return ORDER_ID;

    }

    public static String fetchOrderviaOMS(String orderId) {
        Long item_ID;
        FetchOMSOrder FetchOMSOrderObj = new FetchOMSOrder(orderId);
        FetchOrderOMS FetchOrderOMSObj = new FetchOrderOMS();
        Response fetchOrderOMSResp = FetchOrderOMSObj.fetchPaymentOrderviaOMS(FetchOMSOrderObj);
        item_ID = fetchOrderOMSResp.jsonPath().getJsonObject("[0].items[0].id");
        System.out.println("Item id is : " + item_ID);
        log.info("Item id from OMS is : " + item_ID);
        String item = String.valueOf(item_ID);
        System.out.println("Item id is" + item);
        return item;
    }


    public void GetApiTest(String URL, Map<String, String> CommonQuery, Map<String, String> CommonHeaders) {
        TestGetAPIs TestObj = new TestGetAPIs();

        TestGetAPIs.CommonApiHeaders.putAll(CommonHeaders);
        TestGetAPIs.CommonApiQueryParam.putAll(CommonQuery);
        TestGetAPIs.ApiUrl = P.API.get("api_url") + URL;

        TestObj.GetPositive();
        TestObj.GetWithNoParams();
        TestObj.GetWithNoHeader();
        TestObj.GetWithEmptyParam();
        TestObj.GetWithIndividualEntity();
        TestObj.GetWithPublicLimitedEntity();
        TestObj.GetWithTrustEntity();

    }

    public String IfNotNull(Response ResObj, String Path) {
        String ExpectedResponse = "";
        if (ResObj.jsonPath().getJsonObject(Path) != null) {
            log.info("Object is NOT Null");
            ExpectedResponse = ResObj.jsonPath().getJsonObject(Path).toString();
        } else {
            log.info(Path + " Object value is Null");
        }

        return ExpectedResponse;
    }

    public void AddMoneyToWallet(String SessionToken, String Amount) {
        WalletServices walletServices = new WalletServices();
        WalletAddMoney addMoney = new WalletAddMoney();

        long RandomNo = Utilities.generateRandom(12);
        addMoney.getProperties().setProperty("RandomNo", String.valueOf(RandomNo));
        addMoney.getProperties().setProperty("txnAmount", Amount);

        Response AddMoney = walletServices.WalletAddMoney(addMoney, SessionToken);
    }

    public String GenerateUpiQrCodeWallet() {
        WalletServices walletServices = new WalletServices();
        GenerateUpiQr generateUpi = new GenerateUpiQr();
        String UpiQrWallet = "";
        String QrCodeId = "";
        Response getUpiQr = walletServices.GenerateUpiQr(generateUpi);

        UpiQrWallet = getUpiQr.jsonPath().getJsonObject("response[0].upiHandle").toString();
        log.info("UPI QR code fetched from wallet : " + UpiQrWallet);

        QrCodeId = getUpiQr.jsonPath().getJsonObject("response[0].qrCodeId").toString();
        log.info("QR Code ID Extracted : " + QrCodeId);
        return QrCodeId;
    }

    //Addidng UPI Account for the User
    public void AddUpiAccount(String Mobile) {
        UpiInitiateSmsV3 IniSms = new UpiInitiateSmsV3();
        UpiStoreSms StrSms = new UpiStoreSms();
        UpiDeviceBinding DevBind = new UpiDeviceBinding();
        UpiAddVpa AddVpa = new UpiAddVpa();
        UpiListAccount ListAcc = new UpiListAccount();
        UpiAddAccount AddAcc = new UpiAddAccount();
        UpiServices UpiServices = new UpiServices();

        CreateApplicantOauth(Mobile);
        String SSOToken = ApplicantToken(Mobile, "paytm@123");
        String DeviceId = "53d42d719" + Mobile;
        String ReqId = "PTM8991858049409716351224" + Mobile;
        String VerificationData = "";
        String AccountNo = "";
        String IFSC = "";

        log.info("Device ID is : " + DeviceId + "\n" + "Request Id is : " + ReqId);

        Response IniSmsResp = UpiServices.InitiateSms(IniSms, DeviceId, ReqId, SSOToken);
        VerificationData = IniSmsResp.jsonPath().getJsonObject("respDetails.verificationData").toString();
        log.info("Verification Data is : " + VerificationData);

        Response StoreSmsResp = UpiServices.StoreSms(StrSms, Mobile, VerificationData);
        Assert.assertEquals("true", StoreSmsResp.jsonPath().getJsonObject("success").toString());

        Response DeviceBindResp = UpiServices.DeviceBind(DevBind, SSOToken, DeviceId, ReqId, Mobile, VerificationData);
        // Assert.assertEquals("true",DeviceBindResp.jsonPath().getJsonObject("success").toString());

        Response AddVpaResp = UpiServices.AddVpa(AddVpa, SSOToken, DeviceId, ReqId, Mobile);
        Assert.assertEquals("SUCCESS", AddVpaResp.jsonPath().getJsonObject("status").toString());

        Response ListAccResp = UpiServices.ListAccount(ListAcc, SSOToken, DeviceId, ReqId, Mobile);
        Assert.assertEquals("true", ListAccResp.jsonPath().getJsonObject("success").toString());
        AccountNo = ListAccResp.jsonPath().getJsonObject("MobileAppData.details.accounts[0].accRefNumber").toString();
        IFSC = ListAccResp.jsonPath().getJsonObject("MobileAppData.details.accounts[0].ifsc").toString();

        log.info("Account Number is : " + AccountNo + "\n" + "IFSC is : " + IFSC);

        Response AddAccResp = UpiServices.AddAccount(AddAcc, SSOToken, DeviceId, ReqId, Mobile, AccountNo, IFSC);
        Assert.assertEquals("true", AddAccResp.jsonPath().getJsonObject("success").toString());

    }

    /*
     * This method will jwt token with epoch time,with issuer clientId and custId
     */
    public String generateJwtTokenUsingEpochTimeATS(String issuer, String clientId, String custId) {
        String token = "";

        Date date = new Date();


        Algorithm buildAlgorithm = Algorithm.HMAC256("c9008488-d3aa-499a-9e09-301a90e5e9a3");
        token = JWT.create().withClaim("custId", custId)
                .withClaim("iss", issuer)
                .withClaim("clientId", clientId)
                .withIssuedAt(date).sign(buildAlgorithm);


        return token;
    }

    public Response FetchPanelLeadviaLeadID(String leadID) {
        MiddlewareServices middlewareServicesObject = new MiddlewareServices();
        FetchLead v1FetchLeadObj = new FetchLead(leadID);
        XMWCookie = findXMWTokenforPanel("**********", "paytm@123");
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
        return v1FetchLeadResp;
    }

    public Response uploadDocFromPanel(Map<String, String> headers, File fileUpload, String process) throws InterruptedException {

        SheetUpload sheetUploadObject = new SheetUpload();

        //Adding Headers
        for (Map.Entry m : headers.entrySet()) {
            sheetUploadObject.setHeader(m.getKey().toString(), m.getValue().toString());
        }

        sheetUploadObject.addMultipartFormData("file", fileUpload, "text/csv");
        sheetUploadObject.addMultipartFormData("process", process, "*/*");
        Thread.sleep(2000);

        sheetUploadObject.getRequest().urlEncodingEnabled(false);

        Response sheetUploadObjectResponse = sheetUploadObject.callAPI();

        return sheetUploadObjectResponse;

    }

    public String findXMWTokenforPanel(String username, String password) {
        TokenXMV tokenXMVRequestObject = new TokenXMV();
        oAuthServices oAuthServicesObject = new oAuthServices();
        String oAuthToken = oAuthServicesObject.getoAuthSessionTokenCustID(username, password, true);
        //Add Body Params
        tokenXMVRequestObject.getProperties().setProperty("oAuthToken", oAuthToken);
        tokenXMVRequestObject.getProperties().setProperty("ipAddress", "::ffff:127.0.0.1");

        Response responseObject = tokenXMVRequestObject.callAPI();
        String XMWToken = responseObject.getHeader("Set-Cookie").toString();
        System.out.println("XMW token is :" + XMWToken);

        return XMWToken;
    }

    public String findXMWExTokenforPanel(String username, String password) {
        String xmxTokenEx = "";
        TokenXMV tokenXMVRequestObject = new TokenXMV();
        oAuthServices oAuthServicesObject = new oAuthServices();
        String oAuthToken = oAuthServicesObject.getoAuthSessionTokenCustID(username, password, true);
        //Add Body Params
        tokenXMVRequestObject.getProperties().setProperty("oAuthToken", oAuthToken);
        tokenXMVRequestObject.getProperties().setProperty("ipAddress", "::ffff:127.0.0.1");

        Response responseObject = tokenXMVRequestObject.callAPI();
        Headers allHeaders = responseObject.getHeaders();
        List<String> list = allHeaders.getValues("Set-Cookie");

        for (String s : list) {
            if (s.contains("X-MW-TOKEN-EX=")) {
                xmxTokenEx = s.substring(14);
            }
        }

        String encodedxmwTokenEx = null;
        try {
            encodedxmwTokenEx = URLEncoder.encode(xmxTokenEx, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encodedxmwTokenEx;
    }


    public void GetUpiAccountDetails(String custID) throws Exception {
        GetUPIAccountDetail GetDetail = new GetUPIAccountDetail();
        Response Accdetail = UpiServices.GetUPIAccountInfo(GetDetail, generateJwtTokenforUPISecure(custID), custID);
        UPIAccountID = Accdetail.jsonPath().getJsonObject("userAccountList[0].accountId").toString();
        accountNo = Accdetail.jsonPath().getJsonObject("userAccountList[0].accountNo").toString();
        ifscCode = Accdetail.jsonPath().getJsonObject("userAccountList[0].ifsc").toString();
        accountHolderName = Accdetail.jsonPath().getJsonObject("userAccountList[0].accountHolderName").toString();
        bankName = Accdetail.jsonPath().getJsonObject("userAccountList[0].accountProviderName").toString();
        log.info("UPI Account Data is : " + UPIAccountID);

    }

    public static String generateJwtTokenforUPISecure(String custId) throws Exception {
        Key key = Keys.hmacShaKeyFor("57f5c79570c546ad9cc1e8f003c21ff7".getBytes("UTF-8"));
        String token = "";
        token = Jwts.builder().claim("iss", "OE").claim("custId", String.valueOf(custId)).claim("iat", Math.floor(System.currentTimeMillis() / 1000)).signWith(key,
                SignatureAlgorithm.HS256).compact();
        return token;
    }


    public static String generateJwtToken(String secret, String clientId, Map<String, String> jwtParams, boolean useEpoch) throws Exception {
        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder = JWT.create().withIssuer("OE").withClaim("clientId", clientId);

        builder = builder.withClaim("timestamp", (new DateTime()).toString());

        for (Map.Entry<String, String> jwtParam : jwtParams.entrySet()) {
            builder = builder.withClaim(jwtParam.getKey(), jwtParam.getValue());
        }
        String token = builder.sign(buildAlgorithm);
        System.out.println("Van jwt  " + token);

        return token;
    }

    public static String generateJwtTokenEGS(String secret, String clientId, Map<String, String> jwtParams, boolean useEpoch) throws Exception {
        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder = JWT.create().withIssuer("OE").withClaim("clientId", clientId);

        builder = builder.withIssuedAt(Date.from(Instant.now()));

        for (Map.Entry<String, String> jwtParam : jwtParams.entrySet()) {
            builder = builder.withClaim(jwtParam.getKey(), jwtParam.getValue());
        }
        String token = builder.sign(buildAlgorithm);
        System.out.println("JWT  " + token);

        return token;
    }

    public static String generateTokenKYB() throws NoSuchAlgorithmException, InvalidKeyException {
        long ts = System.currentTimeMillis();
        String cust_id = "1520342039000";
        String client_id = "GG-OE-staging";
        String header = "{\"alg\":\"HS256\",\"typ\":\"JWT\"}";
        String payload = "{\"ts\":\"" + ts + "\",\"cust_id\":\"" + cust_id + "\",\"client_id\":\"" + client_id + "\"}";
        String secret = "89CD7A268CDDB1D8995DE9EE1B5A85E394B498527BEBBF154A59DFE8DC21341D";
        String encodedHeader = Base64.getUrlEncoder().withoutPadding().encodeToString(header.getBytes(StandardCharsets.UTF_8));
        String encodedPayload = Base64.getUrlEncoder().withoutPadding().encodeToString(payload.getBytes(StandardCharsets.UTF_8));
        String unsignedToken = encodedHeader + "." + encodedPayload;

        Mac hmac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        hmac.init(secretKey);
        byte[] signature = hmac.doFinal(unsignedToken.getBytes(StandardCharsets.UTF_8));
        String encodedSignature = Base64.getUrlEncoder().withoutPadding().encodeToString(signature);

        return unsignedToken + "." + encodedSignature;
    }


    public static String generateJwtTokenEOS(String secret, String clientId, Map<String, String> jwtParams) throws Exception {
        Algorithm buildAlgorithm = Algorithm.HMAC256(secret);
        JWTCreator.Builder builder = JWT.create().withClaim("clientId", clientId);

        for (Map.Entry<String, String> jwtParam : jwtParams.entrySet()) {
            builder = builder.withClaim(jwtParam.getKey(), jwtParam.getValue());
        }
        String token = builder.sign(buildAlgorithm);
        System.out.println("JWT  " + token);

        return token;
    }


    // Generating Subscription JWT Token

    public static String createAuth0JwsHMAC(String clientId, String key) throws IllegalArgumentException {
        byte[] decodedKey = Base64.getDecoder().decode(key);

        Algorithm algorithm = Algorithm.HMAC512(decodedKey);

        String token = JWT.create()
                .withIssuedAt(new Date())
                .withClaim("client-id", clientId)
                .sign(algorithm);
        return token;
    }

    public static String BOSScreateJwtHMAC(String clientId, String key) {
        byte[] decodedKey = Base64.getMimeDecoder().decode(key);
        Map<String, Object> claims = new HashMap<String, Object>();
        claims.put("client-id", clientId);
        String token = Jwts.builder().setIssuedAt(new Date()).addClaims(claims)
                .signWith(SignatureAlgorithm.HS512, decodedKey).compact();
        return token;
    }

    public static String createStsTokenForDIY() {
    	 Map<String, Object> claims = new HashMap<>();
         claims.put("clientId", "STS");
         claims.put("iss", "OE");
         claims.put("custId", "123");
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
         sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
         String timestamp = sdf.format(new Date());
         claims.put("timestamp", timestamp);


         String SECRET_KEY="b3794dc7-648f-4f6d-8922-a29026859098";
         String x=Base64.getEncoder().encodeToString(SECRET_KEY.getBytes(StandardCharsets.UTF_8));


         String token=	Jwts.builder()
                 .setClaims(claims)
                 .signWith(SignatureAlgorithm.HS256, x)
                 .compact();
         System.out.println(String.format("STS Token is %s", token));
         return token;
    }

    @Test
    public static void test() {

        String bossToken = BOSScreateJwtHMAC("85c2e7e3-2f22-461c-854c-5f77d9f532be", "hhzuHzBlYMh7d/b1nyvZDHYy2vAp23FGqJsglpDpItrT9p7XN2BnPMRtkMu5lict0HnLPod9vxUPbVuem3yrgA==");
        System.out.println("Boss JWT Token " + bossToken);


    }


    // Generating ODS JWT Token


    public static String createAuth0JwsHMACTEST(String clientId, String key) throws IllegalArgumentException {
//    byte[] decodedKey = Base64.getDecoder().decode(key);

        Algorithm algorithm = Algorithm.HMAC256(key);

        String token = JWT.create()
                .withIssuedAt(new Date())
                .withClaim("clientId", clientId)
                .sign(algorithm);
        return token;
    }

    public Response getListOfAssetDetails(int a) throws Exception {
        String ISSUER = "ATS";
        String CLIENT_ID = "ats-bc";
        String custId = "1001224519";
        MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        String token = MiddlewareServicesObject.generateJwtTokenUsingEpochTimeATS(ISSUER, CLIENT_ID, custId);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("phoneNumber", "8018495590");
        queryParams.put("pageNumber", "1");
        queryParams.put("pageSize", "1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("jwt", token);
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");

        Map<String, String> body = new HashMap<String, String>();
        Response R1 = null;
        if (a == 1) {
            R1 = MiddlewareServicesObject.v1ListOfAssetDetailsForAvailable(queryParams, headers, body);
        }
        if (a == 2) {
            R1 = MiddlewareServicesObject.v1ListOfAssetDetailsForAssigned(queryParams, headers, body);
        }
        if (a == 3) {
            R1 = MiddlewareServicesObject.v1ListOfAssetDetailsForUnmapped(queryParams, headers, body);

        }
        if (a == 4) {
            R1 = MiddlewareServicesObject.v1ListOfAssetDetailsForReturned(queryParams, headers, body);
        }
        if (a == 5) {
            R1 = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Acknowledgement(queryParams, headers, body);
        }
        if (a == 6) {
            R1 = MiddlewareServicesObject.v1ListOfAssetDetailsForPending_Assign(queryParams, headers, body);
        }
        if (a == 7) {
            R1 = MiddlewareServicesObject.v1ListOfAssetDetailsForDeployed(queryParams, headers, body);
        }
        return R1;

    }

    public String FetchPaymentToken() {
        PaymentToken = AgentSessionToken("5555508546", "paytm@123");
        return PaymentToken;


    }

    //Fetching OTP from Seller panel

    public String getOTPFromSellerPanel(String recipients) throws InterruptedException, IOException {


     /*   CookieManager cookieManager = new CookieManager();

        CookieHandler.setDefault(cookieManager);
        URL url = new URL("https://seller-staging.paytm.com");
        URLConnection connection = url.openConnection();
        connection.getContent();
        CookieStore cookieStore = cookieManager.getCookieStore();
        List cookieList = cookieStore.getCookies();

        System.out.println("Cokies value is " +cookieList ); */

        GetOtpFromSellerPanel getOtp = new GetOtpFromSellerPanel();
        long epoch = System.currentTimeMillis() - 120 * 60 * 1000;
        String after_date = String.valueOf(epoch);
        System.out.println("after date value " + after_date);
        getOtp.addParameter("size", "10");
        getOtp.addParameter("offset", "0");
        getOtp.addParameter("recipients", recipients);
        getOtp.addParameter("after_date", after_date);
        long epoch1 = System.currentTimeMillis() + 120 * 60 * 1000;
        String before_date = String.valueOf(epoch1);
        getOtp.addParameter("before_date", before_date);
        //getOtp.addParameter("before_date", "1647876180000");
        getOtp.addParameter("client", "web");
        getOtp.setHeader("Cookie", SellerPanelCookie);
        getOtp.setHeader("Host", "fulfillment-staging.paytm.com");
        Thread.sleep(3000);
        Response OtpResponse = getOtp.callAPI();
        String OTPResponse = OtpResponse.jsonPath().getJsonObject("doc[0].body").toString();
        String OTP = OTPResponse.substring(15, 21);
        System.out.println("OTP is " + OTP);
        System.out.println("OTP length is " + OTP.length());
        return OTP;
    }

    public String PGTokenCreation() {
        PGToken = ApplicantToken("9891497839", "paytm@123");
        return PGToken;
    }

    public Response CreateTerminalInPG(String mid) {
        CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        PGToken = PGTokenCreation();
        headers.put("x-sso-token", PGToken);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("modelName", "A910");

        MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        Response respObj = MiddlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        return respObj;

    }

    public void ActiveTerminalInPG(String mid, String oldEDCSerialNumber, String oldTid, String oldModelName, String bankCode) {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        PGToken = PGTokenCreation();
        headers.put("x-sso-token", PGToken);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", oldEDCSerialNumber);
        body.put("tid", oldTid);
        body.put("modelName", oldModelName);
        body.put("bankCode", bankCode);

        MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        Response respObj = MiddlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);


    }

    public void CreateSubscription(String mid, String custId, String usn, String subscriptionType, String phoneNumber, String frequency, String onboardingDate) {
        CreateSubscription CreateSubscriptionObj = new CreateSubscription(P.TESTDATA.get("CreateSubscriptionBody"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("custId", custId);
        body.put("mid", mid);
        body.put("usn", usn);
        body.put("subscriptionType", subscriptionType);
        body.put("phoneNumber", phoneNumber);
        body.put("frequency", frequency);
        body.put("onboardingDate", java.time.LocalDate.now().toString());

        String jwt = createAuth0JwsHMAC(SEClientId, SEClientToken);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-client-token", jwt);
        headers.put("x-client-id", SEClientId);
        headers.put("Content-Type", "application/json");

        SubscriptionPlan SubscriptionPlanObj = new SubscriptionPlan();
        Response FetchPlanObjResponse = SubscriptionPlanObj.subcriptionCreatePlan(CreateSubscriptionObj, headers, body);


    }

    public static void establishConnectiontoServer(String AgentToken,int redisDB) throws Exception {
        try {
            JSch jsch = new JSch();
            // jsch.addIdentity("/Users/<USER>/Documents/id_rsa_jyoti");
            jsch.addIdentity(SSH_PRIVATE_KEY_PATH);
            Session session = jsch.getSession(SSH_USER_NEW, SSH_HOST_NEW, SSH_PORT);
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();
            System.out.println("SSH Connection established.");
            // Setup port forwarding to Redis server
            int forwardedPort = session.setPortForwardingL(0, redisHost, redisPort);

            // Connect to Redis using Jedis
            Jedis jedis = new Jedis("localhost", forwardedPort);
            jedis.select(redisDB);

            // Perform Redis operations
            System.out.println("Connected to Redis. Database: " + redisDB);
            System.out.println("Redis PING: " + jedis.ping());
            String key = "paytm.oauth." + AgentToken;
            String value = jedis.get(key);
            System.out.println("Value of '" + key + "': " + value);
            // Delete the key
            jedis.del(key);
            System.out.println("Key '" + key + "' deleted.");

            // Verify deletion
            String valuenew = jedis.get(key);
            System.out.println("Value of '" + key + "' after deletion: " + valuenew);

            // Cleanup
            jedis.close();
            session.disconnect();
            System.out.println("Disconnected from Redis and SSH server.");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    public Response OnboardBanksONPG(String PGToken, String mid, String bankName) {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", PGToken);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("bankName", bankName);
        body.put("bankTid", MapBankTid);

        PGServices PGServicesObj = new PGServices();
        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        return respObj;
    }

    public String GetResourceOwnerId(String Number, String Password) {

        String AgentToken = "";
        oAuthServices GetToken = new oAuthServices();
        Authorize Oauth = new Authorize();
        Token Token = new Token();
        Oauth.setResponse_type("code");
        Oauth.setDo_not_redirect("true");
        Oauth.setScope("paytm");
        Oauth.setClient_id("GG-OE-staging");
        Oauth.setUsername(Number);
        Oauth.setPassword(Password);
        Oauth.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
        Oauth.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
        Response getCode = GetToken.oauthorizeGG(Oauth);
        String code = getCode.jsonPath().getString("code");
        log.info("Code is " + code);
        String ResourceOwnerId = "";
        if (code != null) {
            log.info("Inside If Condition");
            Token.setAuthorization("Basic R0ctT0Utc3RhZ2luZzpkOG9iOTU0eDBzRWVBM0pZYTVoUXRqTldac2FUTm9SMg==");
            Token.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
            Token.setCode(code);
            Token.setGrant_type("authorization_code");
            Token.setClient_id("staging-golden-gate");
            Token.setScope("paytm");
            Response resToken = GetToken.tokenGG(Token);
            AgentToken = resToken.jsonPath().getString("access_token");
            ResourceOwnerId = resToken.jsonPath().getString("resourceOwnerId");

            log.info("This is Token " + AgentToken);
            log.info("This is Response Object : " + resToken.getBody().prettyPrint());

        } else {
            log.info("There must be some issue in Oauth ");
        }
        return ResourceOwnerId;
    }

    public Response FetchScreenDetails(String leadId, String token, String entityType, String solutionTypeLevel2, String fetchStrategy) throws JSONException {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", token);

        Map<String, String> params = new HashMap<>();
        params.put("leadId", leadId);
        params.put("fetchStrategy", fetchStrategy != null ? fetchStrategy : "SCREEN_DETAILS");
        params.put("entityType", entityType);
        params.put("solutionTypeLevel2", solutionTypeLevel2);

        String requestPath = "MerchantService/V1/sdMerchant/lead/fetchScreenDetailsUpgradeLimitRequest.json";
        Services.DIYMerchantUpgradeLimit.FetchScreenDetailsUpgradeLimit fetchScreenDetailsUpgradeLimit = new Services.DIYMerchantUpgradeLimit.FetchScreenDetailsUpgradeLimit();
        Response response = fetchScreenDetailsUpgradeLimit.FetchScreenDetailsUpgradeLimit(requestPath, headers, params);

        int httpcode = response.getStatusCode();
        Assert.assertEquals(httpcode, 200);
        return response;
    }

    public boolean
    checkNextScreenDetails(Response checkNextScreenResponse, String screenName) {
        JsonPath jsonPath = checkNextScreenResponse.jsonPath();
        return jsonPath.getList("nextScreenDetails.eligibleScreen")
                .stream()
                .anyMatch(screen -> screenName.equals(screen));
    }

}