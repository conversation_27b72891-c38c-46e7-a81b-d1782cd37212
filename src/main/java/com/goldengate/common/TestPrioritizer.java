package com.goldengate.common;

import java.util.*;

public class TestPrioritizer {
    private AIModel model;

    public TestPrioritizer(AIModel model) {
        this.model = model;
    }

    public List<String> prioritizeTests(Map<String, Object> testData) {
        Map<String, Double> predictions = model.predictFailures(testData);
        List<Map.Entry<String, Double>> sortedTests = new ArrayList<>(predictions.entrySet());

        sortedTests.sort((e1, e2) -> e2.getValue().compareTo(e1.getValue()));

        List<String> prioritizedTests = new ArrayList<>();
        for (Map.Entry<String, Double> entry : sortedTests) {
            prioritizedTests.add(entry.getKey());
        }
        return prioritizedTests;
    }
}
