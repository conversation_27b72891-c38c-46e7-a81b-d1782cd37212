package com.goldengate.common;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
public class ATSUtilityJWTGenerator {

    static String jwtClientSecret = "b444053c-4bf4-42be-8ccb-2e6ec3a39ce1";
    static String jwtClientId = "onboarding-engine";

    public static String generateJwtTokenMap(Map<String, String> jwtParams, String jwtClientId, String jwtClientSecret) {
        Algorithm buildAlgorithm = Algorithm.HMAC256(jwtClientSecret);
        JWTCreator.Builder builder = JWT.create().withIssuer("ATS").withClaim("clientId", jwtClientId);
        builder = builder.withClaim("iat", Date.from(Instant.now()));
        for (Map.Entry<String, String> jwtParam : jwtParams.entrySet()) {
            builder = builder.withClaim(jwtParam.getKey(), jwtParam.getValue());
        }

        return builder.sign(buildAlgorithm);
    }

    public static String generateJwtTokenMap_STS(Map<String, String> jwtParams, String jwtClientId, String jwtClientSecret) {

        Algorithm buildAlgorithm = Algorithm.HMAC256(jwtClientSecret);
        JWTCreator.Builder builder = JWT.create().withClaim("clientId", jwtClientId);
//        builder = builder.withClaim("iat", Instant.now().getEpochSecond());
        builder = builder.withClaim("iat", Date.from(Instant.now()));

        for (Map.Entry<String, String> jwtParam : jwtParams.entrySet()) {
            builder = builder.withClaim(jwtParam.getKey(), jwtParam.getValue());
        }

        return builder.sign(buildAlgorithm);
    }

    public static String generateJwtToken_ATS() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1001224519");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }
    public static String generateJwtToken_ATSUser() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1704050768");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }
    public static String generateJwtToken_ATSFSEwithoutpermission() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1704132818");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }

    public static String generateJwtToken_STS() {
        Map<String, String> jwtParams = new HashMap<>();
        return generateJwtTokenMap(jwtParams,"delhivery_wms","a0c53dd0-01b6-4d9c-b46e-528be240fab9");
    }

    public static String generateJwtToken_ATS_Channel_Ats() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1001224519");
        return generateJwtTokenMap(jwtParams, "ATS","d9b1f864-2d4b-406e-a8fd-8f801d06b71c");
    }

    public static String generateJwtToken_Fse2() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1704050777");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }
    public static String generateJwtToken_ATSWithoutPermission() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1704132815");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }
    public static String generateJwtToken_Fse2Withoutpermission() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1704132821");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }
    public static String generateJwtToken_CC() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1001700821");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }
    public static String generateJwtToken_Fse3() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1001647902");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");

    }


    public static String generateJwtToken_ATSUserDeviceAgeing() {
        Map<String, String> jwtParams = new HashMap<>();
        jwtParams.put("custId", "1704147125");
        return generateJwtTokenMap(jwtParams, "onboarding-engine","b444053c-4bf4-42be-8ccb-2e6ec3a39ce1");
    }

    public static void main(String[] args) {
        ATSUtilityJWTGenerator jwtg = new ATSUtilityJWTGenerator();
        String jwt_token = jwtg.generateJwtToken_ATS();
        String jwt_token1 = jwtg.generateJwtToken_STS();
        String jwt_token2 = jwtg.generateJwtToken_ATSUser();
        String jwt_token3 = jwtg.generateJwtToken_ATSUserDeviceAgeing();
        String jwt_token4 = jwtg.generateJwtToken_Fse2();

        System.out.println(jwt_token);
        System.out.println(jwt_token1);
        System.out.println(jwtg.generateJwtToken_ATS_Channel_Ats());
        System.out.println(jwt_token2);
        System.out.println(jwt_token3);
        System.out.println(jwt_token4);
    }


}