
lead=POST:${base_url}/v1/sdMerchant/lead

TokenXMV=POST:${base_url}/v1/token
ReAllocateAgent=POST:${base_url}/oe/panel/v1/reAllocateAgent

User=GET:${base_url}/v2/user

#============OE Panel API=============#

fetchLeads_OE=GET:${base_url}/oe/panel/v1/fetchLeads
FetchLead=GET:${base_url}/oe/panel/v1/lead/${leadId}
EditLead=POST:${base_url}/oe/panel/v1/editLead/${leadId}
ReallocateAgent=POST:${base_url}/oe/panel/v1/reAllocateAgent
RegisterLeadPanel=POST:${base_url}/v1/panel/leadManagement/registerLead
GetLeadDetails=GET:${base_url}/panel/v1/solution/lead
GetBusinessDoc=GET:${base_url}/panel/v1/business/doc/status
GetAllResources=GET:${base_url}/oe/panel/v1/referenceData/getAllResources
CompanyOnboardPanel=POST:${base_url}/panel/v1/business/lead
FetchPanPanel = GET:${base_url}/panel/v1/business/pan
FetchBusinessProfilePanel = GET:${base_url}/v1/diy/businessprofile/allroles
CreateSolutionPanel = POST:${base_url}/panel/v1/solution/lead
UpdateSolutionPanel = PUT:${base_url}/panel/v1/solution/lead
VerifyDocument = POST:${base_url}/panel/v2/solution/verify/${solution}
PanelPennyDrop = POST:${base_url}/panel/v1/business/pennyDropMultiNameMatch



#==========Document Upload API=============#
UploadDocumentFlow=POST:${base_url}/oe/panel/v1/document/multiPart

#==========Lending API=====================#

triggerotp=GET:${base_url}/lms/merchant/wrapper/triggerotp
validateotp=POST:${base_url}/lms/merchant/wrapper/validateotp
getKycStatus=POST:${base_url}/v2/lending/getKycStatus
callBackSaveOTP=POST:${base_url}/v5/callback/
bank=POST:${base_url}/v1/sdMerchant/bank
fetchBankUpdate=POST:${base_url}/v1/profile/update/lead/status
bank_fetchChild=GET:${base_url}/v1/profile/update/bank
editBank=POST:${base_url}/v1/profile/update
uploadCancelledCheque=POST:${base_url}/v2/upgradeMid/doc
getBreStatus=POST:${base_url}/v2/lending/getBreStatus
dynamicTNC=GET:${base_url}/loan/lead/dynamicTNC
submitApplication=POST:${base_url}/loan/lead/submitApplication
fileUpload_BusinessStatus=POST:${base_url}/oe/panel/v1/fileProcess/upload
fetchLead_LOS=GET:${base_url}/v1/consumer/lead
createLead_LOS=POST:${base_url}/v1/consumer/lead
updateLead_LOS=POST:${base_url}/v1/consumer/lead
otpCallback_LOS=POST:${base_url}/v5/callback/
getKycStatus_LOS=POST:${base_url}/v2/lending/getKycStatus
alternateNoCallBack_LOS=POST:${base_url}/v5/callback/
getBreStatus_LOS=POST:${base_url}/v2/lending/getBreStatus
dataUpdate_LOS=POST:${base_url}/v2/lending/dataUpdate
updateLeadAddress_LOS=POST:${base_url}/v1/consumer/lead
dynamicTnC_LOS=GET:${base_url}/loan/lead/dynamicTNC
saveTncAndSubmitApplication_LOS=POST:${base_url}/loan/lead/submitApplication
lmsCallBack_LOS=POST:${base_url}/v5/callback/
checkBreStatus_LOS=GET:${base_url}/v2/lending/lead/checkBreStatus
createLeadPL_LOS=POST:${base_url}/v1/consumer/lead
updateLeadPL_LOS=POST:${base_url}/v1/consumer/lead/additionalDetails
emailCallBackPL_LOS=POST:${base_url}/v5/callback/
updateLeadAddressPL_LOS=POST:${base_url}/v1/consumer/lead/additionalDetails
addAddressPL_LOS=POST:${base_url}/v1/consumer/lead/additionalDetails
UpdateGenderAndPincode=POST:${base_url}/v2/lending/dataUpdate
UpdatePANAndDOB=POST:${base_url}/v2/lending/dataUpdate


#=========PostPaid API's========================================#
CreateLeadLending=POST:${base_url}/v1/sdMerchant/lead
DeleteLeads=POST:${base_url}/deleteAllLeadsV2
FetchLeadDetails=GET:${base_url}/v1/consumer/lead
AddBasicDetails=POST:${base_url}/v1/consumer/lead
callbackLending=POST:${base_url}/v5/callback/
GetCKYCFromPPBL=POST:${base_url}/v2/lending/getKycStatus
UploadDocument=POST:${base_url}/v2/lending/lead/document
CKYCCallBack=POST:${base_url}/v2/lending/dataUpdate
GetBREStatus=POST:${base_url}/v2/lending/getBreStatus
CheckBREStatus=GET:${base_url}/v2/lending/lead/checkBreStatus
BRECallback=POST:${base_url}/v2/lending/dataUpdate
AddAddress=POST:${base_url}/v1/consumer/lead
FetchDynamicTnc=GET:${base_url}/loan/lead/dynamicTNC
SubmitApplication=POST:${base_url}/loan/lead/submitApplication
PPBLOTPCallback=POST:${base_url}/v5/callback/

#========Business Lending API's========================================#
UpdateGenderAndPincode=POST:${base_url}/v2/lending/dataUpdate
UpdatePANAndDOB=POST:${base_url}/v2/lending/dataUpdate
BREOTPCallback=POST:${base_url}/v5/callback/
BRECallbackMCA=POST:${base_url}/v2/lending/dataUpdate
UpdateLoanOffer=POST:${base_url}/v1/sdMerchant/lead
SaveBankDetails=POST:${base_url}/v1/sdMerchant/bank
UploadCancelledCheque=POST:${base_url}/v2/upgradeMid/doc
EmandateCallback=POST:${base_url}/v5/callback/
UpdateSAI=POST:${base_url}/v2/lending/dataUpdate
SheetUpload=POST:${base_url}/oe/panel/v1/fileProcess/upload

#========Top up===========================================#
AdditionalDetailsTopup=POST:${base_url}/v2/lending/dataUpdate

#==========Profile Update API=====================#
Status=POST:${base_url}/v1/profile/update/lead/status
Update=POST:${base_url}/v1/profile/update
StatusDoc=GET:${base_url}/v1/profile/update/doc/status
CombinedLeadStatus=GET:${base_url}/v1/profile/update/instrument/lead/status

#==========50K API=====================#
Business=POST:${base_url}/v1/sdMerchant/business
AdditionalDetails=POST:${base_url}/v1/sdMerchant/additionalDetails
ValidateBankDetails=POST:${base_url}/v1/sdMerchant/validateBankDetails
UpdateBankDetails=POST:${base_url}/v1/sdMerchant/updateBankDetails

#==========Lead Management API=====================#
RegisterLead=POST:${base_url}/v1/leadManagement/registerLead

#==================Company Onboard / Business API ====================#
GetBusinessv3=GET:${base_url}/v3/merchant/business
GetCompany=GET:${base_url}/v1/company/${PAN}
PostCompany=POST:${base_url}/v1/company
BusinessProfile=GET:${base_url}/v3/merchant/businessprofile
GstExemptionList=GET:${base_url}/v1/gst/gstExemptionList
PennyDropMultiNameMatch=POST:${base_url}/v4/merchant/pennydropmultinamematch

#==========100K API=====================#

SendOtp=POST:${base_url}/v3/sendOtp
ValidateOtp=POST:${base_url}/v3/validateOtp
GetMerchant=GET:${base_url}/v3/merchant/${custId}
GetDocStatus=GET:${base_url}/v3/merchant/documentstatus
SubmitDocs=POST:${base_url}/v3/merchant/document
PinCode=GET:${base_url}/v1/cart/pincodeDetail/${pinCode}
Category=POST:${base_url}/uad/v1/category
SubCategory=POST:${base_url}/uad/v1/subcategory
LanguagePreference=GET:${base_url}/v1/resources/values/languagePreference
Banks=GET:${base_url}/v2/banks/${ifsc}
PennyDrop=POST:${base_url}/v3/merchant/pennydrop
SubmitMerchant=POST:${base_url}/v3/merchant/${custId}
TnC=GET:${base_url}/v2/ekyc/biometric/termsAndConditions

#==========OE PPBL API URLS==============#
bank_api_url=https://goldengate-staging.paytmbank.com/MerchantService

SendIvrRequest=POST:${base_url}/v2/kyc/ivr/sendIvrRequest
LeadRequest=POST:${base_url}/v1/kyc/ivr/leadRequest
ValidateIvrRequest=POST:${base_url}/v3/kyc/ivr/validateIvrRequest
#==========OAuth API URLS==============#
oAuth_api_url=https://accounts-staging.paytm.in

Authorize=POST:${base_url}/oauth2/authorize
Token=POST:${base_url}/oauth2/token
CreateUser=POST:${base_url}/oauth-wormhole/createUser
UserDetails:POST:${base_url}/oauth-wormhole/userDetail

#==========UAD API URLS==============#
uad_url=https://uad-staging.paytm.com
AddCatSubcatSol=POST:${base_url}/uad/v1/add/category/subcategory

#==========OE OCL API URLS==============#
api_url=https://goldengate-staging12.paytm.com/MerchantService
lendingBase=https://fs-staging.paytm.com

#===========Wallet APIs===============#
wallet_url=https://wallet-staging.paytm.in
CreateUserWallet=POST:${base_url}/wallet-web/activateUserWallet
GetQrDetails = POST:${base_url}/wallet-web/getQRCodeInfo
GenerateUpiQr = POST:${base_url}/qrcode/v4/generateQrCode

#=============Loan Tap API=============#
Lead_fetch=GET:${base_url}/v1/sdMerchant/lead/
Lead_create=POST:${base_url}/v1/sdMerchant/lead
lmsCallBack_loanTap=POST:${base_url}/v5/callback/

#=================Pos Insurance=============#
fetchDocStatus_posInsurance=GET:${base_url}/v2/doc/upload/status
uploadDoc_posAgent=POST:${base_url}/v2/doc/upload/
fetchDynamicTnc=GET:${base_url}/v1/sdMerchant/fetchDynamicTnC
sendOtp_posAgent=GET:${base_url}/v1/sdMerchant/sendOtp
saveDynamicTnc_posAgent=POST:${base_url}/v1/sdMerchant/saveDynamicTnC
CallBackv5_posAgent=POST:${base_url}/v5/callback/


#=============Upgrade Merchant Plan=============#
MID = GET:${base_url}/v3/merchant/mid
UpgradePlans = GET:${base_url}/v1/merchant/upgradeplans
FetchDynamicDocs = GET:${base_url}/v4/merchant/doc/status

#=============MAP EDC=============#
Edc=GET:${base_url}/v1/edc
EdcPost=PUT:${base_url}/v1/edc
FetchPayment=GET:${base_url}/v1/edc/payment
FetchQnA=GET:${base_url}/v1/qna/fetchQuestions
ResendOtp=POST:${base_url}/v1/edc/resendOTP

#=============PGP APIs=============#
pgp_url=https://pgp-staging.paytm.in
AppPay=POST:${base_url}/theia/HANDLER_IVR/CLW_APP_PAY/APP

#=============MAP POS=============#
FetchPosPlans=GET:${base_url}/v1/pos/
BrandAssociation=GET:${base_url}/v1/resources/values/brandAssociation
StoreCategory=GET:${base_url}/v1/resources/values/storeCategory

#=============PSA DIY=============#
FetchDocumentStatus = GET:${base_url}/v2/upgradeMid/doc/status
ApplicationStatus = GET:${base_url}/v1/sdMerchant/lead/applicationStatus
StartTest = GET:${base_url}/v1/gamepind/startTest
FetchQuestion = GET:${base_url}/v1/gamepind/fetchQues
SubmitQuestion = POST:${base_url}/v1/gamepind/submitAns
OrderFullfillment = GET:${base_url}/v1/payment/orderfulfilment
NotifyCallback = POST:${base_url}/oe/v1/payment/order/notify
#============Manage Agent API=============#

userInfo=GET:${base_url}/oe/panel/v1/userInfo/${phoneNumber}
agent=POST:${base_url}/oe/panel/v1/agent


#=============SoundBox=============
soundbox=POST:${base_url}/v1/soundbox
SoundBoxFetchPlan=GET:${base_url}/v1/soundbox
SoundBoxChoosePlan=PUT:${base_url}/v1/soundbox
FetchSoundBoxPayment=GET:${base_url}/v1/soundbox/payment
SoundBoxBindUrl=GET:${base_url}/url/soundBoxBindUrl

#=============UNMAP EDC=============
CreateUnmapEdcLead=POST:${base_url}/v1/edc/createUnmapEdcLead
UnmapEDCReplaceReasons=GET:${base_url}/v1/resources/values/UnmapEDCReplaceReasons
UnmapEDCReturnReasons=GET:${base_url}/v1/resources/values/UnmapEDCReturnReasons
fetchAllTerminal=GET:${base_url}/v1/merchant/fetchAllTerminal
validateEDCQr=POST:${base_url}/v1/edc/validateEDCQr
unmapEDCMachine=PUT:${base_url}/v1/edc/unmapEDCMachine

#=============MAP QR=============
MidMobile =GET:${base_url}/v3/merchant/mid/mobile



#============Channel Onboarding API=============#

Mobile=GET:${base_url}/v3/merchant/mid/mobile
ChannelAdd=POST:${base_url}/chlbwp/subscription/v3/add

#============Channel Onboarding API=============#

RevisitOrganised=GET:${base_url}/v1/revisit/storeID
RevisitSubmit = POST:${base_url}/v1/revisit/submit

#============PG CallBack API=============#
PgCallBack = POST:${base_url}/v3/pg/postmid

#============Dynamic Tnc==================

v2FetchDynamicTnc =GET:${base_url}/v2/lead/fetchDynamicTnC
v2SaveDynamicTnc = POST:${base_url}/v2/lead/saveDynamicTnC

