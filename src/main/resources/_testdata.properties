productId=10467
requestJson=MerchantService/v1/profile/update/updateGiftVoucher/${request_url}

updateBankDetails=MerchantService/v1/profile/update/updateForBankDetails/updateRequestBankDetails.json
updateGiftVoucher=MerchantService/v1/profile/update/updateGiftVoucher/updateRequest.json
updateGST=MerchantService/V1/profile/update/updateGiftVoucher/addGstPgProfileUpdateRequest.json
mobileNumber=**********
solutionTypelevel2ForGV=GIFT_VOUCHER
solutionTypelevel2ForBankDetails=BANK_DETAIL_UPDATE

#=======================Validate Otp===================================*/

validateOtp500K=MerchantService/V3/ValidateOtp/ValidateOtpRequest500K.json
validateOtpMerchantCommonOnboard=MerchantService/V3/MerchantCommonOnboard/validateOtpRequest.json
validateOtpMapEdc=MerchantService/V3/ValidateOtp/ValidateOtpMapEdcRequest.json
ValidateOtpUnmapEDC=MerchantService/V3/ValidateOtp/ValidateOtpUnmapEDCRequest.json
ValidateOtpReturnWithClaimAMC=MerchantService/V3/ValidateOtp/validateOtpUnmapEDCReturnRequest.json
validateOtp=MerchantService/V3/ValidateOtp/ValidateOtpRequest.json
ValidateOtpPosAgent=MerchantService/V3/ValidateOtp/ValidateOtpPosAgentRequest.json
validateOtpMerchantDeclare500K= MerchantService/V3/ValidateOtp/ValidateOtpRequestMerchantDeclare500K.json
validateOtpUnlimited= MerchantService/V3/ValidateOtp/ValidateOtpRequestUnlimited.json
ValidateOtpRequestChannelOnboard=MerchantService/V3/ValidateOtp/ValidateOtpRequestChannelOnboard.json
validateOtpUpgradePlans=MerchantService/V3/ValidateOtp/ValidateOtpRequestUpgradePlans.json
validateMapPos=MerchantService/V3/ValidateOtp/ValidateOtpMapPosRequest.json
validateOtpSoundBox=MerchantService/V3/ValidateOtp/ValidateOtpSoundBoxRequest.json
validateOtpMapAllInOneQr = MerchantService/V3/ValidateOtp/ValidateOtpMapQrAllInOne.json
validateOtpCustomizedQr = MerchantService/V3/ValidateOtp/ValidateOtpCustomizedQr.json
validateOtpUnlimitedRevamp = MerchantService/V3/ValidateOtp/ValidateOtpRequestUnlimitedQrRevamp.json
validateOtpAssistedMerchantOnboard = MerchantService/V3/ValidateOtp/VaidateOtpAssistedMerchantOnboard
validateOtpAssistedMerchantOnboardCreate= MerchantService/V3/ValidateOtp/ValidateOtpAssistedMerchantOnboardCreate
ValidateOtpAssistedMerchantTnC = MerchantService/V3/ValidateOtp/ValidateOtpAssistedMerchantSaveTnC
ValidateOtpFastagAssisted = MerchantService/V3/ValidateOtp/ValidateOtpFastagAssisted.json
ValidateOtpUpgradeAmexPlan = MerchantService/V3/ValidateOtp/ValidateOtpUpgradePlanAmex.json
ValidateOtpUpgradeInternationalCard = MerchantService/V3/ValidateOtp/ValidateOtpRequestUpgradePlanInternationalCard.json
ValidateOtpUpgradeEmiOffering =MerchantService/V3/ValidateOtp/ValidateOtpUpgradePlanEmiOffering.json
ValidateOtpSmallMerchantCreate= MerchantService/V3/ValidateOtp/ValidateOtpSmallMerchantCreate.json
#=======================Submit Merchant Details===================================*/
SubmitMerchantCashATPosApproved=MerchantService/V3/SubmitMerchant/CashAtPOSQCApprovedRequest.json
SubmitMerchantCashATPosRejected=MerchantService/V3/SubmitMerchant/CashAtPOSQCRejectedRequest.json
SubmitMerchant500K=MerchantService/V3/SubmitMerchant/SubmitMerchantFor500KRequest.json
SubmitMerchantRejectedUnlimited=MerchantService/V3/SubmitMerchant/SubmitMerchantUnlimitedRejectedRequest.json
SubmitMerchant100K=MerchantService/V3/SubmitMerchant/SubmitMerchantRequest.json
SubmitMerchant100KNameMatchSuccess = MerchantService/V3/SubmitMerchant/Submit100KNameMatchSuccess.json
SubmitMerchant100KNameMatchSuccessReject=MerchantService/V3/SubmitMerchant/Submit100KNameMatchSuccessRejected.json
SubmitMerchant100KTransport=MerchantService/V3/SubmitMerchant/SubmitMerchant100KTransportRequest.json
Submit100KAfterRejection = MerchantService/V3/SubmitMerchant/Submit100KRejectedRequest.json
SubmitMapPos=MerchantService/V3/SubmitMerchant/SubmitMapPosRequest.json
SubmitAndroidPos=MerchantService/V3/SubmitMerchant/SubmitMapAndroidPosRequest.json
SubmitPSA=MerchantService/V3/SubmitMerchant/SubmitPSA.json
UpdatePSA=MerchantService/V3/SubmitMerchant/UpdatePSA.json
SubmitMapQrAllInOne = MerchantService/V3/SubmitMerchant/SubmitMerchantMapQrAllInOne.json
SubmitCustomizedQr = MerchantService/V3/SubmitMerchant/SubmitCustomizedQr.json
UpdateCustomizedQr = MerchantService/V3/SubmitMerchant/UpdateCustomizedQr.json
SubmitAssitedPartialSave = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedPartialSave.json
SubmitAssitedSaveWithPropPAN = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedSaveWithPropPAN.json
SubmitAssistedMerchantPosted = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedPost.json
SubmitAssistedMerchantPostedFixedProp = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedPostFixedProps.json
SubmitAssistedMovablePartial = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedMovablePartial.json
SubmitAssistedMovablePartialTransport = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedPartialSaveTransport.json
SubmitAssistedMovableProp = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedMovableProp.json
SubmitAssistedMovablePosted = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedMovablePost.json
SubmitAssistedMovablePostedTransport = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedMovablePostTransport.json
SubmitAssistedMovablePostedProp = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedMovablePostProps.json
SubmitAssitedPartialSaveONGProp = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedSaveONGProp.json
SubmitAssitedPostONGProp = MerchantService/V3/SubmitMerchant/SubmitMerchantAssistedPostONGProp.json
SubmitSmallMerchantPartial=MerchantService/V3/SubmitMerchant/SubmitSmallMerchantPartial.json
SubmitSmallMerchantPost=MerchantService/V3/SubmitMerchant/SubmitSmallMerchantLeadPost.json
SubmitSmallMerchantUpdateQr=MerchantService/V3/SubmitMerchant/SubmitSmallMerchantUpdateQr.json

/*=======================OE Panel Submit Lead===================================*/
EditLeadposAgent=MerchantServiceOEPanelV1EditLeadPGProfileLeadAction_OE/EditLeadposAgentRequest.json
EditLead_RejectDoc_posAgent=MerchantServiceOEPanelV1EditLeadPGProfileLeadAction_OE/EditLeadRejectDocRequest.json
Submit100KTransportAfterRejection=MerchantService/V3/SubmitMerchant/Submit100KTransportrejectedRequest.json
EditLeadPGProfile = MerchantServiceOEPanelV1EditLead/leadRequest.json
EditLeadLendingLead=MerchantServiceOEPanelV1EditLead/EditleadLendingLeadRequest.json
EditLead100K = MerchantServiceOEPanelV1EditLead/EditLead100kRequest.json
EditLeadUpgradeMidCancelChequeApprove=MerchantServiceOEPanelV1EditLead/EditLeadUpgradeMidCancelChequeApprove.json
EditLead100KRejected=MerchantServiceOEPanelV1EditLead/EditLead100KRejectedRequest.json
EditLead100KTransportRejected=MerchantServiceOEPanelV1EditLead/EditLead100KTransportRejected.json
EditLead100KTransport=MerchantServiceOEPanelV1EditLead/EditLead100KTransportRequest.json
EditLeadUpgradePlan=MerchantServiceOEPanelV1EditLead/EditLeadUpgradePlanRequest.json
EditLead500KNameMatchSuccess=MerchantServiceOEPanelV1EditLead/EditLead500KNameMatchSuccessRequest.json
EditLead500KNameMatchFailed=MerchantServiceOEPanelV1EditLead/EditLead500KNameMatchFailedRequest.json
EditLeadUnlimitedNameMatchFailedRejected=MerchantServiceOEPanelV1EditLead/EditLeadUnlimitedNameMatchFailRejectRequest.json
EditLeadUnlimtedMatchFailed=MerchantServiceOEPanelV1EditLead/EditLeadUnlimitedNameMatchFailSubmitLeadRequest.json
EditLeadPsaDiyPositive = MerchantServiceOEPanelV1EditLead/EditLeadPsaDiyPositive.json
EditLeadRejectFastagAssisted = MerchantServiceOEPanelV1EditLead/EditLeadFastagAssistedRequest.json

RegisterLead =MerchantService/V1/leadManagement/registerLead/RegisterLeadRequest.json
RegisterLeadEdit =MerchantService/V1/leadManagement/registerLead/RegisterLeadRequestEdit.json
CreateLeadBankUpdateLending=MerchantServicev1profileupdateEditbank/leadRequest.json
EditLeadCustomizedQr = MerchantServiceOEPanelV1EditLead/EditLeadCustomizedQr.json
EditLeadRevisitOrganised = MerchantServiceOEPanelV1EditLead/EditLeadOrganisedRevisit.json
EditLeadOrganisedPositive = MerchantServiceOEPanelV1EditLead/EditLeadOrganisedMerchantPositiveSubmit.json
EditLeadVetoSubmit = MerchantServiceOEPanelV1EditLead/EditLeadVetoRequest.json
EditLeadLobPositive = MerchantServiceOEPanelV1EditLead/EditLeadLobPositiveRequest.json
EditLeadBankigLob = MerchantServiceOEPanelV1EditLead/EditLeadPositiveBankingLob.json
EditLeadPositiveMandatoryParams = MerchantServiceOEPanelV1EditLead/EditLeadPositiveMandatoryParams.json
EditLeadPositiveRa = MerchantServiceOEPanelV1EditLead/EditLeadRaPositiveRequest.json
EditLeadBankigLobForZeroComms = MerchantServiceOEPanelV1EditLead/EditPositiveBankingLobForZeroComms.json
EditLeadCmtStaging = MerchantServiceOEPanelV1EditLead/EditLeadPositiveCMTStaging.json
EditLeadCmtProduction = MerchantServiceOEPanelV1EditLead/EditLeadPositiveCMTProduction.json

CreateOrgMerchant = MerchantService/panel/v1/business/lead/CreateOrgMerchantAppSoluionRequest.json
UpdateOrgMerchant = MerchantService/panel/v1/business/lead/UpdateOrgMerchantAppSolutionRequest.json
CreateOnlineMerchantOrg = MerchantService/panel/v1/business/lead/CreateOnlineOrgMerchant.json
UpdatingOnlineMerchantOrg = MerchantService/panel/v1/business/lead/UpdatingOnlineOrgMerchant.json
CreateOfflineWithCmt=MerchantService/panel/v1/business/lead/CreateOfflineOrgMerchantWithCmt.json
UpdateOfflineMerchantWithCmt = MerchantService/panel/v1/business/lead/UpdateOfflineOrgMerchantWithCmt.json
CreateOfflineMerchantWithWallet = MerchantService/panel/v1/business/lead/CreateOfflineMerchantWithWallet.json
UpdateOfflineMerchantWithWallet = MerchantService/panel/v1/business/lead/UpdateOfflineMerchantWithWallet.json
UpdateOnlineFor0Comm = MerchantService/panel/v1/business/lead/UpdatingOnlineOrgMerchantFor0Price.json

EditLeadAssistedMerchantPositiveSubmit = MerchantServiceOEPanelV1EditLead/EditLeadAssistedMerchantRequestPositiveSubmit.json
EditLeadRevisitUnOrganised =MerchantServiceOEPanelV1EditLead/EditLeadUnOrganisedRevisit.json
EditLeadPositiveSmallMerchant=MerchantServiceOEPanelV1EditLead/EditLeadPositiveSmallMerchant.json
#========================PGProfileUpdateAPIs==================================

FetchStatusGV=MerchantService/V1/profile/update/lead/Status/statusRequest.json
BrandEMILeadStatus=MerchantService/V1/profile/update/lead/Status/BrandEMILeadStatusRequest.json
FetchStatusBankUpdate=MerchantService/V1/profile/update/lead/Status/StatusRequestBankUpdate.json
CreateLeadBankUpdatePayments=MerchantServicev1profileupdateEditbank/PGProfileUpdateBankUpdate.json
SubmitbankDetailsUpdate =MerchantServiceOEPanelV1EditLead/EditLeadBankDetailsUpdate.json
CreateLeadEnableDisable = MerchantService/V1/profile/EnableDisable/EnableRequest.json
AddAddress =MerchantServicev1profileupdateEditbank/PGProfileUpdateAddAddress.json
FetchAddAddress =MerchantService/V1/profile/update/lead/Status/StatusRequestAddAddress.json
FetchGstin =MerchantService/V1/profile/update/lead/Status/StatusRequestAddGstin.json
AddGstin =MerchantServicev1profileupdateEditbank/PGProfileUpdateAddGstin.json
FetchInstrumentUpdate =MerchantService/V1/profile/update/lead/Status/StatusRequestInstrumentUpdate.json
SubmitInstrumentUpdate =MerchantServicev1profileupdateEditbank/PGProfileUpdateInstrumentUpdate.json
FetchStatusVendorOnboard=MerchantService/V1/profile/update/lead/Status/StatusRequestVendorOnboard.json
FetchStatusMerchantReactivation =MerchantService/V1/profile/update/lead/Status/StatusRequestMerchantReactivation.json
CreateLeadMerchantReactivation = MerchantService/V1/profile/update/lead/Status/CreateMerchantReactivation.json
#==========================MAP EDC==================================

UpdateEdcLead=MerchantService/V1/EDC/UpdateEdcLead/UpdateEdcLeadRequest.json
MapEdcMachine=MerchantService/V1/EDC/UpdateEdcLead/MapEdcMachineRequest.json
FinishMapping=MerchantService/V1/EDC/UpdateEdcLead/FinishMappingRequest.json
UpdateEdcLeadOms = MerchantService/V1/EDC/UpdateEdcLead/UpdateEdcLeadRequestOms.json
SelectPlanForMapEDC=MerchantService/V1/EDC/UpdateEdcLead/SelectPlanForMapEDCRequest.json
UpdateEdcLeadAndroidWithPOS=MerchantService/V1/EDC/UpdateEdcLeadAndroidWithPOS/UpdateEdcLeadRequest.json
UpdateEdcLeadOmsAndroidWithPOS = MerchantService/V1/EDC/UpdateEdcLeadAndroidWithPOS/UpdateEdcLeadRequestOms.json
MapEdcMachineForAndroidWithPOS=MerchantService/V1/EDC/UpdateEdcLeadAndroidWithPOS/MapEdcMachineRequest.json


#=========================PGP Pay==================================#
TrueOrderCreated=PGP/AppPay/AppPayforOMS.json
FalseOrderCreated=PGP/AppPay/AppPayMerchantRequest.json
NewPaymentBodyRequest=PGP/AppPay/NewPaymentBodyRequest.json

#=========================Lead Create==================================#

BusinessLending = MerchantService/V1/sdMerchant/lead/CreateLead/BusinessLendingLeadRequest.json
LoanTap = MerchantService/V1/sdMerchant/lead/CreateLead/LoanTapLeadRequest.json
FetchPan = MerchantService/V1/sdMerchant/lead/CreateLead/FetchPanLeadRequest.json
PanVerifiedLoanTap = MerchantService/V1/sdMerchant/lead/CreateLead/PanVerifiedLoanTapLeadRequest.json
PosInsurance = MerchantService/V1/sdMerchant/lead/CreateLead/PosInsuranceLeadRequest.json
Offline50K = MerchantService/V1/sdMerchant/lead/CreateLead/Offline50KLeadRequest.json
EmptyBody = MerchantService/V1/sdMerchant/lead/CreateLead/EmptyBodyRequest.json
PsaDiyUpdateLead = MerchantService/V1/sdMerchant/lead/CreateLead/PsaDiyUpdateLeadRequest.json
FastagDiyCreateLead = MerchantService/V1/sdMerchant/lead/FastagDiyCreateLead.json
FastagUpdateLead = MerchantService/V1/sdMerchant/lead/FastagDiyLeadUpdate.json
UnifiedPaymentMerchant =MerchantService/V1/sdMerchant/lead/CreateLead/UninfiedPaymentMerchant.json
#=========================Additional Details==================================#

LoanOffer = MerchantService/V1/sdMerchant/AdditionalDetails/LoanOfferRequest.json
Offline50kAdditional = MerchantService/V1/sdMerchant/AdditionalDetails/Offline50kAdditionalDetailsRequest.json
UpdateAddress = MerchantService/V1/sdMerchant/AdditionalDetails/UpdateAddressRequest.json
UpdatePersonalDetails = MerchantService/V1/sdMerchant/AdditionalDetails/UpdatePersonalDetailsRequest.json
UnifiedPaymentMerchantAdditional =MerchantService/V1/sdMerchant/AdditionalDetails/UnifiedPaymentMerchantRequest.json
#=========================Bank Update sdMerchant==================================#
LendingBankUpdate = MerchantServicev1sdMerchantbank/leadRequest.json
PsaDiyBankUpdate = MerchantServicev1sdMerchantbank/PsaDiyUpdateBank.json

#=========================Order Notify==================================#
PsaDiyOrderNotify = MerchantService/OE/V1/Payment/Order/Notify/PsaDiyNotifyOrderLeadRequest.json
EdcDiyOrderNotify = MerchantService/OE/V1/Payment/Order/Notify/EdcDIYNotifyOrderLeadRequest.json
EMIOfferingDIYOrderNotify=MerchantService/OE/V1/Payment/Order/Notify/CreateLeadEMIOfferingDIYRequest.json
MposDIYOrderNotify=MerchantService/OE/V1/Payment/Order/Notify/CreateLeadMposDIYRequest.json
CashAtPosOrderNotify=MerchantService/OE/V1/Payment/Order/Notify/PaymentCashAtPosDiyRequest.json

#=========================SoundBox==================================#
v1SoundBox=MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxCreateLeadRequest.json
v1SoundBoxChoosePlan=MerchantService/V1/SoundBox/SoundBoxCreateLead/SoundBoxChoosePlan/SoundBoxChoosePlanRequest.json


#============================UNMAP EDC==================================#
ReturnEDC=MerchantService/V1/EDC/unmapEDCMachine/unmapEDCMachineRequest.json
ReplaceEDC=MerchantService/V1/EDC/unmapEDCMachine/ReplaceEDCRequest.json
UnmapReturnWithClaimAMC=MerchantService/V1/EDC/unmapEDCMachine/unmapEDCMachineReturnWithClaimAMCRequest.json
UnmapReplaceWithClaimAMC=MerchantService/V1/EDC/unmapEDCMachine/unmapEDCMachineReplaceWithClaimAMCRequest.json
UnmapEDCCreateLeadRequest=MerchantService/V1/EDC/CreateUnmapEdcLead/CreateUnmapEdcLeadRequest.json
EDCUpgradeCreateLeadRequest=MerchantService/V1/EDC/CreateUnmapEdcLead/CreateLeadEDCUpgradeRequest.json

#===========================EDC DIY ===================================#
validateOrderEdcDIY=MerchantService/V2/edc.validateOrder/validateOrderEdcDIYRequest.json
validateOrderEMIOfferingDIY=MerchantService/V2/edc.validateOrder/validateOrderEMIOfferingDIYRequest.json
validateOrderMposDIY=MerchantService/V2/edc.validateOrder/validateOrderMposDIYRequest.json
CreateLeadCashAtPosDIY=MerchantService/V1/sdMerchant/lead/createLeadCashAtPosDIY/CashAtPosDIYleadRequest.json
validateOrderCashAtPosDIY=MerchantService/V2/edc.validateOrder/validateOrderCashAtPosDIYRequest.json
CreateLeadBrandEMIDIY=MerchantService/V1/sdMerchant/lead/createLeadCashAtPosDIY/CreateLeadBrandEMIDIYRequest.json

#============================Merchant Revisit==================================#
OrganisedSubmit = MerchantService/V1/Revisit/RevisitSubmit/OrganisedRevisitSubmitRequest.json
UnOrganisedSubmit =MerchantService/V1/Revisit/RevisitSubmit/UnOrganisedRevisitSubmitRequest.json
#============================PG Callback==================================#
NewMerchant = MerchantService/V3/PG/PgCallBack/NewMerchantPgCallBackRequest.json
EditMerchant =MerchantService/V3/PG/PgCallBack/EditMerchantPgCallBackRequest.json

#=========================Create QR ======================================#
CreateQRviaOMS=OMS/createQRRequest.json
CheckoutAuthorizeviaOMS=OMS/AuthorizeOMSRequest.json

#============================PG merchant create==================================#

CreateMerchantOnPG = PG.CreateMID/MIDRequest.json
MarkMIDInactive = PG.MarkMIDInactive/MidInactiveRequest.json
CreateTerminalOnPGRequestBody=PG.CreateMID/CreateTerminalONPGRequest.json
ActiveTerminalOnPGRequestBody=PG.CreateMID/ActiveTerminalONPGRequest.json
SetPreferenceOnPGRequestBody=PG.CreateMID/SetPreferenceEditMerchantRequest.json
ApplyTieredMDROnPG=PG.CreateMID/ApplyTieredMdrOnPGRequest.json
ReplaceTerminalOnPGRequestBody=PG.CreateMID/ReplaceTerminalONPGRequest.json
OnboardBanksOnPGRequestBody=PG.CreateMID/OnboardBanksOnPGRequest.json
ConfigureMbidOnPGRequestBody=PG.CreateMID/ConfigureMbidRequest.json
OnboardBanksRBDCOnPGRequestBody=PG.CreateMID/OnboardBanksRBDCRequest.json

#============================Vendor Onboarding ==================================#

AddVendor=MerchantService/V1/profile/update/addVendor/createRequestAddVendor.json
EditLeadFromPanel =MerchantServiceOEPanelV1EditLead/EditLeadAddVendor.json


#===============================CommonOnboardingEDC=============================================#
GetMerchantMID=CommonOnboardingEDC/GetMerchantMIDStatus/GetMerchantMIDRequest.json
CreateMCOLead=CommonOnboardingEDC/CreateMCOLead/CreateMCOLeadRequest.json
FetchMCOLead=CommonOnboardingEDC/FetchMCOLead/FetchMCOLeadRequest.json
SendOTPLead=CommonOnboardingEDC/sendOTPLead/sendOTPLeadRequest.json
GetLeadStatus=CommonOnboardingEDC/GetLeadStatus/GetLeadStatusRequest.json
GetBusiness=CommonOnboardingEDC/GetBusiness/GetBusinessRequest.json
ValidateOTPConfirm=CommonOnboardingEDC/CreateMCOLead/ValidateOTPConfirm.json
GetBank=CommonOnboardingEDC/GetBank/GetBankRequest.json
CKYC=CommonOnboardingEDC/getCKYC/GetCKYCRequest.json
FetchBeauru=CommonOnboardingEDC/fetchBeauru/fetchBeauruRequest.json
AddBanking=CommonOnboardingEDC/addBank/addBankRequest.json
AddBankingBeauru=CommonOnboardingEDC/addBank/addBankandBeauruRequest.json
AddBankingBeauruAadhar=CommonOnboardingEDC/addBank/addBankandBeauruaadharRequest.json
AddBankingBeauruAadharPAN=CommonOnboardingEDC/addBank/addBankandBeauruaadharpanRequest.json
AddBankingBeauruAadharPANbasicdetails=CommonOnboardingEDC/addBank/MerchantBasicdetailsRequest.json
FetchQNA=CommonOnboardingEDC/fetchqnaedc/fetchqnaRequest.json
Fetchdevicedetails=CommonOnboardingEDC/fetchdevicedetailsedc/fetchdevicedetailsedcRequest.json
PartialSaveCalling=CommonOnboardingEDC/partialSave/partialSaveRequest.json
createDeviceLeadReq=CommonOnboardingEDC/deviceOnboardingLead/deviceOnboardingLeadRequest.json
FetchdevicedetailsReq=CommonOnboardingEDC/fetchDeviceDetails/fetchDeviceDetailsRequest.json
FetchBankReq=CommonOnboardingEDC/fetchBank/fetchBankRequest.json
ConfirmDeviceReq=CommonOnboardingEDC/confirmPlanDevice/confirmPlanDevicePropertyRequest.json
SummaryDeviceReq=CommonOnboardingEDC/deviceSummaryEDC/deviceSummaryEDCRequest.json
DeviceAddOnsreq=CommonOnboardingEDC/deviceAddOns/deviceAddOnsRequest.json
CartSaveReq=CommonOnboardingEDC/cartSave/cartSaveEDCRequest.json
OrderSaveReq=CommonOnboardingEDC/orderSave/orderSaveEDCRequest.json
ShopInsuranceRequest=MerchantService/V1/EDC/ShopInsurance/ShopInsuranceRequest.json
EmbeddedshopinsuranceRequest=shop_insurance/embeddedeligibilityRequest.json
ConfirmpolicyRequest=shop_insurance/ConfirmPolicyRequest.json
#===============================ODS=============================================#
FetchPlanWithMID=ODS/FetchPlanODS/FetchPlanODSWithMIDRequest.json
FetchPlanWithoutMID=ODS/FetchPlanODS/FetchPlanWithoutMidRequest.json
OdsUserInfoPath=ODS/V1/OdsUserInfoRequest.json
OdsPayModePath=ODS/V1/OdsPayModeRequest.json
CreatePlanBasic=ODS/CreatePlanODS/CreatePlanODSRequest.json
CreatePlanBasicNew=ODS/CreatePlanODS/CreatePlanODSRequestWithReplicateNew.json
CreatePlanWithReplicate=ODS/CreatePlanODS/CreatePlanODSRequestWithReplicate.json
ViewPlanBasic=ODS/ViewPlanODS/ViewPlanODSRequest.json
OdsAclServicePath=ODS/V1/OdsAclServiceRequest.json
OdsSearchPlanPath=ODS/SearchPlanOds/OdsSearchPlanRequest.json
OdsDeviceDetailsAPI=ODS/FetchDeviceDetails/FetchDeviceDetailsAPIRequest.json
OdsFetchActiveQuestionAnswers = ODS/FetchActiveQuestionAnswers/FetchActiveQuestionAnswersRequest.json
OdsTotalNumberOfRecords = ODS/FetchActiveQuestionAnswers/FetchActiveQuestionAnswersRequest.json
OdsFetchActiveQuestionAnswersWithSectionList = ODS/FetchActiveQuestionAnswers/FetchActiveQuestionAnswersRequestWithSectionList.json
SoundboxRequestWithSRO = ODS/FetchPlanODS/FetchPlanSoundboxWithSRORequest.json
SoundboxRequestWithoutSRO = ODS/FetchPlanODS/FetchPlanSoundboxWithoutSRORequest.json
EDCPlanUpgradeRequest = ODS/FetchPlanODS/FetchPlanWithMIDPlanUpgradeRequest.json
FetchPlanWithMIDAndAgentSRO=ODS/FetchPlanODS/FetchPlanODSWithMIDAndAgentSRORequest.json
FetchPlanWithoutSRO = ODS/FetchPlanODS/FetchPlanODSWithoutMerchantSRO.json
TapAndPayWithSRO = ODS/FetchPlanODS/FetchPlanTapAndPayWithSRO.json
TapAndPayWithoutSRO = ODS/FetchPlanODS/FetchPlanTapAndPayWithoutSRO.json
TapAndPayWithAgentSRO = ODS/FetchPlanODS/FetchPlanTapAndPayWithAgentSRO.json
TapAndPayWithSROandAgent = ODS/FetchPlanODS/FetchPlanTapAndPayAgentAndSRO.json
FetchPlanPRCAddonWithMID=ODS/FetchPlanODS/FetchPlanODS_PRC_AddonWithMIDRequest.json
FetchPlanPRCAddonWithAgentSRO=ODS/FetchPlanODS/FetchPlanODS_PRC_AddonWithAgentSRORequest.json
AddOnMappingPlanSearchAPIODS= ODS/AddOnMappingPlanSearchAPIODS/AddOnMappingPlanSearchAPIODSRequest.json
AddonMappingRequestSearchAPI= ODS/AddonMappingRequestSearchAPI/AddonMappingRequestSearchAPIRequest.json
BulkUpdateSearchPageAPI= ODS/BulkUpdateSearchPageAPI/BulkUpdateSearchPageAPIRequest.json
FetchApplicableFiltersAPIRequest=ODS/FetchApplicableFiltersAPI/FetchApplicableFiltersAPIRequest.json
FetchFilterValuesAPIRequest=ODS/FetchFilterValuesAPI/FetchFilterValuesAPIRequest.json
FetchPlanDifferenceAPIRequest=ODS/PlanDifference/PlanDifferenceRequest.json

#============================Subscription Engine================================#
CreateSubscriptionBody=Subscription/PlanOnboard/CreateSubscriptionRequest.json
CreateLucSubscriptionRequest=Subscription/PlanOnboard/CreateLUCSubscriptionRequest.json
SubscriptionStatusRequest=Subscription/PlanOnboard/SubscriptionStatusRequest.json
CreateConditionalRenalSubsRequest=Subscription/PlanOnboard/CreateConditionalRentalSubscription.json
CreateConditionalRenalSubsRequestWithTID=Subscription/PlanOnboard/CreateConditionalRentalSubscriptionWithTID.json
CreateConditionalRentalSubscriptionWithDeductionInterval=Subscription/PlanOnboard/CreateConditionalRentalSubscriptionWithDeductionInterval.json
ReplaceSubscriptionReqBody=Subscription/PlanOnboard/ReplaceSubscriptionRequest.json
CreateUpfrontAdvanceSubscriptionRequest=Subscription/PlanOnboard/CreateUpfrontAdvanceSubscriptionRequest.json
ConditionalRentalWithUpfrontAdvanceRequest=Subscription/PlanOnboard/ConditionalRentalWithUpfrontAdvanceRequest.json
#===========================KYB================================================#
EditAddressInKYBReqBody=CIF/BrandActiveInKyb/EditAddressInKyb/EditAddressInKybRequest.json
TncAcceptInKybReqBody=CIF/BrandActiveInKyb/TncAcceptInKyb/TncAcceptInKybRequest.json
TncUserInKybReqBody=CIF/BrandActiveInKyb/TncAcceptInKyb/TncUserInKybRequest.json
BrandEMIEditRequest=KYB/EditBrandEMIRequest.json


#=============================Billing =====================================#
BillingOnboardRequest=Billing/BillingOnboardRequest.json
BillingReturnRequest=Billing/BillingReturnRequest.json

#=============================MakerChecker=====================================#

FetchWorkflowRequest=MakerChecker/WorkflowService/FetchWorkflow/FetchWorkflowRequest.json
GetWorkflowDetailRequest=MakerChecker/WorkflowService/GetWorkflowDetail/GetWorkflowDetailRequest.json
FetchRejectionReasonsRequest=MakerChecker/ReviewService/FetchRejectionReasons/FetchRejectionReasonsRequest.json
FetchAllCasesRequest=MakerChecker/ReviewService/FetchAllCases/FetchAllCasesRequest.json
ReviewPanelGetAllResourcesRequest=MakerChecker/ReviewService/ReviewPanelGetAllResources/ReviewPanelGetAllResourcesRequest.json
FetchDMSDocRequest=MakerChecker/ReviewService/FetchDMSDoc/FetchDMSDocRequest.json
CreateWorkflowRequest=MakerChecker/WorkflowService/CreateWorkflow/CreateWorkflowRequest.json
CreateReviewCaseRequest=MakerChecker/ReviewService/CreateReviewCase/CreateReviewCaseRequest.json
DownloadDMSDocRequest=MakerChecker/ReviewService/DownloadDMSDoc/DownloadDMSDocRequest.json
ApproveRejectCaseRequest=MakerChecker/ReviewService/ApproveRejectCase/ApproveRejectCaseRequest.json
ReviewRequestCallbackRequest=MakerChecker/WorkflowService/ReviewRequestCallback/ReviewRequestCallbackRequest.json
GetReviewCaseDetailsRequest=MakerChecker/ReviewService/GetReviewCaseDetails/GetReviewCaseDetailsRequest.json
UpdateReviewCaseStageRequest=MakerChecker/ReviewService/UpdateReviewCaseStage/UpdateReviewCaseStageRequest.json
FetchReviewStatusCountRequest=MakerChecker/ReviewService/FetchReviewStatusCount/FetchReviewStatusCountRequest.json
FetchSubGroupCheckersRequest=MakerChecker/ReviewService/FetchSubGroupCheckers/FetchSubGroupCheckersRequest.json

#=============================EDC DIY =====================================#
DeviceFraudcheckRequest=EDCDIY/DeviceFraudcheckRequest.json


#============================ATS ==================================#
subpartassetupdateRequest=ATS.v1.subpart_assetupdate/subpartassetreplacerequest.json
assetupdateRequest=ATS.v1.subpart_assetupdate/assetupdaterequest.json
ThresholdCountRequest=ATS.v1.getThresholdCount/getThresholdCountRequest.json
ThresholdCountRequestSingleDevice=ATS.v1.getThresholdCount/getThresholdCountRequestForSingleDeviceType.json
CreatePackPickupOrderRequest=ATS/CreateOrder/ATSPackForPickupRequest.json

#=============================EDC assetreplace =====================================#
EDCreplacesubpaercreateleadRequest=EDCreplacesubpart/assetreplacecreateleadrequest.json
sendotpassetRequest=EDCreplacesubpart/sendotpassetRequest.json
validateotpEDCRequest=EDCreplacesubpart/validateotpEDCRequest.json

###################### EGS #################################
SimDetailsRequest:EGS/SimDetailsRequest.json
SafeCustodyRequest=EGS/SafeCustodyRequest.json
VIsimdetailsRequest=EGS/VIsimdetailsRequest.json
VISimActivation=EGS/VISimActivationRequest.json
VISimSafeCustody=EGS/VISimSafeCustodyRequest.json
VIOrderDetails=EGS/VIOrderDetailsRequest.json
AirtelSimActivationRequest=EGS/AirtelSimActivationRequest.json
AirtelReuseSimRequest=EGS/AirtelSimReuseRequest.json

#====================================SIMS================================================#
DeviceSimDetailsRequest=managesim/devicesimdetailsRequest.json
SimupdateLeadRequest=managesim/simReplacementupdateLeadRequest.json

#####################  IOT #######################
CreateLeadIOTServiceFlowRequest=IOT/CreateLeadIOTServiceFlowRequest.json

#################### Update Address EDC #######################
CreateLeadUpdateDeviceAddressRequest=UpdateDeviceAddress/CreateLeadUpdateDeviceAddressRequest.json

#######################   EOS #########################
DeleteDealerRequest=EOS/DeleteDealerRequest.json

##################### Bank Channel ###################
BankChannelLeadCreationRequest=MerchantService/V1/bankcp/BankChannelLeadCreationRequest.json
BankChannelSubventionRequest=MerchantService/V1/bankcp/BankChannelSubventionRequest.json


####################  DEVICE STAND #########################
CreateLeadDeviceStandRequest=DeviceStand/CreateLeadDeviceStandRequest.json

####################  FSM  #################################
EnterpriseBeatCreationRequest=FSM/EnterpriseBeatCreationRequest.json
EDCUpgradeBeatCreationRequest=FSM/EDCUpgradeBeatCreationRequest.json
EDCReplaceBeatCreationRequest=FSM/EDCReplaceBeatCreationRequest.json
EDCDeactivationBeatCreationRequest=FSM/EDCDeactivationBeatCreationRequest.json


#####################  Retention Offer #######################
CreateRetentionOfferRequest=RetentionOfferEDC/CreateLeadRetentionOfferEDCRequest.json



####################  Create Terminal #######################
OnboardBankV3Request=OnboardBank/OnboardBanksV3OnPGRequest.json
AddUPIChannel=PG.CreateMID/UPIAddChannelRequest.json

##################  EMBEDDED INSURANCE ##################
CreateRequestGetMerchantDetailsAVS=EmbeddedInsurance/GetMerchantDetailsResponse.json


####################   EDC DEVICE UPGRADE V2 #####################
EDCDeviceUpgradeV2=EDCDeviceUpgradeV2/EDCDeviceUpgradeV2Request.json
GETleadInfo=UpgradeEDCLeadInfo/LeadInfoRequest.json
DeviceUpgradeFulfilmentBypass=UpgradeFulfilmentBypass/FulfilmentBypassRequest.json



####################   QR Service  #####################
GenerateVPARequest=QRService/GenerateVPARequest.json
GenerateVPAMapQRRequest=QRService/GenerateVPAMapQRRequest.json
ActivateVPARequest=QRService/ActivateVPARequest.json
EditQRDetails=QRService/EditQRCodeDetailsRequest.json
