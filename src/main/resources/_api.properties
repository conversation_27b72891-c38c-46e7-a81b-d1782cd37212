lead=POST:${base_url}/v1/sdMerchant/lead

TokenXMV=POST:${base_url}/v1/token
//ReAllocateAgent=POST:${base_url}/oe/panel/v1/reAllocateAgent

User=GET:${base_url}/v2/user

#============OE Panel API=============#

fetchLeads_OE=GET:${base_url}/oe/panel/v1/fetchLeads
FetchLead=GET:${base_url}/oe/panel/v1/lead/${leadId}
EditLead=POST:${base_url}/oe/panel/v1/editLead/${leadId}
ReallocateAgent=POST:${base_url}/oe/panel/v1/reAllocateAgent
PanelJobReplay=GET:${base_url}/oe/panel/v1/jobReplay/${leadId}
RegisterLeadPanel=POST:${base_url}/v1/panel/leadManagement/registerLead
GetLeadDetails=GET:${base_url}/panel/v1/solution/lead
GetBusinessDoc=GET:${base_url}/panel/v1/business/doc/status
GetAllResources=GET:${base_url}/oe/panel/v1/referenceData/getAllResources
CompanyOnboardPanel=POST:${base_url}/panel/v1/business/lead
FetchPanPanel = GET:${base_url}/panel/v1/business/pan
FetchBusinessProfilePanel = GET:${base_url}/v1/diy/businessprofile/allroles
CreateSolutionPanel = POST:${base_url}/panel/v1/solution/lead
UpdateSolutionPanel = PUT:${base_url}/panel/v1/solution/lead
VerifyDocument = POST:${base_url}/panel/v2/solution/verify/${solution}
PanelPennyDrop = POST:${base_url}/panel/v1/business/pennyDropMultiNameMatch
BusinessDetails = POST:${base_url}/oe/panel/v1/businessDetails
PanelJobReplay = GET:${base_url}/panel/v1/solution/lead/replay/${leadId}


#==========Document Upload API=============#
UploadDocumentFlow=POST:${base_url}/oe/panel/v1/document/multiPart
UploadDocumentNewAPI=POST:${base_url}/v1/workflow/lead/upload/document

#==========KYC New Stack====================#
InitiateKYC=POST:${base_url}/v1/workflow/lead/initiate/kyc
InitiateKYCV2Version=POST:${base_url}/v2/workflow/lead/initiate/kyc

#==========Lending API=====================#

triggerotp=GET:${base_url}/lms/merchant/wrapper/triggerotp
validateotp=POST:${base_url}/lms/merchant/wrapper/validateotp
getKycStatus=POST:${base_url}/v2/lending/getKycStatus
callBackSaveOTP=POST:${base_url}/v5/callback/
bank=POST:${base_url}/v1/sdMerchant/bank
fetchBankUpdate=POST:${base_url}/v1/profile/update/lead/status
bank_fetchChild=GET:${base_url}/v1/profile/update/bank
editBank=POST:${base_url}/v1/profile/update
uploadCancelledCheque=POST:${base_url}/v2/upgradeMid/doc
getBreStatus=POST:${base_url}/v2/lending/getBreStatus
dynamicTNC=GET:${base_url}/loan/lead/dynamicTNC
submitApplication=POST:${base_url}/loan/lead/submitApplication
fileUpload_BusinessStatus=POST:${base_url}/oe/panel/v1/fileProcess/upload
fetchLead_LOS=GET:${base_url}/v1/consumer/lead
createLead_LOS=POST:${base_url}/v1/consumer/lead
updateLead_LOS=POST:${base_url}/v1/consumer/lead
otpCallback_LOS=POST:${base_url}/v5/callback/
getKycStatus_LOS=POST:${base_url}/v2/lending/getKycStatus
alternateNoCallBack_LOS=POST:${base_url}/v5/callback/
getBreStatus_LOS=POST:${base_url}/v2/lending/getBreStatus
dataUpdate_LOS=POST:${base_url}/v2/lending/dataUpdate
updateLeadAddress_LOS=POST:${base_url}/v1/consumer/lead
dynamicTnC_LOS=GET:${base_url}/loan/lead/dynamicTNC
saveTncAndSubmitApplication_LOS=POST:${base_url}/loan/lead/submitApplication
lmsCallBack_LOS=POST:${base_url}/v5/callback/
checkBreStatus_LOS=GET:${base_url}/v2/lending/lead/checkBreStatus
createLeadPL_LOS=POST:${base_url}/v1/consumer/lead
updateLeadPL_LOS=POST:${base_url}/v1/consumer/lead/additionalDetails
emailCallBackPL_LOS=POST:${base_url}/v5/callback/
updateLeadAddressPL_LOS=POST:${base_url}/v1/consumer/lead/additionalDetails
addAddressPL_LOS=POST:${base_url}/v1/consumer/lead/additionalDetails
FetchLeadBackfillingAPI=GET:${base_url}/v1/workflow/lead/detail
NewBankAPI=POST:${base_url}/v1/workflow/lead/bank
UpdateLead=POST:${base_url}/v1/workflow/lead
AsyncBureau=POST:${base_url}/v1/workflow/lead/initiate/bureau

#=========PostPaid API's========================================#
CreateLeadLending=POST:${base_url}/v1/sdMerchant/lead
CreateLeadLendingAril=POST:${base_url}/internal/v3/merchant/${merchant_cust_id}
DeleteLeads=POST:${base_url}/deleteAllLeadsV2
FetchLeadDetails=GET:${base_url}/v1/consumer/lead
AddBasicDetails=POST:${base_url}/v1/consumer/lead
callbackLending=POST:${base_url}/v5/callback/
GetCKYCFromPPBL=POST:${base_url}/v2/lending/getKycStatus
UploadDocument=POST:${base_url}/v2/lending/lead/document
CKYCCallBack=POST:${base_url}/v2/lending/dataUpdate
GetBREStatus=POST:${base_url}/v2/lending/getBreStatus
CheckBREStatus=GET:${base_url}/v2/lending/lead/checkBreStatus
BRECallback=POST:${base_url}/v2/lending/dataUpdate
AddAddress=POST:${base_url}/v1/consumer/lead
FetchDynamicTnc=GET:${base_url}/loan/lead/dynamicTNC
SubmitApplication=POST:${base_url}/loan/lead/submitApplication
PPBLOTPCallback=POST:${base_url}/v5/callback/
fetchCIRv3=POST:${base_url}/v3/lending/lead/fetchCIR
CKYCID=POST:${base_url}/v1/workflow/lead

#==========Lending KYC=====================================#
kyc_base_url=https://kyc-stage5.lending.paytm.com
KYCinitiate=POST:${base_url}/v1/kyc/initiate
KYCStatus=GET:${base_url}/v1/kyc/status
KYCSubmit=POST:${base_url}/v1/kyc/submit

#=========PostPaid MINI API's========================================#
FetchLeadWorkflow=GET:${base_url}/v1/workflow/lead
FetchExistingLeadDetails=GET:${base_url}/v2/lending/fetchExistingDetail
CreateLeadWorkflow=POST:${base_url}/v1/workflow/lead
LeadReset=POST:${base_url}/v1/internal/workflow/lead/reset
FetchCIR=POST:${base_url}/v2/lending/lead/fetchCIR
LeadWorkflowCallback=PUT:${base_url}/v1/workflow/lead/callback

#========Stashfin Lender API's====================================#
CreateToken=POST:${base_url}/v3/login-client
LenderLeadReset=POST:${base_url}/v3/reset-by-phone
stashfin_url=https://devapi.stashfin.com


#========Business Lending API's========================================#
FetchLead_Lending=GET:${base_url}/v1/sdMerchant/lead/
BasicDetailsMCA=POST:${base_url}/v1/sdMerchant/lead
UpdateGenderAndPincode=POST:${base_url}/v2/lending/dataUpdate
UpdatePANAndDOB=POST:${base_url}/v2/lending/dataUpdate
BREOTPCallback=POST:${base_url}/v5/callback/
BRECallbackMCA=POST:${base_url}/v2/lending/dataUpdate
UpdateLoanOffer=POST:${base_url}/v1/sdMerchant/lead
SaveBankDetails=POST:${base_url}/v1/sdMerchant/bank
UploadCancelledCheque=POST:${base_url}/v2/upgradeMid/doc
EmandateCallback=POST:${base_url}/v5/callback/
UpdateSAI=POST:${base_url}/v1/workflow/lead
SheetUpload=POST:${base_url}/oe/panel/v1/fileProcess/upload
DocUpload=POST:${base_url}/bank-integrations/user/v1/file/upload
DocumentStatus=PUT:${base_url}/v2/lending/lead/document/status


#========Top up===========================================#
AdditionalDetailsTopup=POST:${base_url}/v2/lending/dataUpdate

#===========SSFB API's======================================#
AdditionalDataCapture=POST:${base_url}/v5/callback/
PDCLoanApplicationCallback=POST:${base_url}/v5/callback/

#===========Personal Loan API's======================================#
CreatePersonalLoanLead=POST:${base_url}/v1/consumer/lead/
BasicDetails=POST:${base_url}/v1/consumer/lead/additionalDetails/
EmailCallback=POST:${base_url}/v5/callback/
AddAddressDetails=POST:${base_url}/v1/consumer/lead/additionalDetails/
PANDOBDataUpdate=POST:${base_url}/v2/lending/dataUpdate
SecondBREInitiateCallback=POST:${base_url}/v5/callback/
BREInitiate=POST:${base_url}/los/v1/wrapper/bre2
BREStatus=POST:${base_url}/los/v1/wrapper/bre2/status
ThirdBREInitiate=POST:${base_url}/los/v1/wrapper/bre3
ThirdBREStatus=POST:${base_url}/los/v1/wrapper/bre3/status
SaveBankDetailsV2=POST:${base_url}/v2/sdMerchant/bank


#==========Profile Update API=====================#
Status=POST:${base_url}/v1/profile/update/lead/status
Update=POST:${base_url}/v1/profile/update
StatusDoc=GET:${base_url}/v1/profile/update/doc/status
CombinedLeadStatus=GET:${base_url}/v1/profile/update/instrument/lead/status
CreateVendor=POST:${base_url}/v1/profile/update
AddPan=POST:${base_url}/v1/profile/update
AddAddressPGProfileUpdate=POST:${base_url}/v1/profile/update
BankDetailUpdate = POST:${base_url}/v1/profile/update
UpdateBankProof = /v2/upgradeMid/doc

#==========50K API=====================#
Business=POST:${base_url}/v1/sdMerchant/business
AdditionalDetails=POST:${base_url}/v1/sdMerchant/additionalDetails
ValidateBankDetails=POST:${base_url}/v1/sdMerchant/validateBankDetails
UpdateBankDetails=POST:${base_url}/v1/sdMerchant/updateBankDetails

#==========Lead Management API=====================#
RegisterLead=POST:${base_url}/v1/leadManagement/registerLead

#==================Company Onboard / Business API ====================#
GetBusinessv3=GET:${base_url}/v3/merchant/business
GetCompany=GET:${base_url}/v1/company/get
PostCompany=POST:${base_url}/v1/company
BusinessProfile=GET:${base_url}/v3/merchant/businessprofile
GstExemptionList=GET:${base_url}/v1/gst/gstExemptionList
PennyDropMultiNameMatch=POST:${base_url}/v4/merchant/pennydropmultinamematch

#==========100K API=====================#

SendOtp=POST:${base_url}/v3/sendOtp
ValidateOtp=POST:${base_url}/v3/validateOtp
GetMerchant=GET:${base_url}/v3/merchant/${custId}
GetDocStatus=GET:${base_url}/v3/merchant/documentstatus
SubmitDocs=POST:${base_url}/v3/merchant/document
PinCode=GET:${base_url}/v1/cart/pincodeDetail/${pinCode}
Category=POST:${base_url}/uad/v1/category
pin=POST:${base_url}/uad/v1/addressInformation/oauth/pincode
SubCategory=POST:${base_url}/uad/v1/subcategory
LanguagePreference=GET:${base_url}/v1/resources/values/languagePreference
Banks=GET:${base_url}/v2/banks/${ifsc}
PennyDrop=POST:${base_url}/v3/merchant/pennydrop
SubmitMerchant=POST:${base_url}/v3/merchant/${custId}
TnC=GET:${base_url}/v2/ekyc/biometric/termsAndConditions

#==========OE PPBL API URLS==============#
bank_api_url=https://goldengate-staging.paytmbank.com/MerchantService

SendIvrRequest=POST:${base_url}/v2/kyc/ivr/sendIvrRequest
LeadRequest=POST:${base_url}/v1/kyc/ivr/leadRequest
ValidateIvrRequest=POST:${base_url}/v3/kyc/ivr/validateIvrRequest
#==========OAuth API URLS==============#
oAuth_api_url=https://accounts-staging.paytm.in

Authorize=POST:${base_url}/oauth2/authorize
Token=POST:${base_url}/oauth2/token
CreateUser=POST:${base_url}/oauth-wormhole/createUser
UserDetails:POST:${base_url}/oauth-wormhole/userDetail

#===========Rest APIFor EDC URLS============#
Rest_api_url=https://goldengate-staging6.paytm.com/MerchantService/panel/edc

ProductCreate=POST:${base_url}/createProduct

#==========UAD API URLS==============#
uad_url=https://uad-staging.paytm.com
AddCatSubcatSol=POST:${base_url}/uad/v1/add/category/subcategory

#==========SFtoOE====#
CreateBusinessLead=POST:${base_url}/MerchantService/panel/v1/solution/createUpdateLead
sf_url=https://goldengate-staging5.paytm.com
#==========CommonOnbaordingEDC API URLS==============#
getMerchantMIDStatus=GET:${base_url}/MerchantService/v3/merchant/mid
createMCOLead=POST:${base_url}/MerchantService/v3/validateOtp
fetchMCOLead=GET:${base_url}/MerchantService/v3/merchant/fetch/${leadId}
cKYC=GET:${base_url}/MerchantService/v1/common/fetchCkycStatus
sendOTPLead=POST:${base_url}/v3/sendOtp
getLeadStatus=GET:${base_url}/MerchantService/v3/merchant/getLeadStatus
getBusiness=GET:${base_url}/MerchantService/v3/merchant/business
getBank=GET:${base_url}/MerchantService/v2/merchant/bank/fetchMerchantBanks/${custId}
fetchBeauruStatus=GET:${base_url}/MerchantService/v1/common/fetchBureauDetails
addBank=POST:${base_url}/MerchantService/v3/merchant/${custId}
fetchqnadevices=GET:${base_url}/MerchantService/v1/qna/fetchQuestions
fetchmerchantdevicedetails=GET:${base_url}/MerchantService/v1/device/fetchMerchantBasicDeviceDetails
partialSaveCall=POST:${base_url}/MerchantService/v3/merchant/${custId}
createDeviceLead=POST:${base_url}/MerchantService/v1/device/createLead
devicedetailsfetch=GET:${base_url}/MerchantService/v1/device/edc
fetchBank=GET:${base_url}/MerchantService/v2/merchant/bank/fetchMerchantBanks/${custId}
confirmdevicePlanEDC=GET:${base_url}/MerchantService/v1/device/edc
deviceSummary=GET:${base_url}/MerchantService/v1/device/summary
deviceAddons=GET:${base_url}/MerchantService/v1/device/addOns
cartsave=POST:${base_url}/MerchantService/v1/device/cart/save
orderSave=POST:${base_url}/MerchantService/v1/device/order/save
#==========OE OCL API URLS==============#
api_url=https://goldengate-staging6.paytm.com/MerchantService
# api_url=https://goldengate-staging7.paytm.com/MerchantService
git =https://goldengate-staging6.paytm.com/MerchantService
lendingBase=https://fs-staging.paytm.com
fis_url =https://fastag-issuer-issuance-nonprod.paytmdgt.io
tools_base_url=https://bank-tools-stage.lending.paytm.com
Oauth_url= https://accounts-staging.paytm.in
GenerateMobileUser=POST:${base_url}/oauth-wormhole/createUser
FetchCustId=POST:${base_url}/oauth-wormhole/userDetail
#========== LOS API's=================#
Los_url=http://**************:7002
#===========Wallet APIs===============#
wallet_url=https://wallet-staging.paytm.in
CreateUserWallet=POST:${base_url}/wallet-web/activateUserWallet
GetQrDetails = POST:${base_url}/wallet-web/getQRCodeInfo
GenerateUpiQr = POST:${base_url}/qrcode/v4/generateQrCode
WalletAddMoney = POST:${base_url}/wallet-web/AddMoney
#=============Loan Tap API=============#
Lead_fetch=GET:${base_url}/v1/sdMerchant/lead/
Lead_create=POST:${base_url}/v1/sdMerchant/lead
lmsCallBack_loanTap=POST:${base_url}/v5/callback/

#=================Pos Insurance=============#
fetchDocStatus_posInsurance=GET:${base_url}/v2/doc/upload/status
uploadDoc_posAgent=POST:${base_url}/v2/doc/upload/
fetchDynamicTnc=GET:${base_url}/v1/sdMerchant/fetchDynamicTnC
senOtp_posAgent=GET:${base_url}/v1/sdMerchant/sendOtp
saveDynamicTnc_posAgent=POST:${base_url}/v1/sdMerchant/saveDynamicTnC
CallBackv5_posAgent=POST:${base_url}/v5/callback/


#=============Upgrade Merchant Plan=============#
MID = GET:${base_url}/v3/merchant/mid
UpgradePlans = GET:${base_url}/v1/merchant/upgradeplans
FetchDynamicDocs = GET:${base_url}/v4/merchant/doc/status

#=============MAP EDC=============#
Edc=GET:${base_url}/v1/edc
EdcPost=POST:${base_url}/v1/edc/edcOtpText
FetchPayment=GET:${base_url}/v1/edc/payment
FetchQnA=GET:${base_url}/v1/qna/fetchQuestions
ResendOtp=POST:${base_url}/v1/edc/resendOTP
EdcPostAndroidWithPOS=PUT:${base_url}/v1/edc

#=============PGP APIs=============#
pgp_url=https://pgp-staging.paytm.in
AppPay=POST:${base_url}/theia/HANDLER_IVR/CLW_APP_PAY/APP

#=============MAP POS=============#
FetchPosPlans=GET:${base_url}/v1/pos/
BrandAssociation=GET:${base_url}/v1/resources/values/brandAssociation
StoreCategory=GET:${base_url}/v1/resources/values/storeCategory

#=============PSA DIY=============#
FetchDocumentStatus = GET:${base_url}/v2/upgradeMid/doc/status
ApplicationStatus = GET:${base_url}/v1/sdMerchant/lead/applicationStatus
StartTest = GET:${base_url}/v1/gamepind/startTest
FetchQuestion = GET:${base_url}/v1/gamepind/fetchQues
SubmitQuestion = POST:${base_url}/v1/gamepind/submitAns
OrderFullfillment = GET:${base_url}/v1/payment/orderfulfilment
NotifyCallback = POST:${base_url}/oe/v1/payment/order/notify
#============Manage Agent API=============#

userInfo=GET:${base_url}/oe/panel/v1/userInfo/${phoneNumber}
agent=POST:${base_url}/oe/panel/v1/agent


#=============SoundBox=============
soundbox=POST:${base_url}/v1/soundbox
SoundBoxFetchPlan=GET:${base_url}/v1/soundbox/priceMappindDetails
SoundBoxChoosePlan=PUT:${base_url}/v1/soundbox
SoundboxLeadCount=GET:${base_url}/v3/serviceLeadCount
FetchSoundBoxPayment=GET:${base_url}/v1/soundbox/payment
SoundBoxBindUrl=GET:${base_url}/url/soundBoxBindUrl
SoundBOXDeviceType=GET:${base_url}/v1/soundbox/iotDevice
SoundBoxLead=POST:${base_url}/v1/soundbox
SoundboxTnc=GET:${base_url}/v2/ekyc/biometric/termsAndConditions
SoundboxByPassQnA=GET:${base_url}/v1/qna/fetchQuestions
boss_api_url=https://bo-staging.paytm.com
SBMerchantDetails=GET:${base_url}/api/v1/merchant/custId/${custId}
SBPinCode=POST:${base_url}/uad/v1/addressInformation/pincode
goldengate_api_url=https://goldengate-staging15.paytm.com
SentOTPSB=POST:${base_url}/MerchantService/v3/sendOtp
QrValidate=GET:${base_url}/MerchantService/v1/soundbox/prevalidatePaymentQr
FetchDevicedIot=GET:${base_url}/MerchantService/v1/soundbox/iotDevice
Fetchtnc=GET:${base_url}/MerchantService/v1/soundbox/fetchDynamicTnC
Validatedevice=POST:${base_url}/MerchantService/v1/soundbox/validateDevice
ValidateOldDevice=POST:${base_url}/MerchantService/v1/soundbox/validateDevice
GetMerchantid=GET:${base_url}/MerchantService/v3/merchant/mid
fetchtncotp=POST:${base_url}/MerchantService/v3/sendOtp
SendOTPV1=POST:${base_url}/MerchantService/v3/sendOtp
CreatenewSBLead=POST:${base_url}/MerchantService/v1/soundbox
CreateSoundboxReplacementLead=POST:${base_url}/MerchantService/v1/soundbox
FetchSoundboxReplacementLead=GET:${base_url}/MerchantService/v3/merchant/${custId}
DeviceIdentifier =GET:${base_url}/devicebinding/config/sv1
ValidateotpSB =POST:${base_url}/MerchantService/v3/validateOtp
Posid =GET:${base_url}/MerchantService/v1/soundbox/validateAndSavePosIdDetails
SimActivation=PUT:${base_url}/MerchantService/v1/soundbox
FetchPlans=POST:${base_url}/MerchantService/v1/device/fetchPlan
BindDeviceByOTP=PUT:${base_url}/MerchantService/v1/soundbox
ValidateBeatDetails=POST:${base_url}/MerchantService/v1/device/validateBeat
FetchDeviceQuestions=GET:${base_url}/MerchantService/v1/qna/fetchDeviceQuestions
UpdateAssetAnswers=PUT:${base_url}/MerchantService/v1/device/asset
SBFetchYoutubeLink=GET:${base_url}/MerchantService/v1/resources/values/sb_tutorial_video
SBFetchLeadDetails=GET:${base_url}/MerchantService/v3/merchant/1000698372
SBFetchSelectedPlan=POST:${base_url}/MerchantService/v1/soundbox/summary
ValidateAndSaveAssets=PUT:${base_url}/MerchantService/v1/device/asset
SoundboxVASLeadCreation=POST:${base_url}/MerchantService/v1/soundbox
SbPinCode=GET:${base_url}/MerchantService/v2/cart/pincodeDetail/${pincode}
SBUpdateLead=PUT:${base_url}/MerchantService/v1/soundbox
SBADDONUpdateLead=PUT:${base_url}/MerchantService/v1/soundbox
AddOnDetails=POST:${base_url}/MerchantService/v1/soundbox/addOns
DIYFetchAllocated=GET:${base_url}/MerchantService/v1/fetchAllocatedLeads
DIYUpdateDeviceID=PUT:${base_url}/MerchantService/v1/diy/soundbox
SBShopInsuranceUpdateLead=PUT:${base_url}/MerchantService/v1/soundbox
SBPlanSummary=POST:${base_url}/MerchantService/v1/soundbox/summary
SBDIYValidateMID=POST:${base_url}/MerchantService/v1/validateMid
Sound_Box_Validate_OTP=POST:${base_url}/MerchantService/v3/validateOtp
SBDIYViewMerchantSpecificLead=GET:${base_url}/MerchantService/v1/fetchAllocatedLeads
Save_Tnc=POST:${base_url}/MerchantService/v1/soundbox/saveDynamicTnC
SbInsuranceEligibility=GET:${base_url}/MerchantService/v1/device/fetchInsurancePlans
SBPayment=GET:${base_url}/MerchantService/v1/soundbox/payment
SBUPIAutopay=POST:${base_url}/MerchantService/v1/upiMandate
CreateDeviceUnbindLead=POST:${base_url}/MerchantService/v1/soundbox
SbFulfilmentBypass=POST:${base_url}/MerchantService/v1/device/fulfilmentBypass
SBValidateNewChargerInBindFlow=POST:${base_url}/MerchantService/v1/soundbox/validateDevice
SBCheckBTStatus=GET:${base_url}/MerchantService/v1/device/fetchStatus
CardSB_Create_Lead_And_Check_Existing_Subscription=POST:${base_url}/MerchantService/v1/soundbox
DIYV2SBUpdateDeviceAndQR=POST:${base_url}/MerchantService/v1/diy/soundbox/notifyLeadDetails
SimReplacementValidateOTP=POST:${base_url}/MerchantService/v3/validateOtp
SBPlanUpgradeCreateLead=POST:${base_url}/MerchantService/v1/soundbox
SBPreValidatePaymentQRNew=POST:${base_url}/MerchantService/v2/soundbox/prevalidatePaymentQr
FetchNextScreenSB=GET:${base_url}/MerchantService/v1/device/fetchNextScreen
DIYUpdateDeviceDetails=POST:${base_url}/MerchantService/v1/diy/soundbox/notifyLeadDetails
DIYSBQRUpdateAWBNumber=POST:${base_url}/MerchantService/v1/diy/soundbox/notifyLeadDetails
SBDIYV2OrderDelivery=POST:${base_url}/MerchantService/oe/v1/payment/order/notify
FetchPOSIdSB=GET:${base_url}/MerchantService/v1/soundbox/pos
FetchQrDetails=POST:${base_url}/MerchantService/v1/merchant/fetchqrdetails
GetMerchantBasicDetails=GET:${base_url}/MerchantService/v1/QRSticker/getMerchantBasicDetails
InitiateNssLead=POST:${base_url}/MerchantService/v1/device/inititateNssLead
GetSoundBoxMerchantLeadDetails=GET:${base_url}/MerchantService/v1/soundbox/getSoundBoxMerchantLeadDetails
initiateH2HLead=POST:${base_url}/MerchantService/v1/device/inititateH2HLead

#=============UNMAP EDC=============
CreateUnmapEdcLead=POST:${base_url}/v1/edc/createUnmapEdcLead
UnmapEDCReplaceReasons=GET:${base_url}/v1/resources/values/UnmapEDCReplaceReasons
UnmapEDCReturnReasonsWithClaimAMC=GET:${base_url}/v1/resources/values/UnmapEDCReturnReasons
UnmapEDCReplaceReasonsWithClaimAMC=GET:${base_url}/v1/resources/values/UnmapEDCReplaceReasons
UnmapEDCUpgradeReasons=GET:${base_url}/v1/resources/values/UnmapEDCUpgradeReasons
UnmapEDCReturnReasons=GET:${base_url}/v1/resources/values/UnmapEDCReturnReasons
LeadInfo=GET:${base_url}/MerchantService/v1/device/edc/upgrade
fetchAllTerminal=GET:${base_url}/v1/merchant/fetchAllTerminal
validateEDCQr=POST:${base_url}/v1/edc/validateEDCQr
unmapEDCMachine=PUT:${base_url}/v1/edc/unmapEDCMachine
FetchEdcUpgradePlans=GET:${base_url}/v1/edc/fetchUpgradePlans/EDC

#=============MAP QR=============
MidMobile =GET:${base_url}/v3/merchant/mid/mobile
QrMappingSendOtp=POST:${base_url}/MerchantService/v3/sendOtp
QrMappingValidateOTPLeadCreation=POST:${base_url}/MerchantService/v3/validateOtp
QRMappingFetchEligibleBanks=GET:${base_url}/MerchantService/v1/QRSticker/getEligibleBankQRs
QRMappingGetStaticPref=GET:${base_url}/MerchantService/v3/merchant/getStaticPref
QRMappingFetchPosDetails=GET:${base_url}/MerchantService/v2/device/posDetails


#============Channel Onboarding API=============#

Mobile=GET:${base_url}/v3/merchant/mid/mobile
ChannelAdd=POST:${base_url}/chlbwp/subscription/v3/add

#============Revisit API=============#

RevisitOrganised=GET:${base_url}/v1/revisit/storeID
RevisitUnOrganised = GET:${base_url}/v1/revisit/${input}
RevisitSubmit = POST:${base_url}/v1/revisit/submit

#============PG CallBack API=============#
PgCallBack = POST:${base_url}/v3/pg/postmid

#============Dynamic Tnc==================

v2FetchDynamicTnc =GET:${base_url}/v2/lead/fetchDynamicTnC
v2SaveDynamicTnc = POST:${base_url}/v2/lead/saveDynamicTnC

#============PG Url==================

bossPG_url=https://bo-staging.paytm.com
pg_url=https://pg-staging.paytm.in
GetMIDUsingCustID = GET:${base_url}/api/v1/merchant/custId/${custID}
CreateMerchantOnPG = POST:${base_url}/admin/app/api/v3/submitCreateMerchantAPIRequest
MarkingMIDInactiveOnPG= PUT:${base_url}/api/v1/blockUnblock/status
EditMerchantOnPG=POST:${base_url}/admin/app/api/v3/submitEditMerchantAPIRequest
CreateTerminalInPG=POST:${base_url}/api/v1/terminal
ActiveTerminalInPG=PUT:${base_url}/api/v1/terminal/verify
ReplaceTerminalInPG=PUT:${base_url}/api/v1/terminal/replace
GetMIDUsingMobileNumber=GET:${base_url}/api/v1/merchant/custId/0?mobileNumber=${MobileNumber}
CreateEnterpriseMerchantOnPG=POST:${base_url}/admin/app/api/v3/submitCreateMerchantAPIRequest
GetPreference=GET:${base_url}/api/v1/merchant/preference/${mid}
StaticPreference=POST:${base_url}/api/v1/merchant/staticPref

ApplyTieredMdr=POST:${base_url}/api/v4/merchant/client/${mid}/commissions
OnboardBanks=POST:${base_url}/api/v1/terminal/onboard/banks
ConfigureMbidOnPG=POST:${base_url}/admin/app/api/v2/configureMBID
OnboardBanksRBDC=POST:${base_url}/api/v3/terminal/onboard/banks




#============ Assisted Merchant==================
AssistedMerchant = GET:${base_url}/v1/assistedmerchant/${phone}
Commission = GET:${base_url}/v1/commission
Commissiontncs =GET:${base_url}/v1/profile/update/commissiontncs


#============Fastag Assisted==================
ValidateTag = POST:${base_url}/fastag/validateTag
FastagDropDown = GET:${base_url}/fastag/v2/getDropDownList
FetchIssuance= GET:${base_url}/v2/fastag/fetchIssuanceType
CreateFastag = POST:${base_url}/v2/fastag
ValidateFastag = POST:${base_url}/v2/fastag/validate
FetchVirtualCart= GET:${base_url}/v2/fastag/virtualCartQR
FetchV3Merchant= GET:${base_url}/v3/merchant/fetch/${leadId}
FetchFastagTnC= GET:${base_url}/fastag/fetchTnC
FetchFastagPayment= GET:${base_url}/v2/fastag/payment
GetFastagTags = GET:${base_url}/testing/getUnblockTags

#================EDC DIY====================#
fetchPlanEdcDIY=GET:${base_url}/v2/edc/plans
AcceptTermsAndConditionsEdcDIY=GET:${base_url}/v1/sdMerchant/termsAndConditions
validateOrderEdcDIY=POST:${base_url}/v2/edc/validateOrder
CheckEligibilityDIY=GET:${base_url}/v2/edc/diyUpgradePlans/checkEligibility


#================OMS APIs==================#
checkout_url=http://checkout-staging.paytm.com
oms_url=https://oms-staging.paytm.com
FetchOMSOrder=GET:${base_url}/v3/order/${orderId}/fetch
AuthorizeOMS=POST:${base_url}/v1/authorize
CreateQROMS=POST:${base_url}/v3/order/checkout

#================UMO APIs==================#
UpdateIdentity = POST:${base_url}/v1/sdMerchant/updateIdentityDetails
CreateAccount = POST:${base_url}/v1/sdMerchant/lead/createAccount


#============ Online Merchant==================
CreateLead = POST:${base_url}/v1/upgradeMid/lead
CreateLeadReseller = POST:${base_url}/v1/upgradeMid/lead
CreateLeadPaymentLink = POST:${base_url}/v1/upgradeMid/lead
UpdateBusiness = POST:${base_url}/v1/upgradeMid/business
UpdateAddtionalDetails = POST:${base_url}/v1/upgradeMid/additionalDetails
ValidateBankDetailsOnline = POST:${base_url}/v1/upgradeMid/validateBankDetails
UpdateBankDetailOnline = POST:${base_url}/v1/upgradeMid/updateBankDetails
UpdateTncOnline = POST:${base_url}/v1/upgradeMid/tnc
FetchLeadStatus = GET:${base_url}/v1/upgradeMid/lead/status
FetchLeadData = GET:${base_url}/v1/upgradeMid/lead
PaymentsLeadStatus = GET:${base_url}/v1/payments/leads/status
FetchAllBusiness = GET:${base_url}/v1/upgradeMid/business
UpdateBusinessIndividual = POST:${base_url}/v1/upgradeMid/business
CreateLeadOfflineToOnline= POST:${base_url}/v1/upgradeMid/lead

#============ Online Merchant Indigo==================
CreateLeadIndigoOnboarding = POST:${base_url}/v1/upgradeMid/lead
CreateLeadIndigoReseller = POST:${base_url}/v1/upgradeMid/lead
UpdateAddtionalDetailsGSTIndigoOnboarding = POST:${base_url}/v1/upgradeMid/additionalDetails
UpdateAddtionalDetailsAdhaarIndigoOnboarding = POST:${base_url}/v1/upgradeMid/additionalDetails
UpdateAddtionalDetailsAddressIndigoOnboarding = POST:${base_url}/v1/upgradeMid/additionalDetails
UpdateAdditionalDetailsOwner= POST:${base_url}/v1/upgradeMid/additionalDetails
UpdateAdditionalDetailsAadhaarRegistered = POST:${base_url}/v1/upgradeMid/additionalDetails

#============ KYB APIs==================
kyb_url = https://kyc-ite.paytm.in
kyb_getAddress_url=https://address-stage-int.paytm.com
KybSave = POST:${base_url}/kyb/save
KybEdit = POST:${base_url}/kyb/customer/${CustId}/role/Merchant/solution/${SolutionType}
KybCompanyOnboard= POST:${base_url}/v2/company/onboard
GetAddressInKyb=GET:${base_url}/web/kyb/get/address


#==========CIF APIs=====================
cif_url=https://cif-staging.paytm.in
BrandActiveInKyb=GET:${base_url}/kyb/getByPgMid
EditAddressInKyb=POST:${base_url}/kyb/edit/midResourceAddress
KybGet=GET:${base_url}/kyb/get
FetchVettedKeys=GET:${base_url}/kyc/tnc/fetch/vettedKeys
TncAccept=PUT:${base_url}/kyc/tnc/admin/accept
TncUser=PUT:${base_url}/kyc/tnc/usergit add
EditBrandEMI=POST:${base_url}/kyb/ip/brandEmi/edit

#============FSM APIs========================
fsm_url=https://fse-staging.paytm.com
fsm_url=https://fse-staging.paytm.com
FetchFseDetail=GET:${base_url}/fse/admin/fetch/details
FetchMerchantDetail=POST:${base_url}/fse/admin/find/merchantData
FetchBeatDetail=POST:${base_url}/beats/admin/get-beat-tags
UpdateTeamAndSubteam=POST:${base_url}/fse/updatePanel/manpower/upload
EnterpriseBeatCreation=POST:${base_url}/service-flow/service-beat



#=============DIY Cash At Pos================#
CreateLeadDIYCashAtPos=POST:${base_url}/v1/sdMerchant/lead
FetchLeadBrandEMIDIY=GET:${base_url}/v1/sdMerchant/lead
FetchLeadMposDIY=GET:${base_url}/v1/payments/leads/status
CreateLeadMposDIY=POST:${base_url}/v1/sdMerchant/lead
FetchDocumentDIYCashAtPos=GET:${base_url}/v1/upgradeMid/doc/status
UploadDocCashAtPosDIY=POST:${base_url}/v1/upgradeMid/doc
#=============CRM API URLS================#
crm_api_url=http://************
CreateEvent=POST:${base_url}/v1/event/create
CancelEvent=POST:${base_url}/v1/event/cancel
UpdateEvent=POST:${base_url}/v1/event/update

#=============UPI API URLS================#

upi_api_url = https://upipms-staging1.paytmbank.com
upi_store_sms_url = https://upisecure-staging.paytmbank.com
UpiInitiateSmsV3 = GET:${base_url}/upi/ext/meta/app/v3/user/initiate-sms
UpiStoreSms=POST:${base_url}/v1/ext/user/sms/data
UpiDeviceBinding=POST:${base_url}/Paytm_UPI/upi/device-binding-v2
UpiAddVpa=POST:${base_url}/upi/vpa/v2/add-vpa
UpiListAccount=POST:${base_url}/Paytm_UPI/upi/la
UpiAddAccount=POST:${base_url}/Paytm_UPI/upi/add-bank-account
UPI_Secure_url = https://upisecure-staging.paytmbank.com
GetUPIAccountDetail=POST:${base_url}/upi/account/getaccountsdetail

#==========OE ATS API URLS==============#
ats_base_url=https://ats-qa.paytm.com
ats_url=https://ats-uat.paytm.com
sts_url=https://sts-uat-int.paytm.com
CreateOrderPackForPickUp=POST:${base_url}/v1/create-order/submit
Subpartassetupdate=POST:${ats_base_url}/v1/asset/update
AtsCatagory=POST:${base_url}/v1/category
SearchATSCatagory=GET:${base_url}/v1/category/search
CreateATSSupplier=POST:${base_url}/v1/supplier
SearchATSSupplier=GET:${base_url}/v1/supplier/search
CreateSKU=POST:${base_url}/v1/sku/onboarding
FetchSKUDetails=GET:${base_url}/v1/sku/details
GetSKUListOfParams=GET:${base_url}/v1/sku/listParams
SearchSKU=GET:${base_url}/v1/sku/search
UpdateSKU=POST:${base_url}/v1/sku/update
CreateSKUWithoutSubpart=POST:${base_url}/v1/sku/onboarding
CreateSubSku=POST:${base_url}/v1/sku/onboarding
GenerateBarcode=GET:${base_url}/v1/barcode/individual/generate
GenerateChildBarcode=GET:${base_url}/v1/barcode/child/generate
OnboardBarcode=POST:${base_url}/v1/barcode/individual/onboard
OnboardChildBarcode=POST:${base_url}/v1/barcode/child/onboard
OnboardBarcodeInSeries=POST:${base_url}/v1/barcode/individual/series/onboard
ACLFetchPermission=GET:${base_url}/v1/acl/
GenerateQRCode=GET:${base_url}/v1/user/generateQRCode
UserGetDetails=GET:${base_url}/v1/user/getDetail
VerifiedQRCode=GET:${base_url}/v1/user/verifyQRCode
FetchBarcodeDetails=GET:${base_url}/v1/barcode
FetchBarcodeDetailsCanAssign=GET:${base_url}/v1/barcode/canAssign
V2FetchBarcodeDetails=GET:${base_url}/v2/barcode
DownloadBarcodeDetails=GET:${base_url}/v1/barcode/download/details
AssetCountForAllStates=GET:${base_url}/v1/ats/user/assetCount
AssetCountForAvailable=GET:${base_url}/v1//ats/user/assetCount/Available
AssetCountForDeployed=GET:${base_url}/v1//ats/user/assetCount/Deployed
AssetCountForPending_Assign=GET:${base_url}/v1//ats/user/assetCount/Pending_Assign
AssetCountForPending_Acknowledgement=GET:${base_url}/v1//ats/user/assetCount/Pending_Acknowledgement
AssetCountForAssigned=GET:${base_url}/v1//ats/user/assetCount/Assigned
AssetCountForUnmapped=GET:${base_url}/v1//ats/user/assetCount/Unmapped
AssetCountForReturned=GET:${base_url}/v1//ats/user/assetCount/Returned
AssetCountForLost=GET:${base_url}/v1//ats/user/assetCount/Lost
ListOfAssetDetailsForAvailable=GET:${base_url}/v1/ats/user/assetDetails/Available
ListOfAssetDetailsForDeployed=GET:${base_url}/v1/ats/user/assetDetails/Deployed
ListOfAssetDetailsForPending_Assign=GET:${base_url}/v1/ats/user/assetDetails/Pending_Assign
ListOfAssetDetailsForPending_Acknowledgement=GET:${base_url}/v1/ats/user/assetDetails/Pending_Acknowledgement
ListOfAssetDetailsForAssigned=GET:${base_url}/v1/ats/user/assetDetails/Assigned
ListOfAssetDetailsForUnmapped=GET:${base_url}/v1/ats/user/assetDetails/Unmapped
ListOfAssetDetailsForReturned=GET:${base_url}/v1/ats/user/assetDetails/Returned
ListOfAssetDetailsForLost=GET:${base_url}/v1/ats/user/assetDetails/Lost
AtsRequestDownload=POST:${base_url}/v1/downloadRequest/
AtsSkuGroup=POST:${base_url}/v1/skuGroup
FetchSkuGroups=GET:${base_url}/v1/skuGroup/fetch
AtsCheckRequestStatus=GET:${base_url}/v1/downloadRequest/
AtsDownloadFile=GET:${base_url}/v1/downloadRequest/Download
AtsAssetValidate=GET:${base_url}/v1/asset/validate
EmployeeProfile=GET:${base_url}/v1/userProfile/search
AtsAssetUpdate=POST:${base_url}/v1/asset/update
ListOfAssetDetailsForReceive_Rejected=GET:${base_url}/v1/ats/user/assetDetails/Receive_Rejected
CheckRemainingCapacity:POST:${base_url}/v1/inventory/getThresholdCount


#==========UPM V2 API URLS==============#
CreateLead_UPM_V2=POST:${base_url}/v1/sdMerchant/lead
SaveBusiness=POST:${base_url}/v1/sdMerchant/business
AdditionalDetails_UPM_V2=POST:${base_url}/v1/sdMerchant/additionalDetails
SaveIdentityAdhaarDetails=POST:${base_url}/v1/sdMerchant/updateIdentityDetails
SaveIdentityPANDetails=POST:${base_url}/v1/sdMerchant/updateIdentityDetails
SaveIdentityGSTINDetails=POST:${base_url}/v1/sdMerchant/updateIdentityDetails
ValidateBank=POST:${base_url}/v1/sdMerchant/validateBankDetails
SaveBankUPM=POST:${base_url}/v1/sdMerchant/updateBankDetails
SaveRefreeCodeWhatsapp=POST:${base_url}/v1/sdMerchant/additionalDetails


#===============Brand EMI UMP APIS===============#
GetAllBrands=GET:${base_url}/v1/brandEmi/getAllBrands
ValidateDealer=POST:${base_url}/v1/brandEmi/dealer/validate

#=================ICICI APIS====================#
FetchMID=GET:${base_url}/v2/orchestrator/mid
FetchStatus=GET:${base_url}/v2/orchestrator/status
CreateLeadWithOutGSTIN=POST:${base_url}/v2/orchestrator/onboarding
CreateLeadWithGST=POST:${base_url}/v2/orchestrator/onboarding

#===============EOS========================#
eos_url=https://qa5-edc.paytm.com
GetBrandList=POST:${base_url}/eos5/getBrandEmiList
AddDealer=POST:${base_url}/eos5/addBrandEmiDealer
GetBrandDealer=POST:${base_url}/eos5/getBrandEmiDealer
CreateLeadICICI=POST:${base_url}/v2/orchestrator/onboarding
DeleteDealer=POST:${base_url}/eos5/deleteBrandEmiDealer



#===============HDFC POS ONBOARDING APIS===============#

HDFC_BASE_URL=https://goldengate-staging2.paytm.com
#HDFC_BASE_URL=https://goldengate-staging5.paytm.com

CreateHDFCLead=POST:${base_url}/MerchantService/orchestrator/onboarding?channel=OE_PANEL


#=================MerchantCommonOnboard====================#

FetchScreenDetails=GET:${base_url}/v1/common/fetchScreenDetails
AudioDetails=GET: ${base_url}/v1/common/fetchScreenDetails
AdditionalDetailsFetchScreen=GET:${base_url}/v1/common/fetchScreenDetails

#=================GET OTP From Seller Panel====================#


SellerPanelURL=https://fulfillment-staging.paytm.com
GetOtpFromSellerPanel=GET:${base_url}/v1/admin/notification/logs

#================GET All Resources =======================#
GetallResources=POST:${base_url}/v1/getAllResources

#================Light weight API=========================#
Lead=GET:${base_url}/v1/sfcrm/lead
GetMerchantAgreementTnC=GET:${base_url}/v2/profile/update/commissiontncs
MerchantAgreementTnCAccept=POST:${base_url}/v1/upgradeMid/tnc

#================Subscription API=========================#
FetchPlanSubscription=GET:${base_url}/api/v1/device/onboard/plan

#================TestSubscription API=========================#

SubscriptionPlanOnboarding=POST:${base_url}/api/v1/device/onboard/plan
subscription_url=https://subscriptions-staging.paytm.com
CreateSubscription=POST:${base_url}/api/v1/subscription
SubscriptionStatus=PUT:${base_url}/api/v1/subscription/status
FetchNewUsnSubscription=GET:${base_url}/api/v1/subscription/usn/details
FetchOldUsnSubscription=GET:${base_url}/api/v1/merchant/services/details
ReplaceSubscriptionRequest=PUT:${base_url}/api/v2/subscription/usn

#=====================CommonOnbaordingEDC API==================================#
mid_url=https://goldengate-staging5.paytm.com



#=====================ODS API==================================#
ods_url=https://ods-qa1-int.paytm.com
OdsCategories=GET:${base_url}/v1/serviceCategory/categories
FetchPlanODS=POST:${base_url}/v1/plan/fetchPlans
FetchSubCategoryODS=GET:${base_url}/v1/serviceCategory/sub-categories
OdsUserInfo=GET:${base_url}/v1/user
CreatePlanODS=POST:${base_url}/v1/plan/create
ViewPlanODS=GET:${base_url}/v1/plan/view
OdsSearchPlan=GET:${base_url}/v1/plan/search/servicePlan
OdsNumberofRecords=GET:${base_url}/v1/plan/search/totalNumberOfRecords
OdsPayMode=GET:${base_url}/v1/paymentMode/paymentModeTypesSubtypes
OdsAclService=GET:${base_url}/v1/acl
SettlementStrategyFetch=GET:${base_url}/v1/merchant/getSettlementStrategies
CreateLeadTWSToBW=POST:${base_url}/v1/profile/update
AddOnMappingPlanSearchAPIODS=POST:${base_url}/v1/plan/search/mappingPlan
AddonMappingRequestSearchAPI=POST:${base_url}/v1/mappingRequest/search
BulkUpdateSearchPageAPI=POST:${base_url}/v1/bulkUpdate/search
FetchApplicableFiltersAPI=GET:${base_url}/v1/filter/fetchApplicableFilters
FetchFilterValuesAPI=GET:${base_url}/v1/filter/fetchFilterValues
PlanDifferenceAPI=GET:${base_url}/v1/plan/viewDiffPlan

#===========DIY MCO===========#
LeadCreate=POST:${base_url}/v1/sdMerchant/lead
Getstarted=POST:${base_url}/v1/sdMerchant/lead/updateLead
UploadAadhaarOcr=/v1/profile/update/ocr/doc2
UpdateLeadAadhaar=POST:${base_url}/v1/sdMerchant/lead/updateLead
UpdateBussinessCat=POST:${base_url}/v1/sdMerchant/lead/updateLead
UpdatePennyDrop=POST:${base_url}/v1/sdMerchant/lead/updateLead
SubmitBankLead=POST:${base_url}/v1/sdMerchant/lead/updateLead
UpdateShopAddress=POST:${base_url}/v1/sdMerchant/lead/updateLead
AadhaarUpdate=POST:${base_url}/v1/sdMerchant/lead/updateLead
SubmitTheLead=POST:${base_url}/v1/sdMerchant/lead/updateLead
FetchCat=GET:${base_url}/v1/sdMerchant/allCategoryBySolf
UploadAadhaarDoc=/v2/upgradeMid/doc
UploadMerchantSelfie=/v2/upgradeMid/doc
UploadBankProof=/v2/upgradeMid/doc
CreateDeals=POST:${base_url}/v1/profile/update
AddChannelCallback = POST:${base_url}/v1/boss/addChannel/callback


DealsFetchTnc=GET:${base_url}/v1/sdMerchant/fetchDynamicTnC

FetchDeviceDetailsAPI = GET:${base_url}/v1/plan/deviceDetails
FetchActiveQuestionAnswers = POST: ${base_url}/v1/recoveryConfiguration/fetchActiveQuestionAnswers
TotalNumberOfRecords = GET:${base_url}/v1/plan/search/totalNumberOfRecords

#===========DIY EDC===========#

Boss_url=https://bo-staging.paytm.com
FetchMid=GET:${base_url}/api/v2/merchant/details
FetchMerchantDocDetails=GET:${base_url}/v1/sdMerchant/lead/fetchMerchantDocDetails
CreateLead1=POST:${base_url}/v1/sdMerchant/lead
CL1=GET:${base_url}/v1/sdMerchant/lead/fetchMerchantDocDetails
AadhaarDetails=POST:${base_url}/v1/sdMerchant/lead/updateLead
UploadAadhaarOcr1=/v2/upgradeMid/doc
UploadBusinessDoc1=/v2/upgradeMid/doc
BankProof=/v2/upgradeMid/doc
UploadDoc=/v2/upgradeMid/doc

#===========DIY Merchant Limit Upgrade===========#
FetchMerchantDocDetail = GET:${base_url}/v1/sdMerchant/lead/fetchMerchantDocDetails
FetchScreenDetailsLimitUpgrade = GET:${base_url}/v1/sdMerchant/lead/fetchScreenDetails
CreateLeadMerchantLimitUpgrade = POST:${base_url}/v1/sdMerchant/lead
UpdatePanManuallyUpgradeLimit = POST:${base_url}/v1/sdMerchant/lead/updateLead
DiyGstUpdate = POST:${base_url}/v1/sdMerchant/lead
LeadUpdate = POST:${base_url}/v1/sdMerchant/lead/updateLead
FetchSuggestedName = GET:${base_url}/v1/profile/update/fetchSuggestedName

#=====================IOT API==================================#
iot_url=https://goldengate-staging5.paytm.com
IOTnotification=POST:${base_url}/MerchantService/v1/deviceConfig/iot
CreateLeadIOTServiceFlow=POST:${base_url}/MerchantService/v1/deviceConfig/create

#=====================FetchDeploymentDetails API==================================#
Fetchdeploymentdetails_url=https://goldengate-staging5.paytm.com
Fetchdeploymentdetails = GET:${base_url}/MerchantService/v1/device/fetchDeploymentDetails

#----------------------Maker-checker APi's-----------------------------#

workflow_engine_url = https://workflow-engine-qa.paytm.com
Review_service_url = https://review-service-qa.paytm.com
kyr_staging_url = https://cif-staging.paytm.in
CreateReviewCase=POST:${base_url}/v1/case/create
FetchWorkflow=POST:${base_url}/v1/workflow/search
GetWorkflowDetail=GET:${base_url}/v1/workflow/fetch
CreateWorkflow=POST:${base_url}/v1/workflow/create
FetchRejectionReasons=GET:${base_url}/v1/case/rejectionReasons
FetchDMSDoc=GET:${base_url}/v1/case/documents
DownloadDMSDoc=GET:${base_url}/v1/case/downloadDms
FetchAllCases=POST:${base_url}/v1/case/fetch
ReviewPanelGetAllResources=GET:${base_url}/v1/case/getAllResources
ApproveRejectCase=POST:${base_url}/v1/case/review/bulk
ReviewRequestCallback=POST:${base_url}/v1/request/review
GetReviewCaseDetails=GET:${base_url}/v1/case/fetch/caseDetails
UpdateReviewCaseStage=POST:${base_url}/v1/case/update
FetchReviewStatusCount=GET:${base_url}/v1/case/statusCount
FetchSubGroupCheckers=GET:${base_url}/kyr/fetchUsers

######################## BILLING API #############################
billing_url=https://billing-engine-dev-internal.paytm.com
BillingOnboard=POST:${base_url}/refund-service/api/v1/device/onboard
BillingReturn=POST:${base_url}/refund-service/api/v1/device/return

######################## FSE DIY Onboarding #############################

SendOtpFseDiy=POST:${base_url}/v1/fse/diy/sendOtp
SubmitLeadDetail = POST:${base_url}/v1/fse/diy/${custID}
ValidateOtpFseDiy = POST:${base_url}/v1/fse/diy/validateOtp
AddBankDetail =POST:${base_url}/v1/fse/diy/${custID}
AddHomeAddress=POST:${base_url}/v1/fse/diy/${custID}
FetchQuestions = GET:${base_url}/v1/qna/fetchQuestions
FseDiySaveTnc=POST:${base_url}/v1/fse/diy/save/tncs
FetchFseInfo=GET:${base_url}/v1/fse/diy/${custID}




######################## EDC DIY #############################
staging6_api_url=https://goldengate-staging1.paytm.com
DeviceFraudcheck=POST:${base_url}/MerchantService/v1/sfcrm/device/fraudcheck

#==========VMN API URLS==============#
VMN_MerchantDetails_Url=http://***********:9200
MerchantDetailsURLPath=GET:${base_url}/fact_vmn_srs/_search

######################## store cash back #############################
CreateStoreCashBack=POST:${base_url}/v1/profile/update
StoreCashBackTnc=GET:${base_url}/v1/sdMerchant/fetchDynamicTnC


######################## Experian Pull #############################
ExperianPullURL:http://**************:9888
ExperianPull=POST:${base_url}/lending-bureau/service/v1/bureau/credit-report

######################## Fetch Whitelist Details URL #############################
FetchWhitelistDetailsURL:http://**************:10055
FetchWhitelistDetails=GET:${base_url}/lending/risk/stage1/internal/v1/whitelist/details

#=====================EDC assetreplace API==================================#
Edcassetreplacecreatelead=POST:${base_url}/MerchantService/v1/device/asset
ScanassetQR=POST:${base_url}/MerchantService/v1/device/asset
DeviceFetchTnc=GET:${base_url}/MerchantService/v1/device/fetchTnc
ValidateotpEDC =POST:${base_url}/MerchantService/v3/validateOtp
Devicepayment=GET:${base_url}/MerchantService/v1/device/payment



#=====================SIM REPLACEMENT API==================================#
FetchDevicesimdetails=GET:${base_url}/MerchantService/v1/device/simDetails
SimupdateLead = POST:${base_url}/MerchantService/v3/merchant/simReplacement/updateLead

####################### EGS API #####################################
egs_base_url:https://egs-eks-staging.paytm.com
SimDetails:GET:${base_url}/v1/airtel/sim-details
SafeCustody=POST:${base_url}/v1/airtel/safe-custody
VISIMDetails=GET:${base_url}/v1/vi/sim-data
VISimActivation=POST:${base_url}/v1/vi/sim-activation
VISimSafeCustody=POST:${base_url}/v1/vi/safe-custody
VIOrderDetails=POST:${base_url}/v1/vi/safe-custody
AirtelSimActivation=POST:${base_url}/v1/airtel/sim-activation
AirtelReuseSim=POST:${base_url}/v1/airtel/sim-resume


########################  ADDRESS UPDATE ###############
goldengate_url=https://goldengate-staging6.paytm.com
CreateLeadUpdateDeviceAddress=POST:${base_url}/MerchantService/v1/deviceDetails/create

######################## Bank Channel ##################
BankChannelLeadCreation=POST:${base_url}/v1/bankcp/lead

######################## EMI Instrument Enable Disable ##################
InstrumentEnableDisable=POST:${base_url}/v1/profile/update


#=====================shop Insurance==================================#
insurance_url=https://insurance-staging.paytm.com
Embeddedshopinsurance=POST:${base_url}/v1/public/embedded/eligibility
AddonShopInsurance=GET:${base_url}/MerchantService/v1/device/addOns/additional
ShopInsuranceConfirmPolicy=POST:${base_url}/v1/public/embedded/confirmPolicy

##################### DEVICE STAND ############################
device_stand=https://goldengate-staging6.paytm.com
CreateLeadDeviceStand=POST:${base_url}/MerchantService/v1/device/accessories/create

##################### NCMC ############################

GetNcmcProducts=GET:${base_url}/MerchantService/v1/ncmc/productList
ncmcsendotp=POST:${base_url}/MerchantService/v1/ncmc/sendOTP
ncmcvalidateotp=POST:${base_url}/MerchantService/v1/ncmc/validOTP
lead_state=GET:${base_url}/MerchantService/v3/merchant/fetch/${lead_id}
generate_qr_ncmc=POST:${base_url}/MerchantService/v1/ncmc/generateQR
fetch_payment_status=POST:${base_url}/MerchantService/v1/ncmc/paymentStatus
submitKyc=POST:${base_url}/MerchantService//v1/ncmc/submitKyc

##################### Revisit ############################

GetMidRevisit=GET:${base_url}/MerchantService/v3/merchant/mid/mobile
GetDeviceIds=GET:${base_url}/MerchantService/v3/revisit/getDeviceIds/${mid}
MPAaddress=GET:${base_url}/MerchantService/v3/revisit/${mid}
RevisiteBeatDetails=POST:${base_url}/MerchantService/v1/revisit/beatDetails
SendOtpRevisit=POST:${base_url}/MerchantService/v3/sendOtp
ValidateOtpRevisit=POST:${base_url}/MerchantService/v3/validateOtp
Fetchquestions=GET:${base_url}/MerchantService/v1/qna/fetchQuestions
GetmidReferenceNumber=GET:${base_url}/MerchantService/v3/revisit/mid/${shopReferenceNumber}
createLeadRevisit=POST:${base_url}/MerchantService/v3/revisit/submit
RevistLoancapture=POST:${base_url}/MerchantService/v1/revisit/beatDetails
Submitquestions=POST:${base_url}/MerchantService/v3/revisit/submit
LoanIntentfetchDocumentDetails=GET:${base_url}/v4/merchant/doc/status
RevisitSubmitDetails=POST:${base_url}/MerchantService/v3/revisit/submit
RevisitGetKybValidDocs=GET:${base_url}/MerchantService/v3/revisit/getKybValidDocs/${mid}
#=====================EDC Retention OFFER ==================================#
staging_url=https://goldengate-staging6.paytm.com
RetentionOfferEDC=POST:${base_url}/MerchantService/v1/device/retention

################### Create Terminal #####################
boss_url=https://bo-staging.paytm.com
OnboardBankV3=POST:${base_url}/api/v3/terminal/onboard/banks
UPIEligibilityOnBoss=GET:${base_url}/api/v1/bank/upi/eligibility/check
UPIAddChannel=POST:${base_url}/api/v1/bank/upi/add/channel


######################     EMBEDDED INSURANCE ####################
avs_url=https://dev.emi-nonprod.paytm.com
MerchantDetailsAVS=GET:${base_url}/avs/v2/insurance/merchant/get

######################     PSA Agent Onboarding ####################
PSAFetchScreenDetails=GET:${base_url}/MerchantService/v1/common/fetchScreenDetails
PostAdhaardata=POST:${base_url}/MerchantService/v3/merchant/${cust_id}
BankDetailsAgentOnboarding=GET:${base_url}/MerchantService/v1/fse/diy/banks/${ifsccode}
PostBankDetailsAgentOnboarding=POST:${base_url}/MerchantService/v3/merchant/${cust_id}
###################    EDC DEVICE UPGRADE ##################
Staging27_url=https://goldengate-staging6.paytm.com
EDCDeviceUpgradeV2=POST:${base_url}/MerchantService/v1/edc/createUnmapEdcLead
DeviceUpgradevalidateEDCQr=POST:${base_url}/MerchantService/v1/edc/validateEDCQr
UpgradeEDCFulfilmentByPass=POST:${base_url}/v1/device/fulfilmentBypass


################## QR INTEGRATION API ##################
qr_integration_url=https://qr-INTEGRATION.paytm.in
GenerateVPA=POST:${base_url}/qrcode/v5/generateQrCode
ActiveVPA=GET:${base_url}/qrcode/debug/vpa/map
EditQRCodeDetailsRequest=POST:${base_url}/qrcode/v5/editQrCodeDetails

################## Enterprise Api ##################

CreateLeadEnterprise=POST:${base_url}/MerchantService/panel/v1/solution/createUpdateLead/
CreateLeadEnterprise_OnUs=POST:${base_url}/MerchantService/panel/v1/solution/createUpdateLead/
GetLeadDataEnterprise=GET:${base_url}/MerchantService/panel/v1/solution/lead
EnterprisefetchAllResources=GET:${base_url}/MerchantService/oe/panel/v1/referenceData/getAllResources
EnterpriseValidatePan=POST:${base_url}/MerchantService/oe/panel/v1/validate/pan
EnterpriseGetCategorySubCategory=GET:${base_url}/MerchantService/v2/category
EnterpriseGetSubcategory=GET:${base_url}/MerchantService/v2/category/${category}/subcategory
FetchGstFromPan=POST:${base_url}/MerchantService/panel/v1/solution/gstListFromPan
VerifyGST=POST:${base_url}/MerchantService/panel/v1/solution/verify/enterprise_merchant_business
EnterpriseFetchPinCodeDetails=GET:${base_url}/MerchantService/oe/panel/v2/pincodeDetail/${pincode}
EnterpriseUpdateBusinessLead=POST:${base_url}/MerchantService/panel/v1/solution/createUpdateLead
FetchBusinessLead=GET:${base_url}/MerchantService/panel/v1/solution/lead
EnterpriseFetchBasicInfo=GET:${base_url}/MerchantService/panel/v1/solution/lead/fetchBasicInfo
EnterpriseFetchMerchantBanks=GET:${base_url}/MerchantService/panel/v1/solution/fetchMerchantBanks
FetchBankDetailsEnterprise=GET:${base_url}/MerchantService/panel/v1/solution/v2/banks/${ifsc}
EnterprisePennyDrop=POST:${base_url}/MerchantService/panel/v1/solution/pennydrop
EnterpriseSolutionInstruments=POST:${base_url}/MerchantService/panel/v1/solution/instruments
EnterpriseCreateSolutionLead=POST:${base_url}/MerchantService/panel/v1/solution/createUpdateLead
EnterpriseUpdateSolutionLead=POST:${base_url}/MerchantService/panel/v1/solution/createUpdateLead
EnterpriseDocumentStatus=GET:${base_url}/MerchantService/panel/v1/business/doc/status
UploadDocumentsEnterprise=POST:${base_url}/MerchantService/oe/panel/v1/document/multiPart
EnterpriseQC=POST:${base_url}/MerchantService/oe/panel/v1/editLead/${leadId}
FetchQCDetails=GET:${base_url}/MerchantService/oe/panel/v1/lead/${leadId}
EnterpriseCommercial=POST:${base_url}/MerchantService/oe/panel/v1/editLead/${leadId}
ReAllocateAgent=POST:${base_url}/MerchantService/oe/panel/v1/reAllocateAgent





