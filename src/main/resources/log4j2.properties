# Root logger level and appenders
rootLogger.level = INFO
rootLogger.appenderRefs = stdout, logfile
rootLogger.appenderRef.stdout.ref = Console
rootLogger.appenderRef.logfile.ref = File

# Console appender configuration
appender.console.type = Console
appender.console.name = Console
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %d %p [%c] - %m%n

# File appender configuration
appender.file.type = File
appender.file.name = File
appender.file.fileName = target/spring.log
appender.file.layout.type = PatternLayout
appender.file.layout.pattern = %d %p [%c] - %m%n
