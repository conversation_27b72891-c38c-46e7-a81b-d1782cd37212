SET FOREIGN_KEY_CHECKS=0;
DELETE FROM agent_lead_allocation_mapper where ubm_id in (${ubmId});
DELETE FROM ubm_owner_ownership_type where user_business_mapping_owner_id in (SELECT ubmo.id FROM user_business_mapping ubm, user_business_mapping_owner ubmo WHERE ubmo.user_business_mapping_id = ubm.id AND ubm.id in (${ubmId}));
DELETE FROM user_workflow_status where user_business_mapping_owner_id in (SELECT ubmo.id FROM user_business_mapping ubm, user_business_mapping_owner ubmo WHERE ubmo.user_business_mapping_id = ubm.id AND ubm.id in (${ubmId}));
DELETE FROM user_business_mapping_owner where user_business_mapping_id in (${ubmId});
DELETE FROM workflow_status_additional_info WHERE workflow_status_id in (SELECT wfs.id FROM user_business_mapping ubm, workflow_status wfs WHERE wfs.user_business_mapping_id = ubm.id AND ubm.id in (${ubmId}));
DELETE from field_rejection_reason where workflow_status_id in (SELECT id FROM workflow_status WHERE user_business_mapping_id in (${ubmId}));
DELETE from workflow_agent_audit_trail where workflow_status_id in (SELECT id FROM workflow_status WHERE user_business_mapping_id in (${ubmId}));
DELETE FROM audit_event_details WHERE audit_event_id in (SELECT id from audit_event where workflow_status_id in (SELECT id FROM workflow_status WHERE user_business_mapping_id in (${ubmId})));
DELETE from audit_event where workflow_status_id in (SELECT id FROM workflow_status WHERE user_business_mapping_id in (${ubmId}));
DELETE FROM workflow_status WHERE user_business_mapping_id in (${ubmId});
DELETE from job_additional_info where job_id in (SELECT id from job where context_val in (${ubmId}));
DELETE from job where context_val in (${ubmId});
DELETE FROM user_business_mapping where id in (${ubmId});