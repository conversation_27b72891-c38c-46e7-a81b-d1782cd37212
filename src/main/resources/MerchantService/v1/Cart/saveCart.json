{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"status": {"type": "boolean"}, "displayMessage": {"type": "string"}, "errorCode": {"type": "string"}, "cartId": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"planId": {"type": "string"}, "quantity": {"type": "integer"}, "addOns": {"type": "object", "properties": {"amc": {"type": "boolean"}, "emiRental": {"type": "boolean"}}}}, "required": ["planId", "quantity"]}}}, "required": ["status"]}