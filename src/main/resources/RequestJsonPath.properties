CreateLeadForAxis=MerchantService/V1/workflow/lead/Axis/CreateLeadRequestforAxis.json
CreateLeadForFibe=MerchantService/V1/workflow/lead/Fibe/CreateLeadFibe.json
OccupationDetailsForFibe=MerchantService/V1/workflow/lead/Fibe/OccupationDetails.json
AsyncBureau=MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json
MockOfferDetailsFibe={\"baseId\":\"PL_FIBE_OE_Test_7d1502bb\",\"bureauKicker\":false,\"bureauThick\":0,\"field_investigation_needed\":false,\"flow\":\"RISK\",\"isBSAOffer\":false,\"isBre2Required\":false,\"isPaytmVintage\":true,\"loanDownGradable\":false,\"loan_offered\":true,\"newOfferGenerated\":true,\"offerId\":\"PL_FIBE_OE_Test_7d1502bb\",\"paytmThick\":0,\"productId\":\"214\",\"productVersion\":1,\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\"}
LISCreateApplicationRequest=MerchantService/V1/workflow/lead/Fibe/LISCreateApplicationRequest.json
LoanOfferAcceptanceRequest=MerchantService/V1/workflow/lead/Fibe/LoanOfferAcceptanceRequest.json
SearchByPanRequest=MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json
SearchByAadharRequest=MerchantService/V1/workflow/lead/Initiate/KYC/SearchByAAdhaarRequest.json
AdditionalDataCaptured=MerchantService/V1/workflow/lead/Fibe/AdditionalDataCaptured.json
PennyDropRequest=MerchantService/V1/workflow/lead/Fibe/PennyDropRequest.json
EmandateRequest=MerchantService/V1/workflow/lead/Fibe/MandateCallbackRequest.json
LoanAgreementRequest=MerchantService/V1/workflow/lead/Fibe/AcceptLoanAgreementRequest.json
LenderApplicatedUpdatedWorkflowOperation=LENDER_APPLICATION_UPDATED
LISDataUpdateCallback=MerchantService/V1/workflow/lead/Fibe/LISDataUpdateCallbackRequest.json
CreateLeadForShriRam=MerchantService/V1/workflow/lead/ShriRam/CreateLeadRequest.json
CreateLeadForFullerton=MerchantService/V1/workflow/lead/CreateLeadLPBDMergeFullertonRequest.json
CreateLeadForMCASetMandate=MerchantService/V1/workflow/lead/SetMandate/SetMandateRequest.json
