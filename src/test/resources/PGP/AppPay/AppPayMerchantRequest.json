{
  "body": {
    "authMode": "USRPWD",
    "channel": "WAP",
    <#if customerId??>"customerId": "${customerId}", </#if>
    "deviceId": "359211091044016",
    "extendInfo": {
      "udf1": "",
      "additionalInfo": "orderAlreadyCreated:true",
      "merchantUniqueReference": "200071927999"
    },
    "industryType": "Retail",
    <#if orderId??>"orderId": "${orderId}", </#if>
    "paymentMode": "PPI",
    "reqType": "CLW_APP_PAY",
    <#if txnAmount??>"txnAmount": "${txnAmount}" </#if>

  },
  "head": {
    "clientId": "market-app-staging",
    <#if mid??>"mid": "${mid}", </#if>
    "requestTimestamp": "1547109060",
    <#if token??>"token": "${token}", </#if>
    "tokenType": "SSO",
    "version": "v1"
  }
}