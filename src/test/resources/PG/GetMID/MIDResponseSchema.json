{"$schema": "http://json-schema.org/draft-04/schema#", "type": "array", "items": [{"type": "object", "properties": {"mid": {"type": "string"}, "uid": {"type": "string"}, "ppiLimit": {"type": "string"}, "merchantName": {"type": "string"}, "kybId": {"type": "string"}, "businessType": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "businessName": {"type": "string"}, "blockComment": {"type": "string"}, "requestType": {"type": "string"}, "status": {"type": "string"}, "aggregator": {"type": "boolean"}, "payModes": {"type": "array", "items": {}}, "payModeDetails": {"type": "array", "items": [{"type": "object", "properties": {"payMode": {"type": "string"}, "status": {"type": "string"}, "selfAbuser": {"type": "boolean"}, "selfAbuserComm": {"type": "string"}}, "required": ["payMode", "status", "selfAbuser", "selfAbuserComm"]}, {"type": "object", "properties": {"payMode": {"type": "string"}, "status": {"type": "string"}, "selfAbuser": {"type": "boolean"}, "selfAbuserComm": {"type": "string"}}, "required": ["payMode", "status", "selfAbuser", "selfAbuserComm"]}, {"type": "object", "properties": {"payMode": {"type": "string"}, "status": {"type": "string"}, "selfAbuser": {"type": "boolean"}, "selfAbuserComm": {"type": "string"}}, "required": ["payMode", "status", "selfAbuser", "selfAbuserComm"]}, {"type": "object", "properties": {"payMode": {"type": "string"}, "status": {"type": "string"}, "selfAbuser": {"type": "boolean"}, "selfAbuserComm": {"type": "string"}}, "required": ["payMode", "status", "selfAbuser", "selfAbuserComm"]}, {"type": "object", "properties": {"payMode": {"type": "string"}, "status": {"type": "string"}, "selfAbuser": {"type": "boolean"}, "selfAbuserComm": {"type": "string"}}, "required": ["payMode", "status", "selfAbuser", "selfAbuserComm"]}, {"type": "object", "properties": {"payMode": {"type": "string"}, "status": {"type": "string"}, "selfAbuser": {"type": "boolean"}, "selfAbuserComm": {"type": "string"}}, "required": ["payMode", "status", "selfAbuser", "selfAbuserComm"]}]}, "apiEnable": {"type": "boolean"}, "emiAllowed": {"type": "boolean"}, "requestTypeNames": {"type": "array", "items": [{"type": "string"}]}, "isMigrated": {"type": "boolean"}, "kycDetails": {"type": "object", "properties": {"bankAccountNo": {"type": "string"}, "businessIfscNo": {"type": "string"}, "bankAccountHolderName": {"type": "string"}, "bankName": {"type": "string"}}, "required": ["bankAccountNo", "businessIfscNo", "bankAccountHolderName", "bankName"]}, "velocities": {"type": "array", "items": [{"type": "object", "properties": {"maxAmtPerDay": {"type": "string"}, "maxAmtPerMon": {"type": "string"}, "velocityType": {"type": "string"}}, "required": ["maxAmtPerDay", "maxAmtPerMon", "velocityType"]}]}, "commissionDetails": {"type": "array", "items": [{"type": "object", "properties": {"commissionType": {"type": "string"}, "feeType": {"type": "string"}, "isDefault": {"type": "string"}, "commissions": {"type": "array", "items": [{"type": "object", "properties": {"percentCommission": {"type": "string"}, "commissionTypeBoth": {"type": "boolean"}}, "required": ["percentCommission", "commissionTypeBoth"]}]}}, "required": ["commissionType", "feeType", "isDefault", "commissions"]}]}, "businessDetails": {"type": "object", "properties": {"category": {"type": "string"}, "subCategory": {"type": "string"}, "commAddress": {"type": "object", "properties": {"address1": {"type": "string"}, "address2": {"type": "string"}, "address3": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zipcode": {"type": "string"}}, "required": ["address1", "address2", "address3", "city", "state", "zipcode"]}}, "required": ["category", "subCategory", "commAddress"]}, "settlementType": {"type": "string"}, "payoutDays": {"type": "integer"}, "edcStatus": {"type": "object", "properties": {"edcEligible": {"type": "boolean"}}, "required": ["edcEligible"]}, "callBackStatus": {"type": "string"}, "isSystemAbuser": {"type": "boolean"}, "isSelfAbuser": {"type": "boolean"}, "instantActivation": {"type": "boolean"}, "merchantOnline": {"type": "boolean"}}, "required": ["mid", "uid", "ppiLimit", "merchantName", "kybId", "businessType", "firstName", "lastName", "businessName", "blockComment", "requestType", "status", "aggregator", "payModes", "payModeDetails", "apiEnable", "emiAllowed", "requestTypeNames", "isMigrated", "kycDetails", "velocities", "commissionDetails", "businessDetails", "settlementType", "payoutDays", "edcStatus", "callBackStatus", "isSystemAbuser", "isSelfAbuser", "instantActivation", "merchantOnline"]}]}