{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "stage": {"type": "string"}, "creditScore": {"type": "number"}, "lastFetchDate": {"type": "integer"}, "loanOffered": {"type": "boolean"}, "maxLoanAmount": {"type": "string"}, "minLoanAmount": {"type": "string"}, "bureau": {"type": "string"}}, "required": ["refId", "statusCode", "stage", "creditScore", "lastFetchDate", "loanOffered", "maxLoanAmount", "minLoanAmount", "bureau"]}