{
  "status": "active",
  <#if referenceType??>"referenceType": "${referenceType}",</#if>
  <#if referenceValue??>"referenceValue": "${referenceValue}",</#if>

  "empCode": "32984",
  <#if tags??>"tags": ["${tags}"],</#if>
  "tasks": "guiggy",
  "department": "EDC Retail",
  "team": "EDC Retail",
  "subteam": "Field Sales",
<#if product??>"product": "${product}",</#if>
<#if typeOfProductModel??>"typeOfProductModel": "${typeOfProductModel}",</#if>
  "languageOfProduct": "English",
  "reasonForService": "test_reason",
<#if freshdeskTicketNumber??>"freshdeskTicketNumber": "${freshdeskTicketNumber}",</#if>
  "created_at": "2023-08-09T08:19:47Z",
  "cfAlternateNumber": "**********",
  "meta": {
    "deactivateChannels":true,
    <#if mbid??>"mbid": "${mbid}",</#if>
    "businessKeyAccount": "9210",
    "onboardingleadid": "ushahakaka-67338"
  },
  <#if requestType??>"requestType": "${requestType}",</#if>
  <#if deviceCount??>"deviceCount": "${deviceCount}",</#if>
  <#if acquirer??>"acquirer": "${acquirer}",</#if>
  <#if tids??>"tids": "${tids}",</#if>
  "address": {
    "line1": "b-84,sector 22",
    "line2": "sector 22",
    "line3": "line 3 part",
    "country": "India",
    "city": "Delhi",
    "postalCode": "110089",
    "state": "Delhi",
    "landmark": "income tax"
  }
}