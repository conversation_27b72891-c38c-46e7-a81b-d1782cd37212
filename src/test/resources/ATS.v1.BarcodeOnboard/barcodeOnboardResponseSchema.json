{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"message": {"type": "string"}, "catSubList": {"type": "array", "items": [{"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}, {"type": "object", "properties": {"key": {"type": "integer"}, "value": {"type": "string"}}, "required": ["key", "value"]}]}}, "required": ["message", "catSubList"]}