{"createMerReq": {"CREATED_BY": "sobeer_sales", "ACTION": "Submit for Approval", "MERCHANT_DETAILS": {"REQUEST_ID": "*****************", "USER_NAME": "*****************", "ACCOUNT_FOR": "unifiedMerchantPanel", "SOURCE_ID": "OE", "MERCHANT_TYPE": "50K", "OFFLINE_ENABLED": "FALSE", "PPI_LIMITED_MERCHANT": "1", "BUSINESS_NAME": "AASHIT SHARMA", "BUSINESS_TYPE": "PROPRIETORSHIP", "CALLBACK_URL_ENABLED": "TRUE", "CUSTOM": "SYSTEM GENERATED", "MERCHANT_NAME": "<PERSON><PERSON><PERSON>", "CURRENCY": "INR", "REFUND_TO_BANK_ENABLED": "TRUE", "STORE_CARD_DETAILS": "NO", "ADD_MONEY_ENABLE": "TRUE", "CHECKSUM_ENABLED": "TRUE", "NUMBER_OF_RETRY": "1", "CATEGORY": "Entertainment", "SUB_CATEGORY": "Game Parlour", "INDUSTRY_TYPE": "Retail", "WALLET_RECHARGE_OPT": "MANUAL_RECHARGE", "PROFILE_ID": "1", "EMAIL_ALERT": "TRUE", "CONVENIENCE_FEE_TYPE": "1", "VALID_FROM": "08/11/2020", "VALID_TO": "08/10/2023", "MULTI_SUPPORT": "YES", "HOW_MANY": "3", "OCP": "TRUE", "REQUEST_NAME": "DP2Web", "FIRST_NAME": "AASHIT", "LAST_NAME": "SHARMA", "MOBILE_NUMBER": "**********", "PHONE_NUMBER": "**********", "MerchUniqRef": "X", "ACCOUNT_PRIMARY": "FALSE", "CAN_EDIT_PMOBILE": "TRUE", "IS_SUB_USER": "FALSE", "ADDRESS1": "Registered  H  AIHHMS", "ADDRESS2": "Registered  H  AIHHMS", "ADDRESS3": "Registered  H  AIHHMS", "COUNTRY": "India", "STATE": "Uttar Pradesh", "CITY": "<PERSON><PERSON>am Buddha Nagar", "PIN": "201301", "SAME_AS_BUSINESS_ADDR": "FALSE", "COMMUNICATION_ADDRESS1": "Correspondence  H  AIHHMS", "COMMUNICATION_ADDRESS2": "Correspondence  H  AIHHMS", "COMMUNICATION_ADDRESS3": "Correspondence  H  AIHHMS", "COMMUNICATION_COUNTRY": "India", "COMMUNICATION_STATE": "Uttar Pradesh", "COMMUNICATION_CITY": "<PERSON><PERSON>am Buddha Nagar", "COMMUNICATION_PIN": "201301", "ADDRESS_DETAILS": [{"TYPE": "BILLING", "DETAILS": [{"ADDRESS1": "Billing  H  AIHHMS", "ADDRESS2": "Billing  H  AIHHMS", "ADDRESS3": "Billing  H  AIHHMS", "COUNTRY": "India", "STATE": "Maharashtra", "CITY": "Mumbai", "PIN": "400037", "GSTIN": null}]}], "KYC_BANK_NAME": "INDUSIND BANK", "KYC_BANK_ACCOUNT_HOLDER_NAME": "AASHIT SHARMA", "KYC_BANK_ACCOUNT_NO": "***************", "KYC_BUSINESS_PAN_NO": "**********", "KYC_AUTHORIZED_SIGNATORY_PAN_NO": "**********", "KYC_BUSINESS_IFSC_NO": "INDB0000487", "KYC_AUTHORIZED_SIGNATORY_NAME": "AASHIT SHARMA", "KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO": "**********", "COMM_STAT_SELECT": "1", "EMAIL_MERCHANT": "TRUE", "EMAIL_CONSUMER": "FALSE", "REQUEST_TYPE_NAME": "QR_ORDER,SELF_DECLARED_MERCHANT,EDC", "WEBSITE_NAME": "paytm", "SIZE_OF_KEY": "16", "SMS_MERCHANT": "TRUE", "PAYOUT_DAYS": "1", "ONLINE_SETTLEMENT": "FALSE", "FLAG_MERCHANT": "FALSE", "BW_ENABLED": "TRUE", "BW_CONFIG": {"TRANSFER_MODE": "M2B", "AUTO": "TRUE", "TRIGGER_MODE": "TIME_INTERVAL", "TRIGGER_VALUE": "1"}, "API_DISABLED": "TRUE", "MERCHANT_INDUSTRY_TYPE": "BIG", "P2M_ENABLED": "TRUE", "OB_CHANNEL": "GG_APP"}, "URL_DETAILS": [{"WEBSITE_NAME": "dp2wen", "REQUEST_URL": "https://secure.paytm.in/MerchantSite/bankResponse", "RESPONSE_URL": "https://secure.paytm.in/oltp-web/smsInvoiceAddMoney/displayPaymentStatus?ORDER_ID=m1", "PEON_URL": "http://dp2web.com", "IMAGE_NAME": "paytm_log"}, {"WEBSITE_NAME": "paytm", "REQUEST_URL": "https://www.paytm.com", "RESPONSE_URL": "https://cart-beta.paytm.com/payment/status", "IMAGE_NAME": "paytm_log"}, {"WEBSITE_NAME": "retail", "REQUEST_URL": "https://www.paytm.com", "RESPONSE_URL": "https://cart-beta.paytm.com/payment/status", "IMAGE_NAME": "paytm_log"}], "DOCS_DETAILS": {"DETAILED_LIST": ["PAN CARD"]}}, "configVelocity": {"VELOCITIES": [{"VELOCITY_TYPE": "PER_MID", "VELOCITY_DETAILS": {"MAX_AMT_PER_DAY": "100", "MAX_AMT_PER_MONTH": "100"}}]}, "configureMbidAndInstrument": {"configureMerchantCommission": {"ACTION": "EDIT", "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "0", "COMMISSION_TYPE_BOTH": "FALSE"}}, "TXN_TYPES": [{"TXN_TYPE": "EDC", "PAY_MODES": [{"PAY_MODE": "DC", "COMMISSION": {"FEE_TYPE": "TransAmountslap", "PERCENT_COMMISSION": "0.9", "COMMISSION_TYPE_BOTH": "FALSE", "SLAB_1_START_RANGE": "0", "SLAB_1_END_RANGE": "2000", "SLAB_1_PERCENT_COMMISSION": "0.4", "SLAB_1_COMMISSION_TYPE_BOTH": "FALSE", "SLAB_2_START_RANGE": "2000", "SLAB_2_END_RANGE": "-1", "SLAB_2_PERCENT_COMMISSION": "0.9", "SLAB_2_COMMISSION_TYPE_BOTH": "FALSE"}, "BANKS": []}, {"PAY_MODE": "CC", "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "1.25", "COMMISSION_TYPE_BOTH": "FALSE"}, "BANKS": []}]}, {"TXN_TYPE": "Payments", "PAY_MODES": [{"PAY_MODE": "PPI", "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "0", "COMMISSION_TYPE_BOTH": "FALSE"}, "BANKS": []}, {"PAY_MODE": "UPI", "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "0", "COMMISSION_TYPE_BOTH": "FALSE"}, "BANKS": []}, {"PAY_MODE": "NB", "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "0", "COMMISSION_TYPE_BOTH": "FALSE"}, "BANKS": []}]}]}, "CUST_ID": "**********"}