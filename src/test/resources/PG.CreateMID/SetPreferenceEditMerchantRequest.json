{"createMerReq": {"CREATED_BY": "sobeer_sales", "MERCHANT_DETAILS": {"MID": "qvuVIX65993859208748", "REQUEST_ID": "*****************", "ACCOUNT_FOR": "unifiedMerchantPanel", "SOURCE_ID": "OE", "REQUEST_NAME": "DP2Web", "REQUEST_TYPE_NAME": "QR_ORDER,EDC", "MERCHANT_INDUSTRY_TYPE": "BIG", "STATIC_PREF": ["EDC_Retail_Send_Communications", "TIERED_ACCUMULATION_EMI_CC_DC", "EDC_VOID"], "EMI_OFFER": "Y"}}, "configureMbidAndInstrument": {"TXN_TYPES": [{"TXN_TYPE": "EDC", "PAY_MODES": [{"PAY_MODE": "CC", "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "1.75", "COMMISSION_TYPE_BOTH": "FALSE"}, "FEE_RATE_FACTORS": [{"FEE_LEVEL": {"CARD_SCHEME": "AMEX"}, "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "2.6", "COMMISSION_TYPE_BOTH": "FALSE"}}, {"FEE_LEVEL": {"IS_CORP_CARD": "TRUE"}, "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "2.75", "COMMISSION_TYPE_BOTH": "FALSE"}}, {"FEE_LEVEL": {"CARD_SCHEME": "DINERS"}, "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "2.99", "COMMISSION_TYPE_BOTH": "FALSE"}}]}, {"PAY_MODE": "DC", "COMMISSION": {"FEE_TYPE": "TransAmountslap", "COMMISSION_TYPE_BOTH": "FALSE", "SLAB_1_START_RANGE": "0", "SLAB_1_END_RANGE": "2000", "SLAB_1_PERCENT_COMMISSION": "0.4", "SLAB_2_START_RANGE": "2000", "SLAB_2_END_RANGE": "-1", "SLAB_2_PERCENT_COMMISSION": "0.9"}, "FEE_RATE_FACTORS": [{"FEE_LEVEL": {"IS_PREPAID": "TRUE"}, "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "2.2", "COMMISSION_TYPE_BOTH": "FALSE"}}]}, {"PAY_MODE": "EMI", "COMMISSION": {"FEE_TYPE": "simple", "PERCENT_COMMISSION": "0.5", "COMMISSION_TYPE_BOTH": "FALSE"}}, {"PAY_MODE": "EMI_DC", "COMMISSION": {"FEE_TYPE": "TransAmountslap", "COMMISSION_TYPE_BOTH": "FALSE", "SLAB_1_START_RANGE": "0", "SLAB_1_END_RANGE": "2000", "SLAB_1_PERCENT_COMMISSION": "0.5", "SLAB_2_START_RANGE": "2000", "SLAB_2_END_RANGE": "-1", "SLAB_2_PERCENT_COMMISSION": "0.5"}}]}]}, "CUST_ID": "1700059004"}