{
  "createMerReq": {
    "CREATED_BY": "sobeer_sales",
    "ACTION": "Submit for Approval",
    "MERCHANT_DETAILS": {
      <#if REQUEST_ID??>"REQUEST_ID": "${REQUEST_ID}",</#if>
      <#if USER_NAME??>"USER_NAME": "${USER_NAME}",</#if>
      "ACCOUNT_FOR": "unifiedMerchantPanel",
      "SOURCE_ID": "OE",
      "MERCHANT_TYPE": "50K",
      "OFFLINE_ENABLED": "FALSE",
      "PPI_LIMITED_MERCHANT": "1",
      <#if MERCHANT_NAME??>"BUSINESS_NAME": "${MERCHANT_NAME}",</#if>
      <#if BUSINESS_TYPE??>"BUSINESS_TYPE": "${BUSINESS_TYPE}",</#if>
      "CALLBACK_URL_ENABLED": "TRUE",
      "CUSTOM": "SYSTEM GENERATED",
      <#if MERCHANT_NAME??>"MERCHANT_NAME": "${MERCHANT_NAME}",</#if>
      "CURRENCY": "INR",
      "REFUND_TO_BANK_ENABLED": "TRUE",
      "STORE_CARD_DETAILS": "NO",
      "ADD_MONEY_ENABLE": "TRUE",
      "CHECKSUM_ENABLED": "TRUE",
      "NUMBER_OF_RETRY": "1",
      "CATEGORY": "Entertainment",
      "SUB_CATEGORY": "Game Parlour",
      "INDUSTRY_TYPE": "Retail",
      "WALLET_RECHARGE_OPT": "MANUAL_RECHARGE",
      "PROFILE_ID": "1",
      "EMAIL_ALERT": "TRUE",
      <#if KYB_ID??>"KYB_ID": "${KYB_ID}",</#if>
      "CONVENIENCE_FEE_TYPE": "1",
      "VALID_FROM": "09/30/2020",
      "VALID_TO": "09/29/2023",
      "MULTI_SUPPORT": "YES",
      "HOW_MANY": "3",
      "OCP": "TRUE",
      "REQUEST_NAME": "DP2Web",
      "FIRST_NAME": "AASHIT",
      "LAST_NAME": "PAYTM",
      <#if USER_NAME??>"MOBILE_NUMBER": "${USER_NAME}",</#if>
      <#if USER_NAME??>"PHONE_NUMBER": "${USER_NAME}",</#if>
      "MerchUniqRef": "X",
      "ACCOUNT_PRIMARY": "FALSE",
      "CAN_EDIT_PMOBILE": "TRUE",
      "IS_SUB_USER": "FALSE",
      "COUNTRY": "India",
      "SAME_AS_BUSINESS_ADDR": "TRUE",
      "COMMUNICATION_COUNTRY": "India",
      <#if PAN??>"KYC_BUSINESS_PAN_NO": "${PAN}",</#if>
      <#if PAN??>"KYC_AUTHORIZED_SIGNATORY_PAN_NO": "${PAN}",</#if>
      "KYC_BANK_NAME": "INDB Bank",
      <#if MERCHANT_NAME??>"KYC_BANK_ACCOUNT_HOLDER_NAME": "${MERCHANT_NAME}",</#if>
      <#if USER_NAME??>"KYC_BANK_ACCOUNT_NO": "${USER_NAME}",</#if>
      "KYC_BUSINESS_IFSC_NO": "INDB0000499",
      "KYC_AUTHORIZED_SIGNATORY_NAME": "AASHIT SHARMA PAYTM",
      "COMM_STAT_SELECT": "1",
      "EMAIL_MERCHANT": "TRUE",
      "EMAIL_CONSUMER": "FALSE",
      "REQUEST_TYPE_NAME": "SELF_DECLARED_MERCHANT,LINK_BASED_PAYMENT",
      "WEBSITE_NAME": "paytm",
      "SIZE_OF_KEY": "16",
      "SMS_MERCHANT": "TRUE",
      "PAYOUT_DAYS": "1",
      "ONLINE_SETTLEMENT": "TRUE",
      "FLAG_MERCHANT": "FALSE",
      "API_DISABLED": "TRUE",
      "P2M_ENABLED": "TRUE",
      "SOLUTION_TYPE": "OFFLINE",
      "OB_CHANNEL": "DIY_P4B_APP"
    },
    "URL_DETAILS": [{
      "WEBSITE_NAME": "dp2wen",
      "REQUEST_URL": "https://secure.paytm.in/MerchantSite/bankResponse",
      "RESPONSE_URL": "https://secure.paytm.in/oltp-web/smsInvoiceAddMoney/displayPaymentStatus?ORDER_ID=m1",
      "PEON_URL": "http://dp2web.com",
      "IMAGE_NAME": "paytm_log"
    }, {
      "WEBSITE_NAME": "paytm",
      "REQUEST_URL": "https://www.paytm.com",
      "RESPONSE_URL": "https://cart-beta.paytm.com/payment/status",
      "IMAGE_NAME": "paytm_log"
    }, {
      "WEBSITE_NAME": "retail",
      "REQUEST_URL": "https://www.paytm.com",
      "RESPONSE_URL": "https://cart-beta.paytm.com/payment/status",
      "IMAGE_NAME": "paytm_log"
    }],
    "DOCS_DETAILS": {
      "DETAILED_LIST": ["PAN CARD"]
    }
  },
  "configVelocity": {
    "VELOCITIES": [{
      "VELOCITY_TYPE": "PER_MID",
      "VELOCITY_DETAILS": {
        "MAX_AMT_PER_DAY": "10000",
        "MAX_AMT_PER_MONTH": "20000"
      }
    }]
  },
  "configureMbidAndInstrument": {
    "configureMerchantCommission": {
      "ACTION": "EDIT",
      "COMMISSION": {
        "FEE_TYPE": "simple",
        "PERCENT_COMMISSION": "0",
        "COMMISSION_TYPE_BOTH": "FALSE"
      }
    },
    "TXN_TYPES": [{
      "TXN_TYPE": "Payments",
      "PAY_MODES": [{
        "PAY_MODE": "UPI",
        "COMMISSION": {
          "FEE_TYPE": "simple",
          "PERCENT_COMMISSION": "0",
          "COMMISSION_TYPE_BOTH": "FALSE"
        },
        "BANKS": []
      },
        {
          "PAY_MODE": "PPI",
          "COMMISSION": {
            "FEE_TYPE": "simple",
            "PERCENT_COMMISSION": "1",
            "COMMISSION_TYPE_BOTH": "FALSE"
          },
          "BANKS": []
        }
      ]
    }]
  },
  <#if CUST_ID??>"CUST_ID": "${CUST_ID}"</#if>
}
