{
  <#if businessTierPref??>"businessTierPref": "${businessTierPref}",</#if>
  "commissions": [
    {
      <#if transactionType??>"transactionType": "${transactionType}",</#if>
      <#if paymode??>"paymode": "${paymode}",</#if>

      "feeLevel": {
        "EDC": "TRUE"
      },
      <#if isTiered??>"isTiered": "${isTiered}",</#if>
      "commissionInfo": [
        {
          "tieredStart": "0",
          "tieredEnd": "70000",
          "commissionDetails": {
            "feeType": "SIMPLE",
            "commissions": [
              {
                "percentCommission": "0.2",
                <#if convenienceModel??>"convenienceModel": "${convenienceModel}",</#if>
                "commissionTypeBoth": false
              }
            ]
          }
        },
        {
          "tieredStart": "70000",
          "tieredEnd": "-1",
          "commissionDetails": {
            "feeType": "SIMPLE",
            "commissions": [
              {
                "percentCommission": "1.0",
                "convenienceModel": "PCF",
                "commissionTypeBoth": false
              }
            ]
          }
        }
      ]
    },
    {
      "transactionType": "Payments",
      "paymode": "PAYTM_DIGITAL_CREDIT",
      "feeLevel": {},
      "isTiered": true,
      "commissionInfo": [
        {
          "tieredStart": "0",
          "tieredEnd": "70000",
          "commissionDetails": {
            "feeType": "SIMPLE",
            "commissions": [
              {
                "percentCommission": "0.7",
                "convenienceModel": "PCF",
                "commissionTypeBoth": false
              }
            ]
          }
        },
        {
          "tieredStart": "70000",
          "tieredEnd": "-1",
          "commissionDetails": {
            "feeType": "SIMPLE",
            "commissions": [
              {
                "percentCommission": "2.2",
                "convenienceModel": "PCF",
                "commissionTypeBoth": false
              }
            ]
          }
        }
      ]
    },
    {
      "transactionType": "Payments",
      "paymode": "EMI",
      "feeLevel": {
        "EDC": "TRUE"
      },
      "isTiered": true,
      "emiOffer": true,
      "commissionInfo": [
        {
          "tieredStart": "0",
          "tieredEnd": "70000",
          "commissionDetails": {
            "feeType": "SIMPLE",
            "commissions": [
              {
                "percentCommission": "0.5",
                "convenienceModel": "PCF",
                "commissionTypeBoth": false
              }
            ]
          }
        },
        {
          "tieredStart": "70000",
          "tieredEnd": "-1",
          "commissionDetails": {
            "feeType": "SIMPLE",
            "commissions": [
              {
                "percentCommission": "0.5",
                "convenienceModel": "PCF",
                "commissionTypeBoth": false
              }
            ]
          }
        }
      ]
    }
  ]
}