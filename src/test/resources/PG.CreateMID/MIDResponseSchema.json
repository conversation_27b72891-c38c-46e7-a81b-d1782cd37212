{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"createMerReq": {"type": "object", "properties": {"CREATED_BY": {"type": "string"}, "ACTION": {"type": "string"}, "MERCHANT_DETAILS": {"type": "object", "properties": {"REQUEST_ID": {"type": "string"}, "USER_NAME": {"type": "string"}, "ACCOUNT_FOR": {"type": "string"}, "SOURCE_ID": {"type": "string"}, "MERCHANT_TYPE": {"type": "string"}, "OFFLINE_ENABLED": {"type": "string"}, "PPI_LIMITED_MERCHANT": {"type": "string"}, "BUSINESS_NAME": {"type": "string"}, "BUSINESS_TYPE": {"type": "string"}, "CALLBACK_URL_ENABLED": {"type": "string"}, "CUSTOM": {"type": "string"}, "MERCHANT_NAME": {"type": "string"}, "CURRENCY": {"type": "string"}, "REFUND_TO_BANK_ENABLED": {"type": "string"}, "STORE_CARD_DETAILS": {"type": "string"}, "ADD_MONEY_ENABLE": {"type": "string"}, "CHECKSUM_ENABLED": {"type": "string"}, "NUMBER_OF_RETRY": {"type": "string"}, "CATEGORY": {"type": "string"}, "SUB_CATEGORY": {"type": "string"}, "INDUSTRY_TYPE": {"type": "string"}, "WALLET_RECHARGE_OPT": {"type": "string"}, "PROFILE_ID": {"type": "string"}, "EMAIL_ALERT": {"type": "string"}, "CONVENIENCE_FEE_TYPE": {"type": "string"}, "VALID_FROM": {"type": "string"}, "VALID_TO": {"type": "string"}, "MULTI_SUPPORT": {"type": "string"}, "HOW_MANY": {"type": "string"}, "OCP": {"type": "string"}, "REQUEST_NAME": {"type": "string"}, "FIRST_NAME": {"type": "string"}, "LAST_NAME": {"type": "string"}, "MOBILE_NUMBER": {"type": "string"}, "PHONE_NUMBER": {"type": "string"}, "MerchUniqRef": {"type": "string"}, "ACCOUNT_PRIMARY": {"type": "string"}, "CAN_EDIT_PMOBILE": {"type": "string"}, "IS_SUB_USER": {"type": "string"}, "ADDRESS1": {"type": "string"}, "ADDRESS2": {"type": "string"}, "ADDRESS3": {"type": "string"}, "COUNTRY": {"type": "string"}, "STATE": {"type": "string"}, "CITY": {"type": "string"}, "PIN": {"type": "string"}, "SAME_AS_BUSINESS_ADDR": {"type": "string"}, "COMMUNICATION_ADDRESS1": {"type": "string"}, "COMMUNICATION_ADDRESS2": {"type": "string"}, "COMMUNICATION_ADDRESS3": {"type": "string"}, "COMMUNICATION_COUNTRY": {"type": "string"}, "COMMUNICATION_STATE": {"type": "string"}, "COMMUNICATION_CITY": {"type": "string"}, "COMMUNICATION_PIN": {"type": "string"}, "ADDRESS_DETAILS": {"type": "array", "items": [{"type": "object", "properties": {"TYPE": {"type": "string"}, "DETAILS": {"type": "array", "items": [{"type": "object", "properties": {"ADDRESS1": {"type": "string"}, "ADDRESS2": {"type": "string"}, "ADDRESS3": {"type": "string"}, "COUNTRY": {"type": "string"}, "STATE": {"type": "string"}, "CITY": {"type": "string"}, "PIN": {"type": "string"}, "GSTIN": {"type": "null"}}, "required": ["ADDRESS1", "ADDRESS2", "ADDRESS3", "COUNTRY", "STATE", "CITY", "PIN", "GSTIN"]}]}}, "required": ["TYPE", "DETAILS"]}]}, "KYC_BANK_NAME": {"type": "string"}, "KYC_BANK_ACCOUNT_HOLDER_NAME": {"type": "string"}, "KYC_BANK_ACCOUNT_NO": {"type": "string"}, "KYC_BUSINESS_PAN_NO": {"type": "string"}, "KYC_AUTHORIZED_SIGNATORY_PAN_NO": {"type": "string"}, "KYC_BUSINESS_IFSC_NO": {"type": "string"}, "KYC_AUTHORIZED_SIGNATORY_NAME": {"type": "string"}, "KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO": {"type": "string"}, "COMM_STAT_SELECT": {"type": "string"}, "EMAIL_MERCHANT": {"type": "string"}, "EMAIL_CONSUMER": {"type": "string"}, "REQUEST_TYPE_NAME": {"type": "string"}, "WEBSITE_NAME": {"type": "string"}, "SIZE_OF_KEY": {"type": "string"}, "SMS_MERCHANT": {"type": "string"}, "PAYOUT_DAYS": {"type": "string"}, "ONLINE_SETTLEMENT": {"type": "string"}, "FLAG_MERCHANT": {"type": "string"}, "BW_ENABLED": {"type": "string"}, "BW_CONFIG": {"type": "object", "properties": {"TRANSFER_MODE": {"type": "string"}, "AUTO": {"type": "string"}, "TRIGGER_MODE": {"type": "string"}, "TRIGGER_VALUE": {"type": "string"}}, "required": ["TRANSFER_MODE", "AUTO", "TRIGGER_MODE", "TRIGGER_VALUE"]}, "API_DISABLED": {"type": "string"}, "MERCHANT_INDUSTRY_TYPE": {"type": "string"}, "P2M_ENABLED": {"type": "string"}, "OB_CHANNEL": {"type": "string"}}, "required": ["REQUEST_ID", "USER_NAME", "ACCOUNT_FOR", "SOURCE_ID", "MERCHANT_TYPE", "OFFLINE_ENABLED", "PPI_LIMITED_MERCHANT", "BUSINESS_NAME", "BUSINESS_TYPE", "CALLBACK_URL_ENABLED", "CUSTOM", "MERCHANT_NAME", "CURRENCY", "REFUND_TO_BANK_ENABLED", "STORE_CARD_DETAILS", "ADD_MONEY_ENABLE", "CHECKSUM_ENABLED", "NUMBER_OF_RETRY", "CATEGORY", "SUB_CATEGORY", "INDUSTRY_TYPE", "WALLET_RECHARGE_OPT", "PROFILE_ID", "EMAIL_ALERT", "CONVENIENCE_FEE_TYPE", "VALID_FROM", "VALID_TO", "MULTI_SUPPORT", "HOW_MANY", "OCP", "REQUEST_NAME", "FIRST_NAME", "LAST_NAME", "MOBILE_NUMBER", "PHONE_NUMBER", "MerchUniqRef", "ACCOUNT_PRIMARY", "CAN_EDIT_PMOBILE", "IS_SUB_USER", "ADDRESS1", "ADDRESS2", "ADDRESS3", "COUNTRY", "STATE", "CITY", "PIN", "SAME_AS_BUSINESS_ADDR", "COMMUNICATION_ADDRESS1", "COMMUNICATION_ADDRESS2", "COMMUNICATION_ADDRESS3", "COMMUNICATION_COUNTRY", "COMMUNICATION_STATE", "COMMUNICATION_CITY", "COMMUNICATION_PIN", "ADDRESS_DETAILS", "KYC_BANK_NAME", "KYC_BANK_ACCOUNT_HOLDER_NAME", "KYC_BANK_ACCOUNT_NO", "KYC_BUSINESS_PAN_NO", "KYC_AUTHORIZED_SIGNATORY_PAN_NO", "KYC_BUSINESS_IFSC_NO", "KYC_AUTHORIZED_SIGNATORY_NAME", "KYC_AUTHORIZED_SIGNATORY_ID_PROOF_NO", "COMM_STAT_SELECT", "EMAIL_MERCHANT", "EMAIL_CONSUMER", "REQUEST_TYPE_NAME", "WEBSITE_NAME", "SIZE_OF_KEY", "SMS_MERCHANT", "PAYOUT_DAYS", "ONLINE_SETTLEMENT", "FLAG_MERCHANT", "BW_ENABLED", "BW_CONFIG", "API_DISABLED", "MERCHANT_INDUSTRY_TYPE", "P2M_ENABLED", "OB_CHANNEL"]}, "URL_DETAILS": {"type": "array", "items": [{"type": "object", "properties": {"WEBSITE_NAME": {"type": "string"}, "REQUEST_URL": {"type": "string"}, "RESPONSE_URL": {"type": "string"}, "PEON_URL": {"type": "string"}, "IMAGE_NAME": {"type": "string"}}, "required": ["WEBSITE_NAME", "REQUEST_URL", "RESPONSE_URL", "PEON_URL", "IMAGE_NAME"]}, {"type": "object", "properties": {"WEBSITE_NAME": {"type": "string"}, "REQUEST_URL": {"type": "string"}, "RESPONSE_URL": {"type": "string"}, "IMAGE_NAME": {"type": "string"}}, "required": ["WEBSITE_NAME", "REQUEST_URL", "RESPONSE_URL", "IMAGE_NAME"]}, {"type": "object", "properties": {"WEBSITE_NAME": {"type": "string"}, "REQUEST_URL": {"type": "string"}, "RESPONSE_URL": {"type": "string"}, "IMAGE_NAME": {"type": "string"}}, "required": ["WEBSITE_NAME", "REQUEST_URL", "RESPONSE_URL", "IMAGE_NAME"]}]}, "DOCS_DETAILS": {"type": "object", "properties": {"DETAILED_LIST": {"type": "array", "items": [{"type": "string"}]}}, "required": ["DETAILED_LIST"]}}, "required": ["CREATED_BY", "ACTION", "MERCHANT_DETAILS", "URL_DETAILS", "DOCS_DETAILS"]}, "configVelocity": {"type": "object", "properties": {"VELOCITIES": {"type": "array", "items": [{"type": "object", "properties": {"VELOCITY_TYPE": {"type": "string"}, "VELOCITY_DETAILS": {"type": "object", "properties": {"MAX_AMT_PER_DAY": {"type": "string"}, "MAX_AMT_PER_MONTH": {"type": "string"}}, "required": ["MAX_AMT_PER_DAY", "MAX_AMT_PER_MONTH"]}}, "required": ["VELOCITY_TYPE", "VELOCITY_DETAILS"]}]}}, "required": ["VELOCITIES"]}, "configureMbidAndInstrument": {"type": "object", "properties": {"configureMerchantCommission": {"type": "object", "properties": {"ACTION": {"type": "string"}, "COMMISSION": {"type": "object", "properties": {"FEE_TYPE": {"type": "string"}, "PERCENT_COMMISSION": {"type": "string"}, "COMMISSION_TYPE_BOTH": {"type": "string"}}, "required": ["FEE_TYPE", "PERCENT_COMMISSION", "COMMISSION_TYPE_BOTH"]}}, "required": ["ACTION", "COMMISSION"]}, "TXN_TYPES": {"type": "array", "items": [{"type": "object", "properties": {"TXN_TYPE": {"type": "string"}, "PAY_MODES": {"type": "array", "items": [{"type": "object", "properties": {"PAY_MODE": {"type": "string"}, "COMMISSION": {"type": "object", "properties": {"FEE_TYPE": {"type": "string"}, "PERCENT_COMMISSION": {"type": "string"}, "COMMISSION_TYPE_BOTH": {"type": "string"}, "SLAB_1_START_RANGE": {"type": "string"}, "SLAB_1_END_RANGE": {"type": "string"}, "SLAB_1_PERCENT_COMMISSION": {"type": "string"}, "SLAB_1_COMMISSION_TYPE_BOTH": {"type": "string"}, "SLAB_2_START_RANGE": {"type": "string"}, "SLAB_2_END_RANGE": {"type": "string"}, "SLAB_2_PERCENT_COMMISSION": {"type": "string"}, "SLAB_2_COMMISSION_TYPE_BOTH": {"type": "string"}}, "required": ["FEE_TYPE", "PERCENT_COMMISSION", "COMMISSION_TYPE_BOTH", "SLAB_1_START_RANGE", "SLAB_1_END_RANGE", "SLAB_1_PERCENT_COMMISSION", "SLAB_1_COMMISSION_TYPE_BOTH", "SLAB_2_START_RANGE", "SLAB_2_END_RANGE", "SLAB_2_PERCENT_COMMISSION", "SLAB_2_COMMISSION_TYPE_BOTH"]}, "BANKS": {"type": "array", "items": {}}}, "required": ["PAY_MODE", "COMMISSION", "BANKS"]}, {"type": "object", "properties": {"PAY_MODE": {"type": "string"}, "COMMISSION": {"type": "object", "properties": {"FEE_TYPE": {"type": "string"}, "PERCENT_COMMISSION": {"type": "string"}, "COMMISSION_TYPE_BOTH": {"type": "string"}}, "required": ["FEE_TYPE", "PERCENT_COMMISSION", "COMMISSION_TYPE_BOTH"]}, "BANKS": {"type": "array", "items": {}}}, "required": ["PAY_MODE", "COMMISSION", "BANKS"]}]}}, "required": ["TXN_TYPE", "PAY_MODES"]}, {"type": "object", "properties": {"TXN_TYPE": {"type": "string"}, "PAY_MODES": {"type": "array", "items": [{"type": "object", "properties": {"PAY_MODE": {"type": "string"}, "COMMISSION": {"type": "object", "properties": {"FEE_TYPE": {"type": "string"}, "PERCENT_COMMISSION": {"type": "string"}, "COMMISSION_TYPE_BOTH": {"type": "string"}}, "required": ["FEE_TYPE", "PERCENT_COMMISSION", "COMMISSION_TYPE_BOTH"]}, "BANKS": {"type": "array", "items": {}}}, "required": ["PAY_MODE", "COMMISSION", "BANKS"]}, {"type": "object", "properties": {"PAY_MODE": {"type": "string"}, "COMMISSION": {"type": "object", "properties": {"FEE_TYPE": {"type": "string"}, "PERCENT_COMMISSION": {"type": "string"}, "COMMISSION_TYPE_BOTH": {"type": "string"}}, "required": ["FEE_TYPE", "PERCENT_COMMISSION", "COMMISSION_TYPE_BOTH"]}, "BANKS": {"type": "array", "items": {}}}, "required": ["PAY_MODE", "COMMISSION", "BANKS"]}, {"type": "object", "properties": {"PAY_MODE": {"type": "string"}, "COMMISSION": {"type": "object", "properties": {"FEE_TYPE": {"type": "string"}, "PERCENT_COMMISSION": {"type": "string"}, "COMMISSION_TYPE_BOTH": {"type": "string"}}, "required": ["FEE_TYPE", "PERCENT_COMMISSION", "COMMISSION_TYPE_BOTH"]}, "BANKS": {"type": "array", "items": {}}}, "required": ["PAY_MODE", "COMMISSION", "BANKS"]}]}}, "required": ["TXN_TYPE", "PAY_MODES"]}]}}, "required": ["configureMerchantCommission", "TXN_TYPES"]}, "CUST_ID": {"type": "string"}}, "required": ["createMerReq", "configVelocity", "configureMbidAndInstrument", "CUST_ID"]}