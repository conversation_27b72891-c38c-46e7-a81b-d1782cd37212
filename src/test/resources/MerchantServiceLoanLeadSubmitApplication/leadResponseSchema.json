{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"meta": {"type": "object", "properties": {"stage": {"type": "string"}, "refId": {"type": "string"}, "subStage": {"type": "string"}, "status": {"type": "string"}}, "required": ["stage", "refId", "subStage", "status"]}, "data": {"type": "object", "properties": {"state": {"type": "string"}}, "required": ["state"]}}, "required": ["meta", "data"]}