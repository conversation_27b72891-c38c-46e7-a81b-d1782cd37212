{"agentTncStatus": true, "agentKycStatus": true, "merchantDetails": {"solutionTypeLevel2": "nonMinKyc", "merchantCallApi": true, "merchantOpenForm": true, "merchantEditable": false, "leadStageBucket": "Active", "editableFields": {"subCategory": true, "tierType": true, "storeNumber": true, "brandAssociation": true, "businessSpocEmail": true, "subSegment": true, "marketPlaceSegment": true, "storePincode": true, "closingTime": true, "beneficiaryName": true, "segment": true, "marketPlaceSubSegment": true, "pan": true, "ifsc": true, "shopManagerPhone": true, "storeManagerNumber": true, "brandCode": true, "nameAsPerPan": true, "businessEmail": true, "brandName": true, "totalEmployee": true, "monthlyGMV": true, "ownerDob": true, "entityType": true, "solutionTypeLevel3": true, "posRentalPlan": true, "businessOwnerName": true, "marketPlaceEmail": true, "shopManagerEmail": true, "storeAddress": true, "bcaMerchantType": true, "name": true, "storeEmail": true, "avgSalary": true, "storeCity": true, "nameAsPerAadhar": true, "displayName": true, "openingTime": true, "approverDetails": true, "bankName": true, "businessSpocNumber": true, "typeOfShop": true, "gstin": true, "idNumber": true, "storeName": true, "bankAccountNumber": true, "warehouseEmail": true, "email": true, "gstExemptedCategory": true, "storeCategory": true, "countryOfIncorporation": true, "storeDisplayName": true, "shopManagerName": true, "aadharNumber": true, "bankAccountHolderName": true, "primaryContact": true, "educationalQualification": true, "storeManagerEmail": true, "businessSpocName": true, "storeState": true, "category": true}, "rejectedFields": {}, "suggestedBusinessAddresses": [], "suggestedRelatedBusinesses": [], "suggestedBanks": [], "pennyDropDetails": {"bankName": null, "bankAccountNumber": null, "bankAccountHolderName": null, "ifsc": null, "kycName": null, "pennyDropStatus": false, "nameMatchStatus": false, "uuid": null, "documents": [], "upiAccountId": null}, "entityType": "INDIVIDUAL", "oSV": false, "fATCADeclared": false, "midEDCOrConverted": false, "paymentDoneForEDC": false, "fastagPaymentDone": "false", "turnoverbelowTaxableLimitAsGstAct": false, "businessAppliedForGst": false, "suggestedBillingAddress": [], "suggestedRegisteredAddress": [], "suggestedOwnerAddress": [], "businessProofNotRequired": false, "Sub_Stage__c": "LEAD_CREATED", "onboard": "None of the above", "lastModifiedTimestamp": 0, "leadAcquiredTimestamp": *************, "isMerchantRejected__c": false, "substage": "Lead Created"}, "errorCode": "200"}