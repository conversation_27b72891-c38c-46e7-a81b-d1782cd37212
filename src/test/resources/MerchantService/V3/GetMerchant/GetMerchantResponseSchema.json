{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "merchantDetails": {"type": "object", "properties": {"solutionTypeLevel2": {"type": "string"}, "merchantCallApi": {"type": "boolean"}, "merchantOpenForm": {"type": "boolean"}, "merchantEditable": {"type": "boolean"}, "leadStageBucket": {"type": "string"}, "editableFields": {"type": "object", "properties": {"subCategory": {"type": "boolean"}, "tierType": {"type": "boolean"}, "storeNumber": {"type": "boolean"}, "brandAssociation": {"type": "boolean"}, "businessSpocEmail": {"type": "boolean"}, "subSegment": {"type": "boolean"}, "marketPlaceSegment": {"type": "boolean"}, "storePincode": {"type": "boolean"}, "closingTime": {"type": "boolean"}, "beneficiaryName": {"type": "boolean"}, "segment": {"type": "boolean"}, "marketPlaceSubSegment": {"type": "boolean"}, "pan": {"type": "boolean"}, "ifsc": {"type": "boolean"}, "shopManagerPhone": {"type": "boolean"}, "storeManagerNumber": {"type": "boolean"}, "brandCode": {"type": "boolean"}, "nameAsPerPan": {"type": "boolean"}, "businessEmail": {"type": "boolean"}, "brandName": {"type": "boolean"}, "totalEmployee": {"type": "boolean"}, "monthlyGMV": {"type": "boolean"}, "ownerDob": {"type": "boolean"}, "entityType": {"type": "boolean"}, "solutionTypeLevel3": {"type": "boolean"}, "posRentalPlan": {"type": "boolean"}, "businessOwnerName": {"type": "boolean"}, "marketPlaceEmail": {"type": "boolean"}, "shopManagerEmail": {"type": "boolean"}, "storeAddress": {"type": "boolean"}, "bcaMerchantType": {"type": "boolean"}, "name": {"type": "boolean"}, "storeEmail": {"type": "boolean"}, "avgSalary": {"type": "boolean"}, "storeCity": {"type": "boolean"}, "nameAsPerAadhar": {"type": "boolean"}, "displayName": {"type": "boolean"}, "openingTime": {"type": "boolean"}, "approverDetails": {"type": "boolean"}, "bankName": {"type": "boolean"}, "businessSpocNumber": {"type": "boolean"}, "typeOfShop": {"type": "boolean"}, "gstin": {"type": "boolean"}, "idNumber": {"type": "boolean"}, "storeName": {"type": "boolean"}, "bankAccountNumber": {"type": "boolean"}, "warehouseEmail": {"type": "boolean"}, "email": {"type": "boolean"}, "gstExemptedCategory": {"type": "boolean"}, "storeCategory": {"type": "boolean"}, "countryOfIncorporation": {"type": "boolean"}, "storeDisplayName": {"type": "boolean"}, "shopManagerName": {"type": "boolean"}, "aadharNumber": {"type": "boolean"}, "bankAccountHolderName": {"type": "boolean"}, "primaryContact": {"type": "boolean"}, "educationalQualification": {"type": "boolean"}, "storeManagerEmail": {"type": "boolean"}, "businessSpocName": {"type": "boolean"}, "storeState": {"type": "boolean"}, "category": {"type": "boolean"}}}, "rejectedFields": {"type": "object"}, "suggestedBusinessAddresses": {"type": "array", "items": {}}, "suggestedRelatedBusinesses": {"type": "array", "items": {}}, "suggestedBanks": {"type": "array", "items": {}}, "pennyDropDetails": {"type": "object", "properties": {"bankName": {"type": ["null", "string"]}, "bankAccountNumber": {"type": ["null", "string"]}, "bankAccountHolderName": {"type": ["null", "string"]}, "ifsc": {"type": ["null", "string"]}, "kycName": {"type": ["null", "string"]}, "pennyDropStatus": {"type": "boolean"}, "nameMatchStatus": {"type": "boolean"}, "uuid": {"type": ["null", "string"]}, "documents": {"type": "array", "items": {}}, "upiAccountId": {"type": ["null", "string"]}}, "required": ["bankName", "bankAccountNumber", "bankAccountHolderName", "ifsc", "kycName", "pennyDropStatus", "nameMatchStatus", "uuid", "documents", "upiAccountId"]}, "entityType": {"type": "string"}, "oSV": {"type": "boolean"}, "fATCADeclared": {"type": "boolean"}, "midEDCOrConverted": {"type": "boolean"}, "paymentDoneForEDC": {"type": "boolean"}, "fastagPaymentDone": {"type": "string"}, "turnoverbelowTaxableLimitAsGstAct": {"type": "boolean"}, "businessAppliedForGst": {"type": "boolean"}, "suggestedBillingAddress": {"type": "array", "items": {}}, "suggestedRegisteredAddress": {"type": "array", "items": {}}, "suggestedOwnerAddress": {"type": "array", "items": {}}, "businessProofNotRequired": {"type": "boolean"}, "Sub_Stage__c": {"type": "string"}, "onboard": {"type": "string"}, "lastModifiedTimestamp": {"type": "integer"}, "leadAcquiredTimestamp": {"type": "integer"}, "isMerchantRejected__c": {"type": "boolean"}, "substage": {"type": "string"}}, "required": ["merchantCallApi", "merchantOpenForm", "merchantEditable", "leadStageBucket", "editable<PERSON><PERSON>s", "rejectedFields", "suggestedBusinessAddresses", "suggestedRelatedBusinesses", "suggestedBanks", "pennyDropDetails", "entityType", "oSV", "fATCADeclared", "midEDCOrConverted", "paymentDoneForEDC", "fastagPaymentDone", "turnoverbelowTaxableLimitAsGstAct", "businessAppliedForGst", "suggestedBillingAddress", "suggestedReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "businessProofNotRequired", "Sub_Stage__c", "onboard", "lastModifiedTimestamp", "leadAcquiredTimestamp", "isMerchantRejected__c", "substage"]}, "errorCode": {"type": "string"}}, "required": ["agentTncStatus", "agentKycStatus", "merchantDetails", "errorCode"]}