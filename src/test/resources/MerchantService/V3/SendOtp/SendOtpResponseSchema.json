{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "state": {"type": "string"}, "responseCode": {"type": "string"}, "httpStatus": {"type": "integer"}, "status": {"type": "string"}, "skipOtp": {"type": "boolean"}, "proceed": {"type": "boolean"}, "abort": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["agentTncStatus", "state", "responseCode", "httpStatus", "status", "skipOtp", "proceed", "abort", "message"]}