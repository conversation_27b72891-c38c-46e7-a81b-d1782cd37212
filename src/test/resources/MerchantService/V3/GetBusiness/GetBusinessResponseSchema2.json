{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "businesses": {"type": "array", "items": [{"type": "object", "properties": {"leadId": {"type": "string"}, "kybBusinessId": {"type": "string"}, "businessName": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "addressEditable": {"type": "boolean"}}, "required": ["kybBusinessId", "businessName", "openable", "callAPI", "addressEditable"]}]}, "pan": {"type": "string"}, "panEditable": {"type": "boolean"}, "errorCode": {"type": "string"}}, "required": ["errorCode"]}