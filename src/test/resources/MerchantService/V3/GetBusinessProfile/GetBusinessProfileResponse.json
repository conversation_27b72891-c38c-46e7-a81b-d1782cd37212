{"agentTncStatus": true, "agentKycStatus": true, "businessSRO": {"leadId": "36791b9c-8c23-4bda-b14c-4aa2749170f3", "kybBusinessId": "A0b9vhkernnjz525", "businessName": "AASHIT SHARMA", "pan": "**********", "entityType": "PROPRIETORSHIP", "stage": "Lead Created", "kycName": "TOUCH WOOD LIMITED", "openable": false, "callAPI": true, "uuid": "c027bedc-ec91-4e46-a677-07cdbe14f4b4", "custId": 1000583632, "addressEditable": true}, "solutions": {"SolutionsApplyFor": [{"solutionType": "unmap_edc", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "mall_merchant", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "deals_merchant", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "map_edc", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "qr_merchant", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "salary_account", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "super_gv", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": true, "applicantAuthSignatory": false}, {"solutionType": "paytm_mall_merchant", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": true, "applicantAuthSignatory": false}, {"solutionType": "pos", "openable": true, "callAPI": false, "multiAgreement": true, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "pharmacy_delivery", "openable": false, "callAPI": false, "multiAgreement": true, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "add_store", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": true, "applicantAuthSignatory": false}, {"solutionType": "food_delivery", "openable": false, "callAPI": false, "multiAgreement": true, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "grocery_delivery", "openable": false, "callAPI": false, "multiAgreement": true, "mobileEditable": false, "applicantAuthSignatory": false}, {"solutionType": "pos_mall", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": true, "applicantAuthSignatory": false}, {"solutionType": "qr_sticker_mapping", "openable": true, "callAPI": false, "multiAgreement": false, "mobileEditable": false, "applicantAuthSignatory": false}]}, "errorCode": "200"}