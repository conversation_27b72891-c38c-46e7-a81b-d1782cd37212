{"$schema": "http://json-schema.org/draft-04/schema#", "type": "array", "items": [{"type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "businessSRO": {"type": "object", "properties": {"leadId": {"type": "string"}, "kybBusinessId": {"type": "string"}, "businessName": {"type": "string"}, "pan": {"type": "string"}, "entityType": {"type": "string"}, "stage": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "nameAsPerPan": {"type": "string"}, "uuid": {"type": "string"}, "custId": {"type": "integer"}, "addressEditable": {"type": "boolean"}}}, "solutions": {"type": "object", "properties": {"SolutionsApplyFor": {"type": "array", "items": [{"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}, {"type": "object", "properties": {"solutionType": {"type": "string"}, "openable": {"type": "boolean"}, "callAPI": {"type": "boolean"}, "multiAgreement": {"type": "boolean"}, "applicantAuthSignatory": {"type": "boolean"}, "mobileEditable": {"type": "boolean"}}}]}}}, "errorCode": {"type": "string"}}}]}