{
  "userType": "merchant_common_onboard_agreement",
  <#if state??> "state": "${state}",</#if>
  <#if otp??> "otp": "${otp}",</#if>
  <#if mobile??> "mobile": "${mobile}",</#if>
  "individaulMerchantKyc": true,
  "onlySaveTnc": true,
  <#if custId??> "custId": "${custId}",</#if>
  <#if leadId??> "leadId": "${leadId}",</#if>
  "solutionAdditionalInfo": {
    "AGENT_TNC_SHOP_DISTANCE": 0.21375668470648518,
    "AGENT_LATITUDE_TNC": 28.5682878,
    "AGENT_LONGITUDE_TNC": 77.3956838
  }
  }