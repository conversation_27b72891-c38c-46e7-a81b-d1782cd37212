{
  "userType": "common_merchant",
  "skipOtp": true,
  <#if mobile??> "mobile": "${mobile}",</#if>
  "onlySaveTnc": false,
  "solutionAdditionalInfo": {
    "COMPETITOR_QR_DETAILS": "{\"qrCodeDetailsList\": []}"
  },
  "mid": null,
  "edcDiyOrderRequest": {
    <#if fseBeatTagMappingId??> "beatMappingId": "${fseBeatTagMappingId}",</#if>
    <#if referenceValue??> "pgMid": "${referenceValue}",</#if>
    <#if referenceValue??> "orderId": "${referenceValue}",</#if>
    "name": "ARYA ENTERPRISES",
    <#if mobile??> "mobile": "${mobile}",</#if>
    "serviceAddress": {
      "line1": "<PERSON><PERSON><PERSON> ,13 ,Street Sector78 ,Dr <PERSON> ,India ",
      "line2": "",
      "line3": "Sector 116 ,<PERSON><PERSON> ,Gautambuddha Nagar District ",
      "city": "Gautam Buddha Nagar",
      "state": "Uttar Pradesh",
      "postalCode": "201307",
      "country": "India",
      "pincode": "201307",
      "textAddressEmpty": false
    },

  "tags": [{
    <#if fseBeatTagMappingId??> "fseBeatTagMappingId": "${fseBeatTagMappingId}",</#if>
    "tagName": "KYC Update",
    "meta": {}
  }],
  "requestType": "KYC-UPDATE",
  "referenceType": "mid",
  <#if referenceValue??> "referenceValue": "${referenceValue}",</#if>
  "beatClosed": false,
  "beatAutoAllocated": false
}

  }
