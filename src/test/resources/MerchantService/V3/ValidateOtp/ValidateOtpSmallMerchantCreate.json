{
  "userType": "assisted_merchant",
  <#if mobile??> "mobile": "${mobile}",</#if>
  <#if custId??> "custId": "${custId}",</#if>
  "individaulMerchantKyc": true,
  "skipOtp": true,
  "onlyValidateOtp": false,
  "solutionSubType": "small_merchant",
  "solutionAdditionalInfo": {
    "UPI_HANDLE_QR_JSON": "upi://pay?pa=paytmqr281005050101edroxb9bejon@paytm&pn=Rajeev%20Gupta&mc=0000&mode=02&purpose=00",
    "COMPETITOR_QR_LAT": 28.6055638,
    "COMPETITOR_QR_LON": 77.3419141
  }
}
