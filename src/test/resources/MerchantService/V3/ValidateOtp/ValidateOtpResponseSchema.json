{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "moveBack": {"type": "boolean"}, "walletPrime": {"type": "boolean"}, "custId": {"type": "string"}, "proceed": {"type": "boolean"}, "abort": {"type": "boolean"}, "kycDone": {"type": "boolean"}}, "required": ["agentTncStatus", "agentKycStatus", "moveBack", "walletPrime", "custId", "proceed", "abort", "kycDone"]}