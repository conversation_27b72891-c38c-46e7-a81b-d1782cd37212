{
  "deletedFields":{},
  "deletableFields":{},
  "leadDetails":{"additionalDetails":{"editReason":"","docStatusEditFlag":true},
    "documents":[{"name":"Trust Deed Document",
      "type":"Trust Deed Document",
      "documentType":"activity_proof",
      "placeOfIssue":null,
      "dateOfIssue":null,
      "docProvided":"TrustDeed",
      "updatedDocProvided":null,
      "updatedDocType":null,
      "osv":true,
      "docValue":null,
      "expiryDate":null,
      <#if uuid??>"uuid":"${uuid}",</#if>
      "status":"REJECTED",
      "voterIdDetails":null,
      "dlDetails":null,
      "rejectionReason":"Document expired",
      "docSubType":null,
      "action":null,
      <#if uuids??>"uuids":["${uuids}"],</#if>
      "comment":null,
      "addressType":null,
      "statusIndex":0}],
    "leadInfo":{<#if workflowStatusId??>"workflowStatusId":${workflowStatusId}</#if>}
}}

