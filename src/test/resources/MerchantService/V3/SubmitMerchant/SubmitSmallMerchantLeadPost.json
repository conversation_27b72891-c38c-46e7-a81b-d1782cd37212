{
  "nameOfShop": "",
  "nameOfCustomer": "Puneet Kumar",
  "nameOfBusiness": "",
  <#if shopAddress??> "shopAddress": "${shopAddress}",</#if>
  <#if streetName??> "streetName": "${streetName}",</#if>
  <#if areaOfEnrollment??> "areaOfEnrollment": "${areaOfEnrollment}",</#if>
  "state": "Uttar Pradesh",
  "cityOfEnrollment": "Gautam Buddha Nagar",
  "pincode": "201301",
  "landmark": "Mother dairy",
  "latitudeOfShopVehicle": 28.600244,
  "longitudeOfShopVehicle": 77.344925,
  "pennyDropDetails": {
    <#if bankName??> "bankName": "${bankName}",</#if>
    <#if bankAccountNumber??> "bankAccountNumber": "${bankAccountNumber}",</#if>
    <#if bankAccountHolderName??> "bankAccountHolderName": "${bankAccountHolderName}",</#if>
    <#if ifsc??> "ifsc": "${ifsc}",</#if>
    <#if upiAccountId??> "upiAccountId": "${upiAccountId}",</#if>
    "pennyDropStatus": true,
    "nameMatchStatus": true
  },
  "solutionAdditionalInfo": {
    "PAN": "",
    "TYPE_OF_SHOP": "Movable Shop",
    "SEGMENT": "Retail and Shopping",
    "SUB_SEGMENT": "Street Hawker",
    "BUSINESS_TYPE": "",
    "OPT_PAYTM_CARD_MACHINE": false,
    "ASSISTED_OMC": "",
    "MAP_MY_INDIA_LAT": 28.600244,
    "MAP_MY_INDIA_LONG": 77.344925,
    "LAT_LONG_COMPUTATION_RESULT": 659.*************,
    "LAT_LONG_COMPUTATION_RETRY_ATTEMPT": 0,
    "DOB": "",
    "OCR_DETAILS_EDITED": true,
    "SHOP_OWNER_EDITED": true,
    "GENDER": "",
    "SHOP_OWNER_NAME": "Prateek Srivastava",
    "AADHAR_OCR_ACCURACY_INDEX": "99.**************"
  },
  "businessCreationRequestSRO": {
    "kybId": "",
    "leadId": ""
  },
  "userAdditionalInfo": {
    "DOB": "",
    "GENDER": ""
  },
  "gstin": "",
  "partialSave": false,
  "gstExemptedCategory": "",
  "turnoverbelowTaxableLimitAsGstAct": false,
  <#if aadharNumber??> "aadharNumber": "${aadharNumber}",</#if>
  "kycAddress": {
    "line1": "House no.007",
    "line2": "Sector 18",
    "line3": "Panipat City",
    "city": "Panipat",
    "state": "Haryana",
    "pincode": "132103"
  },
  "vehicleType": "",
  "vehicleSubType": "",
  "typeOfOwner": "",
  "vehicleRegistrationId": "",
  "fuelType": "",
  "nameOfOwner": "",
  "mobileNumberOfOwner": "",
  "vehicleName": "",
  "category": "Retail and Shopping",
  <#if relatedBusinessUuid??> "relatedBusinessUuid": "${relatedBusinessUuid}"</#if>
}