{
  <#if mobileNumberOfCustomer??>"mobileNumberOfCustomer": "${mobileNumberOfCustomer}",</#if>
  "nameOfCustomer": "Aashit",
  "entityType": "Individual",
  "panNumber": "",
  "ownerDob": "1986-11-15",
  <#if line1??>"shopAddress": "${line1}",</#if>
  <#if line2??>"streetName": "${line2}",</#if>
  <#if line3??>"areaOfEnrollment": "${line3}",</#if>
  "state": "Uttar Pradesh",
  "cityOfEnrollment": "Gautam Buddha Nagar",
  "pincode": "201301",
  "latitudeOfShopVehicle": "28.***************",
  "longitudeOfShopVehicle": "77.**************",
  "ownerAddressLine1": "Aashit",
  "ownerAddressLine2": "Street",
  "ownerAddressLine3": "Sector",
  "ownerAddressState": "Uttar Pradesh",
  "ownerAddressCity": "<PERSON><PERSON><PERSON>",
  "ownerAddressPincode": "201301",
  "typeOfShop":"Individual shop",
  "languagePreference": "Hindi",
  "alternateNumberOfCustomer": "",
  "vehicleType": "Auto",
  "vehicleSubType": "Ola",
  "typeOfOwner": "Self",
  "vehicleRegistrationId": "UP16CH2832",
  "pennyDropDetails": {
    "bankName": "ANDHRA BANK",
    <#if bankAccountNumber??>"bankAccountNumber": "${bankAccountNumber}",</#if>
<#if ifsc??>"ifsc": "${ifsc}",</#if>
    "kycName": "",
    "bankAccountHolderName": "AASHIT SHARMA",
    "pennyDropStatus": true,
    "nameMatchStatus": false
  },
  "solutionAdditionalInfo": {
    "MERCHANT_DOB": "1992-08-25",
    "MAP_MY_INDIA_LAT": "28.**************",
    "MAP_MY_INDIA_LONG": "77.**************",
    "LAT_LONG_COMPUTATION_RESULT": "282.*************",
    "LAT_LONG_COMPUTATION_RETRY_ATTEMPT": "1"
  },
  "oSV": true,
  "grantedWhatsAppConsent": true,
  "solutionTypeLevel2": "nonMinKyc",
  "category": "Transport",
  "subCategory": "Auto",
  "latitudeOfStickerPhoto": "28.***************",
  "longitudeOfStickerPhoto": "77.**************",
  "latitudeOfShopVehicle1": "28.***************",
  "longitudeOfShopVehicle1": "77.**************",
  "latitudeOfShopVehicle2": "28.***************",
  "longitudeOfShopVehicle2": "77.**************",
  "latitudeOfTransportDoc": "28.***************",
  "longitudeOfTransport": "77.**************",
  "latitudeOfStickerPhoto1": "28.***************",
  "longitudeOfStickerPhoto1": "77.**************",
  "latitudeOfStickerPhoto2": "28.***************",
  "longitudeOfStickerPhoto2": "77.**************"
}