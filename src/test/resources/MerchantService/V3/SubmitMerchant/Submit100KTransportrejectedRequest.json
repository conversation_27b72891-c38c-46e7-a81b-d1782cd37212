{
  <#if mobileNumberOfCustomer??>"mobileNumberOfCustomer": "${mobileNumberOfCustomer}",</#if>
  "nameOfCustomer": "Aashit",
  "entityType": "Individual",
  "panNumber": "",
  "ownerDob": "1993-08-25",
  "vehicleName": "Aashit",
  <#if line1??>"shopAddress": "${line1}",</#if>
  <#if line2??>"streetName": "${line2}",</#if>
  <#if line3??>"areaOfEnrollment": "${line3}",</#if>
  "cityOfEnrollment": "G<PERSON>am <PERSON>",
  "pincode": "201301",
  "latitudeOfShopVehicle": "28.***************",
  "longitudeOfShopVehicle": "77.*************3",
  <#if addressUuid??>"addressUuid": "${addressUuid}",</#if>
  <#if relatedBusinessUuid??>"relatedBusinessUuid": "${relatedBusinessUuid}",</#if>
  "ownerAddressLine1": "Owner floor",
  "ownerAddressLine2": "Owner Street",
  "ownerAddressLine3": "Owner Sector",
  "ownerAddressCity": "Mumbai",
  "ownerAddressState": "Maharashtra",
  "ownerAddressPincode": "400059",
  <#if ownerAddressUUID??>"ownerAddressUUID": "${ownerAddressUUID}",</#if>
  "typeOfShop":"Individual shop",
  "languagePreference": "Hindi",
  "alternateNumberOfCustomer": "",
  "vehicleType": "Auto",
  "vehicleSubType": "Ola",
  "typeOfOwner": "Self",
  "vehicleRegistrationId": "UP16CH2832",
  "pennyDropDetails": {
    "bankName": "ANDHRA BANK",
    <#if bankAccountNumber??>"bankAccountNumber": "${bankAccountNumber}",</#if>
    "ifsc": "ANDB0002029",
    "kycName": "",
    "bankAccountHolderName": "AASHIT SHARMA",
    "pennyDropStatus": true,
    "nameMatchStatus": false
  },
  "solutionAdditionalInfo": {
    "MERCHANT_DOB": "1992-08-25",
    "MAP_MY_INDIA_LAT": "28.**************",
    "MAP_MY_INDIA_LONG": "77.**************",
    "LAT_LONG_COMPUTATION_RESULT": "282.*************",
    "LAT_LONG_COMPUTATION_RETRY_ATTEMPT": "1"
  },
  "oSV": true,
  "grantedWhatsAppConsent": true,
  "solutionTypeLevel2": "nonMinKyc",
  "category": "Transport",
  "subCategory": "Auto",
  "latitudeOfShopVehicle1": "28.***************",
  "longitudeOfShopVehicle1": "77.*************3",
  "latitudeOfShopVehicle2": "28.***************",
  "longitudeOfShopVehicle2": "77.*************3",
  "latitudeOfStickerPhoto": "28.***************",
  "longitudeOfStickerPhoto": "77.*************3",
  "latitudeOfStickerPhoto1": "28.***************",
  "longitudeOfStickerPhoto1": "77.*************3",
  "latitudeOfStickerPhoto2": "28.***************",
  "longitudeOfStickerPhoto2": "77.*************3"
}