{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "entityToSolutionsMap": {"type": "object", "properties": {"INDIVIDUAL": {"type": "array", "items": [{"type": "string"}]}}, "required": ["INDIVIDUAL"]}, "message": {"type": "string"}, "errorCode": {"type": "string"}}, "required": ["agentTncStatus", "agentKycStatus", "entityToSolutionsMap", "message", "errorCode"]}