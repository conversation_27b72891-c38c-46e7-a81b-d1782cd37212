{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"statusCode": {"type": "integer"}, "bankDetails": {"type": "object", "properties": {"bankName": {"type": "string"}, "state": {"type": "string"}, "city": {"type": "string"}, "branch": {"type": "string"}, "branchAddress": {"type": "string"}, "ifscCode": {"type": "string"}}, "required": ["bankName", "state", "city", "branch", "branch<PERSON><PERSON><PERSON>", "ifscCode"]}}, "required": ["statusCode", "bankDetails"]}