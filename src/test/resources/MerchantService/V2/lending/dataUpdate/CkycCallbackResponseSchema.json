{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"statusMessage": {"type": "string"}, "status": {"type": "string"}, "cKycId": {"type": "string"}, "name": {"type": "object", "properties": {"firstName": {"type": "string"}, "middleName": {"type": "string"}, "thirdName": {"type": "string"}}, "required": ["firstName", "middleName", "third<PERSON>ame"]}, "email": {"type": "string"}, "matchPercentageList": {"type": "array", "items": [{"type": "object", "properties": {"type": {"type": "string"}, "percentage": {"type": "string"}}, "required": ["type", "percentage"]}]}, "address": {"type": "object", "properties": {"addressline1": {"type": "string"}, "addressline2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "pincode": {"type": "string"}}, "required": ["addressline1", "addressline2", "city", "state", "pincode"]}, "dob": {"type": "string"}, "gender": {"type": "string"}, "pan": {"type": "string"}, "ckycSuccessMode": {"type": "string"}}, "required": ["statusMessage", "status", "cKycId", "name", "email", "matchPercentageList", "address", "dob", "gender", "pan", "ckycSuccessMode"]}