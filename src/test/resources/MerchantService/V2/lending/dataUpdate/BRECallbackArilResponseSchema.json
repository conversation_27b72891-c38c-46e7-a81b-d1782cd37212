{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"statusMessage": {"type": "string"}, "status": {"type": "string"}, "solutionAdditionalMetaData": {"type": "object", "properties": {"LENDER_NAME": {"type": "string"}, "LOAN_GROUP_LIMIT_NUMBER": {"type": "string"}, "LOAN_GROUP_LIMIT_WORDS": {"type": "string"}, "LOAN_TENURE_UNIT": {"type": "string"}}, "required": ["LENDER_NAME", "LOAN_GROUP_LIMIT_NUMBER", "LOAN_GROUP_LIMIT_WORDS", "LOAN_TENURE_UNIT"]}, "loanOffered": {"type": "boolean"}, "bureau": {"type": "string"}, "creditScore": {"type": "integer"}, "lastFetchDate": {"type": "integer"}, "maxLoanAmount": {"type": "string"}, "minLoanAmount": {"type": "string"}, "maxTenure": {"type": "integer"}, "minTenure": {"type": "integer"}, "rateOfInterest": {"type": "integer"}, "productId": {"type": "string"}, "productVersion": {"type": "integer"}, "offerValidity": {"type": "integer"}, "offerEndDate": {"type": "string"}, "offerStartDate": {"type": "string"}, "baseId": {"type": "string"}, "offerId": {"type": "string"}, "riskGrade": {"type": "string"}, "sourceOfWhitelist": {"type": "string"}, "emandateType": {"type": "string"}, "isActive": {"type": "integer"}, "reasons": {"type": "string"}, "transactionId": {"type": "string"}, "sanctionLetter": {"type": "object", "properties": {"setName": {"type": "string"}}, "required": ["setName"]}, "lenderAgreement": {"type": "object", "properties": {"setName": {"type": "string"}}, "required": ["setName"]}}, "required": ["statusMessage", "status", "solutionAdditionalMetaData", "loanOffered", "bureau", "creditScore", "lastFetchDate", "maxLoanAmount", "minLoanAmount", "maxTenure", "minTenure", "rateOfInterest", "productId", "productVersion", "offerValidity", "offerEndDate", "offerStartDate", "baseId", "offerId", "riskGrade", "sourceOfWhitelist", "emandateType", "isActive", "reasons", "transactionId", "sanctionLetter", "lenderAgreement"]}