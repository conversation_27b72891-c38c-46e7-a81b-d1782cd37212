{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"statusMessage": {"type": "string"}, "status": {"type": "string"}, "creditScore": {"type": "string"}, "lastFetchDate": {"type": "string"}, "bureau": {"type": "string"}, "loanOffered": {"type": "string"}, "minLoanAmount": {"type": "string"}, "maxLoanAmount": {"type": "string"}, "rejectionReason": {"type": "string"}, "authorisedMonthlyLimit": {"type": "string"}}, "required": ["statusMessage", "status", "creditScore", "lastFetchDate", "bureau", "loanOffered", "minLoanAmount", "maxLoanAmount", "rejectionReason", "authorisedMonthlyLimit"]}