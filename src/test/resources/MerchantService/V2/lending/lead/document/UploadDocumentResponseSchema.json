{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"displayMessage": {"type": "string"}, "refId": {"type": "string"}, "statusCode": {"type": "integer"}, "relatedLeads": {"type": "array", "items": {}}, "uuid": {"type": "string"}, "leadId": {"type": "string"}, "stageBumped": {"type": "boolean"}, "allDocsUploaded": {"type": "boolean"}, "currentDocPersisted": {"type": "boolean"}}, "required": ["displayMessage", "refId", "statusCode", "relatedLeads", "uuid", "leadId", "stageBumped", "allDocsUploaded", "currentDocPersisted"]}