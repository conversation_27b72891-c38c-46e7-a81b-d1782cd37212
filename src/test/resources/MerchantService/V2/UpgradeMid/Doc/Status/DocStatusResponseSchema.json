{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "docDetailsSet": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "object", "properties": {"minPages": {"type": "integer"}, "maxPages": {"type": "integer"}, "pendingPages": {"type": "integer"}, "receivedPages": {"type": "integer"}, "multiPageDocLabels": {"type": "null"}}}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}}, "docValueDetails": {"type": "null"}}}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"aadharCardAddressProof": {"type": "string"}}}, "docTypeDisplayName": {"type": "string"}}}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}}, "docValueDetails": {"type": "null"}}}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"selfie": {"type": "string"}}}, "docTypeDisplayName": {"type": "string"}}}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}}, "docValueDetails": {"type": "null"}}}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"educationProof": {"type": "string"}}}, "docTypeDisplayName": {"type": "string"}}}]}, "uploadedDocDetailsSet": {"type": "array", "items": {}}, "optionalDocDetails": {"type": "array", "items": {}}, "solutionLeadId": {"type": "string"}}}