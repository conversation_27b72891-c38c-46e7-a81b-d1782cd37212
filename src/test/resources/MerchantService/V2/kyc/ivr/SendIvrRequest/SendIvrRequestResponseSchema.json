{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "status": {"type": "string"}, "ivrFlow": {"type": "boolean"}, "requestTimeStamp": {"type": "integer"}, "minRequestLimit": {"type": "integer"}, "maxRequestLimit": {"type": "integer"}, "maxRequestTime": {"type": "integer"}, "requestWaitTime": {"type": "integer"}, "otpEnable": {"type": "boolean"}, "ivrNumber": {"type": "string"}, "privilegeCustomer": {"type": "boolean"}, "message": {"type": "string"}, "errorCode": {"type": "string"}}, "required": ["agentTncStatus", "agentKycStatus", "status", "ivrFlow", "requestTimeStamp", "minRequestLimit", "maxRequestLimit", "maxRequestTime", "requestWaitTime", "otpEnable", "ivrNumber", "privilegeCustomer", "message", "errorCode"]}