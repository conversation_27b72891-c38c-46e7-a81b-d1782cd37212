{
  <#if pan??>"pan":"${pan}",</#if>
  <#if entityType??>"entityType":"${entityType}",</#if>
  "solutionAdditionalMetaData": {
    <#if MODEL??>"MODEL":"${MODEL}",</#if>
    <#if SUB_MODEL??>"SUB_MODEL":"${SUB_MODEL}",</#if>
    <#if NAME_AS_PER_NSDL??>"NAME_AS_PER_NSDL":"${NAME_AS_PER_NSDL}",</#if>
    <#if NAME_AS_PER_TAN??>"NAME_AS_PER_TAN":"${NAME_AS_PER_TAN}",</#if>
    <#if DATE_OF_INCORPORATION??>"DATE_OF_INCORPORATION":"${DATE_OF_INCORPORATION}",</#if>
    <#if GST_EXEMPTED??>"GST_EXEMPTED":"${GST_EXEMPTED}",</#if>
    <#if LEGAL_NAME??>"LEGAL_NAME":"${LEGAL_NAME}",</#if>
    <#if TRADE_NAME??>"TRADE_NAME":"${TRADE_NAME}",</#if>
    <#if IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION??>"IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION":"${IS_INVOLVED_IN_INTERNATIONAL_TRANSACTION}",</#if>
    <#if ONBOARDING_ENTITY??>"ONBOARDING_ENTITY":"${ONBOARDING_ENTITY}",</#if>
    <#if FLOW_TYPE??>"FLOW_TYPE":"${FLOW_TYPE}",</#if>
    <#if ONBOARDING_DOCUMENT_TYPE??>"ONBOARDING_DOCUMENT_TYPE":"${ONBOARDING_DOCUMENT_TYPE}",</#if>
    <#if ONBOARDING_DOCUMENT_VALUE??>"ONBOARDING_DOCUMENT_VALUE":"${ONBOARDING_DOCUMENT_VALUE}",</#if>
    "SEGMENT": "Government",
    "SUB_SEGMENT": "Central Department",
    "EMAIL": "<EMAIL>",
    "IS_GSTIN_FETCHED_FROM_PAN": false,
    "GST_API_SUCCESS": true,
    <#if kycverifygstsuccessresponse??>"KYC_VERIFY_GST_SUCCESS_RESPONSE":${kycverifygstsuccessresponse}</#if>,
    "DEALER_CODE": "5235123"
  },
  "retailRelatedBusiness": {
    <#if gstin??>"gstin":"${gstin}",</#if>
    "displayName": "Breaking Bad",
    "addresses": [
      {
        "addressType": "BUSINESS",
        "addressSubType": "CORRESPONDENCE",
        "line1": "8/101",
        "line2": "road",
        "line3": "Road-2",
        <#if pincode??>"pincode":"${pincode}",</#if>
        <#if state??>"state":"${state}",</#if>
        <#if city??>"city":"${city}",</#if>
        <#if country??>"country":"${country}",</#if>
        "copyAddressCheckbox_correspondenceAddressDetails": true
      },
      {
        "addressType": "BUSINESS",
        "addressSubType": "BILLING",
        "line1": "8/101",
        "line2": "road",
        "line3": "Road-2",
        <#if pincode??>"pincode":"${pincode}",</#if>
        <#if state??>"state":"${state}",</#if>
        <#if city??>"city":"${city}",</#if>
        <#if country??>"country":"${country}"</#if>
      }
    ]
  },
  "registeredAddress": {
    "addressType": "BUSINESS",
    "addressSubType": "REGISTERED",
    "line1": "8/101",
    "line2": "road",
    "line3": "Road-2",
    <#if pincode??>"pincode":"${pincode}",</#if>
    <#if state??>"state":"${state}",</#if>
    <#if city??>"city":"${city}",</#if>
    <#if country??>"country":"${country}",</#if>
    "copyAddressCheckbox_registeredAddressDetails": true
  }
}