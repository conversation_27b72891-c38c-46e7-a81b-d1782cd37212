{
    <#if pan??>"pan": "${pan}",</#if>
    <#if entityType??>"entityType": "${entityType}",</#if>
    "solutionAdditionalMetaData": {
        <#if VERTICAL_TYPE??>"VERTICAL_TYPE": "${VERTICAL_TYPE}",</#if>
        <#if CONVENIENCE_FEE_TYPE??>"CONVENIENCE_FEE_TYPE": "${CONVENIENCE_FEE_TYPE}",</#if>
        <#if AGREEMENT_TYPE??>"AGREEMENT_TYPE": "${AGREEMENT_TYPE}",</#if>
        <#if REGION??>"REGION": "${REGION}",</#if>
        <#if SECONDARY_CATEGORY??>"SECONDARY_CATEGORY": "${SECONDARY_CATEGORY}",</#if>
        <#if SHARED_COMMISSION??>"SHARED_COMMISSION": "${SHARED_COMMISSION}",</#if>
        <#if MO<PERSON><PERSON>_NUMBER??>"MOBILE_NUMBER": "${MOBILE_NUMBER}",</#if>
        <#if SOLUTION_TYPE??>"SOLUTION_TYPE": "${SOLUTION_TYPE}",</#if>
        <#if INVOICE_SAP_TYPE??>"INVOICE_SAP_TYPE": "${INVOICE_SAP_TYPE}",</#if>
        <#if SETTLEMENT_TYPE??>"SETTLEMENT_TYPE": "${SETTLEMENT_TYPE}",</#if>
        <#if INVOICE_COMMISSION_TYPE??>"INVOICE_COMMISSION_TYPE": "${INVOICE_COMMISSION_TYPE}",</#if>
        <#if FLOW_TYPE??>"FLOW_TYPE": "${FLOW_TYPE}",</#if>
        <#if ECODE??>"ECODE": "${ECODE}",</#if>
        <#if ACCOUNT_SOURCE_TEAM??>"ACCOUNT_SOURCE_TEAM": "${ACCOUNT_SOURCE_TEAM}",</#if>
        <#if TAX_APPLICABLE??>"TAX_APPLICABLE": "${TAX_APPLICABLE}",</#if>
        <#if FULFILMENT_SERVICE_ID??>"FULFILMENT_SERVICE_ID": "${FULFILMENT_SERVICE_ID}",</#if>
        <#if MERCHANT_INDUSTRY_TYPE??>"MERCHANT_INDUSTRY_TYPE": "${MERCHANT_INDUSTRY_TYPE}",</#if>
        <#if PENNY_DROP_STATUS??>"PENNY_DROP_STATUS": ${PENNY_DROP_STATUS},</#if>
        <#if EMAIL??>"EMAIL": ${EMAIL},</#if>
        <#if MDR_LINE_ITEMS??>"MDR_LINE_ITEMS": "{\"payModeDetails\":[{\"txnType\":\"Payments\",\"paymodes\":{\"UPI\":[{\"bank\":\"DEFAULT\",\"bankDisplayName\":\"DEFAULT\",\"paymode\":\"UPI\",\"feeType\":\"SIMPLE\",\"commissionType\":\"PERCENTAGE\",\"fee\":\"0\",\"approvedFee\":\"0\",\"cardType\":null,\"cardScheme\":null,\"convenienceModel\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null}],\"CC\":[{\"bank\":\"DEFAULT\",\"bankDisplayName\":\"DEFAULT\",\"paymode\":\"CC\",\"feeType\":\"SIMPLE\",\"commissionType\":\"PERCENTAGE\",\"fee\":\"1\",\"cardType\":null,\"cardScheme\":null,\"convenienceModel\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null},{\"cardScheme\":\"DINERS\",\"paymode\":\"CC\",\"feeType\":\"SIMPLE\",\"commissionType\":\"PERCENTAGE\",\"fee\":\"1\",\"cardType\":null,\"bank\":null,\"convenienceModel\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null}]}},{\"txnType\":\"WITHDRAWAL\",\"paymodes\":{\"MANUAL_WITHDRAWAL\":[{\"bank\":\"DEFAULT\",\"bankDisplayName\":\"DEFAULT\",\"paymode\":\"MANUAL_WITHDRAWAL\",\"convenienceModel\":\"PCF\",\"feeType\":\"SIMPLE\",\"commissionType\":\"FLAT\",\"fee\":\"1\",\"cardType\":null,\"cardScheme\":null,\"subscriptionAction\":null,\"feeFactor\":null,\"startRange\":null,\"endRange\":null}]}}],\"txnFlows\":[\"Barcode\"]}",</#if>
        <#if MODEL??>"MODEL": "${MODEL}",</#if>
        <#if ONBOARDING_DOCUMENT_VALUE??>"ONBOARDING_DOCUMENT_VALUE": "${ONBOARDING_DOCUMENT_VALUE}",</#if>
        <#if ONBOARDING_DOCUMENT_TYPE??>"ONBOARDING_DOCUMENT_TYPE": "${ONBOARDING_DOCUMENT_TYPE}"</#if>
    },
    "bankDetails": {
<#if bankName??>"bankName": "${bankName}",</#if>
<#if ifsc??>"ifsc": "${ifsc}",</#if>
<#if bankAccountNumber??>"bankAccountNumber": "${bankAccountNumber}",</#if>
<#if bankAccountHolderNameByPennyDrop??>"bankAccountHolderNameByPennyDrop": "${bankAccountHolderNameByPennyDrop}",</#if>
<#if pennyDropStatus??>"pennyDropStatus": ${pennyDropStatus}</#if>
}
}