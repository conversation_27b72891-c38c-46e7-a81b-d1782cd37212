{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "planToPriceMap": {"type": "object", "properties": {"Quaterly": {"type": "object", "properties": {"amount": {"type": "string"}}, "required": ["amount"]}, "Yearly": {"type": "object", "properties": {"amount": {"type": "string"}}, "required": ["amount"]}, "Half yearly": {"type": "object", "properties": {"amount": {"type": "string"}}, "required": ["amount"]}}, "required": ["Quaterly", "Yearly", "Half yearly"]}}, "required": ["refId", "statusCode", "planToPriceMap"]}