{"$schema": "http://json-schema.org/draft-04/schema#", "type": "array", "items": [{"type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "docDetailsSet": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object"}, "docTypeDisplayName": {"type": "null"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}]}, "uploadedDocDetailsSet": {"type": "array", "items": {}}, "optionalDocDetails": {"type": "array", "items": {}}, "solutionLeadId": {"type": "string"}}, "required": ["refId", "statusCode", "docDetailsSet", "uploadedDocDetailsSet", "optionalDocDetails", "solutionLeadId"]}]}