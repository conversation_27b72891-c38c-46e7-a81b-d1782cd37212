{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"msisdn": {"type": "string"}, "operator": {"type": "string"}, "circle": {"type": "string"}, "contestIdentifier": {"type": "string"}, "language": {"type": "string"}, "contestLevel": {"type": "string"}, "responseCode": {"type": "string"}, "statusCode": {"type": "integer"}, "channel": {"type": "string"}, "description": {"type": "string"}, "intQuestionCount": {"type": "string"}, "contestQuestionsList": {"type": "array", "items": [{"type": "object", "properties": {"quesId": {"type": "integer"}, "circle": {"type": "string"}, "operator": {"type": "string"}, "contestLevel": {"type": "string"}, "question": {"type": "string"}, "option1": {"type": "string"}, "option2": {"type": "string"}, "option3": {"type": "string"}, "option4": {"type": "string"}, "correctAnswer": {"type": "string"}, "quesDifficultyLevel": {"type": "string"}, "questionType": {"type": "string"}, "language": {"type": "string"}, "questionRefId": {"type": "string"}}}]}, "score": {"type": "number"}, "allQuestionsAttempted": {"type": "boolean"}}}