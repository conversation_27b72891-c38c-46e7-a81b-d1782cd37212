{
  "entityType": "INDIVIDUAL",
  <#if custId??>"custId":"${custId}",</#if>
  "solution": "revisit_merchant",
  "mobileNumber": "7494794070",
  "channel": "GG_APP",
  "oTPValidated": true,
  "locationChanged": true,
  "address": {
    "latitude": 26.9009036,
    "longitude": 80.9287575,
    "line1": "Lucknow ,Keshav Nagar ,Fazullaganj ",
    "line3": "Lucknow ,Lucknow ,Lucknow Division ",
    "pincode": "226020",
    "state": "Uttar Pradesh",
    "city": "Lucknow",
    "shopName": "Test",
    "formattedAddress": "306, Keshav Nagar, Fazullaganj, Lucknow, Uttar Pradesh 226020, India",
    "landmark": "Teste"
  },
  <#if mid??>"mid":"${mid}",</#if>
  "competitorPresent": false,
  "blueOfferQrPhotoPresent": false,
  "solutionTypeLevel3": "unorganized",
  "fseRole": "soundbox",
  "fseSubRole": "device service",
  "agentPreOtpValidationLatitude": 26.9009073,
  "agentPreOtpValidationLongitude": 80.9287688,
  "revisitDistance": 403922.233843319,
  "businessDistance": 403922.233843319,
  <#if beatTagName??>"beatTagName":"${beatTagName}",</#if>
  "solutionSubType": "edc",
  "storeReferenceID": null,
  "questionAnswerList": [
    {
      "questionAlias": "If the Merchant has offer for a loan, what's the current status?",
      "answerAlias": "Merchant's loan request pending"
    },
    {
      "questionAlias": "Was the application submitted? - Merchant's loan request pending",
      "answerAlias": "No"
    },
    {
      "questionAlias": "Reason for application not submitted",
      "answerAlias": "Merchant busy"
    }
  ]
}