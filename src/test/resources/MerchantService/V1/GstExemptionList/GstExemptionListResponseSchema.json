{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "gstExemptionCategories": {"type": "array", "items": [{"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}, {"type": "object", "properties": {"category": {"type": "string"}}, "required": ["category"]}]}, "errorCode": {"type": "string"}}, "required": ["agentTncStatus", "agentKycStatus", "gstExemptionCategories", "errorCode"]}