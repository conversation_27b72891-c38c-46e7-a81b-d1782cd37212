{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"typeToModelPriceMap": {"type": "object", "properties": {"Android With POS": {"type": "object", "properties": {"N3": {"type": "array", "items": [{"type": "object", "properties": {"rentalType": {"type": "string"}, "price": {"type": "number"}, "securityDeposit": {"type": "number"}, "amc": {"type": "number"}, "rentalAmount": {"type": "number"}, "id": {"type": "string"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "null"}, "slabOneStartRange": {"type": "string"}, "slabOneEndRange": {"type": "string"}, "slabOnePercentCommission": {"type": "string"}, "slabTwoStartRange": {"type": "string"}, "slabTwoEndRange": {"type": "string"}, "slabTwoPercentCommission": {"type": "string"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}, "mdrOtpText": {"type": "string"}}, "required": ["rentalType", "price", "securityDeposit", "amc", "rentalAmount", "id", "commercials", "mdrOtpText"]}, {"type": "object", "properties": {"rentalType": {"type": "string"}, "price": {"type": "number"}, "securityDeposit": {"type": "number"}, "amc": {"type": "number"}, "rentalAmount": {"type": "number"}, "id": {"type": "string"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "null"}, "slabOneStartRange": {"type": "string"}, "slabOneEndRange": {"type": "string"}, "slabOnePercentCommission": {"type": "string"}, "slabTwoStartRange": {"type": "string"}, "slabTwoEndRange": {"type": "string"}, "slabTwoPercentCommission": {"type": "string"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}, "mdrOtpText": {"type": "string"}}, "required": ["rentalType", "price", "securityDeposit", "amc", "rentalAmount", "id", "commercials", "mdrOtpText"]}]}, "A910": {"type": "array", "items": [{"type": "object", "properties": {"rentalType": {"type": "string"}, "price": {"type": "number"}, "securityDeposit": {"type": "number"}, "amc": {"type": "number"}, "rentalAmount": {"type": "number"}, "id": {"type": "string"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "null"}, "slabOneStartRange": {"type": "string"}, "slabOneEndRange": {"type": "string"}, "slabOnePercentCommission": {"type": "string"}, "slabTwoStartRange": {"type": "string"}, "slabTwoEndRange": {"type": "string"}, "slabTwoPercentCommission": {"type": "string"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}, "mdrOtpText": {"type": "string"}}, "required": ["rentalType", "price", "securityDeposit", "amc", "rentalAmount", "id", "commercials", "mdrOtpText"]}]}}, "required": ["N3", "A910"]}, "GPRS": {"type": "object", "properties": {"D190": {"type": "array", "items": [{"type": "object", "properties": {"rentalType": {"type": "string"}, "price": {"type": "number"}, "securityDeposit": {"type": "number"}, "amc": {"type": "number"}, "rentalAmount": {"type": "number"}, "id": {"type": "string"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "null"}, "slabOneStartRange": {"type": "string"}, "slabOneEndRange": {"type": "string"}, "slabOnePercentCommission": {"type": "string"}, "slabTwoStartRange": {"type": "string"}, "slabTwoEndRange": {"type": "string"}, "slabTwoPercentCommission": {"type": "string"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}, "mdrOtpText": {"type": "string"}}, "required": ["rentalType", "price", "securityDeposit", "amc", "rentalAmount", "id", "commercials", "mdrOtpText"]}, {"type": "object", "properties": {"rentalType": {"type": "string"}, "price": {"type": "number"}, "securityDeposit": {"type": "number"}, "amc": {"type": "number"}, "rentalAmount": {"type": "number"}, "id": {"type": "string"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "null"}, "slabOneStartRange": {"type": "string"}, "slabOneEndRange": {"type": "string"}, "slabOnePercentCommission": {"type": "string"}, "slabTwoStartRange": {"type": "string"}, "slabTwoEndRange": {"type": "string"}, "slabTwoPercentCommission": {"type": "string"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}, "mdrOtpText": {"type": "string"}}, "required": ["rentalType", "price", "securityDeposit", "amc", "rentalAmount", "id", "commercials", "mdrOtpText"]}]}}, "required": ["D190"]}, "Android": {"type": "object", "properties": {"N3": {"type": "array", "items": [{"type": "object", "properties": {"rentalType": {"type": "string"}, "price": {"type": "number"}, "securityDeposit": {"type": "number"}, "amc": {"type": "number"}, "rentalAmount": {"type": "number"}, "id": {"type": "string"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "null"}, "slabOneStartRange": {"type": "string"}, "slabOneEndRange": {"type": "string"}, "slabOnePercentCommission": {"type": "string"}, "slabTwoStartRange": {"type": "string"}, "slabTwoEndRange": {"type": "string"}, "slabTwoPercentCommission": {"type": "string"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}, "mdrOtpText": {"type": "string"}}, "required": ["rentalType", "price", "securityDeposit", "amc", "rentalAmount", "id", "commercials", "mdrOtpText"]}, {"type": "object", "properties": {"rentalType": {"type": "string"}, "price": {"type": "number"}, "securityDeposit": {"type": "number"}, "amc": {"type": "number"}, "rentalAmount": {"type": "number"}, "id": {"type": "string"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "null"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}, "mdrOtpText": {"type": "string"}}, "required": ["rentalType", "price", "securityDeposit", "amc", "rentalAmount", "id", "commercials", "mdrOtpText"]}]}}, "required": ["N3"]}}, "required": ["Android With POS", "GPRS", "Android"]}, "applicableTaxes": {"type": "array", "items": [{"type": "object", "properties": {"percentage": {"type": "integer"}, "type": {"type": "string"}}, "required": ["percentage", "type"]}]}}, "required": ["typeToModelPriceMap", "applicableTaxes"]}