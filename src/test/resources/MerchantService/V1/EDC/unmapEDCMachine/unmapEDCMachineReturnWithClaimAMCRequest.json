{
  "edcOldQrDetails": {
    <#if modelName??> "modelName": "${modelName}",</#if>
    <#if oem??> "oem": "${oem}",</#if>
    <#if serialNo??> "serialNo": "${serialNo}",</#if>
    "httpStatusCode":0
  },
  <#if leadId??> "leadId": "${leadId}",</#if>
  "metaAdditionalDetails":{"buyAmc":false,"paymentDetail":{"additionalTaxes":[],"basePrice":"600","httpStatusCode":0},"solutionSubType":"claimAmcReturned","subscriptionStatus":"ACTIVE","subscriptionType":"AMC","httpStatusCode":0},
  "questionAnswerList": [{
    "answerAlias": "Yes",
    "questionAlias": "Charger received",
    "httpStatusCode":0
  }, {
    "answerAlias": "Yes",
    "questionAlias": "Box received",
    "httpStatusCode":0
  }, {
    "answerAlias": "Yes",
    "questionAlias": "Data cable received",
    "httpStatusCode":0
  }, {
    "answerAlias": "Yes",
    "questionAlias": "Battery received",
    "httpStatusCode":0
  }, {
    "answerAlias": "Yes",
    "questionAlias": "SIM received",
    "httpStatusCode":0
  }],
  "reason": "Return with Claim Amc",
  "unmapRequestType": "Return",
  "httpStatusCode":0

}