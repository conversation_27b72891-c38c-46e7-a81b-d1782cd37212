{
  "edcNewQrDetails":  {
  <#if newmodelName??> "modelName": "${newmodelName}",</#if>
    <#if newoem??> "oem": "${newoem}",</#if>
  <#if newserialNo??> "serialNo": "${newserialNo}"</#if>
},
"edcOldQrDetails": {
<#if oldmodelName??> "modelName": "${oldmodelName}",</#if>
<#if oldoem??> "oem": "${oldoem}",</#if>
<#if oldserialNo??> "serialNo": "${oldserialNo}"</#if>
},
  <#if leadId??> "leadId": "${leadId}",</#if>
  "questionAnswerList": [{
    "answerAlias": "Yes",
    "questionAlias": "Charger received"
  }, {
    "answerAlias": "Yes",
    "questionAlias": "Box received"
  }, {
    "answerAlias": "Yes",
    "questionAlias": "Data cable received"
  }, {
    "answerAlias": "Yes",
    "questionAlias": "Battery received"
  }, {
    "answerAlias": "Yes",
    "questionAlias": "SIM received"
  }],
  "reason": "Connectivity Issues",
  "unmapRequestType": "Replace"
}