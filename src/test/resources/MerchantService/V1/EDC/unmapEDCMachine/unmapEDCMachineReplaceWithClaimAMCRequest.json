{
  "edcNewQrDetails":  {
    <#if newmodelName??> "modelName": "${newmodelName}",</#if>
    <#if newoem??> "oem": "${newoem}",</#if>
    <#if newserialNo??> "serialNo": "${newserialNo}"</#if>
  },
  "edcOldQrDetails": {
    <#if oldmodelName??> "modelName": "${oldmodelName}",</#if>
    <#if oldoem??> "oem": "${oldoem}",</#if>
    <#if oldserialNo??> "serialNo": "${oldserialNo}"</#if>
  },
  <#if leadId??> "leadId": "${leadId}",</#if>
  "metaAdditionalDetails": {
    "buyAmc": false,
    "paymentDetail": {
      "additionalTaxes": [],
      "basePrice": "600",
      "httpStatusCode": 0
    },
    "solutionSubType": "claimAmcReplaced",
    "subscriptionStatus": "ACTIVE",
    "subscriptionType": "AMC",
    "httpStatusCode": 0
  },
  "questionAnswerList": [
    {
      "answerAlias": "Yes",
      "questionAlias": "Charger received",
      "httpStatusCode": 0
    },
    {
      "answerAlias": "Yes",
      "questionAlias": "Box received",
      "httpStatusCode": 0
    },
    {
      "answerAlias": "Yes",
      "questionAlias": "Data cable received",
      "httpStatusCode": 0
    },
    {
      "answerAlias": "Yes",
      "questionAlias": "Battery received",
      "httpStatusCode": 0
    },
    {
      "answerAlias": "Yes",
      "questionAlias": "SIM received",
      "httpStatusCode": 0
    }
  ],
  "reason": "Replacement with Claim Amc",
  "unmapRequestType": "Replace",
  "httpStatusCode": 0
}

