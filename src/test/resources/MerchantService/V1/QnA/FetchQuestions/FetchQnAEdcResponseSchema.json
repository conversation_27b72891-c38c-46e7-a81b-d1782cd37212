{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "entityType": {"type": "string"}, "solutionType": {"type": "string"}, "questionList": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "integer"}, "text": {"type": "string"}, "displayType": {"type": "string"}, "mandatory": {"type": "boolean"}, "options": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "integer"}, "text": {"type": "string"}, "optionAlias": {"type": "string"}, "veto": {"type": "boolean"}}}, {"type": "object", "properties": {"id": {"type": "integer"}, "text": {"type": "string"}, "optionAlias": {"type": "string"}, "veto": {"type": "boolean"}}}]}, "status": {"type": "boolean"}, "questionAlias": {"type": "string"}, "groupName": {"type": "string"}, "curable": {"type": "boolean"}}}]}}}