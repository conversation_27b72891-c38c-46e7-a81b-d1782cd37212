{"agentTncStatus": true, "entityType": "INDIVIDUAL", "solutionType": "fse_diy", "questionList": [{"id": 2413, "text": "Father's Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Father's Name", "pageName": "1", "regex": "^[a-zA-Z' ]*$", "curable": false}, {"id": 2414, "text": "Mother's Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Mother's Name", "pageName": "1", "regex": "^[a-zA-Z' ]*$", "curable": false}, {"id": 2415, "text": "Marital Status", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13165, "text": "Married", "optionAlias": "Married", "veto": false}, {"id": 13166, "text": "Single", "optionAlias": "Single", "veto": false}], "status": false, "questionAlias": "Marital Status", "pageName": "1", "childQuestions": [{"id": 2416, "text": "Anniversary", "textAsPlaceholder": false, "displayType": "date", "mandatory": true, "options": [], "status": false, "questionAlias": "Anniversary", "pageName": "1", "parentSelectedText": "Married", "curable": false}], "curable": false}, {"id": 2417, "text": "Blood Group", "textAsPlaceholder": true, "displayType": "dropdown", "mandatory": true, "options": [{"id": 13167, "text": "A", "optionAlias": "A", "veto": false}, {"id": 13168, "text": "A+", "optionAlias": "A+", "veto": false}, {"id": 13169, "text": "B", "optionAlias": "B", "veto": false}, {"id": 13170, "text": "B+", "optionAlias": "B+", "veto": false}, {"id": 13171, "text": "AB+", "optionAlias": "AB+", "veto": false}, {"id": 13172, "text": "AB-", "optionAlias": "AB-", "veto": false}, {"id": 13173, "text": "O+", "optionAlias": "O+", "veto": false}, {"id": 13174, "text": "O-", "optionAlias": "O-", "veto": false}], "status": false, "questionAlias": "Blood Group", "pageName": "1", "curable": false}, {"id": 2420, "text": "Highest Education", "textAsPlaceholder": true, "displayType": "dropdown", "mandatory": true, "options": [{"id": 13183, "text": "Graduate", "optionAlias": "Graduate", "veto": false}, {"id": 13184, "text": "High School", "optionAlias": "High School", "veto": false}], "status": false, "questionAlias": "Highest Education", "pageName": "1", "curable": false}, {"id": 2418, "text": "Nominee Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Nominee Name", "pageName": "2", "regex": "^[a-zA-Z' ]*$", "curable": false}, {"id": 2419, "text": "Relation", "textAsPlaceholder": true, "displayType": "dropdown", "mandatory": true, "options": [{"id": 13175, "text": "Father", "optionAlias": "Father", "veto": false}, {"id": 13176, "text": "Mother", "optionAlias": "Mother", "veto": false}, {"id": 13177, "text": "Son", "optionAlias": "Son", "veto": false}, {"id": 13179, "text": "Spouse", "optionAlias": "Spouse", "veto": false}, {"id": 13180, "text": "Daughter", "optionAlias": "Daughter", "veto": false}, {"id": 13181, "text": "Brother", "optionAlias": "Brother", "veto": false}, {"id": 13182, "text": "Sister", "optionAlias": "Sister", "veto": false}], "status": false, "questionAlias": "Relation", "pageName": "2", "curable": false}, {"id": 2422, "text": "Nominee Date of Birth", "textAsPlaceholder": false, "displayType": "date", "mandatory": true, "options": [], "status": false, "questionAlias": "Nominee Date of Birth", "pageName": "2", "curable": false}, {"id": 2423, "text": "Gratuity Percentage", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13185, "text": "50%", "optionAlias": "50", "veto": false}, {"id": 13186, "text": "100%", "optionAlias": "100", "veto": false}], "status": false, "questionAlias": "Gratuity Percentage", "pageName": "2", "curable": false}, {"id": 2424, "text": "PF/Pension Percentage", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13187, "text": "50%", "optionAlias": "50", "veto": false}, {"id": 13188, "text": "100%", "optionAlias": "100", "veto": false}], "status": false, "questionAlias": "PF/Pension Percentage", "pageName": "2", "curable": false}, {"id": 2425, "text": "Dependent Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Dependent Name", "pageName": "3", "regex": "^[a-zA-Z' ]*$", "curable": false}, {"id": 2421, "text": "Relation", "textAsPlaceholder": true, "displayType": "dropdown", "mandatory": true, "options": [{"id": 13189, "text": "Father", "optionAlias": "Father", "veto": false}, {"id": 13190, "text": "Mother", "optionAlias": "Mother", "veto": false}, {"id": 13191, "text": "Son", "optionAlias": "Son", "veto": false}, {"id": 13193, "text": "Spouse", "optionAlias": "Spouse", "veto": false}, {"id": 13194, "text": "Daughter", "optionAlias": "Daughter", "veto": false}], "status": false, "questionAlias": "Dependent Relation", "pageName": "3", "curable": false}, {"id": 2426, "text": "Dependent Date of Birth", "textAsPlaceholder": false, "displayType": "date", "mandatory": true, "options": [], "status": false, "questionAlias": "Dependent Date of Birth", "pageName": "3", "curable": false}, {"id": 2427, "text": "<PERSON><PERSON><PERSON><PERSON>", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "<PERSON><PERSON><PERSON><PERSON> card", "pageName": "3", "curable": false}, {"id": 2428, "text": "Are you a fresher?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13195, "text": "Yes", "optionAlias": "Yes", "veto": false}, {"id": 13196, "text": "No", "optionAlias": "No", "veto": false}], "status": false, "questionAlias": "Are you a fresher?", "pageName": "4", "childQuestions": [{"id": 2429, "text": "Organization Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Organization Name", "pageName": "4", "parentSelectedText": "No", "regex": "^[a-zA-Z0-9' ]*$", "curable": false}, {"id": 2430, "text": "Designation", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Designation", "pageName": "4", "parentSelectedText": "No", "regex": "^[a-zA-Z ]*$", "curable": false}, {"id": 2431, "text": "Date of Joining", "textAsPlaceholder": false, "displayType": "date", "mandatory": true, "options": [], "status": false, "questionAlias": "Date of Joining", "pageName": "4", "parentSelectedText": "No", "curable": false}, {"id": 2432, "text": "Date of leaving", "textAsPlaceholder": false, "displayType": "date", "mandatory": true, "options": [], "status": false, "questionAlias": "Date of leaving", "pageName": "4", "parentSelectedText": "No", "curable": false}, {"id": 2433, "text": "Employee mode", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13197, "text": "Permanent", "optionAlias": "Permanent", "veto": false}, {"id": 13198, "text": "Contractual", "optionAlias": "Contractual", "veto": false}], "status": false, "questionAlias": "Employee mode", "pageName": "4", "parentSelectedText": "No", "curable": false}, {"id": 2434, "text": "UAN Number", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "UAN Number", "pageName": "4", "parentSelectedText": "No", "regex": "^[0-9]{12}$", "curable": false}, {"id": 2435, "text": "ESICIP Number", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "ESICIP Number", "pageName": "4", "parentSelectedText": "No", "regex": "^[0-9]{10}$", "curable": false}], "curable": false}, {"id": 2436, "text": "Are you referred by an employee of Paytm Services and its affiliates?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13199, "text": "Yes", "optionAlias": "Yes", "veto": false}, {"id": 13200, "text": "No", "optionAlias": "No", "veto": false}], "status": false, "questionAlias": "Are you referred by an employee of Paytm Services and its affiliates?", "pageName": "5", "childQuestions": [{"id": 2437, "text": "Employee Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Employee Name", "pageName": "5", "parentSelectedText": "Yes", "regex": "^[a-zA-Z' ]*$", "curable": false}, {"id": 2438, "text": "Employee Code", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Employee Code", "pageName": "5", "parentSelectedText": "Yes", "regex": "^[a-zA-Z0-9']*$", "curable": false}], "curable": false}, {"id": 2439, "text": "Is there any relative employed in Paytm Services and its affiliates?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13201, "text": "Yes", "optionAlias": "Yes", "veto": false}, {"id": 13202, "text": "No", "optionAlias": "No", "veto": false}], "status": false, "questionAlias": "Is there any relative employed in Paytm Services and its affiliates?", "pageName": "5", "childQuestions": [{"id": 2445, "text": "Relative Employee Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Relative Employee Name", "pageName": "5", "parentSelectedText": "Yes", "regex": "^[a-zA-Z' ]*$", "curable": false}, {"id": 2446, "text": "Relative Employee Code", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Relative Employee Code", "pageName": "5", "parentSelectedText": "Yes", "regex": "^[a-zA-Z0-9']*$", "curable": false}], "curable": false}, {"id": 2440, "text": "Any employee known to you who is employed in Paytm Services and its affiliates?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13203, "text": "Yes", "optionAlias": "Yes", "veto": false}, {"id": 13204, "text": "No", "optionAlias": "No", "veto": false}], "status": false, "questionAlias": "Any employee known to you who is employed in Paytm Services and its affiliates?", "pageName": "5", "childQuestions": [{"id": 2447, "text": "Known Employee Name", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Known Employee Name", "pageName": "5", "parentSelectedText": "Yes", "regex": "^[a-zA-Z' ]*$", "curable": false}, {"id": 2448, "text": "Known Employee Code", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Known Employee Code", "pageName": "5", "parentSelectedText": "Yes", "regex": "^[a-zA-Z0-9']*$", "curable": false}], "curable": false}, {"id": 2441, "text": "Have you ever been convicted in Civil or Criminal case?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13205, "text": "Yes", "optionAlias": "Yes", "veto": false}, {"id": 13206, "text": "No", "optionAlias": "No", "veto": false}], "status": false, "questionAlias": "Have you ever been convicted in Civil or Criminal case?", "pageName": "6", "childQuestions": [{"id": 2442, "text": "Specify details and nature of the case", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Specify details and nature of the case", "pageName": "6", "parentSelectedText": "Yes", "curable": false}], "curable": false}, {"id": 2443, "text": "Is there any pending Civil or criminal case against you?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13207, "text": "Yes", "optionAlias": "Yes", "veto": false}, {"id": 13208, "text": "No", "optionAlias": "No", "veto": false}], "status": false, "questionAlias": "Is there any pending Civil or criminal case against you?", "pageName": "6", "childQuestions": [{"id": 2444, "text": "Specify details and nature of the pending case", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Specify details and nature of the pending case", "pageName": "6", "parentSelectedText": "Yes", "curable": false}], "curable": false}, {"id": 2449, "text": "Do you have direct or indirect involvement in terms of Directorship/Strategic Interest/Shareholding (more than 5%)/ Any formal or official position in any listed or non-listed entity at the time of joining Paytm?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13209, "text": "Yes", "optionAlias": "Yes", "veto": false}, {"id": 13210, "text": "No", "optionAlias": "No", "veto": false}], "status": false, "questionAlias": "Do you have direct or indirect involvement in terms of Directorship/Strategic Interest/Shareholding (more than 5%)/ Any formal or official position in any listed or non-listed entity at the time of joining Paytm?", "pageName": "7", "childQuestions": [{"id": 2450, "text": "Registered Name of the Entity", "textAsPlaceholder": true, "displayType": "input", "mandatory": true, "options": [], "status": false, "questionAlias": "Registered Name of the Entity", "pageName": "7", "parentSelectedText": "Yes", "curable": false}, {"id": 2451, "text": "Entity Type", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 13211, "text": "Listed", "optionAlias": "Listed", "veto": false}, {"id": 13212, "text": "Non-Listed", "optionAlias": "Non-Listed", "veto": false}], "status": false, "questionAlias": "Entity Type", "pageName": "7", "parentSelectedText": "Yes", "curable": false}], "curable": false}], "skipCurrentQnAScreen": false}