{
  "merchantCallApi": true,
  "merchantOpenForm": true,
  "merchantEditable": false,
  "leadStageBucket": "Active",
  "editableFields": {},
  "rejectedFields": {},
  "rejectionReasons": {},
  "suggestedBusinessAddresses": [],
  "suggestedRelatedBusinesses": [{
    "address": {
      "refId": "=wb-144",
      "statusCode": 0,
      "pincode": 201310,
      "postalCode": 0,
      "state": "Uttar Pradesh",
      "city": "Noida",
      "line1": "HYsba",
      "line2": "Hsjs",
      "line3": "Hsba",
      "latitude": 0,
      "longitude": 0,
      "documents": [],
      "addressType": "RESIDENTIAL",
      "addressSubType": "PERMANENT",
      "status": 3,
      "qcRequired": false
    },
    "editableFields": {},
    "businessAppliedForGst": false,
    "leadRejected": false
  }],
  "suggestedBanks": [],
  "upiLinkEnabled": false,
  "pennyDropDetails": {
    "bankName": null,
    "bankAccountNumber": null,
    "bankAccountHolderName": null,
    "ifsc": null,
    "kycName": null,
    "pennyDropStatus": false,
    "nameMatchStatus": false,
    "uuid": null,
    "documents": [],
    "upiAccountId": null,
    "beneficiaryName": null,
    "accountType": null
  },
  "entityType": "INDIVIDUAL",
  "oSV": false,
  "fATCADeclared": false,
  "midEDCOrConverted": false,
  "solutionAdditionalInfo": {
    "RECRUITER_NAME": "recruitertest",
    "AADHAAR_DEDUP_STATUS": "SUCCESS",
    "CAND_CODE": "CA6631102624",
    "VENDOR_MANAGER_NAME": "PSPL",
    "VENDOR_MANAGER_CODE": "VC38706",
    "BUREAU_PULL_TRIGGERED": "FALSE",
    "CAND_NAME": "Anurag",
    "VENDOR_MANAGER_EMAIL": "<EMAIL>",
    "RECRUITER_UNAME": "recruitertest",
    "GENESIS_QC_STATUS_UPDATE": "SUCCESS",
    "CAND_SOURCE": "Vendor",
    <#if AADHAR_REF_NUMBER??> "AADHAR_REF_NUMBER":"${AADHAR_REF_NUMBER}",</#if>
    "TRANSACTION_ID": "e5a21f60-49fa-4a0a-88e5-044957a91299",
    "NAME_AS_PER_AADHAR": "Anmol ",
    "MERCHANT_DOB": "01/02/2001",
    "GENDER": "female",
    "AADHAR_NO_NOT_READABLE": "true",
    "AADHAR_DATA_EDITED": true,
    "GUARDIAN_NAME": "Yycjgij",
    "AADHAAR_DATA_SOURCE": "OCR"
  },
  "panMandatory": false,
  "paymentDoneForEDC": false,
  "deviceActivationCodePending": false,
  "edcActivationPollingInterval": 0,
  "fastagPaymentDone": "false",
  "turnoverbelowTaxableLimitAsGstAct": false,
  "businessAppliedForGst": false,
  "suggestedBillingAddress": [],
  "suggestedRegisteredAddress": [],
  "suggestedOwnerAddress": [],
  "businessProofNotRequired": false,
  "whatsappConsentAllowed": false,
  "showAccessoriesReceived": false,
  "hideOldEdcQuestionScreen": false,
  "canDiscardCurrentLead": false,
  "sbDeactivationTimeout": 0,
  "sbDeactivationPollingInterval": 0,
  "addressVerified": false,
  "redirectToQC": false,
  "upiMandateRequired": false,
  "overrideOpenForm": false,
  "redirectToUpiMandate": false,
  "addressVerificationRequired": false,
  <#if leadID??> "id":"${leadID}",</#if>
  "merchantCustId": "1737550041587232",
  "Mobile_Number_of_Customer__c": "9650376704",
  "Sub_Stage__c": "LEAD_CREATED",
  "onboard": "None of the above",
  "lastModifiedTimestamp": 0,
  "leadAcquiredTimestamp": 1735827245000,
  "leadCreationTimestamp": 0,
  "isMerchantRejected__c": false,
  <#if leadID??> "id":"${leadID}",</#if>
  "mobileNumberOfCustomer": "9650376704",
  "substage": "Lead Created",
  "userAddressLine1": "Gyxhch",
  "userAddressLine2": "Xgcu",
  "userAddressLine3": " Udtcy",
  "userAddressState": "Uttar Pradesh",
  "userAddressCity": "Gautam Buddha Nagar",
  "userAddressPincode": "201301",
  "userAddressLandMark": "",
  "partialSave": true
}



