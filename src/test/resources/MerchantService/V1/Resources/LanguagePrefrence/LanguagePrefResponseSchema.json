{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"statusCode": {"type": "integer"}, "dataValues": {"type": "array", "items": [{"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}]}}, "required": ["statusCode", "dataValues"]}