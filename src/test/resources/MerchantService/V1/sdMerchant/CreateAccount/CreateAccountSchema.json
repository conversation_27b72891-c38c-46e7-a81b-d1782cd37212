{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"displayMessage": {"type": "string"}, "refId": {"type": "string"}, "statusCode": {"type": "integer"}, "bankAccountHolderName": {"type": "string"}, "bankDetailsUuid": {"type": "string"}, "nameMatchStatus": {"type": "boolean"}}, "required": ["displayMessage", "refId", "statusCode", "bankAccountHolderName", "bankDetailsUuid", "nameMatchStatus"]}