{
  "workflowSubOperation": "ADDRESS_DETAILS",
  <#if shopRelatedBusinessUuid??> "shopRelatedBusinessUuid":"${shopRelatedBusinessUuid}", </#if>
  "addressList": [
    {
      "addressType": "RESIDENTIAL",
      "addressSubType": "CORRESPONDENCE",
      "country": "INDIA",
      "pincode": "124103",
      "state": "new state",
      "city": "testcity",
      "line1": "testline1",
      "line2": "testline2",
      "line3": "testline3",
      "addressAdditionalMetaData": {
        "OWNERSHIP": "RENTED"
      }
    }

  ],
  "solutionAdditionalMetaData": {
    <#if LENDER_ID??> "LENDER_ID":"${LENDER_ID}", </#if>
    <#if LENDER_PRODUCT_ID??> "LENDER_PRODUCT_ID":"${LENDER_PRODUCT_ID}", </#if>
    <#if LENDER_NAME??> "LENDER_NAME":"${LENDER_NAME}", </#if>
    <#if BRE_CREDIT_SCORE??> "BRE_CREDIT_SCORE":"${BRE_CREDIT_SCORE}", </#if>
    "BRE_BUREAU_TYPE": "ABCyt",
    "FAQ_URL": "www.oe.com",
    "CONTACT_US_URL": "www.google.com",
    "LENDER_CONTACT_PHONE_NUMBER": "9654279917"
  }
}