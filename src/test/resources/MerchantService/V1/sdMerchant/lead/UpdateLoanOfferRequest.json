{
   <#if workflowSubOperation??> "workflowSubOperation":"${workflowSubOperation}", </#if>
  
  "solutionAdditionalMetaData": {
  <#if LOAN_AMOUNT_IN_NUMBER??> "LOAN_AMOUNT_IN_NUMBER":"${LOAN_AMOUNT_IN_NUMBER}", </#if>
  <#if LOAN_TENURE??> "LOAN_TENURE":"${LOAN_TENURE}", </#if>
  <#if LOAN_RATE_OF_INTEREST??> "LOAN_RATE_OF_INTEREST":"${LOAN_RATE_OF_INTEREST}", </#if>
  <#if LOAN_EQUATED_DAILY_INSTALLMENT??> "LOAN_EQUATED_DAILY_INSTALLMENT":"${LOAN_EQUATED_DAILY_INSTALLMENT}", </#if>
  <#if LOAN_PROCESSING_FEE??> "LOAN_PROCESSING_FEE":"${LOAN_PROCESSING_FEE}", </#if>
   <#if LOAN_INTEREST_AMOUNT??> "LOAN_INTEREST_AMOUNT":"${LOAN_INTEREST_AMOUNT}", </#if>
   <#if LOAN_AMOUNT_IN_WORDS??> "LOAN_AMOUNT_IN_WORDS":"${LOAN_AMOUNT_IN_WORDS}", </#if>
   <#if BASE_ID??> "BASE_ID":"${BASE_ID}", </#if>
   <#if LOAN_MIN_AMOUNT??> "LOAN_MIN_AMOUNT":"${LOAN_MIN_AMOUNT}", </#if>
  <#if LOAN_MAX_AMOUNT??> "LOAN_MAX_AMOUNT":"${LOAN_MAX_AMOUNT}", </#if>
  <#if LOAN_TENURE_MIN??> "LOAN_TENURE_MIN":"${LOAN_TENURE_MIN}", </#if>
  <#if LOAN_TENURE_MAX??> "LOAN_TENURE_MAX":"${LOAN_TENURE_MAX}", </#if>
  <#if RISK_GRADE??> "RISK_GRADE":"${RISK_GRADE}", </#if>
  <#if IS_ACCEPTANCE_ABOVE_5000??> "IS_ACCEPTANCE_ABOVE_5000":${IS_ACCEPTANCE_ABOVE_5000}, </#if>
  <#if PROCESSING_FEE_RATE??> "PROCESSING_FEE_RATE":"${PROCESSING_FEE_RATE}", </#if>
  <#if IS_SI_MANDATORY??> "IS_SI_MANDATORY":${IS_SI_MANDATORY}, </#if>
  <#if IS_RESTRICTED_MERCHANT??> "IS_RESTRICTED_MERCHANT":${IS_RESTRICTED_MERCHANT}, </#if>
  <#if IS_EMANDATE_ELIGIBLE??> "IS_EMANDATE_ELIGIBLE":${IS_EMANDATE_ELIGIBLE}, </#if>
  <#if LOAN_INCENTIVE??> "LOAN_INCENTIVE":"${LOAN_INCENTIVE}", </#if>
  <#if LOAN_INCENTIVE_ELIGIBLE??> "LOAN_INCENTIVE_ELIGIBLE":"${LOAN_INCENTIVE_ELIGIBLE}", </#if>
  <#if LOAN_INCENTIVE_PERCENTAGE??> "LOAN_INCENTIVE_PERCENTAGE":"${LOAN_INCENTIVE_PERCENTAGE}", </#if>
  <#if IS_PAYTM_VINTAGE_OLDER_THAN_90D??> "IS_PAYTM_VINTAGE_OLDER_THAN_90D":${IS_PAYTM_VINTAGE_OLDER_THAN_90D} </#if>
 
  }
    
}