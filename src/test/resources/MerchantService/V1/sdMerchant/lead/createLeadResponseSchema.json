{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"tnCSetName": {"type": "string"}, "solutionAdditionalMetaData": {"type": "object", "properties": {"LOAN_MIN_AMOUNT": {"type": "string"}, "LOAN_MAX_AMOUNT": {"type": "string"}, "BASE_ID": {"type": "string"}, "LENDER_ID": {"type": "string"}, "PRODUCT_ID": {"type": "string"}, "TNC_ADDITIONAL_PARAM": {"type": "string"}, "WHITELISTING_SOURCE": {"type": "string"}, "OFFER_START_DATE": {"type": "string"}, "OFFER_END_DATE": {"type": "string"}, "PRODUCT_TYPE": {"type": "string"}, "PRODUCT_VERSION": {"type": "string"}, "LUP_ID": {"type": "string"}, "CONVENIENCE_FEE": {"type": "string"}, "IS_SI_MANDATORY": {"type": "boolean"}, "GST": {"type": "string"}, "LENDING_DYNAMIC_TNC": {"type": "string"}, "LENDING_DYNAMIC_SECONDARY_TNC": {"type": "string"}}, "required": ["LOAN_MIN_AMOUNT", "LOAN_MAX_AMOUNT", "BASE_ID", "LENDER_ID", "PRODUCT_ID", "TNC_ADDITIONAL_PARAM", "WHITELISTING_SOURCE", "OFFER_START_DATE", "OFFER_END_DATE", "PRODUCT_TYPE", "PRODUCT_VERSION", "LUP_ID", "CONVENIENCE_FEE", "IS_SI_MANDATORY", "GST", "LENDING_DYNAMIC_TNC", "LENDING_DYNAMIC_SECONDARY_TNC"]}}, "required": ["tnCSetName", "solutionAdditionalMetaData"]}