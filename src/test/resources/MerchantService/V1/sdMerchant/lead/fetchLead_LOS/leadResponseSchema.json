{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"displayMessage": {"type": "string"}, "refId": {"type": "string"}, "statusCode": {"type": "integer"}, "business": {"type": "object", "properties": {"entityType": {"type": "string"}, "businessOwnerName": {"type": "string"}, "category": {"type": "string"}, "subCategory": {"type": "string"}, "pan": {"type": "string"}, "addresses": {"type": "array", "items": {}}, "documents": {"type": "array", "items": {}}, "name": {"type": "string"}, "businessNameAsPerPan": {"type": "string"}, "businessAdditionalInfos": {"type": "object"}}, "required": ["entityType", "businessOwnerName", "category", "subCategory", "pan", "addresses", "documents", "name", "businessNameAsPerPan", "businessAdditionalInfos"]}, "stage": {"type": "string"}, "leadCreationTime": {"type": "integer"}, "editableFields": {"type": "object", "properties": {"tierType": {"type": "boolean"}, "storeNumber": {"type": "boolean"}, "brandAssociation": {"type": "boolean"}, "businessSpocEmail": {"type": "boolean"}, "subSegment": {"type": "boolean"}, "marketPlaceSegment": {"type": "boolean"}, "corporateMobile": {"type": "boolean"}, "shopAddress": {"type": "boolean"}, "iecCode": {"type": "boolean"}, "MERCHANT_DOB": {"type": "boolean"}, "storePincode": {"type": "boolean"}, "closingTime": {"type": "boolean"}, "beneficiaryName": {"type": "boolean"}, "MERCHANT_DOI": {"type": "boolean"}, "segment": {"type": "boolean"}, "marketPlaceSubSegment": {"type": "boolean"}, "annualTurnover": {"type": "boolean"}, "state": {"type": "boolean"}, "ifsc": {"type": "boolean"}, "shopManagerPhone": {"type": "boolean"}, "storeManagerNumber": {"type": "boolean"}, "brandCode": {"type": "boolean"}, "nameAsPerPan": {"type": "boolean"}, "businessEmail": {"type": "boolean"}, "pincode": {"type": "boolean"}, "brandName": {"type": "boolean"}, "investmentsRange": {"type": "boolean"}, "totalEmployee": {"type": "boolean"}, "monthlyGMV": {"type": "boolean"}, "ownerDob": {"type": "boolean"}, "areaOfEnrollment": {"type": "boolean"}, "solutionTypeLevel3": {"type": "boolean"}, "solutionTypeLevel2": {"type": "boolean"}, "posRentalPlan": {"type": "boolean"}, "firstName": {"type": "boolean"}, "marketPlaceEmail": {"type": "boolean"}, "shopManagerEmail": {"type": "boolean"}, "storeAddress": {"type": "boolean"}, "bcaMerchantType": {"type": "boolean"}, "storeEmail": {"type": "boolean"}, "avgSalary": {"type": "boolean"}, "corporateEmail": {"type": "boolean"}, "storeCity": {"type": "boolean"}, "lastName": {"type": "boolean"}, "cityOfEnrollment": {"type": "boolean"}, "nameAsPerAadhar": {"type": "boolean"}, "displayName": {"type": "boolean"}, "openingTime": {"type": "boolean"}, "approverDetails": {"type": "boolean"}, "bankName": {"type": "boolean"}, "businessSpocNumber": {"type": "boolean"}, "typeOfShop": {"type": "boolean"}, "natureOfBusiness": {"type": "boolean"}, "gstin": {"type": "boolean"}, "idNumber": {"type": "boolean"}, "streetName": {"type": "boolean"}, "storeName": {"type": "boolean"}, "bankAccountNumber": {"type": "boolean"}, "warehouseEmail": {"type": "boolean"}, "email": {"type": "boolean"}, "corporateLandline": {"type": "boolean"}, "gstExemptedCategory": {"type": "boolean"}, "storeCategory": {"type": "boolean"}, "countryOfIncorporation": {"type": "boolean"}, "metroId": {"type": "boolean"}, "storeDisplayName": {"type": "boolean"}, "shopManagerName": {"type": "boolean"}, "aadharNumber": {"type": "boolean"}, "bankAccountHolderName": {"type": "boolean"}, "primaryContact": {"type": "boolean"}, "educationalQualification": {"type": "boolean"}, "storeManagerEmail": {"type": "boolean"}, "businessSpocName": {"type": "boolean"}, "storeState": {"type": "boolean"}, "businessType": {"type": "boolean"}}, "required": ["tierType", "storeNumber", "brandAssociation", "businessSpocEmail", "subSegment", "marketPlaceSegment", "corporateMobile", "shopAddress", "iecCode", "MERCHANT_DOB", "storePincode", "closingTime", "beneficiary<PERSON><PERSON>", "MERCHANT_DOI", "segment", "marketPlaceSubSegment", "annualTurnover", "state", "ifsc", "shopManagerPhone", "storeManagerNumber", "brandCode", "nameAsPerPan", "businessEmail", "pincode", "brandName", "investmentsRange", "totalEmployee", "monthlyGMV", "ownerDob", "areaOfEnrollment", "solutionTypeLevel3", "solutionTypeLevel2", "posRentalPlan", "firstName", "marketPlaceEmail", "shopManagerEmail", "storeAddress", "bcaMerchantType", "storeEmail", "avgSalary", "corporateEmail", "storeCity", "lastName", "cityOfEnrollment", "nameAs<PERSON>er<PERSON><PERSON><PERSON>", "displayName", "openingTime", "approverDetails", "bankName", "businessSpocNumber", "typeOfShop", "natureOfBusiness", "gstin", "idNumber", "streetName", "storeName", "bankAccountNumber", "warehouseEmail", "email", "corporateLandline", "gstExemptedCategory", "storeCategory", "countryOfIncorporation", "metroId", "storeDisplayName", "shopManagerName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bankAccountHolderName", "primaryContact", "educationalQualification", "storeManagerEmail", "businessSpocName", "storeState", "businessType"]}, "rejectedFields": {"type": "object"}, "businessLeadStageId": {"type": "integer"}, "suggestedBusinessAddresses": {"type": "array", "items": {}}, "suggestedRelatedBusinesses": {"type": "array", "items": [{"type": "object", "properties": {"address": {"type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "country": {"type": "string"}, "pincode": {"type": "integer"}, "state": {"type": "string"}, "city": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "documents": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "docProvided": {"type": "string"}, "pageNumber": {"type": "string"}, "docAdditionalInfo": {"type": "object", "properties": {"1": {"type": "string"}, "DOC_COUNT": {"type": "string"}}, "required": ["1", "DOC_COUNT"]}}, "required": ["docType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageNumber", "docAdditionalInfo"]}]}, "addressType": {"type": "string"}, "addressSubType": {"type": "string"}, "addressUuid": {"type": "string"}, "addressAdditionalMetaData": {"type": "object", "properties": {"OWNERSHIP": {"type": "string"}}, "required": ["OWNERSHIP"]}, "status": {"type": "integer"}}, "required": ["refId", "statusCode", "country", "pincode", "state", "city", "line1", "line2", "line3", "latitude", "longitude", "documents", "addressType", "addressSubType", "addressUuid", "addressAdditionalMetaData", "status"]}, "relatedBusinessUuid": {"type": "string"}, "editableFields": {"type": "object", "properties": {"vehicleName": {"type": "boolean"}, "numberOfYearsInBusiness": {"type": "boolean"}, "distanceToNearestBranchBankInKms": {"type": "boolean"}, "openingTime": {"type": "boolean"}, "vehicleRegistrationId": {"type": "boolean"}, "typeOfOwner": {"type": "boolean"}, "gstin": {"type": "boolean"}, "nameOfOwner": {"type": "boolean"}, "languagePreference": {"type": "boolean"}, "closingTime": {"type": "boolean"}, "vehicleSubType": {"type": "boolean"}, "fuelType": {"type": "boolean"}, "staffSize": {"type": "boolean"}, "mobileNumberOfOwner": {"type": "boolean"}, "emailOfCustomer": {"type": "boolean"}, "alternateNumberOfCustomer": {"type": "boolean"}, "storeWidth": {"type": "boolean"}, "computerPresentAtShop": {"type": "boolean"}, "nameOfShop": {"type": "boolean"}, "vehicleType": {"type": "boolean"}, "averageDailyCustomerWalkins": {"type": "boolean"}}, "required": ["vehicleName", "numberOfYearsInBusiness", "distanceToNearestBranchBankInKms", "openingTime", "vehicleRegistrationId", "typeOfOwner", "gstin", "nameOfOwner", "languagePreference", "closingTime", "vehicleSubType", "fuelType", "staffSize", "mobileNumberOfOwner", "emailOfCustomer", "alternateNumberOfCustomer", "storeWidth", "computerPresentAtShop", "nameOfShop", "vehicleType", "averageDailyCustomerWalkins"]}, "businessAppliedForGst": {"type": "boolean"}, "leadRejected": {"type": "boolean"}}, "required": ["address", "relatedBusinessUuid", "editable<PERSON><PERSON>s", "businessAppliedForGst", "leadRejected"]}]}, "pennyDropStatus": {"type": "boolean"}, "editableFieldsKyb": {"type": "array", "items": {}}, "suggestedBanks": {"type": "array", "items": [{"type": "object", "properties": {"bankName": {"type": "string"}, "bankAccountNumber": {"type": "string"}, "ifsc": {"type": "string"}, "bankAccountHolderName": {"type": "string"}, "documents": {"type": "array", "items": {}}, "bankDetailsUuid": {"type": "string"}, "status": {"type": "integer"}, "skipBankDetails": {"type": "boolean"}}, "required": ["bankName", "bankAccountNumber", "ifsc", "bankAccountHolderName", "documents", "bankDetailsUuid", "status", "skipBankDetails"]}]}, "custId": {"type": "string"}, "declaration": {"type": "boolean"}, "lastUpdatedAt": {"type": "integer"}, "partialSave": {"type": "boolean"}, "tncAcceptingUsers": {"type": "array", "items": {}}, "inProgressAddresses": {"type": "array", "items": {}}, "panelAsyncFileUpload": {"type": "boolean"}, "primaryOwnerAdditionalInfoMap": {"type": "object"}, "solutionDocSRO": {"type": "array", "items": {}}, "panEditable": {"type": "boolean"}, "paymentPending": {"type": "boolean"}, "qcskipEligible": {"type": "boolean"}, "commissionUpgrading": {"type": "boolean"}}, "required": ["displayMessage", "refId", "statusCode", "business", "stage", "leadCreationTime", "editable<PERSON><PERSON>s", "rejectedFields", "businessLeadStageId", "suggestedBusinessAddresses", "suggestedRelatedBusinesses", "pennyDropStatus", "editable<PERSON><PERSON>sK<PERSON>b", "suggestedBanks", "custId", "declaration", "lastUpdatedAt", "partialSave", "tncAcceptingUsers", "inProgressAddresses", "panelAsyncFileUpload", "primaryOwnerAdditionalInfoMap", "solutionDocSRO", "panEditable", "paymentPending", "qcskipEligible", "commissionUpgrading"]}