{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "entityToSolutionsMap": {"type": "object", "properties": {"INDIVIDUAL": {"type": "array", "items": [{"type": "string"}]}}, "required": ["INDIVIDUAL"]}, "leadId": {"type": "string"}, "message": {"type": "string"}, "errorCode": {"type": "string"}}, "required": ["agentTncStatus", "agentKycStatus", "entityToSolutionsMap", "leadId", "message", "errorCode"]}