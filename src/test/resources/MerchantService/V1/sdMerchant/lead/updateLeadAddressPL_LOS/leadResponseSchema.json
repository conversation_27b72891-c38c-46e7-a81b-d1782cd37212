{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "relatedBusinessUuid": {"type": "string"}, "firstNameAsPerPan": {"type": "string"}, "lastNameAsPerPan": {"type": "string"}, "middleNameAsPerPan": {"type": "string"}, "mobileNumber": {"type": "string"}}, "required": ["refId", "statusCode", "relatedBusinessUuid", "firstNameAsPerPan", "lastNameAsPerPan", "middleNameAsPerPan", "mobileNumber"]}