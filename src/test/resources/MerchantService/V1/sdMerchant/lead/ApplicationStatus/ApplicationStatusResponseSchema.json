{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "bankDetailsSaved": {"type": "boolean"}, "allDocsUploaded": {"type": "boolean"}, "stage": {"type": "string"}, "paymentsDetails": {"type": "object", "properties": {"isPaymentDone": {"type": "boolean"}}}, "uploadedDocStatusList": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "docProvided": {"type": "string"}, "uuid": {"type": "null"}, "status": {"type": "string"}}}, {"type": "object", "properties": {"docType": {"type": "string"}, "docProvided": {"type": "string"}, "uuid": {"type": "string"}, "status": {"type": "string"}}}, {"type": "object", "properties": {"docType": {"type": "string"}, "docProvided": {"type": "string"}, "uuid": {"type": "string"}, "status": {"type": "string"}}}]}, "assessmentTestDetails": {"type": "object", "properties": {"testAttemptsRemaining": {"type": "integer"}, "IS_GAMEPIND_TEST_COMPLETED": {"type": "boolean"}}}}}