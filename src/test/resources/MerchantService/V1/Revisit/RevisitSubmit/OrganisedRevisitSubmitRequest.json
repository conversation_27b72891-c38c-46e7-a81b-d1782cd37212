{
  "entityType": "INDIVIDUAL",
  <#if custId??> "custId": "${custId}",</#if>
  "solution": "revisit_merchant",
  <#if storeReferenceID??> "storeReferenceID": "${storeReferenceID}",</#if>
  "channel": "GG_APP",
  "oTPValidated": false,
  "locationChanged": true,
  "address": {
    "latitude": 28.605600545146928,
    "longitude": 77.34191067516804
  },
  "questionAnswerList": [{
    "questionAlias": "Is merchant using P4B \/Dashboard?",
    "answerAlias": "Using P4B"
  }, {
    "questionAlias": "Merchandising visibilty feedback for the Outlet",
    "answerAlias": "Paytm visibility better than competition"
  }, {
    "questionAlias": "Competition at outlet",
    "answerAliasList": ["Amazon Pay", "Phonepe", "BharatPe"]
  }, {
    "questionAlias": "Outlet Name",
    "answerAlias": "Aashit Play Store"
  }, {
    "questionAlias": "City",
    "answerAlias": "Delhi"
  }, {
    "questionAlias": "store manager mobile \/outlet number",
    "answerAlias": "9953828631"
  }, {
    "questionAlias": "Store location\/address",
    "answerAlias": "Paytm F1 Noida"
  }, {
    "questionAlias": "Merchant accepting Paytm during visit?",
    "answerAlias": "Yes via QR code"
  }, {
    "questionAlias": "Have you done any deployment in this store?",
    "answerAlias": "NO"
  }, {
    "questionAlias": "store manager name",
    "answerAlias": "Aashit Sharma"
  }, {
    "questionAlias": "Is cashier trained in taking payments through Paytm?",
    "answerAlias": "YES"
  }, {
    "questionAlias": "Visit Category",
    "answerAlias": "Food"
  }, {
    "questionAlias": "Any feedback\/Compition presence offer?",
    "answerAlias": "No"
  }],
  <#if solutionRevisited??> "solutionRevisited": "${solutionRevisited}",</#if>
  <#if shopId??> "shopId": "${shopId}",</#if>
  <#if mid??> "mid": "${mid}",</#if>
  "edcBasedMid": false,
  <#if category??> "category": "${category}",</#if>
  "competitorPresent": false,
  "blueOfferQrPhotoPresent": false,

  <#if kybBusinessId??> "kybBusinessId": "${kybBusinessId}",</#if>
  <#if kybContractId??> "kybContractId": "${kybContractId}",</#if>
  <#if kybSolution??> "kybSolution": "${kybSolution}",</#if>

  "oldLatitude": 13.11,
  "oldLongitude": 47.453,
  "solutionTypeLevel3": "organized",
  <#if fseRole??> "fseRole": "${fseRole}",</#if>
  <#if fseSubRole??> "fseSubRole": "${fseSubRole}",</#if>
  "merchantType": "Merchant",
  "typeOfRevisit": "mismatch"
}

