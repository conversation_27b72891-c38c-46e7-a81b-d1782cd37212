{
  "entityType": "INDIVIDUAL",
  <#if custId??> "custId": "${custId}",</#if>
  "solution": "revisit_merchant",
  <#if mobileNumber??> "mobileNumber": "${mobileNumber}",</#if>
  "channel": "GG_APP",
  "oTPValidated": "false",
  "locationChanged": false,
  "questionAnswerList": [{
    "questionAlias": "What are the issues faced by the merchants",
    "answerAliasList": ["Increase in Card transactions over Paytm"]
  }, {
    "questionAlias": "Remarks",
    "answerAlias": "aashit"
  }, {
    "questionAlias": "Type of shop",
    "answerAlias": "Non-fixed"
  }, {
    "questionAlias": "Is shop owned or rented?",
    "answerAlias": "Rented"
  }],
  <#if solutionRevisited??> "solutionRevisited": "${solutionRevisited}",</#if>
  <#if shopId??> "shopId": "${shopId}",</#if>
  <#if mid??> "mid": "${mid}",</#if>
  "edcBasedMid": false,
  "competitorPresent": false,
  "blueOfferQrPhotoPresent": false,
  <#if kybBusinessId??> "kybBusinessId": "${kybBusinessId}",</#if>
  <#if kybContractId??> "kybContractId": "${kybContractId}",</#if>
  <#if kybSolution??> "kybSolution": "${kybSolution}",</#if>
  "oldLatitude": 28.605581666666662,
  "oldLongitude": 77.34200833333333,
  "solutionTypeLevel3": "unorganized",
  <#if fseRole??> "fseRole": "${fseRole}",</#if>
  <#if fseSubRole??> "fseSubRole": "${fseSubRole}",</#if>
  "merchantType": "Company",
  "address": {
    "latitude": 28.605581666666662,
    "longitude": 77.34200833333333
  }
}