{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "custId": {"type": "integer"}, "solutionAddressMap": {"type": "object", "properties": {"p2p_100k": {"type": "array", "items": [{"type": "object", "properties": {"solutionSubType": {"type": "string"}, "address": {"type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "country": {"type": "string"}, "pincode": {"type": "integer"}, "state": {"type": "string"}, "city": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "landmark": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "documents": {"type": "array", "items": {}}, "addressType": {"type": "string"}, "addressSubType": {"type": "string"}, "status": {"type": "integer"}}, "required": ["refId", "statusCode", "country", "pincode", "state", "city", "line1", "line2", "line3", "landmark", "latitude", "longitude", "documents", "addressType", "addressSubType", "status"]}, "displayName": {"type": "string"}, "shopId": {"type": "string"}, "mid": {"type": "string"}, "category": {"type": "string"}, "storeReferenceID": {"type": "string"}, "merchantType": {"type": "string"}, "allowRevisit": {"type": "boolean"}, "shopAddressProofPresent": {"type": "boolean"}, "regionAddressRequired": {"type": "boolean"}, "solutionTypeLevel3": {"type": "string"}, "kybContractId": {"type": "string"}, "fseRole": {"type": "string"}, "fseSubRole": {"type": "string"}, "kybBusinessId": {"type": "string"}, "kybSolution": {"type": "string"}, "edcbaseMid": {"type": "boolean"}, "revisitAllowedForFseTeam": {"type": "boolean"}}, "required": ["solutionSubType", "address", "displayName", "shopId", "mid", "category", "storeReferenceID", "merchantType", "allowRevisit", "shopAddressProofPresent", "regionAddressRequired", "solutionTypeLevel3", "kybContractId", "fseRole", "fseSubRole", "kybBusinessId", "kybSolution", "edcbaseMid", "revisitAllowedForFseTeam"]}]}}, "required": ["p2p_100k"]}, "locationValidationDistance": {"type": "integer"}, "kyc": {"type": "boolean"}}, "required": ["refId", "statusCode", "custId", "solutionAddressMap", "locationValidationDistance", "kyc"]}