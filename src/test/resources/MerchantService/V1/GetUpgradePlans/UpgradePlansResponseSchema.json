{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "merchantPlanDetails": {"type": "array", "items": [{"type": "object", "properties": {"planType": {"type": "string"}, "allowPaymodeInsert": {"type": "boolean"}, "docsRequired": {"type": "boolean"}, "selected": {"type": "boolean"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "string"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}}, "required": ["planType", "allowPaymodeInsert", "docsRequired", "selected", "commercials"]}, {"type": "object", "properties": {"planType": {"type": "string"}, "allowPaymodeInsert": {"type": "boolean"}, "docsRequired": {"type": "boolean"}, "selected": {"type": "boolean"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "string"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "string"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}}, "required": ["planType", "allowPaymodeInsert", "docsRequired", "selected", "commercials"]}, {"type": "object", "properties": {"planType": {"type": "string"}, "allowPaymodeInsert": {"type": "boolean"}, "docsRequired": {"type": "boolean"}, "selected": {"type": "boolean"}, "commercials": {"type": "array", "items": [{"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "string"}, "slabOneStartRange": {"type": "null"}, "slabOneEndRange": {"type": "null"}, "slabOnePercentCommission": {"type": "null"}, "slabTwoStartRange": {"type": "null"}, "slabTwoEndRange": {"type": "null"}, "slabTwoPercentCommission": {"type": "null"}, "transactionType": {"type": "string"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}, {"type": "object", "properties": {"paymode": {"type": "string"}, "feeType": {"type": "string"}, "percentCommission": {"type": "null"}, "slabOneStartRange": {"type": "string"}, "slabOneEndRange": {"type": "string"}, "slabOnePercentCommission": {"type": "string"}, "slabTwoStartRange": {"type": "string"}, "slabTwoEndRange": {"type": "string"}, "slabTwoPercentCommission": {"type": "string"}, "transactionType": {"type": "string"}, "paymodeDisplayName": {"type": "string"}}, "required": ["paymode", "feeType", "percentCommission", "slabOneStartRange", "slabOneEndRange", "slabOnePercentCommission", "slabTwoStartRange", "slabTwoEndRange", "slabTwoPercentCommission", "transactionType", "paymodeDisplayName"]}]}}, "required": ["planType", "allowPaymodeInsert", "docsRequired", "selected", "commercials"]}]}}, "required": ["agentTncStatus", "merchantPlanDetails"]}