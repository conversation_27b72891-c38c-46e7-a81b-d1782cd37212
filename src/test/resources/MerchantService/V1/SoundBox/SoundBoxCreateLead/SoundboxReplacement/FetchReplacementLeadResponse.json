{"agentTncStatus": true, "agentKycStatus": true, "merchantDetails": {"solutionType": "sound_box", "solutionTypeLevel2": "sound_box_replacement", "merchantCallApi": true, "merchantOpenForm": true, "merchantEditable": false, "leadStageBucket": "Active", "editableFields": {"soundBoxFieldsGroup": true}, "nameOfBusiness": "<PERSON>", "rejectedFields": {}, "rejectionReasons": {}, "suggestedBusinessAddresses": [{"address": {"refId": "ZUB-49", "statusCode": 0, "pincode": 152002, "postalCode": 0, "state": "Punjab", "city": "Firozepur", "line1": "G<PERSON><PERSON>", "line2": "<PERSON><PERSON>", "line3": "<PERSON>", "latitude": 0.0, "longitude": 0.0, "documents": [], "addressType": "BUSINESS", "addressSubType": "REGISTERED", "addressUuid": "dcea4e5b-4143-4f7c-9c17-ccca084c12f9", "status": 0, "qcRequired": false}, "editableFields": {}, "businessAppliedForGst": false, "leadRejected": false}, {"address": {"refId": "ZUB-49", "statusCode": 0, "pincode": 411027, "postalCode": 0, "state": "Maharashtra", "city": "Pimpri", "line1": "test", "line2": "test", "line3": "test", "latitude": 24.574827, "longitude": 77.727589, "documents": [], "addressType": "BUSINESS", "addressSubType": "CORRESPONDENCE", "addressUuid": "396b089f-e7a0-4df8-a9d0-6efd641c1cc9", "status": 0, "qcRequired": false}, "editableFields": {}, "businessAppliedForGst": false, "leadRejected": false}], "suggestedRelatedBusinesses": [], "suggestedBanks": [], "relatedBusinessUuid": "f2207a62-59b0-4a40-9dab-8343905d8500", "upiLinkEnabled": false, "pennyDropDetails": {"bankName": null, "bankAccountNumber": null, "bankAccountHolderName": null, "ifsc": null, "kycName": null, "pennyDropStatus": false, "nameMatchStatus": false, "uuid": null, "documents": [], "upiAccountId": null, "beneficiaryName": null, "accountType": null}, "entityType": "INDIVIDUAL", "oSV": false, "fATCADeclared": false, "midEDCOrConverted": true, "questionAnswerList": [{"questionAlias": "Reason For Replacement", "answerAlias": "service_replacement", "answerAliasList": null}], "nameOnPan": "TOUCH WOOD LIMITED", "solutionAdditionalInfo": {"MERCHANT_NAME_IN_PG": "Demo merchant", "PIDF_INCENTIVE_STATUS": "PAYTM_TARGETTED_OPPORUNITY", "IS_SB_UPGRADE_FLOW": "false", "IS_FULFILMENT_NOT_REQUIRED": "false", "MODEL": "Made In India Soundbox 3.0", "IS_OMS_FLOW": "true", "ATS_SUBPART_FAILURE_REASON": "Asset validation failed (Ref: A-ZUA-49-500)", "EXISTING_QR_USED": "FALSE", "POS_IDS": "DEFAULT", "IS_SB_PAYMENT_REQUIRED": "false", "SOLUTION_TYPE_LEVEL_3": "sound_box_3_0_2g", "PG_MID": "HySHnd27878673398759", "SOLUTION_TYPE_LEVEL_2": "sound_box_replacement", "PAYMENT_QR_BYPASSED": "TRUE", "LEFT_ATTEMPTS_FOR_DEVICE_SERIALISTAION": "3", "SB_REPLACEMENT_REASON_ALIAS": "service_replacement", "SB_PRE_QR_VALIDATION_REQUIRED": "TRUE", "SB_DEVICE_ID": "************", "TYPE": "2G", "PIDF_FLOW_ENABLED": "FALSE", "SB_REPLACEMENT_REASON": "Service replacement"}, "panMandatory": false, "paymentDoneForEDC": false, "deviceActivationCodePending": false, "edcActivationPollingInterval": 0, "fastagPaymentDone": "false", "turnoverbelowTaxableLimitAsGstAct": false, "businessAppliedForGst": false, "suggestedBillingAddress": [], "suggestedRegisteredAddress": [], "suggestedOwnerAddress": [], "businessProofNotRequired": false, "grantedWhatsAppConsent": true, "whatsappConsentAllowed": false, "showAccessoriesReceived": false, "hideOldEdcQuestionScreen": false, "canDiscardCurrentLead": true, "sbDeactivationTimeout": 50000, "sbDeactivationPollingInterval": 10000, "addressVerified": false, "redirectToQC": false, "upiMandateRequired": false, "overrideOpenForm": false, "redirectToUpiMandate": false, "businessAdditionalInfos": {}, "addressVerificationRequired": false, "Category__c": "BFSI", "Sub_Category__c": "Loans", "Id": "a5ea03e1-c64f-48e0-85d3-95d87b2e16af", "merchantCustId": "1001788031", "Mobile_Number_of_Customer__c": "7771110999", "panNumber": "**********", "Name_of_Shop__c": "Demo merchant", "Sub_Stage__c": "LEAD_CREATED", "onboard": "None of the above", "lastModifiedTimestamp": 0, "leadAcquiredTimestamp": 1717767308000, "leadCreationTimestamp": 0, "isMerchantRejected__c": false, "category": "BFSI", "mobileNumberOfCustomer": "7771110999", "subCategory": "Loans", "nameOfShop": "Demo merchant", "substage": "Lead Created", "id": "a5ea03e1-c64f-48e0-85d3-95d87b2e16af"}, "errorCode": "200"}