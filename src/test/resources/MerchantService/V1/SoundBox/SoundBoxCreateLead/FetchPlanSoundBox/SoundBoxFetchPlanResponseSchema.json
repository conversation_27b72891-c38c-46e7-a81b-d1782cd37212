{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"typeToModelPriceMap": {"type": "object", "properties": {"2G": {"type": "object", "properties": {"PSB1": {"type": "array", "items": [{"type": "object", "properties": {"price": {"type": "number"}, "rentalType": {"type": "string"}, "securityDeposit": {"type": "number"}, "rentalAmount": {"type": "number"}}, "required": ["price", "rentalType", "securityDeposit", "rentalAmount"]}, {"type": "object", "properties": {"price": {"type": "number"}, "rentalType": {"type": "string"}, "securityDeposit": {"type": "number"}, "rentalAmount": {"type": "number"}}, "required": ["price", "rentalType", "securityDeposit", "rentalAmount"]}]}}, "required": ["PSB1"]}}, "required": ["2G"]}, "applicableTaxes": {"type": "array", "items": [{"type": "object", "properties": {"percentage": {"type": "integer"}, "type": {"type": "string"}}, "required": ["percentage", "type"]}]}}, "required": ["typeToModelPriceMap", "applicableTaxes"]}