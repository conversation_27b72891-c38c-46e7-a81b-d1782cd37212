{
  <#if solutionType??>"solutionType": "${solutionType}",</#if>
  <#if entityType??>"entityType": "${entityType}",</#if>
  <#if solutionTypeLevel2??>"solutionTypeLevel2": "${solutionTypeLevel2}",</#if>
  <#if mid??>"mid": "${mid}",</#if>
  <#if userCustId??>"userCustId": "${userCustId}",</#if>
  <#if agentCustId??>"agentCustId": "${agentCustId}",</#if>
  <#if userMobile??>"userMobile": "${userMobile}",</#if>
  <#if deviceType??>"deviceType": "${deviceType}",</#if>
  <#if deviceCategory??>"deviceCategory": "${deviceCategory}",</#if>
  <#if isFromFSM??>"isFromFSM": "${isFromFSM}",</#if>
  "additionalInfoMap": {
  <#if deviceId??>"deviceId": "${deviceId}",</#if>
  <#if posId??>"posId": "${posId}"</#if> },
    "fulfilmentBypassQuestionAnswerList": [
    {
     <#if questionAlias??>"questionAlias": "${questionAlias}",</#if>
      <#if answerAlias??>"answerAlias": "${answerAlias}"</#if>
    }
    ]
}
