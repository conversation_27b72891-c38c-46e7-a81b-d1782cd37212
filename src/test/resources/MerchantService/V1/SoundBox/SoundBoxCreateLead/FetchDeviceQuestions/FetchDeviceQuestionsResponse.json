{"agentTncStatus": true, "questionList": [{"id": 0, "text": "Does the Merchant need new charger?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 0, "text": "yes", "optionAlias": "yes", "veto": false}, {"id": 0, "text": "no", "optionAlias": "no", "veto": false}], "status": false, "questionAlias": "newChargerCostWhenOldChargerIsNotReturned", "groupName": "device_replacement_upgrade_charger", "questionType": "device_replacement_upgrade_charger", "preRequisite": {"questionSlug": "chargerAvailableCost", "answerDisplayString": "no"}, "curable": false}, {"id": 0, "text": "Is <PERSON> returning the old charger?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 0, "text": "yes", "optionAlias": "yes", "veto": false}, {"id": 0, "text": "no", "optionAlias": "no", "veto": false}], "status": false, "questionAlias": "chargerAvailableCost", "groupName": "device_replacement_upgrade_charger", "questionType": "device_replacement_upgrade_charger", "curable": false}, {"id": 0, "text": "Does the Merchant need new charger?", "textAsPlaceholder": false, "displayType": "radio", "mandatory": true, "options": [{"id": 0, "text": "yes", "optionAlias": "yes", "veto": false}, {"id": 0, "text": "no", "optionAlias": "no", "veto": false}], "status": false, "questionAlias": "newChargerCostWhenOldChargerIsReturned", "groupName": "device_replacement_upgrade_charger", "questionType": "device_replacement_upgrade_charger", "preRequisite": {"questionSlug": "chargerAvailableCost", "answerDisplayString": "yes"}, "curable": false}], "skipCurrentQnAScreen": false}