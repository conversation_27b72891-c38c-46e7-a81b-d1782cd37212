{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"workflowSubOperation": {"type": "string"}, "addressList": {"type": "array", "items": [{"type": "object", "properties": {"addressType": {"type": "string"}, "addressSubType": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "state": {"type": "string"}, "city": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "district": {"type": "string"}}, "required": ["addressType", "addressSubType", "country", "pincode", "state", "city", "line1", "line2", "line3", "district"]}, {"type": "object", "properties": {"addressType": {"type": "string"}, "addressSubType": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "state": {"type": "string"}, "city": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "district": {"type": "string"}}, "required": ["addressType", "addressSubType", "country", "pincode", "state", "city", "line1", "line2", "line3", "district"]}, {"type": "object", "properties": {"addressType": {"type": "string"}, "addressSubType": {"type": "string"}, "country": {"type": "string"}, "pincode": {"type": "string"}, "state": {"type": "string"}, "city": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "district": {"type": "string"}}, "required": ["addressType", "addressSubType", "country", "pincode", "state", "city", "line1", "line2", "line3", "district"]}]}}, "required": ["workflowSubOperation", "addressList"]}