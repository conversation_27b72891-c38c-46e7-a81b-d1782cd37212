{
  "solution": {
    "orgMdrLineItemsDetails": {
      "payModeDetails": {
        "UPI": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "UPI",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "DC": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "DC",
          "bank": "HDFR",
          "bankDisplayName": "Rupay"
        }],
        "PAYTM_DIGITAL_CREDIT": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.50",
          "instruments": "PAYTM_DIGITAL_CREDIT",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "PPI": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.00",
          "instruments": "PPI",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "NB": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.20",
          "instruments": "NB",
          "bank": "PPBL",
          "bankDisplayName": "Paytm Payments Bank"
        }]
      }
    },
    "solutionTypeLevel2": "offline_solution",
    "solutionTypeLevel3": "pos_upi_payment_pg",
    "solutionType": "organised_merchant_product"
  },
  "bankDetails": {
    "accountType": "savings",
    "bankAccountHolderName": "AASHIT BANK",
    <#if mobile??>"mobile": "${mobile}",</#if>
    <#if bankAccountNumber??>"bankAccountNumber": "${bankAccountNumber}",</#if>
    "bankName": "INDUSIND BANK",
    "branchName": "NOIDA SECTOR 62",
    "ifsc": "INDB0000588",
    "nameMatchStatus": false,
    "pennyDropStatus": false
  },
  "business": {
    <#if entityType??>"entityType": "${entityType}",</#if>
    "name": "One 97 Limited"
  },
  <#if leadId??>"leadId": "${leadId}"</#if>

}