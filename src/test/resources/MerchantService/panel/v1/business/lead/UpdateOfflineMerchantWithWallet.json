{
  "solution": {
    "orgMdrLineItemsDetails": {
      "payModeDetails": {
        "CC": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.20",
          "instruments": "CC",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "2.30",
          "instruments": "CC",
          "bank": "AMEX",
          "bankDisplayName": "AMEX"
        }],
        "NB": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.40",
          "instruments": "NB",
          "bank": "PPBL",
          "bankDisplayName": "Paytm Payments Bank"
        }],
        "PPI": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.55",
          "instruments": "PPI",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "PAYTM_DIGITAL_CREDIT": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.90",
          "instruments": "PAYTM_DIGITAL_CREDIT",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "UPI": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1",
          "instruments": "UPI",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "DC": [{
          "feeType": "SLAB",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "2000",
          "fee": "0.50",
          "instruments": "DC",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }, {
          "feeType": "SLAB",
          "commissionType": "PERCENTAGE",
          "startRange": "2000",
          "endRange": "-1",
          "fee": "0.90",
          "instruments": "DC",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "DC",
          "bank": "HDFR",
          "bankDisplayName": "Rupay"
        }]
      }
    },
    "solutionTypeLevel2": "offline_solution",
    "solutionTypeLevel3": "payment_link",
    "solutionType": "organised_merchant_product"
  },
  "bankDetails": {
    "accountType": "savings",
    "bankAccountHolderName": "AASHIT BANK",
    <#if mobile??>"mobile": "${mobile}",</#if>
    <#if bankAccountNumber??>"bankAccountNumber": "${bankAccountNumber}",</#if>
    "bankName": "INDUSIND BANK",
    "branchName": "NOIDA SECTOR 62",
    "ifsc": "INDB0000588",
    "nameMatchStatus": false,
    "pennyDropStatus": false
  },
  "business": {
    <#if entityType??>"entityType": "${entityType}",</#if>
    "name": "One 97 Limited"
  },
  <#if leadId??>"leadId": "${leadId}"</#if>

}