{
  "solution": {
    "solutionType": "payments_organised_merchant",
    "solutionAdditionalInfo": {
      "BUSINESS_ESTABLISHMENT_DATE": "2018-08-25",
      "MERCHANT_MODEL": "Franchise",
      "SETTLEMENT_TYPE": "Aggregator Level",
      "INVOICE_TYPE": "Aggregator Level",
      "MONTHLY_INVOICE": "NO",
      "PAYOUT_REQ_AT_AGGREGATOR": "YES",
      "CHILD_CREATION_ALLOWED": "TRUE"
    }
  },
  "retailRelatedBusiness": {
    "displayName": "Paytm Merchant",
    <#if gstin??>"gstin": "${gstin}",</#if>
    "addresses": [{
      <#if cline1??>"line1": "CORR ${cline1}",</#if>
      <#if cline2??>"line2": "CORR ${cline2}",</#if>
      <#if cline3??>"line3": "CORR ${cline3}",</#if>
      "city": "<PERSON><PERSON><PERSON>",
      "pincode": "201301",
      "state": "Uttar Pradesh",
      "country": "INDIA",
      "addressType": "BUSINESS",
      "addressSubType": "CORRESPONDENCE"
    }, {
      <#if bline1??>"line1": "BILL ${bline1}",</#if>
      <#if bline2??>"line2": "BILL ${bline2}",</#if>
      <#if bline3??>"line3": "BILL ${bline3}",</#if>
      "city": "Central Delhi",
      "pincode": "110011",
      "state": "Delhi",
      "country": "INDIA",
      "addressType": "BUSINESS",
      "addressSubType": "BILLING"
    }]
  },
  "userAdditionalInfo": {
    "TITLE": "Mr",
    "KYC_NAME": "Aashit Sharma",
    "ADDRESS_PROOF_TYPE": "voterId",
    <#if ADDRESS_PROOF_NUMBER??>"ADDRESS_PROOF_NUMBER": "${ADDRESS_PROOF_NUMBER}",</#if>
    "AUTH_SIGN_DOB": "1993-08-25"
  },
  "partialSave": true,
  "business": {
    <#if entityType??>"entityType": "${entityType}",</#if>
    "name": "One 97 Limited"
  },
  <#if leadId??>"leadId": "${leadId}"</#if>
}