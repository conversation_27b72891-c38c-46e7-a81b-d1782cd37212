{
  "solution": {
    "orgMdrLineItemsDetails": {
      "payModeDetails": {
        "CC": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.99",
          "instruments": "CC",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "2.65",
          "instruments": "CC",
          "bank": "AMEX",
          "bankDisplayName": "AMEX"
        }],
        "NB": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "NB",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "NB",
          "bank": "SBI",
          "bankDisplayName": "SBI"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "NB",
          "bank": "HDFC",
          "bankDisplayName": "HDFC"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "NB",
          "bank": "PPBL",
          "bankDisplayName": "Paytm Payments Bank"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "NB",
          "bank": "ICICI",
          "bankDisplayName": "ICICI"
        }, {
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "NB",
          "bank": "AXIS",
          "bankDisplayName": "AXIS"
        }],
        "PPI": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "PPI",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "PAYTM_DIGITAL_CREDIT": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "1.75",
          "instruments": "PAYTM_DIGITAL_CREDIT",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "UPI": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "UPI",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }],
        "DC": [{
          "feeType": "SIMPLE",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "",
          "fee": "0",
          "instruments": "DC",
          "bank": "HDFR",
          "bankDisplayName": "Rupay"
        }, {
          "feeType": "SLAB",
          "commissionType": "PERCENTAGE",
          "startRange": "0",
          "endRange": "2000",
          "fee": "0.40",
          "instruments": "DC",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }, {
          "feeType": "SLAB",
          "commissionType": "PERCENTAGE",
          "startRange": "2000",
          "endRange": "-1",
          "fee": "1.12",
          "instruments": "DC",
          "bank": "DEFAULT",
          "bankDisplayName": "Default"
        }]
      },
      "txnFlowToPayModes": {
        "Payment Links": ["CC", "NB", "PPI", "PAYTM_DIGITAL_CREDIT", "UPI", "DC"],
        "Blink Checkout": ["CC", "NB", "PPI", "PAYTM_DIGITAL_CREDIT", "UPI", "DC"],
        "Subscription": ["CC", "PPI", "DC"],
        "App Invoke": ["CC", "NB", "PPI", "PAYTM_DIGITAL_CREDIT", "UPI", "DC"]
      }
    },
    "solutionTypeLevel2": "online_solution",
    "solutionTypeLevel3": "payment_gateway",
    "solutionType": "organised_merchant_product"
  },
  "bankDetails": {
    "accountType": "savings",
    "bankAccountHolderName": "AASHIT BANK",
    <#if mobile??>"mobile": "${mobile}",</#if>
    <#if bankAccountNumber??>"bankAccountNumber": "${bankAccountNumber}",</#if>
    "bankName": "INDUSIND BANK",
    "branchName": "NOIDA SECTOR 62",
    "ifsc": "INDB0000588",
    "nameMatchStatus": false,
    "pennyDropStatus": false
  },
  "business": {
    <#if entityType??>"entityType": "${entityType}",</#if>
    "name": "One 97 Limited"
  },
  <#if leadId??>"leadId": "${leadId}"</#if>

}