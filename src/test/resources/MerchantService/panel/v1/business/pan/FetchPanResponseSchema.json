{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "docDetailsSet": {"type": "array", "items": {}}, "uploadedDocDetailsSet": {"type": "array", "items": {}}, "optionalDocDetails": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "optionalDocsDetails": {"type": "array", "items": [{"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "optionalDoc": {"type": "boolean"}, "suggestiveData": {"type": "null"}, "rejectionCommentList": {"type": "null"}, "faceDetectionRequired": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "optionalDoc", "suggestiveData", "rejectionCommentList", "faceDetectionRequired"]}, {"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "optionalDoc": {"type": "boolean"}, "suggestiveData": {"type": "null"}, "rejectionCommentList": {"type": "null"}, "faceDetectionRequired": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "optionalDoc", "suggestiveData", "rejectionCommentList", "faceDetectionRequired"]}, {"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "optionalDoc": {"type": "boolean"}, "suggestiveData": {"type": "null"}, "rejectionCommentList": {"type": "null"}, "faceDetectionRequired": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "optionalDoc", "suggestiveData", "rejectionCommentList", "faceDetectionRequired"]}]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "optionalDocsDetails", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "optionalDocsDetails": {"type": "array", "items": [{"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "optionalDoc": {"type": "boolean"}, "suggestiveData": {"type": "null"}, "rejectionCommentList": {"type": "null"}, "faceDetectionRequired": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "optionalDoc", "suggestiveData", "rejectionCommentList", "faceDetectionRequired"]}]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "optionalDocsDetails", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "optionalDocsDetails": {"type": "array", "items": [{"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "optionalDoc": {"type": "boolean"}, "suggestiveData": {"type": "null"}, "rejectionCommentList": {"type": "null"}, "faceDetectionRequired": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "optionalDoc", "suggestiveData", "rejectionCommentList", "faceDetectionRequired"]}, {"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "optionalDoc": {"type": "boolean"}, "suggestiveData": {"type": "null"}, "rejectionCommentList": {"type": "null"}, "faceDetectionRequired": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "optionalDoc", "suggestiveData", "rejectionCommentList", "faceDetectionRequired"]}]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "optionalDocsDetails", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "optionalDocsDetails": {"type": "array", "items": [{"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "optionalDoc": {"type": "boolean"}, "suggestiveData": {"type": "null"}, "rejectionCommentList": {"type": "null"}, "faceDetectionRequired": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "optionalDoc", "suggestiveData", "rejectionCommentList", "faceDetectionRequired"]}]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "optionalDocsDetails", "docTypeDisplayName"]}]}, "solutionLeadId": {"type": "string"}}, "required": ["refId", "statusCode", "docDetailsSet", "uploadedDocDetailsSet", "optionalDocDetails", "solutionLeadId"]}