{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"displayMessage": {"type": "string"}, "refId": {"type": "string"}, "statusCode": {"type": "integer"}, "solution": {"type": "object", "properties": {"solutionType": {"type": "string"}, "documents": {"type": "array", "items": {}}, "nameMatchStatus": {"type": "boolean"}, "oSV": {"type": "boolean"}, "fATCADeclared": {"type": "boolean"}, "turnoverbelowTaxableLimitAsGstAct": {"type": "boolean"}, "businessAppliedForGst": {"type": "boolean"}, "creditFacilityDocRequired": {"type": "boolean"}, "captureNowSelected": {"type": "boolean"}, "retailStore": {"type": "boolean"}, "businessProofNotRequired": {"type": "boolean"}, "fastagPaymentDone": {"type": "string"}, "solutionAdditionalInfo": {"type": "object", "properties": {"LEAD_STATUS": {"type": "string"}, "SOLUTION_TYPE_LEVEL_2": {"type": "string"}, "solutionRequested": {"type": "string"}, "oeSolutionType": {"type": "string"}}, "required": ["LEAD_STATUS", "SOLUTION_TYPE_LEVEL_2", "solutionRequested", "oeSolutionType"]}, "freshKyc": {"type": "boolean"}, "reSchedule": {"type": "boolean"}, "midEDCOrConverted": {"type": "boolean"}, "gstinPresent": {"type": "boolean"}}, "required": ["solutionType", "documents", "nameMatchStatus", "oSV", "fATCADeclared", "turnoverbelowTaxableLimitAsGstAct", "businessAppliedForGst", "creditFacilityDocRequired", "captureNowSelected", "retailStore", "businessProofNotRequired", "fastagPaymentDone", "solutionAdditionalInfo", "freshKyc", "reSchedule", "midEDCOrConverted", "gstinPresent"]}, "stage": {"type": "string"}, "leadCreationTime": {"type": "integer"}, "mobileNumber": {"type": "string"}, "editableFields": {"type": "object"}, "rejectedFields": {"type": "object"}, "businessLeadStageId": {"type": "integer"}, "suggestedBusinessAddresses": {"type": "array", "items": {}}, "suggestedRelatedBusinesses": {"type": "array", "items": {}}, "leadId": {"type": "string"}, "pennyDropStatus": {"type": "boolean"}, "editableFieldsKyb": {"type": "array", "items": {}}, "suggestedBanks": {"type": "array", "items": {}}, "custId": {"type": "string"}, "declaration": {"type": "boolean"}, "lastUpdatedAt": {"type": "integer"}, "partialSave": {"type": "boolean"}, "tncAcceptingUsers": {"type": "array", "items": {}}, "inProgressAddresses": {"type": "array", "items": {}}, "panelAsyncFileUpload": {"type": "boolean"}, "primaryOwnerAdditionalInfoMap": {"type": "object"}, "solutionDocSRO": {"type": "array", "items": {}}, "panEditable": {"type": "boolean"}, "paymentPending": {"type": "boolean"}, "qcskipEligible": {"type": "boolean"}, "commissionUpgrading": {"type": "boolean"}}, "required": ["displayMessage", "refId", "statusCode", "solution", "stage", "leadCreationTime", "mobileNumber", "editable<PERSON><PERSON>s", "rejectedFields", "businessLeadStageId", "suggestedBusinessAddresses", "suggestedRelatedBusinesses", "leadId", "pennyDropStatus", "editable<PERSON><PERSON>sK<PERSON>b", "suggestedBanks", "custId", "declaration", "lastUpdatedAt", "partialSave", "tncAcceptingUsers", "inProgressAddresses", "panelAsyncFileUpload", "primaryOwnerAdditionalInfoMap", "solutionDocSRO", "panEditable", "paymentPending", "qcskipEligible", "commissionUpgrading"]}