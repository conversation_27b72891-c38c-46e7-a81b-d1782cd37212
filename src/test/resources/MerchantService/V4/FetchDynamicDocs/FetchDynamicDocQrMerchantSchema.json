{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "docDetailsSet": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"EstablishmentPhoto": {"type": "string"}}, "required": ["EstablishmentPhoto"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"cancelledChequePhoto": {"type": "string"}}, "required": ["cancelledChequePhoto"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"MerchandisePhoto1": {"type": "string"}}, "required": ["MerchandisePhoto1"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "object", "properties": {"minPages": {"type": "integer"}, "maxPages": {"type": "integer"}, "pendingPages": {"type": "integer"}, "receivedPages": {"type": "integer"}, "multiPageDocLabels": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["minPages", "maxPages", "pendingPages", "receivedPages", "multiPageDocLabels"]}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}, {"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "object", "properties": {"minPages": {"type": "integer"}, "maxPages": {"type": "integer"}, "pendingPages": {"type": "integer"}, "receivedPages": {"type": "integer"}, "multiPageDocLabels": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["minPages", "maxPages", "pendingPages", "receivedPages", "multiPageDocLabels"]}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}, {"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "object", "properties": {"minPages": {"type": "integer"}, "maxPages": {"type": "integer"}, "pendingPages": {"type": "integer"}, "receivedPages": {"type": "integer"}, "multiPageDocLabels": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["minPages", "maxPages", "pendingPages", "receivedPages", "multiPageDocLabels"]}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}, {"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "object", "properties": {"minPages": {"type": "integer"}, "maxPages": {"type": "integer"}, "pendingPages": {"type": "integer"}, "receivedPages": {"type": "integer"}, "multiPageDocLabels": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["minPages", "maxPages", "pendingPages", "receivedPages", "multiPageDocLabels"]}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}, {"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "object", "properties": {"minPages": {"type": "integer"}, "maxPages": {"type": "integer"}, "pendingPages": {"type": "integer"}, "receivedPages": {"type": "integer"}, "multiPageDocLabels": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["minPages", "maxPages", "pendingPages", "receivedPages", "multiPageDocLabels"]}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"passport": {"type": "string"}, "drivingLicense": {"type": "string"}, "voterId": {"type": "string"}, "aadhaar": {"type": "string"}, "nregaJobCard": {"type": "string"}}, "required": ["passport", "drivingLicense", "voterId", "<PERSON><PERSON><PERSON><PERSON>", "nregaJobCard"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"pan": {"type": "string"}}, "required": ["pan"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "object", "properties": {"minPages": {"type": "integer"}, "maxPages": {"type": "integer"}, "pendingPages": {"type": "integer"}, "receivedPages": {"type": "integer"}, "multiPageDocLabels": {"type": "null"}}, "required": ["minPages", "maxPages", "pendingPages", "receivedPages", "multiPageDocLabels"]}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"COI": {"type": "string"}}, "required": ["COI"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"paymentsAuthSignatoryDoc": {"type": "string"}}, "required": ["paymentsAuthSignatoryDoc"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "minimumDocs": {"type": "integer"}, "rejectedDocs": {"type": "array", "items": {}}, "possibleDocs": {"type": "array", "items": [{"type": "string"}]}, "possibleDocuments": {"type": "array", "items": [{"type": "object", "properties": {"docProvided": {"type": "string"}, "docDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "object", "properties": {"suggestiveText": {"type": "null"}, "imageUrl": {"type": "null"}}, "required": ["suggestiveText", "imageUrl"]}, "docValueDetails": {"type": "null"}}, "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "docDisplayName", "multiPageDocDetails", "suggestiveData", "docValueDetails"]}]}, "possibleDocToDisplayName": {"type": "object", "properties": {"paytmAcceptedHerePhoto1": {"type": "string"}}, "required": ["paytmAcceptedHerePhoto1"]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "minimumDocs", "rejectedDocs", "possibleDocs", "possibleDocuments", "possibleDocToDisplayName", "docTypeDisplayName"]}]}, "uploadedDocDetailsSet": {"type": "array", "items": {}}, "optionalDocDetails": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "optionalDocsDetails": {"type": "array", "items": [{"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "null"}, "optionalDoc": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "suggestiveData", "optionalDoc"]}, {"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "null"}, "optionalDoc": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "suggestiveData", "optionalDoc"]}]}, "docTypeDisplayName": {"type": "null"}}, "required": ["docType", "optionalDocsDetails", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "optionalDocsDetails": {"type": "array", "items": [{"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "null"}, "optionalDoc": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "suggestiveData", "optionalDoc"]}]}, "docTypeDisplayName": {"type": "string"}}, "required": ["docType", "optionalDocsDetails", "docTypeDisplayName"]}, {"type": "object", "properties": {"docType": {"type": "string"}, "optionalDocsDetails": {"type": "array", "items": [{"type": "object", "properties": {"docProvidedKey": {"type": "string"}, "docProvidedVal": {"type": "null"}, "rejectionReasonList": {"type": "null"}, "rejectionDataList": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "null"}, "docProvidedDisplayName": {"type": "string"}, "multiPageDocDetails": {"type": "null"}, "suggestiveData": {"type": "null"}, "optionalDoc": {"type": "boolean"}}, "required": ["doc<PERSON><PERSON><PERSON><PERSON>ey", "docProvidedVal", "rejectionReasonList", "rejectionDataList", "uuid", "status", "docProvidedDisplayName", "multiPageDocDetails", "suggestiveData", "optionalDoc"]}]}, "docTypeDisplayName": {"type": "null"}}, "required": ["docType", "optionalDocsDetails", "docTypeDisplayName"]}]}, "solutionLeadId": {"type": "string"}}, "required": ["refId", "statusCode", "docDetailsSet", "uploadedDocDetailsSet", "optionalDocDetails", "solutionLeadId"]}