{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "nameMatchStatus": {"type": "boolean"}, "bankAccountHolderName": {"type": "string"}, "pennydropStatus": {"type": "boolean"}, "message": {"type": "string"}, "errorCode": {"type": "string"}}, "required": ["agentTncStatus", "agentKycStatus", "nameMatchStatus", "pennydropStatus", "message", "errorCode"]}