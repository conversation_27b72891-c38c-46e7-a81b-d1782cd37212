{"errorCode": 200, "message": "SUCCESS (BETt-209)", "requestUUID": "DATA_ENTRY_ACTION_PENDING-1a577df6-2020-4fa4-a495-852b1a9e4778", "refresh": false, "leadDetails": {"uploadDocumentList": null, "businessOwnerDetails": {"custId": "**********", "mobileNumber": "5648410608", "businessOwnerName": "<PERSON><PERSON><PERSON> business owner", "pan": null, "emailId": null, "emailByCompany": null, "level": 0, "alternateNumber": "", "languagePreference": "English", "kycName": null, "businessAs": "Aashit Name of business", "aadharName": null, "panName": null, "ovdName": null, "kycStatus": null, "aadhaarStatus": null, "panStatus": null, "ownershipTypeSet": null, "empId": null, "designation": null, "nameMatchSuccess": null, "isSIMandatory": null, "gender": null, "dob": "1993-08-25", "ownerAddress": {"line1": "Building owner", "line2": "Street Owner", "line3": "Sector Owner", "pincode": 110011, "city": "Central Delhi", "state": "Delhi", "lattitude": 0, "longitude": 0, "pincode_override": 110011}, "name": null, "form60Status": null, "kycType": null, "vdc": null, "nameOfCustomer": null, "emailOfCustomer": null, "applicantName": null, "uuid": null, "lastName": null, "firstName": null}, "businessOwnerDetailList": null, "authorizedSignatoryList": null, "businessEntityDetails": {"entityType": "INDIVIDUAL", "solution": "p2p_100k", "solutionTypeLevel2": "nonMinKyc", "solutionTypeLevel3": null, "businessName": "Aashit Name of business", "displayName": "Aashit Name of business", "category": "Entertainment", "subCategory": "Game Parlour", "gstn": null, "vehicleNumber": null, "vehicleType": null, "vehicleSubType": null, "fuelType": null, "ownerType": null, "ownerName": null, "ownerMobileNumber": null, "tierType": null, "educationQualification": null, "openingTime": null, "closingTime": null, "pgMID": null, "pgUserName": null, "pgMBID": null, "bcaAgentType": null, "incorporationDate": null, "businessCommencementDate": null, "incorporationCountry": null, "incorporationPlace": null, "entityPan": null, "residenceCountry": null, "fatcaDeclaration": null, "nameAsPerNSDL": null, "avgSalary": null, "totalEmployee": null, "lobId": null, "riskCategory": null, "lineOfBusiness": null, "lobStatus": null, "websiteURL": null, "appURL": null, "segment": null, "subSegment": null, "industry": null, "bankingIndustry": null, "bankingSubCategory": null, "marketPlaceMerchantId": null, "marketPlaceEmailId": null, "pgSolutionSubType": null, "pgSolutions": null, "legalName": null, "productOfferingType": null, "isBusinessProofNotRequired": null, "businessAddress": null, "billingAddress": null, "lobReason": null}, "leadInfo": {"source": "GG_APP", "fulfilment": "GG_APP", "stage": "DATA_ENTRY", "subStage": "DATA_ENTRY_ACTION_PENDING", "leadNumber": "1a577df6-2020-4fa4-a495-852b1a9e4778", "applicantMobileNumber": null, "applicantName": null, "applicantCustId": null, "resellerService": null, "resellerPortal": null, "message": null, "rejectedByDE": null, "creditBankFacilityWithOtherBankStatus": null, "systemResponse": null, "instrumentToUpdate": null, "orgId": null, "contractId": null, "accountNumber": null, "agentComment": null, "accountType": null, "primaryCustId": "**********", "channel": "GG_APP", "workflowStatusId": ********, "leadCreationReason": null, "createdTimeStamp": "17-09-2019 10:15:36", "modifiedTimeStamp": "17-09-2019 10:15:43", "dataValidationTimestamp": null}, "bankDetails": {"bankName": "INDUSIND BANK", "bankAccountNumber": "***************", "ifscCode": "ANDB0002029", "bankAccountHolder": "AASHIT SHARMA", "beneficiaryName": null, "branchName": null, "status": null, "nameMatchStatus": null, "reEnterAccountNumber": null, "reEnterIfscCode": null, "accountType": null, "pennyDropStatus": null}, "addressDetails": {"line1": "Paytm F1", "line2": "Near B 121", "line3": "Sector 6", "pincode": 201301, "city": "<PERSON><PERSON>am Buddha Nagar", "state": "Uttar Pradesh", "lattitude": 28.**************, "longitude": 77.**************, "pincode_override": 201301}, "communicationAddress": null, "permanentAddress": null, "shopAddress": null, "cfaAgent": null, "vaAgent": null, "auditTrail": {"creationAppVersion": "3.7.8", "creationLat": null, "creationLong": null, "creationClient": "androidapp", "submissionLat": null, "submissionLong": null, "submissionAppVersion": "3.7.8", "submissionDeviceID": "OPPO-CPH1859-***************", "submissionMacAddress": null, "submissionImei": "***************", "submissionMake": "OPPO", "submissionModel": "CPH1859", "submissionOS": null, "osv": true, "osvTime": "2019-09-17 10:15:43.562"}, "additionalDetails": {"numberOfYearsInBusiness": null, "storeWidth": null, "distanceToNearestBranchBankInKms": null, "staffSize": null, "computerPresentAtShop": null, "averageDailyCustomerWalkins": null, "willingToBuyBiometricDevice": null, "distanceToNearestAggregationPoint": null, "additionalQuestions": null, "shopManagerName": null, "shopManagerPhone": null, "shopManagerEmail": null, "posRentalPlan": null, "socialNetworkURL": null, "businessSpocName": null, "businessSpocEmail": null, "storeName": null, "storeAddress": null, "storeEmail": null, "storeNumber": null, "storeState": null, "storeCity": null, "storePincode": null, "riskScore": null, "segment": null, "subSegment": null, "qrCodeId": null, "brandCode": null, "brandName": null, "businessCheck": null, "rejectedBy": null, "businessCheckRejectionReason": null, "monthlyGMV": null, "tncSets": ["https://kyc-staging.paytm.com/shorturl/9vQ"], "edcSerialNumber": null, "storeManagerEmail": null, "businessEmail": null, "idNumber": null, "brandAssociation": null, "storeDisplayName": null, "storeCategory": null, "planType": null, "merchantDeviceInfo": null, "type": null, "model": null, "price": 0, "rentalType": null, "appliedTaxes": null, "fastagTagSequenceNum": null, "fastagVehicleClass": null, "fastagVehicleType": null, "fastagPricingScheme": null, "fastagChannel": null, "fastagPaymentDone": null, "vehicleNumber": null, "transactionStatus": 0, "leadId": null, "qrCodePath": null, "orderId": null, "mid": null, "amount": null, "expiry": null, "pgMid": null, "pgPlusMid": null, "pos_ids": null, "merchantId": null, "deviceBindingInfoList": null, "currVmnCount": null, "vmnCountSoFar": null, "vmnResponse": null, "midPlan": null, "isBusinessProofNotRequired": null, "gstExemptedCategory": null, "isTurnOverBelowTaxableIncome": null, "isBusinessAppliedForGST": null, "isSmallMerchantDeclaration": null, "edcModel": null, "edcPrice": 0, "edcAppliedTaxes": null, "edcType": null, "edcSimDetails": null, "upiFlag": null, "edcMachineMake": null, "midExist": null, "edcMidExist": null, "midExistTimestamp": null, "edcMidExistTimestamp": null, "securityAmount": 0, "rentalAmount": 0, "authorisedSignatory": null, "businessName": null, "businessType": null, "nameAsPerBank": null, "stampPaperNumber": null, "typeOfShop": "Kuccha shop", "commissionBooking": null, "commissionLmb": null, "commissionPlb": null, "commissionFph": null, "settlementType": null, "displayName": null, "contactName": null, "contactNumber": null, "altPhone": null, "website": null, "propertyType": null, "propertyName": null, "chainName": null, "starRating": null, "gstVerified": null, "accountName": null, "legalEntityName": null, "relationshipManagerDetails": null, "diyPanelLoginUrl": null, "kycPanelTncViewUrl": null, "primaryOwnerAccountCreationUrl": null, "emailId": null, "hotelAmenities": null, "propertyTheme": null, "checkOutTime": null, "checkInTime": null, "roomType": null, "noOfRooms": null, "roomName": null, "numberOfRoomsThisType": null, "bedType": null, "totalOccupancy": null, "roomSize": null, "roomAmenties": null, "roomDescription": null, "description": null, "mealPlan": null, "planStartDate": null, "planEnddate": null, "primaryDesignation": null, "additionalPersonName": null, "additionalHotel": null, "additionalHotelNumber": null, "email": null, "addtionalDesignation": null, "catalogueProductId": null, "hotelId": null, "additionalEmail": null, "parentAccountLink": null, "merchantNamePG": null, "businessNamePG": null, "primaryUserDetailsPG": null, "nameMatchStatusWithMerchantName": null, "nameMatchStatusWithBusinessName": null, "nameMatchStatusWithPrimaryDetails": null, "leadStatus": null, "warehouseEmail": null, "commissionPercent": null, "sapCode": null, "warehouseId": null, "warehouseName": "Aashit Name of business", "qrStatus": null, "pgReferenceId": null, "tcsWaiver": null, "ckycName": null, "nsdlName": null, "ckycDOB": "1993-08-25", "ckycGender": null, "ckycEmailId": null, "ckycNameMatchStatus": null, "ckycCity": "<PERSON><PERSON>am Buddha Nagar", "ckycCountry": null, "ckycPincode": "201301", "ckycState": null, "ckycLine1": "Paytm F1", "ckycLine2": "Near B 121", "ckycLine3": "Sector 6", "preDisbursalPendingReason": null, "leadSubStatus": null, "ckycId": null, "ppiLimit": null, "giftVoucherPlan": null, "storeCode": null, "msmeNumber": null, "lmd": null, "lmsLoanApplicationUserMessage": null, "lmsLoanApplicationSystemMessage": null, "rejectionReason": null, "metroId": null, "wholeSalerStoreId": null, "wholesalerStoreName": null, "creationType": null, "referLeadStatus": null, "name": null, "message": null, "solutionRequested": null, "queryType": null, "queryType2": null, "pageURL": null, "leadBusinessType": null, "numberOfBranches": null, "details": null, "includeMeForNewsletter": null}, "vetoAdditionalQuestions": null, "additionalQuestions": null, "documents": [{"name": "Paytm Accepted Here Sticker", "type": "Paytm Accepted Here Sticker", "documentType": "paytmAcceptedHerePhoto", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "paytmAcceptedHerePhoto1", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM28251074192", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM28251074192"]}, {"name": "Q<PERSON> <PERSON>", "type": "Q<PERSON> <PERSON>", "documentType": "qrStickerPhoto", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "qrSticker1", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM282511164603", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM282511164603"]}, {"name": "Blue Offer QR Photo", "type": "Blue Offer QR Photo", "documentType": "blueOfferQrPhoto", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "blueOfferQrPhoto", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM28250536987", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM28250536987"]}, {"name": "Merchant HelpDesk <PERSON>er", "type": "Merchant HelpDesk <PERSON>er", "documentType": "merchantHelpdeskSticker", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "merchantHelpdeskSticker", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM28250892787", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM28250892787"]}, {"name": "Cancelled Cheque Photo", "type": "Cancelled Cheque Photo", "documentType": "cancelledChequePhoto", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "cancelledChequePhoto", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM282506139090", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM282506139090"]}, {"name": "Driving License", "type": "Driving License", "documentType": "poi", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "drivingLicense", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM282507103451", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM282507103451"]}, {"name": "Shop Front Photo", "type": "Shop Front Photo", "documentType": "shopFrontPhoto", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "shopFrontPhoto", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM282512158335", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM282512158335"]}, {"name": "Business Owner Photo", "type": "Business Owner Photo", "documentType": "businessOwnerPhoto", "placeOfIssue": null, "dateOfIssue": null, "docProvided": "businessOwnerPhoto", "updatedDocProvided": null, "osv": true, "docValue": null, "expiryDate": null, "uuid": "DM282509113985", "status": null, "rejectionReason": null, "docSubType": null, "action": null, "uuids": ["DM282509113985"]}], "agentDetails": [{"agentCustId": "1000540528", "agentMobile": "9654279917", "agentName": "jyoti", "agencyType": null, "agentTeam": null, "agentType": "DATA_ENTRY", "agencyName": null}, {"agentCustId": "1107234113", "agentMobile": null, "agentName": "AASHIT SHARMA", "agencyType": "CFA", "agentTeam": "Corporate Alliance", "agentType": "CFA", "agencyName": "Test"}], "timelineDetail": [{"subStage": "LEAD_CREATED", "successTime": "2019-09-17 10:15:38", "workflowStatusId": 52954793, "isActive": false}, {"subStage": "LEAD_POSTED", "successTime": "2019-09-17 10:15:58", "workflowStatusId": 52954795, "isActive": false}, {"subStage": "DOCUMENT_UPLOADED", "successTime": "2019-09-17 10:16:17", "workflowStatusId": 52954797, "isActive": false}, {"subStage": "MAQUETTE_FRAUD_CHECK_SUCCESS", "successTime": "2019-09-17 10:16:18", "workflowStatusId": 52954799, "isActive": false}, {"subStage": "DATA_ENTRY_ACTION_PENDING", "successTime": "2019-09-17 10:16:04", "workflowStatusId": ********, "isActive": true}], "nocStatus": null, "docAcceptanceMap": {"qrStickerPhoto": 1, "blueOfferQrPhoto": 1, "shopFrontPhoto": 1, "businessOwnerPhoto": 1, "cancelledChequePhoto": 1, "poi": 1, "merchantHelpdeskSticker": 1, "paytmAcceptedHerePhoto": 1}, "slabDataList": null, "userConfiguration": null, "userKycDetailsSRO": null, "mandatoryParams": null, "cmtParams": null, "pgParams": null, "bankInstrumentSROList": [], "creditBankFacilitiesDetails": null, "suggestedBankDetailList": null, "suggestedAddressList": null, "disbursementDetails": {"purposeList": null, "periodDisbursement": null, "caNumberList": null}, "kycDuplicationDetails": null, "form60": null, "deletedSet": null, "preQuotes": {"make": null, "model": null, "variant": null, "rtoLocation": null, "policyExpired": null, "previousPolicyInsurer": null, "policyTenure": null, "claimsUnderPreviousPolicy": null, "yearOfRegistration": null, "lastClaimPreviousPolicy": null}, "addOns": {}, "idv": {}, "previousNCB": {}, "quotes": null, "proposal": null, "discounts": {}, "substageCount": {}, "gstDetails": null, "bankInstrumentSRO": []}, "editableFields": {"documents-qrSticker1-status": "", "addressDetails-city": "", "documents-shopFrontPhoto-status": "", "bankDetails-reEnterAccountNumber": "", "documents-shopFrontPhoto-rejectionReason": "", "businessOwnerDetails-businessAs": "", "addressDetails-state": "", "documents-paytmAcceptedHerePhoto1-rejectionReason": "", "documents-businessOwnerPhoto-status": "", "businessOwnerDetails-ownerAddress-city": "", "addressDetails-line2": "", "addressDetails-line1": "", "documents-qrSticker1-rejectionReason": "", "businessOwnerDetails-businessOwnerName": "", "businessOwnerDetails-ownerAddress-line1": "", "businessOwnerDetails-dob": "", "addressDetails-line3": "", "documents-paytmAcceptedHerePhoto1-status": "", "documents-merchantHelpdeskSticker-status": "", "documents-businessOwnerPhoto-rejectionReason": "", "businessOwnerDetails-gender": "", "additionalDetails-typeOfShop": "", "documents-drivingLicense-status": "", "documents-cancelledChequePhoto-status": "", "documents-drivingLicense-rejectionReason": "", "businessEntityDetails-segment": "", "businessOwnerDetails-ownerAddress-pincode": "", "businessEntityDetails-subSegment": "", "bankDetails-reEnterIfscCode": "", "businessOwnerDetails-ownerAddress-state": "", "documents-merchantHelpdeskSticker-rejectionReason": "", "documents-blueOfferQrPhoto-status": "", "documents-blueOfferQrPhoto-rejectionReason": "", "documents-cancelledChequePhoto-rejectionReason": "", "bankDetails-bankAccountHolder": "", "businessOwnerDetails-ownerAddress-line3": "", "addressDetails-pincode": "", "documents-drivingLicense-docValue": "", "businessOwnerDetails-ownerAddress-line2": ""}, "mandatoryFields": {"documents-qrSticker1-status": "", "addressDetails-city": "", "documents-shopFrontPhoto-status": "", "bankDetails-reEnterAccountNumber": "", "documents-shopFrontPhoto-rejectionReason": "", "businessOwnerDetails-businessAs": "", "addressDetails-state": "", "documents-paytmAcceptedHerePhoto1-rejectionReason": "", "documents-businessOwnerPhoto-status": "", "businessOwnerDetails-ownerAddress-city": "", "addressDetails-line2": "", "addressDetails-line1": "", "documents-qrSticker1-rejectionReason": "", "businessOwnerDetails-businessOwnerName": "", "businessOwnerDetails-ownerAddress-line1": "", "businessOwnerDetails-dob": "", "addressDetails-line3": "", "documents-paytmAcceptedHerePhoto1-status": "", "documents-merchantHelpdeskSticker-status": "", "documents-businessOwnerPhoto-rejectionReason": "", "businessOwnerDetails-gender": "", "additionalDetails-typeOfShop": "", "documents-drivingLicense-status": "", "documents-cancelledChequePhoto-status": "", "documents-drivingLicense-rejectionReason": "", "businessOwnerDetails-ownerAddress-pincode": "", "businessEntityDetails-subSegment": "", "bankDetails-reEnterIfscCode": "", "businessOwnerDetails-ownerAddress-state": "", "documents-merchantHelpdeskSticker-rejectionReason": "", "documents-blueOfferQrPhoto-status": "", "documents-blueOfferQrPhoto-rejectionReason": "", "documents-cancelledChequePhoto-rejectionReason": "", "bankDetails-bankAccountHolder": "", "businessOwnerDetails-ownerAddress-line3": "", "addressDetails-pincode": "", "documents-drivingLicense-docValue": "", "businessOwnerDetails-ownerAddress-line2": ""}, "deletableFields": {}, "deletedFields": null, "additionalBlockSet": [], "actionSet": ["SAVE"], "mode": "EDIT", "config": "type2", "viewMode": "full", "deletedSet": null}