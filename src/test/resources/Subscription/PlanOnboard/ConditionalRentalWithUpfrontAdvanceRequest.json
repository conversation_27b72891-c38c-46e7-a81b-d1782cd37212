{ <#if custId??>"custId": "${custId}",</#if>
  <#if mid??>"mid": "${mid}",</#if>
  <#if subscriptionType??>"subscriptionType": "${subscriptionType}",</#if>
  <#if usn??>"usn": "${usn}",</#if>
  "securityDeposit":"0",
  <#if phoneNumber??>"phoneNumber": "${phoneNumber}",</#if>
  <#if serviceType??>"serviceType": "${serviceType}",</#if>
  <#if serviceName??>"serviceName": "${serviceName}",</#if>
  "planPrice":"0",
  <#if onboardingDate??>"onboardingDate": "${onboardingDate}",</#if>
  <#if deductionStartDate??>"deductionStartDate": "${deductionStartDate}",</#if>
  <#if frequency??>"frequency": "${frequency}",</#if>
  "nextDueDate":null,
  <#if advancePeriod??>"advancePeriod": "${advancePeriod}",</#if>
  <#if proRata??>"proRata": "${proRata}",</#if>
  "endDate":null,
  "otherCharges":null,
  "userSubscriptionMetadata":null,
  <#if deductionInterval??>"deductionInterval": "${deductionInterval}",</#if>
  "conditionalParameter":"TXN_COUNT",
  "rentalRange":"[ODSFetchPlanResponse.ConditionalSlabs(startRange=0, endRange=30, amount=118.0), ODSFetchPlanResponse.ConditionalSlabs(startRange=31, endRange=-1, amount=236.0)]",
"paymentOption":"[CC, DC, EMI_CC, EMI_DC, UPI]",
  "isTid":true,
  "baseAmount":null
}