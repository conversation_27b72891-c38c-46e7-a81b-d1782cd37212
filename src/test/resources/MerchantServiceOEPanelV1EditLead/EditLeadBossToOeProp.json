{
  "deletedFields": {

  },
  "deletableFields": {

  },
  "leadDetails": {
    "businessEntityDetails": {
      "businessAssociationCheck": "verifiedAndProprietor",
      "businessVerificationStatus": "NO_BUSINESS_PROOF",
      "segment": "Food",
      "subSegment": "Restaurant"
    },
    "aadhaarVerificationDetails": {
      "aadhaarGender": "male"
    },
    "additionalDetails": {
      "editReason": "",
      "docStatusEditFlag": true
    },
    "documents": [
      {
        "name": "QR on the Counter",
        "type": "QR on the Counter",
        "documentType": "qrStickerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "qrSticker1",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidQrStickerPhoto??> "uuids": ["${uuidQrStickerPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "bankDetails": {
      "accountType": "savings",
      "bankAccountType": "Individual Account",
      "reEnterAccountNumberLastFour": "2491",
      "bankVerificationStatus": "APPROVED",
      "beneficiaryName": "ANMOL JAIN"
    },
    "businessDocuments": [
      {
        "name": "Inside Shop Photo",
        "type": "Inside Shop Photo",
        "documentType": "shopInnerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopInnerPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopInsidePhoto??> "uuids": ["${uuidShopInsidePhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Shop Photo",
        "type": "Shop Photo",
        "documentType": "shopFrontPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopFrontPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopFrontPhoto??> "uuids": ["${uuidShopFrontPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "merchantDocuments": [
      {
        "name": "Aadhaar",
        "type": "Aadhaar",
        "documentType": "poi",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "aadhaar",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidAadharPhoto1?? && uuidAadharPhoto2??>"uuids": ["${uuidAadharPhoto1}", "${uuidAadharPhoto2}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": null,
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Business Owner Photo",
        "type": "Business Owner Photo",
        "documentType": "businessOwnerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "businessOwnerPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        <#if uuidBusinessOwnerPhoto??> "uuid": "${uuidBusinessOwnerPhoto}",</#if>
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBusinessOwnerPhoto1??> "uuids": ["${uuidBusinessOwnerPhoto1}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "panVerificationDetails": {
      "panVerificationStatus": "Verified",
      "panDOB": "2004-12-06"
    },
    "riskReviewData": {
      "status": "PASS"
    },
    "leadInfo": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}