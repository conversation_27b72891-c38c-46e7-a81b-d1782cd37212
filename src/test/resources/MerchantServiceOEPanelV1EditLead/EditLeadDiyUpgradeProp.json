{
  "deletedFields": {

  },
  "deletableFields": {

  },
  "leadDetails": {
    "businessEntityDetails": {
      "businessAssociationCheck": "verifiedAndProprietor",
      "businessVerificationStatus": "NO_BUSINESS_PROOF",
      "businessAddress": {
        "line1": "fsdwew",
        "line2": "ffsww",
        "line3": "wedwdw",
        "city": "Gautam Buddha Nagar",
        "state": "Uttar Pradesh",
        "pincode": "201301",
        "country": "INDIA"
      }
    },
    "aadhaarVerificationDetails": {
      "aadhaarGender": "male",
      "aadhaarVerificationStatus": "Verified"
    },
    "additionalDetails": {
      "editReason": "",
      "docStatusEditFlag": true
    },
    "documents": [
      {
        "name": "QR on the Counter",
        "type": "QR on the Counter",
        "documentType": "qrStickerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "qrSticker1",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidQrStickerPhoto??> "uuids": ["${uuidQrStickerPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "bankDetails": {
      "accountType": "current",
      "bankAccountType": "Business Account",
      "reEnterAccountNumberLastFour": "1385",
      "bankVerificationStatus": "APPROVED",
      "beneficiaryName": "ANMOL JAIN"
    },
    "businessDocuments": [
      {
        "name": "Inside Shop Photo",
        "type": "Inside Shop Photo",
        "documentType": "shopInnerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopInnerPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopInsidePhoto??> "uuids": ["${uuidShopInsidePhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "GST certificate (Business Proof)",
        "type": "GST certificate (Business Proof)",
        "documentType": "businessProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "GSTCertificate",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBusinessProof??> "uuids": ["${uuidBusinessProof}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": "GSTCertificate",
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Shop Photo",
        "type": "Shop Photo",
        "documentType": "shopFrontPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopFrontPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopFrontPhoto??> "uuids": ["${uuidShopFrontPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Udyam/ Udhyog Aadhaar Certificate",
        "type": "Udyam/ Udhyog Aadhaar Certificate",
        "documentType": "businessProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "udyam_udhyog_aadhaar",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": "DM1424644181344",
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        "uuids": [
          "DM1424644181344"
        ],
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": "udyam_udhyog_aadhaar",
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 3
      }
    ],
    "bankDocuments": [
      {
        "name": "Bank Statement",
        "type": "Bank Statement",
        "documentType": "bankProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "BankStatement",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBankStatementPhoto??> "uuids": ["${uuidBankStatementPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "panVerificationDetails": {
      "panVerificationStatus": "Verified"
    },
    "riskReviewData": {
      "status": "PASS"
    },
    "leadDetailsSection": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}