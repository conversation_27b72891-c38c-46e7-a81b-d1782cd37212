{
  "deletedFields": {},
  "leadDetails": {
    "authorizedSignatoryList": [{
      <#if custId??> "custId": "${custId}",</#if>
      <#if mobileNumber??> "mobileNumber": "${mobileNumber}",</#if>
      "aadhaarStatus": "NA",
      "kycStatus": "false",
      "panStatus": "NA",
      "kycType": "",
      "form60Status": "false",
      "ovdName": "AASHIT SHARMA",
      "ownershipTypeSet": ["AUTHORIZED_SIGNATORY"],
      "level": null,
      "vdc": null
    }],
    "userConfiguration": {
      "userCount": "1"
    },
    "additionalDetails": {
      "editReason": ""
    },
    "documents": [{
      "name": "Paytm Accepted Here Sticker",
      "type": "Paytm Accepted Here Sticker",
      "documentType": "paytmAcceptedHerePhoto",
      "placeOfIssue": null,
      "dateOfIssue": null,
      "docProvided": "paytmAcceptedHerePhoto1",
      "updatedDocProvided": null,
      "osv": true,
      "docValue": null,
      "expiryDate": null,
      <#if uuidPaytmSticker??> "uuid": "${uuidPaytmSticker}",</#if>
      "status": "APPROVED",
      "voterIdDetails": null,
      "dlDetails": null,
      "rejectionReason": null,
      "docSubType": null,
      "action": null,
      <#if uuidPaytmSticker??> "uuids": ["${uuidPaytmSticker}"],</#if>
      "statusIndex": 0
    },
      {
      "name": "PAN",
      "type": "PAN",
      "documentType": "pan",
      "placeOfIssue": null,
      "dateOfIssue": null,
      "docProvided": "pan",
      "updatedDocProvided": null,
      "osv": true,
      "docValue": null,
      "expiryDate": null,
      <#if uuidPAN??> "uuid": "${uuidPAN}",</#if>
      "status": "APPROVED",
      "voterIdDetails": null,
      "dlDetails": null,
      "rejectionReason": null,
      "docSubType": null,
      "action": null,
      <#if uuidPAN??> "uuids": ["${uuidPAN}"],</#if>
      "statusIndex": 0
    }, {
      "name": "Registration Proof",
      "type": "Registration Proof",
      "documentType": "companyAddressProof",
      "placeOfIssue": null,
      "dateOfIssue": null,
      "docProvided": "registrationProof",
      "updatedDocProvided": null,
      "osv": true,
      "docValue": null,
      "expiryDate": null,
      <#if uuidRegProof??> "uuid": "${uuidRegProof}",</#if>
      "status": "APPROVED",
      "voterIdDetails": null,
      "dlDetails": null,
      "rejectionReason": null,
      "docSubType": null,
      "action": null,
      <#if uuidRegProof??> "uuids": ["${uuidRegProof}"],</#if>
      "statusIndex": 0
    }, {
      "name": "Merchandise Photo 1",
      "type": "Merchandise Photo",
      "documentType": "MerchandisePhoto",
      "placeOfIssue": null,
      "dateOfIssue": null,
      "docProvided": "MerchandisePhoto1",
      "updatedDocProvided": null,
      "osv": true,
      "docValue": null,
      "expiryDate": null,
      <#if uuidMerchandise??> "uuid": "${uuidMerchandise}",</#if>
      "status": "APPROVED",
      "voterIdDetails": null,
      "dlDetails": null,
      "rejectionReason": null,
      "docSubType": null,
      "action": null,
      <#if uuidMerchandise??> "uuids": ["${uuidMerchandise}"],</#if>
      "statusIndex": 0
    }, {
      "name": "Shop Establishment Photo",
      "type": "Shop Establishment Photo",
      "documentType": "shop_establish_photo",
      "placeOfIssue": null,
      "dateOfIssue": null,
      "docProvided": "EstablishmentPhoto",
      "updatedDocProvided": null,
      "osv": true,
      "docValue": null,
      "expiryDate": null,
      <#if uuidShopEst??> "uuid": "${uuidShopEst}",</#if>
      "status": "APPROVED",
      "voterIdDetails": null,
      "dlDetails": null,
      "rejectionReason": null,
      "docSubType": null,
      "action": null,
      <#if uuidShopEst??> "uuids": ["${uuidShopEst}"],</#if>
      "statusIndex": 0
    }, {
      "name": "Voter ID",
      "type": "Voter ID",
      "documentType": "poi",
      "placeOfIssue": null,
      "dateOfIssue": null,
      "docProvided": "voterId",
      "updatedDocProvided": null,
      "osv": true,
      "docValue": null,
      "expiryDate": null,
      "uuid": null,
      "status": "APPROVED",
      "voterIdDetails": null,
      "dlDetails": null,
      "rejectionReason": null,
      "docSubType": null,
      "action": null,
      <#if uuidVoterId??> "uuids": ${uuidVoterId},</#if>
      "statusIndex": 0
    }],
    "leadInfo": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}