{
  "deletedFields": {

},
"deletableFields": {

},
"leadDetails": {
"panVerificationDetails": {
"panVerificationStatus": "Verified"
},
"additionalDetails": {
"editReason": "",
"docStatusEditFlag": true
},
"documents": [
{
"name": "Signature",
"type": "Signature",
"documentType": "signature",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "signature",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
  <#if uuidSignature??> "uuid": "${uuidSignature}",</#if>
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidSignature1??> "uuids": ["${uuidSignature1}"],</#if>
"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": null,
"longitude": null,
"distance": null,
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
},
{
"name": "Aadhaar",
"type": "Aadhaar",
"documentType": "poi",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "aadhaar",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidAadharPhoto1?? && uuidAadharPhoto2??>"uuids": ["${uuidAadharPhoto1}", "${uuidAadharPhoto2}"], </#if>
"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": null,
"longitude": null,
"distance": null,
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": null,
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
},
{
"name": "FSE Agent Photo",
"type": "FSE Agent Photo",
"documentType": "businessOwnerPhoto",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "businessOwnerPhoto",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
  <#if uuidBusinessOwnerPhoto??> "uuid": "${uuidBusinessOwnerPhoto}",</#if>
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidBusinessOwnerPhoto1??> "uuids": ["${uuidBusinessOwnerPhoto1}"],</#if>
"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": null,
"longitude": null,
"distance": null,
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
}
],
"bankDetails": {
"bankAccountType": "Individual Account",
"beneficiaryName": "ANMOL JAIN"
},
"reEnterBankDetails": {
"bankVerificationStatus": "APPROVED"
},
"leadInfo": {
  <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
}
}
}