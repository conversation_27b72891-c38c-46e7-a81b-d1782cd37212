{
  "deletedFields": {

  },
  "deletableFields": {

  },
  "leadDetails": {
    "panVerificationDetails": {
      "panDOB": "1995-03-11"
    },
    "additionalDetails": {
      "editReason": "",
      "docStatusEditFlag": true
    },
    "bankDetails": {
      <#if reEnterAccountNumberLastFour??> "reEnterAccountNumberLastFour": "${reEnterAccountNumberLastFour}",</#if>
      "reEnterIfscCode": "icic0001070",
      "bankVerificationStatus": "APPROVED",
      "beneficiaryName": "ANMOL JAIN"
    },
    "merchantDocuments": [
      {
        "name": "PAN ",
        "type": "PAN ",
        "documentType": "pan",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "pan",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        <#if uuidPanPhoto??> "uuid": "${uuidPanPhoto}",</#if>
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidPanPhoto??> "uuids": ["${uuidPanPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "bankDocuments": [
      {
        "name": "Bank Statement",
        "type": "Bank Statement",
        "documentType": "bankProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "BankStatement",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBankPhoto??> "uuids": ["${uuidBankPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "riskReviewData": {
      "status": "PASS"
    },
    "businessEntityDetails": {
      "segment": "Food",
      "subSegment": "Restaurant"
    },
    "leadDetailsSection": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}