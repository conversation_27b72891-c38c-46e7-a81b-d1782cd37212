{
  "deletedFields": {

  },
  "deletableFields": {

  },
  "leadDetails": {
    "businessEntityDetails": {
      "businessAssociationCheck": "verifiedAndProprietor",
      "businessVerificationStatus": "NO_BUSINESS_PROOF",
      "segment": "Beauty and Wellness",
      "subSegment": "Gym and Fitness"
    },
    "aadhaarVerificationDetails": {
      "aadhaarGender": "male",
      "aadhaarVerificationStatus": "Verified"
    },
    "additionalDetails": {
      "editReason": "",
      "docStatusEditFlag": true
    },
    "documents": [
      {
        "name": "QR on the Counter",
        "type": "QR on the Counter",
        "documentType": "qrStickerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "qrSticker1",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidQrStickerPhoto??> "uuids": ["${uuidQrStickerPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "bankDetails": {
      "accountType": "current",
      "bankAccountType": "Business Account",
      "reEnterAccountNumberLastFour": "1334",
      "bankVerificationStatus": "APPROVED",
      "beneficiaryName": "ANMOL JAIN"
    },
    "businessDocuments": [
      {
        "name": "Inside Shop Photo",
        "type": "Inside Shop Photo",
        "documentType": "shopInnerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopInnerPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopInsidePhoto??> "uuids": ["${uuidShopInsidePhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Shop Photo",
        "type": "Shop Photo",
        "documentType": "shopFrontPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopFrontPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopFrontPhoto??> "uuids": ["${uuidShopFrontPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "merchantDocuments": [
      {
        "name": "PAN (Optional)",
        "type": "PAN",
        "documentType": "pan",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "pan",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": "DM1446945110142",
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        "uuids": ["DM1446945110142"],
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "bankDocuments": [
      {
        "name": "Bank Statement",
        "type": "Bank Statement",
        "documentType": "bankProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "BankStatement",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBankStatementPhoto??> "uuids": ["${uuidBankStatementPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "riskReviewData": {
      "status": "PASS"
    },
    "leadDetailsSection": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}