{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"errorCode": {"type": "integer"}, "message": {"type": "string"}, "requestUUID": {"type": "null"}, "refresh": {"type": "boolean"}, "leadDetails": {"type": "object", "properties": {"uploadDocumentList": {"type": "null"}, "businessOwnerDetails": {"type": "null"}, "businessOwnerDetailList": {"type": "array", "items": [{"type": "object", "properties": {"custId": {"type": "string"}, "mobileNumber": {"type": "string"}, "businessOwnerName": {"type": "null"}, "pan": {"type": "null"}, "emailId": {"type": "null"}, "emailByCompany": {"type": "null"}, "level": {"type": "integer"}, "alternateNumber": {"type": "null"}, "languagePreference": {"type": "null"}, "kycName": {"type": "null"}, "businessAs": {"type": "null"}, "aadharName": {"type": "null"}, "panName": {"type": "null"}, "ovdName": {"type": "string"}, "kycStatus": {"type": "null"}, "aadhaarStatus": {"type": "null"}, "panStatus": {"type": "null"}, "ownershipTypeSet": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}, "empId": {"type": "null"}, "designation": {"type": "null"}, "nameMatchSuccess": {"type": "null"}, "isSIMandatory": {"type": "null"}, "gender": {"type": "null"}, "dob": {"type": "null"}, "ownerAddress": {"type": "null"}, "name": {"type": "null"}, "form60Status": {"type": "null"}, "kycType": {"type": "null"}, "vdc": {"type": "null"}, "nameOfCustomer": {"type": "null"}, "emailOfCustomer": {"type": "null"}, "applicantName": {"type": "null"}, "uuid": {"type": "string"}, "lastName": {"type": "null"}, "firstName": {"type": "null"}}, "required": ["custId", "mobileNumber", "businessOwnerName", "pan", "emailId", "emailByCompany", "level", "alternateNumber", "languagePreference", "kycName", "businessAs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "panName", "ovdName", "kycStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "panStatus", "ownershipTypeSet", "empId", "designation", "nameMatchSuccess", "isSIMandatory", "gender", "dob", "owner<PERSON><PERSON><PERSON>", "name", "form60Status", "kycType", "vdc", "nameOfCustomer", "emailOfCustomer", "applicantName", "uuid", "lastName", "firstName"]}]}, "authorizedSignatoryList": {"type": "null"}, "businessEntityDetails": {"type": "object", "properties": {"entityType": {"type": "string"}, "solution": {"type": "string"}, "solutionTypeLevel2": {"type": "string"}, "solutionTypeLevel3": {"type": "null"}, "businessName": {"type": "string"}, "businessDisplayName": {"type": "null"}, "marketPlaceBusinessName": {"type": "null"}, "displayName": {"type": "string"}, "category": {"type": "string"}, "subCategory": {"type": "string"}, "gstn": {"type": "null"}, "vehicleNumber": {"type": "null"}, "vehicleType": {"type": "null"}, "vehicleSubType": {"type": "null"}, "fuelType": {"type": "null"}, "ownerType": {"type": "null"}, "ownerName": {"type": "null"}, "ownerMobileNumber": {"type": "null"}, "tierType": {"type": "null"}, "educationQualification": {"type": "null"}, "openingTime": {"type": "null"}, "closingTime": {"type": "null"}, "pgMID": {"type": "null"}, "pgUserName": {"type": "null"}, "pgMBID": {"type": "null"}, "bcaAgentType": {"type": "null"}, "incorporationDate": {"type": "null"}, "businessCommencementDate": {"type": "null"}, "incorporationCountry": {"type": "null"}, "incorporationPlace": {"type": "null"}, "entityPan": {"type": "string"}, "residenceCountry": {"type": "null"}, "fatcaDeclaration": {"type": "null"}, "nameAsPerNSDL": {"type": "string"}, "avgSalary": {"type": "null"}, "totalEmployee": {"type": "null"}, "lobId": {"type": "null"}, "riskCategory": {"type": "null"}, "lineOfBusiness": {"type": "null"}, "lobStatus": {"type": "null"}, "websiteURL": {"type": "null"}, "appURL": {"type": "null"}, "segment": {"type": "null"}, "subSegment": {"type": "null"}, "industry": {"type": "null"}, "bankingIndustry": {"type": "null"}, "bankingSubCategory": {"type": "null"}, "marketPlaceMerchantId": {"type": "null"}, "marketPlaceEmailId": {"type": "null"}, "pgSolutionSubType": {"type": "null"}, "pgSolutions": {"type": "null"}, "legalName": {"type": "null"}, "productOfferingType": {"type": "null"}, "isBusinessProofNotRequired": {"type": "string"}, "businessAddress": {"type": "object", "properties": {"line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "pincode": {"type": "integer"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "null"}, "pinNotMandatory": {"type": "null"}, "lattitude": {"type": "number"}, "longitude": {"type": "number"}, "pincode_override": {"type": "integer"}}, "required": ["line1", "line2", "line3", "pincode", "city", "state", "country", "pinNotMandatory", "lattitude", "longitude", "pincode_override"]}, "billingAddress": {"type": "object", "properties": {"line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "pincode": {"type": "integer"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "null"}, "pinNotMandatory": {"type": "null"}, "lattitude": {"type": "number"}, "longitude": {"type": "number"}, "pincode_override": {"type": "integer"}}, "required": ["line1", "line2", "line3", "pincode", "city", "state", "country", "pinNotMandatory", "lattitude", "longitude", "pincode_override"]}, "lobReason": {"type": "null"}}, "required": ["entityType", "solution", "solutionTypeLevel2", "solutionTypeLevel3", "businessName", "businessDisplayName", "marketPlaceBusinessName", "displayName", "category", "subCategory", "gstn", "vehicleNumber", "vehicleType", "vehicleSubType", "fuelType", "ownerType", "ownerName", "ownerMobileNumber", "tierType", "educationQualification", "openingTime", "closingTime", "pgMID", "pgUserName", "pgMBID", "bcaAgentType", "incorporationDate", "businessCommencementDate", "incorporationCountry", "incorporationPlace", "entityPan", "residenceCountry", "fatcaDeclaration", "nameAsPerNSDL", "avgSalary", "totalEmployee", "lobId", "riskCategory", "lineOfBusiness", "lobStatus", "websiteURL", "appURL", "segment", "subSegment", "industry", "bankingIndustry", "bankingSubCategory", "marketPlaceMerchantId", "marketPlaceEmailId", "pgSolutionSubType", "pgSolutions", "legalName", "productOfferingType", "isBusinessProofNotRequired", "businessAddress", "billing<PERSON><PERSON>ress", "lobReason"]}, "leadInfo": {"type": "object", "properties": {"source": {"type": "string"}, "fulfilment": {"type": "string"}, "stage": {"type": "string"}, "subStage": {"type": "string"}, "leadNumber": {"type": "string"}, "applicantMobileNumber": {"type": "string"}, "applicantName": {"type": "null"}, "applicantCustId": {"type": "string"}, "resellerService": {"type": "null"}, "resellerPortal": {"type": "null"}, "message": {"type": "null"}, "rejectedByDE": {"type": "null"}, "creditBankFacilityWithOtherBankStatus": {"type": "null"}, "systemResponse": {"type": "null"}, "instrumentToUpdate": {"type": "null"}, "orgId": {"type": "string"}, "contractId": {"type": "null"}, "accountNumber": {"type": "null"}, "agentComment": {"type": "null"}, "accountType": {"type": "null"}, "primaryCustId": {"type": "string"}, "channel": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "leadCreationReason": {"type": "null"}, "createdTimeStamp": {"type": "string"}, "modifiedTimeStamp": {"type": "string"}, "dataValidationTimestamp": {"type": "null"}}, "required": ["source", "fulfilment", "stage", "subStage", "leadNumber", "applicantMobileNumber", "applicantName", "applicantCustId", "resellerService", "resellerPortal", "message", "rejectedByDE", "creditBankFacilityWithOtherBankStatus", "systemResponse", "instrumentToUpdate", "orgId", "contractId", "accountNumber", "agentComment", "accountType", "primaryCustId", "channel", "workflowStatusId", "leadCreationReason", "createdTimeStamp", "modifiedTimeStamp", "dataValidationTimestamp"]}, "bankDetails": {"type": "object", "properties": {"bankName": {"type": "string"}, "bankAccountNumber": {"type": "string"}, "ifscCode": {"type": "string"}, "bankAccountHolder": {"type": "string"}, "beneficiaryName": {"type": "null"}, "branchName": {"type": "string"}, "status": {"type": "null"}, "nameMatchStatus": {"type": "null"}, "reEnterAccountNumber": {"type": "null"}, "reEnterIfscCode": {"type": "null"}, "accountType": {"type": "null"}, "pennyDropStatus": {"type": "null"}, "pgTransactionMessage": {"type": "null"}}, "required": ["bankName", "bankAccountNumber", "ifscCode", "bankAccountHolder", "beneficiary<PERSON><PERSON>", "branchName", "status", "nameMatchStatus", "reEnterAccountNumber", "reEnterIfscCode", "accountType", "pennyDropStatus", "pgTransactionMessage"]}, "addressDetails": {"type": "object", "properties": {"line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "pincode": {"type": "integer"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "null"}, "pinNotMandatory": {"type": "null"}, "lattitude": {"type": "number"}, "longitude": {"type": "number"}, "pincode_override": {"type": "integer"}}, "required": ["line1", "line2", "line3", "pincode", "city", "state", "country", "pinNotMandatory", "lattitude", "longitude", "pincode_override"]}, "communicationAddress": {"type": "null"}, "permanentAddress": {"type": "null"}, "shopAddress": {"type": "null"}, "cfaAgent": {"type": "null"}, "vaAgent": {"type": "null"}, "auditTrail": {"type": "object", "properties": {"creationAppVersion": {"type": "string"}, "creationLat": {"type": "null"}, "creationLong": {"type": "null"}, "creationClient": {"type": "string"}, "submissionLat": {"type": "null"}, "submissionLong": {"type": "null"}, "submissionAppVersion": {"type": "string"}, "submissionDeviceID": {"type": "string"}, "submissionMacAddress": {"type": "null"}, "submissionImei": {"type": "string"}, "submissionMake": {"type": "string"}, "submissionModel": {"type": "string"}, "submissionOS": {"type": "null"}, "osv": {"type": "null"}, "osvTime": {"type": "null"}, "clientIp": {"type": "null"}, "androidId": {"type": "null"}, "browserName": {"type": "null"}, "browserVersion": {"type": "null"}}, "required": ["creationAppVersion", "creationLat", "creationLong", "creationClient", "submissionLat", "submissionLong", "submissionAppVersion", "submissionDeviceID", "submissionMacAddress", "submissionImei", "submissionMake", "submissionModel", "submissionOS", "osv", "osvTime", "clientIp", "androidId", "browserName", "browserVersion"]}, "additionalDetails": {"type": "object", "properties": {"numberOfYearsInBusiness": {"type": "null"}, "storeWidth": {"type": "null"}, "distanceToNearestBranchBankInKms": {"type": "null"}, "staffSize": {"type": "null"}, "computerPresentAtShop": {"type": "null"}, "averageDailyCustomerWalkins": {"type": "null"}, "willingToBuyBiometricDevice": {"type": "null"}, "distanceToNearestAggregationPoint": {"type": "null"}, "additionalQuestions": {"type": "null"}, "shopManagerName": {"type": "null"}, "shopManagerPhone": {"type": "null"}, "shopManagerEmail": {"type": "null"}, "posRentalPlan": {"type": "null"}, "socialNetworkURL": {"type": "null"}, "businessSpocName": {"type": "null"}, "businessSpocEmail": {"type": "null"}, "storeName": {"type": "null"}, "storeAddress": {"type": "null"}, "storeEmail": {"type": "null"}, "storeNumber": {"type": "null"}, "storeState": {"type": "null"}, "storeCity": {"type": "null"}, "storePincode": {"type": "null"}, "riskScore": {"type": "null"}, "segment": {"type": "null"}, "subSegment": {"type": "null"}, "qrCodeId": {"type": "null"}, "brandCode": {"type": "null"}, "brandName": {"type": "null"}, "businessCheck": {"type": "null"}, "rejectedBy": {"type": "null"}, "businessCheckRejectionReason": {"type": "null"}, "monthlyGMV": {"type": "null"}, "tncSets": {"type": "null"}, "edcSerialNumber": {"type": "null"}, "storeManagerEmail": {"type": "null"}, "businessEmail": {"type": "null"}, "idNumber": {"type": "null"}, "brandAssociation": {"type": "null"}, "storeDisplayName": {"type": "null"}, "storeCategory": {"type": "null"}, "planType": {"type": "null"}, "kybShopReferenceId": {"type": "null"}, "merchantDeviceInfo": {"type": "null"}, "type": {"type": "null"}, "model": {"type": "null"}, "price": {"type": "number"}, "rentalType": {"type": "null"}, "appliedTaxes": {"type": "null"}, "fastagTagSequenceNum": {"type": "null"}, "fastagVehicleClass": {"type": "null"}, "fastagVehicleType": {"type": "null"}, "fastagPricingScheme": {"type": "null"}, "fastagChannel": {"type": "null"}, "fastagPaymentDone": {"type": "null"}, "vehicleNumber": {"type": "null"}, "initiatingChannel": {"type": "null"}, "transactionStatusCode": {"type": "null"}, "paymentStatus": {"type": "null"}, "kybDedupeErrorMessage": {"type": "null"}, "fastagIssuanceFailed": {"type": "null"}, "fastagIssuanceFailureCode": {"type": "null"}, "fastagIssuanceFailureReason": {"type": "null"}, "fastagFailReplay": {"type": "null"}, "qcSkip": {"type": "null"}, "fastagPaymentFailureReason": {"type": "null"}, "transactionStatus": {"type": "integer"}, "leadId": {"type": "null"}, "qrCodePath": {"type": "null"}, "orderId": {"type": "null"}, "mid": {"type": "null"}, "amount": {"type": "null"}, "expiry": {"type": "null"}, "pgMid": {"type": "null"}, "pgPlusMid": {"type": "null"}, "pos_ids": {"type": "null"}, "merchantId": {"type": "null"}, "deviceBindingInfoList": {"type": "null"}, "currVmnCount": {"type": "null"}, "vmnCountSoFar": {"type": "null"}, "vmnResponse": {"type": "null"}, "midPlan": {"type": "null"}, "isBusinessProofNotRequired": {"type": "string"}, "gstExemptedCategory": {"type": "null"}, "isTurnOverBelowTaxableIncome": {"type": "string"}, "isBusinessAppliedForGST": {"type": "string"}, "isSmallMerchantDeclaration": {"type": "string"}, "edcModel": {"type": "null"}, "edcPrice": {"type": "number"}, "edcAppliedTaxes": {"type": "null"}, "edcType": {"type": "null"}, "edcSimDetails": {"type": "null"}, "upiFlag": {"type": "null"}, "edcMachineMake": {"type": "null"}, "midExist": {"type": "null"}, "edcMidExist": {"type": "null"}, "midExistTimestamp": {"type": "null"}, "edcMidExistTimestamp": {"type": "null"}, "securityAmount": {"type": "number"}, "rentalAmount": {"type": "number"}, "authorisedSignatory": {"type": "null"}, "businessName": {"type": "null"}, "businessType": {"type": "null"}, "nameAsPerBank": {"type": "null"}, "stampPaperNumber": {"type": "null"}, "typeOfShop": {"type": "null"}, "commissionBooking": {"type": "null"}, "commissionLmb": {"type": "null"}, "commissionPlb": {"type": "null"}, "commissionFph": {"type": "null"}, "settlementType": {"type": "null"}, "displayName": {"type": "null"}, "contactName": {"type": "null"}, "contactNumber": {"type": "null"}, "altPhone": {"type": "null"}, "website": {"type": "null"}, "propertyType": {"type": "null"}, "propertyName": {"type": "null"}, "chainName": {"type": "null"}, "starRating": {"type": "null"}, "gstVerified": {"type": "null"}, "accountName": {"type": "null"}, "legalEntityName": {"type": "null"}, "relationshipManagerDetails": {"type": "null"}, "diyPanelLoginUrl": {"type": "null"}, "kycPanelTncViewUrl": {"type": "null"}, "primaryOwnerAccountCreationUrl": {"type": "null"}, "emailId": {"type": "null"}, "hotelAmenities": {"type": "null"}, "propertyTheme": {"type": "null"}, "checkOutTime": {"type": "null"}, "checkInTime": {"type": "null"}, "roomType": {"type": "null"}, "noOfRooms": {"type": "null"}, "roomName": {"type": "null"}, "numberOfRoomsThisType": {"type": "null"}, "bedType": {"type": "null"}, "totalOccupancy": {"type": "null"}, "roomSize": {"type": "null"}, "roomAmenties": {"type": "null"}, "roomDescription": {"type": "null"}, "description": {"type": "null"}, "mealPlan": {"type": "null"}, "planStartDate": {"type": "null"}, "planEnddate": {"type": "null"}, "primaryDesignation": {"type": "null"}, "additionalPersonName": {"type": "null"}, "additionalHotel": {"type": "null"}, "additionalHotelNumber": {"type": "null"}, "email": {"type": "null"}, "addtionalDesignation": {"type": "null"}, "catalogueProductId": {"type": "null"}, "hotelId": {"type": "null"}, "additionalEmail": {"type": "null"}, "parentAccountLink": {"type": "null"}, "merchantNamePG": {"type": "null"}, "businessNamePG": {"type": "null"}, "primaryUserDetailsPG": {"type": "null"}, "nameMatchStatusWithMerchantName": {"type": "null"}, "nameMatchStatusWithBusinessName": {"type": "null"}, "nameMatchStatusWithPrimaryDetails": {"type": "null"}, "leadStatus": {"type": "null"}, "warehouseEmail": {"type": "null"}, "commissionPercent": {"type": "null"}, "sapCode": {"type": "null"}, "sapMessage": {"type": "null"}, "warehouseId": {"type": "null"}, "warehouseName": {"type": "string"}, "qrStatus": {"type": "null"}, "pgReferenceId": {"type": "null"}, "tcsWaiver": {"type": "null"}, "ckycName": {"type": "null"}, "nsdlName": {"type": "null"}, "ckycDOB": {"type": "null"}, "ckycGender": {"type": "null"}, "ckycEmailId": {"type": "null"}, "ckycNameMatchStatus": {"type": "null"}, "ckycCity": {"type": "string"}, "ckycCountry": {"type": "null"}, "ckycPincode": {"type": "string"}, "ckycState": {"type": "null"}, "ckycLine1": {"type": "string"}, "ckycLine2": {"type": "string"}, "ckycLine3": {"type": "string"}, "preDisbursalPendingReason": {"type": "null"}, "leadSubStatus": {"type": "null"}, "ckycId": {"type": "null"}, "ppiLimit": {"type": "null"}, "giftVoucherPlan": {"type": "null"}, "storeCode": {"type": "null"}, "msmeNumber": {"type": "null"}, "lmd": {"type": "null"}, "externalPOSId": {"type": "null"}, "lmsLoanApplicationUserMessage": {"type": "null"}, "lmsLoanApplicationSystemMessage": {"type": "null"}, "rejectionReason": {"type": "null"}, "metroId": {"type": "null"}, "wholeSalerStoreId": {"type": "null"}, "wholesalerStoreName": {"type": "null"}, "wholesaleDiscoverability": {"type": "null"}, "creationType": {"type": "null"}, "referLeadStatus": {"type": "null"}, "name": {"type": "null"}, "message": {"type": "null"}, "solutionRequested": {"type": "null"}, "queryType": {"type": "null"}, "queryType2": {"type": "null"}, "pageURL": {"type": "null"}, "leadBusinessType": {"type": "null"}, "numberOfBranches": {"type": "null"}, "details": {"type": "null"}, "includeMeForNewsletter": {"type": "null"}, "isNotificationSuccessful": {"type": "string"}, "amc": {"type": "null"}, "notificationTimestamp": {"type": "null"}, "editReason": {"type": "null"}, "bmAdditionalDetails": {"type": "null"}, "revisitTypeEmail": {"type": "null"}, "funnelDroppedReason": {"type": "null"}, "merchantDOB": {"type": "null"}, "merchantDOI": {"type": "null"}}, "required": ["numberOfYearsInBusiness", "storeWidth", "distanceToNearestBranchBankInKms", "staffSize", "computerPresentAtShop", "averageDailyCustomerWalkins", "willingToBuyBiometricDevice", "distanceToNearestAggregationPoint", "additionalQuestions", "shopManagerName", "shopManagerPhone", "shopManagerEmail", "posRentalPlan", "socialNetworkURL", "businessSpocName", "businessSpocEmail", "storeName", "storeAddress", "storeEmail", "storeNumber", "storeState", "storeCity", "storePincode", "riskScore", "segment", "subSegment", "qrCodeId", "brandCode", "brandName", "businessCheck", "rejectedBy", "businessCheckRejectionReason", "monthlyGMV", "tncSets", "edcSerialNumber", "storeManagerEmail", "businessEmail", "idNumber", "brandAssociation", "storeDisplayName", "storeCategory", "planType", "kybShopReferenceId", "merchantDeviceInfo", "type", "model", "price", "rentalType", "appliedTaxes", "fastagTagSequenceNum", "fastagVehicleClass", "fastagVehicleType", "fastagPricingScheme", "fastagChannel", "fastagPaymentDone", "vehicleNumber", "initiatingChannel", "transactionStatusCode", "paymentStatus", "kybDedupeErrorMessage", "fastagIssuanceFailed", "fastagIssuanceFailureCode", "fastagIssuanceFailureReason", "fastagFailReplay", "qcSkip", "fastagPaymentFailureReason", "transactionStatus", "leadId", "qrCodePath", "orderId", "mid", "amount", "expiry", "pgMid", "pgPlusMid", "pos_ids", "merchantId", "deviceBindingInfoList", "currVmnCount", "vmnCountSoFar", "vmnResponse", "midPlan", "isBusinessProofNotRequired", "gstExemptedCategory", "isTurnOverBelowTaxableIncome", "isBusinessAppliedForGST", "isSmallMerchantDeclaration", "edcModel", "edcPrice", "edcAppliedTaxes", "edcType", "edcSimDetails", "upiFlag", "edcMachineMake", "midExist", "edcMidExist", "midExistTimestamp", "edcMidExistTimestamp", "securityAmount", "rentalAmount", "authorisedSignatory", "businessName", "businessType", "nameAsPerBank", "stampPaperNumber", "typeOfShop", "commissionBooking", "commissionLmb", "commissionPlb", "commissionFph", "settlementType", "displayName", "contactName", "contactNumber", "altPhone", "website", "propertyType", "propertyName", "chainName", "starRating", "gstVerified", "accountName", "legalEntityName", "relationshipManagerDetails", "diyPanelLoginUrl", "kycPanelTncViewUrl", "primaryOwnerAccountCreationUrl", "emailId", "hotelAmenities", "propertyTheme", "checkOutTime", "checkInTime", "roomType", "noOfRooms", "roomName", "numberOfRoomsThisType", "bedType", "totalOccupancy", "roomSize", "roomAmenties", "roomDescription", "description", "mealPlan", "planStartDate", "planEnddate", "primaryDesignation", "additionalPersonName", "additionalHotel", "additionalHotelNumber", "email", "addtionalDesignation", "catalogueProductId", "hotelId", "additionalEmail", "parentAccountLink", "merchantNamePG", "businessNamePG", "primaryUserDetailsPG", "nameMatchStatusWithMerchantName", "nameMatchStatusWithBusinessName", "nameMatchStatusWithPrimaryDetails", "leadStatus", "warehouseEmail", "commissionPercent", "sapCode", "sapMessage", "warehouseId", "warehouseName", "qrStatus", "pgReferenceId", "tcsWaiver", "ckycName", "nsdlName", "ckycDOB", "ckycGender", "ckycEmailId", "ckycNameMatchStatus", "ckycCity", "ckycCountry", "ckycPincode", "ckycState", "ckycLine1", "ckycLine2", "ckycLine3", "preDisbursalPendingReason", "leadSubStatus", "ckycId", "ppiLimit", "giftVoucherPlan", "storeCode", "msmeNumber", "lmd", "externalPOSId", "lmsLoanApplicationUserMessage", "lmsLoanApplicationSystemMessage", "rejectionReason", "metroId", "wholeSalerStoreId", "wholesalerStoreName", "wholesaleDiscoverability", "creationType", "referLeadStatus", "name", "message", "solutionRequested", "queryType", "queryType2", "pageURL", "leadBusinessType", "numberOfBranches", "details", "includeMeForNewsletter", "isNotificationSuccessful", "amc", "notificationTimestamp", "editReason", "bmAdditionalDetails", "revisitTypeEmail", "funnelDroppedReason", "merchantDOB", "merchantDOI"]}, "vetoAdditionalQuestions": {"type": "null"}, "additionalQuestions": {"type": "null"}, "documents": {"type": "array", "items": [{"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "string"}, "voterIdDetails": {"type": "null"}, "dlDetails": {"type": "null"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "voterIdDetails", "dlDetails", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "string"}, "status": {"type": "string"}, "voterIdDetails": {"type": "null"}, "dlDetails": {"type": "null"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "voterIdDetails", "dlDetails", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "string"}, "status": {"type": "string"}, "voterIdDetails": {"type": "null"}, "dlDetails": {"type": "null"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "voterIdDetails", "dlDetails", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "string"}, "voterIdDetails": {"type": "null"}, "dlDetails": {"type": "null"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "voterIdDetails", "dlDetails", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "string"}, "status": {"type": "string"}, "voterIdDetails": {"type": "null"}, "dlDetails": {"type": "null"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "voterIdDetails", "dlDetails", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "string"}, "status": {"type": "string"}, "voterIdDetails": {"type": "null"}, "dlDetails": {"type": "null"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "voterIdDetails", "dlDetails", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "null"}, "status": {"type": "string"}, "voterIdDetails": {"type": "null"}, "dlDetails": {"type": "null"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "voterIdDetails", "dlDetails", "rejectionReason", "docSubType", "action", "uuids"]}]}, "agentDetails": {"type": "array", "items": [{"type": "object", "properties": {"agentCustId": {"type": "string"}, "agentMobile": {"type": "null"}, "agentName": {"type": "string"}, "agencyType": {"type": "string"}, "agentTeam": {"type": "null"}, "agentType": {"type": "string"}, "agencyName": {"type": "null"}}, "required": ["agentCustId", "agentMobile", "<PERSON><PERSON><PERSON>", "agencyType", "agentTeam", "agentType", "agencyName"]}, {"type": "object", "properties": {"agentCustId": {"type": "string"}, "agentMobile": {"type": "null"}, "agentName": {"type": "string"}, "agencyType": {"type": "string"}, "agentTeam": {"type": "string"}, "agentType": {"type": "string"}, "agencyName": {"type": "null"}}, "required": ["agentCustId", "agentMobile", "<PERSON><PERSON><PERSON>", "agencyType", "agentTeam", "agentType", "agencyName"]}]}, "timelineDetail": {"type": "array", "items": [{"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}]}, "nocStatus": {"type": "null"}, "docAcceptanceMap": {"type": "object", "properties": {"companyAddressProof": {"type": "integer"}, "shop_establish_photo": {"type": "integer"}, "cancelledChequePhoto": {"type": "integer"}, "MerchandisePhoto": {"type": "integer"}, "poi": {"type": "integer"}, "pan": {"type": "integer"}, "paytmAcceptedHerePhoto": {"type": "integer"}}, "required": ["companyAddressProof", "shop_establish_photo", "cancelledChequePhoto", "MerchandisePhoto", "poi", "pan", "paytmAcceptedHerePhoto"]}, "slabDataList": {"type": "null"}, "userConfiguration": {"type": "null"}, "userKycDetailsSRO": {"type": "null"}, "mandatoryParams": {"type": "null"}, "cmtParams": {"type": "null"}, "fseParams": {"type": "null"}, "pgParams": {"type": "null"}, "bankInstrumentSROList": {"type": "array", "items": {}}, "creditBankFacilitiesDetails": {"type": "null"}, "suggestedBankDetailList": {"type": "null"}, "suggestedAddressList": {"type": "null"}, "disbursementDetails": {"type": "object", "properties": {"purposeList": {"type": "null"}, "periodDisbursement": {"type": "null"}, "caNumberList": {"type": "null"}}, "required": ["purposeList", "periodDisbursement", "caNumberList"]}, "kycDuplicationDetails": {"type": "null"}, "form60": {"type": "null"}, "deletedSet": {"type": "null"}, "preQuotes": {"type": "object", "properties": {"make": {"type": "null"}, "model": {"type": "null"}, "variant": {"type": "null"}, "rtoLocation": {"type": "null"}, "policyExpired": {"type": "null"}, "previousPolicyInsurer": {"type": "null"}, "policyTenure": {"type": "null"}, "claimsUnderPreviousPolicy": {"type": "null"}, "yearOfRegistration": {"type": "null"}, "lastClaimPreviousPolicy": {"type": "null"}}, "required": ["make", "model", "variant", "rtoLocation", "policyExpired", "previousPolicyInsurer", "policyTenure", "claimsUnderPreviousPolicy", "yearOfRegistration", "lastClaimPreviousPolicy"]}, "addOns": {"type": "object"}, "idv": {"type": "object"}, "previousNCB": {"type": "object"}, "quotes": {"type": "null"}, "proposal": {"type": "null"}, "discounts": {"type": "object"}, "substageCount": {"type": "object", "properties": {"PANEL_SUCCESS": {"type": "integer"}}, "required": ["PANEL_SUCCESS"]}, "gstDetails": {"type": "null"}, "childLead": {"type": "array", "items": {}}, "bankInstrumentSRO": {"type": "array", "items": {}}}, "required": ["uploadDocumentList", "businessOwnerDetails", "businessOwnerDetailList", "authorizedSignatoryList", "businessEntityDetails", "leadInfo", "bankDetails", "addressDetails", "communicationAddress", "<PERSON><PERSON><PERSON><PERSON>", "shopAddress", "cfaAgent", "vaAgent", "auditTrail", "additionalDetails", "vetoAdditionalQuestions", "additionalQuestions", "documents", "agentDetails", "timelineDetail", "nocStatus", "docAcceptanceMap", "slabDataList", "userConfiguration", "userKycDetailsSRO", "mandatoryParams", "cmtParams", "fseParams", "pgParams", "bankInstrumentSROList", "creditBankFacilitiesDetails", "suggestedBankDetailList", "suggestedAddressList", "disbursementDetails", "kycDuplicationDetails", "form60", "deletedSet", "preQuotes", "addOns", "idv", "previousNCB", "quotes", "proposal", "discounts", "substageCount", "gstDetails", "childLead", "bankInstrumentSRO"]}, "editableFields": {"type": "null"}, "mandatoryFields": {"type": "null"}, "deletableFields": {"type": "null"}, "deletedFields": {"type": "null"}, "maskedFields": {"type": "object", "properties": {"businessOwnerDetailList.d3967739-8408-4e1d-9468-a120fa0d088f.pan": {"type": "string"}, "additionalDetails.storeEmail": {"type": "string"}, "businessOwnerDetailList.d3967739-8408-4e1d-9468-a120fa0d088f.panName": {"type": "string"}, "businessEntityDetails.marketPlaceEmailId": {"type": "string"}, "bankDetails.bankAccountHolder": {"type": "string"}, "bankDetails.pgTransactionMessage": {"type": "string"}, "bankDetails.reEnterIfscCode": {"type": "string"}, "additionalDetails.ckycEmailId": {"type": "string"}, "additionalDetails.businessSpocEmail": {"type": "string"}, "additionalDetails.businessEmail": {"type": "string"}, "businessOwnerDetailList.d3967739-8408-4e1d-9468-a120fa0d088f.emailByCompany": {"type": "string"}, "bankDetails.status": {"type": "string"}, "bankDetails.bankName": {"type": "string"}, "bankDetails.beneficiaryName": {"type": "string"}, "leadInfo.applicantMobileNumber": {"type": "string"}, "additionalDetails.email": {"type": "string"}, "bankDetails.bankAccountNumber": {"type": "string"}, "businessOwnerDetailList.d3967739-8408-4e1d-9468-a120fa0d088f.mobileNumber": {"type": "string"}, "additionalDetails.emailId": {"type": "string"}, "businessOwnerDetailList.d3967739-8408-4e1d-9468-a120fa0d088f.emailOfCustomer": {"type": "string"}, "bankDetails.branchName": {"type": "string"}, "additionalDetails.shopManagerEmail": {"type": "string"}, "businessOwnerDetailList.d3967739-8408-4e1d-9468-a120fa0d088f.emailId": {"type": "string"}, "additionalDetails.additionalEmail": {"type": "string"}, "additionalDetails.warehouseEmail": {"type": "string"}, "businessEntityDetails.entityPan": {"type": "string"}, "bankDetails.nameMatchStatus": {"type": "string"}, "bankDetails.ifscCode": {"type": "string"}, "bankDetails.pennyDropStatus": {"type": "string"}, "bankDetails.reEnterAccountNumber": {"type": "string"}, "bankDetails.accountType": {"type": "string"}, "additionalDetails.storeManagerEmail": {"type": "string"}, "businessEntityDetails.ownerMobileNumber": {"type": "string"}}, "required": ["bankDetails.ifscCode", "bankDetails.pennyDropStatus", "bankDetails.reEnterAccountNumber"]}, "additionalBlockSet": {"type": "array", "items": {}}, "actionSet": {"type": "array", "items": [{"type": "string"}]}, "mode": {"type": "string"}, "config": {"type": "null"}, "viewMode": {"type": "string"}, "deletedSet": {"type": "null"}}, "required": ["errorCode", "message", "requestUUID", "refresh", "leadDetails", "editable<PERSON><PERSON>s", "mandatoryFields", "deletableFields", "deletedFields", "maskedFields", "additionalBlockSet", "actionSet", "mode", "config", "viewMode", "deletedSet"]}