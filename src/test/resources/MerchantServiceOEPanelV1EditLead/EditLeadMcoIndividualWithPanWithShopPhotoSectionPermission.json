{
  "deletedFields": {

  },
  "deletableFields": {

  },
  "leadDetails": {
    "businessEntityDetails": {
      "billingAddress": {
        "line1": "<PERSON><PERSON><PERSON> ,13 ,Street Sector78 ,Dr <PERSON> Clinic ,India ",
        "line2": null,
        "line3": "Sector 116 ,Dadri ,Gautambuddha Nagar District ",
        "regionType": null,
        "landmark": null,
        "region": null,
        "postalOfficeName": null,
        "city": "Gautam Buddha Nagar",
        "state": "Uttar Pradesh",
        "pinNotMandatory": null,
        "pincode": 201307,
        "country": null,
        "lattitude": 28.5682864,
        "longitude": 77.3956823,
        "shopName": "Sk189,116",
        "landmarkName": "Near footprint ",
        "formattedAddress": "Sne<PERSON>, Unnamed Road, Sector 116, Noida, Uttar Pradesh. 34 m from Dr Lucky Chandekar Clinic, Pin-201307 (India)"
      }
    },
    "panVerificationDetails": {
      "panDOB": "1992-02-12"
    },
    "additionalDetails": {
      "editReason": "",
      "shopBoardVerificationStatus": "",
      "docStatusEditFlag": true
    },
    "bankDetails": {
      "accountType": "savings",
      "bankAccountType": "Individual Account",
      "reEnterAccountNumberLastFour": "9743",
      "bankVerificationStatus": "APPROVED",
      "beneficiaryName": "ANMOL JAIN"
    },
    "merchantDocuments": [
      {
        "name": "Aadhaar ",
        "type": "Aadhaar ",
        "documentType": "poi",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "aadhaar",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidAadharPhoto1?? && uuidAadharPhoto2??>"uuids": ["${uuidAadharPhoto1}", "${uuidAadharPhoto2}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": null,
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Business Owner Photo",
        "type": "Business Owner Photo",
        "documentType": "businessOwnerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "businessOwnerPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        <#if uuidBusinessOwnerPhoto??> "uuid": "${uuidBusinessOwnerPhoto}",</#if>
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBusinessOwnerPhoto1??> "uuids": ["${uuidBusinessOwnerPhoto1}"],</#if>
        "status": "APPROVED",
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "PAN ",
        "type": "PAN ",
        "documentType": "pan",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "pan",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        <#if uuidPanPhoto??> "uuid": "${uuidPanPhoto}",</#if>
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidPanPhoto??> "uuids": ["${uuidPanPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 3
      }
    ],
    "bankDocuments": [
      {
        "name": "Cancel Cheque Photo",
        "type": "Cancel Cheque Photo",
        "documentType": "bankProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "cancelledChequePhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBankPhoto??> "uuids": ["${uuidBankPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "shopPhotoDetails": {
      "documentDetails": {
        "name": "Shop Photo",
        "type": "Shop Photo",
        "documentType": "shopFrontPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopFrontPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopPhoto??> "uuids": ["${uuidShopPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "oldMccDocProvided": null
      },
      "categoryVerificationStatus": "Verified",
      "typeOfShop": "Fixed Shop",
      "shopPhotoDocument": "SHOP_PHOTO_WITH_BOARD",
      "businessVerificationStatus": "ONLINE_VERIFIED",
      "segment": "Food",
      "subSegment": "Restaurant"
    },
    "riskReviewData": {
      "status": "PASS"
    },
    "addressDetails": {
      "shopName": "Sk189,116"
    },
    "leadDetailsSection": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}