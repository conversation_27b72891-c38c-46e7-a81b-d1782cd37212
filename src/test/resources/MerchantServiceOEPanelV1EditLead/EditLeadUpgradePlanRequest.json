{"deletedFields":{},
  "deletableFields":{},
  "leadDetails":{"businessEntityDetails":
  {
    <#if segment??>"segment": "${segment}", </#if>
    <#if subSegment??>"subSegment": "${subSegment}" </#if>

  },
    "additionalDetails":{"editReason":"","docStatusEditFlag":true},
    "documents":[{"name":"Business Invoice Document","type":"Business Invoice Document","documentType":"businessProofDocument","placeOfIssue":null,"dateOfIssue":null,"docProvided":"businessProofDocument","updatedDocProvided":null,"updatedDocType":null,"osv":true,"docValue":null,"expiryDate":null,
      <#if uuid??>"uuid": "${uuid}", </#if>
      "status":"APPROVED","voterIdDetails":null,"dlDetails":null,"rejectionReason":null,"docSubType":null,"action":null,
      <#if uuids??>"uuids":["${uuids}"],</#if>
      "comment":null,"addressType":null,"entityUuid":null,"ownerName":null,"statusIndex":0},{"name":"Business Justification Document","type":"Business Justification Document","documentType":"businessJustificationDocument","placeOfIssue":null,"dateOfIssue":null,"docProvided":"businessJustificationDocument","updatedDocProvided":null,"updatedDocType":null,"osv":true,"docValue":null,"expiryDate":null,
      <#if uuid??>"uuid": "${uuid}", </#if>
      "status":"APPROVED","voterIdDetails":null,"dlDetails":null,"rejectionReason":null,"docSubType":null,"action":null,
      <#if uuids??>"uuids":["${uuids}"],</#if>
      "comment":null,"addressType":null,"entityUuid":null,"ownerName":null,"statusIndex":0}],
    "leadInfo":{
      <#if workflowStatusId??>"workflowStatusId": "${workflowStatusId}" </#if>

    }}}