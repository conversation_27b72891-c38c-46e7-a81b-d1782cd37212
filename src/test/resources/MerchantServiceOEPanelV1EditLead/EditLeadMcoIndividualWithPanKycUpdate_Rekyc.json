{
  "deletableFields": {
  },
  "deletedFields": {
  },
  "leadDetails": {
    "additionalDetails": {
      "docStatusEditFlag": true,
      "editReason": "",
      "shopBoardVerificationStatus": ""
    },
    "bankDetails": {
      "accountType": "savings",
      "bankAccountType": "Individual Account",
      "bankVerificationStatus": "APPROVED",
      "reEnterAccountNumberLastFour": "6304"
    },
    "bankDocuments": [
      {
        "action": null,
        "additionalStatus": null,
        "addressType": null,
        "approverEmail": null,
        "comment": null,
        "containsNonRedactedAadhaar": "No",
        "dateOfIssue": null,
        "distance": null,
        "dlDetails": null,
        "docProvided": "cancelledChequePhoto",
        "docProvidedUpdated": null,
        "docSubType": null,
        "documentAuditTrail": null,
        "documentType": "bankProof",
        "docValue": null,
        "entityUuid": null,
        "expiryDate": null,
        "latitude": null,
        "longitude": null,
        "name": "Cancel Cheque Photo",
        "noExpiryDate": null,
        "oldMccDocProvided": null,
        "osv": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "ownerMobile": null,
        "ownerName": null,
        "ownershipTypeSet": null,
        "placeOfIssue": null,
        "rejectionReason": null,
        "status": "APPROVED",
        "statusIndex": 0,
        "type": "Cancel Cheque Photo",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "uuid": null,
        <#if uuidBankPhoto??> "uuids": ["${uuidBankPhoto}"],</#if>
        "voterIdDetails": null
      }
    ],
    "businessDocuments": [
      {
        "action": null,
        "additionalStatus": null,
        "addressType": null,
        "approverEmail": null,
        "comment": null,
        "containsNonRedactedAadhaar": "No",
        "dateOfIssue": null,
        "distance": "0.****************",
        "dlDetails": null,
        "docProvided": "shopFrontPhoto",
        "docProvidedUpdated": null,
        "docSubType": null,
        "documentAuditTrail": null,
        "documentType": "shopFrontPhoto",
        "docValue": null,
        "entityUuid": null,
        "expiryDate": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "name": "Shop Photo",
        "noExpiryDate": null,
        "oldMccDocProvided": null,
        "osv": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "ownerMobile": null,
        "ownerName": null,
        "ownershipTypeSet": null,
        "placeOfIssue": null,
        "rejectionReason": null,
        "status": "APPROVED",
        "statusIndex": 0,
        "type": "Shop Photo",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "uuid": null,
        <#if uuidShopPhoto??> "uuids": ["${uuidShopPhoto}"],</#if>
        "voterIdDetails": null
      }
    ],
    "businessEntityDetails": {
      "businessVerificationStatus": "Non-Verified Business Proof",
      "categoryVerificationStatus": "Verified",
      "segment": "Food",
      "shopPhotoDocument": "SHOP_PHOTO_WITH_BOARD",
      "subSegment": "Restaurant",
      "typeOfShop": "Fixed Shop"
    },
    "leadInfo": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    },
    "merchantDocuments": [
      {
        "action": null,
        "additionalStatus": null,
        "addressType": null,
        "approverEmail": null,
        "comment": null,
        "containsNonRedactedAadhaar": null,
        "dateOfIssue": null,
        "distance": "0.****************",
        "dlDetails": null,
        "docProvided": "aadhaar",
        "docProvidedUpdated": null,
        "docSubType": null,
        "documentAuditTrail": null,
        "documentType": "poi",
        "docValue": null,
        "entityUuid": null,
        "expiryDate": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "name": "Aadhaar ",
        "noExpiryDate": null,
        "oldMccDocProvided": null,
        "osv": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "ownerMobile": null,
        "ownerName": null,
        "ownershipTypeSet": null,
        "placeOfIssue": null,
        "rejectionReason": null,
        "status": "APPROVED",
        "statusIndex": 0,
        "type": "Aadhaar ",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "uuid": null,
        <#if uuidAadharPhoto1?? && uuidAadharPhoto2??>"uuids": ["${uuidAadharPhoto1}", "${uuidAadharPhoto2}"], </#if>
        "voterIdDetails": null
      },
      {
        "action": null,
        "additionalStatus": null,
        "addressType": null,
        "approverEmail": null,
        "comment": null,
        "containsNonRedactedAadhaar": "No",
        "dateOfIssue": null,
        "distance": null,
        "dlDetails": null,
        "docProvided": "businessOwnerPhoto",
        "docProvidedUpdated": null,
        "docSubType": null,
        "documentAuditTrail": null,
        "documentType": "businessOwnerPhoto",
        "docValue": null,
        "entityUuid": null,
        "expiryDate": null,
        "latitude": null,
        "longitude": null,
        "name": "Business Owner Photo",
        "noExpiryDate": null,
        "oldMccDocProvided": null,
        "osv": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "ownerMobile": null,
        "ownerName": null,
        "ownershipTypeSet": null,
        "placeOfIssue": null,
        "rejectionReason": null,
        "status": "APPROVED",
        "statusIndex": 0,
        "type": "Business Owner Photo",
        "updatedDocProvided": null,
        "updatedDocType": null,
        <#if uuidBusinessOwnerPhoto??> "uuid": "${uuidBusinessOwnerPhoto}",</#if>
        <#if uuidBusinessOwnerPhoto1??> "uuids": ["${uuidBusinessOwnerPhoto1}"],</#if>
        "voterIdDetails": null
      },
      {
        "action": null,
        "additionalStatus": null,
        "addressType": null,
        "approverEmail": null,
        "comment": null,
        "containsNonRedactedAadhaar": "No",
        "dateOfIssue": null,
        "distance": "0.****************",
        "dlDetails": null,
        "docProvided": "pan",
        "docProvidedUpdated": null,
        "docSubType": null,
        "documentAuditTrail": null,
        "documentType": "pan",
        "docValue": null,
        "entityUuid": null,
        "expiryDate": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "name": "PAN ",
        "noExpiryDate": null,
        "oldMccDocProvided": null,
        "osv": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "ownerMobile": null,
        "ownerName": null,
        "ownershipTypeSet": null,
        "placeOfIssue": null,
        "rejectionReason": null,
        "status": "APPROVED",
        "statusIndex": 3,
        "type": "PAN ",
        "updatedDocProvided": null,
        "updatedDocType": null,
        <#if uuidPanPhoto??> "uuid": "${uuidPanPhoto}",</#if>
        <#if uuidPanPhoto??> "uuids": ["${uuidPanPhoto}"],</#if>
        "voterIdDetails": null
      }
    ],
    "panVerificationDetails": {
      "panDOB": "2004-12-06"
    },
    "riskReviewData": {
      "status": "PASS"
    }
  }
}