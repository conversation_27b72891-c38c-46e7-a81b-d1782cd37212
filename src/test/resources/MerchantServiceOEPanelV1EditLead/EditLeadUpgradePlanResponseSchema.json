{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"errorCode": {"type": "integer"}, "message": {"type": "string"}, "requestUUID": {"type": "null"}, "refresh": {"type": "boolean"}, "leadDetails": {"type": "object", "properties": {"uploadDocumentList": {"type": "null"}, "businessOwnerDetails": {"type": ["null", "object"]}, "businessOwnerDetailList": {"type": ["array", "null"], "items": [{"type": "object", "properties": {"custId": {"type": "string"}, "mobileNumber": {"type": "string"}, "businessOwnerName": {"type": "null"}, "pan": {"type": "null"}, "emailId": {"type": "null"}, "emailByCompany": {"type": "null"}, "level": {"type": "integer"}, "alternateNumber": {"type": "null"}, "languagePreference": {"type": "null"}, "kycName": {"type": "null"}, "businessAs": {"type": "null"}, "aadharName": {"type": "null"}, "panName": {"type": "null"}, "ovdName": {"type": "null"}, "kycStatus": {"type": "null"}, "aadhaarStatus": {"type": "null"}, "panStatus": {"type": "null"}, "ownershipTypeSet": {"type": "array", "items": [{"type": "string"}]}, "empId": {"type": "null"}, "designation": {"type": "null"}, "nameMatchSuccess": {"type": "null"}, "isSIMandatory": {"type": "null"}, "gender": {"type": "null"}, "dob": {"type": "null"}, "ownerAddress": {"type": "null"}, "name": {"type": "null"}, "form60Status": {"type": "null"}, "kycType": {"type": "null"}, "vdc": {"type": "null"}, "nameOfCustomer": {"type": "null"}, "emailOfCustomer": {"type": "null"}, "applicantName": {"type": "null"}, "uuid": {"type": "string"}, "lastName": {"type": "null"}, "firstName": {"type": "null"}}, "required": ["custId", "mobileNumber", "businessOwnerName", "pan", "emailId", "emailByCompany", "level", "alternateNumber", "languagePreference", "kycName", "businessAs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "panName", "ovdName", "kycStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "panStatus", "ownershipTypeSet", "empId", "designation", "nameMatchSuccess", "isSIMandatory", "gender", "dob", "owner<PERSON><PERSON><PERSON>", "name", "form60Status", "kycType", "vdc", "nameOfCustomer", "emailOfCustomer", "applicantName", "uuid", "lastName", "firstName"]}]}, "authorizedSignatoryList": {"type": "null"}, "businessEntityDetails": {"type": "object", "properties": {"entityType": {"type": "string"}, "solution": {"type": "string"}, "solutionTypeLevel2": {"type": "string"}, "solutionTypeLevel3": {"type": "null"}, "businessName": {"type": "string"}, "displayName": {"type": ["null", "string"]}, "category": {"type": "string"}, "subCategory": {"type": "string"}, "gstn": {"type": "null"}, "vehicleNumber": {"type": "null"}, "vehicleType": {"type": "null"}, "vehicleSubType": {"type": "null"}, "fuelType": {"type": "null"}, "ownerType": {"type": "null"}, "ownerName": {"type": "null"}, "ownerMobileNumber": {"type": "null"}, "tierType": {"type": "null"}, "educationQualification": {"type": "null"}, "openingTime": {"type": "null"}, "closingTime": {"type": "null"}, "pgMID": {"type": "null"}, "pgUserName": {"type": "null"}, "pgMBID": {"type": "null"}, "bcaAgentType": {"type": "null"}, "incorporationDate": {"type": "null"}, "businessCommencementDate": {"type": "null"}, "incorporationCountry": {"type": "null"}, "incorporationPlace": {"type": "null"}, "entityPan": {"type": ["string", "pan"]}, "residenceCountry": {"type": "null"}, "fatcaDeclaration": {"type": "null"}, "nameAsPerNSDL": {"type": "string"}, "avgSalary": {"type": "null"}, "totalEmployee": {"type": "null"}, "lobId": {"type": "null"}, "riskCategory": {"type": "null"}, "lineOfBusiness": {"type": "null"}, "lobStatus": {"type": "null"}, "websiteURL": {"type": "null"}, "appURL": {"type": "null"}, "segment": {"type": "string"}, "subSegment": {"type": "string"}, "industry": {"type": "null"}, "bankingIndustry": {"type": "null"}, "bankingSubCategory": {"type": "null"}, "marketPlaceMerchantId": {"type": ["string", "null"]}, "marketPlaceEmailId": {"type": "null"}, "pgSolutionSubType": {"type": "null"}, "pgSolutions": {"type": "null"}, "legalName": {"type": "null"}, "productOfferingType": {"type": "null"}, "isBusinessProofNotRequired": {"type": "null"}, "businessAddress": {"type": ["object", "null"], "properties": {"line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "string"}, "pincode": {"type": "integer"}, "city": {"type": "string"}, "state": {"type": "string"}, "lattitude": {"type": "number"}, "longitude": {"type": "number"}, "pincode_override": {"type": "integer"}}, "required": ["line1", "line2", "line3", "pincode", "city", "state", "lattitude", "longitude", "pincode_override"]}, "billingAddress": {"type": "null"}, "lobReason": {"type": "null"}}, "required": ["entityType", "solution", "solutionTypeLevel2", "solutionTypeLevel3", "businessName", "displayName", "category", "subCategory", "gstn", "vehicleNumber", "vehicleType", "vehicleSubType", "fuelType", "ownerType", "ownerName", "ownerMobileNumber", "tierType", "educationQualification", "openingTime", "closingTime", "pgMID", "pgUserName", "pgMBID", "bcaAgentType", "incorporationDate", "businessCommencementDate", "incorporationCountry", "incorporationPlace", "entityPan", "residenceCountry", "fatcaDeclaration", "nameAsPerNSDL", "avgSalary", "totalEmployee", "lobId", "riskCategory", "lineOfBusiness", "lobStatus", "websiteURL", "appURL", "segment", "subSegment", "industry", "bankingIndustry", "bankingSubCategory", "marketPlaceMerchantId", "marketPlaceEmailId", "pgSolutionSubType", "pgSolutions", "legalName", "productOfferingType", "isBusinessProofNotRequired", "businessAddress", "billing<PERSON><PERSON>ress", "lobReason"]}, "leadInfo": {"type": "object", "properties": {"source": {"type": ["null", "string"]}, "fulfilment": {"type": ["null", "string"]}, "stage": {"type": "string"}, "subStage": {"type": "string"}, "leadNumber": {"type": "string"}, "applicantMobileNumber": {"type": ["string", "null"]}, "applicantName": {"type": ["null", "string"]}, "applicantCustId": {"type": ["string", "null"]}, "resellerService": {"type": "null"}, "resellerPortal": {"type": "null"}, "message": {"type": "null"}, "rejectedByDE": {"type": "null"}, "creditBankFacilityWithOtherBankStatus": {"type": "null"}, "systemResponse": {"type": "null"}, "instrumentToUpdate": {"type": "null"}, "orgId": {"type": ["string", "null"]}, "contractId": {"type": "null"}, "accountNumber": {"type": "null"}, "agentComment": {"type": "null"}, "accountType": {"type": "null"}, "primaryCustId": {"type": "string"}, "channel": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "leadCreationReason": {"type": "null"}, "createdTimeStamp": {"type": "string"}, "modifiedTimeStamp": {"type": "string"}, "dataValidationTimestamp": {"type": "null"}}, "required": ["source", "fulfilment", "stage", "subStage", "leadNumber", "applicantMobileNumber", "applicantName", "applicantCustId", "resellerService", "resellerPortal", "message", "rejectedByDE", "creditBankFacilityWithOtherBankStatus", "systemResponse", "instrumentToUpdate", "orgId", "contractId", "accountNumber", "agentComment", "accountType", "primaryCustId", "channel", "workflowStatusId", "leadCreationReason", "createdTimeStamp", "modifiedTimeStamp", "dataValidationTimestamp"]}, "bankDetails": {"type": "object", "properties": {"bankName": {"type": ["null", "string"]}, "bankAccountNumber": {"type": ["null", "string"]}, "ifscCode": {"type": ["null", "string"]}, "bankAccountHolder": {"type": ["null", "string"]}, "beneficiaryName": {"type": "null"}, "branchName": {"type": "null"}, "status": {"type": "null"}, "nameMatchStatus": {"type": "null"}, "reEnterAccountNumber": {"type": "null"}, "reEnterIfscCode": {"type": "null"}, "accountType": {"type": "null"}, "pennyDropStatus": {"type": "null"}}, "required": ["bankName", "bankAccountNumber", "ifscCode", "bankAccountHolder", "beneficiary<PERSON><PERSON>", "branchName", "status", "nameMatchStatus", "reEnterAccountNumber", "reEnterIfscCode", "accountType", "pennyDropStatus"]}, "addressDetails": {"type": "object", "properties": {"pincode": {"type": "integer"}}, "required": ["pincode"]}, "communicationAddress": {"type": ["object", "null"], "properties": {"pincode": {"type": "integer"}}, "required": ["pincode"]}, "permanentAddress": {"type": ["object", "null"], "properties": {"pincode": {"type": "integer"}}, "required": ["pincode"]}, "shopAddress": {"type": ["object", "null"], "properties": {"pincode": {"type": "integer"}}, "required": ["pincode"]}, "cfaAgent": {"type": "null"}, "vaAgent": {"type": "null"}, "auditTrail": {"type": "object", "properties": {"creationAppVersion": {"type": "string"}, "creationLat": {"type": "null"}, "creationLong": {"type": "null"}, "creationClient": {"type": "null"}, "submissionLat": {"type": "null"}, "submissionLong": {"type": "null"}, "submissionAppVersion": {"type": "null"}, "submissionDeviceID": {"type": "null"}, "submissionMacAddress": {"type": "null"}, "submissionImei": {"type": "null"}, "submissionMake": {"type": "null"}, "submissionModel": {"type": "null"}, "submissionOS": {"type": "null"}, "osv": {"type": "null"}, "osvTime": {"type": "null"}}, "required": ["creationAppVersion", "creationLat", "creationLong", "creationClient", "submissionLat", "submissionLong", "submissionAppVersion", "submissionDeviceID", "submissionMacAddress", "submissionImei", "submissionMake", "submissionModel", "submissionOS", "osv", "osvTime"]}, "additionalDetails": {"type": "object", "properties": {"numberOfYearsInBusiness": {"type": "null"}, "storeWidth": {"type": "null"}, "distanceToNearestBranchBankInKms": {"type": "null"}, "staffSize": {"type": "null"}, "computerPresentAtShop": {"type": "null"}, "averageDailyCustomerWalkins": {"type": "null"}, "willingToBuyBiometricDevice": {"type": "null"}, "distanceToNearestAggregationPoint": {"type": "null"}, "additionalQuestions": {"type": "null"}, "shopManagerName": {"type": "null"}, "shopManagerPhone": {"type": "null"}, "shopManagerEmail": {"type": "null"}, "posRentalPlan": {"type": "null"}, "socialNetworkURL": {"type": "null"}, "businessSpocName": {"type": "null"}, "businessSpocEmail": {"type": "null"}, "storeName": {"type": "null"}, "storeAddress": {"type": "null"}, "storeEmail": {"type": "null"}, "storeNumber": {"type": "null"}, "storeState": {"type": "null"}, "storeCity": {"type": "null"}, "storePincode": {"type": "null"}, "riskScore": {"type": "null"}, "segment": {"type": "null"}, "subSegment": {"type": "null"}, "qrCodeId": {"type": "null"}, "brandCode": {"type": "null"}, "brandName": {"type": "null"}, "businessCheck": {"type": "null"}, "rejectedBy": {"type": "null"}, "businessCheckRejectionReason": {"type": "null"}, "monthlyGMV": {"type": "null"}, "tncSets": {"type": "null"}, "edcSerialNumber": {"type": "null"}, "storeManagerEmail": {"type": "null"}, "businessEmail": {"type": "null"}, "idNumber": {"type": "null"}, "brandAssociation": {"type": "null"}, "storeDisplayName": {"type": "null"}, "storeCategory": {"type": "null"}, "planType": {"type": "null"}, "merchantDeviceInfo": {"type": "null"}, "type": {"type": "null"}, "model": {"type": "null"}, "price": {"type": "number"}, "rentalType": {"type": "null"}, "appliedTaxes": {"type": "null"}, "fastagTagSequenceNum": {"type": "null"}, "fastagVehicleClass": {"type": "null"}, "fastagVehicleType": {"type": "null"}, "fastagPricingScheme": {"type": "null"}, "fastagChannel": {"type": "null"}, "fastagPaymentDone": {"type": "null"}, "vehicleNumber": {"type": "null"}, "transactionStatus": {"type": "integer"}, "leadId": {"type": "null"}, "qrCodePath": {"type": "null"}, "orderId": {"type": "null"}, "mid": {"type": "null"}, "amount": {"type": "null"}, "expiry": {"type": "null"}, "pgMid": {"type": "string"}, "pgPlusMid": {"type": "null"}, "pos_ids": {"type": "null"}, "merchantId": {"type": "null"}, "deviceBindingInfoList": {"type": "null"}, "currVmnCount": {"type": "null"}, "vmnCountSoFar": {"type": "null"}, "vmnResponse": {"type": "null"}, "midPlan": {"type": "null"}, "isBusinessProofNotRequired": {"type": "null"}, "gstExemptedCategory": {"type": "null"}, "isTurnOverBelowTaxableIncome": {"type": "null"}, "isBusinessAppliedForGST": {"type": "null"}, "isSmallMerchantDeclaration": {"type": "null"}, "edcModel": {"type": "null"}, "edcPrice": {"type": "number"}, "edcAppliedTaxes": {"type": "null"}, "edcType": {"type": "null"}, "edcSimDetails": {"type": "null"}, "upiFlag": {"type": "null"}, "edcMachineMake": {"type": "null"}, "midExist": {"type": "null"}, "edcMidExist": {"type": "null"}, "midExistTimestamp": {"type": "null"}, "edcMidExistTimestamp": {"type": "null"}, "securityAmount": {"type": "number"}, "rentalAmount": {"type": "number"}, "authorisedSignatory": {"type": "null"}, "businessName": {"type": "null"}, "businessType": {"type": "null"}, "nameAsPerBank": {"type": "null"}, "stampPaperNumber": {"type": "null"}, "typeOfShop": {"type": "null"}, "commissionBooking": {"type": "null"}, "commissionLmb": {"type": "null"}, "commissionPlb": {"type": "null"}, "commissionFph": {"type": "null"}, "settlementType": {"type": "null"}, "displayName": {"type": "null"}, "contactName": {"type": "null"}, "contactNumber": {"type": "null"}, "altPhone": {"type": "null"}, "website": {"type": "null"}, "propertyType": {"type": "null"}, "propertyName": {"type": "null"}, "chainName": {"type": "null"}, "starRating": {"type": "null"}, "gstVerified": {"type": "null"}, "accountName": {"type": "null"}, "legalEntityName": {"type": "null"}, "relationshipManagerDetails": {"type": "null"}, "diyPanelLoginUrl": {"type": "null"}, "kycPanelTncViewUrl": {"type": "null"}, "primaryOwnerAccountCreationUrl": {"type": "null"}, "emailId": {"type": "null"}, "hotelAmenities": {"type": "null"}, "propertyTheme": {"type": "null"}, "checkOutTime": {"type": "null"}, "checkInTime": {"type": "null"}, "roomType": {"type": "null"}, "noOfRooms": {"type": "null"}, "roomName": {"type": "null"}, "numberOfRoomsThisType": {"type": "null"}, "bedType": {"type": "null"}, "totalOccupancy": {"type": "null"}, "roomSize": {"type": "null"}, "roomAmenties": {"type": "null"}, "roomDescription": {"type": "null"}, "description": {"type": "null"}, "mealPlan": {"type": "null"}, "planStartDate": {"type": "null"}, "planEnddate": {"type": "null"}, "primaryDesignation": {"type": "null"}, "additionalPersonName": {"type": "null"}, "additionalHotel": {"type": "null"}, "additionalHotelNumber": {"type": "null"}, "email": {"type": "null"}, "addtionalDesignation": {"type": "null"}, "catalogueProductId": {"type": "null"}, "hotelId": {"type": "null"}, "additionalEmail": {"type": "null"}, "parentAccountLink": {"type": "null"}, "merchantNamePG": {"type": "null"}, "businessNamePG": {"type": "null"}, "primaryUserDetailsPG": {"type": "null"}, "nameMatchStatusWithMerchantName": {"type": "null"}, "nameMatchStatusWithBusinessName": {"type": "null"}, "nameMatchStatusWithPrimaryDetails": {"type": "null"}, "leadStatus": {"type": "null"}, "warehouseEmail": {"type": "null"}, "commissionPercent": {"type": "null"}, "sapCode": {"type": "null"}, "warehouseId": {"type": "null"}, "warehouseName": {"type": "null"}, "qrStatus": {"type": "null"}, "pgReferenceId": {"type": "null"}, "tcsWaiver": {"type": "null"}, "ckycName": {"type": ["null", "string"]}, "nsdlName": {"type": "null"}, "ckycDOB": {"type": ["null", "string"]}, "ckycGender": {"type": ["null", "string"]}, "ckycEmailId": {"type": ["null", "string"]}, "ckycNameMatchStatus": {"type": ["null", "string"]}, "ckycCity": {"type": ["null", "string"]}, "ckycCountry": {"type": ["null", "string"]}, "ckycPincode": {"type": ["null", "string"]}, "ckycState": {"type": ["null", "string"]}, "ckycLine1": {"type": "null"}, "ckycLine2": {"type": "null"}, "ckycLine3": {"type": "null"}, "preDisbursalPendingReason": {"type": "null"}, "leadSubStatus": {"type": "null"}, "ckycId": {"type": "null"}, "ppiLimit": {"type": "string"}, "giftVoucherPlan": {"type": "null"}, "storeCode": {"type": "null"}, "msmeNumber": {"type": "null"}, "lmd": {"type": "null"}, "lmsLoanApplicationUserMessage": {"type": "null"}, "lmsLoanApplicationSystemMessage": {"type": "null"}, "rejectionReason": {"type": "null"}, "metroId": {"type": "null"}, "wholeSalerStoreId": {"type": "null"}, "wholesalerStoreName": {"type": "null"}, "creationType": {"type": "null"}, "referLeadStatus": {"type": "null"}, "name": {"type": "null"}, "message": {"type": "null"}, "solutionRequested": {"type": "null"}, "queryType": {"type": "null"}, "queryType2": {"type": "null"}, "pageURL": {"type": "null"}, "leadBusinessType": {"type": "null"}, "numberOfBranches": {"type": "null"}, "details": {"type": "null"}, "includeMeForNewsletter": {"type": "null"}, "isNotificationSuccessful": {"type": "string"}, "amc": {"type": "null"}}, "required": ["numberOfYearsInBusiness", "storeWidth", "distanceToNearestBranchBankInKms", "staffSize", "computerPresentAtShop", "averageDailyCustomerWalkins", "willingToBuyBiometricDevice", "distanceToNearestAggregationPoint", "additionalQuestions", "shopManagerName", "shopManagerPhone", "shopManagerEmail", "posRentalPlan", "socialNetworkURL", "businessSpocName", "businessSpocEmail", "storeName", "storeAddress", "storeEmail", "storeNumber", "storeState", "storeCity", "storePincode", "riskScore", "segment", "subSegment", "qrCodeId", "brandCode", "brandName", "businessCheck", "rejectedBy", "businessCheckRejectionReason", "monthlyGMV", "tncSets", "edcSerialNumber", "storeManagerEmail", "businessEmail", "idNumber", "brandAssociation", "storeDisplayName", "storeCategory", "planType", "merchantDeviceInfo", "type", "model", "price", "rentalType", "appliedTaxes", "fastagTagSequenceNum", "fastagVehicleClass", "fastagVehicleType", "fastagPricingScheme", "fastagChannel", "fastagPaymentDone", "vehicleNumber", "transactionStatus", "leadId", "qrCodePath", "orderId", "mid", "amount", "expiry", "pgMid", "pgPlusMid", "pos_ids", "merchantId", "deviceBindingInfoList", "currVmnCount", "vmnCountSoFar", "vmnResponse", "midPlan", "isBusinessProofNotRequired", "gstExemptedCategory", "isTurnOverBelowTaxableIncome", "isBusinessAppliedForGST", "isSmallMerchantDeclaration", "edcModel", "edcPrice", "edcAppliedTaxes", "edcType", "edcSimDetails", "upiFlag", "edcMachineMake", "midExist", "edcMidExist", "midExistTimestamp", "edcMidExistTimestamp", "securityAmount", "rentalAmount", "authorisedSignatory", "businessName", "businessType", "nameAsPerBank", "stampPaperNumber", "typeOfShop", "commissionBooking", "commissionLmb", "commissionPlb", "commissionFph", "settlementType", "displayName", "contactName", "contactNumber", "altPhone", "website", "propertyType", "propertyName", "chainName", "starRating", "gstVerified", "accountName", "legalEntityName", "relationshipManagerDetails", "diyPanelLoginUrl", "kycPanelTncViewUrl", "primaryOwnerAccountCreationUrl", "emailId", "hotelAmenities", "propertyTheme", "checkOutTime", "checkInTime", "roomType", "noOfRooms", "roomName", "numberOfRoomsThisType", "bedType", "totalOccupancy", "roomSize", "roomAmenties", "roomDescription", "description", "mealPlan", "planStartDate", "planEnddate", "primaryDesignation", "additionalPersonName", "additionalHotel", "additionalHotelNumber", "email", "addtionalDesignation", "catalogueProductId", "hotelId", "additionalEmail", "parentAccountLink", "merchantNamePG", "businessNamePG", "primaryUserDetailsPG", "nameMatchStatusWithMerchantName", "nameMatchStatusWithBusinessName", "nameMatchStatusWithPrimaryDetails", "leadStatus", "warehouseEmail", "commissionPercent", "sapCode", "warehouseId", "warehouseName", "qrStatus", "pgReferenceId", "tcsWaiver", "ckycName", "nsdlName", "ckycDOB", "ckycGender", "ckycEmailId", "ckycNameMatchStatus", "ckycCity", "ckycCountry", "ckycPincode", "ckycState", "ckycLine1", "ckycLine2", "ckycLine3", "preDisbursalPendingReason", "leadSubStatus", "ckycId", "ppiLimit", "giftVoucherPlan", "storeCode", "msmeNumber", "lmd", "lmsLoanApplicationUserMessage", "lmsLoanApplicationSystemMessage", "rejectionReason", "metroId", "wholeSalerStoreId", "wholesalerStoreName", "creationType", "referLeadStatus", "name", "message", "solutionRequested", "queryType", "queryType2", "pageURL", "leadBusinessType", "numberOfBranches", "details", "includeMeForNewsletter", "isNotificationSuccessful", "amc"]}, "vetoAdditionalQuestions": {"type": ["null", "string"]}, "additionalQuestions": {"type": "null"}, "documents": {"type": "array", "items": [{"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "string"}, "status": {"type": "string"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "string"}, "status": {"type": "string"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "rejectionReason", "docSubType", "action", "uuids"]}, {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "documentType": {"type": "string"}, "placeOfIssue": {"type": "null"}, "dateOfIssue": {"type": "null"}, "docProvided": {"type": "string"}, "updatedDocProvided": {"type": "null"}, "osv": {"type": "boolean"}, "docValue": {"type": "null"}, "expiryDate": {"type": "null"}, "uuid": {"type": "string"}, "status": {"type": "string"}, "rejectionReason": {"type": "null"}, "docSubType": {"type": "null"}, "action": {"type": "null"}, "uuids": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "documentType", "placeOfIssue", "dateOfIssue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedDocProvided", "osv", "docV<PERSON>ue", "expiryDate", "uuid", "status", "rejectionReason", "docSubType", "action", "uuids"]}]}, "agentDetails": {"type": "array", "items": [{"type": "object", "properties": {"agentCustId": {"type": "string"}, "agentMobile": {"type": "null"}, "agentName": {"type": "string"}, "agencyType": {"type": "string"}, "agentTeam": {"type": "null"}, "agentType": {"type": "string"}, "agencyName": {"type": "null"}}, "required": ["agentCustId", "agentMobile", "<PERSON><PERSON><PERSON>", "agencyType", "agentTeam", "agentType", "agencyName"]}, {"type": "object", "properties": {"agentCustId": {"type": "string"}, "agentMobile": {"type": "null"}, "agentName": {"type": "null"}, "agencyType": {"type": "null"}, "agentTeam": {"type": "null"}, "agentType": {"type": "string"}, "agencyName": {"type": "null"}}, "required": ["agentCustId", "agentMobile", "<PERSON><PERSON><PERSON>", "agencyType", "agentTeam", "agentType", "agencyName"]}]}, "timelineDetail": {"type": "array", "items": [{"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}, {"type": "object", "properties": {"subStage": {"type": "string"}, "successTime": {"type": "string"}, "workflowStatusId": {"type": "integer"}, "isActive": {"type": "boolean"}}, "required": ["subStage", "successTime", "workflowStatusId", "isActive"]}]}, "nocStatus": {"type": "null"}, "docAcceptanceMap": {"type": "object", "properties": {"merchant_shop_front_photo": {"type": "integer"}, "shopInnerPhoto": {"type": "integer"}, "lob": {"type": "integer"}}, "required": []}, "slabDataList": {"type": "null"}, "userConfiguration": {"type": "null"}, "userKycDetailsSRO": {"type": "null"}, "mandatoryParams": {"type": "null"}, "cmtParams": {"type": "null"}, "pgParams": {"type": "null"}, "bankInstrumentSROList": {"type": "array", "items": {}}, "creditBankFacilitiesDetails": {"type": "null"}, "suggestedBankDetailList": {"type": "null"}, "suggestedAddressList": {"type": "null"}, "disbursementDetails": {"type": "object", "properties": {"purposeList": {"type": "null"}, "periodDisbursement": {"type": "null"}, "caNumberList": {"type": "null"}}, "required": ["purposeList", "periodDisbursement", "caNumberList"]}, "kycDuplicationDetails": {"type": "null"}, "form60": {"type": "null"}, "deletedSet": {"type": "null"}, "preQuotes": {"type": "object", "properties": {"make": {"type": "null"}, "model": {"type": "null"}, "variant": {"type": "null"}, "rtoLocation": {"type": "null"}, "policyExpired": {"type": "null"}, "previousPolicyInsurer": {"type": "null"}, "policyTenure": {"type": "null"}, "claimsUnderPreviousPolicy": {"type": "null"}, "yearOfRegistration": {"type": "null"}, "lastClaimPreviousPolicy": {"type": "null"}}, "required": ["make", "model", "variant", "rtoLocation", "policyExpired", "previousPolicyInsurer", "policyTenure", "claimsUnderPreviousPolicy", "yearOfRegistration", "lastClaimPreviousPolicy"]}, "addOns": {"type": "object"}, "idv": {"type": "object"}, "previousNCB": {"type": "object"}, "quotes": {"type": "null"}, "proposal": {"type": "null"}, "discounts": {"type": "object"}, "substageCount": {"type": "object", "properties": {"PANEL_SUCCESS": {"type": "integer"}}, "required": ["PANEL_SUCCESS"]}, "gstDetails": {"type": "null"}, "bankInstrumentSRO": {"type": "array", "items": {}}}, "required": ["uploadDocumentList", "businessOwnerDetails", "businessOwnerDetailList", "authorizedSignatoryList", "businessEntityDetails", "leadInfo", "bankDetails", "addressDetails", "communicationAddress", "<PERSON><PERSON><PERSON><PERSON>", "shopAddress", "cfaAgent", "vaAgent", "auditTrail", "additionalDetails", "vetoAdditionalQuestions", "additionalQuestions", "documents", "agentDetails", "timelineDetail", "nocStatus", "docAcceptanceMap", "slabDataList", "userConfiguration", "userKycDetailsSRO", "mandatoryParams", "cmtParams", "pgParams", "bankInstrumentSROList", "creditBankFacilitiesDetails", "suggestedBankDetailList", "suggestedAddressList", "disbursementDetails", "kycDuplicationDetails", "form60", "deletedSet", "preQuotes", "addOns", "idv", "previousNCB", "quotes", "proposal", "discounts", "substageCount", "gstDetails", "bankInstrumentSRO"]}, "editableFields": {"type": "null"}, "mandatoryFields": {"type": "null"}, "deletableFields": {"type": "null"}, "deletedFields": {"type": "null"}, "additionalBlockSet": {"type": "null"}, "actionSet": {"type": "null"}, "mode": {"type": "string"}, "config": {"type": "null"}, "viewMode": {"type": "string"}, "deletedSet": {"type": "null"}}, "required": ["errorCode", "message", "requestUUID", "refresh", "leadDetails", "editable<PERSON><PERSON>s", "mandatoryFields", "deletableFields", "deletedFields", "additionalBlockSet", "actionSet", "mode", "config", "viewMode", "deletedSet"]}