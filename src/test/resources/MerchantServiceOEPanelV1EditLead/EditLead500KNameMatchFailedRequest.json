{
  "deletedFields": {},
  "leadDetails": {
    "authorizedSignatoryList": [{
      <#if custId??> "custId": "${custId}",</#if>
      <#if mobileNumber??> "mobileNumber": "${mobileNumber}",</#if>
      "aadhaarStatus": "NA",
      "kycStatus": "false",
      "panStatus": "NA",
      "kycType": "",
      "form60Status": "false",
      "ovdName": "AASHIT SHARMA",
      "ownershipTypeSet": ["AUTHORIZED_SIGNATORY"],
      "level": null,
      "vdc": null
    }],
    "userConfiguration": {
      "userCount": "1"
    },
    "additionalDetails": {
      "merchantDOB": "1993-08-25",
      "editReason": "",
      "docStatusEditFlag": true,
      "bankVerificationStatus": "APPROVED",
      "businessVerificationStatus": "REJECTED"

    },
    <#if documents??> "documents": ${documents},</#if>
"bankDetails": {
      <#if reEnterAccountNumber??> "reEnterAccountNumber": "${reEnterAccountNumber}",</#if>
      "reEnterIfscCode": "ANDB0002029"
    },
      "qcReviewDetails": [{
"status": "Verified",
"reason": null,
"key": "dob",
"value": "1993-08-25",
"label": "Date of Birth"
}, {
"status": "Verified",
"reason": null,
"key": "gender",
"value": "male",
"label": "Gender"
}, {
"status": "Verified",
"reason": null,
"key": "businessEstDate",
"value": "1991-09-22",
"label": "Date of Business Registration"
}, {
"status": "Verified",
"reason": null,
"key": "email",
<#if mobileNumber??> "value": "${mobileNumber}@yopmail.com",</#if>
"label": "Email"
}],
    "leadInfo": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}