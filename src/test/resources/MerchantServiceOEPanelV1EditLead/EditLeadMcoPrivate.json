{
"deletedFields": {

},
"deletableFields": {

},
"leadDetails": {
"businessEntityDetails": {
"categoryVerificationStatus": "Verified",
"typeOfShop": "Fixed Shop",
"businessAssociationCheck": "verifiedAndProprietor",
"businessVerificationStatus": "ONLINE_VERIFIED",
"billingAddress": {
"line1": "<PERSON><PERSON><PERSON> ,13 ,Street Sector78 ,Dr Lucky Chan<PERSON>ar Clinic ,India ",
"line2": null,
"line3": "Sector 116 ,Dadri ,Gautambuddha Nagar District ",
"regionType": null,
"landmark": null,
"region": null,
"postalOfficeName": null,
"city": "Gautam Buddha Nagar",
"state": "Uttar Pradesh",
"pinNotMandatory": null,
"pincode": 201307,
"country": null,
"lattitude": 28.5682864,
"longitude": 77.3956823,
"shopName": "Sk189,116",
"landmarkName": "Near footprint ",
"formattedAddress": "<PERSON><PERSON><PERSON>, Unnamed Road, Sector 116, Noida, Uttar Pradesh. 34 m from Dr Lucky Chandekar Clinic, Pin-201307 (India)"
},
"shopPhotoDocument": "SHOP_PHOTO_WITH_BOARD",
"segment": "Food",
"subSegment": "Restaurant"
},
"panVerificationDetails": {
"panDOI": "2025-01-15"
},
"additionalDetails": {
"editReason": "",
"docStatusEditFlag": true
},
"bankDetails": {
"accountType": "current",
"bankAccountType": "Business Account",
"reEnterAccountNumberLastFour": "2171",
"bankVerificationStatus": "APPROVED",
"beneficiaryName": "ANMOL JAIN"
},
"businessDocuments": [
{
"name": "Certificate of Incorporation",
"type": "Certificate of Incorporation",
"documentType": "coi",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "COI",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidCOI??> "uuids": ["${uuidCOI}"],</#if>
"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": "28.5682852",
"longitude": "77.3956855",
"distance": "0.****************",
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
},
{
"name": "Board Resolution",
"type": "Board Resolution",
"documentType": "authSignatoryDeclaration",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "authSignatoryDeclaration",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidAuthSignatoryDeclaration??> "uuids": ["${uuidAuthSignatoryDeclaration}"],</#if>

"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": "28.5682852",
"longitude": "77.3956855",
"distance": "0.****************",
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
},
{
"name": "Shop Photo",
"type": "Shop Photo",
"documentType": "shopFrontPhoto",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "shopFrontPhoto",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidShopPhoto??> "uuids": ["${uuidShopPhoto}"],</#if>

"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": "28.5682852",
"longitude": "77.3956855",
"distance": "0.****************",
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
},
{
"name": "Food License (FSSI License)",
"type": "Food License (FSSI License)",
"documentType": "businessProof",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "foodLicense",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": true,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidBusinessProof??> "uuids": ["${uuidBusinessProof}"],</#if>

"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": "28.5682852",
"longitude": "77.3956855",
"distance": "0.****************",
"docProvidedUpdated": "foodLicense",
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
},
{
"name": "Registration Certiticate including Udyog Aadhar",
"type": "Registration Certiticate including Udyog Aadhar",
"documentType": "POIAWB",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "registrationCertificate_poiawb",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidPOIAWB??> "uuids": ["${uuidPOIAWB}"],</#if>

"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": "28.5682852",
"longitude": "77.3956855",
"distance": "0.****************",
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
}
],
"merchantDocuments": [
{
"name": "Aadhaar ",
"type": "Aadhaar ",
"documentType": "poi",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "aadhaar",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidAadharPhoto1?? && uuidAadharPhoto2??>"uuids": ["${uuidAadharPhoto1}", "${uuidAadharPhoto2}"], </#if>

"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": "28.5682852",
"longitude": "77.3956855",
"distance": "0.****************",
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": null,
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
},
{
"name": "PAN  (Optional)",
"type": "PAN ",
"documentType": "pan",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "pan",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": "DM1429927169598",
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidPanPhoto??> "uuids": ["${uuidPanPhoto}"],</#if>
"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": "28.5682852",
"longitude": "77.3956855",
"distance": "0.****************",
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 3
}
],
"bankDocuments": [
{
"name": "Cancel Cheque Photo",
"type": "Cancel Cheque Photo",
"documentType": "bankProof",
"placeOfIssue": null,
"dateOfIssue": null,
"docProvided": "cancelledChequePhoto",
"updatedDocProvided": null,
"updatedDocType": null,
"osv": null,
"docValue": null,
"expiryDate": null,
"noExpiryDate": null,
"uuid": null,
"status": "APPROVED",
"voterIdDetails": null,
"dlDetails": null,
"rejectionReason": null,
"docSubType": null,
"action": null,
  <#if uuidBankPhoto??> "uuids": ["${uuidBankPhoto}"],</#if>
"comment": null,
"addressType": null,
"entityUuid": null,
"ownerName": null,
"ownerMobile": null,
"ownerEmail": null,
"ownerFirstName": null,
"approverEmail": null,
"ownershipTypeSet": null,
"latitude": null,
"longitude": null,
"distance": null,
"docProvidedUpdated": null,
"additionalStatus": null,
"containsNonRedactedAadhaar": "No",
"documentAuditTrail": null,
"oldMccDocProvided": null,
"statusIndex": 0
}
],
"riskReviewData": {
"status": "PASS"
},
"addressDetails": {
"shopName": "Sk189,116"
},
"leadDetailsSection": {
  <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
}
}
}