{
  "deletedFields": {

  },
  "deletableFields": {

  },
  "leadDetails": {
    "businessEntityDetails": {
      "categoryVerificationStatus": "Verified",
      "typeOfShop": "Fixed Shop",
      "businessAssociationCheck": "verifiedAndProprietor",
      "businessVerificationStatus": "ONLINE_VERIFIED",
      "shopPhotoDocument": "SHOP_PHOTO_WITH_BOARD",
      "segment": "Food",
      "subSegment": "Restaurant"
    },
    "panVerificationDetails": {
      "panDOI": "2024-12-04"
    },
    "additionalDetails": {
      "editReason": "",
      "docStatusEditFlag": true
    },
    "bankDetails": {
      "accountType": "current",
      "bankAccountType": "Business Account",
      "reEnterAccountNumberLastFour": "6213",
      "bankVerificationStatus": "APPROVED",
      "beneficiaryName": "ANMOL JAIN"
    },
    "businessDocuments": [
      {
        "name": "Certificate of Incorporation",
        "type": "Certificate of Incorporation",
        "documentType": "coi",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "COI",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": "2027-07-31",
        "noExpiryDate": false,
        "uuid": null,
        "status": "REJECTED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": [
          "Random document"
        ],
        "docSubType": null,
        "action": null,
        <#if uuidCOI??> "uuids": ["${uuidCOI}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Board Resolution",
        "type": "Board Resolution",
        "documentType": "authSignatoryDeclaration",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "authSignatoryDeclaration",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": "2026-10-24",
        "noExpiryDate": false,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidAuthSignatoryDeclaration??> "uuids": ["${uuidAuthSignatoryDeclaration}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Shop Photo",
        "type": "Shop Photo",
        "documentType": "shopFrontPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "shopFrontPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidShopPhoto??> "uuids": ["${uuidShopPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Food License (FSSI License)",
        "type": "Food License (FSSI License)",
        "documentType": "businessProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "foodLicense",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": "2029-12-05",
        "noExpiryDate": false,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBusinessProof??> "uuids": ["${uuidBusinessProof}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": "foodLicense",
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Registration Certiticate including Udyog Aadhar",
        "type": "Registration Certiticate including Udyog Aadhar",
        "documentType": "POIAWB",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "registrationCertificate_poiawb",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": "2024-12-31",
        "noExpiryDate": false,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidPOIAWB??> "uuids": ["${uuidPOIAWB}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "Yes",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "merchantDocuments": [
      {
        "name": "Aadhaar ",
        "type": "Aadhaar ",
        "documentType": "poi",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "aadhaar",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidAadharPhoto1?? && uuidAadharPhoto2??>"uuids": ["${uuidAadharPhoto1}", "${uuidAadharPhoto2}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": null,
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "PAN  (Optional)",
        "type": "PAN ",
        "documentType": "pan",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "pan",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": "DM1417730172088",
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidPanPhoto??> "uuids": ["${uuidPanPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": "28.5682852",
        "longitude": "77.3956855",
        "distance": "0.****************",
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 3
      }
    ],
    "bankDocuments": [
      {
        "name": "Cancel Cheque Photo",
        "type": "Cancel Cheque Photo",
        "documentType": "bankProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "cancelledChequePhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBankPhoto??> "uuids": ["${uuidBankPhoto}"],</#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "riskReviewData": {
      "status": "PASS"
    },
    "leadInfo": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}