{
  "leadDetails": {
    "businessOwnerDetails": {
      <#if custId??> "custId": "${custId}",</#if>
      "ownerAddress": {}
    },
    "businessOwnerDetailList": null,
    "businessEntityDetails": {
      "entityType": "INDIVIDUAL",
      "solution": "revisit_merchant",
      "solutionTypeLevel2": "payments",
      "solutionTypeLevel3": "organized",
      <#if pgMID??> "pgMID": "${pgMID}",</#if>

      "businessAddress": {},
      "billingAddress": {}
    },
    "addressDetails": {
      "line1": "PAYTM F1",
      "line2": "Sector-1 Kale Khan",
      "line3": "Delhi",
      "region": null,
      "regionType": null,
      "pincode": 110011,
      "city": "North Delhi",
      "state": "Delhi",
      "country": null,
      "pinNotMandatory": null,
      "lattitude": 28.605600545146928,
      "longitude": 77.34191067516804,
      "landmark": null,
      "pincode_override": 110011
    },

    "userConfiguration": {},
    "additionalDetails": {

      "additionalQuestions": {
        "Is merchant using P4B /Dashboard?": "Using P4B",
        "Merchandising visibilty feedback for the Outlet": "Paytm visibility better than competition",
        "Competition at outlet": "Amazon Pay,Phonepe,BharatPe",
        "LOCATION_UPDATED": "true",
        "Outlet Name": "Aashit Play Store",
        "store manager mobile /outlet number": "9953828631",
        "City": "Delhi",
        "Store location/address": "Paytm F1 Noida",
        "Merchant accepting Paytm during visit?": "Yes via QR code",
        "Have you done any deployment in this store?": "NO",
        "SOLUTION_REVISITED": "p2p_100k",
        "store manager name": "Aashit Sharma",
        "Is cashier trained in taking payments through Paytm?": "YES",
        "OTP_VALIDATED": "false",
        "Visit Category": "Food",
        "Any feedback/Compition presence offer?": "No"
      },

      <#if kybShopReferenceId??> "kybShopReferenceId": "${kybShopReferenceId}",</#if>
      "superAdminFlag": true,
      "docStatusEditFlag": true
    },

    "uploadDocumentList": null,
    <#if documents??> "documents": ${documents},</#if>
    "bankDetails": {},
    "nocStatus": {},
    "mandatoryParams": null,
    "cmtParams": null,
    "additionalQuestions": {},
    "timelineDetail": [],
    "childLead": [],
    "vetoAdditionalQuestions": {},
    "fseParams": {},
    "riskReviewData": null
  }
}