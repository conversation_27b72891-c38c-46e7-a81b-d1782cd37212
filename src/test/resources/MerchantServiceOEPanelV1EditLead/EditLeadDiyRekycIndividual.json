{
  "deletedFields": {

  },
  "deletableFields": {

  },
  "leadDetails": {
    "aadhaarVerificationDetails": {
      "aadhaarGender": "male"
    },
    "additionalDetails": {
      "editReason": "",
      "docStatusEditFlag": true
    },
    "bankDetails": {
      "reEnterAccountNumberLastFour": "0302",
      "bankVerificationStatus": "APPROVED",
      "beneficiaryName": "ANMOL JAIN"
    },
    "merchantDocuments": [
      {
        "name": "Aadhaar",
        "type": "Aadhaar",
        "documentType": "poi",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "aadhaar",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidAadharPhoto1?? && uuidAadharPhoto2??>"uuids": ["${uuidAadharPhoto1}", "${uuidAadharPhoto2}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": null,
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      },
      {
        "name": "Business Owner Photo",
        "type": "Business Owner Photo",
        "documentType": "businessOwnerPhoto",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "businessOwnerPhoto",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": "DM1431030150814",
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        "uuids": [
          "DM1431030150814"
        ],
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "bankDocuments": [
      {
        "name": "Bank Statement",
        "type": "Bank Statement",
        "documentType": "bankProof",
        "placeOfIssue": null,
        "dateOfIssue": null,
        "docProvided": "BankStatement",
        "updatedDocProvided": null,
        "updatedDocType": null,
        "osv": null,
        "docValue": null,
        "expiryDate": null,
        "noExpiryDate": null,
        "uuid": null,
        "status": "APPROVED",
        "voterIdDetails": null,
        "dlDetails": null,
        "rejectionReason": null,
        "docSubType": null,
        "action": null,
        <#if uuidBankStatementPhoto??> "uuids": ["${uuidBankStatementPhoto}"], </#if>
        "comment": null,
        "addressType": null,
        "entityUuid": null,
        "ownerName": null,
        "ownerMobile": null,
        "ownerEmail": null,
        "ownerFirstName": null,
        "approverEmail": null,
        "ownershipTypeSet": null,
        "latitude": null,
        "longitude": null,
        "distance": null,
        "docProvidedUpdated": null,
        "additionalStatus": null,
        "containsNonRedactedAadhaar": "No",
        "documentAuditTrail": null,
        "oldMccDocProvided": null,
        "statusIndex": 0
      }
    ],
    "panVerificationDetails": {
      "panVerificationStatus": "Verified"
    },
    "riskReviewData": {
      "status": "PASS"
    },
    "leadDetailsSection": {
      <#if workflowStatusId??> "workflowStatusId": "${workflowStatusId}"</#if>
    }
  }
}