{
  "id": 5597869,
  "phoneNumber": "**********",
  "custId": **********,
  <#if emailId??> "emailId": "${emailId}", </#if>
  "roleList": [
    "OE_PANEL"
  ],
  "agentTncVersion": 0,
  "createWallet": "NO",
  "bankAgentType": "OTHERS",
  "agencyName": "hotel_group",
  <#if agentName??> "agentName": "${agentName}", </#if>
  "logoutRequired": 0,
  "ekycEnabled": false,
  "agentTeam": "KYC point",
  <#if status??> "status": "${status}", </#if>
  "bucketZoneStates": "",
  "createdAt": "2019-10-15 12:22:21.0",
  <#if groups??> "groups": "${groups}", </#if>
  "address": {
    "id": 4680197,
    "country": null,
    <#if pincode??> "pincode": "${pincode}", </#if>
    "state": "Delhi",
    "city": "East Delhi",
    "line1": "ww",
    "line2": "wwew",
    "line3": "wee",
    "landmark": null,
    "latitude": 0,
    "longitude": 0,
    "residentialStatus": null,
    "createdAt": *************,
    "updatedAt": *************,
    "status": "IN_PROGRESS",
    "addressUuid": "17c6b195-b589-4f29-b54a-de7a1f795b02"
  },
  "addressId": 0,
  "userAdditionalInfo": {
    "id": 189,
    "userId": 5597869,
    "kycPointOnboardedBy": null,
    <#if inactiveReason??> "inactiveReason": "${inactiveReason}", </#if>
    "complianceReasons": null,
    <#if recordType??> "recordType": "${recordType}", </#if>
    <#if agentSubtype??> "agentSubtype": "${agentSubtype}", </#if>
    "asmName": "sfoedie",
    "tncName": null,
    "tlName": null,
    "tlNumber": null,
    "tlEmployeeId": null,
    "reportingManagerName": null,
    "reportingManagerCustId": null,
    <#if dateOfJoining??> "dateOfJoining": "${dateOfJoining}", </#if>
    "dateOfLeaving": "2019-10-15 00:00:00.0",
    "reasonOfLeaving": null,
    "dateOfJoiningInDate": null,
    "dateOfLeavingInDate": null,
    "createdBy": "1106992015",
    "lastModifiedBy": null,
    "employeeId": null,
    "opsManagerName": null,
    "opsManagerId": null,
    "designation": null,
    "alternatePhoneNumber": null,
    "displayName": null
  },
  "bulkFlag": false,
  "newRecord": false,
  "showTncNum": 0,
  "createWalletNum": 0
}