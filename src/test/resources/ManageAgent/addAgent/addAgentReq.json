{
  <#if id??> "id": ${id}, </#if>
  <#if phoneNumber??> "phoneNumber": ${phoneNumber}, </#if>
  "custId": **********,
  <#if emailId??> "emailId": "${emailId}", </#if>
  <#if roleList??> "roleList": ${roleList}, </#if>
  "agentTncVersion": 0,
  "createWallet": "NO",
  "bankAgentType": "OTHERS",
  <#if agentName??> "agentName": "${agentName}", </#if>
  "logoutRequired": 0,
  "ekycEnabled": false,
  "agentTeam": "UnOrganized Offline",
  <#if status??> "status": "${status}", </#if>
  "bucketZoneStates": "",
  "createdAt": "2019-10-21 11:13:56.0",
  <#if groups??> "groups": ${groups}, </#if>
  "address": {
    "id": 4680483,
    "country": null,
    <#if pincode??> "pincode": "${pincode}", </#if>
    "state": "Delhi",
    "city": "South West Delhi",
    "line1": "2404",
    "line2": "BANSAL BOOT HOUSE",
    "line3": null,
    "landmark": null,
    "latitude": 0.0,
    "longitude": 0.0,
    "residentialStatus": null,
    "createdAt": *************,
    "updatedAt": *************,
    "status": "IN_PROGRESS",
    "addressUuid": "910b1c52-8a48-4144-92ba-1a2a8147bf86"
  },
  "addressId": 0,
  "userAdditionalInfo": {
    "id": 201,
    "userId": 5597881,
    "kycPointOnboardedBy": null,
    <#if inactiveReason??> "inactiveReason": "${inactiveReason}", </#if>
    "complianceReasons": null,
    <#if recordType??> "recordType": "${recordType}", </#if>
    <#if agentSubtype??> "agentSubtype": "${agentSubtype}", </#if>
    "asmName": null,
    "tncName": null,
    "tlName": null,
    "tlNumber": null,
    "tlEmployeeId": null,
    "reportingManagerName": null,
    "reportingManagerCustId": null,
    <#if dateOfJoining??> "dateOfJoining": "${dateOfJoining}", </#if>
    "dateOfLeaving": null,
    "reasonOfLeaving": null,
    "dateOfJoiningInDate": null,
    "dateOfLeavingInDate": null,
    "createdBy": "1106992015",
    "lastModifiedBy": "1106992015",
    "employeeId": null,
    "opsManagerName": null,
    "opsManagerId": null,
    "designation": null,
    "alternatePhoneNumber": null,
    "displayName": null
  },
  "bulkFlag": false,
  "newRecord": false,
  "showTncNum": 0,
  "createWalletNum": 0
}