{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "leadId": {"type": "string"}, "stage": {"type": "string"}, "panNameMatchSuccess": {"type": "boolean"}, "kycStatus": {"type": "string"}, "ckycName": {"type": "string"}}, "required": ["refId", "statusCode", "leadId", "stage", "panNameMatchSuccess", "kycStatus", "ckycName"]}