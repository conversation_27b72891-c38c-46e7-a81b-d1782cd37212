Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:28:22.839913
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:28:23.020185
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:28:23.399997
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:28:24.184749
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6kO-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6kO-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:29:44.339282
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:29:44.531449
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:29:44.931474
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:29:45.640567
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6kj-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6kj-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:30:43.219699
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:30:43.434566
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:30:43.863126
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:30:44.588149
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6kw-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6kw-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:33:36.657292
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:33:37.18088
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:33:37.747074
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:33:38.494816
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6lN-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6lN-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:34:25.597164
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:34:25.837186
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:34:26.292401
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:34:27.036504
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6lX-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6lX-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:34:51.199514
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:34:51.394385
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:34:51.821315
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:34:52.583513
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6lk-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6lk-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:35:19.596367
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:35:19.835003
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:35:20.229743
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:35:20.983194
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6lv-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6lv-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:35:53.257011
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:35:53.467898
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:35:53.943016
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:35:54.706567
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6l2-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6l2-236-400)"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:43:22.240994
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:43:22.44198
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:43:23.021168
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:43:24.196907
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6nX-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6nX-236-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-13T17:43:24.708044
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-13T17:43:25.221605
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-13T17:43:25.98137
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6nb-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6nb-236-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-13T17:43:26.1694
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-13T17:43:26.354842
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:48:25.667743
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:48:25.943983
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:48:26.445573
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:48:27.238738
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6oN-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6oN-236-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-13T17:48:27.435177
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-13T17:48:27.692516
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-13T17:48:28.492582
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6oQ-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6oQ-236-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-13T17:48:28.703173
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-13T17:48:29.006412
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-13T17:48:29.242168
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-13T17:48:29.462567
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-13T17:48:29.673956
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-13T17:48:30.004489
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-13T17:48:30.835485
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6oX-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6oX-236-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-13T17:48:31.966072
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-13T17:48:32.644898
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-13T17:48:33.517601
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T17:51:11.69218
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T17:51:11.93462
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T17:51:12.38439
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T17:51:13.148679
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6o4-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6o4-236-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-13T17:51:13.345076
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-13T17:51:13.554204
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-13T17:51:14.240089
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6o7-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6o7-236-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-13T17:51:14.456637
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-13T17:51:14.696917
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-13T17:51:14.96411
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-13T17:51:15.14688
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-13T17:51:15.380933
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-13T17:51:15.604762
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-13T17:51:16.265336
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6pC-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6pC-236-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-13T17:51:16.473849
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-13T17:51:16.644961
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-13T17:51:16.8952
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T18:06:39.188956
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T18:06:39.426131
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T18:06:40.922035
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T18:06:41.68347
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6p0-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6p0-236-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-13T18:06:41.986834
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-13T18:06:42.174473
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-13T18:06:43.039566
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6p3-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6p3-236-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-13T18:06:43.545566
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-13T18:06:44.257964
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-13T18:06:44.945712
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-13T18:06:45.29467
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-13T18:06:45.741989
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-13T18:06:45.969134
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-13T18:06:46.64049
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6p_-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6p_-236-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-13T18:06:46.890106
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-13T18:06:47.088725
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-13T18:06:47.280959
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-13T18:06:47.464389
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-13T18:06:47.714337
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-13T18:06:48.015513
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-13T18:06:48.754129
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6qF-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6qF-236-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-13T18:06:49.064069
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-13T18:06:49.289078
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-13T18:06:49.476379
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-13T18:06:49.684724
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-13T18:06:49.994057
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-13T18:06:50.689886
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6qL-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6qL-236-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-13T18:06:51.101972
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-13T18:06:51.310768
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-13T18:06:51.505465
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-13T18:06:51.714824
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-13T18:06:51.982955
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-13T18:06:52.70398
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6qR-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6qR-236-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-13T18:06:52.956817
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-13T18:06:53.18414
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-13T18:06:53.397024
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-13T18:06:53.597326
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-13T18:06:53.850499
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-13T18:06:54.05386
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-13T18:06:54.31583
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-13T18:06:55.127494
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-6qZ-236-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-6qZ-236-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-13T18:06:55.344246
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-13T18:06:55.597178
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-13T18:06:56.083034
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T19:43:20.436839
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T19:43:44.662773
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T19:43:56.170172
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T19:44:01.023155
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-13T19:44:01.944605
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-13T19:44:07.730495
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-13T19:44:08.245703
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-13T19:44:08.64574
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-13T19:44:09.126116
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-13T19:44:09.528879
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-13T19:44:09.903492
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-13T19:44:10.101873
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-13T19:44:10.287778
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-13T19:44:10.491236
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-13T19:44:10.909731
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-13T19:44:11.114518
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-13T19:44:16.24421
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-13T19:44:16.592895
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-13T19:44:17.46329
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-13T19:44:17.815945
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-13T19:44:18.174053
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-13T19:44:18.562472
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-13T19:44:18.79363
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-13T19:44:19.024716
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-13T19:44:19.237993
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-13T19:44:19.432491
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-13T19:44:19.715195
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-13T19:44:19.968579
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-13T19:44:20.228915
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-13T19:44:20.438294
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-13T19:44:20.634899
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-13T19:44:20.840663
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-13T19:44:21.045887
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-13T19:44:21.280563
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-13T19:44:21.465953
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-13T19:44:21.651366
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-13T19:44:21.837285
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-13T19:44:22.035505
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-13T19:44:22.242589
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-13T19:44:22.445104
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-13T19:44:22.663275
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-13T19:44:22.880086
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-13T19:44:23.080602
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-13T19:44:23.575297
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T20:10:57.020198
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T20:11:20.367101
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-13T20:26:35.785626
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-13T20:26:57.305779
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-13T20:27:08.309361
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-13T20:27:12.423389
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-13T20:27:12.822528
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-16T11:02:13.553125
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-16T11:02:38.123089
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-16T11:02:45.1789
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-16T11:02:48.28075
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-16T11:02:48.413079
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-16T11:02:48.533724
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-16T11:02:48.669214
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-16T11:02:48.801354
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-16T11:02:48.939901
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-16T11:02:49.075751
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-16T11:02:49.207772
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-16T11:02:49.339338
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-16T11:02:49.467881
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-16T11:02:49.611091
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-16T11:02:49.741234
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-16T11:02:49.872635
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-16T11:02:50.005574
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-16T11:02:50.142004
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-16T11:02:50.266855
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-16T11:02:50.395213
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-16T11:02:50.537732
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-16T11:02:50.677966
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-16T11:02:50.806423
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-16T11:02:50.932422
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-16T11:02:51.062045
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-16T11:02:51.191874
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-16T11:02:51.327939
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-16T11:02:51.463822
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-16T11:02:51.605522
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-16T11:02:51.749619
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-16T11:02:51.870908
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-16T11:02:51.996885
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-16T11:02:52.134727
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-16T11:02:52.266821
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-16T11:02:52.40371
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-16T11:02:52.54013
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-16T11:02:52.67627
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-16T11:02:52.826823
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-16T11:02:52.959455
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-16T11:02:53.095444
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-16T11:02:53.258718
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-16T11:02:53.4104
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-16T11:02:53.549052
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-16T11:02:53.812823
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-16T16:06:42.659853
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-16T16:07:11.691757
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-16T16:07:20.026934
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-16T16:07:23.200381
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-16T16:07:23.345655
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-16T16:07:23.497951
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-16T16:07:23.64731
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-16T16:07:23.831853
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-16T16:07:23.997703
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-16T16:07:24.132932
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-16T16:07:24.291528
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-16T16:07:24.44484
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-16T16:07:24.581436
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-16T16:07:24.793663
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-16T16:07:24.95166
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-16T16:07:25.109784
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-16T16:07:25.440577
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-16T16:07:25.835613
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-16T16:07:26.1607
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-16T16:07:26.492665
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-16T16:07:26.843708
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-16T16:07:27.217458
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-16T16:07:27.591368
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-16T16:07:27.776811
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-16T16:07:27.90826
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-16T16:07:28.054535
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-16T16:07:28.238598
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-16T16:07:28.395052
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-16T16:07:28.559132
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-16T16:07:28.753217
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-16T16:07:28.910834
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-16T16:07:29.084922
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-16T16:07:29.293588
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-16T16:07:29.443853
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-16T16:07:29.626964
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-16T16:07:29.797274
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-16T16:07:29.96854
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-16T16:07:30.136304
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-16T16:07:30.290607
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-16T16:07:30.467392
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-16T16:07:30.664223
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-16T16:07:30.844575
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-16T16:07:30.983821
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-16T16:07:31.304733
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-16T17:14:18.06412
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-16T17:14:44.572114
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-16T17:14:53.400653
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-16T17:14:56.667113
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-16T17:14:56.809589
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-16T17:14:56.948262
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-16T17:14:57.088851
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-16T17:14:57.274524
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-16T17:14:57.464816
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-16T17:14:57.676913
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-16T17:14:57.8469
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-16T17:14:58.010571
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-16T17:14:58.174435
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-16T17:14:58.325907
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-16T17:14:58.493968
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-16T17:14:58.673193
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-16T17:14:58.82725
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-16T17:14:58.968178
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-16T17:14:59.124573
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-16T17:14:59.290066
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-16T17:14:59.474838
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-16T17:14:59.674491
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-16T17:14:59.818997
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-16T17:15:00.081155
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-16T17:15:00.242856
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-16T17:15:00.400619
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-16T17:15:00.57683
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-16T17:15:00.76016
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-16T17:15:00.945466
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-16T17:15:01.128883
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-16T17:15:01.283091
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-16T17:15:01.776439
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-16T17:15:02.344108
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-16T17:15:02.638494
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-16T17:15:02.987374
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-16T17:15:03.340933
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-16T17:15:03.690549
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-16T17:15:03.916689
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-16T17:15:04.044642
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-16T17:15:04.176993
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-16T17:15:04.332431
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-16T17:15:04.525557
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-16T17:15:04.716319
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-16T17:15:05.00558
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-16T17:30:42.352193
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-16T17:31:10.90999
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-16T17:31:19.773674
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-16T17:56:43.02849
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-16T17:56:43.176268
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-16T17:56:43.489655
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-16T17:56:43.695599
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-16T17:56:43.924898
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-16T17:56:44.102853
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-16T17:56:44.308217
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-16T17:56:44.617664
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-16T17:56:44.773709
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-16T17:56:44.980509
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-16T17:56:45.183294
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-16T17:56:45.375732
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-16T17:56:45.726766
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-16T17:56:45.978118
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-16T17:56:46.166619
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-16T17:56:46.31614
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-16T17:56:46.63247
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-16T17:56:46.762864
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-16T17:56:47.020842
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-16T17:56:47.15312
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-16T17:56:47.305715
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-16T17:56:47.437875
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-16T17:56:47.755406
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-16T17:56:47.901526
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-16T17:56:48.15549
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-16T17:56:48.353572
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-16T17:56:48.51583
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-16T17:56:48.788069
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-16T17:56:49.027562
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-16T17:56:49.463482
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-16T17:56:49.873755
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-16T17:56:50.497726
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-16T17:56:51.068992
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-16T17:56:51.389308
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-16T17:56:52.046852
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-16T17:56:52.436553
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-16T17:56:52.709177
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-16T17:56:52.987892
Status Code: 410
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Please login again. Either your session has expired or you are already logged in on another device.","message":"Please login again. Either your session has expired or you are already logged in on another device."}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-16T17:56:53.155431
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-16T17:56:53.372182
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-16T17:56:53.725253
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-16T21:20:02.505735
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-16T21:25:46.22916
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-16T21:37:03.362396
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-16T21:37:07.662156
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7ax-113-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7ax-113-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-16T21:37:08.019246
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-16T21:37:08.43348
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-16T21:37:09.293725
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7a0-113-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7a0-113-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-16T21:37:09.64633
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-16T21:37:09.986864
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-16T21:37:10.228384
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-16T21:37:10.405858
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-16T21:37:10.608792
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-16T21:37:10.790164
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-16T21:37:11.519771
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7a7-113-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7a7-113-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-16T21:37:11.718198
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-16T21:37:11.922969
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-16T21:37:12.13794
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-16T21:37:12.332109
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-16T21:37:12.615199
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-16T21:37:12.830824
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-16T21:37:13.56863
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7bC-113-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7bC-113-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-16T21:37:13.791318
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-16T21:37:14.007159
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-16T21:37:14.205649
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-16T21:37:14.412251
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-16T21:37:14.599399
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-16T21:37:15.35684
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7bI-113-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7bI-113-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-16T21:37:15.675456
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-16T21:37:15.908343
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-16T21:37:16.340466
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-16T21:37:16.527949
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-16T21:37:16.7354
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-16T21:37:17.458729
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7bO-113-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7bO-113-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-16T21:37:17.659248
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-16T21:37:17.865962
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-16T21:37:18.04779
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-16T21:37:18.248938
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-16T21:37:18.455718
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-16T21:37:18.671609
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-16T21:37:18.870443
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-16T21:37:19.587997
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7bW-113-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7bW-113-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-16T21:37:19.792734
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-16T21:37:20.048651
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-16T21:37:20.495293
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-17T10:27:20.11985
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-17T10:33:45.388331
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-17T10:33:55.217378
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-17T10:33:59.571264
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7ud-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7ud-228-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-17T10:33:59.813457
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-17T10:34:00.188088
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-17T10:34:01.056706
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7ug-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7ug-228-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-17T10:34:01.245466
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-17T10:34:01.460661
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-17T10:34:01.660119
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-17T10:34:01.857142
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-17T10:34:02.035172
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-17T10:34:02.224533
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-17T10:34:02.969472
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7un-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7un-228-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-17T10:34:03.161253
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-17T10:34:03.379153
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-17T10:34:03.605674
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-17T10:34:03.815788
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-17T10:34:04.028249
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-17T10:34:04.24792
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-17T10:34:05.024176
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7uu-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7uu-228-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-17T10:34:05.266519
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-17T10:34:05.539482
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-17T10:34:05.73021
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-17T10:34:05.906088
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-17T10:34:06.102305
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-17T10:34:07.243783
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7u0-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7u0-228-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-17T10:34:07.587308
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-17T10:34:07.926798
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-17T10:34:08.281564
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-17T10:34:09.266359
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-17T10:34:09.502794
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-17T10:34:10.184539
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7u6-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7u6-228-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-17T10:34:10.425189
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-17T10:34:10.604795
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-17T10:34:10.794056
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-17T10:34:10.972771
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-17T10:34:11.160612
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-17T10:34:11.373037
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-17T10:34:11.588987
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-17T10:34:12.29131
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-7vC-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-7vC-228-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-17T10:34:12.504999
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-17T10:34:12.694695
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-17T10:34:13.103978
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-17T11:19:16.538946
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-17T11:26:34.378947
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-17T11:26:46.12529
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-17T11:26:51.77938
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-70A-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-70A-228-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-17T11:26:52.544327
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-17T11:26:53.050856
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-17T11:26:53.918157
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-70D-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-70D-228-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-17T11:26:54.174318
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-17T11:26:54.445981
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-17T11:26:54.660877
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-17T11:26:54.888747
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-17T11:26:55.106656
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-17T11:26:55.334639
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-17T11:26:56.121408
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-70K-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-70K-228-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-17T11:26:56.545372
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-17T11:26:56.769384
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-17T11:26:56.98989
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-17T11:26:57.250545
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-17T11:26:57.509145
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-17T11:26:57.72663
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-17T11:26:58.49175
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-70R-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-70R-228-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-17T11:26:58.716679
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-17T11:26:58.928711
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-17T11:26:59.138384
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-17T11:26:59.332986
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-17T11:26:59.585079
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-17T11:27:00.502755
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-70X-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-70X-228-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-17T11:27:00.782567
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-17T11:27:00.99156
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-17T11:27:01.230763
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-17T11:27:01.510498
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-17T11:27:01.765828
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-17T11:27:02.508037
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-70d-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-70d-228-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-17T11:27:02.72919
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-17T11:27:02.939664
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-17T11:27:03.149985
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-17T11:27:03.359946
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-17T11:27:03.640637
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-17T11:27:03.939333
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-17T11:27:04.15832
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-17T11:27:04.953517
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-70l-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-70l-228-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-17T11:27:05.595867
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-17T11:27:05.962739
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-17T11:27:07.424668
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-17T12:17:04.01193
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-17T17:25:23.978753
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-17T17:25:24.196116
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-17T17:25:24.734569
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-17T17:25:25.582205
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-8j3-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-8j3-228-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-17T17:25:26.104631
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-17T17:25:26.383895
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-17T17:25:27.284497
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-8j6-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-8j6-228-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-17T17:25:27.743982
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-17T17:25:29.24037
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-17T17:25:29.742643
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-17T17:25:30.076228
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-17T17:25:30.34174
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-17T17:25:30.584764
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-17T17:25:31.421861
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-8kB-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-8kB-228-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-17T17:25:31.734087
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-17T17:25:32.060132
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-17T17:25:32.676279
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-17T17:25:33.017311
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-17T17:25:33.258326
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-17T17:25:33.47175
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-17T17:25:34.429831
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-8kI-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-8kI-228-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-17T17:25:34.781512
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-17T17:25:35.081329
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-17T17:25:35.412675
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-17T17:25:35.732792
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-17T17:25:36.127672
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-17T17:25:37.117966
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-8kO-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-8kO-228-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-17T17:25:37.441552
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-17T17:25:37.693167
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-17T17:25:38.983403
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-17T17:25:39.239967
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-17T17:25:39.465921
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-17T17:25:40.533505
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-8kU-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-8kU-228-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-17T17:25:40.775092
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-17T17:25:41.020237
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-17T17:25:41.220539
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-17T17:25:42.123209
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-17T17:25:42.680244
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-17T17:25:43.034457
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-17T17:25:43.370445
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-17T17:25:44.238496
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-8kc-228-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-8kc-228-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-17T17:25:44.442652
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-17T17:25:44.654909
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-17T17:25:45.057357
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-24T18:12:39.391586
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-24T18:14:16.930775
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-24T18:15:46.818971
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-24T18:15:51.520918
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-nCr-29-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-nCr-29-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-24T18:15:51.722593
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-24T18:15:51.917451
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-24T18:15:52.623767
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-nCu-29-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-nCu-29-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-24T18:15:52.817702
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-24T18:15:53.116174
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-24T18:15:53.337574
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-24T18:15:53.547003
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-24T18:15:53.779077
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-24T18:15:53.999513
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-24T18:15:54.935256
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-nC1-29-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-nC1-29-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-24T18:15:55.116045
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-24T18:15:55.313579
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-24T18:15:55.534983
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-24T18:15:55.777434
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-24T18:15:55.980128
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-24T18:15:56.237201
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-24T18:15:57.006426
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-nC8-29-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-nC8-29-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-24T18:15:57.241684
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-24T18:15:57.434519
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-24T18:15:57.649757
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-24T18:15:57.853541
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-24T18:15:58.301502
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-24T18:15:58.954125
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-nDC-29-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-nDC-29-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-24T18:15:59.142462
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-24T18:15:59.362283
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-24T18:15:59.554475
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-24T18:15:59.765551
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-24T18:16:00.015101
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-24T18:16:00.772316
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-nDI-29-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-nDI-29-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-24T18:16:00.953923
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-24T18:16:01.146209
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-24T18:16:01.35631
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-24T18:16:01.603599
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-24T18:16:01.834497
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-24T18:16:02.03058
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-24T18:16:02.238107
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-24T18:16:02.909501
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-nDQ-29-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-nDQ-29-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-24T18:16:04.239207
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-24T18:16:04.455437
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-24T18:16:04.875423
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-30T13:07:03.663912
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-30T13:07:59.847053
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-30T13:08:50.166719
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-30T13:08:54.288128
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-n5V-45-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-n5V-45-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-30T13:08:54.489084
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-30T13:08:54.688893
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-30T13:08:55.38981
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-n5Y-45-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-n5Y-45-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-30T13:08:55.586573
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-30T13:08:55.772693
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-30T13:08:55.985908
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-30T13:08:56.179489
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-30T13:08:56.412838
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-30T13:08:56.608799
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-30T13:08:57.326074
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-n5f-45-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-n5f-45-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-30T13:08:57.526914
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-30T13:08:57.727209
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-30T13:08:57.925573
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-30T13:08:58.142553
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-30T13:08:58.368579
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-30T13:08:58.551646
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-30T13:08:59.275713
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-n5m-45-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-n5m-45-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-30T13:08:59.466707
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-30T13:08:59.645495
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-30T13:09:01.105116
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-30T13:09:01.317613
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-30T13:09:01.503292
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-30T13:09:02.245361
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-n5s-45-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-n5s-45-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-30T13:09:02.433212
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-30T13:09:02.62766
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-30T13:09:02.809829
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-30T13:09:03.045198
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-30T13:09:03.286959
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-30T13:09:04.014099
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-n5y-45-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-n5y-45-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-30T13:09:04.292849
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-30T13:09:04.475758
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-30T13:09:04.671169
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-30T13:09:04.871086
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-30T13:09:05.110502
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-30T13:09:05.323001
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-30T13:09:05.526927
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-30T13:09:06.340711
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-n56-45-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-n56-45-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-30T13:09:06.757056
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-30T13:09:06.949009
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-30T13:09:07.402136
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2024-12-31T10:41:07.809405
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2024-12-31T10:41:08.161743
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2024-12-31T10:41:08.563836
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2024-12-31T10:41:09.662723
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-oF8-145-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-oF8-145-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2024-12-31T10:41:09.861148
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2024-12-31T10:41:10.18191
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2024-12-31T10:41:10.863877
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-oF=-145-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-oF=-145-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2024-12-31T10:41:11.08448
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2024-12-31T10:41:11.283597
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2024-12-31T10:41:11.469088
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2024-12-31T10:41:11.677645
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2024-12-31T10:41:11.971786
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2024-12-31T10:41:12.164043
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2024-12-31T10:41:12.930684
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-oGG-145-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-oGG-145-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2024-12-31T10:41:13.182392
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2024-12-31T10:41:13.453856
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2024-12-31T10:41:13.656385
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2024-12-31T10:41:14.059843
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2024-12-31T10:41:14.263524
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2024-12-31T10:41:14.448228
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2024-12-31T10:41:15.17074
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-oGN-145-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-oGN-145-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2024-12-31T10:41:15.400162
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2024-12-31T10:41:15.637743
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2024-12-31T10:41:15.841854
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2024-12-31T10:41:16.058186
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2024-12-31T10:41:16.277961
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2024-12-31T10:41:16.961174
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-oGT-145-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-oGT-145-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2024-12-31T10:41:17.177174
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2024-12-31T10:41:17.367366
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2024-12-31T10:41:17.560453
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2024-12-31T10:41:17.760909
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2024-12-31T10:41:18.027504
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2024-12-31T10:41:19.137552
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-oGZ-145-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-oGZ-145-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2024-12-31T10:41:20.490664
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2024-12-31T10:41:21.194884
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2024-12-31T10:41:22.350701
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2024-12-31T10:41:22.566867
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2024-12-31T10:41:22.811791
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2024-12-31T10:41:23.026819
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2024-12-31T10:41:23.226377
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2024-12-31T10:41:23.941067
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-oGh-145-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-oGh-145-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2024-12-31T10:41:24.127577
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2024-12-31T10:41:24.319428
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2024-12-31T10:41:24.707713
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
<<<<<<< Updated upstream
Time: 2025-01-02T15:05:15.615683
=======
Time: 2025-01-02T14:01:47.761543
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
<<<<<<< Updated upstream
Time: 2025-01-02T15:05:38.278615
=======
Time: 2025-01-02T14:03:00.770078
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:18.3219
=======
Time: 2025-01-02T14:06:44.352025
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:23.654851
Status Code: 200
Response Body: {"agentTncStatus":false,"agentKycStatus":true,"agentTncUrl":"https://cif-staging-int.paytm.in/kyc/tnc/get/*********","agentTncVersion":"3"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-02T15:06:23.8535
=======
Time: 2025-01-02T14:06:47.536986
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_Ey-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_Ey-144-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-02T14:06:47.653159
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:24.045394
=======
Time: 2025-01-02T14:06:47.770703
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:24.822377
Status Code: 200
Response Body: {"agentTncStatus":false,"agentKycStatus":true,"agentTncUrl":"https://cif-staging-int.paytm.in/kyc/tnc/get/*********","agentTncVersion":"3"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-02T15:06:25.007792
=======
Time: 2025-01-02T14:06:48.373007
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_E1-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_E1-144-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-02T14:06:48.488451
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:25.214885
=======
Time: 2025-01-02T14:06:48.602805
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:25.383556
=======
Time: 2025-01-02T14:06:48.717078
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:25.573677
=======
Time: 2025-01-02T14:06:48.847169
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:25.771308
=======
Time: 2025-01-02T14:06:48.952233
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:25.989933
=======
Time: 2025-01-02T14:06:49.062707
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:26.764236
Status Code: 200
Response Body: {"agentTncStatus":false,"agentKycStatus":true,"agentTncUrl":"https://cif-staging-int.paytm.in/kyc/tnc/get/*********","agentTncVersion":"3"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-02T15:06:26.958596
=======
Time: 2025-01-02T14:06:49.728157
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_E8-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_E8-144-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-02T14:06:49.860807
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:27.138748
=======
Time: 2025-01-02T14:06:49.977098
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:27.329494
=======
Time: 2025-01-02T14:06:50.123978
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:27.54933
=======
Time: 2025-01-02T14:06:50.233909
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:27.763912
=======
Time: 2025-01-02T14:06:50.351006
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:27.955487
=======
Time: 2025-01-02T14:06:50.479256
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:28.599776
Status Code: 200
Response Body: {"agentTncStatus":false,"agentKycStatus":true,"agentTncUrl":"https://cif-staging-int.paytm.in/kyc/tnc/get/*********","agentTncVersion":"3"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-02T15:06:28.78163
=======
Time: 2025-01-02T14:06:51.164646
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_FD-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_FD-144-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-02T14:06:51.334503
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:28.989013
=======
Time: 2025-01-02T14:06:51.443841
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:29.195315
=======
Time: 2025-01-02T14:06:51.55413
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:29.370644
=======
Time: 2025-01-02T14:06:51.66541
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:29.58995
=======
Time: 2025-01-02T14:06:51.774493
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:30.289947
Status Code: 200
Response Body: {"agentTncStatus":false,"agentKycStatus":true,"agentTncUrl":"https://cif-staging-int.paytm.in/kyc/tnc/get/*********","agentTncVersion":"3"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-02T15:06:30.512997
=======
Time: 2025-01-02T14:06:52.496452
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_FJ-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_FJ-144-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-02T14:06:52.607114
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:30.69707
=======
Time: 2025-01-02T14:06:52.720258
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:30.881613
=======
Time: 2025-01-02T14:06:52.825578
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:31.045255
=======
Time: 2025-01-02T14:06:52.937039
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:31.227329
=======
Time: 2025-01-02T14:06:53.137357
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:31.885504
Status Code: 200
Response Body: {"agentTncStatus":false,"agentKycStatus":true,"agentTncUrl":"https://cif-staging-int.paytm.in/kyc/tnc/get/*********","agentTncVersion":"3"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-02T15:06:32.074357
=======
Time: 2025-01-02T14:06:53.723953
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_FP-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_FP-144-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-02T14:06:53.835192
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:32.288881
=======
Time: 2025-01-02T14:06:53.938154
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:32.48742
=======
Time: 2025-01-02T14:06:54.052563
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:32.756138
=======
Time: 2025-01-02T14:06:54.212473
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:32.974238
=======
Time: 2025-01-02T14:06:54.336874
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:33.428171
=======
Time: 2025-01-02T14:06:54.45067
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:33.634629
=======
Time: 2025-01-02T14:06:54.579163
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:34.273523
Status Code: 200
Response Body: {"agentTncStatus":false,"agentKycStatus":true,"agentTncUrl":"https://cif-staging-int.paytm.in/kyc/tnc/get/*********","agentTncVersion":"3"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-02T15:06:34.476305
=======
Time: 2025-01-02T14:06:55.260278
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_FX-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_FX-144-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-02T14:06:55.399897
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:34.659006
=======
Time: 2025-01-02T14:06:55.521242
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
<<<<<<< Updated upstream
Time: 2025-01-02T15:06:35.019228
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-02T15:32:35.92895
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-02T15:34:00.542879
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-02T15:38:14.794167
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-02T15:38:19.348568
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_mY-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_mY-144-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-02T15:38:19.569932
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-02T15:38:19.761134
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-02T15:38:20.479345
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_mb-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_mb-144-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-02T15:38:20.839571
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-02T15:38:21.348533
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-02T15:38:21.699681
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-02T15:38:22.049037
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-02T15:38:22.556868
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-02T15:38:23.253084
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-02T15:38:23.978038
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_mi-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_mi-144-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-02T15:38:24.142381
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-02T15:38:24.338623
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-02T15:38:24.562462
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-02T15:38:24.773183
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-02T15:38:24.974391
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-02T15:38:25.160753
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-02T15:38:25.824423
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_mp-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_mp-144-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-02T15:38:26.016902
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-02T15:38:26.218385
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-02T15:38:26.464833
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-02T15:38:26.67799
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-02T15:38:26.852164
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-02T15:38:27.596728
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_mv-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_mv-144-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-02T15:38:27.821595
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-02T15:38:28.070716
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-02T15:38:28.324305
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-02T15:38:28.570661
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-02T15:38:28.778526
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-02T15:38:29.505062
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_m1-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_m1-144-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-02T15:38:29.726516
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-02T15:38:29.903408
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-02T15:38:30.165223
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-02T15:38:30.383204
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-02T15:38:30.625991
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-02T15:38:30.81824
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-02T15:38:31.025967
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-02T15:38:31.695078
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-_m_-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-_m_-144-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-02T15:38:31.898994
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-02T15:38:32.090065
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-02T15:38:32.524383
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-02T16:47:37.807235
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-02T16:49:00.395959
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-02T16:51:46.101995
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-02T16:51:49.630137
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-02T16:51:49.808373
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-02T16:51:50.033164
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-02T16:51:50.359945
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-02T16:51:50.803625
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-02T16:51:51.306829
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-02T16:51:51.664027
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-02T16:51:51.998527
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-02T16:51:52.351868
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-02T16:51:52.699199
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-02T16:51:53.039717
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-02T16:51:53.280176
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-02T16:51:53.48256
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-02T16:51:53.658933
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-02T16:51:53.840936
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-02T16:51:54.023083
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-02T16:51:54.231845
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-02T16:51:54.448417
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-02T16:51:54.628684
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-02T16:51:54.828245
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-02T16:51:55.073023
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-02T16:51:55.24826
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-02T16:51:55.46302
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-02T16:51:55.653066
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-02T16:51:55.848659
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-02T16:51:56.0348
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-02T16:51:56.24192
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-02T16:51:56.464182
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-02T16:51:56.654524
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-02T16:51:56.82442
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-02T16:51:57.01874
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-02T16:51:57.21603
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-02T16:51:57.459513
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-02T16:51:57.647895
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-02T16:51:57.863866
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-02T16:51:58.052505
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-02T16:51:58.245209
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-02T16:51:58.460075
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-02T16:51:58.654364
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-02T16:51:58.843406
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC45_CreateLead_DuplicateRegistration
Time: 2025-01-02T16:51:59.231471
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-02T16:51:59.449664
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-02T17:37:37.356456
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-02T17:38:53.008624
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-02T17:43:27.157508
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-02T17:43:31.871885
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-=GZ-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-=GZ-144-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-02T17:43:32.402834
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-02T17:43:32.981978
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-02T17:43:33.753072
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-=Gd-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-=Gd-144-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-02T17:43:33.925482
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-02T17:43:34.127278
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-02T17:43:34.330246
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-02T17:43:34.545301
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-02T17:43:34.732089
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-02T17:43:34.920759
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-02T17:43:35.568473
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-=Gk-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-=Gk-144-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-02T17:43:35.774087
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-02T17:43:35.954752
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-02T17:43:36.131993
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-02T17:43:36.308078
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-02T17:43:36.500889
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-02T17:43:36.704557
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-02T17:43:37.358013
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-=Gs-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-=Gs-144-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-02T17:43:37.547724
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-02T17:43:37.733608
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-02T17:43:37.975048
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-02T17:43:38.14575
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-02T17:43:38.336782
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-02T17:43:39.018976
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-=Gz-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-=Gz-144-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-02T17:43:39.205464
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-02T17:43:39.378733
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-02T17:43:39.641743
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-02T17:43:39.884874
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-02T17:43:40.064075
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-02T17:43:40.740444
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-=G5-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-=G5-144-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-02T17:43:40.923542
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-02T17:43:41.09438
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-02T17:43:41.278863
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-02T17:43:41.444505
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-02T17:43:41.63622
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-02T17:43:41.831218
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-02T17:43:42.022024
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-02T17:43:42.708329
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-=HB-144-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-=HB-144-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-02T17:43:42.895915
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-02T17:43:43.105038
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-02T17:43:43.456312
=======
Time: 2025-01-02T14:06:55.744445
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-03T19:32:44.781153
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-03T19:34:15.529484
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-03T19:39:02.357569
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-03T19:39:12.930091
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAMA-100-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAMA-100-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-03T19:39:13.530804
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-03T19:39:14.38349
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-03T19:39:15.122488
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAME-100-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAME-100-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-03T19:39:15.360139
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-03T19:39:16.125978
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-03T19:39:16.360622
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-03T19:39:17.359056
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-03T19:39:18.140883
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-03T19:39:19.41233
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-03T19:39:20.269174
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAMM-100-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAMM-100-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-03T19:39:21.363162
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-03T19:39:22.460998
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-03T19:39:23.166646
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-03T19:39:24.542832
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-03T19:39:25.356501
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-03T19:39:26.360518
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-03T19:39:27.06319
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAMU-100-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAMU-100-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-03T19:39:27.363357
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-03T19:39:27.893592
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-03T19:39:28.519004
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-03T19:39:29.357995
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-03T19:39:30.153228
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-03T19:39:31.113722
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAMc-100-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAMc-100-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-03T19:39:32.249772
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-03T19:39:33.126256
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-03T19:39:33.535999
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-03T19:39:34.261571
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-03T19:39:35.149837
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-03T19:39:36.308354
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAMi-100-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAMi-100-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-03T19:39:37.516516
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-03T19:39:39.128802
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-03T19:39:39.371636
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-03T19:39:40.151666
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-03T19:39:40.360103
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-03T19:39:41.355208
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-03T19:39:42.447573
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-03T19:39:43.916216
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAMr-100-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAMr-100-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-03T19:39:44.404312
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-03T19:39:45.457158
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-03T19:39:47.432843
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-03T21:09:42.103458
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-03T21:11:15.757193
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-03T21:12:20.38304
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-03T21:12:25.181691
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-ofS-194-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-ofS-194-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-03T21:12:25.370917
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-03T21:12:25.645978
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-03T21:12:26.40647
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-ofV-194-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-ofV-194-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-03T21:12:26.626254
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-03T21:12:26.803477
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-03T21:12:27.029044
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-03T21:12:27.225469
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-03T21:12:27.430662
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-03T21:12:27.706127
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-03T21:12:28.457353
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-ofc-194-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-ofc-194-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-03T21:12:28.668085
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-03T21:12:28.861109
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-03T21:12:29.104798
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-03T21:12:29.332663
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-03T21:12:29.556802
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-03T21:12:29.798532
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-03T21:12:30.874352
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-ofj-194-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-ofj-194-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-03T21:12:31.211686
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-03T21:12:31.563956
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-03T21:12:32.090226
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-03T21:12:32.427222
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-03T21:12:32.780263
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-03T21:12:33.542602
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-ofp-194-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-ofp-194-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-03T21:12:33.740051
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-03T21:12:33.916211
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-03T21:12:34.092383
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-03T21:12:34.512377
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-03T21:12:34.756516
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-03T21:12:35.522713
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-ofv-194-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-ofv-194-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-03T21:12:35.779436
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-03T21:12:35.98357
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-03T21:12:36.185615
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-03T21:12:36.399695
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-03T21:12:36.628905
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-03T21:12:36.824782
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-03T21:12:37.030179
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-03T21:12:37.774421
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-of3-194-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-of3-194-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-03T21:12:37.973021
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-03T21:12:38.180446
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-03T21:12:38.801229
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-07T19:55:04.264351
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-07T19:56:25.795352
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-07T19:59:57.775175
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-07T20:00:01.725005
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAt4-213-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAt4-213-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-07T20:00:01.936722
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-07T20:00:02.133732
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-07T20:00:02.894324
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAt7-213-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAt7-213-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-07T20:00:03.07654
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-07T20:00:03.284226
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-07T20:00:03.500447
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-07T20:00:03.694428
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-07T20:00:03.898352
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-07T20:00:04.093651
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-07T20:00:04.810423
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAuC-213-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAuC-213-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-07T20:00:04.992933
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-07T20:00:05.178947
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-07T20:00:05.355677
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-07T20:00:05.570574
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-07T20:00:05.750216
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-07T20:00:05.931124
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-07T20:00:06.629658
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAuJ-213-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAuJ-213-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-07T20:00:06.815471
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-07T20:00:07.020382
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-07T20:00:07.211489
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-07T20:00:07.394511
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-07T20:00:07.595388
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-07T20:00:08.319232
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAuP-213-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAuP-213-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-07T20:00:08.531643
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-07T20:00:08.726014
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-07T20:00:08.930014
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-07T20:00:09.116205
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-07T20:00:09.292408
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-07T20:00:10.100074
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAuV-213-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAuV-213-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-07T20:00:10.435502
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-07T20:00:10.790718
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-07T20:00:11.323215
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-07T20:00:11.829855
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-07T20:00:12.18238
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-07T20:00:12.51943
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-07T20:00:12.773355
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-07T20:00:13.538477
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BAud-213-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BAud-213-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-07T20:00:13.721327
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-07T20:00:13.902093
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-07T20:00:14.297369
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-10T12:11:11.091358
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-10T12:12:40.365088
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-10T12:18:16.88964
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-10T12:18:29.011798
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BBOz-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BBOz-243-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-10T12:18:30.672945
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-10T12:18:32.094452
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-10T12:18:39.048742
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BBO5-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BBO5-243-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-10T12:18:39.33426
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-10T12:18:41.726132
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-10T12:18:42.016085
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-10T12:18:43.339514
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-10T12:18:44.58794
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-10T12:18:45.940229
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-10T12:18:48.962161
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BBPH-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BBPH-243-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-10T12:18:49.236646
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-10T12:18:52.617019
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-10T12:18:54.061374
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-10T12:18:55.652901
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-10T12:18:57.896812
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-10T12:19:01.069784
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-10T12:19:05.027488
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BBPW-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BBPW-243-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-10T12:19:05.302947
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-10T12:19:05.604888
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-10T12:19:07.257112
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-10T12:19:08.465228
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-10T12:20:18.643862
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-10T12:20:19.522928
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BBPe-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BBPe-243-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-10T12:20:20.11805
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-10T12:20:20.408493
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-10T12:20:20.71644
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-10T12:20:21.51271
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-10T12:20:21.869334
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-10T12:20:22.632754
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BBPk-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BBPk-243-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-10T12:20:22.947647
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-10T12:20:23.297776
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-10T12:20:23.599343
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-10T12:20:23.954545
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-10T12:20:24.248308
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-10T12:20:24.551577
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-10T12:20:24.915011
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-10T12:20:25.915042
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BBPs-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BBPs-243-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-10T12:20:26.543968
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-10T12:20:26.898496
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-10T12:20:28.521033
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-10T13:42:42.209949
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-10T13:44:04.297429
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-10T15:01:45.835721
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-10T15:01:50.894262
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-10T15:01:51.181343
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-10T15:01:51.509901
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-10T15:01:51.817212
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-10T15:01:52.089171
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-10T15:01:52.380928
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-10T15:01:52.676362
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-10T15:01:53.015633
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-10T15:01:53.330634
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-10T15:01:53.631154
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-10T15:01:53.977891
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-10T15:01:54.278473
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-10T15:01:54.576433
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-10T15:01:54.913652
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-10T15:01:55.194425
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-10T15:01:55.510817
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-10T15:01:55.819054
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-10T15:01:56.122065
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-10T15:01:56.465142
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-10T15:01:56.80928
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-10T15:01:57.102843
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-10T15:01:57.374294
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-10T15:01:57.66836
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-10T15:01:58.018068
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-10T15:01:58.349596
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-10T15:01:58.750363
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-10T15:01:59.08682
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-10T15:01:59.520332
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-10T15:01:59.865067
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-10T15:02:00.232388
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-10T15:02:00.558064
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-10T15:02:00.877913
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-10T15:02:01.158699
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-10T15:02:01.451556
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-10T15:02:01.715925
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-10T15:02:02.01908
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-10T15:02:02.300563
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-10T15:02:02.583558
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-10T15:02:02.930308
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-10T15:02:03.210686
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC45_CreateLead_DuplicateRegistration
Time: 2025-01-10T15:02:03.828711
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-10T15:02:04.128716
Status Code: 502
Response Body: <html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
<hr><center>nginx</center>
</body>
</html>

----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-10T15:11:17.783783
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-10T15:12:50.888957
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-10T15:17:11.910263
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-10T15:17:28.246809
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BB9q-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BB9q-243-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-10T15:17:30.201247
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-10T15:17:31.797212
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-10T15:17:37.777583
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BB9w-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BB9w-243-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-10T15:17:38.18428
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-10T15:17:40.978071
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-10T15:17:41.751364
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-10T15:17:43.446879
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-10T15:17:44.809167
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-10T15:17:46.289518
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-10T15:17:49.454005
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BB9_-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BB9_-243-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-10T15:17:49.796413
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-10T15:17:55.374303
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-10T15:17:57.636615
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-10T15:17:59.778854
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-10T15:18:02.194142
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-10T15:18:04.819687
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-10T15:18:09.010401
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BB_N-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BB_N-243-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-10T15:18:09.539109
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-10T15:18:10.053973
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-10T15:18:11.9109
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-10T15:18:13.59584
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-10T15:21:33.301711
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-10T15:21:33.939648
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BB_V-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BB_V-243-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-10T15:21:34.076045
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-10T15:21:34.24431
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-10T15:21:34.412106
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-10T15:21:34.611104
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-10T15:21:34.80593
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-10T15:21:35.475884
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BB_b-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BB_b-243-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-10T15:21:35.600892
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-10T15:21:35.756263
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-10T15:21:35.886588
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-10T15:21:36.014897
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-10T15:21:36.147416
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-10T15:21:36.294435
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-10T15:21:36.419697
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-10T15:21:37.084227
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BB_j-243-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BB_j-243-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-10T15:21:37.281955
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-10T15:21:37.413952
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-10T15:21:37.712835
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-14T18:22:29.295662
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-14T18:23:30.208622
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-14T18:27:26.32559
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-14T18:27:35.722043
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BCS5-16-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BCS5-16-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-14T18:27:37.260273
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-14T18:27:39.540029
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-14T18:27:44.934626
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BCTD-16-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BCTD-16-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-14T18:27:45.101762
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-14T18:27:47.048744
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-14T18:27:47.235851
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-14T18:27:48.715538
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-14T18:27:49.741299
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-14T18:27:50.815978
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-14T18:27:53.399284
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BCTU-16-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BCTU-16-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-14T18:27:53.588003
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-14T18:27:57.378701
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-14T18:27:58.538938
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-14T18:27:59.998181
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-14T18:28:01.301519
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-14T18:28:03.279062
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-14T18:28:06.643464
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BCTl-16-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BCTl-16-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-14T18:28:06.806104
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-14T18:28:06.985657
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-14T18:28:08.026873
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-14T18:28:08.998084
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-14T18:28:21.339054
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-14T18:28:22.104107
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BCTt-16-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BCTt-16-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-14T18:28:22.535479
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-14T18:28:22.714718
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-14T18:28:22.890257
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-14T18:28:23.073101
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-14T18:28:23.240842
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-14T18:28:23.929754
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BCTz-16-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BCTz-16-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-14T18:28:24.110098
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-14T18:28:24.291917
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-14T18:28:24.522588
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-14T18:28:24.683335
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-14T18:28:24.858224
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-14T18:28:25.034469
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-14T18:28:25.210563
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-14T18:28:25.905581
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BCT7-16-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BCT7-16-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-14T18:28:26.093465
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-14T18:28:26.276952
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-14T18:28:26.611608
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-16T18:54:53.9099
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-16T18:56:32.013342
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-16T19:00:41.718368
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-16T19:00:50.563636
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BD5k-42-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BD5k-42-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-16T19:00:51.981043
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-16T19:00:53.136707
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-16T19:00:58.797329
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BD5r-42-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BD5r-42-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-16T19:00:59.126344
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-16T19:01:01.63506
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-16T19:01:01.818324
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-16T19:01:03.019644
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-16T19:01:04.243202
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-16T19:01:05.387312
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-16T19:01:08.0147
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BD5_-42-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BD5_-42-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-16T19:01:08.198345
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-16T19:01:09.499338
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-16T19:01:10.656143
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-16T19:01:12.184037
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-16T19:01:14.564849
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-16T19:01:21.698416
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-16T19:01:24.979939
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BD6T-42-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BD6T-42-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-16T19:01:25.183042
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-16T19:01:25.457815
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-16T19:01:26.951905
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-16T19:01:28.394845
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-16T19:02:37.501405
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-16T19:02:38.367699
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BD65-42-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BD65-42-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-16T19:02:38.534398
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-16T19:02:38.710642
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-16T19:02:38.87934
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-16T19:02:39.118196
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-16T19:02:39.295805
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-16T19:02:40.015263
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BD7A-42-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BD7A-42-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-16T19:02:40.183975
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-16T19:02:40.350735
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-16T19:02:40.529328
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-16T19:02:40.695403
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-16T19:02:40.889597
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-16T19:02:41.09295
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-16T19:02:41.35329
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-16T19:02:42.049369
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BD7J-42-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BD7J-42-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-16T19:02:42.235754
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-16T19:02:42.434694
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-16T19:02:42.77772
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-17T16:00:26.516883
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-17T16:01:38.629891
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-17T16:05:25.994876
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-17T16:05:36.584091
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEcx-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEcx-175-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-17T16:05:37.8472
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-17T16:05:38.954812
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-17T16:05:44.364729
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEc3-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEc3-175-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-17T16:05:44.559958
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-17T16:05:46.894998
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-17T16:05:47.252467
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-17T16:05:48.799327
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-17T16:05:49.80903
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-17T16:05:51.094433
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-17T16:05:53.653375
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEdF-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEdF-175-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-17T16:05:53.846982
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-17T16:05:56.535471
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-17T16:05:57.671735
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-17T16:05:59.038063
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-17T16:06:00.585492
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-17T16:06:08.033673
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-17T16:06:11.459751
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEdU-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEdU-175-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-17T16:06:11.654047
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-17T16:06:11.842427
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-17T16:06:12.956524
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-17T16:06:14.226747
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-17T16:06:25.535055
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-17T16:06:26.191954
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEdc-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEdc-175-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-17T16:06:26.403561
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-17T16:06:26.609665
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-17T16:06:26.78566
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-17T16:06:26.956389
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-17T16:06:27.153562
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-17T16:06:27.880102
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEdi-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEdi-175-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-17T16:06:28.306297
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-17T16:06:28.582653
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-17T16:06:29.075753
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-17T16:06:29.409976
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-17T16:06:29.940973
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-17T16:06:30.287393
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-17T16:06:30.693174
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-17T16:06:31.352445
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEdq-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEdq-175-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-17T16:06:31.566046
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-17T16:06:31.772349
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-17T16:06:32.187629
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-01-17T16:34:50.517431
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-01-17T16:36:41.155968
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-01-17T16:40:52.507954
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-01-17T16:41:04.312494
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEjC-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEjC-175-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-01-17T16:41:05.600387
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-01-17T16:41:06.734662
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-01-17T16:41:12.463904
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEjI-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEjI-175-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-01-17T16:41:12.649547
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-01-17T16:41:15.00363
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-01-17T16:41:15.344964
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-01-17T16:41:16.686713
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-01-17T16:41:17.731438
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-01-17T16:41:18.815073
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-01-17T16:41:21.317097
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEjX-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEjX-175-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-01-17T16:41:21.542072
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-01-17T16:41:24.188741
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-01-17T16:41:25.190423
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-01-17T16:41:26.381282
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-01-17T16:41:27.91226
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-01-17T16:41:39.743663
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-01-17T16:41:44.057476
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEjo-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEjo-175-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-01-17T16:41:44.568921
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-01-17T16:41:44.754476
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-01-17T16:41:45.802665
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-01-17T16:41:46.921585
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-01-17T16:41:57.517664
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-01-17T16:41:58.436102
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEjw-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEjw-175-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-01-17T16:41:58.723769
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-01-17T16:41:58.920876
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-01-17T16:41:59.142312
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-01-17T16:41:59.321483
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-01-17T16:41:59.542773
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-01-17T16:42:00.370081
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEj2-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEj2-175-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-01-17T16:42:00.583123
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-01-17T16:42:00.762957
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-01-17T16:42:00.960508
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-01-17T16:42:01.180736
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-01-17T16:42:01.392901
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-01-17T16:42:01.610076
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-01-17T16:42:01.806045
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-01-17T16:42:02.463633
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BEj_-175-400)","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BEj_-175-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-01-17T16:42:02.725025
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-01-17T16:42:02.904961
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-01-17T16:42:03.311681
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
<<<<<<< Updated upstream
Time: 2025-02-24T18:45:18.108948
=======
Time: 2025-02-13T12:51:25.224778
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
<<<<<<< Updated upstream
Time: 2025-02-24T18:46:50.564369
=======
Time: 2025-02-13T12:52:45.115034
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
<<<<<<< Updated upstream
Time: 2025-02-24T18:51:57.637284
=======
Time: 2025-02-13T12:56:52.882352
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
<<<<<<< Updated upstream
Time: 2025-02-24T18:52:12.639726
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BMWY-6-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BMWY-6-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-02-24T18:52:14.027348
=======
Time: 2025-02-13T12:57:01.460211
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BKsh-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BKsh-65-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-02-13T12:57:02.899291
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
<<<<<<< Updated upstream
Time: 2025-02-24T18:52:15.36958
=======
Time: 2025-02-13T12:57:04.29716
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
<<<<<<< Updated upstream
Time: 2025-02-24T18:52:20.6846
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BMWe-6-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BMWe-6-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-02-24T18:52:20.938957
=======
Time: 2025-02-13T12:57:09.572361
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BKsr-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BKsr-65-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-02-13T12:57:09.733689
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
<<<<<<< Updated upstream
Time: 2025-02-24T18:52:22.899324
=======
Time: 2025-02-13T12:57:11.522887
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
<<<<<<< Updated upstream
Time: 2025-02-24T18:52:23.120808
=======
Time: 2025-02-13T12:57:11.695071
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:06.144788
=======
Time: 2025-02-13T12:57:12.838228
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:07.456452
=======
Time: 2025-02-13T12:57:13.78985
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:08.802775
=======
Time: 2025-02-13T12:57:14.927103
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:11.309402
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BMXE-6-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BMXE-6-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-02-24T18:53:11.567157
=======
Time: 2025-02-13T12:57:17.441115
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BKs7-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BKs7-65-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-02-13T12:57:17.785681
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:12.799781
=======
Time: 2025-02-13T12:57:19.221441
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:13.770843
=======
Time: 2025-02-13T12:57:20.221963
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:15.015819
=======
Time: 2025-02-13T12:57:21.479767
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:16.290767
=======
Time: 2025-02-13T12:57:22.565898
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:18.251306
=======
Time: 2025-02-13T12:57:29.540373
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:21.996381
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BMXT-6-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BMXT-6-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-02-24T18:53:22.213292
=======
Time: 2025-02-13T12:57:32.963853
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BKtO-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BKtO-65-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-02-13T12:57:33.243599
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:22.456013
=======
Time: 2025-02-13T12:57:33.379765
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:23.767444
=======
Time: 2025-02-13T12:57:34.284486
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:24.819121
=======
Time: 2025-02-13T12:57:35.209571
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:35.509784
=======
Time: 2025-02-13T12:57:45.080509
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:36.210431
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BMXb-6-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BMXb-6-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-02-24T18:53:36.451659
=======
Time: 2025-02-13T12:57:46.090107
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BKtZ-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BKtZ-65-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-02-13T12:57:46.445272
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:36.664314
=======
Time: 2025-02-13T12:57:46.801881
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:36.877173
=======
Time: 2025-02-13T12:57:47.184267
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:37.099591
=======
Time: 2025-02-13T12:57:47.323494
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:37.322775
=======
Time: 2025-02-13T12:57:47.464391
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:38.061006
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BMXh-6-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BMXh-6-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-02-24T18:53:38.295901
=======
Time: 2025-02-13T12:57:48.238077
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BKtg-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BKtg-65-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-02-13T12:57:48.378041
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:38.507956
=======
Time: 2025-02-13T12:57:48.512231
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:38.730914
=======
Time: 2025-02-13T12:57:48.654576
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:38.936337
=======
Time: 2025-02-13T12:57:48.842848
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:39.153371
=======
Time: 2025-02-13T12:57:48.983419
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:39.363608
=======
Time: 2025-02-13T12:57:49.117251
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:39.591686
=======
Time: 2025-02-13T12:57:49.296163
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:40.280176
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BMXp-6-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BMXp-6-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-02-24T18:53:40.504329
=======
Time: 2025-02-13T12:57:49.984765
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BKto-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BKto-65-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-02-13T12:57:50.130964
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:40.721679
=======
Time: 2025-02-13T12:57:50.264363
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
<<<<<<< Updated upstream
Time: 2025-02-24T18:53:41.151185
=======
Time: 2025-02-13T12:57:50.593732
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-02-13T14:30:50.311689
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-02-13T14:32:28.125829
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-02-13T14:37:08.895637
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-02-13T14:37:31.512511
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BLA1-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BLA1-65-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-02-13T14:37:35.494347
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-02-13T14:37:39.383648
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-02-13T14:37:48.001872
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BLBB-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BLBB-65-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-02-13T14:37:48.139527
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-02-13T14:37:50.013122
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-02-13T14:37:50.187868
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-02-13T14:37:51.244113
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-02-13T14:37:52.125839
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-02-13T14:37:53.210361
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-02-13T14:37:55.814302
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BLBR-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BLBR-65-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-02-13T14:37:56.145634
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-02-13T14:37:57.544073
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-02-13T14:37:58.772445
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-02-13T14:38:00.370406
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-02-13T14:38:01.556304
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-02-13T14:38:03.595797
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-02-13T14:38:07.25099
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BLBk-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BLBk-65-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-02-13T14:38:07.384687
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-02-13T14:38:07.51897
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-02-13T14:38:08.53926
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-02-13T14:38:09.606892
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-02-13T14:38:20.616653
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-02-13T14:38:21.282721
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BLBs-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BLBs-65-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-02-13T14:38:21.417023
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-02-13T14:38:21.549631
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-02-13T14:38:21.674416
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-02-13T14:38:21.803432
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-02-13T14:38:22.006022
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-02-13T14:38:22.641456
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BLBy-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BLBy-65-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-02-13T14:38:22.78096
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-02-13T14:38:22.942701
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-02-13T14:38:23.077468
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-02-13T14:38:23.213447
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-02-13T14:38:23.43532
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-02-13T14:38:23.652432
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-02-13T14:38:24.118893
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-02-13T14:38:24.994479
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BLB6-65-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BLB6-65-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-02-13T14:38:25.333623
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-02-13T14:38:25.693526
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-02-13T14:38:26.380812
>>>>>>> Stashed changes
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-03-17T15:53:17.379549
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-03-17T15:53:17.51517
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-03-17T15:53:17.818334
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-03-17T15:53:18.52761
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BPwv-140-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BPwv-140-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-03-17T15:53:18.691259
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-03-17T15:53:18.828445
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-03-17T15:53:19.447728
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BPwy-140-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BPwy-140-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-03-17T15:53:19.596992
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-03-17T15:53:19.746274
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-03-17T15:53:19.89425
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-03-17T15:53:20.038716
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-03-17T15:53:20.174622
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-03-17T15:53:20.306792
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-03-17T15:53:20.933601
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BPw5-140-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BPw5-140-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-03-17T15:53:21.075766
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-03-17T15:53:21.203074
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-03-17T15:53:21.327917
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-03-17T15:53:21.454393
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-03-17T15:53:21.582519
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-03-17T15:53:21.738069
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-03-17T15:53:22.348827
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BPxA-140-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BPxA-140-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-03-17T15:53:22.490531
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-03-17T15:53:22.656338
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-03-17T15:53:22.788917
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-03-17T15:53:22.956164
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-03-17T15:53:23.131083
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-03-17T15:53:23.736743
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BPxG-140-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BPxG-140-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-03-17T15:53:23.874284
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-03-17T15:53:24.002092
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-03-17T15:53:24.132179
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-03-17T15:53:24.306799
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-03-17T15:53:24.466003
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-03-17T15:53:25.104338
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BPxM-140-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BPxM-140-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-03-17T15:53:25.238302
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-03-17T15:53:25.368057
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-03-17T15:53:25.512825
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-03-17T15:53:25.693022
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-03-17T15:53:25.817335
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-03-17T15:53:25.958039
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-03-17T15:53:26.089833
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-03-17T15:53:26.687195
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BPxU-140-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BPxU-140-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-03-17T15:53:26.815421
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-03-17T15:53:26.950181
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-03-17T15:53:27.207724
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-04-30T16:24:15.880311
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-04-30T16:25:49.627881
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-04-30T16:30:15.931222
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-04-30T16:30:20.616496
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BZIF-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BZIF-27-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-04-30T16:30:22.797034
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-04-30T16:30:24.905859
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-04-30T16:30:31.486447
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BZIP-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BZIP-27-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-04-30T16:30:31.636194
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-04-30T16:30:35.979802
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-04-30T16:30:36.214164
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-04-30T16:30:37.55861
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-04-30T16:30:38.890405
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-04-30T16:30:41.069874
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-04-30T16:30:45.617649
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BZIi-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BZIi-27-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-04-30T16:30:45.801354
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-04-30T16:30:49.878182
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-04-30T16:30:50.804636
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-04-30T16:30:54.205876
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-04-30T16:30:55.645278
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-04-30T16:31:02.49434
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-04-30T16:31:05.998834
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BZI8-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BZI8-27-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-04-30T16:31:06.136411
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-04-30T16:31:06.256041
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-04-30T16:31:07.143387
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-04-30T16:31:07.998745
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-04-30T16:31:37.516926
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-04-30T16:31:38.13322
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BZJT-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BZJT-27-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-04-30T16:31:38.307229
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-04-30T16:31:38.439525
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-04-30T16:31:38.562689
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-04-30T16:31:38.708662
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-04-30T16:31:38.848619
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-04-30T16:31:39.481622
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BZJZ-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BZJZ-27-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-04-30T16:31:39.616498
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-04-30T16:31:39.743558
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-04-30T16:31:39.867034
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-04-30T16:31:39.994722
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-04-30T16:31:40.119272
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-04-30T16:31:40.252812
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-04-30T16:31:40.387309
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-04-30T16:31:41.221015
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BZJi-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BZJi-27-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-04-30T16:31:41.370615
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-04-30T16:31:41.521442
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-04-30T16:31:41.837616
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-04-30T18:38:13.659251
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-04-30T18:39:49.961456
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-04-30T18:44:26.563728
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-04-30T18:44:37.915772
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BbWO-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BbWO-27-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-04-30T18:44:39.084899
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-04-30T18:44:40.078667
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-04-30T18:44:46.224648
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BbWZ-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BbWZ-27-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-04-30T18:44:46.373034
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-04-30T18:44:51.265267
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-04-30T18:44:51.388561
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-04-30T18:44:51.530879
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-04-30T18:44:51.726858
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-04-30T18:44:51.847854
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-04-30T18:44:52.462153
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BbWo-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BbWo-27-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-04-30T18:44:52.60699
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-04-30T18:44:52.7576
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-04-30T18:44:53.078714
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-04-30T18:44:53.324551
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-04-30T18:44:53.448977
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-04-30T18:44:53.5736
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-04-30T18:44:54.273791
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BbWw-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BbWw-27-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-04-30T18:44:54.436777
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-04-30T18:44:54.577179
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-04-30T18:44:54.743941
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-04-30T18:44:54.872961
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-04-30T18:44:54.992715
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-04-30T18:44:55.689413
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BbW2-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BbW2-27-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-04-30T18:44:55.820216
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-04-30T18:44:55.970217
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-04-30T18:44:56.101743
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-04-30T18:44:56.250772
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-04-30T18:44:56.381109
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-04-30T18:44:57.052937
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BbW8-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BbW8-27-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-04-30T18:44:57.209326
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-04-30T18:44:57.343251
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-04-30T18:44:57.480071
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-04-30T18:44:57.603613
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-04-30T18:44:57.756749
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-04-30T18:44:57.881565
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-04-30T18:44:58.01062
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-04-30T18:44:58.668125
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BbXF-27-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BbXF-27-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-04-30T18:44:58.853764
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-04-30T18:44:59.013285
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-04-30T18:44:59.30471
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC01_CreateLead_InvalidMobile
Time: 2025-05-01T13:14:30.464516
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC02_CreateLead_EmptyMobile
Time: 2025-05-01T13:16:01.371099
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC04_AddBankDetail_InvalidAccount
Time: 2025-05-01T13:20:19.642617
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC05_UploadDoc_InvalidDocType
Time: 2025-05-01T13:20:33.934793
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BciK-171-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BciK-171-400)"}
----------------------------------------
Test: TC06_CreateLead_InvalidEmail
Time: 2025-05-01T13:20:35.426754
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC07_AddBankDetail_InvalidIFSC
Time: 2025-05-01T13:20:36.554283
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC08_UploadDoc_EmptyFile
Time: 2025-05-01T13:20:42.985467
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BciQ-171-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BciQ-171-400)"}
----------------------------------------
Test: TC09_CreateLead_InvalidPincode
Time: 2025-05-01T13:20:43.140692
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC10_CreateLead_InvalidBusinessName
Time: 2025-05-01T13:20:48.446698
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC11_CreateLead_InvalidGST
Time: 2025-05-01T13:20:48.595582
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC12_CreateLead_InvalidPAN
Time: 2025-05-01T13:20:49.769939
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC13_AddBankDetail_MismatchedName
Time: 2025-05-01T13:20:50.734317
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC14_CreateLead_InvalidAddress
Time: 2025-05-01T13:20:52.839742
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC15_UploadDoc_InvalidFormat
Time: 2025-05-01T13:20:57.213998
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-Bcie-171-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-Bcie-171-400)"}
----------------------------------------
Test: TC16_CreateLead_InvalidState
Time: 2025-05-01T13:20:57.357769
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC17_CreateLead_ExpiredToken
Time: 2025-05-01T13:21:01.994686
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC18_CreateLead_InvalidSolutionType
Time: 2025-05-01T13:21:03.106461
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC19_CreateLead_InvalidCity
Time: 2025-05-01T13:21:06.44878
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC20_CreateLead_InvalidContactPerson
Time: 2025-05-01T13:21:07.688135
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC21_AddBankDetail_InvalidBranch
Time: 2025-05-01T13:21:09.805446
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC22_UploadDoc_OversizedFile
Time: 2025-05-01T13:21:13.196699
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-Bcit-171-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-Bcit-171-400)"}
----------------------------------------
Test: TC23_CreateLead_InvalidAlternateMobile
Time: 2025-05-01T13:21:13.549319
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC24_CreateLead_InvalidDateFormat
Time: 2025-05-01T13:21:13.897017
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC25_CreateLead_InvalidBusinessCategory
Time: 2025-05-01T13:21:15.237264
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC26_CreateLead_InvalidWebsite
Time: 2025-05-01T13:21:16.18037
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC27_CreateLead_InvalidCoordinates
Time: 2025-05-01T13:21:59.614214
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC28_UploadDoc_InvalidExpiryDate
Time: 2025-05-01T13:22:00.293353
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-Bci1-171-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-Bci1-171-400)"}
----------------------------------------
Test: TC29_CreateLead_SpecialCharacters
Time: 2025-05-01T13:22:00.566404
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC30_CreateLead_InvalidMCC
Time: 2025-05-01T13:22:00.709515
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC31_CreateLead_InvalidTaxNumber
Time: 2025-05-01T13:22:00.962817
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC32_CreateLead_InvalidTurnover
Time: 2025-05-01T13:22:01.166848
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC33_CreateLead_InvalidCurrency
Time: 2025-05-01T13:22:01.305982
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC34_UploadDoc_InvalidFileSignature
Time: 2025-05-01T13:22:02.138889
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-Bci7-171-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-Bci7-171-400)"}
----------------------------------------
Test: TC35_CreateLead_MismatchedOwnerDetails
Time: 2025-05-01T13:22:02.301518
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC36_CreateLead_InvalidPostalCode
Time: 2025-05-01T13:22:02.60968
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC37_CreateLead_FutureIncorporationDate
Time: 2025-05-01T13:22:02.754474
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC38_CreateLead_InvalidRegistrationNumber
Time: 2025-05-01T13:22:03.003078
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC39_CreateLead_InvalidDirectorDetails
Time: 2025-05-01T13:22:03.304265
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC40_CreateLead_InvalidBusinessAddress
Time: 2025-05-01T13:22:03.658556
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC41_AddBankDetail_InvalidBranchCode
Time: 2025-05-01T13:22:03.822581
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC42_UploadDoc_MismatchedDetails
Time: 2025-05-01T13:22:04.448705
Status Code: 400
Response Body: {"agentTncStatus":true,"displayMessage":"Invalid Lead Id (Ref: G-BcjD-171-400)","solutionType":"merchant_common_onboard","allDocsUploaded":false,"message":"Invalid Lead Id (Ref: G-BcjD-171-400)"}
----------------------------------------
Test: TC43_CreateLead_InvalidSectorCode
Time: 2025-05-01T13:22:04.761904
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC44_CreateLead_InvalidContactFormat
Time: 2025-05-01T13:22:04.954857
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
Test: TC46_CreateLead_InvalidShareholderInfo
Time: 2025-05-01T13:22:05.360853
Status Code: 412
Response Body: {"agentTncStatus":true,"agentKycStatus":true,"displayMessage":"Invalid request","message":"Invalid request"}
----------------------------------------
