{
  <#if agentCustId??>"agentCustId": "${agentCustId}",</#if>
  <#if userCustId??>"userCustId": "${userCustId}",</#if>
  <#if merchantName??>"merchantName": "${merchantName}",</#if>
  <#if userMobile??>"userMobile": "${userMobile}",</#if>
  <#if mid??>"mid": "${mid}",</#if>
  <#if entityType??>"entityType": "${entityType}",</#if>
  <#if solutionType??>"solutionType": "${solutionType}",</#if>

  "fulfilmentBypassQuestionAnswerList": [
    {
      "questionAlias": "Select reason to close request",
      "answerAlias": "Service using Sim Replacement"
    }
  ],

  <#if leadId??>"leadId": "${leadId}",</#if>
  "additionalInfoMap": {
    <#if deviceId??>"deviceId": "${deviceId}",</#if>
    <#if model??>"model": "${model}",</#if>
    <#if vendor??>"vendor": "${vendor}"</#if>
  }

}
