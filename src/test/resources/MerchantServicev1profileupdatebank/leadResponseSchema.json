{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"refId": {"type": "string"}, "statusCode": {"type": "integer"}, "bankDetailsList": {"type": "array", "items": [{"type": "object", "properties": {"bankName": {"type": "string"}, "bankAccountNumber": {"type": "string"}, "ifsc": {"type": "string"}, "documents": {"type": "array", "items": {}}, "bankDetailsUuid": {"type": "string"}, "status": {"type": "integer"}}, "required": ["bankName", "bankAccountNumber", "ifsc", "documents", "bankDetailsUuid", "status"]}, {"type": "object", "properties": {"bankName": {"type": "string"}, "bankAccountNumber": {"type": "string"}, "ifsc": {"type": "string"}, "documents": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "docProvided": {"type": "string"}, "docURL": {"type": "string"}}, "required": ["docType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "docURL"]}]}, "bankDetailsUuid": {"type": "string"}, "status": {"type": "integer"}}, "required": ["bankName", "bankAccountNumber", "ifsc", "documents", "bankDetailsUuid", "status"]}, {"type": "object", "properties": {"bankName": {"type": "string"}, "bankAccountNumber": {"type": "string"}, "ifsc": {"type": "string"}, "bankAccountHolderName": {"type": "string"}, "documents": {"type": "array", "items": [{"type": "object", "properties": {"docType": {"type": "string"}, "docProvided": {"type": "string"}, "docURL": {"type": "string"}}, "required": ["docType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "docURL"]}]}, "bankDetailsUuid": {"type": "string"}, "status": {"type": "integer"}}, "required": ["bankName", "bankAccountNumber", "ifsc", "bankAccountHolderName", "documents", "bankDetailsUuid", "status"]}]}}, "required": ["refId", "statusCode", "bankDetailsList"]}