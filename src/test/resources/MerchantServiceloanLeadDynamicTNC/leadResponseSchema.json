{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"meta": {"type": "object", "properties": {"refId": {"type": "string"}, "status": {"type": "string"}}, "required": ["refId", "status"]}, "data": {"type": "object", "properties": {"state": {"type": "object", "properties": {"code": {"type": "string"}, "tncName": {"type": "string"}, "version": {"type": "integer"}, "url": {"type": "string"}, "accept": {"type": "integer"}, "uniqueIdentifier": {"type": "string"}, "md5": {"type": "string"}}, "required": ["code", "tncName", "version", "url", "accept", "uniqueIdentifier", "md5"]}}, "required": ["state"]}}, "required": ["meta", "data"]}