{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"status": {"type": "string"}, "state": {"type": "string"}, "tnCList": {"type": "array", "items": [{"type": "object", "properties": {"code": {"type": "string"}, "description": {"type": "string"}, "version": {"type": "integer"}}, "required": ["code", "description", "version"]}]}}, "required": ["status", "state", "tnCList"]}