{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"id": {"type": "integer"}, "phoneNumber": {"type": "string"}, "custId": {"type": "integer"}, "emailId": {"type": "string"}, "roleList": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}, "agentTncVersion": {"type": "integer"}, "createWallet": {"type": "string"}, "bankAgentType": {"type": "string"}, "agentName": {"type": "string"}, "logoutRequired": {"type": "integer"}, "ekycEnabled": {"type": "boolean"}, "agentTeam": {"type": "string"}, "status": {"type": "integer"}, "bucketZoneStates": {"type": "string"}, "createdAt": {"type": "string"}, "groups": {"type": "array", "items": {}}, "address": {"type": "object", "properties": {"id": {"type": "integer"}, "country": {"type": "null"}, "pincode": {"type": "integer"}, "state": {"type": "string"}, "city": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "line3": {"type": "null"}, "landmark": {"type": "null"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "residentialStatus": {"type": "null"}, "createdAt": {"type": "integer"}, "updatedAt": {"type": "integer"}, "status": {"type": "string"}, "addressUuid": {"type": "string"}}, "required": ["id", "country", "pincode", "state", "city", "line1", "line2", "line3", "landmark", "latitude", "longitude", "residentialStatus", "createdAt", "updatedAt", "status", "addressUuid"]}, "addressId": {"type": "integer"}, "userAdditionalInfo": {"type": "object", "properties": {"id": {"type": "integer"}, "userId": {"type": "integer"}, "kycPointOnboardedBy": {"type": "null"}, "inactiveReason": {"type": "null"}, "complianceReasons": {"type": "null"}, "recordType": {"type": "string"}, "agentSubtype": {"type": "string"}, "asmName": {"type": "null"}, "tncName": {"type": "null"}, "tlName": {"type": "null"}, "tlNumber": {"type": "null"}, "tlEmployeeId": {"type": "null"}, "reportingManagerName": {"type": "null"}, "reportingManagerCustId": {"type": "null"}, "dateOfJoining": {"type": "string"}, "dateOfLeaving": {"type": "null"}, "reasonOfLeaving": {"type": "null"}, "dateOfJoiningInDate": {"type": "null"}, "dateOfLeavingInDate": {"type": "null"}, "createdBy": {"type": "string"}, "lastModifiedBy": {"type": "null"}, "employeeId": {"type": "null"}, "opsManagerName": {"type": "null"}, "opsManagerId": {"type": "null"}, "designation": {"type": "null"}, "alternatePhoneNumber": {"type": "null"}, "displayName": {"type": "null"}}, "required": ["id", "userId", "kycPointOnboardedBy", "inactiveReason", "complianceReasons", "recordType", "agentSubtype", "asmName", "tncName", "tlName", "tlNumber", "tlEmployeeId", "reportingManagerName", "reportingManagerCustId", "dateOfJoining", "dateOfLeaving", "reasonOfLeaving", "dateOfJoiningInDate", "dateOfLeavingInDate", "created<PERSON>y", "lastModifiedBy", "employeeId", "opsManagerName", "opsManagerId", "designation", "alternatePhoneNumber", "displayName"]}, "bulkFlag": {"type": "boolean"}, "newRecord": {"type": "boolean"}, "showTncNum": {"type": "integer"}, "createWalletNum": {"type": "integer"}}, "required": ["id", "phoneNumber", "custId", "emailId", "roleList", "agentTncVersion", "createWallet", "bankAgentType", "<PERSON><PERSON><PERSON>", "logoutRequired", "ekycEnabled", "agentTeam", "status", "bucketZoneStates", "createdAt", "groups", "address", "addressId", "userAdditionalInfo", "bulkFlag", "newRecord", "showTncNum", "createWalletNum"]}