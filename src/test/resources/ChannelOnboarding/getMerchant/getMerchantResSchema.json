{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"agentTncStatus": {"type": "boolean"}, "agentKycStatus": {"type": "boolean"}, "mids": {"type": "array", "items": [{"type": "object", "properties": {"mid": {"type": "string"}, "edcBasedMid": {"type": "boolean"}, "smallMerchantDeclaration": {"type": "null"}, "category": {"type": "string"}, "subCategory": {"type": "string"}, "PPI_LIMIT": {"type": "string"}, "MERCHANT_NAME": {"type": "string"}}, "required": ["mid", "edcBasedMid", "smallMerchantDeclaration", "category", "subCategory", "PPI_LIMIT", "MERCHANT_NAME"]}, {"type": "object", "properties": {"mid": {"type": "string"}, "edcBasedMid": {"type": "boolean"}, "smallMerchantDeclaration": {"type": "null"}, "category": {"type": "string"}, "subCategory": {"type": "string"}, "PPI_LIMIT": {"type": "string"}, "MERCHANT_NAME": {"type": "string"}}, "required": ["mid", "edcBasedMid", "smallMerchantDeclaration", "category", "subCategory", "PPI_LIMIT", "MERCHANT_NAME"]}]}}, "required": ["agentTncStatus", "agentKycStatus", "mids"]}