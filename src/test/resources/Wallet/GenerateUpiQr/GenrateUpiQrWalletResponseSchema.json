{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"requestGuid": {"type": "null"}, "orderId": {"type": "null"}, "status": {"type": "string"}, "statusCode": {"type": "string"}, "statusMessage": {"type": "string"}, "response": {"type": "array", "items": [{"type": "object", "properties": {"stickerId": {"type": "string"}, "qrCodeId": {"type": "string"}, "upiHandle": {"type": "string"}, "mapResponse": {"type": "null"}}, "required": ["stickerId", "qrCodeId", "upiHandle", "mapResponse"]}]}}, "required": ["requestGuid", "orderId", "status", "statusCode", "statusMessage", "response"]}