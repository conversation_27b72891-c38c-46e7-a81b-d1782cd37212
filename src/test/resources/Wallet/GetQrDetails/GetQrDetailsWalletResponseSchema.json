{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"status": {"type": "string"}, "statusCode": {"type": "string"}, "statusMessage": {"type": "string"}, "response": {"type": "object", "properties": {"payeeType": {"type": "string"}, "currencyCode": {"type": "string"}, "category": {"type": "string"}, "subCategory": {"type": "string"}, "service": {"type": "string"}, "extendedInfo": {"type": "object", "properties": {"logoURL": {"type": "string"}}, "required": ["logoURL"]}, "invoiceDetails": {"type": "object", "properties": {"otherDetails": {"type": "array", "items": [{"type": "object", "properties": {"rowname": {"type": "string"}, "rowvalue": {"type": "string"}}, "required": ["rowname", "rowvalue"]}]}}, "required": ["otherDetails"]}, "mode": {"type": "string"}, "offlinePostConvenience": {"type": "boolean"}, "mappingId": {"type": "string"}, "pgEnabled": {"type": "boolean"}, "merchantTransId": {"type": "string"}, "qrCodeId": {"type": "string"}, "merchantVerified": {"type": "boolean"}, "REQUEST_TYPE": {"type": "string"}, "EXPIRY_DATE": {"type": "string"}, "NAME": {"type": "string"}, "MERCHANT_NAME": {"type": "string"}, "MOBILE_NO": {"type": "string"}, "TXN_AMOUNT": {"type": "string"}, "INDUSTRY_TYPE_ID": {"type": "string"}, "MERCHANT_GUID": {"type": "string"}, "ORDER_ID": {"type": "string"}, "MERCHANT_STATUS": {"type": "string"}, "CHANNEL_ID": {"type": "string"}}, "required": ["payeeType", "currencyCode", "category", "subCategory", "service", "extendedInfo", "invoiceDetails", "mode", "offlinePostConvenience", "mappingId", "pgEnabled", "merchantTransId", "qrCodeId", "merchantVerified", "REQUEST_TYPE", "EXPIRY_DATE", "NAME", "MERCHANT_NAME", "MOBILE_NO", "TXN_AMOUNT", "INDUSTRY_TYPE_ID", "MERCHANT_GUID", "ORDER_ID", "MERCHANT_STATUS", "CHANNEL_ID"]}}, "required": ["status", "statusCode", "statusMessage", "response"]}