package OCL.EDCDIY;
import Request.EDCDIY.DeviceFraudcheck;
import Services.EDCDIY.EDCDIYMiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.annotations.BeforeTest;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class FraudcheckDevice extends BaseMethod {
    EDCDIYMiddlewareServices edcdiymiddlewareservicesobject = new EDCDIYMiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FraudcheckDevice.class);

    String Token="";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {
        Map<String, String> jwtParams = new HashMap<>();
        UUID uuid = UUID.randomUUID();
        System.out.println("seceret key: "+ uuid.toString() );
        jwtParams.put("custId", "1701423974");
        Token = generateJwtToken("2d35025a-17d1-11ed-861d-0242ac120002", "BFF",jwtParams,false );
        LOGGER.info("Token : " +Token);

    }

    @Test(priority = 0, description = "Check api response when lat/long and pincode are of non-risky location", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "81.637");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "305001");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response when lat/long and pincode are of non-risky location " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
       // Assert.assertEquals(respObj.jsonPath().getString("action_recommended"),"PASS");
    }
    @Test(priority = 0, description = "Check api response when lat/long are of blocking location and pincode is of non-risky location", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "27.508476");
        body.put("longitude", "77.15865");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "305001");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response when lat/long are of blocking location and pincode is of non-risky location " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
       // Assert.assertEquals(respObj.jsonPath().getString("action_recommended"),"BLOCK");
    }
    @Test(priority = 0, description = "Check api response when lat/long are of non-risky location and pincode is of risky location", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "81.637");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "321024");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response when lat/long are of non-risky location and pincode is of risky location " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
      //  Assert.assertEquals(respObj.jsonPath().getString("action_recommended"),"BLOCK");
    }
    @Test(priority = 0, description = "Check api response when lat/long and pincode are of risky location", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "27.508476");
        body.put("longitude", "77.15865");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "321024");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response when lat/long and pincode are of risky location " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 200);
      //  Assert.assertEquals(respObj.jsonPath().getString("action_recommended"),"BLOCK");
    }
    @Test(priority = 0, description = "Check api response without custId in headers", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "27.508476");
        body.put("longitude", "77.15865");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "321024");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response without custId in headers " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 401);

    }
    @Test(priority = 0, description = "Check api response without token in headers", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", "");
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "27.508476");
        body.put("longitude", "77.15865");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "321024");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response without token in headers " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 401);

    }
    @Test(priority = 0, description = "Check api response without latitude value in body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "");
        body.put("longitude", "77.15865");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "321024");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response without latitude value in body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Check api response without longitude value in body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "321024");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response without longitude value in body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Check api response without pincode value in body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response without pincode value in body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Check api response without channel value in body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "81.637");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "305001");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response without channel value in body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 500);
    }
    @Test(priority = 0, description = "Check api response without mid value in body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "81.637");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "305001");
        body.put("mid", "");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("api response without mid value in body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Check api response without evaluationtype value in body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization",Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "81.637");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "305001");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "");
        body.put("customer_id", "1701423974");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("Check api response without evaluationtype value in body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }
    @Test(priority = 0, description = "Check api response without custID value in body", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_FraudcheckDevice() {

        DeviceFraudcheck devicefraudcheck = new DeviceFraudcheck(P.TESTDATA.get("DeviceFraudcheckRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("authorization", Token);
        headers.put("custId", "1701423974");

        Map<String, String> body = new HashMap<String, String>();
        body.put("latitude", "21.237");
        body.put("longitude", "81.637");
        body.put("solutionType", "map_edc");
        body.put("user_phone", "9999111753");
        body.put("agent_id", "1107195733");
        body.put("channel", "DIY_P4B_APP");
        body.put("display_name", "TOUCH WOOD LIMITED");
        body.put("requestType", "map_edc");
        body.put("business_address_line1", "Pocket C4 house number 260");
        body.put("business_address_line2", "sector6");
        body.put("business_address_line3", "DEOGHAR");
        body.put("business_address_city", "DEOGHAR");
        body.put("business_address_state", "JHARKHAND");
        body.put("business_address_pin", "305001");
        body.put("mid", "ADfyin30849684056371");
        body.put("device_type", "EDC");
        body.put("device_name", "GM1901");
        body.put("os_version", "10");
        body.put("device_id", "OnePlus-GM1901-9046eec81af9340b");
        body.put("evaluationType", "edc_deployment");
        body.put("customer_id", "");

        Response respObj = edcdiymiddlewareservicesobject.edcDiyMiddlewareServices(devicefraudcheck,body, headers);

        LOGGER.info("Check api response without custID value in body " + respObj.statusCode());

        Assert.assertEquals(respObj.statusCode(), 400);
    }

}
