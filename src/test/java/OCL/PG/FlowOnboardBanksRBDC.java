package OCL.PG;

import Request.PG.OnboardBanks;
import Request.PG.OnboardBanksRBDC;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowOnboardBanksRBDC extends BaseMethod
{
    PGServices PGServicesObj = new PGServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowOnboardBanksRBDC.class);


    public static String MobileNumber = "**********";
    public static String Token = "";
    public static String TID = "";
    public static String SerialNumber = "";
    public static String ModelName = "";
    public static String TerminalStatus = "";
    public static String mid = "Aggreg66852330295863";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 0, description = "Onboard RBDC Bank on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_OnboardBanksONPGForRBDC()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
         SerialNumber = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", SerialNumber);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", mid);
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid mid", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_OnboardBanksONPGWithInvalidMid()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg668523302958631");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg668523302958631");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"MID is invalid.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with empty mid", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_OnboardBanksONPGWithEmptyMid()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"MID is mandatory field, cannot be empty.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid model name", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_OnboardBanksONPGWithInvalidModelName()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A9101");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Device Model is not Valid.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with empty model name", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_OnboardBanksONPGWithEmptyModelName()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Device Model is not Valid.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid model name", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_OnboardBanksONPGWithnvalidOsType()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID1");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_1906"),"OS Type is not Valid.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with empty model name", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_OnboardBanksONPGWithEmptyOsType()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_1906"),"OS Type is not Valid.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid vendor ", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_OnboardBanksONPGWithnvalidVendor()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX1");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Vendor Name is not Valid.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with empty vendor", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_OnboardBanksONPGWithEmptyVendor()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROiD");
        body.put("vendorName", "");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Vendor name is mandatory field, cannot be empty.");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with invalid source ", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_OnboardBanksONPGWithnvalidSource()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE1");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("error"),"Bad Request");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with empty source", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_OnboardBanksONPGWithEmptySource()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("error"),"Bad Request");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid instrument ", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_OnboardBanksONPGWithnvalidinstrument()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM1");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Only UPI and SYSTEM instrument are supported!");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with empty instrument", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_OnboardBanksONPGWithEmptyinstrument()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", MapSerialNo);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Only UPI and SYSTEM instrument are supported!");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with empty serial number", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_OnboardBanksONPGWithEmptySerialNumber()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", " ");
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Device serial number is mandatory field.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG when terminal already onboarded", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_OnboardBanksONPGWhenTerminalAlreadyOnboarded()
    {
        OnboardBanksRBDC OnboardBanksRBDCObj = new OnboardBanksRBDC(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);


        Map<String, String> body = new HashMap<>();
        body.put("mid", "Aggreg66852330295863");
        body.put("serialNo", SerialNumber);
        body.put("modelName", "A910");
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("mid", "Aggreg66852330295863");
        body.put("instrument", "SYSTEM");

        Response respObj = PGServicesObj.onboardBanksRBDCOnPG(OnboardBanksRBDCObj, headers, body);
        int StatusCode = respObj.getStatusCode();
       String actual= respObj.jsonPath().getJsonObject("acquirerStatus.statusMsg").toString();
       Assert.assertTrue(actual.contains("Terminal already onboarded. Paytm TID : "));
        Assert.assertEquals(StatusCode, 200);

    }

}
