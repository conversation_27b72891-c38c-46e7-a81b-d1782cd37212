package OCL.PG;

import Request.PG.ConfigureMbidOnPG;
import Request.PG.OnboardBanks;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;


public class ConfigureMbid extends BaseMethod
{
    PGServices PGServicesObj = new PGServices();
    private static final Logger LOGGER = LogManager.getLogger(ConfigureMbid.class);


    public static String MobileNumber = "**********";
    public static String Token = "";
    public static String mid = "pvufVk98108581129485";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }


    @Test(priority = 0, description = "Configure Mbid on PG", groups = {"Regression"}, dependsOnMethods = "TC_001")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;

        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_444");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.CHANNEL[0]"),"Auth Mode,Comm Mode,Bank and Industry already configured");

    }
    @Test(priority = 0, description = "Configure Mbid on PG with invalid mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;

        Map<String, String> body = new HashMap<>();
        body.put("MID", "pvufVk981085811294851");
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_400");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.MID[0]"),"Invalid Mid");

    }
    @Test(priority = 0, description = "Configure Mbid on PG with empty mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;

        Map<String, String> body = new HashMap<>();
        body.put("MID", " ");
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_400");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.MID[0]"),"MID required.");

    }
    @Test(priority = 0, description = "Configure Mbid on PG with invalid bank", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;

        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC1");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_500");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.BANK[0]"),"Invalid BANK:AWDC1");

    }
    @Test(priority = 0, description = "Configure Mbid on PG with empty bank", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", " ");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_400");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.BANK[0]"),"BANK required.");

    }

    @Test(priority = 0, description = "Configure Mbid on PG with invalid channel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC1");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_500");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.CHANNEL[0]"),"Invalid CHANNEL");

    }

    @Test(priority = 0, description = "Configure Mbid on PG with empty channel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", " ");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_500");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.CHANNEL[0]"),"Invalid CHANNEL");

    }
    @Test(priority = 0, description = "Configure Mbid on PG with invalid paymode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC1");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_400");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.instantSettlement[0]"),"Instant Settlement can only be enabled for EMI_CARDLESS or Primary NB,DC,CC & UPI paymodes.");

    }

    @Test(priority = 0, description = "Configure Mbid on PG with empty paymode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", " ");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_400");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.instantSettlement[0]"),"Instant Settlement can only be enabled for EMI_CARDLESS or Primary NB,DC,CC & UPI paymodes.");

    }

    @Test(priority = 0, description = "Configure Mbid on PG with empty paymode & INSTANT_SETTLEMENT_ENABLED false ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", " ");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "false");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_500");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.PAY_MODE[0]"),"Invalid PAYMENT INSTRUMENT");

    }
    @Test(priority = 0, description = "Configure Mbid on PG with empty paymode1 & INSTANT_SETTLEMENT_ENABLED false ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC1");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[1]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[1]"),"BO_400");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.instantSettlement[1]"),"Instant Settlement can only be enabled for EMI_CARDLESS or Primary NB,DC,CC & UPI paymodes.");

    }
    @Test(priority = 0, description = "Configure Mbid on PG with empty mbid ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", " ");
        body.put("BANK", "AWDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
        Assert.assertEquals(respObj.jsonPath().getString("STATUS[0]"),"ERROR");
        Assert.assertEquals(respObj.jsonPath().getString("STATUS_CODE[0]"),"BO_500");
        Assert.assertEquals(respObj.jsonPath().getJsonObject("DATA.MBID[0]")," ");

    }
    @Test(priority = 0, description = "Configure Mbid on PG for bank-amxe ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "AMXE");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Configure Mbid on PG for bank-hfpp ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "HFPP");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
        @Test(priority = 0, description = "Configure Mbid on PG for bank-sbpp ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "SBPP");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Configure Mbid on PG for bank-RBDC ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "RBDC");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Configure Mbid on PG for bank-edcd ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "EDCD");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "FALSE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Configure Mbid on PG with deactivate edc true ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "EDCD");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", "TRUE");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Configure Mbid on PG with deactivate edc empty ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020()
    {
        ConfigureMbidOnPG ConfigureMbidOnPGObj = new ConfigureMbidOnPG(P.TESTDATA.get("ConfigureMbidOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer MBIDCreate = Utilities.randomNumberGenerator(4);
        String MBID = "JYOT" + MBIDCreate;


        Map<String, String> body = new HashMap<>();
        body.put("MID", mid);
        body.put("MBID", MBID);
        body.put("BANK", "EDCD");
        body.put("CHANNEL", "EDC");
        body.put("PAY_MODE", "DC");
        body.put("PAY_MODE1", "CC");
        body.put("INSTANT_SETTLEMENT_ENABLED", "true");
        body.put("DEACTIVATE_EDC", " ");

        Response respObj = PGServicesObj.configureMbidOnPG(ConfigureMbidOnPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
}
