package OCL.PG;

import Request.PG.ActiveTerminalInPG;
import Request.PG.CreateTerminalInPG;
import Request.PG.OnboardBanks;
import Request.PG.ReplaceTerminalInPG;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowReplaceTerminalInPG extends BaseMethod {
    PGServices PGServicesObj = new PGServices();
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowCreateTerminalInPG.class);


    public static String MobileNumber = "**********";
    public static String Token = "";
    public static String AgentToken = "";
    public static String TID = "";
    public static String SerialNumber = "";
    public static String EDCNewSerialNumber = "";
    public static String ModelName = "";
    public static String TerminalStatus = "";
    public static String version = "4.6.9";
    public static String BankCode = "";
    public static String mid = "pvufVk98108581129485";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }


    @Test(priority = 1, description = "Onboard SBPP Bank on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_OnboardBanksONPGForSBPP() {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        TID = respObj.jsonPath().getString("request.tid");
        LOGGER.info("TID is : " + TID);
        TerminalStatus = respObj.jsonPath().getJsonObject("acquirerStatus.status").toString();
        LOGGER.info("Terminal Status is : " + TerminalStatus);
        SerialNumber = respObj.jsonPath().getJsonObject("request.serialNo").toString();
        LOGGER.info("Serial Number is : " + SerialNumber);
        ModelName = respObj.jsonPath().getString("request.modelName");
        LOGGER.info("Model name is : " + ModelName);
        BankCode = respObj.jsonPath().getString("acquirerStatus.bankName");
        LOGGER.info("Bank code is : " + BankCode);
        int StatusCode = respObj.getStatusCode();
        if (StatusCode == 400) {
            Response respObj1 = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }

    }

    @Test(priority = 2, description = "Create Terminal in PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_ActiveTerminalInPG() {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", SerialNumber);
        body.put("tid", TID);
        body.put("modelName", ModelName);
        body.put("bankCode", "SBPP");

        Response respObj = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if (StatusCode == 400) {
            Response respObj1 = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }

    }

    @Test(priority = 3, description = "Replace Terminal in PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_ReplaceTerminalInPGWhenDeviceAlreadyReturned() {
        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", "AERJD356ty7");
        body.put("serialNo1", "JBKUSN8");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");
        body.put("source", "OE");
        body.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertTrue(respObj.jsonPath().getString("EDC_TMS2001").contains("No Machine found against this MID as device already returned"));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with invalid mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_ReplaceTerminalInPGWithInvalidMid() {
        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", "KyeiXd633865179820231");
        body.put("serialNo", "JBKUSN7");
        body.put("serialNo1", "JBKUSN8");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");
        body.put("source", "OE");
        body.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("MID is invalid."));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with empty mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_ReplaceTerminalInPGWithEmptyMid() {
        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", " ");
        body.put("serialNo", "JBKUSN7");
        body.put("serialNo1", "JBKUSN8");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");
        body.put("source", "OE");
        body.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertTrue(respObj.jsonPath().getString("EDC_304").contains("MID is mandatory field, cannot be empty."));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with invalid serial number", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_ReplaceTerminalInPGWithInvalidSerialNumber() {
        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", "123");
        body.put("serialNo1", "JBKUSN8");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");
        body.put("source", "OE");
        body.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertTrue(respObj.jsonPath().getString("EDC_TMS2000").contains("Invalid mid serial number combination"));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with empty serial number", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_ReplaceTerminalInPGWithEmptySerialNumber() {
        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", " ");
        body.put("serialNo1", "JBKUSN8");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");
        body.put("source", "OE");
        body.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertTrue(respObj.jsonPath().getString("EDC_TMS2000").contains("Invalid mid serial number combination"));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with invalid vendor", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_ReplaceTerminalInPGWithInvalidVendor() {
        Integer SerialNo = Utilities.randomNumberGenerator(8);
        EDCNewSerialNumber = "JYOTK" + SerialNo;

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX1");
        body1.put("modelName", "A50");
        body1.put("source", "OE");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertTrue(respObj2.jsonPath().getString("EDC_304").contains("Vendor Name is not Valid."));
        Assert.assertEquals(StatusCode2, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with empty vendor", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_ReplaceTerminalInPGWithEmptyVendor() {

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", " ");
        body1.put("modelName", "A50");
        body1.put("source", "OE");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertTrue(respObj2.jsonPath().getString("EDC_304").contains("Vendor name is mandatory field, cannot be empty."));
        Assert.assertEquals(StatusCode2, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with invalid model number", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_ReplaceTerminalInPGWithInvalidModelNumber() {

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX");
        body1.put("modelName", "A501");
        body1.put("source", "OE");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertTrue(respObj2.jsonPath().getString("EDC_304").contains("Device Model is not Valid."));
        Assert.assertEquals(StatusCode2, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with empty model number", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_ReplaceTerminalInPGWithEmptyModelNumber() {

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX");
        body1.put("modelName", " ");
        body1.put("source", "OE");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertTrue(respObj2.jsonPath().getString("EDC_304").contains("Device Model is not Valid."));
        Assert.assertEquals(StatusCode2, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with invalid source", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_ReplaceTerminalInPGWithInvalidSource() {

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX");
        body1.put("modelName", "A50");
        body1.put("source", "OE1");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertEquals(StatusCode2, 400);

    }

    @Test(priority = 3, description = "Replace Terminal in PG with empty source ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_ReplaceTerminalInPGWithEmptySource() {

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX");
        body1.put("modelName", "A50");
        body1.put("source", " ");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertEquals(StatusCode2, 400);

    }

    @Test(priority = 4, description = "Replace Terminal in PG with valid data ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_ReplaceTerminalInPGWithValidData() {

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        EDCNewSerialNumber = "JYOTK" + SerialNo;

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX");
        body1.put("modelName", "A50");
        body1.put("source", "OE");
        body1.put("requestId", "@1233");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        if(StatusCode2 !=200)
        {
            Response respObj3 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
            int StatusCode3 = respObj3.getStatusCode();
            Assert.assertEquals(StatusCode3, 200);
        }


    }

   /* @Test(priority = 3, description = "Replace Terminal in PG with empty request id", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_ReplaceTerminalInPGWithEmptyRequestId() {

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX");
        body1.put("modelName", "A50");
        body1.put("source", "OE");
        body1.put("requestId", "#$");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertEquals(StatusCode2, 200);

    }

    @Test(priority = 5, description = "Replace Terminal in PG with diff model", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_ReplaceTerminalInPGWithDiffModel() {
        Integer SerialNo = Utilities.randomNumberGenerator(8);
        EDCNewSerialNumber = "JYOTK" + SerialNo;

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "NEXGO");
        body1.put("modelName", "NEXGOG2PLUS");
        body1.put("source", "OE");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertEquals(StatusCode2, 200);
    }
*/
   /* @Test(priority = 4, description = "Replace Terminal in PG with correct details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_ReplaceTerminalInPGWithValidData() {
        Integer SerialNo = Utilities.randomNumberGenerator(8);
        EDCNewSerialNumber = "JYOTK" + SerialNo;

        ReplaceTerminalInPG ReplaceTerminalInPGObj = new ReplaceTerminalInPG(P.TESTDATA.get("ReplaceTerminalOnPGRequestBody"));

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("Content-Type", "application/json");
        headers1.put("x-sso-token", Token);

        Map<String, String> body1 = new HashMap<>();
        body1.put("mid", mid);
        body1.put("serialNo", SerialNumber);
        body1.put("serialNo1", EDCNewSerialNumber);
        body1.put("vendorName", "PAX");
        body1.put("modelName", "A910");
        body1.put("source", "OE");
        body1.put("requestId", "d784a6fb-c91e-4b47-9db7-5c158aa50ee2");


        Response respObj2 = PGServicesObj.replaceEDCMachine(ReplaceTerminalInPGObj, headers1, body1);
        int StatusCode2 = respObj2.getStatusCode();
        Assert.assertEquals(StatusCode2, 200);
    }
*/

    @Test(priority = 5, description = "Create Terminal in PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_CreateTerminalInPGWithValidDataWithPEDCBank() {
        CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");

        Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        TID = respObj.jsonPath().getString("tid");
        LOGGER.info("TID is : " + TID);
        TerminalStatus = respObj.jsonPath().getString("status");
        LOGGER.info("Terminal Status is : " + TerminalStatus);
        SerialNumber = respObj.jsonPath().getString("serialNo");
        LOGGER.info("Serial Number is : " + SerialNumber);
        ModelName = respObj.jsonPath().getString("modelName");
        LOGGER.info("Model name is : " + ModelName);
        int StatusCode = respObj.getStatusCode();
        if (StatusCode == 400) {
            Response respObj1 = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertTrue(respObj1.jsonPath().getString("BO_474").contains("PEDC acquirer configuration is no longer supported by this API"));
            Assert.assertEquals(StatusCode1, 400);
        }
    }
}
