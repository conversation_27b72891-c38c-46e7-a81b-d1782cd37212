package OCL.PG;


import Request.PG.UPIAddChannel;
import Request.PG.UPIEligibilityOnBoss;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowApplyYesBankDQR extends BaseMethod {
    PGServices PGServicesObj = new PGServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowApplyYesBankDQR.class);


    public static String MobileNumber = "**********";
    public static String Token = "";
    public static String cookie = "BOSS_SESSION=5e3c22b1-2a7c-425e-8764-1184e0f88127";
//    public static String cookie = "BOSS_SESSION=ae42cb4b-cce2-40fb-9da9-6ade683ac4a5";

    public static String mid = "eDMper22281548422107";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 0, description = "Yes bank Eligibility check on PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_YESBankEligibilityCheck() {
        UPIEligibilityOnBoss UPIEligibilityOnBossObj = new UPIEligibilityOnBoss();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> params = new HashMap<String, String>();
        params.put("mid", mid);

        Response respObj = PGServicesObj.checkUPIEligibilityOnBoss(UPIEligibilityOnBossObj, headers, params);
        int StatusCode = respObj.getStatusCode();
        if (StatusCode == 400) {
            Response respObj1 = PGServicesObj.checkUPIEligibilityOnBoss(UPIEligibilityOnBossObj, headers, params);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
    }

    @Test(priority = 0, description = "Yes bank Eligibility check on PG with invalid mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_YESBankEligibilityCheckWithInvalidMid() {
        UPIEligibilityOnBoss UPIEligibilityOnBossObj = new UPIEligibilityOnBoss();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> params = new HashMap<String, String>();
        params.put("mid", "eDMper2228");

        Response respObj = PGServicesObj.checkUPIEligibilityOnBoss(UPIEligibilityOnBossObj, headers, params);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("BO_412"), "Invalid Mid");

    }

    @Test(priority = 0, description = "Yes bank Eligibility check on PG with empty mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_YESBankEligibilityCheckWithEmptyMid() {
        UPIEligibilityOnBoss UPIEligibilityOnBossObj = new UPIEligibilityOnBoss();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> params = new HashMap<String, String>();
        params.put("mid", " ");

        Response respObj = PGServicesObj.checkUPIEligibilityOnBoss(UPIEligibilityOnBossObj, headers, params);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("BO_412"), "Invalid Mid");
    }

    @Test(priority = 0, description = "Yes bank Eligibility check on PG with invalid cookie", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_YESBankEligibilityCheckWithInvalidCookie() {
        UPIEligibilityOnBoss UPIEligibilityOnBossObj = new UPIEligibilityOnBoss();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", "abdd");

        Map<String, String> params = new HashMap<String, String>();
        params.put("mid", mid);

        Response respObj = PGServicesObj.checkUPIEligibilityOnBoss(UPIEligibilityOnBossObj, headers, params);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 403);
        Assert.assertEquals(respObj.jsonPath().getString("message"), "Access Denied");


    }

    @Test(priority = 0, description = "Yes bank Eligibility check on PG with empty cookie", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_YESBankEligibilityCheckWithEmptyCookie()
    {
        UPIEligibilityOnBoss UPIEligibilityOnBossObj = new UPIEligibilityOnBoss();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", " ");

        Map<String, String> params = new HashMap<String, String>();
        params.put("mid", mid);

        Response respObj = PGServicesObj.checkUPIEligibilityOnBoss(UPIEligibilityOnBossObj, headers, params);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 403);
        Assert.assertEquals(respObj.jsonPath().getString("message"), "Access Denied");


    }

    @Test(priority = 0, description = "Add UPI Channel on PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_AddUPIChannelOnPG()
    {
        UPIAddChannel UPIAddChannelObj = new UPIAddChannel(P.TESTDATA.get("AddUPIChannel"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", mid);
        body.put("bankName", "PTYES");

        Response respObj = PGServicesObj.verifyUPIAddChannel(UPIAddChannelObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if (StatusCode == 400)
        {
            Response respObj1 = PGServicesObj.verifyUPIAddChannel(UPIAddChannelObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
    }

    @Test(priority = 0, description = "Add UPI Channel on PG with empty mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_AddUPIChannelOnPGWithEmptyMid() {
        UPIAddChannel UPIAddChannelObj = new UPIAddChannel(P.TESTDATA.get("AddUPIChannel"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", " ");
        body.put("bankName", "PTYES");

        Response respObj = PGServicesObj.verifyUPIAddChannel(UPIAddChannelObj, headers, body);
//        int StatusCode = respObj.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//        Assert.assertEquals(respObj.jsonPath().getString("BO_733"), "Error while adding UPI channel");
    }

    @Test(priority = 0, description = "Add UPI Channel on PG with invalid mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_AddUPIChannelOnPGWithInvalidMid() {
        UPIAddChannel UPIAddChannelObj = new UPIAddChannel(P.TESTDATA.get("AddUPIChannel"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "abdc");
        body.put("bankName", "PTYES");

        Response respObj = PGServicesObj.verifyUPIAddChannel(UPIAddChannelObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("LS_500"), "Invalid Mid");
    }

    @Test(priority = 0, description = "Add UPI Channel on PG with empty bank name", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_AddUPIChannelOnPGWithEmptyBankName()
    {
        UPIAddChannel UPIAddChannelObj = new UPIAddChannel(P.TESTDATA.get("AddUPIChannel"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "eDMper22281548422107");
        body.put("bankName", " ");

        Response respObj = PGServicesObj.verifyUPIAddChannel(UPIAddChannelObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
        Assert.assertEquals(respObj.jsonPath().getString("BO_733"), "Error while adding UPI channel");
    }

    @Test(priority = 0, description = "Add UPI Channel on PG with invalid bankName", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_AddUPIChannelOnPGWithInvalidBankName()
    {
        UPIAddChannel UPIAddChannelObj = new UPIAddChannel(P.TESTDATA.get("AddUPIChannel"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("cookie", cookie);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid", "eDMper22281548422107");
        body.put("bankName", "PTYES1");

        Response respObj = PGServicesObj.verifyUPIAddChannel(UPIAddChannelObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 500);
        Assert.assertEquals(respObj.jsonPath().getString("BO_733"), "Error while adding UPI channel");
    }
}
