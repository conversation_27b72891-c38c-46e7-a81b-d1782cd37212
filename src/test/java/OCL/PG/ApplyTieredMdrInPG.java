package OCL.PG;

import Request.PG.ApplyTieredMdr;
import Request.PG.CreateTerminalInPG;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class ApplyTieredMdrInPG extends BaseMethod
{

    PGServices PGServicesObj = new PGServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowCreateTerminalInPG.class);


    public static String MobileNumber = "9891497839";
    public static String Token = "";
    public static String AgentToken="";
    public static String TID = "";
    public static String SerialNumber = "";
    public static String ModelName = "";
    public static String TerminalStatus = "";
    public static String version="5.1.6";
    public static String mid = "FNrNTk11503758289999";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 0, description = "Apply tiered mdr in PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_ApplyTieredMdrInPG() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with empty preference", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_ApplyTieredMdrInPGWithEmptyPref() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", " ");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with invalid preference", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_ApplyTieredMdrInPGWithInvalidPref() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "abc");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with empty transaction type", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_ApplyTieredMdrInPGWithEmptyTransactionType() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", " ");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with invalid TransactionType", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_ApplyTieredMdrInPGWithInvalidTransactionType() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments1");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, description = "Apply tiered mdr in PG with empty paymode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_ApplyTieredMdrInPGWithEmptyPaymode() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", " ");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with invalid paymode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_ApplyTieredMdrInPGWithInvalidPaymode() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC1");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, description = "Apply tiered mdr in PG with empty IsTieredFlag", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_ApplyTieredMdrInPGWithEmptyIsTieredFlag() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", " ");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 500);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with invalid IsTieredFlag", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_ApplyTieredMdrInPGWithInvalidIsTieredFlag() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true1");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, description = "Apply tiered mdr in PG with empty convenienceModel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_ApplyTieredMdrInPGWithEmptyConvenienceModel() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", " ");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with invalid convenienceModel", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_ApplyTieredMdrInPGWithInvalidConvenienceModel() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF1");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }

    @Test(priority = 0, description = "Apply tiered mdr in PG with empty source", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_ApplyTieredMdrInPGWithEmptySource() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
       params.put("source", " ");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with invalid source", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_ApplyTieredMdrInPGWithInvalidSource() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE1");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "PAY_METHOD_LEVEL");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Apply tiered mdr in PG with none preference", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_ApplyTieredMdrInPGWithNonePref() {

        ApplyTieredMdr ApplyTieredMdrObj=new ApplyTieredMdr(mid);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("source", "OE");

        Map<String, String> body = new HashMap<>();
        body.put("businessTierPref", "NONE");
        body.put("transactionType", "Payments");
        body.put("paymode", "CC");
        body.put("isTiered", "true");
        body.put("convenienceModel", "PCF");

        Response respObj = PGServicesObj.applyTieredMdr(ApplyTieredMdrObj, headers, params,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }







}
