package OCL.PG;

import Request.PG.OnboardBanks;
import Services.PG.PGServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowOnboardBanksOnPG extends BaseMethod
{
    PGServices PGServicesObj = new PGServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowOnboardBanksOnPG.class);


    public static String MobileNumber = "**********";
    public static String Token = "";
    public static String TID = "";
    public static String SerialNumber = "";
    public static String ModelName = "";
    public static String TerminalStatus = "";
    public static String mid = "Aggreg66852330295863";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 0, description = "Onboard SBPP Bank on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_OnboardBanksONPGForSBPP()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid mid", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_OnboardBanksONPGWithInvalidMid()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksRBDCOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "A910");
        body.put("vendorName", "PAX");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"MID is invalid.");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with empty mid", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_OnboardBanksONPGwithEmptyMid()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", "");
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"MID is mandatory field, cannot be empty.");


    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid serial number", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_OnboardBanksONPGWithInvalidSerialNumber()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", "@");
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Device serial number can only be alphanumeric.");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with empty serial number", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_OnboardBanksONPGwithEmptySerialNumber()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", " ");
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Device serial number is mandatory field.");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with invalid os type", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_OnboardBanksONPGWithInvalidOSType()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo",MapSerialNo );
        body.put("osType", "LINUX1");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_1906"),"OS Type is not Valid.");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with empty OS Type", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_OnboardBanksONPGwithEmptyOSType()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", " ");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_1906"),"OS Type is not Valid.");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with invalid vendor", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_OnboardBanksONPGWithInvalidVendor()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo",MapSerialNo );
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO1");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Vendor Name is not Valid.");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with empty vendor", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_OnboardBanksONPGwithEmptyVendor()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", " ");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Vendor name is mandatory field, cannot be empty.");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid source", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_OnboardBanksONPGWithInvalidSource()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo",MapSerialNo );
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE1");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("error"),"Bad Request");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with empty source", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_OnboardBanksONPGwithEmptyVendor()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", " ");
        body.put("bankName", "SBPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("error"),"Bad Request");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with invalid bankName", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_OnboardBanksONPGWithInvalidBankName()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo",MapSerialNo );
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP1");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Request invalid as bank [SBPP1] are not supported ");

    }
    @Test(priority = 0, description = "Onboard Banks on PG with empty bank name", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_OnboardBanksONPGwithEmptyBankName()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", " ");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"bankName should not be blank or null in acquiringList");

    }

    @Test(priority = 0, description = "Onboard Banks on PG with invalid bank tid", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_OnboardBanksONPGWithInvalidBankTid()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo",MapSerialNo );
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", "@");

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 0, description = "Onboard Banks on PG with empty bank tid", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_OnboardBanksONPGwithEmptyBankTid()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP");
        body.put("bankTid", " ");

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Request invalid as bankTid is null for [SBPP] bank");

    }
    @Test(priority = 0, description = "Onboard HFPP Bank on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_OnboardBanksONPGForHFPP()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "HFPP");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
    }
    @Test(priority = 0, description = "Onboard AWDC Bank on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_OnboardBanksONPGForAWDC()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "AWDC");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
    }

    @Test(priority = 0, description = "Onboard AMXE Bank on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_OnboardBanksONPGForAMXE()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "AMXE");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        if(StatusCode==400)
        {
            Response respObj1 = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
            int StatusCode1 = respObj1.getStatusCode();
            Assert.assertEquals(StatusCode1, 200);
        }
    }

    @Test(priority = 0, description = "Onboard Two banks Bank on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_OnboardTwoBanksONPG()
    {
        OnboardBanks OnboardBanksObj = new OnboardBanks(P.TESTDATA.get("OnboardBanksOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;


        Integer bankTid = Utilities.randomNumberGenerator(3);
        String MapBankTid = "183" + bankTid;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "NEXGO");
        body.put("source", "OE");
        body.put("bankName", "SBPP,AWDC");
        body.put("bankTid", MapBankTid);

        Response respObj = PGServicesObj.onboardBanksOnPG(OnboardBanksObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);
        Assert.assertEquals(respObj.jsonPath().getString("EDC_304"),"Request invalid as bank [SBPP,AWDC] are not supported ");


    }

}
