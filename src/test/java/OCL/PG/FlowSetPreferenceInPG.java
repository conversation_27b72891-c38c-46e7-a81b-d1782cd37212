package OCL.PG;

import Request.PG.CreateTerminalInPG;
import Request.PG.EditMerchantOnPG;
import Request.PG.GetPreference;
import Services.MechantService.MiddlewareServices;
import Services.PG.PGServices;
import Services.PGP.PGPServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowSetPreferenceInPG extends BaseMethod
{
    PGServices PGServicesObj = new PGServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowSetPreferenceInPG.class);

    public static String MobileNumber = "9891497839";
    public static String Token = "";
    public static String preferenceName="";
    public static String preferenceValue = "";
    public static String SerialNumber = "";
    public static String ModelName = "";
    public static String TerminalStatus = "";
    public static String version="5.0.9";
    public static String mid = "oHGIDR41525363490062";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }


    @Test(priority = 0, description = "Set Preference on PG", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_GetPreferenceOnPG() {
        GetPreference GetPreferenceObj=new GetPreference(mid);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Response respObj = PGServicesObj.getPreferenceFromPG(GetPreferenceObj,headers);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        String bodyresp=respObj.asString();


    }

    @Test(priority = 0, description = "Print Preference Name on PG", groups = {"Regression"}, dependsOnMethods = "TC_002_GetPreferenceOnPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_PrintPreferenceNameOnPG() {
        GetPreference GetPreferenceObj=new GetPreference(mid);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Response respObj = PGServicesObj.getPreferenceFromPG(GetPreferenceObj,headers);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
preferenceName=respObj.jsonPath().getJsonObject("merchantPref[0].prefName").toString();
        preferenceValue=respObj.jsonPath().getJsonObject("merchantPref[0].prefValue").toString();
System.out.println("Preference Name is : "+ preferenceName);
        System.out.println("Preference Value is : "+ preferenceValue);

    }

    @Test(priority = 0, description = "Set Preference  on PG", groups = {"Regression"}, dependsOnMethods = "TC_003_PrintPreferenceNameOnPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_SetPreferenceOnPGInEditMerchant()
    {
        Request.PG.EditMerchantOnPG EditMerchantOnPGObj=new EditMerchantOnPG(P.TESTDATA.get("SetPreferenceOnPGRequestBody"));
        Map<String, String> body = new HashMap<String, String>();

        Response respObj= PGServicesObj.editMerchantOnPG(EditMerchantOnPGObj,body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

}
