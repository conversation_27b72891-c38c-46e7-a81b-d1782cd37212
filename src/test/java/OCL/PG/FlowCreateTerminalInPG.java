package OCL.PG;

import Request.PG.ActiveTerminalInPG;
import Request.PG.CreateTerminalInPG;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowCreateTerminalInPG extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowCreateTerminalInPG.class);


    public static String MobileNumber = "**********";
    public static String Token = "";
    public static String AgentToken="";
    public static String TID = "";
    public static String SerialNumber = "";
    public static String ModelName = "";
    public static String TerminalStatus = "";
    public static String version="4.6.9";
    public static String mid = "mSwKOf37011070984938";

    @Test(priority = 0, description = "Create Token for PG", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws Exception {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        establishConnectiontoServer(Token,5);
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 0, description = "Create Terminal in PG with invalid OS Type", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_CreateTerminalInPGWithInvalidOSType() {
        CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX11");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");

        Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, description = "Create Terminal in PG with invalid vendor", groups = {"Regression"}, dependsOnMethods = "TC_002_CreateTerminalInPGWithInvalidOSType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_CreateTerminalInPGWithInvalidVendorType() {
        CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX11");
        body.put("modelName", "A50");

        Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }

    @Test(priority = 0, description = "Create Terminal in PG with invalid model type", groups = {"Regression"}, dependsOnMethods = "TC_003_CreateTerminalInPGWithInvalidVendorType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_CreateTerminalInPGWithInvalidModelType() {
        CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("modelName", "A501");

        Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);


    }


    @Test(priority = 0, description = "Create Terminal in PG", groups = {"Regression"}, dependsOnMethods = "TC_004_CreateTerminalInPGWithInvalidModelType")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_CreateTerminalInPG() {
      //  CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));
        PGToken = ApplicantToken("**********", "paytm@123");
        LOGGER.info("Token for PG : " + PGToken);
        Response res=OnboardBanksONPG(PGToken,mid,"HEDC");
        if(res.getStatusCode()==400)
        {
            OnboardBanksONPG(PGToken,mid,"HEDC");
        }
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");

     //   Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        TID = res.jsonPath().getJsonObject("request.tid").toString();
        LOGGER.info("TID is : " + TID);
        TerminalStatus = res.jsonPath().getJsonObject("acquirerStatus.status").toString();
        LOGGER.info("Terminal Status is : " + TerminalStatus);
        SerialNumber = res.jsonPath().getJsonObject("request.serialNo").toString();
        LOGGER.info("Serial Number is : " + SerialNumber);
        ModelName = res.jsonPath().getJsonObject("request.modelName").toString();
        LOGGER.info("Model name is : " + ModelName);
        int StatusCode = res.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, description = "Create Terminal in PG with onboarded terminal ", groups = {"Regression"}, dependsOnMethods = "TC_005_CreateTerminalInPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_CreateTerminalInPGWithMappedSerialNumber() {
        CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", SerialNumber);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");

        Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, description = "Active Terminal in PG with inavlid serial number", groups = {"Regression"}, dependsOnMethods = "TC_006_CreateTerminalInPGWithMappedSerialNumber")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_ActiveTerminalInPGWithInvalidSerialNumber() {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", "***********");
        body.put("tid", TID);
        body.put("modelName", ModelName);
        body.put("bankCode","HEDC");

        Response respObj = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }


    @Test(priority = 0, description = "Active Terminal in PG with invalid tid", groups = {"Regression"}, dependsOnMethods = "TC_007_ActiveTerminalInPGWithInvalidSerialNumber")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_ActiveTerminalInPGWithInvalidTID() {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", SerialNumber);
        body.put("tid", "********");
        body.put("modelName", ModelName);
        body.put("bankCode","HEDC");

        Response respObj = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 0, description = "Active Terminal in PG with invalid model", groups = {"Regression"}, dependsOnMethods = "TC_008_ActiveTerminalInPGWithInvalidTID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_ActiveTerminalInPGWithInvalidModelName() {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", SerialNumber);
        body.put("tid", TID);
        body.put("modelName", "A9100");
        body.put("bankCode","HEDC");

        Response respObj = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, description = "Active Terminal in PG with invalid mid", groups = {"Regression"}, dependsOnMethods = "TC_009_ActiveTerminalInPGWithInvalidModelName")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_ActiveTerminalInPGWithInvalidMID() {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", "mimSwKOf3701107098493811d");
        body.put("serialNo", SerialNumber);
        body.put("tid", TID);
        body.put("modelName", ModelName);
        body.put("bankCode","HEDC");


        Response respObj = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, description = "Active Terminal in PG With invalid token", groups = {"Regression"}, dependsOnMethods = "TC_010_ActiveTerminalInPGWithInvalidMID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_ActiveTerminalInPGWithInvalidToken() {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", "teyyyueuurrrrrr");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", SerialNumber);
        body.put("tid", TID);
        body.put("modelName", ModelName);
        body.put("bankCode","HEDC");

        Response respObj = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, description = "Create Terminal in PG with invalid MID", groups = {"Regression"}, dependsOnMethods = "TC_011_ActiveTerminalInPGWithInvalidToken")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_CreateTerminalInPGWithInvalidMID() {
       // CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        PGToken = ApplicantToken("**********", "paytm@123");
        LOGGER.info("Token for PG : " + PGToken);
        Response res=OnboardBanksONPG(PGToken,mid,"HEDC");
        if(res.getStatusCode()==400)
        {
            OnboardBanksONPG(PGToken,mid,"HEDC");
        }
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", "mSwKOf3701107098493811");
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");

      //  Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        int StatusCode = res.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
    @Test(priority = 0, description = "Create Terminal in PG with invalid Token", groups = {"Regression"}, dependsOnMethods = "TC_012_CreateTerminalInPGWithInvalidMID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_CreateTerminalInPGWithInvalidToken() {
    //    CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));
        PGToken = ApplicantToken("**********", "paytm@123");
        LOGGER.info("Token for PG : " + PGToken);
        Response res=OnboardBanksONPG(PGToken,mid,"HEDC");
        if(res.getStatusCode()==400)
        {
            OnboardBanksONPG(PGToken,mid,"HEDC");
        }
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", "**********");

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "ANDROID");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");

      //  Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
       /* int StatusCode = res.getStatusCode();
        Assert.assertEquals(StatusCode, 400);*/


    }

    @Test(priority = 0, description = "Create Terminal in PG", groups = {"Regression"}, dependsOnMethods = "TC_013_CreateTerminalInPGWithInvalidToken")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_CreateTerminalInPGWithValidData() {
        // CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        PGToken = ApplicantToken("**********", "paytm@123");
        LOGGER.info("Token for PG : " + PGToken);
        Response res=OnboardBanksONPG(PGToken,mid,"HEDC");
        if(res.getStatusCode()==400) {
            OnboardBanksONPG(PGToken, mid, "HEDC");


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("x-sso-token", Token);

            Integer SerialNo = Utilities.randomNumberGenerator(8);
            String MapSerialNo = "JYOT" + SerialNo;

            Map<String, String> body = new HashMap<>();
            body.put("mid", mid);
            body.put("serialNo", MapSerialNo);
            body.put("osType", "ANDROID");
            body.put("vendorName", "PAX");
            body.put("modelName", "A50");

            // Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
            TID = res.jsonPath().getJsonObject("request.tid").toString();
            LOGGER.info("TID is : " + TID);
            TerminalStatus = res.jsonPath().getJsonObject("acquirerStatus.status").toString();
            LOGGER.info("Terminal Status is : " + TerminalStatus);
            SerialNumber = res.jsonPath().getJsonObject("request.serialNo").toString();
            LOGGER.info("Serial Number is : " + SerialNumber);
            ModelName = res.jsonPath().getJsonObject("request.modelName").toString();
            LOGGER.info("Model name is : " + ModelName);
            int StatusCode = res.getStatusCode();
            Assert.assertEquals(StatusCode, 200);

        }
    }
    @Test(priority = 0, description = "Active Terminal in PG", groups = {"Regression"}, dependsOnMethods = "TC_014_CreateTerminalInPGWithValidData")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_ActiveTerminalInPG() {
        ActiveTerminalInPG ActiveTerminalInPGObj = new ActiveTerminalInPG(P.TESTDATA.get("ActiveTerminalOnPGRequestBody"));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", SerialNumber);
        body.put("tid", TID);
        body.put("modelName", ModelName);
        body.put("bankCode","HEDC");

        Response respObj = middlewareServicesObject.ActiveTerminalInPGMethod(ActiveTerminalInPGObj, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, description = "Create Terminal in PG with invalid OS Type", groups = {"Regression"}, dependsOnMethods = "TC_001_MerchantLogin")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_CreateTerminalInPGWithInvalidOSType() {
    //    CreateTerminalInPG CreateTerminalInPGObj = new CreateTerminalInPG(P.TESTDATA.get("CreateTerminalOnPGRequestBody"));

        PGToken = ApplicantToken("**********", "paytm@123");
        LOGGER.info("Token for PG : " + PGToken);
        Response res=OnboardBanksONPG(PGToken,mid,"HEDC");
        if(res.getStatusCode()==400)
        {
            OnboardBanksONPG(PGToken,mid,"HEDC");
        }
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("x-sso-token", Token);

        Integer SerialNo = Utilities.randomNumberGenerator(8);
        String MapSerialNo = "JYOT" + SerialNo;

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("serialNo", MapSerialNo);
        body.put("osType", "LINUX11");
        body.put("vendorName", "PAX");
        body.put("modelName", "A50");

      //  Response respObj = middlewareServicesObject.CreateTerminalInPGMethod(CreateTerminalInPGObj, headers, body);
        int StatusCode = res.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }
}
