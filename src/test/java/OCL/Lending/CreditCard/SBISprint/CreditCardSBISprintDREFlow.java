package OCL.Lending.CreditCard.SBISprint;

import OCL.Lending.CreditCard.SBI.CreditCardSBIDREFlow;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public class CreditCardSBISprintDREFlow extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(CreditCardSBISprintDREFlow.class);
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility = new Utilities();

    String sessionToken = "";
    String leadId = "";
    String custId = "1700506244";
    String consumerNumber = "7775555567";
    String consumerPassword = "paytm@123";
    String token = "";
    String uuid = "";
    String ckycStage = "";
    String stage = "";
    String feStage = "";
    String userIPAddress = "";
    String loanUserLatitude = "";
    String loanUserLongitude = "";
    String tncAdditionalParam = "";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier = "";
    String md5 = "";
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String staticTncAcceptanceTimeStamp = "";
    String bureauRequest = "";
    String bureauResponse = "";
    String bureauCreditState = "";
    String breCreditScore = "";
    String bureauPullTimeStamp = "";
    String offerDetails;
    String offerId;

    String requestBodyJsonPath = "";

    Map<String, String> commonHeaders;
    Response responseObject = null;

    @BeforeClass()
    public void intitializeInputData() {
        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);
    }


    @Test(description = "Verify whether there is any existing CreditCard SBI lead present or not", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchLeadDeatils() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Reset the existing lead of the number", dependsOnMethods = "TC001_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_DeleteExistingLead() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_QA, custId, LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);
        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }

    @Test(description = "Create PostPaid Lead with all deatils", dependsOnMethods = "TC002_DeleteExistingLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_CreateSBILead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();

        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("ipAddress", LendingConstants.SBI_IP_ADDRESS);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);
        body.put("PRODUCT_TYPE", "CREDIT_CARD");
        body.put("LENDER", "SBI");
        body.put("PRODUCT_VERSION", "1");
        body.put("LENDER_ID", "SBI");
        body.put("CUSTOMER_ONBOARDING_MODE", "DRE");


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBICreateLead.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 201;
            return status;
        });


        if (responseObject.getStatusCode() == 201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"), LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);

            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");
            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());

    }

    @Test(description = "Create PostPaid Lead with all deatils", dependsOnMethods = "TC003_CreateSBILead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_FetchLeadDeatils() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "QDE Initiated", dependsOnMethods = "TC004_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_SBIQDEInitiated() throws InterruptedException {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("solutionTypeLevel2", LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);


        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "QDE_INITIATED");
        body.put("COMPANY_NAME", "Paytm");
        body.put("PAN", LendingConstants.CREDIT_CARD_SBI_PAN);
        body.put("F_NAME", "Nishant");
        body.put("L_NAME", "Sharma");
        body.put("MOTHER_NAME", "Rashmi Sharma");
        body.put("DOB", "1994-10-08");
        body.put("VARIANT", "CC_AFFLUENT_WITH_SBI");
        body.put("EMAIL", "<EMAIL>");
        body.put("GENDER", "MALE");
        body.put("NATIONALITY", "Indian");
        body.put("PRIMARY_DESIGNATION", "Engineer");
        body.put("OCCUPATION", "SALARIED");
        body.put("IS_ADDRESS_EDITED", "true");
        body.put("LENDER_STATIC_TNC_SETNAME", "paytm_sbicard_fullaccess_tnc");
        body.put("LENDER", "SBI");

        body.put("line1", "Ramprastha Colony");
        body.put("line2", "Mayur Vihar Phase 1");
        body.put("line3", "Mayur Vihar");
        body.put("city", "Noida");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201301");
        body.put("addressType", "BUSINESS");
        body.put("addressSubType", "CURRENT");
        body.put("latitude", 0);
        body.put("longitude", 0);


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIQDEInitiated.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_INITIATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.QDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "679");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_INITIATED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC005_SBIQDEInitiated", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "Customer Soft Approved", dependsOnMethods = "TC006_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_CustomerSoftApproved() throws InterruptedException {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        boolean status = false;

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_INITIATED.getStage())) {
            QDERequestSuccess();
            status = true;
        } else if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_REQUEST_SUCCESS.getStage())) {
            CustomerSoftApproved();
        }


        if (status = true) {
            CustomerSoftApproved();
        }
    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC007_CustomerSoftApproved", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "Address Confirm Success Callback", dependsOnMethods = "TC008_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC009_AddressConfirmSuccessCallback() throws InterruptedException {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "ADDRESS_CONFIRMED");
        body.put("STP", "N");


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBISprintAddressConfirmCallback.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDRESS_CONFIRMED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDRESS_CONFIRMED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.ADDRESS_CONFIRMED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1676");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDRESS_CONFIRMED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC009_AddressConfirmSuccessCallback", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC010_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Photo Matched", dependsOnMethods = "TC010_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC011_PhotoMatched() throws InterruptedException {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "PHOTO_MATCHED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBISprintPhotoMatch.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PHOTO_MATCHED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.PHOTO_MATCHED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.PHOTO_MATCHED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1677");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.PHOTO_MATCHED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC011_PhotoMatched", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC012_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "PennyDrop Credit Success", dependsOnMethods = "TC012_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC013_PennyDropCreditSuccess() throws InterruptedException {


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "PENNYDROP_CREDIT_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBISprintPhotoMatch.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPOINTMENT_BOOKING_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.PENNYDROP_CREDIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.PENNYDROP_CREDIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1680");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.PENNYDROP_CREDIT_SUCCESS.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC013_PennyDropCreditSuccess", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC014_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "PennyDrop Credit Success", dependsOnMethods = "TC014_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC015_PennyDropDebitSuccess() throws InterruptedException {


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "PENNYDROP_DEBIT_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBISprintPhotoMatch.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PENNYDROP_DEBIT_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.PENNYDROP_DEBIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.PENNYDROP_DEBIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1681");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.PENNYDROP_DEBIT_SUCCESS.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC015_PennyDropDebitSuccess", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC016_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "MITC Completed", dependsOnMethods = "TC016_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC017_MITCCompleted() throws InterruptedException {


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "MITC_COMPLETED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBISprintPhotoMatch.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.MITC_COMPLETED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.MITC_COMPLETED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.MITC_COMPLETED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1682");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.MITC_COMPLETED.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC017_MITCCompleted", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC018_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "MITC Completed", dependsOnMethods = "TC018_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC019_ECardGenerated() throws InterruptedException {


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "ECARD_GENERATED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBISprintPhotoMatch.json";


        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ECARD_GENERATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ECARD_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.ECARD_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1683");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ECARD_GENERATED.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC019_ECardGenerated", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC020_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "MITC Completed", dependsOnMethods = "TC020_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC021_ApplicationApproved() throws InterruptedException {


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "APPLICATION_APPROVED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBISprintPhotoMatch.json";


        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_APPROVED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.APPLICATION_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "675");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_APPROVED.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC021_ApplicationApproved", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC022_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SPRINT_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken,
                    custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }




    public Response QDERequestSuccess() {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "QDE_REQUEST_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIDDESubmitted.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_REQUEST_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_REQUEST_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.QDE_REQUEST_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1668");
        }
        return responseObject;
    }


    public Response CustomerSoftApproved() {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CUSTOMER_SOFT_APPROVED");
        body.put("CARD_ELIGIBILITY_VARIANT", "CC_AFFLUENT_WITH_SBI");
        body.put("APPLICATION_REF_NUM", "2520713000009");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBICustomerSoftApproved.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_SUBMITTED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "668");
        }

        return responseObject;
    }

}