package OCL.Lending.CreditCard.SBI;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public class CreditCardSBIRTOFlow extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(CreditCardSBIRTOFlow.class);
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility = new Utilities();

    String sessionToken = "";
    String leadId = "";
    String custId = "1700506244";
    String consumerNumber = "7775555567";
    String consumerPassword = "paytm@123";
    String token = "";
    String uuid = "";
    String ckycStage = "";
    String stage = "";
    String feStage = "";
    String userIPAddress = "";
    String loanUserLatitude = "";
    String loanUserLongitude = "";
    String tncAdditionalParam = "";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier = "";
    String md5 = "";
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String staticTncAcceptanceTimeStamp = "";
    String bureauRequest = "";
    String bureauResponse = "";
    String bureauCreditState = "";
    String breCreditScore = "";
    String bureauPullTimeStamp = "";
    String offerDetails;
    String offerId;

    String requestBodyJsonPath = "";

    Map<String, String> commonHeaders;
    Response responseObject = null;

    @BeforeClass()
    public void intitializeInputData() {
        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);
    }


    @Test(description = "Verify whether there is any existing CreditCard SBI lead present or not", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchLeadDeatils() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Reset the existing lead of the number", dependsOnMethods = "TC001_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_DeleteExistingLead() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_QA, custId, LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);

        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }

    @Test(description = "Create PostPaid Lead with all deatils", dependsOnMethods = "TC002_DeleteExistingLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_CreateSBILead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();

        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("ipAddress", LendingConstants.SBI_IP_ADDRESS);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);
        body.put("PRODUCT_TYPE", "CREDIT_CARD");
        body.put("LENDER", "SBI");
        body.put("PRODUCT_VERSION", "1");
        body.put("LENDER_ID", "SBI");
        body.put("CUSTOMER_ONBOARDING_MODE", "RTO");


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBICreateLead.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.getStatusCode() == 201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"), LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);

            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");
            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());

    }

    @Test(description = "Fetch Lead Details", dependsOnMethods = "TC003_CreateSBILead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_FetchLeadDeatils() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }



    @Test(description = "Basic Details", dependsOnMethods = "TC004_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_SBIBasicDetails() throws InterruptedException {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);


        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BASIC_DETAILS");
        body.put("PINCODE", 201011);
        body.put("PAN", LendingConstants.CREDIT_CARD_SBI_PAN);
        body.put("F_NAME", "Nishant");
        body.put("L_NAME", "Sharma");
        body.put("DOB", "1994-10-08");
        body.put("EMAIL", "<EMAIL>");
        body.put("GENDER", "MALE");
        body.put("NET_MONTHLY_INCOME", "1000000");
        body.put("OCCUPATION", "SALARIED");
        body.put("STATIC_TNC_SETNAME", "paytm_sbicard_fullaccess_tnc");
        body.put("LENDER_STATIC_TNC_SETNAME", "paytm_sbicard_fullaccess_tnc");
        body.put("line1", "Ramprastha Colony");
        body.put("line2", "Mayur Vihar Phase 1");
        body.put("line3", "Mayur Vihar");
        body.put("city", "Noida");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201301");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType", "CURRENT");
        body.put("latitude", 0);
        body.put("longitude", 0);


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIBasicDetails.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "144");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());

    }

    @Test(description = "Fetch Lead Details", dependsOnMethods = "TC005_SBIBasicDetails", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_FetchLeadDeatils() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }




    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_UpdateBureauDataSetInSAI() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("leadId", leadId);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", false);
        body.put("status", "SUCCESS");
        body.put("F_NAME", "BHAIRAVI");
        body.put("L_NAME", "LATASREE");
        body.put("GENDER", "FEMALE");
        body.put("PINCODE", "600024");
        body.put("PAN", "**********");
        body.put("DOB", "1979-10-05");
        body.put("EMAIL", "<EMAIL>");

        responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body, true);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());

    }


    @Test(description = "Fetch CIR", dependsOnMethods = "TC007_UpdateBureauDataSetInSAI", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_FetchCIR() throws InterruptedException {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("leadId",leadId);


        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v2FetchCIR(queryParams, finalHeaders, body);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BUREAU_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BUREAU_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "194");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BUREAU_SUCCESS.getStage());

    }


    @Test(description = "Fetch Lead Details", dependsOnMethods = "TC008_FetchCIR", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC009_FetchLeadDeatils() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
        }
    }

    @Test(description = "Offer Accept", dependsOnMethods = "TC009_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC010_OfferAccepted() throws InterruptedException {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId",leadId);
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("solutionTypeLevel2", LendingConstants.SBI_SOLUTION_TYPE_LEVEL2);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);


        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("latitude", "27.2046");
        headers.put("longitude", "77.49.77");
        headers.put("ipAddress", "************");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "OFFER_ACCEPTED");
        body.put("VARIANT", "CC_AFFLUENT_WITH_SBI");
        body.put("LENDER", "SBI");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIOfferAccept.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "367");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());

    }

    @Test(description = "Fetch Lead Details", dependsOnMethods = "TC010_OfferAccepted", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC011_FetchLeadDeatils() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "QDE Initiated", dependsOnMethods = "TC011_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC012_SBIQDEInitiated() throws InterruptedException {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("solutionTypeLevel2", LendingConstants.SBI_SOLUTION_TYPE_LEVEL2);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);


        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "QDE_INITIATED");
        body.put("PAN", LendingConstants.CREDIT_CARD_SBI_PAN);

        body.put("F_NAME", "Nishant");
        body.put("L_NAME", "Sharma");
        body.put("DOB", "1994-10-08");
        body.put("COMPANY_NAME", "Paytm");
        body.put("PINCODE", 201011);
        body.put("MOTHER_NAME", "Rashmi Sharma");
        body.put("VARIANT", "CC_AFFLUENT_WITH_SBI");
        body.put("EMAIL", "<EMAIL>");
        body.put("GENDER", "MALE");
        body.put("NATIONALITY", "Indian");
        body.put("PRIMARY_DESIGNATION", "Engineer");
        body.put("OCCUPATION", "SALARIED");
        body.put("IS_ADDRESS_EDITED", "true");
        body.put("LENDER_STATIC_TNC_SETNAME", "paytm_sbicard_fullaccess_tnc");
        body.put("LENDER", "SBI");

        body.put("line1", "Ramprastha Colony");
        body.put("line2", "Mayur Vihar Phase 1");
        body.put("line3", "Mayur Vihar");
        body.put("city", "Noida");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201301");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType", "CURRENT");
        body.put("latitude", 0);
        body.put("longitude", 0);


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIQDEInitiated.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_INITIATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.QDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "679");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_INITIATED.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC012_SBIQDEInitiated", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC013_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "Customer Soft Approved", dependsOnMethods = "TC013_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC014_CustomerSoftApproved() throws InterruptedException {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        boolean status = false;

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_INITIATED.getStage())) {
            QDERequestSuccess();
            status = true;
        } else if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_REQUEST_SUCCESS.getStage())) {
            CustomerSoftApproved();
        }


        if (status = true) {
            CustomerSoftApproved();
        }
    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC014_CustomerSoftApproved", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC015_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "DDE Initiated", dependsOnMethods = "TC015_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC016_DDEInitiated() throws InterruptedException {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "DDE_INITIATED");
        body.put("mobile", consumerNumber);
        body.put("CARD_COLOR", "BLACK");
        body.put("NAME_ON_CARD", "NISHANT SHARMA");
        body.put("PERMANENT_AND_DOCUMENT_ADDRESS_SAME", "TRUE");

        body.put("line1", "Ramprastha Colony");
        body.put("line2", "Mayur Vihar Phase 1");
        body.put("line3", "Mayur Vihar");
        body.put("city", "Noida");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201301");
        body.put("addressType", "BUSINESS");
        body.put("addressSubType", "REGISTERED");
        body.put("latitude", 0);
        body.put("longitude", 0);

        body.put("line1", "Ramprastha Colony");
        body.put("line2", "Mayur Vihar Phase 1");
        body.put("line3", "Mayur Vihar");
        body.put("city", "Noida");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201301");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType", "PERMANENT");
        body.put("latitude", 0);
        body.put("longitude", 0);

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIDDEInitiated.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DDE_INITIATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.DDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "670");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_INITIATED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC016_DDEInitiated", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC017_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "DDE Submitted", dependsOnMethods = "TC017_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC018_DDESubmitted() throws InterruptedException {


        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        boolean status = false;

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DDE_INITIATED.getStage())) {
            DDERequestSuccess();
            status = true;
        } else if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DDE_REQUEST_SUCCESS.getStage())) {
            DDESubmitSuccess();
        }


        if (status = true) {
            DDESubmitSuccess();
        }
    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC018_DDESubmitted", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC019_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Appointment Booking Success", dependsOnMethods = "TC019_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC020_AppointmentBookingSuccess() throws InterruptedException {


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "APPOINTMENT_BOOKING_SUCCESS");
        body.put("ADDRESS_FOR_VISIT", "RESIDENTIAL");
        body.put("APPOINTMENT_DATE", "2022-07-04");
        body.put("APPOINTMENT_TIME", "16:00-18:00");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIAppointmentBookingSuccess.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPOINTMENT_BOOKING_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPOINTMENT_BOOKING_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.APPOINTMENT_BOOKING_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "871");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPOINTMENT_BOOKING_SUCCESS.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC020_AppointmentBookingSuccess", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC021_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "APPLICATION_APPROVED", dependsOnMethods = "TC021_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC022_ApplicationApproved() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "APPLICATION_APPROVED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIDDESubmitted.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_APPROVED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.APPLICATION_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "675");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_APPROVED.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC022_ApplicationApproved", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC023_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.SBI_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,
                    sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    //***************HELPER METHODS STARTS******************//

    public Response QDERequestSuccess() {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "QDE_REQUEST_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIDDESubmitted.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_REQUEST_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_REQUEST_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.QDE_REQUEST_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1668");
        }
        return responseObject;
    }


    public Response CustomerSoftApproved() {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CUSTOMER_SOFT_APPROVED");
        body.put("CARD_ELIGIBILITY_VARIANT", "CC_AFFLUENT_WITH_SBI");
        body.put("APPLICATION_REF_NUM", "2520713000009");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBICustomerSoftApproved.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_SUBMITTED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "668");
        }
        return responseObject;
    }


    public Response DDERequestSuccess() {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "DDE_REQUEST_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIDDESubmitted.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DDE_REQUEST_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_REQUEST_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.DDE_REQUEST_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "1671");
        }
        return responseObject;
    }


    public Response DDESubmitSuccess() {
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "DDE_SUBMIT_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/SBI/SBIDDESubmitted.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DDE_SUBMIT_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_SUBMIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.DDE_SUBMIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "671");

        }
        return responseObject;
    }

    //***************HELPER METHODS ENDS******************//

}
