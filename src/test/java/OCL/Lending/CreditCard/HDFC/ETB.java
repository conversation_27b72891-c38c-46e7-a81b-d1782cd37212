package OCL.Lending.CreditCard.HDFC;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public class ETB extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(ETB.class);
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility = new Utilities();

    String sessionToken = "";
    String leadId = "";
    String custId = "1701431606";
    String consumerNumber = "7775555582";
    String consumerPassword = "paytm@123";
    String token = "";
    String userIPAddress = "";
    String staticTncAcceptanceTimeStamp = "";
    String requestBodyJsonPath = "";
    Map<String, String> commonHeaders;
    Response responseObject = null;

    @BeforeClass()
    public void intitializeInputData() throws SQLException {
        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);
    }


    @Test(description = "Verify whether there is any existing CreditCard HDFC lead present or not", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchLeadDeatils() {


        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Reset the existing lead of the number", dependsOnMethods = "TC001_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_DeleteExistingLead() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_QA, custId, LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);

        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }

    @Test(description = "Create PostPaid Lead with all deatils", dependsOnMethods = "TC002_DeleteExistingLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_CreateHDFCLead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.CREDIT_CARD_TYPE_LEVEL2_HDFC);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();

        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("ipAddress", LendingConstants.HDFC_IP_ADDRESS);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);
        body.put("PRODUCT_TYPE", "mass");
        body.put("LENDER", "HDFC");
        body.put("PRODUCT_VERSION", "1");
        body.put("LENDER_ID", "1");
        body.put("MERCHANT_ID", "vyhcsz60151047759105");
        body.put("OFFER_ID", "HDFC_TEST4_CREDIT_CARD_100000154_1001714402_1642487593909_58b4512a");
        body.put("SEGMENT", "BQC");
        body.put("VARIANT", "hdfc_consumer_premium_affluent");
        body.put("BANKING_SEGMENT", "ETB");
        body.put("CPV_FLAG", "1");
        body.put("APPROVAL_TIMESTAMP", "2021-07-16 13:13:13.557");
        body.put("IS_VKYC_ENABLED", "false");
        body.put("USER_IP_ADDRESS", "************");
        body.put("LEAD_CREATION_DEVICE_MANUFACTURER", "Realme");


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCCreateLead.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 201;
            return status;
        });


        if (responseObject.getStatusCode() == 201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"), LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);

            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");
            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC003_CreateHDFCLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_FetchLeadAllData() {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Update lead basic details", dependsOnMethods = "TC004_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateLeadBasicDetails() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BASIC_DETAILS_SUBMITTED");
        body.put("PAN", LendingConstants.CREDIT_CARD_HDFC_PAN);
        body.put("F_NAME", "Nishant");
        body.put("M_NAME", "Kumar");
        body.put("L_NAME", "Sharma");
        body.put("LENDER_STATIC_TNC_SETNAME", "cc_hdfc_consent");
        body.put("LENDER_COMMUNICATION_STATIC_TNC_SETNAME", "hdfc_dnc_ndnc_consent");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCBasicDetails.json";


        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS_SUBMITTED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS_SUBMITTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BASIC_DETAILS_SUBMITTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "664");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"), LendingConstants.CREDIT_CARD_HDFC_PAN);
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS_SUBMITTED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC005_UpdateLeadBasicDetails", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "QDE Initiated", dependsOnMethods = "TC006_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_HDFCQDEInitiated() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "QDE_INITIATED");
        body.put("USER_TYPE", LendingConstants.CC_HDFC_ETB);

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCQDEInitiated.json";


        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_INITIATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.QDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "679");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_INITIATED.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC007_HDFCQDEInitiated", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "QDE Submitted", dependsOnMethods = "TC008_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC009_HDFCQDESubmitted() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "QDE_SUBMITTED");
        body.put("FULL_NAME", "NISHANT SHARMA");
        body.put("DOB", "1982-12-24");
        body.put("EMAIL", "<EMAIL>");
        body.put("GENDER", "Male");
        body.put("ANNUAL_INCOME", "10000");
        body.put("NSDL_NAME", "Nishant Sharma");
        body.put("OCCUPATION", "Salaried");
        body.put("COMPANY_NAME", "Paytm");
        body.put("PRIMARY_DESIGNATION", "Software Engineer");
        body.put("IS_SENIOR_OFFICIAL_RELATED", "TRUE");
        body.put("SENIOR_OFFICIAL_NAME", "ETB Test");
        body.put("SENIOR_OFFICIAL_RELATION_TYPE", "SIBLING");
        body.put("LENDER_STATIC_TNC_SETNAME", "cc_hdfc_consent");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCQDESubmitted.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.QDE_SUBMITTED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_SUBMITTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.QDE_SUBMITTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "666");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.QDE_SUBMITTED.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC009_HDFCQDESubmitted", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC010_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Customer Soft Approved", dependsOnMethods = "TC010_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC011_CustomerSoftApproved() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CUSTOMER_SOFT_APPROVED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCCustomerSoftApproved.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "668");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.CUSTOMER_SOFT_APPROVED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC011_CustomerSoftApproved", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC012_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "DDE Initiated", dependsOnMethods = "TC012_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC013_DDEInitiated() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "DDE_INITIATED");
        body.put("NAME_ON_CARD", "Nishant Sharma");
        body.put("CREDIT_CARD_PLASTIC_CODE", "0001");
        body.put("BUSINESS_EMAIL", "<EMAIL>");
        body.put("CUSTOMER_VERIFICATION_MODE", "NetBanking");
        body.put("MOTHER_NAME", "Rashmi Sharma");
        body.put("CURRENT_ADDRESS_CONSENT", "YES");

        body.put("line1", "Ramprastha Colony");
        body.put("line2", "Mayur Vihar Phase 1");
        body.put("line3", "Mayur Vihar");
        body.put("city", "Noida");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201301");
        body.put("addressType", "BUSINESS");
        body.put("addressSubType", "REGISTERED");
        body.put("latitude", 0);
        body.put("longitude", 0);

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCDDEInitiated.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DDE_INITIATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.DDE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "670");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_INITIATED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC013_DDEInitiated", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC014_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "DDE Submitted", dependsOnMethods = "TC014_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC015_DDESubmitted() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "DDE_SUBMIT_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCCustomerSoftApproved.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);

            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DDE_SUBMIT_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_SUBMIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.DDE_SUBMIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "671");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.DDE_SUBMIT_SUCCESS.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC015_DDESubmitted", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC016_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Net Banking Verification Success", dependsOnMethods = "TC016_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC017_NetBankingVerificationSuccess() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "NET_BANKING_VERIFICATION_SUCCESS");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCCustomerSoftApproved.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.NET_BANKING_VERIFICATION_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.NET_BANKING_VERIFICATION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.NET_BANKING_VERIFICATION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "674");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.NET_BANKING_VERIFICATION_SUCCESS.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC017_NetBankingVerificationSuccess", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC018_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "APPLICATION_APPROVED", dependsOnMethods = "TC018_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC019_ApplicationApproved() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "APPLICATION_APPROVED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCCustomerSoftApproved.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_APPROVED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.APPLICATION_APPROVED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "675");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_APPROVED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC019_ApplicationApproved", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC020_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description = "Application Closed", dependsOnMethods = "TC020_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC021_ApplicationClosed() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.CREDIT_CARD);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "APPLICATION_CLOSED");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/HDFC/HDFCCustomerSoftApproved.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_CLOSED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_CLOSED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.APPLICATION_CLOSED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "676");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.APPLICATION_CLOSED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC021_ApplicationClosed", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC022_FetchLeadAllData() throws InterruptedException {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(2, TimeUnit.SECONDS).until(() -> {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.CREDIT_CARD, LendingConstants.HDFC_SOLUTION_TYPE_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }
}

