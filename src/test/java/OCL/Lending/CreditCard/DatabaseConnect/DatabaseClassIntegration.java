package OCL.Lending.CreditCard.DatabaseConnect;

import Services.LendingService.LendingBaseAPI;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import org.testng.annotations.Test;

public class DatabaseClassIntegration extends BaseMethod {


    @Test(description = "Verify whether there is any existing CreditCard HDFC lead present or not", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchLeadDetails() throws Exception {

       // LendingBaseAPI.establishConnection();
     //   LendingBaseAPI.UpdateQueryToTriggerBatch("46a88a64-d531-4cc0-b2c8-62f61f42789f");
       // LendingBaseAPI.insertNodeQuery("46a88a64-d531-4cc0-b2c8-62f61f42789f", "367");
       System.out.println(LendingBaseAPI.SelectQueryUBMId("52378900-bf59-40ee-8506-0555ea043941"));
      //  LendingBaseAPI.backstageMovement("46a88a64-d531-4cc0-b2c8-62f61f42789f", "225");
    }
}

