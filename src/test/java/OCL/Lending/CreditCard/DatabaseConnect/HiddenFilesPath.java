//package OCL.Lending.CreditCard.DatabaseConnect;// Java program to find and open
//// all the hidden files in a
//// directory
//import java.awt.Desktop;
//import java.io.File;
//import java.io.IOException;
//import java.util.List;
//import java.util.Scanner;
//
//import java.io.File;
//
//public class HiddenFilesPath {
//	public static void main(String[] args) {
//		File directory = new File(System.getProperty("user.home"));
//
//
//		String sshDirectoryPath = System.getProperty("user.home") + "/.ssh";
//		System.out.println("SSH Directory Path: " + sshDirectoryPath);
//
//
//
//		File[] files = directory.listFiles();
//
//		if (files != null) {
//			for (File file : files) {
//				// Check if the file is hidden
//				if (file.isHidden()) {
//					// Print the absolute path of the hidden file
//					System.out.println(file.getAbsolutePath());
//				}
//			}
//		}
//	}
//}