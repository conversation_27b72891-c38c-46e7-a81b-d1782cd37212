package OCL.Lending.CreditCard.DatabaseConnect;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class DatabaseAccess {
    private static final String SSH_USER = "nishant_30099";
    private static final String SSH_PRIVATE_KEY_PATH = System.getProperty("user.home") + "/.ssh/id_rsa";
    private static final String SSH_HOST = "************";
    private static final int SSH_PORT = 22;


    private static final String DB_USER = "michael";
    private static final String DB_PASSWORD = "Paytm@123";
    private static final String DB_HOST = "************";
    private static final int DB_PORT = 3310;
    private static final String DB_NAME = "";

    public static Connection establishConnection() throws Exception {

        JSch jsch = new JSch();
        jsch.addIdentity(SSH_PRIVATE_KEY_PATH);
        Session session = jsch.getSession(SSH_USER, SSH_HOST, SSH_PORT);
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        int assigned_port = session.setPortForwardingL(DB_PORT, DB_HOST, DB_PORT);

        Class.forName("com.mysql.cj.jdbc.Driver");
        return DriverManager.getConnection("**********************:" + assigned_port + "/" + DB_NAME, DB_USER, DB_PASSWORD);
    }


    public static void main(String[] args) {
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {

            connection = establishConnection();
            statement = connection.createStatement();
            statement.execute("use oe_lending_staging3_221103");
            resultSet = statement.executeQuery("SELECT * FROM jwt_client_details;");


            while (resultSet.next()) {
                System.out.println("Client ID : " + resultSet.getString("client_id"));
                System.out.println("Secret Key : " + resultSet.getString("secret"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (resultSet != null) {
                    resultSet.close();
                }

                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}