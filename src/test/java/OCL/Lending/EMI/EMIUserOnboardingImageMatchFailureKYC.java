package OCL.Lending.EMI;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;

public class EMIUserOnboardingImageMatchFailureKYC extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(EMIUserOnboardingImageMatchFailureKYC.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1002531921";
		 String consumerNumber="8755844882";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
	;
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
	
		
		 

		 Response responseObject= null;
	
		private String offerDetails;
		private String loanOfferId;
		private String kycAddressLenderStaticTNCSetname;
		private String kycAddressLenderStaticTNCAcceptanceTimeStamp;
		private String kycAddressLenderStaticTNCAcceptanceIP;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing ABFL EMI lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_EMI_FetchLeadDeatils()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_EMI_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();

			  queryParams.put("solution", LendingConstants.EMI);
				queryParams.put("leadId",leadId);
				queryParams.put("custId", custId);

			  queryParams.put("leadId",leadId);
			  

			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create EMI Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreateEMILead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.EMI);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
	    	
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  	   body.put("PRODUCT_TYPE", "EMI");
		  	   body.put("PRODUCT_VERSION", "1");
		  	   body.put("PRODUCT_ID","9000");
			   body.put("BASE_ID", "ABFL_EMI_VIVEK_f15daccb");
			   body.put("LOAN_OFFER_ID", "5d74bcb6-bf25-419f-92e5-d5e20ae2eef3");
			   body.put("STATIC_TNC_SETNAME", LendingConstants.EMI_STATIC_TNC_SETNAME);
			   body.put("LENDER_STATIC_TNC_SETNAME", LendingConstants.EMI_LENDER_STATIC_TNC_SETNAME);
			   body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.EMI_LENDING_DYNAMIC_SECONDARY_TNC);
			   body.put("LENDER_ID", "6");
			   body.put("FLOW_TYPE", "RISK");
			   body.put("BUREAU_PRIORITY", "CIBIL,EXPERIAN");
			   body.put("IS_SELFIE_BEFORE_KYC", "true");
			   body.put("KYC_VERSION", "V3");
			   body.put("KYC_MODES_INITIATE_SEQUENCE", "SEARCH_BY_PAN,OFFLINE_AADHAAR,DIGILOCKER");
		  	 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/EMI/CreateLeadRequest.json";
		  	  
		  
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);

			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the data on the created lead using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateEMILead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<10;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		     }
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"286");
		        
		        Map<String,String> queryParams=new HashMap<String,String>();
		    	
		  	    queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		  	    queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		  	 
		  	    Map<String,String> headers=new HashMap<String,String>();
		  	    token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		  	    headers = LendingBaseAPI.setHeadersReceivedFromFE();
		  	    headers.put("Authorization", token);
		  	    headers.put("Content-Type", "application/json");
		  	    headers.put("custId", custId);
	         
		  	    Map<String,Object> body=new HashMap<String,Object>();
		  	    body.put("workflowOperation","BASIC_DETAILS");
		  	    body.put("DOB", LendingConstants.DOB_STASHFIN);
		  	    body.put("PAN", Pan);
	    	   	body.put("EMAIL", Utilities.randomEmailGeneration());
	    	   
		        requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/MCABasicDetailRequest.json";
		     

		  		 for(int i=0;i<2;i++)
		  		 {
		  			 
		  			 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
		  			 
		  			  if(responseObject.getStatusCode()==200)
		  			   break;
		  		 }
		  			  

		  		  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		  			  {
		  				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		  		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		  		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		  		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		  		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		  			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
		  		       
		  		        
		  		        leadId=responseObject.jsonPath().getString("leadId");
		  		        custId=responseObject.jsonPath().getString("custId");
		  		        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
		  		        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
		  		        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
		  		        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
		  		        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
		  		        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
		  		    
		  		      }
		  		  
		  	        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		  		
		  	  
		      }
		     }
		     
		     
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC004_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC005_UpdateBureauDataSetInSAI() {
			  Map<String, String> queryParams = new HashMap<String, String>();
				 queryParams.put("solution",LendingConstants.EMI);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		    
				
				
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowMovementRequired", "false");
				body.put("F_NAME", "BHAIRAVI");
				body.put("L_NAME", "LATASREE");
				body.put("GENDER", "FEMALE");
				body.put("PINCODE", "600024");
				body.put("PAN", Pan);
				body.put("DOB", "1979-10-05");
				body.put("EMAIL", "<EMAIL>");
				
			    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			//Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());
			
			}
		  
		  
		  
		  
		     @Test(description = "Fetch CIR",dependsOnMethods = "TC005_UpdateBureauDataSetInSAI",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC006_FetchCIR()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		    	 
				   Map<String,String> headers=new HashMap<String,String>();
			       headers.put("session_token", sessionToken);
			       headers.put("Content-Type", "application/json");
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			   
			
			 	  
			 	 for(int i=0;i<10;i++)
				  {
					  responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);
				
				 
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))
					  
					  break;
				  
				  }
			 	 
			 	
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			       
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
			        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


			       
			        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
			        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
			      
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC006_FetchCIR",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC007_FetchLeadAllData() throws JSONException
			    {
				  for(int i=0;i<33;i++)
				  {
				   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
					  break;
				  
				  }
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
				    
				  {
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
				        
				        //Hit BRE1 Callback
				        
				        LOGGER.info("Callback not coming so hitting BRE1 Callback");
				        
				        responseObject=    lendingBaseClassObject. BRE1CallbackforMCA (leadId,LendingConstants.EMI,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,custId);
				        
				        
		            }
				  
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			       
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        
	            }
			   
			    
			       
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");
				 offerDetails=responseObject.jsonPath().getString("solutionAdditionalInfo.BRE1_OFFER_DETAILS");
			     JSONObject jsonObject = new JSONObject(offerDetails);
			     loanOfferId=jsonObject.get("offerId").toString();
			     System.out.println(loanOfferId);
			
				     
			    }
			  
	  
			  @Test(description = "EMI Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC007_FetchLeadAllData")
				@Owner(emailId = "<EMAIL>")
				public void TC008_LoanOfferAccept() {
					 Map<String,String> queryParams=new HashMap<String,String>();
						
					 queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					 queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
					 
					 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
						
					 Map<String, String> headers = new HashMap<String, String>();
					 headers.put("Authorization", token);
					 headers.put("Content-Type", "application/json");
					 headers.put("custId", custId);
				       
				     Map<String,Object> body=new HashMap<String,Object>();
				     body.put("workflowOperation", "OFFER_ACCEPTED");
				     
				     //solutionAdditionalInfo
				     body.put("LOAN_OFFER_ID", loanOfferId);
				     body.put("BASE_ID", "OE_EMI_April_shivangi1_e46f71a1");
				     body.put("EMI_LIMIT_IN_NUMBER", "18000");
				     body.put("TOTAL_JOINING_FEES", "0");
				     body.put("TAX_ON_JOINING_FEES", "0");
			
				     
				     requestBodyJsonPath="MerchantService/V1/workflow/lead/EMI/OfferAcceptedRequest.json";
					   
				     responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					    	
				     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
				     {
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
				        
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),loanOfferId);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"OE_EMI_April_shivangi1_e46f71a1");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMI_LIMIT_IN_NUMBER"),"18000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TOTAL_JOINING_FEES"),"0");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TAX_ON_JOINING_FEES"),"0");
					  }
				   }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_LoanOfferAccept",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC009_FetchLeadAllData() throws JSONException, SQLException
			    {
				  for(int i=0;i<35;i++)
				  {
				   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
					  break;
				  
				  }
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				  
				 
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
			    }
			  
			  @Test(description = "Verify the EMI lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC009_FetchLeadAllData")
				@Owner(emailId = "<EMAIL>")
				public void TC010_uploadCustomerPhoto() throws InterruptedException {
					
					Response responseObject = lendingBaseClassObject.KYCImageUpload("customerPhoto", leadId, custId,
							LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.EMI, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"others","KYCImageMatchFailure.jpeg");
				
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
					Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
				
					
				}
				
				@Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC010_uploadCustomerPhoto")
				@Owner(emailId = "<EMAIL>")
				public void TC011_VerifyUploadedCustomerPhoto() {

					responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					//Customerphoto
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");
							
				}
				
				@Test(description = "Verify the EMI lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC011_VerifyUploadedCustomerPhoto")
				@Owner(emailId = "<EMAIL>")
				public void TC012_UploadSelfie() throws InterruptedException {
					
					Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
							LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.EMI, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"selfie","KYCImageMatchFailure.jpeg");
				
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
					Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
				
					
				}
				
				@Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC012_UploadSelfie", groups = {
				"Regression" })
				@Owner(emailId = "<EMAIL>")
				public void TC013_VerifyUploadedSelfie() {

					responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
						Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
						Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");
						
					}else {
						Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
						Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");

						
					}
					
				}
				
				  @Test(description = "Update data in SAI Table", dependsOnMethods = "TC013_VerifyUploadedSelfie", groups = { "Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC014_LeadDataUpdateForKYC_InSAI() {
					Map<String, String> queryParams = new HashMap<String, String>();
					 queryParams.put("solution",LendingConstants.EMI);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
			     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			    
			     	 String randomPan=utility.randomIndividualPANValueGenerator();
					
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
					
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("workflowMovementRequired", "false");
					body.put("PAN","**********");
					body.put("DOB", "1995-09-08");
					body.put("GENDER", "FEMALE");
					body.put("NSDL_NAME", "Himika Juneja");
					
                   requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";
					
				    responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
					Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
					
					
					}
				
				@Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC014_LeadDataUpdateForKYC_InSAI")
				@Owner(emailId = "<EMAIL>")
				public void TC015_InitiateKYC_UsingSearchByPan() {
					Map<String,String> queryParams=new HashMap<String,String>();
					queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			  	 
					Map<String,String> headers=new HashMap<String,String>();
					token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
				    headers = LendingBaseAPI.setHeadersReceivedFromFE();
				    headers.put("Authorization", token);
				    headers.put("Content-Type", "application/json");
				    headers.put("custId", custId);
				       
				    Map<String,Object> body=new HashMap<String,Object>();
				    body.put("kycMode","SEARCH_BY_PAN");
				  
				    
				    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";
				    
				    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
					
				}
				
				@Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC015_InitiateKYC_UsingSearchByPan")
				@Owner(emailId = "<EMAIL>")
					public void TC016_FetchDataPostKYCIntiated() throws JSONException
					{
					  
					 for(int i =0;i<=35;i++) {
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage())) {
						    	 break;
						     }
					 }
				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
				     {
				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage());
				  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2022");
				  	   	
				  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				  	   	
			  	   	}
				    
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage());
				  	  
				
					}
				
				//selfie upload
				@Test(description = "Verify the EMI lead Re Upload SELFIE First attempt ",groups = {"Regression"},dependsOnMethods = "TC016_FetchDataPostKYCIntiated")
				@Owner(emailId = "<EMAIL>")
				public void TC017_ReUploadSelfie_FirstAttemptAfterFailure() throws InterruptedException {
					
					Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
							LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.EMI, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"selfie","KYCImageMatchFailure.jpeg");
				
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
					Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
				
					
				}
				//initaite kyc
				//kyc callback
				@Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC017_ReUploadSelfie_FirstAttemptAfterFailure")
				@Owner(emailId = "<EMAIL>")
				public void TC018_InitiateKYC_UsingSearchByPan() {
					Map<String,String> queryParams=new HashMap<String,String>();
					queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			  	 
					Map<String,String> headers=new HashMap<String,String>();
					token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
				    headers = LendingBaseAPI.setHeadersReceivedFromFE();
				    headers.put("Authorization", token);
				    headers.put("Content-Type", "application/json");
				    headers.put("custId", custId);
				       
				    Map<String,Object> body=new HashMap<String,Object>();
				    body.put("kycMode","SEARCH_BY_PAN");
				  
				    
				    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";
				    
				    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
					
				}
				
				@Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC018_InitiateKYC_UsingSearchByPan")
				@Owner(emailId = "<EMAIL>")
					public void TC019_FetchDataPostKYCIntiated() throws JSONException
					{
					  
					 for(int i =0;i<=35;i++) {
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage())) {
						    	 break;
						     }
					 }
				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
				     {
				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage());
				  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2022");
				  	   	
				  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				  	   	
			  	   	}
				    
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage());
				  	  
				
					}
				@Test(description = "Verify the EMI lead Re Upload SELFIE second attempt ",groups = {"Regression"},dependsOnMethods = "TC019_FetchDataPostKYCIntiated")
				@Owner(emailId = "<EMAIL>")
				public void TC020_ReUploadSelfie_SecondAttemptAfterFailure() throws InterruptedException {
					
					Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
							LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.EMI, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"selfie","KYCImageMatchFailure.jpeg");
				
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
					Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
				
					
				}
				//initaite kyc
				//kyc callback
				@Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC020_ReUploadSelfie_SecondAttemptAfterFailure")
				@Owner(emailId = "<EMAIL>")
				public void TC021_InitiateKYC_UsingSearchByPan() {
					Map<String,String> queryParams=new HashMap<String,String>();
					queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			  	 
					Map<String,String> headers=new HashMap<String,String>();
					token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
				    headers = LendingBaseAPI.setHeadersReceivedFromFE();
				    headers.put("Authorization", token);
				    headers.put("Content-Type", "application/json");
				    headers.put("custId", custId);
				       
				    Map<String,Object> body=new HashMap<String,Object>();
				    body.put("kycMode","SEARCH_BY_PAN");
				  
				    
				    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";
				    
				    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
					
				}
				
				@Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC021_InitiateKYC_UsingSearchByPan")
				@Owner(emailId = "<EMAIL>")
					public void TC022_FetchDataPostKYCIntiated() throws JSONException
					{
					  
					 for(int i =0;i<=35;i++) {
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage())) {
						    	 break;
						     }
					 }
				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
				     {
				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage());
				  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2022");
				  	   	
				  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				  	   	
			  	   	}
				    
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REUPLOAD_REQUIRED.getStage());
				  	  
				
					}@Test(description = "Verify the EMI lead Re Upload SELFIE third attempt ",groups = {"Regression"},dependsOnMethods = "TC022_FetchDataPostKYCIntiated")
					@Owner(emailId = "<EMAIL>")
					public void TC023_ReUploadSelfie_ThirdAttemptAfterFailure() throws InterruptedException {
						
						Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
								LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.EMI, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"selfie","KYCImageMatchFailure.jpeg");
					
						lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					
						LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
						Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
						Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
						Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
					    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
					    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
					
						
					}
					//initaite kyc
					//kyc callback
					@Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC023_ReUploadSelfie_ThirdAttemptAfterFailure")
					@Owner(emailId = "<EMAIL>")
					public void TC024_InitiateKYC_UsingSearchByPan() {
						Map<String,String> queryParams=new HashMap<String,String>();
						queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
				  	 
						Map<String,String> headers=new HashMap<String,String>();
						token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
					    headers = LendingBaseAPI.setHeadersReceivedFromFE();
					    headers.put("Authorization", token);
					    headers.put("Content-Type", "application/json");
					    headers.put("custId", custId);
					       
					    Map<String,Object> body=new HashMap<String,Object>();
					    body.put("kycMode","SEARCH_BY_PAN");
					  
					    
					    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";
					    
					    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
						
						lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
						Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
						Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
						Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
						Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
						
					}
					
					@Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC024_InitiateKYC_UsingSearchByPan")
					@Owner(emailId = "<EMAIL>")
						public void TC025_FetchDataPostKYCIntiated() throws JSONException
						{
						  
						 for(int i =0;i<=35;i++) {
								responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					
							     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
							    	 break;
							     }
						 }
					    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
					     {
					    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
					  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");
					  	   	
					  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					  	   	
				  	   	}
					    
				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
					  	  
					
						}
				
				
				  @Test(description = "Update data in SAI Table", dependsOnMethods = "TC025_FetchDataPostKYCIntiated", groups = { "Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC026_LeadDataUpdateForKYC_InSAI() {
					
				    Map<String, String> queryParams = new HashMap<String, String>();
					 queryParams.put("solution",LendingConstants.EMI);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
			     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			    
			     	 String randomPan=utility.randomIndividualPANValueGenerator();
					
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
					
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("workflowMovementRequired", "false");
					body.put("PAN",randomPan);
					body.put("DOB", "1973-01-01");
					body.put("GENDER", "MALE");
					body.put("NSDL_NAME", "Mohammad Kalim Ansari");
					
                   requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";
					
				    responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
					Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.KYC_FAILED.getStage());
					
					
					}
				 
				
				
		      	
		  
		    

}