package OCL.Lending.EMI;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import Services.LendingService.enums.Solution;
import Services.LendingService.enums.SolutionTypeLevel2;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;


import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;

/**EMI lead created with KYC SBP , OA failed and then retried with OA
 * Bank validation pending and then banking action done
 * E-mandate done on lead
 * 
 * <AUTHOR>
 *
 */
public class TestEMIUserOnboardingFlowUsingOA  extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(TestEMIUserOnboardingFlowUsingOA.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="**********";
		 String consumerNumber="**********";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
	;
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
	
		
		 

		 Response responseObject= null;
	
		private String offerDetails;
		private String loanOfferId;
		private String kycAddressLenderStaticTNCSetname;
		private String kycAddressLenderStaticTNCAcceptanceTimeStamp;
		private String kycAddressLenderStaticTNCAcceptanceIP;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing ABFL EMI lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_EMI_FetchLeadDeatils()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_EMI_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();

			  queryParams.put("solution", LendingConstants.EMI);
				queryParams.put("leadId",leadId);
				queryParams.put("custId", custId);

			  queryParams.put("leadId",leadId);
			  

			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create EMI Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreateEMILead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.EMI);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
	    	
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  	   body.put("PRODUCT_TYPE", "EMI");
		  	   body.put("PRODUCT_VERSION", "1");
		  	   body.put("PRODUCT_ID","900");
			   body.put("BASE_ID", "ABFL_shivangi_test_f90b7002");
			   body.put("LOAN_OFFER_ID", "ABFL_shivangi_test_f90b7002");
			   body.put("STATIC_TNC_SETNAME", LendingConstants.EMI_STATIC_TNC_SETNAME);
			   body.put("LENDER_STATIC_TNC_SETNAME", LendingConstants.EMI_LENDER_STATIC_TNC_SETNAME);
			   body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.EMI_LENDING_DYNAMIC_SECONDARY_TNC);
			   body.put("LENDER_ID", "6");
			   body.put("FLOW_TYPE", "RISK");
			   body.put("BUREAU_PRIORITY", "CIBIL,EXPERIAN");
			   body.put("IS_SELFIE_BEFORE_KYC", "true");
			   body.put("KYC_VERSION", "V3");
			   body.put("KYC_MODES_INITIATE_SEQUENCE", "SEARCH_BY_PAN,OFFLINE_AADHAAR,DIGILOCKER");
		  	 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/EMI/CreateLeadRequest.json";
		  	  
		  
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);

			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the data on the created lead using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateEMILead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<40;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		     }
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"286");
		        
		        Map<String,String> queryParams=new HashMap<String,String>();
		    	
		  	    queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		  	    queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		  	 
		  	    Map<String,String> headers=new HashMap<String,String>();
		  	    token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		  	    headers = LendingBaseAPI.setHeadersReceivedFromFE();
		  	    headers.put("Authorization", token);
		  	    headers.put("Content-Type", "application/json");
		  	    headers.put("custId", custId);
	         
		  	    Map<String,Object> body=new HashMap<String,Object>();
		  	    body.put("workflowOperation","BASIC_DETAILS");
		  	    body.put("DOB", LendingConstants.DOB_STASHFIN);
		  	    body.put("PAN", Pan);
	    	   	body.put("EMAIL", Utilities.randomEmailGeneration());
				 body.put("F_NAME", "TOUCH");
				 body.put("M_NAME", "WOOD");
				 body.put("L_NAME", "LIMITED");
				 body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
				 body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");
	    	   
		        requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/MCABasicDetailRequest.json";
		     

		  		 for(int i=0;i<2;i++)
		  		 {
		  			 
		  			 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
		  			 
		  			  if(responseObject.getStatusCode()==200)
		  			   break;
		  		 }
		  			  

		  		  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		  			  {
		  				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		  		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		  		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		  		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		  		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
		  		        //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		  			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
		  		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
		  		       
		  		        
		  		        leadId=responseObject.jsonPath().getString("leadId");
		  		        custId=responseObject.jsonPath().getString("custId");
		  		        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
		  		        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
		  		        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
		  		        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
		  		        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
		  		        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
		  		    
		  		      }
		  		  
		  	        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		  		
		  	  
		      }
		     }
		     
		     
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC004_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC005_UpdateBureauDataSetInSAI() {
			  Map<String, String> queryParams = new HashMap<String, String>();
				 queryParams.put("solution",LendingConstants.EMI);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		    
				
				
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowMovementRequired", "false");
				body.put("F_NAME", "BHAIRAVI");
				body.put("L_NAME", "LATASREE");
				body.put("GENDER", "FEMALE");
				body.put("PINCODE", "600024");
				body.put("PAN", Pan);
				body.put("DOB", "1973-01-01");
				body.put("EMAIL", "<EMAIL>");
				
			    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			//Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());
			
			}
		  
		  
		  
		  
		     @Test(description = "Fetch CIR",dependsOnMethods = "TC005_UpdateBureauDataSetInSAI",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC006_FetchCIR()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		    	 
				   Map<String,String> headers=new HashMap<String,String>();
			       headers.put("session_token", sessionToken);
			       headers.put("Content-Type", "application/json");
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			   
			
			 	  
			 	 for(int i=0;i<10;i++)
				  {
					  responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);
				
				 
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))
					  
					  break;
				  
				  }
			 	 
			 	
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			       
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
			        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


			       
			        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
			        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
			      
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC006_FetchCIR",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC007_FetchLeadAllData() throws JSONException
			    {
				  for(int i=0;i<23;i++)
				  {
				   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
					  break;
				  
				  }
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
				    
				  {
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
				        
				        //Hit BRE1 Callback
				        
				        LOGGER.info("Callback not coming so hitting BRE1 Callback");

					  String offerDetails= "{\"advanceEmi\":0,\"baseId\":\"OE_EMI_March_shivangi1_597724b6\",\"bureauKicker\":false,\"bureauThick\":1,\"coolOffPeriod\":0,\"eMandateType\":\"OPTIONAL\",\"field_investigation_needed\":false,\"flow\":\"RISK\",\"irr\":20.0,\"isBSAOffer\":false,\"isBre2Required\":false,\"joiningFees\":0.0,\"loanDownGradable\":false,\"loan_offered\":true,\"maxEmiAmount\":18000.0,\"maxLimit\":200000.0,\"maxLoanAmount\":200000.0,\"maxTenure\":24,\"merchantCategory\":\"Red,Amber,Green,Online\",\"minDownpayment\":2.0,\"newOfferGenerated\":true,\"oem\":\"\",\"offerEndDate\":\"Thu Jul 06 00:00:00 IST 2023\",\"offerId\":\"b695b9a0-5cac-48e9-8a0b-5e70d0612761\",\"offerStartDate\":\"Fri Apr 07 00:00:00 IST 2023\",\"paytmThick\":1,\"pemiRiskSegment\":\"PP_27\",\"permanentBlock\":60,\"pfFeeRate\":3.0,\"prevLenderLimit\":100000.0,\"processingFeeOverride\":true,\"productId\":\"9000\",\"productVersion\":1,\"riskGrade\":\"M3\",\"riskSegment\":\"M3\",\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\",\"subIrr\":18.0,\"taxOnJoiningFees\":0.0,\"temporaryBlock\":15,\"totalJoiningFees\":0.0}";
				        
				        responseObject=    lendingBaseClassObject. BRE1CallbackGeneric (leadId,LendingConstants.EMI,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,custId,offerDetails);
				        
				        
		            }
				  
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			       
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        
	            }
			   
			    
			       
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");
				 offerDetails=responseObject.jsonPath().getString("solutionAdditionalInfo.BRE1_OFFER_DETAILS");
			     JSONObject jsonObject = new JSONObject(offerDetails);
			     loanOfferId=jsonObject.get("offerId").toString();
			     System.out.println(loanOfferId);
			
				     
			    }
			  
	  
			  @Test(description = "EMI Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC007_FetchLeadAllData")
				@Owner(emailId = "<EMAIL>")
				public void TC008_LoanOfferAccept() {
					 Map<String,String> queryParams=new HashMap<String,String>();
						
					 queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					 queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
					 
					 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
						
					 Map<String, String> headers = new HashMap<String, String>();
					 headers.put("Authorization", token);
					 headers.put("Content-Type", "application/json");
					 headers.put("custId", custId);
				       
				     Map<String,Object> body=new HashMap<String,Object>();
				     body.put("workflowOperation", "OFFER_ACCEPTED");
				     
				     //solutionAdditionalInfo
				     body.put("LOAN_OFFER_ID", loanOfferId);
				     body.put("BASE_ID", "ABFL_shivangi_test_f90b7002");
				     body.put("EMI_LIMIT_IN_NUMBER", "18000");
				     body.put("TOTAL_JOINING_FEES", "0");
				     body.put("TAX_ON_JOINING_FEES", "0");
			
				     
				     requestBodyJsonPath="MerchantService/V1/workflow/lead/EMI/OfferAcceptedRequest.json";
					   
				     responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					    	
				     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
				     {
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
				        
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),loanOfferId);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"OE_EMI_April_shivangi1_e46f71a1");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMI_LIMIT_IN_NUMBER"),"18000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TOTAL_JOINING_FEES"),"0");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TAX_ON_JOINING_FEES"),"0");
					  }
				   }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_LoanOfferAccept",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC009_FetchLeadAllData() throws JSONException, SQLException
			    {
				  for(int i=0;i<35;i++)
				  {
				   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
					  break;
				  
				  }
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				  
				 
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
			    }
			  
//			  @Test(description = "Verify the EMI lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC009_FetchLeadAllData")
//				@Owner(emailId = "<EMAIL>")
//				public void TC010_uploadCustomerPhoto() throws InterruptedException {
//
//					Response responseObject = lendingBaseClassObject.KYCImageUpload("customerPhoto", leadId, custId,
//							LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.EMI, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"others","ImageOfflineAdhaar.jpeg");
//
//					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
//					Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
//				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
//				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
//
//
//				}
//
//				@Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC010_uploadCustomerPhoto")
//				@Owner(emailId = "<EMAIL>")
//				public void TC011_VerifyUploadedCustomerPhoto() {
//
//					responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					//Customerphoto
//					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
//					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");
//
//				}
				
				@Test(description = "Verify the EMI lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC009_FetchLeadAllData")
				@Owner(emailId = "<EMAIL>")
				public void TC012_UploadSelfie() throws InterruptedException {
					
					Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
							LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.EMI, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"selfie","RohanOfflineAadhaar.jpg");
				
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
					Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
				
					
				}
				
				@Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC012_UploadSelfie", groups = {
				"Regression" })
				@Owner(emailId = "<EMAIL>")
				public void TC013_VerifyUploadedSelfie() {

					responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
					if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
						Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
						Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");
						
					}else {
						Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
						Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");

						
					}
					
				}
				
//				@Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC013_VerifyUploadedSelfie")
//				@Owner(emailId = "<EMAIL>")
//				public void TC014_InitiateKYC_UsingSearchByPan() {
//					Map<String,String> queryParams=new HashMap<String,String>();
//					queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//
//					Map<String,String> headers=new HashMap<String,String>();
//					token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//				    headers = LendingBaseAPI.setHeadersReceivedFromFE();
//				    headers.put("Authorization", token);
//				    headers.put("Content-Type", "application/json");
//				    headers.put("custId", custId);
//
//				    Map<String,Object> body=new HashMap<String,Object>();
//				    body.put("kycMode","SEARCH_BY_PAN");
//
//
//				    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";
//
//				    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
//
//					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//					Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
//					Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
//					Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
//
//				}
//
//				@Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC014_InitiateKYC_UsingSearchByPan")
//				@Owner(emailId = "<EMAIL>")
//					public void TC015_FetchDataPostKYCIntiated() throws JSONException
//					{
//
//					 for(int i =0;i<=15;i++) {
//							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
//						    	 break;
//						     }
//					 }
//				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
//				     {
//				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
//				  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");
//
//				  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//
//			  	   	}
//
//			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
//
//
//					}
//
//				@Test(description="Initiate KYC using OA",groups = {"Regression"},dependsOnMethods = "TC013_VerifyUploadedSelfie")
//				@Owner(emailId = "<EMAIL>")
//				public void TC016_InitiateKYC_UsingOfflineAAdhaar() {
//					Map<String,String> queryParams=new HashMap<String,String>();
//					queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//
//					Map<String,String> headers=new HashMap<String,String>();
//					token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//				    headers = LendingBaseAPI.setHeadersReceivedFromFE();
//				    headers.put("Authorization", token);
//				    headers.put("Content-Type", "application/json");
//				    headers.put("custId", custId);
//
//				    Map<String,Object> body=new HashMap<String,Object>();
//				    body.put("shareCode","2346");
//
//
//				    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";
//
//				    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
//
//					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//					Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
//					Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
//					Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
//
//				}
//
//				@Test(description = "Verify lead details after initiate kyc",groups={"Regression"},dependsOnMethods = "TC016_InitiateKYC_UsingOfflineAAdhaar")
//				@Owner(emailId = "<EMAIL>")
//					public void TC017_FetchDataPostKYCIntiated() throws JSONException
//					{
//
//					 for(int i =0;i<=15;i++) {
//							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
//						    	 break;
//						     }
//					 }
//				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
//				     {
//				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
//				  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");
//
//				  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//
//			  	   	}
//
//			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
//
//
//
//					}
				
				  @Test(description = "Update data in SAI Table", dependsOnMethods = "TC013_VerifyUploadedSelfie", groups = { "Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC018_LeadDataUpdateForKYC_InSAI() {
					Map<String, String> queryParams = new HashMap<String, String>();
					 queryParams.put("solution",LendingConstants.EMI);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
			     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			    
			     	 String randomPan=utility.randomIndividualPANValueGenerator();
					
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);

					  Map<String, Object> body = new HashMap<String, Object>();
					  body.put("workflowMovementRequired", "false");
					  body.put("PAN","**********");
					  body.put("DOB", "1998-09-12");
					  body.put("GENDER", "MALE");
					  body.put("NSDL_NAME", "Rohan Shivaji Sonawane");

                    requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";
					
				    responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				

					}
				  @Test(description="Initiate KYC using OA",groups = {"Regression"},dependsOnMethods = "TC018_LeadDataUpdateForKYC_InSAI")
					@Owner(emailId = "<EMAIL>")
					public void TC019_InitiateKYC_UsingOfflineAAdhaar() {
						Map<String,String> queryParams=new HashMap<String,String>();
						queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
				  	 
						Map<String,String> headers=new HashMap<String,String>();
						token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
					    headers = LendingBaseAPI.setHeadersReceivedFromFE();
					    headers.put("Authorization", token);
					    headers.put("Content-Type", "application/json");
					    headers.put("custId", custId);
					       
					    Map<String,Object> body=new HashMap<String,Object>();
					    body.put("shareCode","1234");
					  
					    
					    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";
					    
					    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
						
						lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
						Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
						Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
						Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
						Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
						
					}
					
					@Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC019_InitiateKYC_UsingOfflineAAdhaar")
					@Owner(emailId = "<EMAIL>")
						public void TC020_FetchDataPostKYCIntiated() throws JSONException
						{

							try {


								Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
								{
									responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, Solution.paytm_emi.getSolutionType(), SolutionTypeLevel2.ABFL.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
									if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage()))
										Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());
									return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage());
								});
							} catch (Exception e) {
								System.out.println("FAILED");
								e.printStackTrace();
							}


							if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_INITIATED.getStage())) {
								LOGGER.info("Actual Callback not coming from KYC so hitting KYC SBP mock Callback");

								responseObject = lendingBaseClassObject.KYCCallbackusingOfflineAdhaar(leadId, Solution.paytm_emi.getSolutionType(), LendingConstants.DIY_P4B_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, SolutionTypeLevel2.ABFL.getSolutionTypeLevel2(), custId);

							}
							LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
							Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());
							Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "375");

						}
					
					 @Test(description = "Accept address consent",dependsOnMethods = "TC020_FetchDataPostKYCIntiated",groups = {"Regression"})
					  @Owner(emailId = "<EMAIL>",isAutomated = true)
					    public void TC021_Accept_KYCAddressConsent()
					    {
						  Map<String,String> queryParams=new HashMap<String,String>();
						
						  queryParams.put("solution",LendingConstants.EMI);
						  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
				    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
				     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
				    	
						
				    	  Map<String,String> headers=new HashMap<String,String>();
				    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
					       headers = LendingBaseAPI.setHeadersReceivedFromFE();
					       headers.put("Authorization", token);
					       headers.put("Content-Type", "application/json");
					       headers.put("custid", custId);
					       headers.put("ipAddress", "************");
					        
					       Map<String,Object> body = new HashMap<String, Object>();
					  	   body.put("workflowOperation","KYC_ADDRESS_CONSENT_ACCEPTED");
					  	   body.put("KYC_ADDRESS_LENDER_STATIC_TNC_SETNAME", LendingConstants.EMI_KYC_ADDRESS_LENDER_STATIC_TNC_SETNAME);
					  	   
					  	 
						   requestBodyJsonPath="MerchantService/V1/workflow/lead/EMI/KYCAddressConsentAcceptance.json";
					  	  
					  
						 for(int i=0;i<2;i++)
						 {
							 
							 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
							 
							  if(responseObject.getStatusCode()==200)
							   break;
						 }
							  
				
						  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
							  {
								LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
						        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
						        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
						        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
						        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
						        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_ADDRESS_CONSENT_ACCEPTED.getStage());
						        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_ADDRESS_CONSENT_ACCEPTED.getStage());
						        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2030");


						        
						        leadId=responseObject.jsonPath().getString("leadId");
						        custId=responseObject.jsonPath().getString("custId");
						        kycAddressLenderStaticTNCAcceptanceIP=responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_ADDRESS_LENDER_STATIC_TNC_ACCEPTANCE_IP");
						        kycAddressLenderStaticTNCAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_ADDRESS_LENDER_STATIC_TNC_ACCEPTANCE_TIMESTAMP");
						        kycAddressLenderStaticTNCSetname=responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_ADDRESS_LENDER_STATIC_TNC_SETNAME");
						     
						    
						    
						      }
						  
					    }
						  
					

			@Test(description = "Update lead details",dependsOnMethods = "TC021_Accept_KYCAddressConsent",groups = {"Regression"})
			@Owner(emailId = "<EMAIL>",isAutomated = true)
			  public void TC022_VerifyLeadStage()
			  {
				  
				  for(int i=0;i<30;i++)
				  {
				   
					  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_SUCCESS.getStage()))
					  break;
				  
				  }
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
				    
				  {
				     LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_REQUESTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");  
				        
				        //Hit BRE2 Callback
			         LOGGER.info("Callback not coming so hitting BRE2 Callback");
				        
				      responseObject=lendingBaseClassObject. BRE2CallbackforEMI (leadId,LendingConstants.EMI,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,custId);
				        
				        
			      }
				  
			   if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_SUCCESS.getStage()))
			   {
			  	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			     
			      Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_SUCCESS.getStage());
			      Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
			      Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"715");
	
			  }
			   
			      Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_SUCCESS.getStage());
			      Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
		
  }
			
			  @Test(description = "Update data in SAI Table", dependsOnMethods = "TC022_VerifyLeadStage", groups = { "Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC023_LeadDataUpdateForKYC_InSAI() {
				
			
				 Map<String, String> queryParams = new HashMap<String, String>();
				 queryParams.put("solution",LendingConstants.EMI);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		    
				
				
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowMovementRequired", "false");
				body.put("PAN",Pan);
				body.put("DOB", "1979-10-05");
				body.put("GENDER", "FEMALE");
				body.put("EMAIL", "<EMAIL>");
				
				  responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			     lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			     Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
					Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BRE2_SUCCESS.getStage());
					
				
				}
				@Test(description = "Enter the bank details", dependsOnMethods = "TC023_LeadDataUpdateForKYC_InSAI", groups = {
				"Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC024_SaveBankDetails() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.EMI);
			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
			headers.put("Content-Type", "application/json");
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowOperation", "VERIFY_BANK_DETAILS");
			body.put("EMANDATE_TYPE", "Internet Banking");
			body.put("bankName", "PAYTM BANK");
			body.put("bankAccountNumber", "************");
			body.put("ifsc", "PYTM0123456");
			body.put("bankAccountHolderName", "testNameMatch");
			
			Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
			
			if (responseObject.getStatusCode() == 200) {
			
				Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VALIDATION_PENDING");
			
			}
			
			else {
				
				for (int i = 1; i < 4; i++) {
					LOGGER.info("Again hitting with same data: retry-count: " + i);
					responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
					
					if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VALIDATION_PENDING"))
						break;
				}
			
			}
			
		
				Assert.assertEquals(responseObject.getStatusCode(),200);
				
			
			}
				
				
				@Test(description = "Verify for CKYC name update in SAI", dependsOnMethods = "TC024_SaveBankDetails", groups = { "Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC025_UpdateKYCNameInSAI() {
			
				
				 Map<String, String> queryParams = new HashMap<String, String>();
				 queryParams.put("solution",LendingConstants.EMI);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		   
				
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowMovementRequired", "false");
				body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
				body.put("PAN", Pan);
				requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
				responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);
				
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
				Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
				responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
						LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			
				
				
				}
				 
				 
					@Test(description = "Enter the bank details", dependsOnMethods = "TC025_UpdateKYCNameInSAI", groups = {
					"Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC026_SaveBankDetails() {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("solution", LendingConstants.EMI);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("session_token", sessionToken);
				headers.put("Content-Type", "application/json");
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowOperation", "VERIFY_BANK_DETAILS");
				body.put("EMANDATE_TYPE", "Internet Banking");
				body.put("bankName", "PAYTM BANK");
				body.put("bankAccountNumber", Utilities.generateRandomBankAccountNumber());
				body.put("ifsc", "PYTM0123456");
				body.put("bankAccountHolderName", "testNameMatch");
				
				Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
				
				if (responseObject.getStatusCode() == 200) {
				
					Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");
				
				}
				
				else {
					
					for (int i = 1; i < 6; i++) {
						LOGGER.info("Again hitting with same data: retry-count: " + i);
						responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
						
						if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
							break;
					}
				
				}
				
			
					Assert.assertEquals(responseObject.getStatusCode(),200);
					
				
				}
			 
				@Test(description = "For EMI lead Bank Verification", dependsOnMethods = "TC026_SaveBankDetails",groups = {"Regression"})
				@Owner(emailId = "<EMAIL>")
				public void TC027_FetchLeadPostBankVerification() {
					for(int i=0;i<5;i++) {
					responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
							LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
						if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
							break;
						}
					}
					LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
				    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"PAYTM BANK");
				   
				    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");
				   
				    
				    
				}	 
				
				@Test(description = "Verify EMI Lead Emandate Callback", dependsOnMethods = "TC027_FetchLeadPostBankVerification", groups = {
				"Regression" })
				@Owner(emailId =  "<EMAIL>")
				public void TC028_EmandateCallback() throws InterruptedException {
					Map<String, String> queryParams = new HashMap<String, String>();
					queryParams.put("leadId", leadId);
					queryParams.put("solution", LendingConstants.EMI);
				
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
				
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
				
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("status", "EMANDATE_SUCCESS");
				
					Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
				
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
				
				}
				
				@Test(description = "Emandate Callback Stage Verification", dependsOnMethods = "TC028_EmandateCallback",groups = {"Regression"})
				@Owner(emailId = "<EMAIL>")
				public void TC029_EMI_FetchLeadPostEmandate() {
					responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
							LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
				}
			  
				@Test(description = " Generate Sanction Letter", dependsOnMethods = "TC029_EMI_FetchLeadPostEmandate", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC030_GenerateSanctionLetter() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
			queryParams.put("solution", LendingConstants.EMI);
			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);

			queryParams.put("tncType", "LOAN_SANCTION_TNC");
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
		
			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.jsonPath().getString("meta.status").equals("success")) {
		
				codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
				tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
				urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
				uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
				md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
			}
		
		}
		
		@Test(description = "Accept sanction letter", dependsOnMethods = "TC030_GenerateSanctionLetter", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC031_AcceptSanctionLettert() throws InterruptedException {
		
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
			queryParams.put("solution", LendingConstants.EMI);
			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sessionToken);
			headers.put("latitude", "25");
			headers.put("longitude", "32");
			headers.put("ipAddress", "***********");
			headers.put("deviceIdentifier", "OE-Automation");
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
		
			body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
			body.put("SECONDARY_ACCEPTED_TNC_VERSION", 9);
		
			body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
			body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
			
			requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3LoanAgreementAcceptrequest.json";
			responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());

			Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		
		}
		    
	
		@Test(description = "Update lead details",dependsOnMethods = "TC031_AcceptSanctionLettert",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>",isAutomated = true)
		  public void TC032_VerifyLeadStage_LMSSubmitApplicationJob()
		  {
			  
			  for(int i=0;i<10;i++)
			  {
			   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
				  break;
			  
			  }
			 
		
		   
		      Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
		      Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
			  Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"346");
	
            }
		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC032_VerifyLeadStage_LMSSubmitApplicationJob",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC033_LMSDataCallback() throws JSONException
		    {
			  
			  Map<String,String> queryParams=new HashMap<String,String>();
				
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.EMI,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  
	    	   Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_JWT_Secret);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>(); 
		       for(int i=0;i<10;i++){
				responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				 
		  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
				   break;
			 }
		     if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
		     {	
			       body.put("workflowOperation","LMS_APPLICATION_APPROVED");
			  	   body.put("LOAN_ACCOUNT_NUMBER", "EMI"+Utilities.randomLendingLoanAccountNumberGenerator());
			  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
			  	   body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
			  	   body.put("LENDER_LOAN_ACCOUNT_NUMBER", "DXW-M10126-*********");
			  	   
		    	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LMSSubmitApplicationCallbackRequest.json";   
		    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
				
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"330");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_CREATED_ON"),"*************");
					  }
		     }
		     
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
		    }  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC033_LMSDataCallback",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC034_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<25;i++)
			  {
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.EMI,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
			  {
				  break;
			  }
			  
			  }	  
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
		    }
		  
				     	   
				
		      	
		  
		    

}
