package OCL.Lending.BusinessLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class HomeFirst extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(HomeFirst.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1002179993";
		 String consumerNumber="7770008819";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
		 String uuid="";
		 String md5="";
		 String code="";
		 String tncName="";
		 String url="";
		 String uniqueIdentifier="";
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
		 String kybSecondaryTNCDisplayURL="";
		 String loanAgreementDate="";
		 String kybTNCDisplayURL="";
		 String panNameMatchTimeStamp="";
		 String panNameMatchPercentage="";
		 String breLastFetchDate="";
		 

		 Response responseObject= null;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing Home First lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDeatils()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("leadId",leadId);
			  queryParams.put("solution",LendingConstants.HL_DISTRIBUTION);
			  queryParams.put("custId",custId);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreateHomeFirstLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.HL_DISTRIBUTION);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.HOME_FIRST);
	    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
			
	    	   Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile","7770008819");
		  	   body.put("LENDER_ID", "15");
		  	   body.put("PRODUCT_ID", "100000158");
		  	   body.put("PRODUCT_TYPE", "HL");
		  	   body.put("PRODUCT_VERSION","1");
			   body.put("FLOW_TYPE", "DISTRIBUTION");
			   body.put("MERCHANT_ID", "TQyPdP1001670120");
			   body.put("OFFER_START_DATE", "2022-02-10");
			   body.put("OFFER_END_DATE", "2022-05-14");
			   body.put("LOAN_MAX_AMOUNT", "800000");
			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
		  	   body.put("USER_INPUT_F_NAME", "Shivangi");
		  	   body.put("USER_INPUT_M_NAME", "");
		  	   body.put("USER_INPUT_L_NAME", "Goswami");
			   body.put("GENDER","Female");
		  		 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CreateHomeFirstLeadRequest.json";
		  	
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.USER_INPUT_L_NAME"),"Goswami");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.USER_INPUT_F_NAME"),"Shivangi");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"100000158");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),"Redmi");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),"77.0900393");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2022-02-10");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"HL");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2022-05-14");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),"28.66494");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"800000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"Female");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"15");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"TQyPdP1001670120");

			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 @Test(description = "Update lead basic details",dependsOnMethods = "TC003_CreateHomeFirstLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_UpdateLeadBasicDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","BASIC_DETAILS");
		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
		  	   body.put("F_NAME", "Shivangi");
		  	   body.put("L_NAME","goswami");
		  	   body.put("CITY", "Delhi");
		  
	
		       
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/BasicDetailsHomeFirstRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),"TOUCH WOOD LIMITED");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CITY"),"Delhi");
    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),"TOUCH");

				        leadId=responseObject.jsonPath().getString("leadId");
				        custId=responseObject.jsonPath().getString("custId");
				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
				    
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
			
			  
		    }
		 
		 @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC004_UpdateLeadBasicDetails", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC005_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.HL_DISTRIBUTION);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "BHAIRAVI");
			body.put("L_NAME", "LATASREE");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "600024");
			body.put("PAN", "**********");
			body.put("DOB", "1979-10-05");
			body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());
			
			}
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC005_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchCIR()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.HOME_FIRST);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		   
		
		 	  
		 	 for(int i=0;i<10;i++)
			  {
				  responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);
			
			 
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
		       // Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
		        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


		        bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
		        bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
		        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
		        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
		      
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC006_FetchCIR",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC007_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<25;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		       
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
		     }
		   
		
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC007_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC008_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<13;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
			    
			  {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			       
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
			        
			        //Hit BRE1 Callback
			        
			        LOGGER.info("BRE1 Callback from risk is not coming so hitting BRE1 via manual callback");
			        
			        responseObject=    lendingBaseClassObject. callbackWithOnlyWorkflowoperation (custId, LendingConstants.HL_DISTRIBUTION,"BRE1_SUCCESS");
			        
			        
	            }
			  
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
            }
		   
		    
		       
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC008_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC009_UpdateExistingDetailsInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.HL_DISTRIBUTION);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "TOUCH");
			body.put("L_NAME", "LIMITED");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "110096");
			body.put("PAN", Pan);
			body.put("DOB", LendingConstants.DOB_STASHFIN);
			body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
			
			}
		  
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC009_UpdateExistingDetailsInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC010_UpdateAdditionalDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.HOME_FIRST);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","ADDITIONAL_DETAILS");
		  	   body.put("EMPLOYMENT_TYPE", "Salaried");
		  	   body.put("ANNUAL_INCOME", "200000");
		  	   body.put("IS_ITR_FILED", "Yes");
		  	   body.put("PROPERTY_TYPE","Commercial");
			   body.put("PROPERTY_VALUE","500000");
			   body.put("LOAN_AMOUNT_REQUESTED","25000");
			   body.put("LOAN_TENURE_REQUESTED","10");
			   body.put("PROPERTY_SUB_TYPE","Factory");
			   body.put("MONTHLY_INCOME","10000");
			   body.put("INCOME_MODE","CASH");
			
			   body.put("line1","A-3,Saket");
			   body.put("line2","148 Civil Lines");
			   body.put("line3","Near Axis Bank");
			   body.put("city","DELHI");
			   body.put("state","DELHI");
			   body.put("pincode","110091");
			   body.put("addressType","BUSINESS");
			   body.put("addressSubType","REGISTERED");
		       
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/PiramalAdditionalDetailsWithCoapplicantDetailsRequest.json";
		      responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
			
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"708");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMPLOYMENT_TYPE"),"Salaried");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ANNUAL_INCOME"),"200000");
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_ITR_FILED"),"Yes");
    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_TYPE"),"Commercial");
      			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_VALUE"),"500000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_ITR_FILED"),"Yes");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_TYPE"),"Commercial");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_VALUE"),"500000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_REQUESTED"),"25000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_REQUESTED"),"10");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_SUB_TYPE"),"Factory");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MONTHLY_INCOME"),"10000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INCOME_MODE"),"CASH");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].addressType"),"BUSINESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].addressSubType"),"REGISTERED");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].line1"),"A-3,Saket");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].line2"),"148 Civil Lines");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].line3"),"Near Axis Bank");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].city"),"DELHI");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].pincode"),"110091");
				        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].state"),"DELHI");
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage());
		  
		    }
		  
		  @Test(description = "Update lead details",dependsOnMethods = "TC010_UpdateAdditionalDetails",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC011_VerifyLeadStage()
		    {
			  
			  for(int i=0;i<10;i++)
			  {
			   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_SUCCESS.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
			    
			  {
			     LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");  
			        
			        //Hit BRE2 Callback
                   LOGGER.info("Callback not coming so hitting BRE2 Callback");
			        
			      responseObject=lendingBaseClassObject. BRE2CallbackWithLoanOfferKeys (custId,LendingConstants.HL_DISTRIBUTION);
			        
			        
	            }
			  
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_SUCCESS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"715");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
            }
		   

			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPLICATION_SUCCESS.getStage());
			 
		    }
		  
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC011_VerifyLeadStage",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC012_FetchLeadAllData() throws JSONException
//		    {
//			  
//			  for(int i=0;i<15;i++)
//			  {
//			   
//			
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.HL_DISTRIBUTION,LendingConstants.HOME_FIRST,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//			 
//			  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
//			  {
//				  break;
//			  }
//			  
//			  }
//			  
//	         
//		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
//		    
//		
//		    }
		  
		  
		  
}
