package OCL.Lending.BusinessLending.ABFL;

import java.util.HashMap;
import java.util.Map;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;

public class TestMCAABFLUsingOA extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(TestMCAABFLUsingOA.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();

		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1002531918";
		 String consumerNumber="8755844880";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
		 String uuid="";
		 String md5="";
		 String code="";
		 String tncName="";
		 String url="";
		 String uniqueIdentifier="";
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
		 String kybSecondaryTNCDisplayURL="";
		 String loanAgreementDate="";
		 String kybTNCDisplayURL="";
		 String panNameMatchTimeStamp="";
		 String panNameMatchPercentage="";
		 String breLastFetchDate="";
		 private String randomGeneratedPan="";
		 private String randomBankAccountNumber;

		 Response responseObject= null;
		
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing MCA Piramal lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDetails_MCA_Piramal()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDetails_MCA_Piramal",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();

			  queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
				queryParams.put("leadId",leadId);
				queryParams.put("custId", custId);

			  queryParams.put("leadId",leadId);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create MCA V3 ABFL Lead",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_Create_MCA_V3_ABFL_Lead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
	    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  	   body.put("PRODUCT_TYPE", "MCA");
		  	   body.put("PRODUCT_VERSION", "1");
		  	   body.put("PRODUCT_ID","104");
			   body.put("LOAN_AMOUNT_IN_NUMBER", "75000");
			   body.put("LOAN_INTEREST_AMOUNT", "6720");
			   body.put("LOAN_AMOUNT_IN_WORDS", "Seventy Five thousand rupees only");
			   body.put("LOAN_MIN_AMOUNT", "10000");
			   body.put("LOAN_MAX_AMOUNT", "300000");
			   body.put("LOAN_TENURE", "180");
		  	   body.put("LOAN_TENURE_MIN", "180");
		  	   body.put("LOAN_TENURE_MAX", "180");
		  	   body.put("LOAN_TENURE_UNIT", "DAY");
			   body.put("LOAN_RATE_OF_INTEREST","35");
		  	   body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "454");
			   body.put("LOAN_PROCESSING_FEE", "750");
			   body.put("LOAN_INCENTIVE_PERCENTAGE", "0");
			   body.put("MERCHANT_ID", "LLlnce56294583655911");
			   body.put("BASE_ID", "mca_abfl_OE_prefill_shivangi_ccce46d2");
			   body.put("LOAN_OFFER_ID", "d9ce04e4-530b-411e-8fae-b9aa0e51c9b3");
			   body.put("RISK_GRADE", "MCA|BVB84");
			   body.put("PROCESSING_FEE_RATE", "1.0");
			   body.put("IS_ACCEPTANCE_ABOVE_5000", true);
			   body.put("IS_SI_MANDATORY", true);
			   body.put("IS_RESTRICTED_MERCHANT", false);
			   body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
			   body.put("IS_EMANDATE_ELIGIBLE", false);
			   body.put("STATIC_TNC_SETNAME", "loanstatictnc");
			   body.put("LENDER_STATIC_TNC_SETNAME", "bl_ckyck_lender_consent_abfl");
			   body.put("LENDING_DYNAMIC_TNC", "bl_loan_agreement_abfl");
			   body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_abfl");
			   body.put("FLOW_TYPE", "RISK");
			   body.put("LENDER_ID", "6");
			   body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "885");
			   body.put("LOAN_DISBURSAL_AMOUNT", "74115");
			   body.put("TOTAL_AMOUNT_PAID_BY_BORROWER", "82605");
			   body.put("ANNUAL_PERCENTAGE_RATE", "40.07");
			   body.put("IS_SELFIE_BEFORE_KYC", "TRUE");
			   body.put("BUSINESS_CATEGORY", "Airport");
			   body.put("BUSINESS_SUB_CATEGORY", "DutyFree");
			   body.put("LOAN_EDI_IN_WORDS", "Twenty eight");
			   body.put("OFFER_GENERATION_TIMESTAMP", "1684139128951");
			   body.put("OFFER_ACCEPTANCE_TIMESTAMP", "1684139128951");
			   body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "TRUE");
		  	 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/MCAABFLRequest.json";
		  	  
		  	  
			   
			
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"104");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"75000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),"6720");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_WORDS"),"Seventy Five thousand rupees only");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MIN_AMOUNT"),"10000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"300000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"180");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"180");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"180");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"DAY");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"35");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"454");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),"750");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_PERCENTAGE"),"0");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"LLlnce56294583655911");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"mca_abfl_OE_prefill_shivangi_ccce46d2");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"d9ce04e4-530b-411e-8fae-b9aa0e51c9b3");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_GRADE"),"MCA|BVB84");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"1.0");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"loanstatictnc");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_abfl");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"bl_loan_agreement_abfl");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"bl_sanction_letter_abfl");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"6");
			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_Create_MCA_V3_ABFL_Lead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		    
		
		    }
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC005_UpdateLeadBasicDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","BASIC_DETAILS");
		  	   body.put("DOB", "1973-01-01");
		  	   body.put("PAN", Pan);
		  	   body.put("EMAIL", "<EMAIL>");
				body.put("F_NAME", "TOUCH");
				body.put("M_NAME", "WOOD");
				body.put("L_NAME", "LIMITED");
				body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
				body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");
		  	   
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/MCABasicDetailRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				      //  Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
				       
				        
				        leadId=responseObject.jsonPath().getString("leadId");
				        custId=responseObject.jsonPath().getString("custId");
				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
				        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				  
			    
		     
			  
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_UpdateLeadBasicDetails",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

		        lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
		        
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		    
		
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC007_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			 queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
	    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
			
			randomGeneratedPan=UtilitiesObject.randomIndividualPANValueGenerator();
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowMovementRequired", "false");
	
			body.put("F_NAME", "BHAIRAVI");
			body.put("CKYC_F_NAME", "BHAIRAVI");
			body.put("CKYC_L_NAME", "LATASREE");
			body.put("L_NAME", "LATASREE");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "600024");
			//random pan generated each time so that there is new entry at lender end
			body.put("PAN", randomGeneratedPan);
			body.put("DOB", "1979-10-05");
			body.put("EMAIL", "<EMAIL>");
			body.put("CKYC_PINCODE", "600024");
			
			
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/DataUpdateRequest.json";
			
		    responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
			Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());

			
			}
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC007_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC008_FetchCIR()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       
		       String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
		   
		
		 	  
		 	 for(int i=0;i<10;i++)
			  {
				  responseObject= lendingBaseClassObject.v3FetchCIR(queryParams, headers,body,requestjsonpath);
			
			 
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
		        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


		        bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
		        bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
		        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
		        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
		      
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_FetchCIR",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC009_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<100;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
			    
			  {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			       
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
			        
			        //Hit BRE1 Callback
			        
			        LOGGER.info("Actual Callback not coming from risk so hitting BRE1 mock Callback");
			        
			        responseObject=    lendingBaseClassObject. BRE1CallbackforMCAABFL (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,custId);
			        
			        
	            }
			  
			    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
		  
		    }
		  @Test(description = "Verify lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC009_FetchLeadAllData")
			@Owner(emailId = "<EMAIL>")
			public void TC010_uploadCustomerPhoto() throws InterruptedException {
				
				Response responseObject = lendingBaseClassObject.KYCImageUpload("customerPhoto", leadId, custId,
						LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"others","RohanOfflineAadhaar.jpg");
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
			
				
			}
			
			@Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC010_uploadCustomerPhoto")
			@Owner(emailId = "<EMAIL>")
			public void TC011_VerifyUploadedCustomerPhoto() {

				responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				//Customerphoto
				Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
				Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");
						
			}
			
			@Test(description = "Verify the ABFL lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC011_VerifyUploadedCustomerPhoto")
			@Owner(emailId = "<EMAIL>")
			public void TC012_UploadSelfie() throws InterruptedException {
				
				Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
						LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"selfie","RohanOfflineAadhaar.jpg");
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
			}
			
			@Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC012_UploadSelfie", groups = {
			"Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC013_VerifyUploadedSelfie() {

				responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");
					
				}else {
					Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");

					
				}
				
			}
			
			@Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC013_VerifyUploadedSelfie")
			@Owner(emailId = "<EMAIL>")
			public void TC014_InitiateKYC_UsingSearchByPan() {
				Map<String,String> queryParams=new HashMap<String,String>();
				queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		  	 
				Map<String,String> headers=new HashMap<String,String>();
				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			    headers = LendingBaseAPI.setHeadersReceivedFromFE();
			    headers.put("Authorization", token);
			    headers.put("Content-Type", "application/json");
			    headers.put("custId", custId);
			       
			    Map<String,Object> body=new HashMap<String,Object>();
			    body.put("kycMode","SEARCH_BY_PAN");
			  
			    
			    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";
			    
			    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
				
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
				Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
				Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
				Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
				
			}
			
			@Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC014_InitiateKYC_UsingSearchByPan")
			@Owner(emailId = "<EMAIL>")
				public void TC015_FetchDataPostKYCIntiated() throws JSONException
				{
				  
				 for(int i =0;i<=55;i++) {
						responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			
					     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
					    	 break;
					     }
				 }
			    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
			     {
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
			  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");
			  	   	
			  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			  	   	
		  	   	}
			    
		    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
			  	  
			
				}
			
			@Test(description="Initiate KYC using OA",groups = {"Regression"},dependsOnMethods = "TC015_FetchDataPostKYCIntiated")
			@Owner(emailId = "<EMAIL>")
			public void TC016_InitiateKYC_UsingOfflineAAdhaar() {
				Map<String,String> queryParams=new HashMap<String,String>();
				queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		  	 
				Map<String,String> headers=new HashMap<String,String>();
				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			    headers = LendingBaseAPI.setHeadersReceivedFromFE();
			    headers.put("Authorization", token);
			    headers.put("Content-Type", "application/json");
			    headers.put("custId", custId);
			       
			    Map<String,Object> body=new HashMap<String,Object>();
			    body.put("shareCode","1234");
			  
			    
			    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";
			    
			    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
				
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
				Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
				Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
				Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
				
			}
			
			@Test(description = "Verify lead details after initiate kyc",groups={"Regression"},dependsOnMethods = "TC016_InitiateKYC_UsingOfflineAAdhaar")
			@Owner(emailId = "<EMAIL>")
				public void TC017_FetchDataPostKYCIntiated() throws JSONException
				{
				  
				 for(int i =0;i<=65;i++) {
						
					 responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			
					     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
					    	 break;
					     }
				 }
			    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
			     {
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
			  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");
			  	   	
			  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			  	   	
		  	   	}
			    
		    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
			  	  
			
				
				}
			
			  @Test(description = "Update data in SAI Table", dependsOnMethods = "TC017_FetchDataPostKYCIntiated", groups = { "Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC018_LeadDataUpdateForKYC_InSAI() {
				Map<String, String> queryParams = new HashMap<String, String>();
				 queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
				
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
				
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowMovementRequired", "false");
				body.put("PAN","**********");
				body.put("DOB", "1998-09-12");
				body.put("GENDER", "MALE");
				body.put("NSDL_NAME", "Rohan Shivaji Sonawane");
				
				requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";
				
			    responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);
				
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
				//Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
				
				}
			  @Test(description="Initiate KYC using OA",groups = {"Regression"},dependsOnMethods = "TC018_LeadDataUpdateForKYC_InSAI")
				@Owner(emailId = "<EMAIL>")
				public void TC019_InitiateKYC_UsingOfflineAAdhaar() {
					Map<String,String> queryParams=new HashMap<String,String>();
					queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			  	 
					Map<String,String> headers=new HashMap<String,String>();
					token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
				    headers = LendingBaseAPI.setHeadersReceivedFromFE();
				    headers.put("Authorization", token);
				    headers.put("Content-Type", "application/json");
				    headers.put("custId", custId);
				       
				    Map<String,Object> body=new HashMap<String,Object>();
				    body.put("shareCode","1234");
				  
				    
				    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";
				    
				    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
					
				}
				
			  @Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC019_InitiateKYC_UsingOfflineAAdhaar")
				@Owner(emailId = "<EMAIL>")
					public void TC020_FetchDataPostKYCIntiated() throws JSONException
					{
					  
					 for(int i =0;i<=50;i++) {
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage())) {
						    	 break;
						     }
					 }
				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_INITIATED.getStage()))
				     {
                     //Hit KYC Callback
				        
				        LOGGER.info("Actual Callback not coming from KYC so hitting KYC OA mock Callback");
				        
				        responseObject=    lendingBaseClassObject. KYCCallbackusingOfflineAdhaar (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,custId);
				        
			  	   	}
				    
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_COMPLETED.getStage());
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_COMPLETED.getStage());
			  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"375");
			  	   	
				
					}
				@Test(description = "Verify lead details after KYC",groups={"Regression"},dependsOnMethods = "TC020_FetchDataPostKYCIntiated")
				@Owner(emailId = "<EMAIL>")
					public void TC021_FetchDataKYCInitiate() throws JSONException
					{
					  
					 for(int i =0;i<=35;i++) {
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_REQUESTED.getStage())) {
						    	 break;
						     }
					 }
				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
				     {
				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
				  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");
				  	   	
				  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				  	   	
			  	   	}
				    
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
				  	  
				
					}
				
				@Test(description = "Verify Second BRE callback", dependsOnMethods = "TC021_FetchDataKYCInitiate",groups = {"Regression"})
				@Owner(emailId = "<EMAIL>")
				public void TC022_SecondBRECallback() {
					
					responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
							LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
							equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
				     {
						Map<String, String> queryParams = new HashMap<String, String>();
						queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
						queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
						queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
						queryParams.put("leadId", leadId);
						
						token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,LendingConstants.JWT_RISK_KEY);
					
						Map<String, String> headers = new HashMap<String, String>();
						headers.put("Authorization", token);
						headers.put("Content-Type", "application/json");
						headers.put("custId", custId);
					
						Map<String, Object> body = new HashMap<String, Object>();
						body.put("workflowOperation", "BRE2_SUCCESS");
						body.put("BASE_ID", "mca_abfl_OE_prefill_shivangi_ccce46d2");
						body.put("LOAN_OFFER_ID", "d9ce04e4-530b-411e-8fae-b9aa0e51c9b3");
						body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
						body.put("SKIP_EMANDATE_ELIGIBLE", "false");
						body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");
				   
						responseObject = lendingBaseClassObject.BRE2CallbackforMCAABFL(leadId,LendingConstants.BUSINESS_LENDING_V3, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,custId);
						LOGGER.info("BRE 2 Success with mock callback");
						
				     }	else {
				    	 responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
									LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				    	 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_COMPLETED.getStage());
				    	 LOGGER.info("BRE 2 Success without callback");
				     }
					
					 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_COMPLETED.getStage());
					
					 Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
					 Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"780");
					}
				
				  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC022_SecondBRECallback",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC023_FetchLead_AfterBRE2() throws JSONException
				    {
					  
					  for(int i=0;i<25;i++)
					  {
					   
					  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
					
					 
					  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
						  
						  
						  break;
					  
					  }
					  
					  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE3_REQUESTED.getStage()))
					  
					  {
						  
						  
						  lendingBaseClassObject. callbackWithOnlyWorkflowoperation(custId,LendingConstants.BUSINESS_LENDING_V3,"BRE3_SUCCESS");
						  
					  }
					  
					  
				   
				    }
				
				 @Test(description = "update kyc name for bank details", dependsOnMethods = "TC023_FetchLead_AfterBRE2", groups = { "Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC024_UpdateKYCNameInSAI() {
					Map<String, String> queryParams = new HashMap<String, String>();
					  queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
			     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
					
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
					
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("workflowMovementRequired", "false");
					body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
					
					String requestPath="MerchantService/v2/lending/dataUpdate/UpdateKYCNameForBankRequest.json";
					
					Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestPath);
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					
					
					  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					
					 
					  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
				
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
					
					  System.out.println(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"));
				
					
					}
				 @Test(description = "Enter the bank details", dependsOnMethods = "TC024_UpdateKYCNameInSAI", groups = {
					"Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC025_SaveBankDetails() {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
		
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("session_token", sessionToken);
				headers.put("Content-Type", "application/json");
				
				randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowOperation", "VERIFY_BANK_DETAILS");
				body.put("EMANDATE_TYPE", "Internet Banking");
				body.put("bankName", "PAYTM BANK");
				body.put("bankAccountNumber",randomBankAccountNumber);
				body.put("ifsc", "PYTM0123456");
				body.put("bankAccountHolderName", "testNameMatch");
				
				Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
				
				if (responseObject.getStatusCode() == 200) {
				
					Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");
				
				}
				
				else {
					
					for (int i = 1; i < 6; i++) {
						LOGGER.info("Again hitting with same data: retry-count: " + i);
						responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
						
						if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
							break;
					}
				
				}
				
			
					Assert.assertEquals(responseObject.getStatusCode(),200);
					
				
				}
			 
				@Test(description = "For ABFL lead Bank Verification", dependsOnMethods = "TC025_SaveBankDetails",groups = {"Regression"})
				@Owner(emailId = "<EMAIL>")
				public void TC026_FetchLeadPostBankVerification() {
					for(int i=0;i<5;i++) {
					responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
							LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
						if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
							break;
						}
					}
					LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
				    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
				    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"PAYTM BANK");
				    //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankA"),randomBankAccountNumber);
				    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");
				   
				    
				    
				}	 
				
				  @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC026_FetchLeadPostBankVerification")
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC027_GenerateLoanAgreement()
				    {
				 Map<String,String> queryParams=new HashMap<String,String>();
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
				 queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
				 queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
				
				   				 			  
				  Map<String,String> headers=new HashMap<String,String>();
				  headers.put("session_token",sessionToken);
		     
			   
			  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			  	
			 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
			 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
			 	{
			 	
			    code=responseObject.jsonPath().getString("data.state.code");
			    tncName=responseObject.jsonPath().getString("data.state.tncName");
			    url=responseObject.jsonPath().getString("data.state.url");
			    uniqueIdentifier=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
			    md5=responseObject.jsonPath().getString("data.state.md5");
			 	}
			 	
				    }
		 	
				  @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC027_GenerateLoanAgreement")
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC028_GenerateSanctionLetter()
				    {
				 Map<String,String> queryParams=new HashMap<String,String>();
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
				 queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
				 queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
				 queryParams.put("tncType", "LOAN_SANCTION_TNC");
				   				 			  
				  Map<String,String> headers=new HashMap<String,String>();
				  headers.put("session_token",sessionToken);
		     
			   
			  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			  	
			 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
			 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
			 	{
			 	
			 	codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
			 	tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
			 	urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
			 	uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
			 	md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
			 	}
				  
			 	
				    }
				  
				
				  @Test(description = "Accept Loan Agreement",dependsOnMethods = "TC028_GenerateSanctionLetter",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC029_AcceptLoanAgreement()
				    {
					  Map<String,String> queryParams=new HashMap<String,String>();
					  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
				      queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
				      queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
						   	
					  
			    	  Map<String,String> headers=new HashMap<String,String>();
				       headers = LendingBaseAPI.setHeadersReceivedFromFE();
				       headers.put("session_token", sessionToken);
				       headers.put("Content-Type", "application/json");
				        
				       Map<String,Object> body = new HashMap<String, Object>();
				  	   body.put("workflowOperation","LOAN_AGREEMENT_ACCEPTED");
				  	   body.put("LENDING_DYNAMIC_TNC", tncName);
				  	   body.put("KYB_TNC_REF_NO", uniqueIdentifier);
				  	   body.put("TNC_ACCEPTED_CODE", md5);
				  	   body.put("TNC_ACCEPTED_VERSION", 3);
				  	   body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
				  	   body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
				  	   body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
				  	   body.put("SECONDARY_ACCEPTED_TNC_VERSION", 2);
				  	   body.put("WAIT_FOR_EMANDATE", "TRUE");
				  
				  	  
					   
				  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/LoanAgreementAcceptWithsanctionRequest.json";
					 
					 for(int i=0;i<2;i++)
					 {
						 
						 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body,requestBodyJsonPath);
						 
						  if(responseObject.getStatusCode()==200)
						   break;
					 }
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"815");
					        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC"),tncNameSanctionLetter);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),tncName);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_AGREEMENT_ACCEPTED"),"TRUE");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_VERSION"),"3");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC_VERSION"),"2");
					     
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),tncNameSanctionLetter);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_CODE"),tncName);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WAIT_FOR_EMANDATE"),"TRUE");


					        lenderCustomerId=responseObject.jsonPath().getString("LENDER_CUSTOMER_ID");
					        sanctionLetterAcceptanceTimestamp=responseObject.jsonPath().getString("SANCTION_LETTER_TNC_ACCEPTANCE_TIMESTAMP");
					        kybSecondaryTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_SECONDARY_TNC_DISPLAY_URL");
					        loanAgreementDate=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AGREEMENT_DATE");
					        kybTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_TNC_DISPLAY_URL");
					     
					    
					    
					      }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
					  
				    }		
				  
				  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC029_AcceptLoanAgreement", groups = { "Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC030_UpdateActualPanInSAI() {
					Map<String, String> queryParams = new HashMap<String, String>();
					 queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
			     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
			    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
					
					
					randomGeneratedPan=UtilitiesObject.randomIndividualPANValueGenerator();
					
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
					
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("workflowMovementRequired", "false");
					body.put("F_NAME", "BHAIRAVI");
					body.put("L_NAME", "LATASREE");
					body.put("GENDER", "FEMALE");
					body.put("PINCODE", "600024");
					body.put("PAN", Pan);
					body.put("DOB", "1979-10-05");
					body.put("EMAIL", "<EMAIL>");
					
				    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.EMANDATE_REQUIRED.getStage());

					
					}
				  
				  @Test(description = "Verify ABFL Lead Emandate Callback", dependsOnMethods = "TC030_UpdateActualPanInSAI", groups = {
					"Regression" })
					@Owner(emailId =  "<EMAIL>")
					public void TC031_EmandateCallback() throws InterruptedException {
						Map<String, String> queryParams = new HashMap<String, String>();
						queryParams.put("leadId", leadId);
						queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
					
						token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
					
						Map<String, String> headers = new HashMap<String, String>();
						headers.put("Authorization", token);
						headers.put("Content-Type", "application/json");
						headers.put("custId", custId);
					
						Map<String, Object> body = new HashMap<String, Object>();
						body.put("status", "EMANDATE_SUCCESS");
					
						Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
					
						lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					
						Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
					
					}
					
					@Test(description = "Emandate Callback Stage Verification", dependsOnMethods = "TC031_EmandateCallback",groups = {"Regression"})
					@Owner(emailId = "<EMAIL>")
					public void TC032_FetchLeadPostEmandate() {
						responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
								LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
						LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
						
						Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
					    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_IN_PROGRESS.getStage());
					    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
					}
					
					
					  @Test(description = "If PDC Callback is not received manually hit callback",dependsOnMethods = "TC032_FetchLeadPostEmandate",groups = {"Regression"})
					  @Owner(emailId = "<EMAIL>",isAutomated = true)
					    public void TC033_VerifyPDCCallback() throws JSONException
					    {
						  
						  Map<String,String> queryParams=new HashMap<String,String>();
							
						  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						  
				    	   Map<String,String> headers=new HashMap<String,String>();
				    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
					       headers = LendingBaseAPI.setHeadersReceivedFromFE();
					       headers.put("Authorization", token);
					       headers.put("Content-Type", "application/json");
					       headers.put("custId", custId);
					       
					       Map<String,Object> body=new HashMap<String,Object>();
					  	 
					  	 for(int i=0;i<10;i++)
						 {
							 
					  		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
							 
					  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_PENDING.getStage()))
							   break;
						 } 
					  	 
					  	 
					  
						 //PDC Callback
					  	 
					   
				  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage()))
				  		{
				  	       body.put("workflowOperation","LOAN_APPLICATION_ACCEPTED");
					  	   body.put("PDC_REASON_ID", "7");
					  	   body.put("PDC_USER_MESSAGE", "Disbursal Done");
					  	   body.put("BUSINESS_STATUS", "APPROVED_BY_LMS");
					  	   body.put("PDC_SYSTEM_MESSAGE", "Direct Approval of Loan Account is enabled");
					  	   
					  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PDCLoanApplicationPendingCallbackRequest.json";   
				    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
						
				  			
				  		}
				  		
				  		 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
				  		
					    }
					  
					
					@Test(description = "Update lead details",dependsOnMethods = "TC033_VerifyPDCCallback",groups = {"Regression"})
					@Owner(emailId = "<EMAIL>",isAutomated = true)
					  public void TC034_VerifyLeadStage_LMSSubmitApplicationJob()
					  {
						  
						  for(int i=0;i<20;i++)
						  {
						   
							  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
						
						 
						  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
							  break;
						  
						  }
						 
					
					   
					      Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
					      Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
						  Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"346");
				
			            }
					  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC034_VerifyLeadStage_LMSSubmitApplicationJob",groups = {"Regression"})
					  @Owner(emailId = "<EMAIL>",isAutomated = true)
					    public void TC035_LMSDataCallback() throws JSONException
					    {
						  
						  Map<String,String> queryParams=new HashMap<String,String>();
							
						  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						  
				    	   Map<String,String> headers=new HashMap<String,String>();
				    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_JWT_Secret);
					       headers = LendingBaseAPI.setHeadersReceivedFromFE();
					       headers.put("Authorization", token);
					       headers.put("Content-Type", "application/json");
					       headers.put("custId", custId);
					       
					       Map<String,Object> body=new HashMap<String,Object>(); 
					       for(int i=0;i<10;i++){
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
							 
					  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
							   break;
						 }
					     if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
					     {	
						       body.put("workflowOperation","LMS_APPLICATION_APPROVED");
						  	   body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_ABFL"+Utilities.randomLendingLoanAccountNumberGenerator());
						  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
						  	   body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
						  	   body.put("LENDER_LOAN_ACCOUNT_NUMBER", "DXW-M10126-*********");
						  	   
					    	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LMSSubmitApplicationCallbackRequest.json";   
					    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
							
							  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage()))
								  {
									LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
							        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
							        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
							        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
							        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
							        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
							        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
							        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"330");
							        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
							        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_CREATED_ON"),"*************");
								  }
					     }
					     
					     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
					    }  
					  
					  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC035_LMSDataCallback",groups = {"Regression"})
					  @Owner(emailId = "<EMAIL>",isAutomated = true)
					    public void TC036_FetchLeadAllData() throws JSONException
					    {
						  for(int i=0;i<25;i++)
						  {
						  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
						 
						  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
						  {
							  break;
						  }
						  
						  }	  
					     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
					    }
					  
							     	   
							
					      	
				  
}
