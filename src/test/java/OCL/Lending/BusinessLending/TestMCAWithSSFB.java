package OCL.Lending.BusinessLending;

import Request.MerchantService.v1.TokenXMV;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.opencsv.CSVWriter;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigInteger;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestMCAWithSSFB extends LendingBaseAPI {

	private static final Logger LOGGER = LogManager.getLogger(TestMCAWithSSFB.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();

	String sessionToken = "";
	String leadId = "";
	String custId = "";
	String agentNumber = "7773330157";
	String agentPassword = "paytm@123";
	String token = "";
	String uuid = "";
	String ckycStage = "";
	String loanOffered = "";
	String maxLoanAmount = "";
	String authorisedMonthlyLimit = "";
	String stage = "";
	String code = "";
	String tncName = "";
	String url = "";
	String uniqueIdentifier = "";
	String md5 = "";
	String codeSanctionLetter = "";
	String tncNameSanctionLetter = "";
	String urlSanctionLetter = "";
	String uniqueIdentifierSanctionLetter = "";
	String md5SanctionLetter = "";
	String LMS_SECRET = "fd61f785-e867-471b-90c6-1447b4331712";
	String minLoanAmount="";
	String maxTenure="";
	String minTenure="";
	String rateOfInterest="";

	public static final String SOLUTION = "business_lending";
	public static final String SOLUTION_TYPE_LEVEL_2 = "SSFB";
	public static final String SOLUTION_TYPE_LEVEL_3 = "Unsecured_Short_term_Loan_Simplified";
	public static final String ENTITY_TYPE = "INDIVIDUAL";
	public static final String CHANNEL = "DIY_P4B_APP";
	public static final String PAN = "**********";
	public static final String DOB = "1989-04-21";
	public static final String EMAIL = "<EMAIL>";
	public static final String ISSUER = "OE";
	public static final String CLIENT_ID = "LMS";
	public static final String WORKFLOW_VERSION = "V2";
	Map<String, String> commonHeaders;
	

	@BeforeClass()
	public void intitializeInputData() throws IOException {

		LOGGER.info(" Before Suite Method for Agent Login ");
		sessionToken = ApplicantToken(agentNumber, agentPassword);
		LOGGER.info("Applicant Token for Lending : " + sessionToken);
		commonHeaders = setcommonHeaders();

	}

	@Test(description = "Delete all existing leads of the number", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC001_DeleteExistingLead_ForSSFB() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("mobile", agentNumber);

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683");

		lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
	}

	@Test(description = "Verify whether there is any existing lead present or not", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC002_FetchLeadDeatils() {
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.LEAD_NOT_PRESENT.getStage());

		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");
	}

	@Test(description = "Create MCA Lead with lender SSFB", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC003_CreateSSFBLead() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("solution", SOLUTION);
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		queryParams.put("channel", CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("tnCSetName", "loanStaticTnc");
		body.put("WORKFLOW_VERSION", WORKFLOW_VERSION);
		body.put("PRODUCT_TYPE", "MCA");
		body.put("PRODUCT_VERSION", "1");
		body.put("PRODUCT_ID", "67");
		body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
		body.put("LOAN_INTEREST_AMOUNT", "500");
		body.put("LOAN_AMOUNT_IN_WORDS", "Ten thousand Only");
		body.put("LOAN_MIN_AMOUNT", "10000");
		body.put("LOAN_MAX_AMOUNT", "125000");
		body.put("LOAN_TENURE", "720");
		body.put("LOAN_TENURE_MIN", "90");
		body.put("LOAN_TENURE_MAX", "720");
		body.put("LOAN_TENURE_UNIT", "DAY");
		body.put("LOAN_RATE_OF_INTEREST", "20");
		body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
		body.put("LOAN_PROCESSING_FEE", "100.0");
		body.put("LOAN_INCENTIVE", "5000");
		body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
		body.put("LOAN_INCENTIVE_PERCENTAGE", "0");
		body.put("MERCHANT_ID", "pXXIQZ34665245852304");
		body.put("USER_IP_ADDRESS", "*************");
		body.put("BASE_ID", "mca_SSFB_1000856875_0d265c01");
		body.put("LOAN_OFFER_ID", "mca_SSFB_1000856875_0d265c01_NEW_OFFER_MCA_e6c667c");
		body.put("RISK_GRADE", "MCA|BAB20");
		body.put("PROCESSING_FEE_RATE", 1.0);
		body.put("IS_ACCEPTANCE_ABOVE_5000", true);
		body.put("IS_SI_MANDATORY", true);
		body.put("IS_RESTRICTED_MERCHANT", true);
		body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
		body.put("IS_EMANDATE_ELIGIBLE", true);
		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
		body.put("LENDING_DYNAMIC_TNC", "bl_loan_agreement_ssfb");
		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_ssfb");

		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,SOLUTION_TYPE_LEVEL_2);
		if (responseObject.getStatusCode() == 200) {
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
			leadId = responseObject.jsonPath().getString("leadId");
		}

		else {
			LOGGER.info("Try to hit the API again");
			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,SOLUTION_TYPE_LEVEL_2);
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
			leadId = responseObject.jsonPath().getString("leadId");
		}

	}

	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC003_CreateSSFBLead", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC004_FetchTheCreatedLeadDeatils() {

		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.LEAD_CREATED.getStage());
		custId = responseObject.jsonPath().getString("custId");
				
	}

	@Test(description = "Verify the callback for PPBL OTP", dependsOnMethods = "TC003_CreateSSFBLead", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC005_PPBLOTPCallback() throws InterruptedException {

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", SOLUTION);

		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);

		Map<String, String> headers = new HashMap<String, String>();
		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "DIY_P4B_APP");

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "PPBL_TNC_VERIFIED");
		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");

		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
	
		verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

	}
	
		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC005_PPBLOTPCallback", groups = {
		"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC006_FetchTheCreatedLeadDeatils() {
	
	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
			sessionToken, LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
	custId = responseObject.jsonPath().getString("custId");
			
	}

	@Test(description = "Add Basic details", dependsOnMethods = "TC005_PPBLOTPCallback", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC007_AddBasicDetails() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("solution", SOLUTION);
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("channel", CHANNEL);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowSubOperation", "BASIC_DETAILS");
		body.put("DOB", DOB);
		body.put("PAN", PAN);
		body.put("EMAIL", EMAIL);

		Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);

		verifyResponseCodeAs200OK(responseObject);

		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
		Assert.assertEquals(responseObject.jsonPath().getBoolean("isLeadAlreadyExists"), true);

	}
		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC007_AddBasicDetails", groups = {
		"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC008_FetchTheCreatedLeadDeatils() {
	
	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
			sessionToken, LendingLeadStages.BASIC_DETAILS.getStage());
	custId = responseObject.jsonPath().getString("custId");
			
	}
	@Test(description = "Update Pan and dob details", dependsOnMethods = "TC007_AddBasicDetails", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC009_UpdatePANAndDOBDetails() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", SOLUTION);

		Map<String, String> headers = new HashMap<String, String>();
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
		body.put("status", "SUCCESS");
		body.put("DOB_VALIDATION_STATUS", "TRUE");
		body.put("VALIDATED_DOB", "1988-12-18");
		body.put("VALIDATED_PAN", PAN);
		body.put("F_NAME", "TOUCH");
		body.put("M_NAME", "WOOD");
		body.put("L_NAME", "LIMITED");
		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");

		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);

		verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());

	}

	@Test(description = "Update ", dependsOnMethods = "TC009_UpdatePANAndDOBDetails", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC010_UpdateGenderAndPincodeDetails() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", SOLUTION);

		Map<String, String> headers = new HashMap<String, String>();
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
		body.put("status", "SUCCESS");
		body.put("BRE_INPUT_GENDER", "Female");
		body.put("BRE_INPUT_PINCODE", "243001");

		Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);

		verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());

	}

	@Test(description = "BRE OTP Verification", dependsOnMethods = "TC010_UpdateGenderAndPincodeDetails", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC011_BREOTPVerification() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", SOLUTION);

		Map<String, String> headers = new HashMap<String, String>();
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		headers.put("channel", "DIY_P4B_APP");

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "BRE_OTP_VERIFIED");
		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");

		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);

		verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

	}
	
		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC011_BREOTPVerification", groups = {
		"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC012_FetchTheCreatedLeadDeatils() {
	
	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
			sessionToken, LendingLeadStages.BRE_OTP_VERIFIED.getStage());
	custId = responseObject.jsonPath().getString("custId");
			
	}

	@Test(description = "BRE Validation Pending", dependsOnMethods = "TC011_BREOTPVerification", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC013_BREValidationPending() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", SOLUTION);

		Map<String, String> headers = new HashMap<String, String>();
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		headers.put("channel", "DIY_P4B_APP");

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "BRE_VALIDATION_PENDING");
		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");

		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);

		verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

	}

	@Test(description = "Verify Lead stage", dependsOnMethods = "TC013_BREValidationPending",groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC014_VerifyLeadStage() {

		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.BRE_VALIDATION_PENDING.getStage());

	}

	@Test(description = "Fetch BRE Response", dependsOnMethods = "TC013_BREValidationPending", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC015_FetchBREResponse() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("channel", CHANNEL);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
	

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");

		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);

		verifyResponseCodeAs200OK(responseObject);

	}

	@Test(description = "Verify Lead stage", dependsOnMethods = "TC015_FetchBREResponse", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC016_VerifyLeadStage() {

		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.BRE_RESPONSE_AWAITED.getStage());

	}

	@Test(description = "Check BRE Response", dependsOnMethods = "TC015_FetchBREResponse", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC017_CheckBREResponse() throws SQLException {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("channel", CHANNEL);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");

		Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);

		LOGGER.info("Status Code : " + responseObject.getStatusCode());

	// If response of BRE status API is BRE success then get loan amount
		
		for(int i=0;i<10;i++)
			
		{
			responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			if (responseObject.getStatusCode() == 200
					&& responseObject.jsonPath().getString("stage").equals("BRE_SUCCESS"))
			{
			
				stage = responseObject.jsonPath().getString("stage");
				loanOffered = responseObject.jsonPath().getString("loanOffered");
				maxLoanAmount = responseObject.jsonPath().getString("maxLoanAmount");
		
				
				break;
			
			}
			
			
		}
		
		//If response is not BRE success there can be two cases
		// 1.Response can be BRE ERROR in that case move the node back to BRE Response awaited
					// and hit callback API and move lead to BRE success
	if (responseObject.getStatusCode() == 200
			&& responseObject.jsonPath().getString("stage").equals("BRE_ERROR")||responseObject.getStatusCode() == 200
					&& responseObject.jsonPath().getString("stage").equals("NOTIFICATION_SUCCESS") )
				{
					int updatedRows = 0;
					String currentActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("167", leadId);
					LOGGER.info("Current Active Node is : " + currentActiveworkflowStatusId);
			
					updatedRows = runUpdateQueryOnWorkflowStatus(0, new BigInteger(currentActiveworkflowStatusId));
					Assert.assertEquals(updatedRows, 1);
			
					String previousActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("366", leadId);
					updatedRows = runUpdateQueryOnWorkflowStatus(1, new BigInteger(previousActiveworkflowStatusId));
					LOGGER.info("Updated Active Node is : " + previousActiveworkflowStatusId);
					Assert.assertEquals(updatedRows, 1);
					
					
					BRECallback();
			
				}
	


	
		// 2.Response can be unable to fetch the details , in this case lead will still be at BRE REsponse awaited
		//  so hit the callback API and move lead to BRE success
	
			else {
		     
				if(responseObject.getStatusCode() == 500
						&&responseObject.jsonPath().getString("displayMessage").equals("We could not fetch your details. Please try again later.")||responseObject.getStatusCode() == 200
								&& responseObject.jsonPath().getString("stage").equals("BRE_RESPONSE_AWAITED"))
					BRECallback();
				
	
		        }
					
			
	}
	
	
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void BRECallback() {
	
		LOGGER.info("Using Callback API");
		
		Map<String, String> queryParams = new HashMap<String, String>();
		
		queryParams.put("solution", SOLUTION);
		queryParams.put("leadId", leadId);
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
	
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("custId", custId);
		headers.put("Content-Type", "application/json;charset=utf-8");
	
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("statusMessage", "BRE_SUCCESS");
		body.put("status", "SUCCESS");
		body.put("creditScore", "780.3");
		body.put("lastFetchDate", "1606694400000");
		body.put("bureau", "CIBIL");
		body.put("loanOffered", "true");
		body.put("newOfferGenerated", "false");
		body.put("rejectionReason", "Rejection reason");
		body.put("baseId", "test_ssfb_99901211");
		body.put("riskGrade", "H");
		body.put("processingFeeRate", "1.0");
		body.put("isAcceptanceAbove5000", true);
		body.put("isSiMandatory", true);
		body.put("isRestrictedMerchant", false);
		body.put("isPaytmVintageOlderThan90d", true);
		body.put("minLoanAmount", "10000");
		body.put("maxLoanAmount", "500000");
		body.put("maxTenure", 180);
		body.put("minTenure", 90);
		body.put("rateOfInterest", 36);
		body.put("fieldInvestigationNeeded", true);
		body.put("isHomeFiNeeded", true);
	
		Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		if (responseObject.getStatusCode() == 200)
				{
					stage = responseObject.jsonPath().getString("oeStage");
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
				
			
				
				}
	
	}
	
	
	@Test(description = "Verify Lead stage", dependsOnMethods = "TC017_CheckBREResponse", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC018_VerifyLeadStage() {

		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.BRE_SUCCESS.getStage());

	}
	
			@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC017_CheckBREResponse", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC019_CheckCKYCStatus() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("solution", SOLUTION);
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		queryParams.put("channel", CHANNEL);
		queryParams.put("dob", DOB);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");
		
		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
		
		verifyResponseCodeAs200OK(responseObject);
		ckycStage = responseObject.jsonPath().getString("stage");
		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
		
		}
		
		@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC019_CheckCKYCStatus", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC020_VerifyLeadStage() {
		
		lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL, sessionToken, ckycStage);
		
		}
		
		@Test(description = "Upload selfie", dependsOnMethods = "TC019_CheckCKYCStatus", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC021_UploadSelfie() throws InterruptedException {
		
		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
				"INDIVIDUAL", SOLUTION, SOLUTION_TYPE_LEVEL_2, sessionToken);
		
		verifyResponseCodeAs200OK(responseObject);
		
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
		uuid = responseObject.jsonPath().getString("uuid");
		
		Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
		LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
		Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
		Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
		
		}
		
		@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC021_UploadSelfie", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC022_VerifyUploadedDocument() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, ckycStage);
		Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docType"), "selfie");
		Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docProvided"), "selfie");
		Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docUUId"), uuid);
		
		}
		
		@Test(description = "Upload Customer Photo", dependsOnMethods = "TC021_UploadSelfie", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC023_UploadCustomerPhoto() throws InterruptedException {
		
		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
				"INDIVIDUAL", SOLUTION, SOLUTION_TYPE_LEVEL_2, sessionToken);
		
		verifyResponseCodeAs200OK(responseObject);
		
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
		uuid = responseObject.jsonPath().getString("uuid");
		
		Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
		LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
		Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
		Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
		
		}
		
		@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC023_UploadCustomerPhoto", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC024_VerifyUploadedDocument() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, ckycStage);
		List<Object> docTypes = responseObject.jsonPath().getList("solutionDocSRO.docType");
		docTypes.contains("customerPhoto");
		List<Object> docProvided = responseObject.jsonPath().getList("solutionDocSRO.docProvided");
		docProvided.contains("others");
		
		}
		
		@Test(description = "CKYC Callback", dependsOnMethods = "TC024_VerifyUploadedDocument", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC025_CKYCCallback() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("leadId", leadId);
		
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("statusMessage", "CKYC_VERIFIED");
		body.put("status", "SUCCESS");
		body.put("cKycId", "4353435454356");
		body.put("firstName", "TOUCH");
		body.put("middleName", "WOOD");
		body.put("thirdName", "LIMITED");
		body.put("email", "<EMAIL>");
		body.put("type", "SELFIE");
		body.put("percentage", "100");
		body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
		body.put("addressline2", "MAYUR VIHAR PHASE 1");
		body.put("city", "DELHI");
		body.put("state", "EAST DELHI");
		body.put("pincode", "110091");
		body.put("dob", DOB);
		body.put("gender", "male");
		body.put("pan", PAN);
		body.put("ckycSuccessMode", "OFFLINE_AADHAR");
		
		Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
		
		verifyResponseCodeAs200OK(responseObject);
		
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PAN_VERIFIED.getStage());
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC025_CKYCCallback", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC026_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.PAN_VERIFIED.getStage());
		
		LOGGER.info("Verify that detials are present in userAdditionalInfo");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
		LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
		Assert.assertEquals(
				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),
				"FALSE");
		Assert.assertEquals(
				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),
				"FALSE");
		
		}
		
		
		
		//data update call as lender is doing bureau pull /hard pull only on few numbers
		
		 @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC026_VerifyLeadStage", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC027_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", SOLUTION);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("PAN", "**********");
			body.put("GENDER", "MALE");
			body.put("PINCODE", "600024");
			body.put("PAN", "**********");
			body.put("DOB", "1989-04-21");
			body.put("EMAIL", "<EMAIL>");
			
		   Response responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PAN_VERIFIED.getStage());
			
			}
		 
		
			@Test(description = "Fetch BRE 2 Response", dependsOnMethods = "TC027_UpdateBureauDataSetInSAI", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC028_FetchBRE2Response() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("channel", CHANNEL);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		queryParams.put("action", "STAGE2");
	
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");
		
		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
		
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		Assert.assertEquals(responseObject.jsonPath().getString("status"),"In progress");
		Assert.assertEquals(responseObject.jsonPath().getString("action"),"STAGE2");
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC028_FetchBRE2Response", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC029_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.BRE2_RESPONSE_AWAITED.getStage());
		
		}
		
		@Test(description = "Check BRE 2 Response", dependsOnMethods = "TC029_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC030_CheckBRE2Response() throws SQLException {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("channel", CHANNEL);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		queryParams.put("action", "STAGE2");
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");
		
		
		Response responseObject = null;
		
		// If response of BRE status API is BRE success then get loan amount
		
		for(int i=0;i<20;i++)
			
		{
			responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals("BRE2_SUCCESS")) 
					
			{
			
				stage = responseObject.jsonPath().getString("stage");
				loanOffered = responseObject.jsonPath().getString("loanOffered");
				maxLoanAmount = responseObject.jsonPath().getString("maxLoanAmount");
				minLoanAmount = responseObject.jsonPath().getString("minLoanAmount");
				maxTenure = responseObject.jsonPath().getString("maxTenure");
				minTenure = responseObject.jsonPath().getString("minTenure");
				rateOfInterest = responseObject.jsonPath().getString("rateOfInterest");
				Assert.assertEquals(responseObject.jsonPath().getBoolean("newOfferGenerated"), false);
			
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_SUCCESS.getStage());
				break;
			
			}
			
			LOGGER.info("Stage received in BRE response"+ responseObject.jsonPath().getString("stage"));
			
			
			
		}
		
	
	// If response of BRE status API is UPDATE_LOAN_OFFER then verify new offer generated flag
	// and hit update loan offer API
	if (responseObject.getStatusCode() == 200
			&& responseObject.jsonPath().getString("stage").equals("BRE2_UPDATE_LOAN_OFFER")) 
	{
	
		Assert.assertEquals(responseObject.jsonPath().getString("newOfferGenerated"), "true");
		UpdateLoanOffer();
	
	}
	
	// IF response is not BRE success/Update loan offer then their can be two cases
	else {
	
		// 1.Response can be BRE ERROR in that case move the node back to BRE Response awaited
		// and hit callback API and move lead to BRE success
		if (responseObject.getStatusCode() == 200
				&& responseObject.jsonPath().getString("stage").equals("BRE2_ERROR"))
					{
						int updatedRows = 0;
						String currentActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("714", leadId);
						LOGGER.info("Current Active Node is : " + currentActiveworkflowStatusId);
				
						updatedRows = runUpdateQueryOnWorkflowStatus(0, new BigInteger(currentActiveworkflowStatusId));
						Assert.assertEquals(updatedRows, 1);
				
						String previousActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("566", leadId);
						updatedRows = runUpdateQueryOnWorkflowStatus(1, new BigInteger(previousActiveworkflowStatusId));
						LOGGER.info("Updated Active Node is : " + previousActiveworkflowStatusId);
						Assert.assertEquals(updatedRows, 1);
						
						
						BRE2Callback();
				
					}
		

	
	
		// 2.Response can be 500 : unable to fetch the details or 200: BRE Response awaited , in this case lead will still be at BRE REsponse awaited
		//  so hit the callback API and move lead to BRE success
	
			else {
		     
			
				BRE2Callback();
				
	
		        }
		
		   }
	
	}
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void UpdateLoanOffer() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("channel", CHANNEL);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowSubOperation", "UPDATE_LOAN_OFFER");
		body.put("LOAN_AMOUNT_IN_NUMBER", "50000");
		body.put("LOAN_TENURE", "271");
		body.put("LOAN_RATE_OF_INTEREST", "46");
		body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "11121");
		body.put("LOAN_PROCESSING_FEE", "301");
		body.put("LOAN_INTEREST_AMOUNT", "1231");
		body.put("LOAN_AMOUNT_IN_WORDS", "Fifty Thousand");
		body.put("BASE_ID", "1107227275_test");
		body.put("LOAN_MIN_AMOUNT", "10000.0");
		body.put("LOAN_MAX_AMOUNT", "200000.0");
		body.put("LOAN_TENURE_MIN", "91");
		body.put("LOAN_TENURE_MAX", "271");
		body.put("RISK_GRADE", "H");
		body.put("IS_ACCEPTANCE_ABOVE_5000", false);
		body.put("PROCESSING_FEE_RATE", "6.0");
		body.put("IS_SI_MANDATORY", false);
		body.put("IS_RESTRICTED_MERCHANT", false);
		body.put("IS_EMANDATE_ELIGIBLE", false);
		body.put("LOAN_INCENTIVE", "5000");
		body.put("LOAN_INCENTIVE_ELIGIBLE", false);
		body.put("LOAN_INCENTIVE_PERCENTAGE", "0.2");
		body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
		
		Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers, body);
		
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
		Assert.assertEquals(responseObject.jsonPath().getBoolean("isLeadAlreadyExists"), true);
		
		}
		
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void BRE2Callback() {
		
			LOGGER.info("Using Callback API");
			
			Map<String, String> queryParams = new HashMap<String, String>();
			
			queryParams.put("solution", SOLUTION);
			queryParams.put("leadId", leadId);
			token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			headers.put("Content-Type", "application/json;charset=utf-8");
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "BRE2_SUCCESS");
			body.put("status", "SUCCESS");
			body.put("creditScore", "799.0");
			body.put("lastFetchDate", "1645142400000");
			body.put("bureau", "CIBIL");
			body.put("loanOffered", "true");
			body.put("newOfferGenerated", "false");
			body.put("rejectionReason", "Rejection reason");
			body.put("baseId", "MCA_shivangi_automation_1_034190f1");
			body.put("riskGrade", "H");
			body.put("processingFeeRate", "1.0");
			body.put("isAcceptanceAbove5000", true);
			body.put("isSiMandatory", true);
			body.put("isRestrictedMerchant", false);
			body.put("isPaytmVintageOlderThan90d", true);
			body.put("minLoanAmount", "10000");
			body.put("maxLoanAmount", "50000");
			body.put("maxTenure", 720);
			body.put("minTenure", 720);
			body.put("rateOfInterest", 46);
			body.put("fieldInvestigationNeeded", true);
			body.put("isHomeFiNeeded", true);
			body.put("action", "STAGE2");
			
			String requestJSON="MerchantService/V2/lending/dataUpdate/MCABRE2CallbackRequest.json";
		
			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body, requestJSON);
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.getStatusCode() == 200)
					{
						stage = responseObject.jsonPath().getString("oeStage");
						Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
					
				
					
					}
		
		}
		
		@Test(description = "Add addtional details to SSFB Lead", dependsOnMethods = "TC030_CheckBRE2Response", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC031_AdditionalDataCaptureCallback() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("leadId", leadId);
		
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		headers.put("channel", CHANNEL);

		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "ADDITIONAL_DATA_CAPTURED");
		body.put("RELIGION_KEY", "26");
		body.put("RELIGION", "Hindu");
		body.put("GENDER_KEY", "19");
		body.put("GENDER", "Male");
		body.put("MARITAL_STATUS_KEY", "22");
		body.put("MARITAL_STATUS", "SINGLE");
		body.put("SPOUSE_NAME", "test abcd");
		body.put("FATHER_NAME", "Vinay giri");
		body.put("MOTHER_NAME", "Archana giri goswami");
		
		Response responseObject = lendingBaseClassObject.additionalDataCaptureCallback(queryParams, headers, body);
		
		verifyResponseCodeAs200OK(responseObject);
		
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC031_AdditionalDataCaptureCallback", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC032_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
		LOGGER.info("Verify that detials are present in userAdditionalInfo");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.GENDER_KEY"), "19");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.RELIGION_KEY"), "26");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MARITAL_STATUS_KEY"), "22");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.VALIDATED_DOB"), "1988-12-18");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MARITAL_STATUS"), "SINGLE");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.SPOUSE_NAME"), "test abcd");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.RELIGION"), "Hindu");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.GENDER"), "Male");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.FATHER_NAME"), "Vinay giri");
		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MOTHER_NAME"), "Archana giri goswami");
		
		}
		
		@Test(description = "CKYC name update in SAI", dependsOnMethods = "TC032_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC033_UpdateKYCNameInSAI() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("leadId", leadId);
		
		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
		body.put("status", "SUCCESS");
		body.put("CKYC_NAME", LendingConstants.CKYC_NAME);
		
		Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);
		
		verifyResponseCodeAs200OK(responseObject);
		
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
		
		}
		
		@Test(description = "Enter the bank details", dependsOnMethods = "TC033_UpdateKYCNameInSAI", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC034_SaveBankDetails() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", SOLUTION);
		queryParams.put("entityType", "INDIVIDUAL");
		queryParams.put("channel", CHANNEL);
		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
		queryParams.put("solutionTypeLevel3", SOLUTION_TYPE_LEVEL_3);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json");
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("bankName", "PAYTM BANK");
		body.put("bankAccountNumber", "************");
		body.put("ifsc", "PYTM0123456");
		body.put("bankAccountHolderName", "Shivangi Goswami");
		
		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
		
		if (responseObject.getStatusCode() == 200) {
		
			Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
		
		}
		
		else {
			
			for (int i = 1; i < 4; i++) {
				LOGGER.info("Again hitting with same data: retry-count: " + i);
				responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
				
				if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
					break;
			}
		
		}
		
	
			Assert.assertEquals(responseObject.getStatusCode(),200);
			
		
		}
		
		@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC034_SaveBankDetails", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC035_FetchDynamicTnc() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);

			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.jsonPath().getString("meta.status").equals("success")) {

				code = responseObject.jsonPath().getString("data.state.code");
				tncName = responseObject.jsonPath().getString("data.state.tncName");
				url = responseObject.jsonPath().getString("data.state.url");
				uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
				md5 = responseObject.jsonPath().getString("data.state.md5");
			}
			
			else
			{

				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
			}

		}

		@Test(description = "Fetch Dynamic T and C Sanction Letter", dependsOnMethods = "TC035_FetchDynamicTnc", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC036_FetchDynamicTncSanctionLetter() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
			queryParams.put("tncType", "LOAN_SANCTION_TNC");

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);

			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.jsonPath().getString("meta.status").equals("success")) {

				codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
				tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
				urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
				uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
				md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
			}
			
		}
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC036_FetchDynamicTncSanctionLetter", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC037_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.BANKING_ACTION_DONE.getStage());
		
		}
		
		@Test(description = "Verify submit application", dependsOnMethods = "TC037_VerifyLeadStage", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC038_SubmitApplication() throws InterruptedException {
		
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sessionToken);
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("WAIT_FOR_EMANDATE", "TRUE");
			body.put("WORKFLOW_VERSION", WORKFLOW_VERSION);
			body.put("accept", 1);
			body.put("tncName", tncName);
			body.put("uniqueIdentifier", uniqueIdentifier);
			body.put("md5", md5);
		
			body.put("accept1", 1);
			body.put("tncName1", tncNameSanctionLetter);
		
			body.put("uniqueIdentifier1", uniqueIdentifierSanctionLetter);
			body.put("md51", md5SanctionLetter);
		
			Response responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body,"TRUE",WORKFLOW_VERSION);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
		
			{
				LOGGER.info("Try again");
				responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body);
				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
		
			}
		
			else if (responseObject.jsonPath().getString("meta.stage").contentEquals("LMS_POST")) {
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("meta.subStage"), "APPLICATION_PENDING");
				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
				Assert.assertEquals(responseObject.jsonPath().getString("data.state"), "Loan Application is Accepted");
		
			}
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC038_SubmitApplication", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC039_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
		
		}
		
		
		@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC039_VerifyLeadStage", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC040_EmandateCallback() throws InterruptedException {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
			queryParams.put("solution", SOLUTION);
		
			token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("status", "EMANDATE_SUCCESS");
		
			Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
		
			verifyResponseCodeAs200OK(responseObject);
		
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC040_EmandateCallback", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC041_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
				sessionToken, LendingLeadStages.EMANDATE_SUCCESS.getStage());
		
		}

		@Test(description = "Verify PDC Callback", dependsOnMethods = "TC041_VerifyLeadStage", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC042_PDCCallback() throws InterruptedException {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
			queryParams.put("solution", SOLUTION);
		
			token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId,LMS_SECRET);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			headers.put("channel", CHANNEL);
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("status", "LOAN_APPLICATION_PENDING");
			body.put("BUSINESS_STATUS", "PENDING_BY_LMS");
			body.put("LMS_PREDISBURSAL_MESSAGE", "Direct Approval of Loan Account is disabled");
			body.put("USER_MESSAGE", "Loan disbursal is accepted");
			body.put("LMS_REASON_ID", "0");
			body.put("SYSTEM_MESSAGE", "Direct Approval of Loan Account is rejected ");
			body.put("AML_SCORE", "10");
			
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
					sessionToken, LendingLeadStages.EMANDATE_SUCCESS.getStage());
			
			if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.EMANDATE_SUCCESS.getStage()))
				{
				responseObject = lendingBaseClassObject.pdcLoanApplicationPending(queryParams, headers, body);
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
				
				}
			
		}
		
				@Test(description = "Sheet upload from panel", dependsOnMethods = "TC042_PDCCallback", groups = {
				"Regression" })
		      @Owner(emailId = "<EMAIL>", isAutomated = true)
		     public void TC043_UploadSheetONPanel() throws InterruptedException, IOException {
		
			TokenXMV tokenXMW = new TokenXMV();
			Response responseObject = MiddlewareServicesObject.v1Token("7771216290", "paytm@123");
			String XMWToken = responseObject.getHeader("Set-Cookie").toString();
		
			System.out.println("XMW token is :" + XMWToken);
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Content-Type", "multipart/form-data");
			headers.put("Cookie", XMWToken);
		
			File fileUpload = new File(
					"src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/BusinessSampleFile.csv");
			FileWriter outputfile = new FileWriter(fileUpload);
			CSVWriter writer = new CSVWriter(outputfile);
			String[] header = { "Lead Id", "Business status ", "Rejection Reason\n" };
			writer.writeNext(header);
			String[] data1 = { leadId, "APPROVED", "N/A" };
			writer.writeNext(data1);
			writer.flush();
			writer.close();
		
			responseObject = lendingBaseClassObject.uploadSheetFromPanel(headers, fileUpload, "BUSINESS_STATUS");
			if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
					.contentEquals(" has been successfully uploaded")) {
				LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
		
			}
		
		}
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC043_UploadSheetONPanel", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC044_VerifyLeadStage() {
				
		

		     Response responseObject = null;
		
			 Map<String,String> queryParams=new HashMap<String,String>();
		        queryParams.put("entityType",ENTITY_TYPE );
		        queryParams.put("solution",SOLUTION);
		        queryParams.put("channel",CHANNEL);

		        Map<String,String> headers=new HashMap<String,String>();
		        headers.put("session_token",sessionToken);

		for(int i=0;i<25;i++)
		        {
		        responseObject= fetchLeadDetails(queryParams, headers);

			        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
			        {
			            LOGGER.info("Verify Lead Stage "+responseObject.jsonPath().getString("stage"));
			           break;
	
			        }
			           
		        
		        }
		
		  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());

			}
		

	
	/**
	 * Method to set headers which are used in lead creation request
	 * 
	 * @return
	 */
	public Map<String, String> setcommonHeaders() {

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");

		return headers;
	}

}
