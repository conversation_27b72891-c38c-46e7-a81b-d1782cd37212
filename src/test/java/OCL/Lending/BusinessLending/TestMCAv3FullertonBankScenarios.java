package OCL.Lending.BusinessLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestMCAv3FullertonBankScenarios extends BaseMethod {


        private static final Logger LOGGER = LogManager.getLogger(TestMCAv3FullertonBankScenarios.class);
        oAuthServices oAuthServicesObject = new oAuthServices();
        MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        Utilities UtilitiesObject = new Utilities();
        LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
        Utilities utility=new Utilities();

        String sessionToken = "";
        String leadId="";
        String custId="**********";
        String consumerNumber="**********";
        String consumerPassword="paytm@123";
        String token="";
        String stage="";
        String feStage="";
        String userIPAddress="";
        String loanUserLatitude="";
        String loanUserLongitude="";
        String tncAdditionalParam="";
        String staticTncAcceptanceTimeStamp="";
        String lenderCustomerId="";
        String requestBodyJsonPath="";
        String Pan="**********";
        String Email="";
        String DOB="";
        String applicationId="";
        String bureauRequest="";
        String bureauResponse="";
        String bureauCreditState="";
        String breCreditScore="";
        String PanValidationTimeStamp="";
        String uuid="";
        String md5="";
        String code="";
        String tncName="";
        String url="";
        String uniqueIdentifier="";
        ;
        String codeSanctionLetter="";
        String tncNameSanctionLetter="";
        String urlSanctionLetter="";
        String uniqueIdentifierSanctionLetter="";
        String md5SanctionLetter="";
        String sanctionLetterAcceptanceTimestamp="";
        String kybSecondaryTNCDisplayURL="";
        String loanAgreementDate="";
        String kybTNCDisplayURL="";
        String panNameMatchTimeStamp="";
        String panNameMatchPercentage="";
        String breLastFetchDate="";


        Response responseObject= null;
        private String lendingImageQCRequired;
        private String kycResponseTimeStamp;
        private String kycRequestId;
        private String kycId;
        private String lendingPanNameQCRequired;
        private String selfieMatchVendor;
        private String panValidationTimestamp;
        private String selfieMatchPercentage;
        private String stageLendingURL;



        @BeforeClass()
        @Parameters({"stagingLendingUrl"})
        public void intitializeInputData(String stagingLendingUrl) {
            stageLendingURL=stagingLendingUrl;
            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }



        @Test(description = "Verify whether there is any existing MCA fullerton lead present or not",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC001_FetchLeadDeatils()
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {


                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


            }

        }


        @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC002_DeleteExistingLead()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("leadId",leadId);
            queryParams.put("custId", custId);

            queryParams.put("leadId",leadId);



            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);


            lendingBaseClassObject.resetLendingLeads(queryParams, headers);
        }




        @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC003_CreateMCAFullertonDistributionPiramalLead()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
            queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custid", custId);
            headers.put("ipAddress", "************");

            Map<String,Object> body = new HashMap<String, Object>();
            body.put("workflowOperation","CREATE_LEAD");
            body.put("mobile", consumerNumber);
            body.put("PRODUCT_TYPE", "MCA");
            body.put("PRODUCT_VERSION", "1");
            body.put("PRODUCT_ID","85");
            body.put("LOAN_AMOUNT_IN_NUMBER", "20000");
            body.put("LOAN_INTEREST_AMOUNT", "6492");
            body.put("LOAN_AMOUNT_IN_WORDS", "Twenty Thousand");
            body.put("LOAN_MIN_AMOUNT", "15000");
            body.put("LOAN_MAX_AMOUNT", "1000000");
            body.put("LOAN_TENURE", "720");
            body.put("LOAN_TENURE_MIN", "720");
            body.put("LOAN_TENURE_MAX", "720");
            body.put("LOAN_TENURE_UNIT", "DAY");
            body.put("LOAN_RATE_OF_INTEREST","30");
            body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "37");
            body.put("LOAN_PROCESSING_FEE", "200");
            body.put("LOAN_INCENTIVE", "5000");
            body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
            body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
            body.put("MERCHANT_ID", "AFTkYT90127981618272");
            body.put("BASE_ID", "MCA_Shivangi_bank1_39ba09ca");
            body.put("LOAN_OFFER_ID", "MCA_Shivangi_bank1_39ba09ca");
            body.put("RISK_GRADE", "MCA|KRL6");
            body.put("PROCESSING_FEE_RATE", "1.0");
            body.put("IS_ACCEPTANCE_ABOVE_5000", true);
            body.put("IS_SI_MANDATORY", true);
            body.put("IS_RESTRICTED_MERCHANT", true);
            body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
            body.put("IS_EMANDATE_ELIGIBLE", true);
            body.put("STATIC_TNC_SETNAME", "loanstatictnc");
            body.put("LENDER_STATIC_TNC_SETNAME", "bl_ckyck_lender_consent_fullerton");
            body.put("LENDING_DYNAMIC_TNC", "bl_loan_agreement_fullerton");
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_fullerton");
            body.put("FLOW_TYPE", "RISK");
            body.put("LENDER_ID", "15");
            body.put("IS_ADDITIONAL_DATA_REQUIRED", "False");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/CreateLeadRequestMCAMigration.json";





            for(int i=0;i<2;i++)
            {

                responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

                if(responseObject.getStatusCode()==201)
                    break;
            }


            if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"85");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"20000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),"6492");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_WORDS"),"Twenty Thousand");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MIN_AMOUNT"),"15000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"1000000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"720");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"720");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"720");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"DAY");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"30");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"37");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE"),"5000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_ELIGIBLE"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_PERCENTAGE"),"4");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"AFTkYT90127981618272");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"MCA_Shivangi_bank1_39ba09ca");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"MCA_Shivangi_bank1_39ba09ca");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_GRADE"),"MCA|KRL6");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"1.0");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"loanstatictnc");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_fullerton");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"bl_loan_agreement_fullerton");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"bl_sanction_letter_fullerton");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"15");

                leadId=responseObject.jsonPath().getString("leadId");
                custId=responseObject.jsonPath().getString("custId");
                userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
                staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");



            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());

        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateMCAFullertonDistributionPiramalLead",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC004_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<25;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
                    break;

            }

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            }


            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());


        }

        @Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC005_UpdateLeadBasicDetails()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation","BASIC_DETAILS");
            body.put("DOB", LendingConstants.DOB_STASHFIN);
            body.put("PAN", Pan);
            body.put("EMAIL", Utilities.randomEmailGeneration());

            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/MCABasicDetailRequest.json";


            for(int i=0;i<2;i++)
            {

                responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

                if(responseObject.getStatusCode()==200)
                    break;
            }


            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);


                leadId=responseObject.jsonPath().getString("leadId");
                custId=responseObject.jsonPath().getString("custId");
                userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
                staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
                Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
                DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
                Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
                PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());




        }


        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_UpdateLeadBasicDetails",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC006_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<15;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
                    break;

            }

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

                lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");

            }


            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());


        }

        @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC007_UpdateBureauDataSetInSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
            body.put("status", "SUCCESS");
            body.put("F_NAME", "BHAIRAVI");
            body.put("L_NAME", "LATASREE");
            body.put("GENDER", "FEMALE");
            body.put("PINCODE", "600024");
            body.put("PAN", "**********");
            body.put("DOB", "1979-10-05");
            body.put("EMAIL", "<EMAIL>");

            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());

        }

        @Test(description = "Fetch CIR",dependsOnMethods = "TC007_UpdateBureauDataSetInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC008_FetchCIR()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            headers.put("session_token", sessionToken);
            headers.put("Content-Type", "application/json");

            Map<String,Object> body=new HashMap<String,Object>();



            for(int i=0;i<10;i++)
            {
                responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);


                if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))

                    break;

            }


            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");



            bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
            breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");

        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_FetchCIR",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC009_FetchLeadAllData() throws JSONException
        {
            for(int i=0;i<25;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
                    break;

            }
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))

            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");

                //Hit BRE1 Callback

                LOGGER.info("Callback not coming so hitting BRE1 Callback");

                responseObject=    lendingBaseClassObject. BRE1CallbackforMCA (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);


            }

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_SUCCESS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
            }



            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
        }

        @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC009_FetchLeadAllData", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC010_UpdateExistingDetailsInSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
            body.put("status", "SUCCESS");
            body.put("F_NAME", "TOUCH");
            body.put("L_NAME", "LIMITED");
            body.put("GENDER", "FEMALE");
            body.put("PINCODE", "110096");
            body.put("PAN", Pan);
            body.put("DOB", LendingConstants.DOB_STASHFIN);
            body.put("EMAIL", Email);

            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");


        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC010_UpdateExistingDetailsInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC011_FetchLeadAllData() throws JSONException, InterruptedException
        {

            Thread.sleep(10000);


            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))

            {

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");

            }

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))

            {

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");

                responseObject=lendingBaseClassObject.BRE1CallbackforMCA(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);


                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stagaveId"),"396");
            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());

        }

        @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC011_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC012_MCAV3_InitiateKYC_UsingSearchByPan() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("kycMode","SEARCH_BY_PAN");


            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC012_MCAV3_InitiateKYC_UsingSearchByPan")
        @Owner(emailId = "<EMAIL>")
        public void TC013_MCAV3_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=15;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());


        }


        @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC013_MCAV3_FetchDataPostKYCIntiated")
        @Owner(emailId = "<EMAIL>")
        public void TC014_MCAV3_UpdateLeadDetailsinSAI() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("statusMessage","ADDITIONAL_INFO_UPDATE");
            body.put("status","SUCCESS");
            body.put("PAN", "**********");
            body.put("DOB","1973-01-01");
            body.put("GENDER","MALE");
            body.put("NSDL_NAME","Nagu Singh");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";

            responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");


        }

        @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC014_MCAV3_UpdateLeadDetailsinSAI")
        @Owner(emailId = "<EMAIL>")
        public void TC015_MCAV3_InitiateKYC_UsingSearchByAadhaar() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("kycMode","SEARCH_BY_AADHAAR");
            body.put("aadhaar","2671");
            body.put("gender","MALE");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByAAdhaarRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }


        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC015_MCAV3_InitiateKYC_UsingSearchByAadhaar")
        @Owner(emailId = "<EMAIL>")
        public void TC016_MCAV3_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=45;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2020");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());


        }

        @Test(description = "Verify the PLv3 lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC016_MCAV3_FetchDataPostKYCIntiated")
        @Owner(emailId = "<EMAIL>")
        public void TC017_MCAV3_UploadCustomerPhoto() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2, sessionToken,"others");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);


        }

        @Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC017_MCAV3_UploadCustomerPhoto")
        @Owner(emailId = "<EMAIL>")
        public void TC018_MCAV3_VerifyUploadedCustomerPhoto() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            //Customerphoto
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");

        }

        @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC018_MCAV3_VerifyUploadedCustomerPhoto", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC019_UpdateSAIWithLenderDetails() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
            body.put("status", "SUCCESS");
            body.put("F_NAME", "DHARMENDAR");
            body.put("L_NAME", "SINGH");
            body.put("GENDER", "MALE");
            body.put("PINCODE", "400076");
            body.put("PAN", "**********");
            body.put("DOB", "1984-08-11");
            body.put("EMAIL", "<EMAIL>");
            body.put("NSDL_NAME", "DHARMENDAR SINGH");

            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

            String requestData= "MerchantService/V2/lending/dataUpdate/UpdateSAIWithLenderDetailsRequest.json";

            responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestData);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

            Assert.assertEquals(responseObject.jsonPath().getString("oeStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
        }



        @Test(description = "Verify the PLv3 lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC019_UpdateSAIWithLenderDetails")
        @Owner(emailId = "<EMAIL>")
        public void TC020_MCAV3_UploadSelfie() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2, sessionToken,"selfie");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);


        }

        @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC020_MCAV3_UploadSelfie", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC021_MCAV3_VerifyUploadedSelfie() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


            }

        }

        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC021_MCAV3_VerifyUploadedSelfie")
        @Owner(emailId = "<EMAIL>")
        public void TC022_MCAV3_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=35;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_UPLOADED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_UPLOADED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }




        }


        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC022_MCAV3_FetchDataPostKYCIntiated")
        @Owner(emailId = "<EMAIL>")
        public void TC023_MCAV3_FetchDataPostSelfieUploaded() throws JSONException
        {

            for(int i =0;i<=65;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_REQUESTED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_FAILURE_REASON"),"CKYC_ID_NOT_FOUND");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_SUCCESS_MODE"),"SEARCH_BY_AADHAAR");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_GENDER"),"MALE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_SELFIE_UPLOAD_REQUIRED"),"true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_MODE"),"SEARCH_BY_AADHAAR");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_STATUS"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_DOB"),"1973-01-01");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PAN_SEARCH_CKYC_SUCCESS"),"false");


            lendingImageQCRequired=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED");
            kycResponseTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_RESPONSE_TIMESTAMP");
            kycRequestId=responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_REQUEST_ID");
            kycId= responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_ID");
            selfieMatchVendor= responseObject.jsonPath().getString("solutionAdditionalInfo.SELFIE_MATCH_VENDOR");
            lendingPanNameQCRequired= responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED");
            panNameMatchPercentage= responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE");
            panValidationTimestamp= responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
            selfieMatchPercentage= responseObject.jsonPath().getString("solutionAdditionalInfo.SELFIE_MATCH_PERCENTAGE");

        }
        @Test(description = "Update lead details",dependsOnMethods = "TC023_MCAV3_FetchDataPostSelfieUploaded",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC024_VerifyLeadStage()
        {

            for(int i=0;i<10;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
                    break;

            }
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))

            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");

                //Hit BRE2 Callback
                LOGGER.info("Callback not coming so hitting BRE2 Callback");

                responseObject=lendingBaseClassObject. BRE2CallbackforFullerton (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);


            }

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_SUCCESS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_COMPLETED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"780");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"MALE");
            }




            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),"FALSE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SELFIE_MATCH_PERCENTAGE"),"100");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_fullerton");

                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FATHER_NAME"),"MR NAGU  SINGH");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PAN_SEARCH_CKYC_SUCCESS"),"false");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),"FALSE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE"),"100");

                panNameMatchTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_TIMESTAMP");
                panNameMatchPercentage=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE");
                breLastFetchDate=responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_LAST_FETCH_DATE");
                PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");

            }

            //Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());

        }

        @Test(description = "Update lead details",dependsOnMethods = "TC024_VerifyLeadStage",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC025_VerifyLeadStage()
        {

            for(int i=0;i<10;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
                    break;

            }
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE3_REQUESTED.getStage()))

            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1521");

                //Hit BRE3 Callback
                LOGGER.info("Callback not coming so hitting BRE3 Callback Manually");

                responseObject=lendingBaseClassObject. BRE3Callback (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);


            }

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE3_SUCCESS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"MALE");
            }



            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),"FALSE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SELFIE_MATCH_PERCENTAGE"),"100");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_fullerton");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FATHER_NAME"),"MR NAGU  SINGH");

                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PAN_SEARCH_CKYC_SUCCESS"),"false");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),"FALSE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE"),"100");

                panNameMatchTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_TIMESTAMP");
                panNameMatchPercentage=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE");
                breLastFetchDate=responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_LAST_FETCH_DATE");
                PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());

                }
            @Test(description = "Update lead details",dependsOnMethods = "TC025_VerifyLeadStage",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>",isAutomated = true)
            public void TC026_SaveBankDetailsPennyDropfailed() throws InterruptedException {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
                queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("session_token", sessionToken);
                headers.put("Content-Type", "application/json");

                Map<String, Object> body = new HashMap<String, Object>();

                body.put("workflowOperation", "VERIFY_BANK_DETAILS");
                body.put("EMANDATE_TYPE", "Internet Banking");
                body.put("bankName", "PAYTM BANK");
                body.put("bankAccountNumber", "$");
                body.put("ifsc", "PYTM0123456");
                body.put("bankAccountHolderName", "testNameMatch");


                Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                if (responseObject.getStatusCode() == 200) {

                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_FAILURE");
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),"BANK_VERIFICATION_FAILURE");

                }
        }



            @Test(description = "Verify for MCA v3 CKYC name update in SAI", dependsOnMethods = "TC026_SaveBankDetailsPennyDropfailed",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC027_FetchLeadAndValidatePennyDropFailedDetails() {
                responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VERIFICATION_FAILURE.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"796");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"$");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");

            }


            @Test(description = "Update lead details",dependsOnMethods = "TC027_FetchLeadAndValidatePennyDropFailedDetails",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>",isAutomated = true)
            public void TC028_SaveBankDetailsPennyDropfailed() throws InterruptedException {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
                queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("session_token", sessionToken);
                headers.put("Content-Type", "application/json");

                Map<String, Object> body = new HashMap<String, Object>();

                body.put("workflowOperation", "VERIFY_BANK_DETAILS");
                body.put("EMANDATE_TYPE", "Internet Banking");
                body.put("bankName", "PAYTM BANK");
                body.put("bankAccountNumber", "#");
                body.put("ifsc", "PYTM0123456");
                body.put("bankAccountHolderName", "testNameMatch");


                Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                if (responseObject.getStatusCode() == 200) {

                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_FAILURE");
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),"BANK_VERIFICATION_FAILURE");

                }
            }

            @Test(description = "Verify for MCA v3 CKYC name update in SAI", dependsOnMethods = "TC028_SaveBankDetailsPennyDropfailed",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC029_FetchLeadDetailsAfterBankVerificationFailure() {
                responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VERIFICATION_FAILURE.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"796");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"$");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");

            }


            @Test(description = "Verify MCA v3 Lead the bank details", dependsOnMethods = "TC029_FetchLeadDetailsAfterBankVerificationFailure", groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC030_BankValidationPending() throws InterruptedException {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);


                Map<String, String> headers = new HashMap<String, String>();
                headers.put("session_token", sessionToken);
                headers.put("Content-Type", "application/json");

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "VERIFY_BANK_DETAILS");
                body.put("EMANDATE_TYPE", "Internet Banking");
                body.put("bankName", "PAYTM BANK");
                body.put("bankAccountNumber", "************");
                body.put("ifsc", "PYTM0123456");
                body.put("bankAccountHolderName", "testNameMatch");

                responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

                if (responseObject.getStatusCode() == 200) {
                    Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
                }

                else {

                    if (responseObject.jsonPath().getString("displayMessage").contentEquals("Name match failed"))
                    {
                        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("nameMatchSuccess"),"false");

                    }

                }


            }


            @Test(description = "Verify for MCA v3 CKYC name update in SAI", dependsOnMethods = "TC030_BankValidationPending",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC031_FetchLeadDetailsAfterBankValidationPending() {
                responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"558");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"************");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");

            }


            @Test(description = "Verify MCA v3 Lead the bank details", dependsOnMethods = "TC031_FetchLeadDetailsAfterBankValidationPending", groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC032_BankValidationPending() throws InterruptedException {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);


                Map<String, String> headers = new HashMap<String, String>();
                headers.put("session_token", sessionToken);
                headers.put("Content-Type", "application/json");

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "VERIFY_BANK_DETAILS");
                body.put("EMANDATE_TYPE", "Internet Banking");
                body.put("bankName", "PAYTM BANK");
                body.put("bankAccountNumber", "************");
                body.put("ifsc", "PYTM0123456");
                body.put("bankAccountHolderName", "testNameMatch");

                responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

                if (responseObject.getStatusCode() == 200) {

                    Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
                }

                else {

                    if (responseObject.jsonPath().getString("displayMessage").contentEquals("Name match failed"))
                    {
                        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("nameMatchSuccess"),"false");

                    }

                }


            }


            @Test(description = "Verify for MCA v3 CKYC name update in SAI", dependsOnMethods = "TC032_BankValidationPending",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC033_FetchLeadDetailsAfterBankValidationPending() {
                responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"558");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"************");
                Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");

            }








    @Test(description = "Verify for CKYC name update in SAI", dependsOnMethods = "TC033_FetchLeadDetailsAfterBankValidationPending", groups = { "Regression" })
            @Owner(emailId = "<EMAIL>", isAutomated = true)
            public void TC034_UpdateKYCNameInSAI() {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                queryParams.put("leadId", leadId);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
                body.put("status", "SUCCESS");
                body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
                body.put("PAN", Pan);

                requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
                responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
                Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            }


            @Test(description = "Enter the bank details", dependsOnMethods = "TC034_UpdateKYCNameInSAI", groups = {
                        "Regression" })
                @Owner(emailId = "<EMAIL>", isAutomated = true)
                public void TC035_SaveBankDetails() {
                    Map<String, String> queryParams = new HashMap<String, String>();
                    queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                    queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                    queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
                    queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
                    queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);

                    Map<String, String> headers = new HashMap<String, String>();
                    headers.put("session_token", sessionToken);
                    headers.put("Content-Type", "application/json");

                    Map<String, Object> body = new HashMap<String, Object>();
                    body.put("workflowOperation", "VERIFY_BANK_DETAILS");
                    body.put("EMANDATE_TYPE", "Internet Banking");
                    body.put("bankName", "ICIC BANK");
                    body.put("bankAccountNumber", "************");
                    body.put("ifsc", "ICIC0001254");
                    body.put("bankAccountHolderName", "testNameMatch");

                    Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                    if (responseObject.getStatusCode() == 200) {

                        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VALIDATION_PENDING");

                    }

                    else {

                        for (int i = 1; i < 4; i++) {
                            LOGGER.info("Again hitting with same data: retry-count: " + i);
                            responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                            if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VALIDATION_PENDING"))
                                break;
                        }

                    }


                    Assert.assertEquals(responseObject.getStatusCode(),200);


                }


                @Test(description = "For MCA v3 Bank Verification", dependsOnMethods = "TC035_SaveBankDetails",groups = {"Regression"})
                @Owner(emailId = "<EMAIL>")
                public void TC36_FetchLeadPostBankVerification() {
                    for(int i=0;i<5;i++) {
                        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
                            break;
                        }
                    }
                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
                    Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.bankName"),"ICIC BANK");
                    Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.bankAccountNumber"),"************");
                    Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.ifsc"),"ICIC0001254");



                }


            @Test(description = "Verify MCA v3 Lead the bank details", dependsOnMethods = "TC36_FetchLeadPostBankVerification", groups = {
                    "Regression" })
            @Owner(emailId = "<EMAIL>")
            public void TC037_UpdateNewBankDetails() throws InterruptedException {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);


                Map<String, String> headers = new HashMap<String, String>();
                headers.put("session_token", sessionToken);
                headers.put("Content-Type", "application/json");

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "VERIFY_BANK_DETAILS");
                body.put("EMANDATE_TYPE", "Internet Banking");
                body.put("bankName", bankName);
                body.put("bankAccountNumber", "************");
                body.put("ifsc", "PYTM0123456");
                body.put("bankAccountHolderName", "testNameMatch");



                responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

                if (responseObject.getStatusCode() == 200) {

                    Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
                }

                else {

                    if (responseObject.jsonPath().getString("displayMessage").contentEquals("Name match failed"))
                    {
                        //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Name match failed");

                    }

                }


            }



            @Test(description = "Verify for MCA v3 CKYC name update in SAI",dependsOnMethods = "TC037_UpdateNewBankDetails",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC038_VerifyPennydropForNewBankDetails() {
                responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
                Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.bankAccount"),"************");
                Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.ifsc"),"PYTM0123456");
                Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.source"),"LENDING");
                Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.pennyDropStatus"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.pennyDropName"),"Paytm");

            }

            @Test(description = "Verify MCA v3 Lead the bank details", dependsOnMethods = "TC038_VerifyPennydropForNewBankDetails", groups = {
                    "Regression" })
            @Owner(emailId = "<EMAIL>")
            public void TC039_SaveSameBankDetailsAgain() throws InterruptedException {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);


                Map<String, String> headers = new HashMap<String, String>();
                headers.put("session_token", sessionToken);
                headers.put("Content-Type", "application/json");

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("bankName", bankName);
                body.put("bankAccountNumber", "************");
                body.put("ifsc", "PYTM0123456");
                body.put("bankAccountHolderName", "testNameMatch");
                body.put("EMANDATE_TYPE", "Internet Banking");

                responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

                if (responseObject.getStatusCode() == 200) {

                    Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
                    responseObject.jsonPath().getString("displayMessage").contentEquals("Bank details are already verified.");
                }
            }


                @Test(description = "Verify when lead is on banking action done and user retries with " +
                        "another bank and penny drop fails then no stage movement should happen same stage " +
                        "should be returned and base response code and penny drop error message should be updated ", dependsOnMethods = "TC039_SaveSameBankDetailsAgain", groups = {"Regression" })
                @Owner(emailId = "<EMAIL>")
                public void TC040_VerifyAddingNewBankAndGetPennyDropFailed() throws InterruptedException {
                    Map<String, String> queryParams = new HashMap<String, String>();
                    queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
                    queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                    queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                    queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);


                    Map<String, String> headers = new HashMap<String, String>();
                    headers.put("session_token", sessionToken);
                    headers.put("Content-Type", "application/json");

                    Map<String, Object> body = new HashMap<String, Object>();
                    body.put("bankName", bankName);
                    body.put("bankAccountNumber", "&");
                    body.put("ifsc", "PYTM0123456");
                    body.put("bankAccountHolderName", "testNameMatch");
                    body.put("EMANDATE_TYPE", "Internet Banking");


                    responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

                    if (responseObject.getStatusCode() == 200) {

                        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_FAILURE");
                        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"BANK_VERIFICATION_FAILURE");

                    }
                }

//
//                @Test(description = "Verify for MCA v3 CKYC name update in SAI", dependsOnMethods = "TC040_VerifyAddingNewBankAndGetPennyDropFailed",groups = {"Regression"})
//                @Owner(emailId = "<EMAIL>")
//                public void TC041_FetchLeadAndValidatePennyDropFailedDetails() {
//                    responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
//                            LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
//                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//
//                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
//                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
//                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
//                    Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.bankAccount"),"************");
//                    Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.ifsc"),"PYTM0123456");
//
//                }




}


