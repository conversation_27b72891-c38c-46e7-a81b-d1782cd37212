//package OCL.Lending.BusinessLending;
//
//import java.io.IOException;
//import java.sql.SQLException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.apache.log4j.Logger;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import com.paytm.apitools.core.P;
//import com.paytm.apitools.util.annotations.Owner;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//import io.restassured.response.Response;
//
//public class TestAril extends LendingBaseAPI {
//
//	private static final Logger LOGGER = Logger.getLogger(TestAril.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
//
//	String sessionToken = "";
//	String leadId = "";
//	String merchantCustId = "1001045548";
//	String merchantNumber = "**********";
//	String merchantPassword = "test@123";
//	String merchantID = "nSjTzJ63838398332681";
//	String agentCustId = "1000334963";
//	String agentNumber = "**********";
//	String prodcutType = "ARIL";
//	String token = "";
//	String uuid = "";
//	String ckycStage = "";
//	String loanOffered = "";
//	String maxLoanAmount = "";
//	String authorisedMonthlyLimit = "";
//	String stage = "";
//	String code = "";
//	String tncName = "";
//	String url = "";
//	String uniqueIdentifier = "";
//	String md5 = "";
//	String codeSanctionLetter = "";
//	String tncNameSanctionLetter = "";
//	String urlSanctionLetter = "";
//	String uniqueIdentifierSanctionLetter = "";
//	String md5SanctionLetter = "";
//	String host = "";
//	String solutionTypeLevel2 = "";
//	String pan = "";
//	String LMS_SECRET = "fd61f785-e867-471b-90c6-1447b4331712";
//
//	public static final String SOLUTION = "aril";
//	public static final String SOLUTION_TYPE_LEVEL_3 = "";
//	public static final String ENTITY_TYPE = "PROPRIETORSHIP";
//	public static final String CHANNEL = "GG_APP";
//	// public static final String PAN = "";
//	public static final String DOB = "1989-04-21";
//	public static final String EMAIL = "<EMAIL>";
//	public static final String ISSUER = "OE";
//	public static final String CLIENT_ID = "LMS";
//
//	Map<String, String> commonHeaders;
//
//	@BeforeClass()
//	public void intitializeInputData() throws IOException {
//
//		LOGGER.info(" Before Suite Method for merchant Login ");
//		sessionToken = ApplicantToken(merchantNumber, merchantPassword);
//		LOGGER.info("Applicant Token for Lending : " + sessionToken);
//
//		host = lendingBaseClassObject.getHostUrl();
//		commonHeaders = setcommonHeaders();
//
//	}
//
//	@Test(description = "Delete all existing leads of the number", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC001_DeleteExistingLead() throws SQLException {
//		// Map<String, String> queryParams = new HashMap<String, String>();
//		// queryParams.put("mobile", merchantNumber);
//
//		// Map<String, String> headers = new HashMap<String, String>();
//		// headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683");
//
//		// lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
//
//		String Query = "update user_business_mapping set status=2 where mobile_number=********** and solution_type='aril'";
//		LOGGER.info("Executing Query : " + Query);
//		lendingBaseClassObject.UpdateQuery(Query);
//
//	}
//
//	@Test(description = "Verify whether there is any existing lead present or not", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC002_FetchLeadDeatils() {
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "LEAD_NOT_PRESENT");
//
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");
//	}
//
//	@Test(description = "Create ARIL Lead with lender CLIX", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC003_CreateArilLead() throws SQLException {
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", ENTITY_TYPE);
//		queryParams.put("solutionType", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, agentCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Content-Type", "application/json; charset=UTF-8");
//		headers.put("Authorization", token);
//		headers.put("deviceName", "Mi_A1");
//		headers.put("deviceIdentifier", "Xiaomi-MiA1-866409032095149");
//		headers.put("client", "androidapp");
//		headers.put("imei", "866409032095149");
//		headers.put("deviceManufacturer", "Xiaomi");
//		headers.put("version", "3.8.9");
//		headers.put("latitude", "28.6056097");
//		headers.put("longitude", "77.3419034");
//		headers.put("appLanguage", "en");
//		headers.put("channel", "GG_APP");
//		headers.put("custId", agentCustId);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("PRODUCT_TYPE", prodcutType);
//		body.put("MERCHANT_ID", merchantID);
//		body.put("AGENT_NAME", "Neeraj");
//		body.put("AGENT_MOBILE_NUMBER", agentNumber);
//		body.put("IS_GROUP_ACCOUNT", "true");
//		body.put("MOBILE_NUMBER", merchantNumber);
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, merchantCustId);
//		if (responseObject.getStatusCode() == 200) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("message"));
//			Assert.assertEquals(responseObject.jsonPath().getString("message"), "Lead successfully created.");
//			leadId = responseObject.jsonPath().getString("leadId");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, merchantCustId);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//			leadId = responseObject.jsonPath().getString("leadId");
//		}
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC003_CreateArilLead", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC004_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "LEAD_CREATED");
//		String custId = responseObject.jsonPath().getString("custId");
//		System.out.println(custId);
//		LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_TYPE"),
//				"ARIL");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.AGENT_MOBILE_NUMBER"),
//				"**********");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.SEGMENT"),
//				"Automobiles and Vehicles");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_GROUP_ACCOUNT"),
//				"true");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.AGENT_NAME"),
//				"Neeraj");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.SUB_SEGMENT"),
//				"Automobile Parts and Accessories");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LINKED_QR_MERCHANT_LEAD_ID"),
//				"d7026d41-dd75-4db1-94c4-d60d576b5399");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.MERCHANT_ID"),
//				"nSjTzJ63838398332681");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.APPLICANT_NAME"),
//				"TOUCH WOOD LIMITED");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MOBILE_NUMBER"), "**********");
//
//	}
//
//	@Test(description = "Verify the callback for PPBL OTP", dependsOnMethods = "TC003_CreateArilLead", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC005_PPBLOTPCallback() throws InterruptedException {
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, merchantCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, merchantCustId, "GG_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "LENDING_OTP_VERIFIED");
//		body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "1584098137622");
//
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body, true);
//		Thread.sleep(1000);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC005_PPBLOTPCallback", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC006_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "LENDING_OTP_VERIFIED");
//		String merchantcustId = responseObject.jsonPath().getString("custId");
//		LOGGER.info(("Merchant customer id  from fetch lead  response is " + merchantcustId));
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//
//	}
//
//	@Test(description = "Updating ARIL Lead ", dependsOnMethods = "TC005_PPBLOTPCallback", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC007_UpdateArilLead() throws SQLException {
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", ENTITY_TYPE);
//		queryParams.put("solutionType", SOLUTION);
//		queryParams.put("leadId", leadId);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, agentCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Content-Type", "application/json; charset=UTF-8");
//		headers.put("Authorization", token);
//		headers.put("deviceName", "Mi_A1");
//		headers.put("deviceIdentifier", "Xiaomi-MiA1-866409032095149");
//		headers.put("client", "androidapp");
//		headers.put("imei", "866409032095149");
//		headers.put("deviceManufacturer", "Xiaomi");
//		headers.put("version", "3.8.9");
//		headers.put("latitude", "28.6056097");
//		headers.put("longitude", "77.3419034");
//		headers.put("appLanguage", "en");
//		headers.put("channel", "GG_APP");
//		headers.put("custId", agentCustId);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("DOB", "1989-04-21");
//		body.put("EMAIL", "<EMAIL>");
//		body.put("GENDER", "MALE");
//		body.put("MERCHANT_DOI", "2015-11-03");
//		body.put("IS_MERCHANT_DOI_EDITABLE", "true");
//
//		LOGGER.info(body);
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, merchantCustId,
//				true);
//		if (responseObject.getStatusCode() == 200) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("message"));
//			Assert.assertEquals(responseObject.jsonPath().getString("message"),
//					"Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//			leadId = responseObject.jsonPath().getString("leadId");
//		}
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC007_UpdateArilLead", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC008_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "LEAD_UPDATED");
//		String merchantcustId = responseObject.jsonPath().getString("custId");
//		LOGGER.info(("Merchant customer id  from fetch lead  response is " + merchantcustId));
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.USER_PROVIDED_MERCHANT_DOI"),
//				"2015-11-03");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.USER_PROVIDED_EMAIL"),
//				"<EMAIL>");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.MERCHANT_DOI"),
//				"2015-11-03");
//		Assert.assertEquals(responseObject.jsonPath()
//				.getString("solution.solutionAdditionalInfo.USER_PROVIDED_IS_MERCHANT_DOI_EDITABLE"), "true");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.USER_PROVIDED_GENDER"),
//				"MALE");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.USER_PROVIDED_DOB"),
//				"1989-04-21");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_MERCHANT_DOI_EDITABLE"),
//				"false");
//
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.DOB"), "1989-04-21");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.GENDER"), "MALE");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.EMAIL"), "<EMAIL>");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.PAN"), "**********");
//
//	}
//
//	@Test(description = "BRE Validation Pending", dependsOnMethods = "TC008_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC09_BREValidationPending() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, agentCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", agentCustId);
//		headers.put("channel", "DIY_P4B_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "COMPLETE PROFILE FETCHED");
//
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC09_BREValidationPending", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC010_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "BRE_VALIDATION_PENDING");
//		String merchantcustId = responseObject.jsonPath().getString("custId");
//		LOGGER.info(("Merchant customer id  from fetch lead  response is " + merchantcustId));
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BRE_VALIDATION_PENDING_REASON"),
//				"COMPLETE PROFILE FETCHED");
//
//	}
//
//	@Test(description = "BRE OTP Verified", dependsOnMethods = "TC010_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC11_BREOtpVerified() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, agentCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", agentCustId);
//		headers.put("channel", "DIY_P4B_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_OTP_VERIFIED");
//		body.put("ALTERNATE_MOBILE_NUMBER", "8130591481");
//
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC11_BREOtpVerified", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC012_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "BRE_OTP_VERIFIED");
//		String merchantcustId = responseObject.jsonPath().getString("custId");
//		LOGGER.info(("Merchant customer id  from fetch lead  response is " + merchantcustId));
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//		// Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ALTERNATE_MOBILE_NUMBER"),"8130591481");
//
//	}
//
//	@Test(description = "BRE Request Invoked", dependsOnMethods = "TC012_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC13_BRERequestInvoked() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, agentCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", agentCustId);
//		headers.put("channel", "DIY_P4B_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_REQUEST_INVOKED");
//
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC13_BRERequestInvoked", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC014_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "BRE_RESPONSE_AWAITED");
//		String merchantcustId = responseObject.jsonPath().getString("custId");
//		LOGGER.info(("Merchant customer id  from fetch lead  response is " + merchantcustId));
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//		// Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ALTERNATE_MOBILE_NUMBER"),"8130591481");
//
//	}
//
//	@Test(description = "Bre Success Callback", dependsOnMethods = "TC014_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC015_BRESuccesResponse() throws SQLException {
//
//		LOGGER.info("Using Callback API");
//		Map<String, String> queryParams = new HashMap<String, String>();
//
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("leadId", leadId);
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, agentCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//
//		headers.put("Authorization", token);
//		headers.put("custId", agentCustId);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "BRE_SUCCESS");
//		body.put("status", "SUCCESS");
//		body.put("LENDER_NAME", "CLIX");
//		body.put("LOAN_GROUP_LIMIT_NUMBER", "200000");
//		body.put("LOAN_GROUP_LIMIT_WORDS", "Two Lakh");
//		body.put("LOAN_TENURE_UNIT", "MONTHLY");
//		body.put("loanOffered", true);
//		body.put("bureau", "CIBIL");
//		body.put("creditScore", 690);
//		body.put("lastFetchDate", 1585383464);
//		body.put("maxLoanAmount", "200000");
//		body.put("minLoanAmount", "20000");
//		body.put("maxTenure", 2);
//		body.put("minTenure", 2);
//		body.put("rateOfInterest", 2);
//		body.put("productId", "64");
//		body.put("productVersion", 1);
//		body.put("offerValidity", 30);
//		body.put("offerEndDate", "2021-12-22");
//		body.put("offerStartDate", "2020-09-23");
//		body.put("baseId", "plclix006");
//		body.put("offerId", "XYZ");
//		body.put("riskGrade", "L");
//		body.put("sourceOfWhitelist", "product");
//		body.put("emandateType", "MANDATORY");
//		body.put("isActive", 1);
//		body.put("reasons", "[]");
//		body.put("transactionId", "0aac434f-32fe-456a-93eb-d116271298df");
//		// body.put("setName","");
//		body.put("setName", "loanvettedarilgrouptnc");
//
//		Response responseObject = lendingBaseClassObject.breCallbackAril(queryParams, headers, body, true);
//		verifyResponseCodeAs200OK(responseObject);
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC015_BRESuccesResponse", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC016_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "BRE_SUCCESS");
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDER_NAME"), "CLIX");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_GROUP_LIMIT_NUMBER"),
//				"200000");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_GROUP_LIMIT_WORDS"),
//				"Two Lakh");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_TENURE_UNIT"),
//				"MONTHLY");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BRE_LOAN_OFFERED"),
//				"true");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.EMANDATE_TYPE"),
//				"MANDATORY");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OFFER_VALIDITY"),
//				"30");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_TENURE_MAX"),
//				"2");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_OFFER_ID"),
//				"XYZ");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_ACTIVE"), "1");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_MAX_AMOUNT"),
//				"200000");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.WHITELISTING_SOURCE"),
//				"product");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),
//				"loanvettedarilgrouptnc");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_TENURE_MIN"),
//				"2");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"), "2");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BRE_BUREAU_TYPE"),
//				"CIBIL");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BRE_CREDIT_SCORE"),
//				"690.0");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_ID"), "64");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.RISK_GRADE"), "L");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_VERSION"),
//				"1");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OFFER_START_DATE"),
//				"2020-09-23");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BASE_ID"),
//				"plclix006");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_TYPE"),
//				"ARIL");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_LENDING_AUDIT_NEEDED"), "TRUE");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OFFER_END_DATE"),
//				"2021-12-22");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.TRANSACTION_ID"),
//				"0aac434f-32fe-456a-93eb-d116271298df");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_MIN_AMOUNT"),
//				"20000");
//
//		solutionTypeLevel2 = responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDER_NAME");
//		pan = responseObject.jsonPath().getString("business.pan");
//
//	}
//
//	@Test(description = "Verify the callback for lending lead Tnc", dependsOnMethods = "TC016_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC017_LendingLeadOTPCallback() throws InterruptedException {
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, merchantCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, merchantCustId, "GG_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "LENDING_LEAD_TNC");
//		body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2020-09-10T14:06:35.952+05:30");
//		body.put("USER_IP_ADDRESS", "127.0.0.1");
//
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body, true, true);
//		Thread.sleep(1000);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC017_LendingLeadOTPCallback", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC018_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "LENDING_LEAD_TNC");
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.USER_IP_ADDRESS"),
//				"127.0.0.1");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP"),
//				"2020-09-10T14:06:35.952+05:30");
//
//	}
//
//	@Test(description = "Verify the callback for lending lead Tnc", dependsOnMethods = "TC018_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC019_ArilOTPCallback() throws InterruptedException {
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, merchantCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, merchantCustId, "GG_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "OTP_VERIFIED");
//		body.put("OTP_VALIDATION_TIMESTAMP", "2020-09-10T14:06:35.952+05:30");
//		body.put("OTP_VALIDATION_ISSUER_IP", "127.0.0.1");
//
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body, "aril");
//		Thread.sleep(1000);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC019_ArilOTPCallback", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC020_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "OTP_VERIFIED");
//		LOGGER.info(("Current lead stage is : " + responseObject.jsonPath().getString("stage")));
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OTP_VALIDATION_ISSUER_IP"),
//				"127.0.0.1");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OTP_VALIDATION_TIMESTAMP"),
//				"2020-09-10T14:06:35.952+05:30");
//
//	}
//
//	@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC020_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC021_CheckCKYCStatus() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", ENTITY_TYPE);
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
//		queryParams.put("channel", CHANNEL);
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		verifyResponseCodeAs200OK(responseObject);
//		ckycStage = responseObject.jsonPath().getString("stage");
//		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//
//	}
//
//	@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC021_CheckCKYCStatus", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC022_VerifyLeadStage() {
//
//		lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL, sessionToken, ckycStage);
//
//	}
//
//	@Test(description = "Upload selfie", dependsOnMethods = "TC022_VerifyLeadStage", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC023_UploadSelfie() throws InterruptedException {
//
//		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, merchantCustId,
//				ENTITY_TYPE, SOLUTION, solutionTypeLevel2, sessionToken);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//		uuid = responseObject.jsonPath().getString("uuid");
//
//		Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//		LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//		Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//		Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//
//	}
//
//	@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC023_UploadSelfie", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC024_VerifyUploadedDocument() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, ckycStage);
//		Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docType"), "selfie");
//		Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docProvided"), "selfie");
//		Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docUUId"), uuid);
//
//	}
//
//	@Test(description = "Upload Customer Photo", dependsOnMethods = "TC024_VerifyUploadedDocument", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC025_UploadCustomerPhoto() throws InterruptedException {
//
//		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, merchantCustId,
//				ENTITY_TYPE, SOLUTION, solutionTypeLevel2, sessionToken);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//		uuid = responseObject.jsonPath().getString("uuid");
//
//		Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//		LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//		Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//		Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//
//	}
//
//	@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC025_UploadCustomerPhoto", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC026_VerifyUploadedDocument() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, ckycStage);
//		List<Object> docTypes = responseObject.jsonPath().getList("solutionDocSRO.docType");
//		docTypes.contains("customerPhoto");
//		List<Object> docProvided = responseObject.jsonPath().getList("solutionDocSRO.docProvided");
//		docProvided.contains("others");
//
//	}
//
//	@Test(description = "CKYC Callback", dependsOnMethods = "TC026_VerifyUploadedDocument", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC027_CKYCCallback() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("leadId", leadId);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, merchantCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", merchantCustId);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "CKYC_VERIFIED");
//		body.put("status", "SUCCESS");
//		body.put("cKycId", "4353435454356");
//		body.put("firstName", "TOUCH");
//		body.put("middleName", "WOOD");
//		body.put("thirdName", "LIMITED");
//		body.put("email", "<EMAIL>");
//		body.put("type", "SELFIE");
//		body.put("percentage", "100");
//		body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//		body.put("addressline2", "MAYUR VIHAR PHASE 1");
//		body.put("city", "DELHI");
//		body.put("state", "EAST DELHI");
//		body.put("pincode", "110091");
//		body.put("dob", DOB);
//		body.put("gender", "male");
//		body.put("pan", pan);
//		body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//
//		Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), "PAN_VERIFIED");
//
//	}
//
//	@Test(description = "Verify Lead stage", dependsOnMethods = "TC027_CKYCCallback", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC028_VerifyLeadStage() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "PAN_VERIFIED");
//
//		LOGGER.info("Verify that detials are present in userAdditionalInfo");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
//		LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),
//				"FALSE");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),
//				"FALSE");
//
//	}
//
//	@Test(description = "CKYC Callback", dependsOnMethods = "TC028_VerifyLeadStage", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC029_UpdateKYCNameInSAI() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("leadId", leadId);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, merchantCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", merchantCustId);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("CKYC_NAME", "TestBeneficiary");
//
//		Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), "PAN_VERIFIED");
//
//	}
//
//	@Test(description = "Enter the bank details", dependsOnMethods = "TC029_UpdateKYCNameInSAI", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC030_SaveBankDetails() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("entityType", ENTITY_TYPE);
//		queryParams.put("channel", CHANNEL);
//		queryParams.put("solutionTypeLevel2", solutionTypeLevel2);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//
//		
//		if (responseObject.getStatusCode() == 200) {
//		
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
//		
//		}
//		
//		else {
//			
//			for (int i = 1; i < 4; i++) {
//				LOGGER.info("Again hitting with same data: retry-count: " + i);
//				responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//				
//				if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
//					break;
//			}
//		
//		}
//		
//	
//			Assert.assertEquals(responseObject.getStatusCode(),200);
//			
//		
//		}
//	
//
//	@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC030_SaveBankDetails", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC031_FetchDynamicTnc() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//
//		LOGGER.info("Status Code : " + responseObject.getStatusCode());
//		if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//
//			code = responseObject.jsonPath().getString("data.state.code");
//			tncName = responseObject.jsonPath().getString("data.state.tncName");
//			url = responseObject.jsonPath().getString("data.state.url");
//			uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//			md5 = responseObject.jsonPath().getString("data.state.md5");
//		}
//
//	}
//
//	@Test(description = "Verify submit application", dependsOnMethods = "TC031_FetchDynamicTnc", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC032_SubmitApplication() throws InterruptedException {
//// Check why this is not working--< giving error could not save vetted tnc
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Content-Type", "application/json");
//		headers.put("session_token", sessionToken);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("accept", 1);
//		body.put("tncName", tncName);
//		body.put("uniqueIdentifier", uniqueIdentifier);
//		body.put("md5", md5);
//
//		Response responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body, true);
//
//		LOGGER.info("Status Code : " + responseObject.getStatusCode());
//		if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
//
//		{
//			LOGGER.info("Try again");
//			responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body,true);
//			Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//
//		}
//
//		else if (responseObject.jsonPath().getString("meta.stage").contentEquals("LMS_POST")) {
//			verifyResponseCodeAs200OK(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("meta.subStage"), "APPLICATION_PENDING");
//			Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//			Assert.assertEquals(responseObject.jsonPath().getString("data.state"), "Loan Application is Accepted");
//
//		}
//
//	}
//
//	@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC032_SubmitApplication", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC033_EmandateCallback() throws InterruptedException {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, merchantCustId,LMS_SECRET);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", merchantCustId);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "EMANDATE_SUCCESS");
//
//		Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	/**
//	 * Method to set headers which are used in lead creation request
//	 * 
//	 * @return
//	 */
//	public Map<String, String> setcommonHeaders() {
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		return headers;
//	}
//
//}
