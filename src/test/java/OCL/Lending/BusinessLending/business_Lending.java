/*
 * package OCL.Lending.BusinessLending;
 * 
 * import Request.MerchantService.loan.lead.dynamicTNC; import
 * Request.MerchantService.loan.lead.submitApplication; import
 * Request.MerchantService.oe.panel.v1.editLead.EditLead; import
 * Request.MerchantService.oe.panel.v1.fileProcess.fileUpload_BusinessStatus;
 * import Request.MerchantService.oe.panel.v1.lead.FetchLead; import
 * Request.MerchantService.v1.TokenXMV; import
 * Request.MerchantService.v1.profile.update.bank_fetchChild; import
 * Request.MerchantService.v1.profile.update.editBank; import
 * Request.MerchantService.v1.profile.update.fetchBankUpdate; import
 * Request.MerchantService.v1.sdMerchant.*; import
 * Request.MerchantService.v1.sdMerchant.lms.merchant.wrapper.triggerotp; import
 * Request.MerchantService.v1.sdMerchant.lms.merchant.wrapper.validateotp;
 * import Request.MerchantService.v2.lending.getBreStatus; import
 * Request.MerchantService.v2.lending.getKycStatus; import
 * Request.MerchantService.v2.upgradeMid.doc.uploadCancelledCheque; import
 * Request.MerchantService.v5.callback.callBackSaveOTP; import
 * Request.MerchantService.v5.callback.lmsCallBack_loanTap; import
 * Request.oAuth.oauth2.Authorize; import Request.oAuth.oauth2.Token; import
 * Services.MechantService.MiddlewareServices; import
 * Services.Utilities.Utilities; import Services.oAuth.oAuthServices; import
 * com.auth0.jwt.JWT; import com.auth0.jwt.algorithms.Algorithm; import
 * com.goldengate.common.BaseMethod; import com.jcraft.jsch.JSchException;
 * import com.opencsv.CSVWriter; import com.paytm.apitools.core.P; import
 * com.paytm.apitools.util.annotations.Owner; import
 * io.restassured.response.Response; import org.apache.log4j.Logger; import
 * org.openqa.selenium.WebDriver; import
 * org.openqa.selenium.firefox.FirefoxDriver; import org.testng.Assert; import
 * org.testng.annotations.BeforeSuite; import org.testng.annotations.Test;
 * import java.io.*; import java.time.LocalDateTime; import java.time.ZoneId;
 * import java.util.HashMap; import java.util.Map;
 * 
 * 
 * public class business_Lending extends BaseMethod {
 * 
 * 
 * public static String leadStatus = ""; public static String leadId = "";
 * public static String PGleadId = ""; public static String custId =
 * "**********"; public static String state = ""; public static String
 * childLeadId = ""; public static String mid = "PEzFxM18980044687785"; public
 * static String token = ""; public static String AgentNo = "**********"; public
 * static String session_token = ""; public static String code = ""; public
 * static String XMWToken = ""; public static String pan = "**********"; public
 * static String workflowStatusId=""; public static String
 * bankAccountNumber="************"; public static String ifsc="icic0006622";
 * public static String bankAccountHolderName="";
 * 
 * 
 * File fis = new File(
 * "/home/<USER>/Documents/OE_API_Automation/oe-api-automation/src/main/resources/cancelCheque.pdf"
 * ); File fileUpload = new File(
 * "/home/<USER>/Documents/OE_API_Automation/oe-api-automation/src/main/resources/Business_Status_Test_file.csv"
 * );
 * 
 * private static final Logger LOGGER =
 * Logger.getLogger(business_Lending.class); MiddlewareServices
 * middlewareServicesObject = new MiddlewareServices(); Utilities utilities =
 * new Utilities();
 * 
 * 
 * @BeforeSuite
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true) public void
 * getAgentToken() { LOGGER.info(" Before Suite Method for Agent Login ");
 * session_token = ApplicantToken(AgentNo,"paytm@123");
 * LOGGER.info("Applicant Token for Lending : " + session_token);
 * 
 * }
 * 
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "jwt token creation")
 * 
 * public String generateJwtToken() {
 * 
 * LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30")); //
 * LocalDate localDate = localDateTime.toLocalDate();
 * System.out.println("Date is :" + localDateTime); String ts =
 * localDateTime.toString();
 * 
 * Algorithm buildAlgorithm =
 * Algorithm.HMAC256("fd61f785-e867-471b-90c6-1447b4331712"); token =
 * JWT.create().withIssuer("OE") .withClaim("clientId", "LMS")
 * .withClaim("custId", custId) .withClaim("timestamp", ts +
 * "+05:30").sign(buildAlgorithm); return token; }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "positive response  for callback for save otp", priority
 * = 16) public void callbackOTP1() throws IOException, JSchException {
 * callBackSaveOTP callBackotp = new callBackSaveOTP(); generateJwtToken();
 * System.out.println("token is" + " " + token); Response responseObject =
 * middlewareServicesObject.callBackSaveOTP(callBackotp, leadId,
 * "business_lending", token, "no-cache", "application/json",
 * "1a9a3858-cb60-471c-b910-2ee58a4a5f24", "DIY_P4B_APP", custId);
 * callBackotp.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * int StatusCode = responseObject.getStatusCode();
 * 
 * System.out.println("Status Code is " + StatusCode);
 * callBackotp.validateResponseAgainstJSONSchema(
 * "MerchantServicev5callbackforSaveOTP/leadResponseSchema.json");
 * Assert.assertEquals(StatusCode, 200); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "get Ckyc details", priority = 17) public void getCkyc1()
 * throws IOException, JSchException { getKycStatus kycStatus = new
 * getKycStatus(); Response responseObject =
 * middlewareServicesObject.getKycStatus(kycStatus, "INDIVIDUAL", "CLIX",
 * "Unsecured_Short_term_Loan_Simplified", "business_lending", "DIY_P4B_APP",
 * session_token, "application/json");
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); // kycStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendinggetKycStatus/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "get pan details", priority = 18) public void fetchPan1()
 * throws IOException, JSchException { Map<String, String> queryParams = new
 * HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>(); body.put("pan",
 * pan);
 * 
 * Lead_create panStatus = new Lead_create(P.TESTDATA.get("FetchPan"));
 * 
 * Response responseObject = middlewareServicesObject.CreateLead(panStatus,
 * queryParams, headers, body);
 * 
 * panStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * panStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")); int
 * StatusCode = responseObject.getStatusCode();
 * System.out.println("Status Code is " + StatusCode); //
 * panStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1sdMerchantleadFetchPan/leadResponseSchema.json");
 * Assert.assertEquals(StatusCode, 200); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "get Bank details", priority = 19) public void bank1()
 * throws IOException, JSchException { bank bankStatus = new
 * bank(P.TESTDATA.get("LendingBankUpdate"));
 * 
 * Response responseObject = middlewareServicesObject.getBankDetails(bankStatus,
 * "INDIVIDUAL", "CLIX", "Unsecured_Short_term_Loan_Simplified",
 * "business_lending", "DIY_P4B_APP", session_token, "application/json",
 * "***************", "ANDB0002032");
 * 
 * System.out.println("API Response Code : " + responseObject.getStatusCode());
 * System.out.println("ref id is :" +
 * responseObject.jsonPath().getString("refId"));
 * bankStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * bankStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")); int
 * StatusCode = responseObject.getStatusCode(); String actualMsg =
 * responseObject.jsonPath().getString("stage");
 * 
 * System.out.println("Status Code is: " + StatusCode);
 * bankStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1sdMerchantbank/leadResponseSchema.json");
 * 
 * 
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "fetch update lead status", priority = 20) public void
 * fetchBankUpdateLeadStatus1() throws IOException, JSchException {
 * 
 * fetchBankUpdate bankUpdateStatus = new fetchBankUpdate(); Response
 * responseObject = middlewareServicesObject.fetchBankUpdate(bankUpdateStatus,
 * "INDIVIDUAL", "BANK_DETAIL_UPDATE", "pg_profile_update", session_token,
 * "application/json", "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb",
 * "BabaBlackSheepWeAreInShitDeep", mid,
 * "Unsecured_Short_term_Loan_Simplified"); leadStatus =
 * responseObject.jsonPath().getString("leadStatus"); PGleadId =
 * responseObject.jsonPath().getString("leadId");
 * bankUpdateStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * bankUpdateStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); bankUpdateStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1profileupdateleadstatus/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "fetch all saved bank", priority = 21) public void
 * fetchAllSavedBank1() throws IOException, JSchException { bank_fetchChild
 * fetchSavedBank = new bank_fetchChild(); Response responseObject =
 * middlewareServicesObject.fetchSavedBank(fetchSavedBank, "INDIVIDUAL",
 * "pg_profile_update", session_token);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); // fetchSavedBank.validateResponseAgainstJSONSchema(
 * "MerchantServicev1profileupdatebank/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid SolutionType",priority = 1) public void
 * fetchLead_invalidSolution_Type() throws IOException,
 * JSchException,IllegalArgumentException { Lead_fetch fetchLeadObject = new
 * Lead_fetch(); Map<String, String> queryParams = new HashMap<String,
 * String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX3");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("session_token", session_token);
 * 
 * Response responseObject =
 * middlewareServicesObject.FetchLead(fetchLeadObject,queryParams,headers); int
 * StatusCode = responseObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 200); System.out.println("Status Code is " + StatusCode); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid entityType",priority = 2) public void
 * fetchLead_invalidEntity_Type() throws IOException, JSchException { Lead_fetch
 * fetchLeadObject = new Lead_fetch(); Map<String, String> queryParams = new
 * HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIV"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("session_token", session_token);
 * 
 * Response responseObject =
 * middlewareServicesObject.FetchLead(fetchLeadObject,queryParams,headers);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid session token",priority = 3) public void
 * fetchLead_invalidSessionToken() throws IOException, JSchException {
 * Lead_fetch fetchLeadObject = new Lead_fetch(); Map<String, String>
 * queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("session_token", "session_token");
 * 
 * Response responseObject =
 * middlewareServicesObject.FetchLead(fetchLeadObject,queryParams,headers);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 401); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid channel",priority = 4) public void
 * fetchLead_invalidChannel() throws IOException, JSchException { Lead_fetch
 * fetchLeadObject = new Lead_fetch(); Map<String, String> queryParams = new
 * HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("session_token", session_token);
 * 
 * Response responseObject =
 * middlewareServicesObject.FetchLead(fetchLeadObject,queryParams,headers);
 * 
 * // System.out.println("Response Fields : " + fetchLeadObject.toString());
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid solution",priority = 5) public void
 * fetchLead_invalidSolution() throws IOException, JSchException { Lead_fetch
 * fetchLeadObject = new Lead_fetch(); Map<String, String> queryParams = new
 * HashMap<String, String>();
 * 
 * queryParams.put("solution", "lending"); queryParams.put("entityType",
 * "INDIV"); queryParams.put("channel", "DIY_P4B_APP");
 * queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("session_token", session_token);
 * 
 * Response responseObject =
 * middlewareServicesObject.FetchLead(fetchLeadObject,queryParams,headers);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "positive fetch lead", priority = 6) public void
 * fetchLead() { Lead_fetch fetchLeadObject = new Lead_fetch(); Map<String,
 * String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("session_token", session_token);
 * 
 * Response responseObject =
 * middlewareServicesObject.FetchLead(fetchLeadObject,queryParams,headers);
 * 
 * fetchLeadObject.setRefId(responseObject.jsonPath().getString("refId"));
 * fetchLeadObject.setStatusCode(responseObject.jsonPath().getInt("statusCode"))
 * ; fetchLeadObject.setLeadId(responseObject.jsonPath().getString("leadId"));
 * 
 * pan = responseObject.jsonPath().getJsonObject("business.pan").toString();
 * System.out.println("PAN : "+pan);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); // fetchLeadObject.validateResponseAgainstJSONSchema(
 * "BusinessLending/fetchLead/leadResponseSchema.json"); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid SolutionType",priority = 7) public void
 * createLead_invalidSolution_Type() throws IOException, JSchException {
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX3");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * body.put("MERCHANT_ID", mid);
 * 
 * 
 * 
 * Lead_create createLeadObject = new
 * Lead_create(P.TESTDATA.get("BusinessLending"));
 * 
 * Response responseObject =
 * middlewareServicesObject.CreateLead(createLeadObject, queryParams, headers,
 * body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid entityType",priority = 8) public void
 * createLead_invalidEntity_Type() throws IOException, JSchException {
 * 
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", ""); queryParams.put("channel", "DIY_P4B_APP");
 * queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * body.put("MERCHANT_ID", mid);
 * 
 * 
 * 
 * Lead_create createLeadObject = new
 * Lead_create(P.TESTDATA.get("BusinessLending"));
 * 
 * Response responseObject =
 * middlewareServicesObject.CreateLead(createLeadObject, queryParams, headers,
 * body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid session token",priority = 9) public void
 * createLead_invalidSessionToken() throws IOException, JSchException {
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * "aa1f36bc-7c0e-b052-9b6d18a33000");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * body.put("MERCHANT_ID", mid);
 * 
 * Lead_create createLeadObject = new
 * Lead_create(P.TESTDATA.get("BusinessLending"));
 * 
 * Response responseObject =
 * middlewareServicesObject.CreateLead(createLeadObject, queryParams, headers,
 * body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 410); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid channel",priority = 10) public void
 * createLead_invalidChannel() throws IOException, JSchException { Map<String,
 * String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * body.put("MERCHANT_ID", mid);
 * 
 * Lead_create createLeadObject = new
 * Lead_create(P.TESTDATA.get("BusinessLending"));
 * 
 * Response responseObject =
 * middlewareServicesObject.CreateLead(createLeadObject, queryParams, headers,
 * body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Invalid solution",priority = 11) public void
 * createLead_invalidSolution() throws IOException, JSchException { Map<String,
 * String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "lending"); queryParams.put("entityType",
 * "INDIVIDUAL"); queryParams.put("channel", "DIY_P4B_APP");
 * queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * body.put("MERCHANT_ID", mid);
 * 
 * Lead_create createLeadObject = new
 * Lead_create(P.TESTDATA.get("BusinessLending"));
 * 
 * Response responseObject =
 * middlewareServicesObject.CreateLead(createLeadObject, queryParams, headers,
 * body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status Code is " +
 * StatusCode); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "Positive create Lead", priority = 12) public void
 * createLead() throws IOException, JSchException {
 * 
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * body.put("MERCHANT_ID", mid);
 * 
 * Lead_create createLeadObject = new
 * Lead_create(P.TESTDATA.get("BusinessLending"));
 * 
 * Response responseObject =
 * middlewareServicesObject.CreateLead(createLeadObject, queryParams, headers,
 * body);
 * 
 * createLeadObject.setRefId(responseObject.jsonPath().getString("refId"));
 * createLeadObject.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); leadId = responseObject.jsonPath().getString("leadId");
 * System.out.println("lead id is" + " " + leadId);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); createLeadObject.validateResponseAgainstJSONSchema(
 * "BusinessLending.createLead/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "sso_token is passed as null",priority = 13) public void
 * sso_tokenAsNull() throws IOException, JSchException { triggerotp otp = new
 * triggerotp(); String expectedMsg =
 * "org.springframework.web.client.HttpClientErrorException: 400 Bad Request";
 * Response ResObject = middlewareServicesObject.triggerOtp(otp, "null"); String
 * actualMsg = ResObject.jsonPath().getString("error_message"); //
 * System.out.println("msg "+ " " +actualMsg ); int StatusCode =
 * ResObject.getStatusCode();
 * Assert.assertTrue(actualMsg.contains(expectedMsg));
 * Assert.assertEquals(StatusCode, 400); System.out.println("Status Code is " +
 * " " + StatusCode); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "positive response while triggering otp", priority = 14)
 * public void triggerOTP() throws IOException, JSchException { triggerotp otp =
 * new triggerotp(); System.out.println("Session Token : " + session_token);
 * Response responseObject = middlewareServicesObject.triggerOtp(otp,
 * session_token); //
 * otp.setStatusCode(responseObject.jsonPath().getInt("statusCode")); //
 * otp.setLeadId(responseObject.jsonPath().getString("leadId"));
 * System.out.println("Status : " +
 * responseObject.jsonPath().getString("status"));
 * otp.setStatus(responseObject.jsonPath().getString("status")); state =
 * responseObject.jsonPath().getString("state"); System.out.println("State is "
 * + state); int StatusCode = responseObject.getStatusCode();
 * System.out.println("Status Code is " + StatusCode); String expectedMsg =
 * "success"; String actualMsg = responseObject.jsonPath().getString("status");
 * Assert.assertEquals(StatusCode, 200);
 * Assert.assertTrue(actualMsg.contains(expectedMsg));
 * otp.validateResponseAgainstJSONSchema(
 * "lmsMerchantWrapperTriggerotp/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "lead id is passed as null in validate otp",priority =
 * 15) public void leadId_AsNull_validateOTP() throws IOException, JSchException
 * { validateotp validateotp = new validateotp(); Response ResObject =
 * middlewareServicesObject.validateOtp(validateotp, session_token,
 * "application/json", "", "","888888"); int StatusCode =
 * ResObject.getStatusCode(); // String expectedMsg = "failure"; // String
 * actualMsg = ResObject.jsonPath().getString("status"); //
 * Assert.assertTrue(actualMsg.contains(expectedMsg));
 * Assert.assertEquals(StatusCode, 200);
 * System.out.println("Status Code is  "+StatusCode);
 * 
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "positive response while validating otp", priority = 16)
 * public void validateOTP() throws IOException, JSchException { validateotp
 * validateotp = new validateotp();
 * 
 * Response responseObject = middlewareServicesObject.validateOtp(validateotp,
 * session_token, "application/json", leadId, state, "888888"); //
 * validateotp.setRefId(responseObject.jsonPath().getString("refId")); //
 * validateotp.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * validateotp.setLeadId(responseObject.jsonPath().getString("leadId"));
 * validateotp.setStatus(responseObject.jsonPath().getString("status")); String
 * status = validateotp.getStatus(); // leadId=validateotp.getLeadId();
 * System.out.println("lead id is" + " " + leadId);
 * System.out.println("State is " + state); int StatusCode =
 * responseObject.getStatusCode(); Assert.assertEquals(StatusCode, 200);
 * System.out.println("Status Code is " + StatusCode);
 * validateotp.validateResponseAgainstJSONSchema(
 * "lmsMerchantWrapperValidateotp/leadResponseSchema.json");
 * 
 * 
 * if (responseObject.jsonPath().getString("status").equals("failure")) {
 * System.out.
 * println("status is failure because Lms callback in different Staging for callback otp."
 * ); }
 * 
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "positive response  for callback for save otp", priority
 * = 17) public void callbackOTP() throws IOException, JSchException {
 * callBackSaveOTP callBackotp = new callBackSaveOTP(); generateJwtToken();
 * System.out.println("token is" + " " + token); Response responseObject =
 * middlewareServicesObject.callBackSaveOTP(callBackotp, leadId,
 * "business_lending", token, "no-cache", "application/json",
 * "1a9a3858-cb60-471c-b910-2ee58a4a5f24", "DIY_P4B_APP", custId);
 * callBackotp.setStatusCode(responseObject.jsonPath().getInt("statusCode"));
 * int StatusCode = responseObject.getStatusCode();
 * 
 * System.out.println("Status Code is " + StatusCode);
 * callBackotp.validateResponseAgainstJSONSchema(
 * "MerchantServicev5callbackforSaveOTP/leadResponseSchema.json");
 * Assert.assertEquals(StatusCode, 200); }
 * 
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "invalid session token is passed in ckyc api",priority =
 * 18) public void sso_AsNull_Ckyc() throws IOException, JSchException {
 * getKycStatus kycStatus = new getKycStatus(); Response ResObject =
 * middlewareServicesObject.getKycStatus(kycStatus, "INDIVIDUAL", "CLIX",
 * "Unsecured_Short_term_Loan_Simplified", "business_lending", "DIY_P4B_APP",
 * "null", "application/json"); int StatusCode = ResObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 410); System.out.println("Status Code is " +
 * " " + StatusCode);
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "invalid entity is passed in ckyc api",priority = 19)
 * public void invalid_entitytype_Ckyc() throws IOException, JSchException {
 * getKycStatus kycStatus = new getKycStatus(); Response ResObject =
 * middlewareServicesObject.getKycStatus(kycStatus, "BUSINESS", "CLIX",
 * "Unsecured_Short_term_Loan_Simplified", "business_lending", "DIY_P4B_APP",
 * "null", "application/json"); int StatusCode = ResObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 410); System.out.println("Status code is " +
 * " " + StatusCode);
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "invalid solution is passed in ckyc api",priority = 20)
 * public void invalid_solution_Ckyc() throws IOException, JSchException {
 * getKycStatus kycStatus = new getKycStatus(); Response ResObject =
 * middlewareServicesObject.getKycStatus(kycStatus, "INDIVIDUAL", "CLIX",
 * "Unsecured_Short_term_Loan_Simplified", "business", "DIY_P4B_APP", "null",
 * "application/json"); int StatusCode = ResObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 410); System.out.println("Status code is " +
 * " " + StatusCode); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "invalid channel is passed in ckyc api",priority = 21)
 * public void invalid_channel_Ckyc() throws IOException, JSchException {
 * 
 * getKycStatus kycStatus = new getKycStatus(); Response ResObject =
 * middlewareServicesObject.getKycStatus(kycStatus, "INDIVIDUAL", "CLIX",
 * "Unsecured_Short_term_Loan_Simplified", "business_lending", "DIY_P4B_",
 * "null", "application/json"); int StatusCode = ResObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 410); System.out.println("Status code is " +
 * " " + StatusCode);
 * 
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "get Ckyc details", priority = 22) public void getCkyc()
 * throws IOException, JSchException { getKycStatus kycStatus = new
 * getKycStatus(); Response responseObject =
 * middlewareServicesObject.getKycStatus(kycStatus, "INDIVIDUAL", "CLIX",
 * "Unsecured_Short_term_Loan_Simplified", "business_lending", "DIY_P4B_APP",
 * session_token, "application/json");
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); // kycStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev2LendinggetKycStatus/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "invalid entity is passed in fetch pan api",priority =
 * 23) public void invalid_entitytype_fetchPan() throws IOException,
 * JSchException { Map<String, String> queryParams = new HashMap<String,
 * String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "Business"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>(); body.put("pan",
 * pan);
 * 
 * Lead_create createLeadObject = new Lead_create(P.TESTDATA.get("FetchPan"));
 * 
 * Response ResObject = middlewareServicesObject.CreateLead(createLeadObject,
 * queryParams, headers, body);
 * 
 * int StatusCode = ResObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 500); System.out.println("Status code is " + " " + StatusCode);
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "invalid solution is passed in fetch pan api",priority =
 * 24) public void invalid_solution_fetchPan() throws IOException, JSchException
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "upgrade_mid"); queryParams.put("entityType",
 * "INDIVIDUAL"); queryParams.put("channel", "DIY_P4B_APP");
 * queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>(); body.put("pan",
 * pan);
 * 
 * Lead_create createLeadObject = new Lead_create(P.TESTDATA.get("FetchPan"));
 * 
 * Response ResObject = middlewareServicesObject.CreateLead(createLeadObject,
 * queryParams, headers, body);
 * 
 * int StatusCode = ResObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 500); System.out.println("Status code is " + " " + StatusCode); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "invalid channel is passed in fetch pan api",priority =
 * 25) public void invalid_channel_fetchPan() throws IOException, JSchException
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>(); body.put("pan",
 * pan);
 * 
 * Lead_create createLeadObject = new Lead_create(P.TESTDATA.get("FetchPan"));
 * 
 * Response ResObject = middlewareServicesObject.CreateLead(createLeadObject,
 * queryParams, headers, body);
 * 
 * int StatusCode = ResObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 500); System.out.println("Status code is " + " " + StatusCode);
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "get pan details", priority = 26) public void fetchPan()
 * throws IOException, JSchException {
 * 
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "DIY_P4B_APP"); queryParams.put("solutionTypeLevel2", "CLIX");
 * queryParams.put("solutionTypeLevel3",
 * "Unsecured_Short_term_Loan_Simplified");
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("UncleScrooge",
 * "BabaBlackSheepWeAreInShitDeep"); headers.put("session_token",
 * session_token);
 * 
 * Map<String, String> body = new HashMap<String, String>(); body.put("pan",
 * pan);
 * 
 * Lead_create panStatus = new Lead_create(P.TESTDATA.get("FetchPan"));
 * 
 * Response responseObject = middlewareServicesObject.CreateLead(panStatus,
 * queryParams, headers, body);
 * 
 * panStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * panStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")); int
 * StatusCode = responseObject.getStatusCode();
 * System.out.println("Status Code is " + StatusCode); //
 * panStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1sdMerchantleadFetchPan/leadResponseSchema.json");
 * Assert.assertEquals(StatusCode, 200); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "invalid entity is passed in fetch bank details api",priority = 27) public
 * void invalid_entitytype_fetchBankDetails() throws IOException, JSchException
 * { bank bankStatus = new bank(P.TESTDATA.get("LendingBankUpdate"));
 * 
 * Response ResObject = middlewareServicesObject.getBankDetails(bankStatus,
 * "PUBLIC", "CLIX", "Unsecured_Short_term_Loan_Simplified", "business_lending",
 * "DIY_P4B_APP", session_token,
 * "application/json","***********","SBIN0005226"); // String
 * actualMsg=ResObject.jsonPath().getString("displayMessage"); int StatusCode =
 * ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 500);
 * System.out.println("Status code is " + " " + StatusCode); String expectedMsg
 * =
 * "We are unable to process your request. Please try again after sometime to continue."
 * ; // Assert.assertTrue(actualMsg.contains(expectedMsg)); //
 * System.out.println(ResObject.toString());
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "invalid solution is passed in fetch bank details api",priority = 28) public
 * void invalid_solution_fetchBankDetails() throws IOException, JSchException {
 * bank bankStatus = new bank(P.TESTDATA.get("LendingBankUpdate")); Response
 * ResObject = middlewareServicesObject.getBankDetails(bankStatus, "INDIVIDUAL",
 * "CLIX", "Unsecured_Short_term_Loan_Simplified", "upgrade_mid", "DIY_P4B_APP",
 * session_token, "application/json","***********","SBIN0005227"); int
 * StatusCode = ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 404);
 * System.out.println("Status code is " + " " + StatusCode); // String
 * actualMsg=ResObject.jsonPath().getString("displayMessage"); // String
 * expectedMsg="We are unable to process your request. Please try again after sometime to continue."
 * ; // Assert.assertTrue(actualMsg.contains(expectedMsg)); }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "invalid channel is passed in fetch bank details api",priority = 29) public
 * void invalid_channel_fetchBankDetails() throws IOException, JSchException {
 * bank bankStatus = new bank(P.TESTDATA.get("LendingBankUpdate")); Response
 * ResObject = middlewareServicesObject.getBankDetails(bankStatus, "INDIVIDUAL",
 * "CLIX", "Unsecured_Short_term_Loan_Simplified", "business_lending",
 * "DIY_P4B", session_token, "application/json","***********","SBIN0005228");
 * int StatusCode = ResObject.getStatusCode(); Assert.assertEquals(StatusCode,
 * 500); System.out.println("Status code is " + " " + StatusCode); String
 * actualMsg=ResObject.jsonPath().getString("displayMessage"); String
 * expectedMsg="We are unable to process your request. Please try again after sometime to continue."
 * ; // Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "get Bank details", priority = 30) public void bank()
 * throws IOException, JSchException { bank bankStatus = new
 * bank(P.TESTDATA.get("LendingBankUpdate"));
 * 
 * Response responseObject = middlewareServicesObject.getBankDetails(bankStatus,
 * "INDIVIDUAL", "CLIX", "Unsecured_Short_term_Loan_Simplified",
 * "business_lending", "DIY_P4B_APP", session_token, "application/json",
 * "***********", "sbin0005226");
 * 
 * System.out.println("API Response Code : " + responseObject.getStatusCode());
 * System.out.println("ref id is :" +
 * responseObject.jsonPath().getString("refId"));
 * bankStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * bankStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")); int
 * StatusCode = responseObject.getStatusCode(); String actualMsg =
 * responseObject.jsonPath().getString("stage");
 * 
 * System.out.println("Status Code is: " + StatusCode);
 * bankStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1sdMerchantbank/leadResponseSchema.json");
 * 
 * if (actualMsg.equals("BANK_VALIDATION_PENDING")) { System.out.
 * println("BANK_VALIDATION_PENDING !! name match is failed .Please provide other bank account"
 * ); }
 * 
 * Assert.assertEquals(StatusCode, 202); Assert.assertEquals(actualMsg,
 * "BANK_VALIDATION_PENDING"); }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "invalid entityType is passed in fetch bank update lead status api",priority
 * = 31) public void invalid_entityType_fetchBankUpdateLeadStatus() throws
 * IOException, JSchException { fetchBankUpdate bankUpdateStatus = new
 * fetchBankUpdate(); Response ResObject =
 * middlewareServicesObject.fetchBankUpdate(bankUpdateStatus, "BUSINESS",
 * "BANK_DETAIL_UPDATE","pg_profile_update",session_token,"application/json",
 * "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb","BabaBlackSheepWeAreInShitDeep",mid,
 * "Unsecured_Short_term_Loan_Simplified"); int StatusCode =
 * ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 500);
 * System.out.println("Status code is " + " " + StatusCode);
 * 
 * // String actualMsg=ResObject.jsonPath().getString("displayMessage"); //
 * String
 * expectedMsg="We are unable to process your request. Please try again after sometime to continue."
 * ; // Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "invalid solution is passed in fetch bank update lead status api",priority =
 * 32) public void incorrectSolution_fetchBankUpdateLeadStatus() throws
 * IOException, JSchException { fetchBankUpdate bankUpdateStatus = new
 * fetchBankUpdate(); Response ResObject =
 * middlewareServicesObject.fetchBankUpdate(bankUpdateStatus, "INDIVIDUAL",
 * "BANK_DETAIL_UPDATE","upgrade_mid",session_token,"application/json",
 * "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb","BabaBlackSheepWeAreInShitDeep",mid,
 * "Unsecured_Short_term_Loan_Simplified"); int StatusCode =
 * ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 200);
 * System.out.println("Status code is " + " " + StatusCode);
 * 
 * // String actualMsg=ResObject.jsonPath().getString("displayMessage");
 * //String
 * expectedMsg="We are unable to process your request. Please try again after sometime to continue."
 * ; //Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "different solution is passed in fetch bank update lead status api",priority
 * = 33) public void differentSolution_fetchBankUpdateLeadStatus() throws
 * IOException, JSchException { fetchBankUpdate bankUpdateStatus = new
 * fetchBankUpdate(); Response ResObject =
 * middlewareServicesObject.fetchBankUpdate(bankUpdateStatus, "BUSINESS",
 * "BANK_DETAIL_UPDATE","business_lending",session_token,"application/json",
 * "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb","BabaBlackSheepWeAreInShitDeep",mid,
 * "Unsecured_Short_term_Loan_Simplified"); int StatusCode =
 * ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 500);
 * System.out.println("Status code is " + " " + StatusCode);
 * 
 * // String actualMsg = ResObject.jsonPath().getString("leadStatus"); // String
 * expectedMsg = "LEAD_NOT_PRESENT"; //
 * Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "Null mid is passed in fetch bank update lead status api",priority = 34)
 * public void nullMID_fetchBankUpdateLeadStatus() throws IOException,
 * JSchException { fetchBankUpdate bankUpdateStatus = new fetchBankUpdate();
 * Response ResObject =
 * middlewareServicesObject.fetchBankUpdate(bankUpdateStatus, "INDIVIDUAL",
 * "BANK_DETAIL_UPDATE","pg_profile_update",session_token,"application/json",
 * "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb","BabaBlackSheepWeAreInShitDeep","abc",
 * "Unsecured_Short_term_Loan_Simplified"); int StatusCode =
 * ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 200);
 * System.out.println("Status code is " + " " + StatusCode);
 * 
 * // String actualMsg=ResObject.jsonPath().getString("leadStatus"); // String
 * expectedMsg="LEAD_NOT_PRESENT";
 * //Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "incorrectSolutionSubType is passed in fetch bank update lead status api"
 * ,priority = 35) public void
 * incorrectSolutionSubType_fetchBankUpdateLeadStatus() throws IOException,
 * JSchException { fetchBankUpdate bankUpdateStatus = new fetchBankUpdate();
 * Response ResObject =
 * middlewareServicesObject.fetchBankUpdate(bankUpdateStatus, "BUSINESS",
 * "BANK_DETAIL","pg_profile_update",session_token,"application/json",
 * "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb","BabaBlackSheepWeAreInShitDeep",mid,
 * "Unsecured_Short_term_Loan_Simplified"); int StatusCode =
 * ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 500);
 * System.out.println("Status code is " + " " + StatusCode);
 * 
 * // String actualMsg=ResObject.jsonPath().getString("leadStatus"); // String
 * expectedMsg="LEAD_NOT_PRESENT";
 * //Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "incorrectSolutionTypeLevel3 is passed in fetch bank update lead status api"
 * ,priority = 36) public void
 * incorrectSolutionTypeLevel3_fetchBankUpdateLeadStatus() throws IOException,
 * JSchException { fetchBankUpdate bankUpdateStatus = new fetchBankUpdate();
 * Response ResObject =
 * middlewareServicesObject.fetchBankUpdate(bankUpdateStatus, "BUSINESS",
 * "BANK_DETAIL_UPDATE","pg_profile_update",session_token,"application/json",
 * "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb","BabaBlackSheepWeAreInShitDeep",mid,
 * "Unsecured_Short_term_Loan"); int StatusCode = ResObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status code is " +
 * " " + StatusCode);
 * 
 * // String actualMsg=ResObject.jsonPath().getString("leadStatus"); // String
 * expectedMsg="LEAD_NOT_PRESENT";
 * //Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "fetch update lead status", priority = 37) public void
 * fetchBankUpdateLeadStatus() throws IOException, JSchException {
 * 
 * fetchBankUpdate bankUpdateStatus = new fetchBankUpdate(); Response
 * responseObject = middlewareServicesObject.fetchBankUpdate(bankUpdateStatus,
 * "INDIVIDUAL", "BANK_DETAIL_UPDATE", "pg_profile_update", session_token,
 * "application/json", "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb",
 * "BabaBlackSheepWeAreInShitDeep", mid,
 * "Unsecured_Short_term_Loan_Simplified"); leadStatus =
 * responseObject.jsonPath().getString("leadStatus"); PGleadId =
 * responseObject.jsonPath().getString("leadId");
 * bankUpdateStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * bankUpdateStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); bankUpdateStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1profileupdateleadstatus/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "invalid entityType is passed in fetch all saved bank api",priority = 38)
 * public void invalid_entityType_fetchAllSavedBank() throws IOException,
 * JSchException { bank_fetchChild fetchSavedBank = new bank_fetchChild();
 * Response ResObject = middlewareServicesObject.fetchSavedBank(fetchSavedBank,
 * "Business", "pg_profile_update", session_token); int StatusCode =
 * ResObject.getStatusCode(); Assert.assertEquals(StatusCode, 500);
 * System.out.println("Status code is " + " " + StatusCode);
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description =
 * "invalid Solution is passed in fetch all saved bank api",priority = 39)
 * public void invalid_Solution_fetchAllSavedBank() throws IOException,
 * JSchException { bank_fetchChild fetchSavedBank = new bank_fetchChild();
 * Response ResObject = middlewareServicesObject.fetchSavedBank(fetchSavedBank,
 * "INDIVIDUAL", "", session_token); int StatusCode = ResObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status code is " +
 * " " + StatusCode);
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "fetch all saved bank", priority = 40) public void
 * fetchAllSavedBank() throws IOException, JSchException { bank_fetchChild
 * fetchSavedBank = new bank_fetchChild(); Response responseObject =
 * middlewareServicesObject.fetchSavedBank(fetchSavedBank, "INDIVIDUAL",
 * "pg_profile_update", session_token);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); // fetchSavedBank.validateResponseAgainstJSONSchema(
 * "MerchantServicev1profileupdatebank/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "create new edit bank-lead already exists", priority =
 * 41) public void leadAlreadyExist_createNewBankWorkflow() throws IOException,
 * JSchException { editBank createNewLead = new
 * editBank(P.TESTDATA.get("CreateLeadBankUpdateLending"));
 * 
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "PAYTM_APP"); queryParams.put("solutionSubType", "pg_profile_update");
 * queryParams.put("solutionTypeLevel3", "BANK_DETAIL_UPDATE");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * 
 * body.put("bankAccountHolderName", bankAccountHolderName);
 * body.put("bankAccountNumber", bankAccountNumber); body.put("ifsc", ifsc);
 * body.put("mid", null); body.put("parentLeadId", leadId);
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * 
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject = middlewareServicesObject.editBank(createNewLead,
 * queryParams, headers, body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status Code is " +
 * StatusCode);
 * 
 * 
 * } }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "create new edit bank-parent lead id as null", priority =
 * 42) public void ParentLeadIdAsNull_createNewBank() throws
 * IOException,JSchException { editBank createNewLead = new
 * editBank(P.TESTDATA.get("CreateLeadBankUpdateLending"));
 * 
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "PAYTM_APP"); queryParams.put("solutionSubType", "pg_profile_update");
 * queryParams.put("solutionTypeLevel3", "BANK_DETAIL_UPDATE");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * 
 * body.put("bankAccountHolderName", bankAccountHolderName);
 * body.put("bankAccountNumber", bankAccountNumber); body.put("ifsc", ifsc);
 * body.put("mid", mid); body.put("parentLeadId", null);
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * 
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject = middlewareServicesObject.editBank(createNewLead,
 * queryParams, headers, body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status code is " +
 * StatusCode);
 * 
 * 
 * } }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "create new edit bank-mid as null", priority = 43) public
 * void MIdAsNull_createNewBank() throws
 * IOException,JSchException,IllegalArgumentException {
 * 
 * editBank createNewLead = new
 * editBank(P.TESTDATA.get("CreateLeadBankUpdateLending"));
 * 
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "PAYTM_APP"); queryParams.put("solutionSubType", "pg_profile_update");
 * queryParams.put("solutionTypeLevel3", "BANK_DETAIL_UPDATE");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * 
 * body.put("bankAccountHolderName", bankAccountHolderName);
 * body.put("bankAccountNumber", bankAccountNumber); body.put("ifsc", ifsc);
 * body.put("mid", null); body.put("parentLeadId", leadId);
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * 
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject = middlewareServicesObject.editBank(createNewLead,
 * queryParams, headers, body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println(" Status Code is" +
 * StatusCode);
 * 
 * }
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "create new edit bank-invalid solution", priority = 44)
 * public void invalidSolution_createNewBank() throws IOException,JSchException
 * { editBank createNewLead = new
 * editBank(P.TESTDATA.get("CreateLeadBankUpdateLending"));
 * 
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "PAYTM_APP"); queryParams.put("solutionSubType", "pg_profile_update");
 * queryParams.put("solutionTypeLevel3", "BANK_DETAIL_UPDATE");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * 
 * body.put("bankAccountHolderName", bankAccountHolderName);
 * body.put("bankAccountNumber", bankAccountNumber); body.put("ifsc", ifsc);
 * body.put("mid", null); body.put("parentLeadId", leadId);
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * 
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject = middlewareServicesObject.editBank(createNewLead,
 * queryParams, headers, body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println(" Status Code is" +
 * StatusCode);
 * 
 * } }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "create new edit bank-different solution", priority = 45)
 * public void differentSolution_createNewBank() throws
 * IOException,JSchException { editBank createNewLead = new
 * editBank(P.TESTDATA.get("CreateLeadBankUpdateLending"));
 * 
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "PAYTM_APP"); queryParams.put("solutionSubType", "pg_profile_update");
 * queryParams.put("solutionTypeLevel3", "BANK_DETAIL_UPDATE");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * 
 * body.put("bankAccountHolderName", bankAccountHolderName);
 * body.put("bankAccountNumber", bankAccountNumber); body.put("ifsc", ifsc);
 * body.put("mid", null); body.put("parentLeadId", leadId);
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * 
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject = middlewareServicesObject.editBank(createNewLead,
 * queryParams, headers, body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println(" Status Code is" +
 * StatusCode);
 * 
 * } }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "create new edit bank-invalid solutionSubType", priority
 * = 46) public void invalidSolutionSubType_createNewBank() throws
 * IOException,JSchException { editBank createNewLead = new
 * editBank(P.TESTDATA.get("CreateLeadBankUpdateLending"));
 * 
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "PAYTM_APP"); queryParams.put("solutionSubType", "pg_profile_update");
 * queryParams.put("solutionTypeLevel3", "BANK_DETAIL_UPDATE");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * 
 * body.put("bankAccountHolderName", bankAccountHolderName);
 * body.put("bankAccountNumber", bankAccountNumber); body.put("ifsc", ifsc);
 * body.put("mid", null); body.put("parentLeadId", leadId);
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * 
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject = middlewareServicesObject.editBank(createNewLead,
 * queryParams, headers, body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println(" Status Code is" +
 * StatusCode);
 * 
 * } }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "create new edit bank", priority = 47) public void
 * createNewBankWorkflow() throws IOException, JSchException { editBank
 * createNewBank = new editBank(leadId);
 * 
 * 
 * if (leadStatus.equals("LEAD_NOT_PRESENT")) { editBank createNewLead = new
 * editBank(P.TESTDATA.get("CreateLeadBankUpdateLending"));
 * 
 * { Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "PAYTM_APP"); queryParams.put("solutionSubType", "pg_profile_update");
 * queryParams.put("solutionTypeLevel3", "BANK_DETAIL_UPDATE");
 * 
 * Map<String, String> body = new HashMap<String, String>();
 * 
 * body.put("bankAccountHolderName", bankAccountHolderName);
 * body.put("bankAccountNumber", bankAccountNumber); body.put("ifsc", ifsc);
 * body.put("mid", null); body.put("parentLeadId", leadId);
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * 
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject = middlewareServicesObject.editBank(createNewLead,
 * queryParams, headers, body);
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 500); System.out.println("Status Code is " +
 * StatusCode); // createNewBank.validateResponseAgainstJSONSchema(
 * "MerchantServicev1profileupdateEditbank/leadResponseSchema.json"); String
 * nameMatchStatus = responseObject.jsonPath().getString("nameMatchSuccess");
 * System.out.println(" " + PGleadId);
 * 
 * } } }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "inappropriate lead stage in upload cancelled cheque",
 * priority = 48) public void inappropriateLeadStage_uploadCancelCheque() throws
 * IOException, JSchException { uploadCancelledCheque cancelledCheque = new
 * uploadCancelledCheque();
 * 
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "UMP_WEB"); queryParams.put("solutionLeadId", childLeadId);
 * queryParams.put("solutionSubType", "BANK_DETAIL_UPDATE");
 * queryParams.put("docProvided","cancelled Cheque Photo");
 * queryParams.put("docType","Cancelled Cheque");
 * 
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject =
 * middlewareServicesObject.uploadCancelCheque(cancelledCheque,queryParams,
 * headers , fis);
 * 
 * 
 * cancelledCheque.setRefId(responseObject.jsonPath().getString("refId"));
 * cancelledCheque.setStatusCode(responseObject.jsonPath().getInt("statusCode"))
 * ; String actualMsg=responseObject.jsonPath().getString("displayMessage");
 * String expectedMsg="Upload image is not allowed, inappropriate lead stage.";
 * int StatusCode = responseObject.getStatusCode();
 * System.out.println("Status Code is " + StatusCode);
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "upload cancelled cheque", priority = 49) public void
 * uploadCancelCheque_failed() throws IOException, JSchException {
 * uploadCancelledCheque cancelledCheque = new uploadCancelledCheque();
 * 
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "pg_profile_update");
 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("channel",
 * "UMP_WEB"); queryParams.put("solutionLeadId", childLeadId);
 * queryParams.put("solutionSubType", "BANK_DETAIL_UPDATE");
 * queryParams.put("docProvided","cancelled Cheque Photo");
 * queryParams.put("docType","Cancelled Cheque");
 * 
 * 
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("UncleScrooge-Type", "BabaBlackSheepWeAreInShitDeep");
 * headers.put("session_token", session_token);
 * 
 * Response responseObject =
 * middlewareServicesObject.uploadCancelCheque(cancelledCheque,queryParams,
 * headers , fis);
 * 
 * 
 * cancelledCheque.setRefId(responseObject.jsonPath().getString("refId"));
 * cancelledCheque.setStatusCode(responseObject.jsonPath().getInt("statusCode"))
 * ;
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 400); System.out.println("Status Code is " +
 * StatusCode); // cancelledCheque.validateResponseAgainstJSONSchema(
 * "MerchantServicev2upgradeMiddoc/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * 
 * @Test(description = "fetch XMW token for OE panel", priority = 50)
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true) public void
 * getOE_XMW() { TokenXMV tokenXMW = new TokenXMV(); Response responseObject =
 * middlewareServicesObject.v1Token("9560526665", "paytm@123"); XMWToken =
 * responseObject.getHeader("Set-Cookie").toString();
 * System.out.println("XMW token is :" + XMWToken);
 * 
 * }
 * 
 * 
 * @Test(description = "perform QC on pg profile lead on OE panel", priority =
 * 51)
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * public void PGProfileLeadAction_FromOE() throws IOException, JSchException {
 * String mobileNumber = AgentNo; FetchLead v1FetchLeadObj = new
 * FetchLead(leadId); waitForLoad(5000); Response v1FetchLeadResp =
 * middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);
 * 
 * workflowStatusId = v1FetchLeadResp.jsonPath().getJsonObject(
 * "leadDetails.leadInfo.workflowStatusId").toString(); EditLead
 * PGProfileAction_Status = new EditLead(leadId,
 * P.TESTDATA.get("EditLeadLendingLead")); System.out.println("xmW token is :" +
 * XMWToken); Response responseObject =
 * middlewareServicesObject.v1EditLeadOE(PGProfileAction_Status, "SUBMIT",
 * "9560526665", "1106992015", XMWToken, "application/json");
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 200); System.out.println("Status Code is " +
 * StatusCode); // String expectedMsg = "PANEL_SUCCESS"; // String actualMsg =
 * responseObject.jsonPath().getJsonObject(
 * "leadDetails.timelineDetail[5].subStage").toString(); //
 * Assert.assertTrue(actualMsg.contains(expectedMsg));
 * 
 * 
 * 
 * 
 * }
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "fetch update lead status-check pg lead status", priority
 * = 52) public void fetchBankUpdateLeadStatus_checkPGLeadStatus() throws
 * IOException, JSchException {
 * 
 * 
 * fetchBankUpdate bankUpdateStatus = new fetchBankUpdate(); Response
 * responseObject = middlewareServicesObject.fetchBankUpdate(bankUpdateStatus,
 * "INDIVIDUAL", "BANK_DETAIL_UPDATE", "pg_profile_update", session_token,
 * "application/json", "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb",
 * "BabaBlackSheepWeAreInShitDeep", mid,
 * "Unsecured_Short_term_Loan_Simplified"); leadStatus =
 * responseObject.jsonPath().getString("leadStatus"); PGleadId =
 * responseObject.jsonPath().getString("leadId");
 * bankUpdateStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * bankUpdateStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); System.out.println("Lead status :" + leadStatus);
 * System.out.println("PG profile lead id :" + PGleadId); int StatusCode =
 * responseObject.getStatusCode(); Assert.assertEquals(StatusCode, 200);
 * System.out.println("Status Code is " + StatusCode);
 * bankUpdateStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1profileupdateleadstatus/leadResponseSchema.json");
 * 
 * 
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "fetch update lead status-check pg lead status", priority
 * = 53) public void fetchBankUpdateLeadStatus_checkPGStatus() throws
 * IOException, JSchException {
 * 
 * 
 * 
 * fetchBankUpdate bankUpdateStatus = new fetchBankUpdate(); Response
 * responseObject = middlewareServicesObject.fetchBankUpdate(bankUpdateStatus,
 * "INDIVIDUAL", "BANK_DETAIL_UPDATE", "pg_profile_update", session_token,
 * "application/json", "3dc4705d-6a4b-42dc-84e6-47d3f7c4e4cb",
 * "BabaBlackSheepWeAreInShitDeep", mid,
 * "Unsecured_Short_term_Loan_Simplified"); leadStatus =
 * responseObject.jsonPath().getString("leadStatus"); PGleadId =
 * responseObject.jsonPath().getString("leadId");
 * bankUpdateStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * bankUpdateStatus.setStatusCode(responseObject.jsonPath().getInt("statusCode")
 * ); System.out.println("Lead status :" + leadStatus);
 * System.out.println("PG profile lead id :" + PGleadId); int StatusCode =
 * responseObject.getStatusCode(); Assert.assertEquals(StatusCode, 200);
 * System.out.println("Status Code is " + StatusCode); //
 * bankUpdateStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev1profileupdateleadstatus/leadResponseSchema.json");
 * 
 * 
 * if (leadStatus.equals("LEAD_CLOSED")) { System.out.println(
 * "======================================================================================"
 * ); bank(); } else { System.out.println("pg lead is not closed"); }
 * 
 * 
 * 
 * }
 * 
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "fetch Bre status", priority = 54) public void
 * fetchBreStatus() throws IOException, JSchException { // bank(); getBreStatus
 * BREStatus = new getBreStatus(); Response responseObject =
 * middlewareServicesObject.fetchBREStatus(BREStatus, "INDIVIDUAL",
 * "DIY_P4B_APP", "CLIX", "Unsecured_Short_term_Loan_Simplified",
 * "business_lending", session_token, "application/json");
 * 
 * int StatusCode = responseObject.getStatusCode();
 * Assert.assertEquals(StatusCode, 400); System.out.println("Status Code is " +
 * StatusCode); BREStatus.validateResponseAgainstJSONSchema(
 * "MerchantServicev2lendingGetBreStatus/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "generate tnc status", priority = 55) public void
 * generateTNC() throws IOException, JSchException { dynamicTNC
 * generateTNCStatus = new dynamicTNC(); Response responseObject =
 * middlewareServicesObject.generateTNC(generateTNCStatus, "INDIVIDUAL",
 * "LENDING_MERCHANT_CLIX_UNSECURED_LOAN", session_token);
 * 
 * generateTNCStatus.setRefId(responseObject.jsonPath().getString("refId"));
 * 
 * generateTNCStatus.setStatus(responseObject.jsonPath().getString("status"));
 * String actualMsg =
 * responseObject.jsonPath().getJsonObject("meta.status").toString();
 * System.out.println("Actual msg is " + " " + actualMsg); int StatusCode =
 * responseObject.getStatusCode(); Assert.assertEquals(StatusCode, 200); String
 * expectedMsg = "success"; //
 * Assert.assertTrue(actualMsg.contains(expectedMsg));
 * System.out.println("Status Code is " + StatusCode);
 * Assert.assertEquals(StatusCode,200); //
 * generateTNCStatus.validateResponseAgainstJSONSchema(
 * "MerchantServiceloanLeadDynamicTNC/leadResponseSchema.json");
 * 
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "save tnc status", priority = 56) public void saveTNC()
 * throws IOException, JSchException { submitApplication saveTNCStatus = new
 * submitApplication(); Response responseObject =
 * middlewareServicesObject.saveTNC(saveTNCStatus, "INDIVIDUAL",
 * "LENDING_MERCHANT_CLIX_UNSECURED_LOAN", session_token, "application/json");
 * 
 * int StatusCode = responseObject.getStatusCode();
 * 
 * System.out.println("Status Code is " + StatusCode);
 * Assert.assertEquals(StatusCode, 200); //
 * saveTNCStatus.validateResponseAgainstJSONSchema(
 * "MerchantServiceLoanLeadSubmitApplication/leadResponseSchema.json");
 * 
 * }
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "file upload business status", priority = 57)
 * 
 * public void fileUpload_BusinessStatus() throws IOException, JSchException {
 * fileUpload_BusinessStatus fileUpload_Status = new
 * fileUpload_BusinessStatus();
 * 
 * 
 * // Create csv file FileWriter outputfile = new FileWriter(fileUpload);
 * 
 * // Write to CSV file which is open CSVWriter writer = new
 * CSVWriter(outputfile);
 * 
 * String[] header = {"Lead Id", "Business status ", "Rejection Reason\n"};
 * writer.writeNext(header);
 * 
 * // add data to csv String[] data1 = {leadId, "APPROVED", "N/A"};
 * writer.writeNext(data1); writer.flush(); writer.close(); Response
 * responseObject =
 * middlewareServicesObject.fileUpload_BusinessStatus(fileUpload_Status,
 * "multipart/form-data; boundary=---------------------------185267642420916896811591725747"
 * , XMWToken, fileUpload, "BUSINESS_STATUS");
 * 
 * int StatusCode = responseObject.getStatusCode();
 * System.out.println("Status Code is " + StatusCode); if (StatusCode == 200) {
 * System.out.println("File Uploaded "); } else {
 * System.out.println("File not  Uploaded "); }
 * 
 * }
 * 
 * 
 * @Owner(emailId = "<EMAIL>", isAutomated = true)
 * 
 * @Test(description = "lms callback", priority = 58) public void
 * lmsCallBack_lending() {
 * 
 * 
 * if (fileUpload.delete()) { System.out.println("File deleted successfully"); }
 * else { System.out.println("Error in file upload!!!"); } generateJwtToken();
 * 
 * Map<String, String> queryParams = new HashMap<String, String>();
 * 
 * queryParams.put("solution", "business_lending"); queryParams.put("leadId",
 * leadId);
 * 
 * 
 * Map<String, String> headers = new HashMap<String, String>();
 * headers.put("Content-Type", "application/json"); headers.put("custId",
 * custId); headers.put("channel", "DIY_P4B_APP"); headers.put("Authorization",
 * token);
 * 
 * Map<String, String> body = new HashMap<String, String>(); body.put("status",
 * "SUCCESS"); lmsCallBack_loanTap lmsCallBack_loanTapObject = new
 * lmsCallBack_loanTap(); Response responseObject =
 * middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject,
 * queryParams, headers, body); int StatusCode = responseObject.getStatusCode();
 * 
 * Assert.assertEquals(StatusCode, 400); System.out.println("Status Code is " +
 * StatusCode);
 * 
 * if (StatusCode == 200) { System.out.println("Lead Successfully closed "); }
 * 
 * // lmsCallBack_loanTapObject.validateResponseAgainstJSONSchema(
 * "MerchantService.v5/callback/lmsCallBack_loanTap/leadResponseSchema.json");
 * 
 * }
 * 
 * 
 * }
 */