package OCL.Lending.BusinessLending.SetMandate;

import OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.LendingService.enums.ProductId;
import Services.LendingService.enums.Solution;
import Services.LendingService.enums.SolutionTypeLevel2;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

public class TestMCASetMandate extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(TestShriRamMCA.class);
    Properties prop = this.configProperties();


    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
    Utilities utility=new Utilities();

    String sessionToken = "";
    String leadId="";
    String custId="1701263594";
    String consumerNumber="8860440122";
    String consumerPassword="paytm@123";
    String token="";
    String stage="";
    String feStage="";
    String userIPAddress="";
    String loanUserLatitude="";
    String loanUserLongitude="";
    String tncAdditionalParam="";
    String staticTncAcceptanceTimeStamp="";
    String lenderCustomerId="";
    String requestBodyJsonPath="";
    String Pan="**********";
    String Email="";
    String DOB="";
    String applicationId="";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String PanValidationTimeStamp="";
    String uuid="";
    String md5="";
    String code="";
    String tncName="";
    String url="";
    String uniqueIdentifier="";
    ;
    String codeSanctionLetter="";
    String tncNameSanctionLetter="";
    String urlSanctionLetter="";
    String uniqueIdentifierSanctionLetter="";
    String md5SanctionLetter="";
    String sanctionLetterAcceptanceTimestamp="";
    String kybSecondaryTNCDisplayURL="";
    String loanAgreementDate="";
    String kybTNCDisplayURL="";
    String panNameMatchTimeStamp="";
    String panNameMatchPercentage="";
    String breLastFetchDate="";
    String offerRequest="";


    Response responseObject= null;
    private String randomBankAccountNumber;
    private boolean MOCK_BRE1=false;
    private boolean MOCK_LIS_CREATE_APPLICATION=false;
    private String lenderApplicationId;
    private String currentBureau;


    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }



    @Test(description = "Verify whether there is any existing MCA Shri Ram lead present or not",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_FetchLeadDeatils()
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails("", Solution.mca_set_mandate.getSolutionType(), SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200)
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId=responseObject.jsonPath().getString("leadId");
        }

        if(responseObject.jsonPath().getInt("statusCode")==404)
        {


            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


        }

    }

    @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_DeleteExistingLead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution", Solution.mca_set_mandate.getSolutionType());
        queryParams.put("leadId",leadId);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);


        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }

    @Test(description = "Create MCA Set Mandate Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_CreateSetMandateLead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "a631eb3a-3bd6-4c44-bd76-586949bacff9");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_FICCL8515283265");
        body.put("PARENT_LENDER_ID", "15");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "365");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "1110");
        body.put("LOAN_AMOUNT_IN_NUMBER", "350000");
        body.put("PARENT_MERCHANT_ID", "AFTkYT90127981618291");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 201;
            return status;
        });



        if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.PO_BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            //Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_TYPE"),"MCA");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_ONBOARDING_JOURNEY"),"FALSE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_LEAD_ID"),"a631eb3a-3bd6-4c44-bd76-586949bacff9");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"1110");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),"RONIT ARYA");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_ID"),"85");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_NUMBER"),"PYTMMCA_FICCL8515283265");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"85");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),"Redmi");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),"77.4977");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),"SAMRAT SARDAR");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS"),"************");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ALLOWED_CALLBACK_OPERATIONS"),"[\"EMANDATE_SUCCESS\"]");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"350000");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),"27.2046");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"365");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"15");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_MERCHANT_ID"),"AFTkYT90127981618291");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_LENDER_ID"),"15");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"AFTkYT90127981618291");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"ICICI BANK");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"**********");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"ICIC0001257");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),"RONIT ARYA");

            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());

    }

    @Test(description = "Enter the bank details", dependsOnMethods = "TC003_CreateSetMandateLead", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_VerifyBankValidationPendingNode() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2());

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);

        randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "VERIFY_BANK_DETAILS");
        body.put("EMANDATE_TYPE", "Internet Banking");
        body.put("bankName", "PAYTM BANK");
        body.put("bankAccountNumber",randomBankAccountNumber);
        body.put("ifsc", "PYTM0123456");
        body.put("bankAccountHolderName", "testNameMatch");

        Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VALIDATION_PENDING")) {

            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VALIDATION_PENDING");

        }

        else {

            for (int i = 1; i < 6; i++) {
                LOGGER.info("Again hitting with same data: retry-count: " + i);
                responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VALIDATION_PENDING"))
                    break;
            }

        }


        Assert.assertEquals(responseObject.getStatusCode(),200);

        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"BANK_VALIDATION_PENDING");
        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.PO_BANK_VALIDATION_PENDING.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"558");
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"PAYTM BANK");
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),"BENE CUSTOMER NAME");



    }

    @Test(description = "update kyc name for bank details", dependsOnMethods = "TC004_VerifyBankValidationPendingNode", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateKYCNameInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2());


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);

        String requestPath="MerchantService/v2/lending/dataUpdate/UpdateKYCNameForBankRequest.json";

        Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);


        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.mca_set_mandate.getSolutionType(),SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);


        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());

        System.out.println(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"));


    }
    @Test(description = "Enter the bank details", dependsOnMethods = "TC005_UpdateKYCNameInSAI", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_VerifyBankingActionDoneNode() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2());

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);

        randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "VERIFY_BANK_DETAILS");
        body.put("EMANDATE_TYPE", "Internet Banking");
        body.put("bankName", "PAYTM BANK");
        body.put("bankAccountNumber",randomBankAccountNumber);
        body.put("ifsc", "PYTM0123456");
        body.put("bankAccountHolderName", "testNameMatch");

        Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS")) {

            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");

        }

        else {

            for (int i = 1; i < 6; i++) {
                LOGGER.info("Again hitting with same data: retry-count: " + i);
                responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
                    break;
            }

        }


        Assert.assertEquals(responseObject.getStatusCode(),200);


    }


    @Test(description = "For CLIX_TOPUP lead Bank Verification", dependsOnMethods = "TC006_VerifyBankingActionDoneNode",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC007_FetchLeadPostBankVerification() {

        try {

            Awaitility.await().atMost(30, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, Solution.mca_set_mandate.getSolutionType(), SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage());
            });

        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }


        if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.PO_BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "428");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"), "PAYTM BANK");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"), "PYTM0123456");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMANDATE_TYPE"), "Internet Banking");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ACCOUNT_HOLDER_NAME_VS_NSDL_NAME_MATCH_PERCENTAGE"), "N/A");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ACCOUNT_HOLDER_NAME_VS_CKYC_NAME_MATCH_PERCENTAGE"), "100");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_PAN_NAME_MATCH_PERCENTAGE"), "N/A");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_BANK_NAME_QC_REQUIRED"), "FALSE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_NAME_MATCH_PERCENTAGE"), "100");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_VERIFICATION_MODE"), "PENNY_DROP");
        }
    }

    @Test(description = "Verify CLIX_TOPUP Lead Emandate Callback", dependsOnMethods = "TC007_FetchLeadPostBankVerification", groups = {
            "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC008_EmandateCallback() throws InterruptedException {

        Response responseObject =  lendingBaseClassObject. callbackWithOnlyWorkflowoperation(custId,Solution.mca_set_mandate.getSolutionType(),"EMANDATE_SUCCESS");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "557");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMANDATE_SUCCESS"), "TRUE");

    }

    @Test(description = "Emandate Callback Stage Verification", dependsOnMethods = "TC008_EmandateCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC009_FetchLeadPostEmandate() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,Solution.mca_set_mandate.getSolutionType(),SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC009_FetchLeadPostEmandate",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC010_FetchLeadAllData() throws JSONException
    {
        for(int i=0;i<25;i++)
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.mca_set_mandate.getSolutionType(),SolutionTypeLevel2.CLIX_TOPUP.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
            {
                break;
            }

        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
    }


}
