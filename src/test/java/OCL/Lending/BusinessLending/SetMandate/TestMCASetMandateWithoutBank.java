package OCL.Lending.BusinessLending.SetMandate;

import OCL.Lending.BusinessLending.ShriRam.TestShriRamMCA;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.LendingService.enums.Solution;
import Services.LendingService.enums.SolutionTypeLevel2;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

public class TestMCASetMandateWithoutBank extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(TestShriRamMCA.class);
    Properties prop = this.configProperties();


    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
    Utilities utility=new Utilities();

    String sessionToken = "";
    String leadId="";
    String custId="**********";
    String consumerNumber="**********";
    String consumerPassword="paytm@123";
    String token="";
    String stage="";
    String feStage="";
    String userIPAddress="";
    String loanUserLatitude="";
    String loanUserLongitude="";
    String tncAdditionalParam="";
    String staticTncAcceptanceTimeStamp="";
    String lenderCustomerId="";
    String requestBodyJsonPath="";
    String Pan="**********";
    String Email="";
    String DOB="";
    String applicationId="";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String PanValidationTimeStamp="";
    String uuid="";
    String md5="";
    String code="";
    String tncName="";
    String url="";
    String uniqueIdentifier="";
    ;
    String codeSanctionLetter="";
    String tncNameSanctionLetter="";
    String urlSanctionLetter="";
    String uniqueIdentifierSanctionLetter="";
    String md5SanctionLetter="";
    String sanctionLetterAcceptanceTimestamp="";
    String kybSecondaryTNCDisplayURL="";
    String loanAgreementDate="";
    String kybTNCDisplayURL="";
    String panNameMatchTimeStamp="";
    String panNameMatchPercentage="";
    String breLastFetchDate="";
    String offerRequest="";


    Response responseObject= null;
    private String randomBankAccountNumber;
    private boolean MOCK_BRE1=false;
    private boolean MOCK_LIS_CREATE_APPLICATION=false;
    private String lenderApplicationId;
    private String currentBureau;


    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }



    @Test(description = "Verify whether there is any existing MCA Shri Ram lead present or not",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_FetchLeadDeatils()
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails("", Solution.mca_set_mandate.getSolutionType(), SolutionTypeLevel2.ABFL.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200)
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId=responseObject.jsonPath().getString("leadId");
        }

        if(responseObject.jsonPath().getInt("statusCode")==404)
        {


            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


        }

    }

    @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_DeleteExistingLead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution", Solution.mca_set_mandate.getSolutionType());
        queryParams.put("leadId",leadId);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);


        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }


    @Test(description = "Create MCA Set Mandate Lead without parent lead id",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_MissingParentLeadId()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("PARENT_LEAD_ID is null or empty"));

        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("PARENT_LEAD_ID is null or empty"));

    }

    @Test(description = "Create MCA Set Mandate Lead without isOnboarding journey flag",dependsOnMethods = "TC003_MissingParentLeadId",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_MissingOnboardingJourneyflag()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("IS_ONBOARDING_JOURNEY is null or empty"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("IS_ONBOARDING_JOURNEY is null or empty"));

    }

    @Test(description = "Create MCA Set Mandate Lead without loan account number",dependsOnMethods = "TC004_MissingOnboardingJourneyflag",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_MissingLoanAccountNumber()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("LOAN_ACCOUNT_NUMBER is null or empty"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("LOAN_ACCOUNT_NUMBER is null or empty"));

    }

    @Test(description = "Create MCA Set Mandate Lead without parent lender id",dependsOnMethods = "TC005_MissingLoanAccountNumber",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_MissingParentLenderId()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("PARENT_LENDER_ID is null or empty"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("PARENT_LENDER_ID is null or empty"));

    }

    @Test(description = "Create MCA Set Mandate Lead without parent product type",dependsOnMethods = "TC006_MissingParentLenderId",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_MissingParentProductType()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("PARENT_PRODUCT_TYPE is null or empty"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("PARENT_PRODUCT_TYPE is null or empty"));

    }

    @Test(description = "Create MCA Set Mandate Lead without FLOW_TYPE",dependsOnMethods = "TC007_MissingParentProductType",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_MissingFlowType()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("FLOW_TYPE is null or empty"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("FLOW_TYPE is null or empty"));

    }


    @Test(description = "Create MCA Set Mandate Lead without loan tenure",dependsOnMethods = "TC008_MissingFlowType",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_MissingLoanTenure()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid value of LOAN_TENURE"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid value of LOAN_TENURE"));

    }
    @Test(description = "Create MCA Set Mandate Lead without loan equated daily installment",dependsOnMethods = "TC008_MissingFlowType",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC010_MissingDailyInstallmentAmount()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid value of LOAN_EQUATED_DAILY_INSTALLMENT"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid value of LOAN_EQUATED_DAILY_INSTALLMENT"));

    }


    @Test(description = "Create MCA Set Mandate Lead without loan amount in number",dependsOnMethods = "TC010_MissingDailyInstallmentAmount",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC011_MissingLoanAmountInNumber()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 400;
            return status;
        });



        if(responseObject.getStatusCode()==400 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid value of LOAN_AMOUNT_IN_NUMBER"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("VALIDATION_FAILED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid value of LOAN_AMOUNT_IN_NUMBER"));

    }

    @Test(description = "Create MCA Set Mandate Lead without merchant Id",dependsOnMethods = "TC011_MissingLoanAmountInNumber",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC012_MissingMerchantId()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 417;
            return status;
        });



        if(responseObject.getStatusCode()==417 && responseObject.jsonPath().getString("status").equals("ERROR"))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("merchant id is mandatory in headers"));
            Assert.assertTrue(responseObject.jsonPath().getString("baseResponseCode").contains("LUPV2_USER_PROFILE_NOT_CREATED"));
        }

        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("merchant id is mandatory in headers"));

    }

    @Test(description = "Create MCA Set Mandate Lead with all deatils",dependsOnMethods = "TC012_MissingMerchantId",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC013_CreateSetMandateLead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.mca_set_mandate.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.ABFL.getSolutionTypeLevel2());


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PARENT_LEAD_ID", "6a70e49e-01be-4758-893b-07787bfbdd16");
        body.put("IS_ONBOARDING_JOURNEY", "FALSE");
        body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_PCHFL1139343773");
        body.put("PARENT_LENDER_ID", "1");
        body.put("PARENT_PRODUCT_TYPE", "MCA");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_TENURE", "360");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
        body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
        body.put("PARENT_MERCHANT_ID", "aABkYn42142443695219");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForMCASetMandate"));
            final boolean status = responseObject.getStatusCode() == 201;
            return status;
        });



        if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.PO_BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            //Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_TYPE"),"MCA");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_ONBOARDING_JOURNEY"),"FALSE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_LEAD_ID"),"6a70e49e-01be-4758-893b-07787bfbdd16");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"270");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),"BENE CUSTOMER NAME");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_ID"),"113");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_NUMBER"),"PYTMMCA_PCHFL1139343773");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"113");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),"Redmi");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),"77.4977");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),"Mohammad Kalim Ansari");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS"),"************");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ALLOWED_CALLBACK_OPERATIONS"),"[\"EMANDATE_SUCCESS\"]");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"60000");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),"27.2046");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"360");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_MERCHANT_ID"),"aABkYn42142443695219");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_LENDER_ID"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"aABkYn42142443695219");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"SBI  BANK");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"*********");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"SBIN0002222");
            Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),"BENE CUSTOMER NAME");

            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());

    }


    @Test(description = "Verify ABFL Lead Emandate Callback", dependsOnMethods = "TC013_CreateSetMandateLead", groups = {
            "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC014_EmandateCallback() throws InterruptedException {

        Response responseObject =  lendingBaseClassObject. callbackWithOnlyWorkflowoperation(custId,Solution.mca_set_mandate.getSolutionType(),"EMANDATE_SUCCESS");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "557");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMANDATE_SUCCESS"), "TRUE");

    }


    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC014_EmandateCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC015_FetchLeadAllData() throws JSONException
    {
        for(int i=0;i<25;i++)
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.mca_set_mandate.getSolutionType(),SolutionTypeLevel2.ABFL.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
            {
                break;
            }

        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
    }



}
