package OCL.Lending.BusinessLending.ShriRam;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.LendingService.enums.ProductId;
import Services.LendingService.enums.Solution;
import Services.LendingService.enums.SolutionTypeLevel2;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.google.gson.Gson;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.json.JSONException;
import org.slf4j.LoggerFactory;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.math.BigInteger;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

public class TestShriRamMCA  extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(TestShriRamMCA.class);
    Properties prop = this.configProperties();


    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
    Utilities utility=new Utilities();

    String sessionToken = "";
    String leadId="";
    String custId="1000110576";
    String consumerNumber="7838179383";
    String consumerPassword="paytm@123";
    String token="";
    String stage="";
    String feStage="";
    String userIPAddress="";
    String loanUserLatitude="";
    String loanUserLongitude="";
    String tncAdditionalParam="";
    String staticTncAcceptanceTimeStamp="";
    String lenderCustomerId="";
    String requestBodyJsonPath="";
    String Pan="**********";
    String Email="";
    String DOB="";
    String applicationId="";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String PanValidationTimeStamp="";
    String uuid="";
    String md5="";
    String code="";
    String tncName="";
    String url="";
    String uniqueIdentifier="";
    ;
    String codeSanctionLetter="";
    String tncNameSanctionLetter="";
    String urlSanctionLetter="";
    String uniqueIdentifierSanctionLetter="";
    String md5SanctionLetter="";
    String sanctionLetterAcceptanceTimestamp="";
    String kybSecondaryTNCDisplayURL="";
    String loanAgreementDate="";
    String kybTNCDisplayURL="";
    String panNameMatchTimeStamp="";
    String panNameMatchPercentage="";
    String breLastFetchDate="";
    String offerRequest="";


    Response responseObject= null;
    private String randomBankAccountNumber;
    private boolean MOCK_BRE1=false;
    private boolean MOCK_LIS_CREATE_APPLICATION=false;
    private String lenderApplicationId;
    private String currentBureau;
    private String tncNameEDIDeclaration;
    private String urlEDIDeclaration;
    private String uniqueIdentifierEDIDeclaration;
    private String md5EDIDeclaration;
    private String codeEDIDeclaration;


    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }



    @Test(description = "Verify whether there is any existing MCA Shri Ram lead present or not",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_FetchLeadDeatils()
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails("", Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200)
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId=responseObject.jsonPath().getString("leadId");
        }

        if(responseObject.jsonPath().getInt("statusCode")==404)
        {


            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


        }

    }

    @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_DeleteExistingLead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution", Solution.business_lending_v3.getSolutionType());
        queryParams.put("leadId",leadId);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);


        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }

    @Test(description = "Create shri ram Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_CreateShriRamLead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",Solution.business_lending_v3.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", utility.randomMobileNumberGeneratorStartWith(7));
        body.put("PRODUCT_TYPE", "MCA");
        body.put("PRODUCT_VERSION", "1");
        body.put("PRODUCT_ID", ProductId.SHRI_RAM_PID.getProductId());
        body.put("LOAN_AMOUNT_IN_NUMBER", "20000");
        body.put("LOAN_INTEREST_AMOUNT", "10");
        body.put("LOAN_AMOUNT_IN_WORDS", "Twenty Thousand");
        body.put("LOAN_EDI_IN_WORDS", "Fifty Four");
        body.put("LOAN_MIN_AMOUNT", "10000");
        body.put("LOAN_MAX_AMOUNT", "650000");
        body.put("LOAN_TENURE", "450");
        body.put("LOAN_TENURE_MIN", "450");
        body.put("LOAN_TENURE_MAX", "450");
        body.put("LOAN_TENURE_UNIT", "DAY");
        body.put("LOAN_TOTAL_INTEREST_AMOUNT","4300");
        body.put("LOAN_RATE_OF_INTEREST","33");
        body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "54");
        body.put("LOAN_PROCESSING_FEE", "800");
        body.put("LOAN_INCENTIVE", "5000");
        body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
        body.put("LOAN_INCENTIVE_PERCENTAGE", "0");
        body.put("MERCHANT_ID", "kWegv14734270521119");
        body.put("BASE_ID", "MCA_Shriram_OE_11_27eb2325");
        body.put("LOAN_OFFER_ID", "MCA_Shriram_OE_11_27eb2325");
        body.put("RISK_GRADE", "MCA|KUR134");
        body.put("PROCESSING_FEE_RATE", "4.0");
        body.put("IS_ACCEPTANCE_ABOVE_5000", true);
        body.put("IS_SI_MANDATORY", true);
        body.put("IS_RESTRICTED_MERCHANT", false);
        body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
        body.put("IS_EMANDATE_ELIGIBLE", true);
        body.put("STATIC_TNC_SETNAME", "loanstatictnc");
        body.put("LENDER_STATIC_TNC_SETNAME", "bl_ckyck_lender_consent_shriram");
        body.put("LENDING_DYNAMIC_TNC", "bl_loan_agreement_shriram");
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_shriram");
        body.put("FLOW_TYPE", "RISK");
        body.put("BUSINESS_CATEGORY", "individual services");
        body.put("BUSINESS_SUB_CATEGORY", "Other Services");
        body.put("LENDER_ID", "16");
        body.put("LENDER_NAME", "SFL");
        body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "944");
        body.put("LOAN_DISBURSAL_AMOUNT", "19056");
        body.put("TOTAL_AMOUNT_PAID_BY_BORROWER", "25244");
        body.put("ANNUAL_PERCENTAGE_RATE", "41.1");
        body.put("KYC_MODES_INITIATE_SEQUENCE", "SEARCH_BY_PAN,SEARCH_BY_AADHAAR,OFFLINE_AADHAAR,DIGILOCKER");
        body.put("BUREAU_PRIORITY", "CIBIL,EXPERIAN");
        body.put("OFFER_GENERATION_TIMESTAMP", "1696415399080");
        body.put("OFFER_ACCEPTANCE_TIMESTAMP", "1696415399080");
        body.put("DOB", LendingConstants.DOB_STASHFIN);
        body.put("PAN", Pan);
        body.put("EMAIL", Utilities.randomEmailGeneration());
        body.put("F_NAME", "TOUCH");
        body.put("M_NAME", "WOOD");
        body.put("L_NAME", "LIMITED");
        body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
        body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");

        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForShriRam"));
            final boolean status = responseObject.getStatusCode() == 201;
            return status;
        });



        if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
            //Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"106");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"20000");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),"10");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_WORDS"),"Twenty Thousand");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MIN_AMOUNT"),"10000");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"650000");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"450");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"450");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"450");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"DAY");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"33");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"54");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),"800");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE"),"5000");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_PERCENTAGE"),"0");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"kWegv14734270521119");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"MCA_Shriram_OE_11_27eb2325");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"MCA_Shriram_OE_11_27eb2325");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_GRADE"),"MCA|KUR134");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"4.0");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"loanstatictnc");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_shriram");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"bl_loan_agreement_shriram");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"bl_sanction_letter_shriram");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"16");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"),"CIBIL,EXPERIAN");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),LendingConstants.DOB_STASHFIN);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");
            userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");



        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());

    }

    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC003_CreateShriRamLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_UpdateBureauDataSetInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", Solution.business_lending_v3.getSolutionType());
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("F_NAME", "Nimesh");
        body.put("L_NAME", "Hande");
        body.put("PAN", utility.randomIndividualPANValueGenerator());
        body.put("DOB", "1981-11-21");
        body.put("EMAIL", "<EMAIL>");

        responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body, true);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());


    }

    @Test(description = "Fetch CIR", dependsOnMethods = "TC003_CreateShriRamLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_FetchCIR_Async() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(), LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();

        		
        Map<String, String> finalHeaders = headers;

        Map<String, String> finalQueryParams = queryParams;

            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.v1InitiateBureau(finalQueryParams, finalHeaders, body, prop.getProperty("AsyncBureau"));
                final boolean status = responseObject.getStatusCode() == 200;
                return status;
            });

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BUREAU_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BUREAU_INITIATED.getStage());
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
        LOGGER.info("statusCode : " + responseObject.jsonPath().getString("statusCode"));
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BUREAU_INITIATED");
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "862");


    }

    @Test(description = "Update data in SAI Table", dependsOnMethods = "TC005_FetchCIR_Async", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_LeadDataUpdateForKYC_InSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",Solution.business_lending_v3.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("PAN","**********");
        body.put("DOB", "1998-09-12");
        body.put("GENDER", "MALE");
        body.put("NSDL_NAME", "Rohan Shivaji Sonawane");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";

        responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        //Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());


    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA", dependsOnMethods = "TC006_LeadDataUpdateForKYC_InSAI", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_FetchLeadAllData_ForBureauSuccess() throws JSONException {

        try {

            Awaitility.await().atMost(30, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE1_SUCCESS.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage());
            });

        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }


        if (LendingConstants.IS_MOCK_CALLBACK && MOCK_BRE1 && responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage())) {

            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE1_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "705");

            //Hit BRE1 Callback

            LOGGER.info("Actual Callback not coming from risk so hitting BRE1 mock Callback");
            responseObject = lendingBaseClassObject.BRE1CallbackGeneric(leadId,
                    Solution.business_lending_v3.getSolutionType(), LendingConstants.DIY_P4B_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), custId, prop.getProperty("LISCreateApplicationRequest"));


        }
        try {


            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
            });
        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }

        responseObject=lendingBaseClassObject.lisCreateApplicationSuccess(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,custId,"LENDER_CREATE_APPLICATION_SUCCESS");


        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "234");
        currentBureau = responseObject.jsonPath().getString("solutionAdditionalInfo.CURRENT_BUREAU");
        lenderApplicationId = responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_APPLICATION_ID");

    }

    @Test(description = "Update data in SAI Table", dependsOnMethods = "TC007_FetchLeadAllData_ForBureauSuccess", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_LeadDataUpdateForKYC_InSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",Solution.business_lending_v3.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("PAN","**********");
        body.put("DOB", "1998-09-12");
        body.put("GENDER", "MALE");
        body.put("NSDL_NAME", "Rohan Shivaji Sonawane");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";

        responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");

    }

    @Test(description = "Verify the ABFL lead Upload SELFIE ", groups = {"Regression"}, dependsOnMethods = "TC008_LeadDataUpdateForKYC_InSAI")
    @Owner(emailId = "<EMAIL>")
    public void TC009_UploadSelfie() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), sessionToken, "selfie", "RohanOfflineAadhaar.jpg");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2021");


    }

    @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC009_UploadSelfie", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC010_VerifyUploadedSelfie() {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        if (responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

        } else {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


        }

    }


    @Test(description = "Initiate KYC using SBP", groups = {"Regression"}, dependsOnMethods = "TC010_VerifyUploadedSelfie")
    @Owner(emailId = "<EMAIL>")
    public void TC011_InitiateKYC_UsingSearchByPan() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(), LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());

        Map<String, String> headers = new HashMap<String, String>();
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("shareCode","1234");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";

        responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");


        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

    }

    @Test(description = "Verify lead details after kyc initiate", groups = {"Regression"}, dependsOnMethods = "TC011_InitiateKYC_UsingSearchByPan")
    @Owner(emailId = "<EMAIL>")
    public void TC012_FetchDataPostKYCIntiated() throws Exception {

        try {


            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage());
            });
        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }

        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_INITIATED.getStage())) {
            LOGGER.info("Actual Callback not coming from KYC so hitting KYC SBP mock Callback");

            responseObject = lendingBaseClassObject.KYCCallbackusingOfflineAdhaar(leadId, Solution.business_lending_v3.getSolutionType(), LendingConstants.DIY_P4B_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), custId);

        }


        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
            LendingBaseAPI.establishConnection();
            System.out.println(LendingBaseAPI.SelectQueryUBMId(leadId));
            LendingBaseAPI.backstageMovement(leadId, "2000");
            responseObject = lendingBaseClassObject.KYCCallbackusingOfflineAdhaar(leadId, Solution.business_lending_v3.getSolutionType(), LendingConstants.DIY_P4B_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), custId);
        }


        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "375");

    }

    @Test(description = "Verify lead details after KYC",groups={"Regression"},dependsOnMethods = "TC012_FetchDataPostKYCIntiated")
    @Owner(emailId = "<EMAIL>")
    public void TC013_FetchDataKYCInitiate() throws JSONException
    {

        try {


            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE2_REQUESTED.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_REQUESTED.getStage());
            });
        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());


    }

    @Test(description = "Verify Second BRE callback", dependsOnMethods = "TC013_FetchDataKYCInitiate",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC014_SecondBRECallback() {

        try {


            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(), SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(), LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_COMPLETED.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE2_COMPLETED.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_COMPLETED.getStage());
            });
        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }


        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
        {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", Solution.business_lending_v3.getSolutionType());
            queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,LendingConstants.JWT_RISK_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            String offerDetails="{\"avgGMVgrid\":\"G5\",\"baseId\":\"MCA_Shriram_shivangi_69d08787\",\"behaviouralScore\":7.0,\"bureau\":\"EXPERIAN\",\"bureauDecile\":\"2\",\"bureauKicker\":false,\"creditScore\":899,\"field_investigation_needed\":false,\"gmvIndex\":2,\"incentiveRate\":0.0,\"isAcceptanceAbove5000\":1,\"isBSAOffer\":false,\"isBre2Required\":false,\"isEmandateEligible\":0,\"isIncentiveAllowed\":0,\"isPaytmVintageOlderThan90d\":1,\"isRenewal\":false,\"isRestrictedMerchant\":0,\"isSiMandatory\":\"0\",\"lastFetchDate\":1696896000000,\"loanAmountCappingGrid\":\"LA5\",\"loanDownGradable\":false,\"loan_offered\":true,\"lowestGmvgrid\":\"G5\",\"maxLoanAmount\":25000.0,\"maxTenure\":450,\"mcrsDecile\":1,\"minLoanAmount\":10000.0,\"minTenure\":450,\"newOfferGenerated\":false,\"offerEndDate\":\"Tue Oct 15 00:00:00 IST 2024\",\"offerId\":\"d8d5785f-73b0-419b-ab34-132ea349ce00\",\"offerStartDate\":\"Mon Oct 09 00:00:00 IST 2023\",\"pfFeeRate\":4.0,\"productId\":\"106\",\"productVersion\":1,\"rateOfInterest\":33.0,\"renewalEnhancement\":0,\"renewalLastLoanClosureGrid\":\"RN2\",\"riskGrade\":\"MCA|KUR134\",\"roiCode\":\"R6\",\"skipMandate\":false,\"sourceOfWhitelist\":\"Risk\",\"tenureCappingGrid\":\"T4\",\"vintageCode\":\"V3\"}";

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "BRE2_SUCCESS");
            body.put("BASE_ID", "MCA_Shriram_shivangi_69d08787");
            body.put("LENDER_APPLICATION_ID", "AAMBR2310100002");
            body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
            body.put("OFFER_DETAILS", new Gson().toJsonTree(offerDetails));
            body.put("LOAN_OFFER_ID", "d8d5785f-73b0-419b-ab34-132ea349ce00");
            body.put("BRE_LAST_FETCH_DATE", "Tue Oct 10 05:30:00 IST 2023");
            body.put("BRE2_SUCCESS", "TRUE");

            String requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/mcaABFL/BRE2MockCallbackRequest.json";
            Response responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        }
        else {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_COMPLETED.getStage());
            LOGGER.info("BRE 2 Success without callback");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_COMPLETED.getStage());

        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"780");
    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC014_SecondBRECallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC015_FetchLead_AfterBRE2() throws JSONException
    {

        for(int i=0;i<25;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))


                break;

        }

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE3_REQUESTED.getStage()))

        {


            lendingBaseClassObject. callbackWithOnlyWorkflowoperation(custId,Solution.business_lending_v3.getSolutionType(),"BRE3_SUCCESS");

        }



    }

    @Test(description = "update kyc name for bank details", dependsOnMethods = "TC015_FetchLead_AfterBRE2", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC016_UpdateKYCNameInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",Solution.business_lending_v3.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);

        String requestPath="MerchantService/v2/lending/dataUpdate/UpdateKYCNameForBankRequest.json";

        Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);


        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);


        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());

        System.out.println(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"));
    }



    @Test(description = "Enter the bank details", dependsOnMethods = "TC016_UpdateKYCNameInSAI", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC017_SaveBankDetails() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", Solution.business_lending_v3.getSolutionType());
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "VERIFY_BANK_DETAILS");
        body.put("EMANDATE_TYPE", "Internet Banking");
        body.put("bankName", "PAYTM BANK");
        body.put("bankAccountNumber",randomBankAccountNumber);
        body.put("ifsc", "PYTM0123456");
        body.put("bankAccountHolderName", "testNameMatch");
        body.put("AUTHORIZED_SIGNATORY_NAME", "Shivangi Goswami");
        body.put("SETTLEMENT_BANK_ACCOUNT_NAME", "Shivangi enterprises");
        body.put("IS_SETTLEMENT_BANK_PPBL_ACCOUNT", "FALSE");


        String requestPath="MerchantService/V1/workflow/lead/bank/SaveBankWithSettlementAndAuthorizedSignatoryDetails.json";


        Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body,requestPath);

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("baseResponseCode").equals( "BANK_VERIFICATION_SUCCESS")) {

            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");

        }

        else {

            for (int i = 1; i < 6; i++) {
                LOGGER.info("Again hitting with same data: retry-count: " + i);
                responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body,requestPath);

                if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
                    break;
            }

        }


        Assert.assertEquals(responseObject.getStatusCode(),200);


    }

    @Test(description = "For Fullerton lead Bank Verification", dependsOnMethods = "TC017_SaveBankDetails",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC018_FetchLeadPostBankVerification() {
        for(int i=0;i<5;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
                break;
            }
        }
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"PAYTM BANK");
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),randomBankAccountNumber);
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.AUTHORIZED_SIGNATORY_NAME"),"Shivangi Goswami");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SETTLEMENT_BANK_ACCOUNT_NAME"),"Shivangi enterprises");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_SETTLEMENT_BANK_PPBL_ACCOUNT"),"FALSE");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_DISBURSAL_ACCOUNT_SOLE_PROP"),"FALSE");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_SETTLEMENT_SOLE_PROP"),"TRUE");


    }
    @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC018_FetchLeadPostBankVerification")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC019_GenerateLoanAgreement()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(),LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);


        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token",sessionToken);


        Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " +responseObject.getStatusCode());
        if(responseObject.jsonPath().getString("meta.status").equals("success"))
        {

            code=responseObject.jsonPath().getString("data.state.code");
            tncName=responseObject.jsonPath().getString("data.state.tncName");
            url=responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifier=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5=responseObject.jsonPath().getString("data.state.md5");
        }
        else
        {
            responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
            code=responseObject.jsonPath().getString("data.state.code");
            tncName=responseObject.jsonPath().getString("data.state.tncName");
            url=responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifier=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5=responseObject.jsonPath().getString("data.state.md5");

        }


    }

    @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC019_GenerateLoanAgreement")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC020_GenerateSanctionLetter()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(),LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
        queryParams.put("tncType", "LOAN_SANCTION_TNC");

        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token",sessionToken);


        Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " +responseObject.getStatusCode());
        if(responseObject.jsonPath().getString("meta.status").equals("success"))
        {

            codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
            tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
            urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
        }

        else
        {
            responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
            codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
            tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
            urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");

        }


    }

    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC020_GenerateSanctionLetter", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC021_UpdateActualPanInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",Solution.business_lending_v3.getSolutionType());
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("CKYC_NAME", "Shivangi Goswami");
        body.put("PAN", Pan);



        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/UpdatePanAndCKYCNameRequest.json";

        responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);


        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);


        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());


    }


    @Test(description = "Accept Loan Agreement",dependsOnMethods = "TC021_UpdateActualPanInSAI",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC022_AcceptLoanAgreement()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(),LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);


        Map<String,String> headers=new HashMap<String,String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","LOAN_AGREEMENT_ACCEPTED");
        body.put("LENDING_DYNAMIC_TNC", tncName);
        body.put("KYB_TNC_REF_NO", uniqueIdentifier);
        body.put("TNC_ACCEPTED_CODE", md5);
        body.put("TNC_ACCEPTED_VERSION", 3);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
        body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC_VERSION", 2);
        body.put("EDI_SOLE_PROP_DECLARATION_TNC", "bl_declaration_propreitor_onboarding");
        body.put("DEBIT_TYPE", "NON_DIRECT_DEBIT");
        body.put("DEBIT_TYPE_STATIC_TNC_SETNAME", "bl_ppbl_direct_debit");
        body.put("WAIT_FOR_EMANDATE", "TRUE");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/AcceptLoanAgreementWithDebitTypeAndEDI.json";

        for(int i=0;i<54;i++)
        {

            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body,requestBodyJsonPath);

            if(responseObject.getStatusCode()==200)
                break;
        }


        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.EDI_SOLE_PROP_DECLARATION_REQUIRED.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EDI_SOLE_PROP_DECLARATION_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EDI_SOLE_PROP_DECLARATION_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2353");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC"),tncNameSanctionLetter);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),tncName);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_AGREEMENT_ACCEPTED"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),tncNameSanctionLetter);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_CODE"),tncName);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WAIT_FOR_EMANDATE"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NON_PPBL_SETTLEMENT_NAME_MISMATCH"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_SETTLEMENT_BANK_PPBL_ACCOUNT"),"FALSE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_AUTHORIZED_SIGNATORY_NAME_QC_REQUIRED"),"FALSE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_SETTLEMENT_BANK_ACCOUNT_NAME_QC_REQUIRED"),"FALSE");


            lenderCustomerId=responseObject.jsonPath().getString("LENDER_CUSTOMER_ID");
            sanctionLetterAcceptanceTimestamp=responseObject.jsonPath().getString("SANCTION_LETTER_TNC_ACCEPTANCE_TIMESTAMP");
            kybSecondaryTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_SECONDARY_TNC_DISPLAY_URL");
            loanAgreementDate=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AGREEMENT_DATE");
            kybTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_TNC_DISPLAY_URL");



        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EDI_SOLE_PROP_DECLARATION_REQUIRED.getStage());





    }
    @Test(description = "Fetch EDI declaration",groups = {"Regression"},dependsOnMethods = "TC022_AcceptLoanAgreement")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC023_GenerateEDIDeclaration()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(),LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
        queryParams.put("tncType", "EDI_SOLE_PROP_DECLARATION_TNC");


        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token",sessionToken);


        Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " +responseObject.getStatusCode());
        if(responseObject.jsonPath().getString("meta.status").equals("success"))
        {

            codeEDIDeclaration=responseObject.jsonPath().getString("data.state.code");
            tncNameEDIDeclaration=responseObject.jsonPath().getString("data.state.tncName");
            urlEDIDeclaration=responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifierEDIDeclaration=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5EDIDeclaration=responseObject.jsonPath().getString("data.state.md5");
        }

        else
        {
            responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
            codeEDIDeclaration=responseObject.jsonPath().getString("data.state.code");
            tncNameEDIDeclaration=responseObject.jsonPath().getString("data.state.tncName");
            urlEDIDeclaration=responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifierEDIDeclaration=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5EDIDeclaration=responseObject.jsonPath().getString("data.state.md5");

        }


    }

    @Test(description = "Accept EDI Declaration",dependsOnMethods = "TC023_GenerateEDIDeclaration",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC024_AcceptEDIDeclaration()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(),LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2());
        queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);


        Map<String,String> headers=new HashMap<String,String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","EDI_SOLE_PROP_DECLARATION_ACCEPTED");
        body.put("EDI_SOLE_PROP_DECLARATION_TNC", tncNameEDIDeclaration);
        body.put("EDI_SOLE_PROP_DECLARATION_TNC_REF_NO", uniqueIdentifierEDIDeclaration);
        body.put("EDI_SOLE_PROP_DECLARATION_TNC_ACCEPTED_CODE", md5EDIDeclaration);
        body.put("EDI_SOLE_PROP_DECLARATION_TNC_ACCEPTED_VERSION", 2);
        body.put("IS_EDI_SOLE_PROP_DECLARATION_TNC_ACCEPTED", "TRUE");



        requestBodyJsonPath="MerchantService/V1/workflow/lead/AcceptEDIDeclarationRequest.json";

        for(int i=0;i<4;i++)
        {

            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body,requestBodyJsonPath);

            if(responseObject.getStatusCode()==200)
                break;
        }


        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.EMANDATE_REQUIRED.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"815");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC"),tncNameSanctionLetter);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),tncName);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_AGREEMENT_ACCEPTED"),"TRUE");
//					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_VERSION"),"3");
//					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC_VERSION"),"2");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),tncNameSanctionLetter);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_CODE"),tncName);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WAIT_FOR_EMANDATE"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NON_PPBL_SETTLEMENT_NAME_MISMATCH"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_SETTLEMENT_BANK_PPBL_ACCOUNT"),"FALSE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_AUTHORIZED_SIGNATORY_NAME_QC_REQUIRED"),"FALSE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_SETTLEMENT_BANK_ACCOUNT_NAME_QC_REQUIRED"),"FALSE");


            lenderCustomerId=responseObject.jsonPath().getString("LENDER_CUSTOMER_ID");
            sanctionLetterAcceptanceTimestamp=responseObject.jsonPath().getString("SANCTION_LETTER_TNC_ACCEPTANCE_TIMESTAMP");
            kybSecondaryTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_SECONDARY_TNC_DISPLAY_URL");
            loanAgreementDate=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AGREEMENT_DATE");
            kybTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_TNC_DISPLAY_URL");



        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());

    }


    @Test(description = "Verify ABFL Lead Emandate Callback", dependsOnMethods = "TC024_AcceptEDIDeclaration", groups = {
            "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC025_EmandateCallback() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", Solution.business_lending_v3.getSolutionType());

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("status", "EMANDATE_SUCCESS");

        Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

    }

    @Test(description = "Emandate Callback Stage Verification", dependsOnMethods = "TC025_EmandateCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC026_FetchLeadPostEmandate() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.EMANDATE_SUCCESS.getStage())) {



            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");

        }

        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {



            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.PREDISBURSAL_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"312");

        }

    }

    @Test(description = "If PDC Callback is not received manually hit callback",dependsOnMethods = "TC026_FetchLeadPostEmandate",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC027_VerifyPDCCallback() throws JSONException
    {

        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(),LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();

        for(int i=0;i<10;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_PENDING.getStage()))
                break;
        }



        //PDC Callback


        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage()))
        {
            body.put("workflowOperation","LOAN_APPLICATION_ACCEPTED");
            body.put("PDC_REASON_ID", "7");
            body.put("PDC_USER_MESSAGE", "Disbursal Done");
            body.put("BUSINESS_STATUS", "APPROVED_BY_LMS");
            body.put("PDC_SYSTEM_MESSAGE", "Direct Approval of Loan Account is enabled");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PDCLoanApplicationPendingCallbackRequest.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());

    }



    @Test(description = "Update lead details",dependsOnMethods = "TC027_VerifyPDCCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC028_VerifyLeadStage_LMSSubmitApplicationJob()
    {

        for(int i=0;i<40;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
                break;

        }



        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"346");

    }
    @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC028_VerifyLeadStage_LMSSubmitApplicationJob",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC029_LMSDataCallback() throws JSONException
    {

        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,Solution.business_lending_v3.getSolutionType(),LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_JWT_Secret);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        for(int i=0;i<10;i++){
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
                break;
        }
        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
        {
            body.put("workflowOperation","LMS_APPLICATION_APPROVED");
            body.put("LOAN_ACCOUNT_NUMBER", "PYTMMCA_ABFL"+Utilities.randomLendingLoanAccountNumberGenerator());
            body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
            body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
            body.put("LENDER_LOAN_ACCOUNT_NUMBER", "DXW-M10126-*********");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LMSSubmitApplicationCallbackRequest.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"330");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_CREATED_ON"),"*************");
            }
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC029_LMSDataCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC030_FetchLeadAllData() throws JSONException
    {
        for(int i=0;i<35;i++)
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,Solution.business_lending_v3.getSolutionType(),SolutionTypeLevel2.SHRIRAM.getSolutionTypeLevel2(),LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
            {
                break;
            }

        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
    }



}
