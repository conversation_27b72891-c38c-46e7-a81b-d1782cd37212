package OCL.Lending.BusinessLending;

import Request.MerchantService.v1.TokenXMV;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.opencsv.CSVWriter;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MCAMigrationSSFB extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(MCAMigrationSSFB.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1001644263";
		 String consumerNumber="7771118638";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
		 String uuid="";
		 String md5="";
		 String code="";
		 String tncName="";
		 String url="";
		 String uniqueIdentifier="";
	;
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
		 String kybSecondaryTNCDisplayURL="";
		 String loanAgreementDate="";
		 String kybTNCDisplayURL="";
		 String panNameMatchTimeStamp="";
		 String panNameMatchPercentage="";
		 String breLastFetchDate="";
		 

		 Response responseObject= null;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing stashfin lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDeatils()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("leadId",leadId);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreateBTDistributionPiramalLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
	    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  	   body.put("PRODUCT_TYPE", "MCA");
		  	   body.put("PRODUCT_VERSION", "1");
		  	   body.put("PRODUCT_ID","10000020");
			   body.put("LOAN_AMOUNT_IN_NUMBER", "60000");
			   body.put("LOAN_INTEREST_AMOUNT", "2000");
			   body.put("LOAN_AMOUNT_IN_WORDS", "Sixty Thousand");
			   body.put("LOAN_MIN_AMOUNT", "10000");
			   body.put("LOAN_MAX_AMOUNT", "300000");
			   body.put("LOAN_TENURE", "720");
		  	   body.put("LOAN_TENURE_MIN", "90");
		  	   body.put("LOAN_TENURE_MAX", "720");
		  	   body.put("LOAN_TENURE_UNIT", "DAY");
			   body.put("LOAN_RATE_OF_INTEREST","20");
		  	   body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
			   body.put("LOAN_PROCESSING_FEE", "30000");
			   body.put("LOAN_INCENTIVE", "5000");
			   body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
			   body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
			   body.put("MERCHANT_ID", "PmqhWE02197439774596");
			   body.put("BASE_ID", "mca_1001644263_3bd73505");
			   body.put("LOAN_OFFER_ID", "mca_1001644263_3bd73505");
			   body.put("RISK_GRADE", "MCA|GHB64");
			   body.put("PROCESSING_FEE_RATE", "1.0");
			   body.put("IS_ACCEPTANCE_ABOVE_5000", true);
			   body.put("IS_SI_MANDATORY", true);
			   body.put("IS_RESTRICTED_MERCHANT", true);
			   body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
			   body.put("IS_EMANDATE_ELIGIBLE", true);
			   body.put("STATIC_TNC_SETNAME", "loanstatictnc");
			   body.put("LENDER_STATIC_TNC_SETNAME", "bl_ckyck_lender_consent_ssfb");
			   body.put("LENDING_DYNAMIC_TNC", "bl_loan_agreement_ssfb");
			   body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_ssfb");
			   body.put("FLOW_TYPE", "RISK");
			   body.put("LENDER_ID", "7");
			   body.put("IS_ADDITIONAL_DATA_REQUIRED", "true");
		  	 
			   requestBodyJsonPath="MerchantService/V1/workflow/Lead/CreateLeadRequestMCAMigration.json";
		  	  
		  	  
			   
			
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"10000020");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"60000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),"2000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_WORDS"),"Sixty Thousand");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MIN_AMOUNT"),"10000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"300000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"720");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"90");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"720");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"DAY");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"20");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"270");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),"30000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE"),"5000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_ELIGIBLE"),"200");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_PERCENTAGE"),"4");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"PmqhWE02197439774596");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"mca_1001644263_3bd73505");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"mca_1001644263_3bd73505");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_GRADE"),"MCA|GHB64");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"1.0");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"loanstatictnc");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_ssfb");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"bl_loan_agreement_ssfb");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"bl_sanction_letter_ssfb");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"7");
			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateBTDistributionPiramalLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		    
		
		    }
		  
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC005_UpdateLeadBasicDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","BASIC_DETAILS");
		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
		  	   body.put("PAN", "**********");
		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
		  	   
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/MCABasicDetailRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
				       
				        
				        leadId=responseObject.jsonPath().getString("leadId");
				        custId=responseObject.jsonPath().getString("custId");
				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
				        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				  
			    
		     
			  
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_UpdateLeadBasicDetails",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

		        lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
		        
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		    
		
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC007_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "BHAIRAVI");
			body.put("L_NAME", "LATASREE");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "600024");
			body.put("PAN", "**********");
			body.put("DOB", "1979-10-05");
			body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());
			
			}
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC007_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC008_FetchCIR()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		   
		
		 	  
		 	 for(int i=0;i<10;i++)
			  {
				  responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);
			
			 
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
		        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


		        bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
		        bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
		        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
		        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
		      
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_FetchCIR",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC009_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<25;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		       
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"234");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
            }
		   
		
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC009_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC010_UpdateExistingDetailsInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "TOUCH");
			body.put("L_NAME", "LIMITED");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "110096");
			body.put("PAN", Pan);
			body.put("DOB", LendingConstants.DOB_STASHFIN);
			body.put("EMAIL", Email);
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
			
			}
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC010_UpdateExistingDetailsInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC011_FetchLeadAllData() throws JSONException, InterruptedException
		    {
			
			 
//			 ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
//			 final CountDownLatch latch = new CountDownLatch(1);
//			 
//			 
//			Runnable myrun= new Runnable() {
//
//					@Override
//					public void run() {
//						
//						
//						for(int i=0;i<2;i++)
//						  {
//						   System.out.println("Thread started "+i);
//						  
//						   latch.countDown();
//						  
//						   responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//						
//						 
//						  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
//							try {
//								latch.await();
//							} catch (InterruptedException e) {
//								// TODO Auto-generated catch block
//								e.printStackTrace();
//							}
//							  executorService.shutdownNow();
//							  break;
//						  
//						  }
//	            	 
//					}
//	             };
//
////           executorService.execute(new Runnable() {
////
////				@Override
////				public void run() {
////				lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
////					
////				}
////            	 
////             });
////             
//             
//             executorService.scheduleAtFixedRate(myrun,  0, 60, TimeUnit.SECONDS) ;
			 
			  
			  //if in 10 seconds OE gets  a callback then ok move to node 707-->396 (KYC_IN_PROGRESS)
			  //if it doesn't means callback ni aai so hit callback
       
			  Thread.sleep(10000);
			  
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
		
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
				
			  {
				  
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");  
				  
			  }
			  
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
					
			  {
				  
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");  
			        
			        responseObject=lendingBaseClassObject.BRE1CallbackforMCA(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,custId);
				  
			        
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");  
			  }
			  
			    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
	  
		    }
		  
		  
		  @Test(description = "Upload selfie",groups = {"Regression"},dependsOnMethods = "TC011_FetchLeadAllData")
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC012_UploadSelfie() throws InterruptedException
		    {
			    
		  	   
		  	 Response responseObject= lendingBaseClassObject.utilityForDocumentUpload("selfie",leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,sessionToken);
		  	
		  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
	   	    
	        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Document uploaded successfully.");
		    Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
		    uuid=responseObject.jsonPath().getString("uuid");
		    
		    Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"),false);
		    LOGGER.info("All docs uploaded : " +responseObject.jsonPath().getBoolean("allDocsUploaded"));
		    Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"),false);
		    Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"),true);
		  	 
		  	
		    }
		 
		
		 @Test(description = "Upload Customer Photo",groups = {"Regression"},dependsOnMethods = "TC012_UploadSelfie")
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC013_UploadCustomerPhoto() throws InterruptedException
		    {
			    
		  	   
		  	 Response responseObject= lendingBaseClassObject.utilityForDocumentUpload("customerPhoto",leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,sessionToken);
		  	
		  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
	   	    
	        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Document uploaded successfully.");
		    Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
		    uuid=responseObject.jsonPath().getString("uuid");
		    
		    Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"),false);
		    LOGGER.info("All docs uploaded : " +responseObject.jsonPath().getBoolean("allDocsUploaded"));
		    Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"),false);
		    Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"),true);
		  	 
		  	
		    }
		 
		 @Test(description = "Verify the  details of Uploaded Document",groups = {"Regression"},dependsOnMethods = "TC013_UploadCustomerPhoto")
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC014_VerifyUploadedDocument()
		    {
			  	      
			 
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			
		    
				List<Object> docTypes=responseObject.jsonPath().getList("documents.docType");
				Assert.assertTrue(docTypes.contains("selfie"));
				Assert.assertTrue(docTypes.contains("others"));
				
				List<Object> docProvided=responseObject.jsonPath().getList("documents.docProvided");
				Assert.assertTrue(docProvided.contains("selfie"));
				Assert.assertTrue(docProvided.contains("customerPhoto"));
			   
		      
		    }
		 
		 @Test(description = "CKYC Callback",groups = {"Regression"},dependsOnMethods = "TC014_VerifyUploadedDocument")
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC015_CKYCCallback()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
	    	  queryParams.put("leadId",leadId);
	    	  
	    	  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId,LendingConstants.LMS_SECRET);
	    	 			 			  
	    	   Map<String,String> headers=new HashMap<String,String>();
	    	    headers.put("Authorization",token);
			    headers.put("Content-Type","application/json");
		        headers.put("custId",custId);
		     
	    	   Map<String,Object> body = new HashMap<String, Object>();
	    	   body.put("CKYC_PAN", "**********");
		  	   body.put("CKYC_DOB", "1979-10-05");
		  	   body.put("CKYC_ATTEMPT_TIMESTAMP", "1633347388366");
		  	   body.put("CKYC_RESPONSE_TIMESTAMP", "1633347388370");
		  	   body.put("statusMessage","CKYC_VERIFIED");
		  	   body.put("status", "SUCCESS");
		  	   body.put("cKycId", "4353435454356");
		  	   body.put("firstName", "TOUCH");
		  	   body.put("secondName", "WOOD");
		  	   body.put("thirdName", "LIMITED");
		  	   body.put("email", "<EMAIL>");
		  	   body.put("type", "SELFIE");
		  	   body.put("percentage", "100");
		  	   body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
		  	   body.put("addressline2", "MAYUR VIHAR PHASE 1");
		  	   body.put("city", "DELHI");
		  	   body.put("state", "EAST DELHI");
		  	   body.put("pincode", "243001");
		  	   body.put("dob", LendingConstants.DOB_PP_MINI);
		  	   body.put("gender", "Female");
		  	   body.put("pan", Pan);
		  	   body.put("isPanSearchCkycSuccess", true);
		  	   body.put("ckycSuccessMode", "SEARCH_BY_PAN");
		  	   
		  	  requestBodyJsonPath="MerchantService/v2/lending/dataUpdate/KycCallbackWithNewKeysRequest.json";
		  	
		  	   Response responseObject= lendingBaseClassObject.ckycCallback(queryParams, headers,body, requestBodyJsonPath,  true);
		  	  
		  	   lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
		       Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
		       Assert.assertEquals(responseObject.jsonPath().getString("oeStage"),LendingLeadStages.PAN_VERIFIED.getStage());
		     		
			 		   
		    }
		 //SECOND BRE Callback
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC015_CKYCCallback",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC016_VerifyLeadStage()
		    {
			  
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				
				 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
				
			  {
				  
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");  
				  
			  }
			  
			  
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","ADDITIONAL_DATA_CAPTURED");
		  	   body.put("RELIGION", "Hindu");
		  	   body.put("GENDER", "Female");
		  	   body.put("MARITAL_STATUS", "MARRIED");
		  	   body.put("SPOUSE_NAME", "ABCD TEST");
		  	   body.put("FATHER_NAME", "Vinay");
		  	   body.put("MOTHER_NAME", "Archana");
		  	   
		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/AdditionalDataCaptureMCARequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"386");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SELFIE_MATCH_PERCENTAGE"),"100");
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_ssfb");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SPOUSE_NAME"),"ABCD TEST");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED_INITIAL_VALUE"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FATHER_NAME"),"Vinay");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PAN_SEARCH_CKYC_SUCCESS"),"true");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE"),"100");
				     
				        panNameMatchTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_TIMESTAMP");
				        panNameMatchPercentage=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE");
				        breLastFetchDate=responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_LAST_FETCH_DATE");
				        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
			 
		    }
		 
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC015_CKYCCallback",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC016_AdditionalDataCapture()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","ADDITIONAL_DATA_CAPTURED");
		  	   body.put("RELIGION", "Hindu");
		  	   body.put("GENDER", "Female");
		  	   body.put("MARITAL_STATUS", "MARRIED");
		  	   body.put("SPOUSE_NAME", "ABCD TEST");
		  	   body.put("FATHER_NAME", "Vinay");
		  	   body.put("MOTHER_NAME", "Archana");
		  	   
		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/AdditionalDataCaptureMCARequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"386");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SELFIE_MATCH_PERCENTAGE"),"100");
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_ssfb");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SPOUSE_NAME"),"ABCD TEST");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED_INITIAL_VALUE"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FATHER_NAME"),"Vinay");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PAN_SEARCH_CKYC_SUCCESS"),"true");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE"),"100");
				     
				        panNameMatchTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_TIMESTAMP");
				        panNameMatchPercentage=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_NAME_MATCH_PERCENTAGE");
				        breLastFetchDate=responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_LAST_FETCH_DATE");
				        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
			 
		    }
		  @Test(description = "update kyc name for bank details", dependsOnMethods = "TC016_AdditionalDataCapture", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC017_UpdateKYCNameInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("CKYC_NAME", LendingConstants.CKYC_NAME);
			
			Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
			
			}
			
			@Test(description = "Enter the bank details", dependsOnMethods = "TC017_UpdateKYCNameInSAI", groups = {
				"Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC018_SaveBankDetails() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
			queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
			headers.put("Content-Type", "application/json");
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("bankName", "PAYTM BANK");
			body.put("bankAccountNumber", "************");
			body.put("ifsc", "PYTM0123456");
			body.put("bankAccountHolderName", "Shivangi Goswami");
			
			Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
			
			if (responseObject.getStatusCode() == 200) {
			
				Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
			
			}
			
			else {
				
				for (int i = 1; i < 4; i++) {
					LOGGER.info("Again hitting with same data: retry-count: " + i);
					responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
					
					if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
						break;
				}
			
			}
			
		
				Assert.assertEquals(responseObject.getStatusCode(),200);
				
			
			}
			
			  @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC018_SaveBankDetails")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC019_GenerateLoanAgreement()
			    {
			 Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
			 queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
			 queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
			
			   				 			  
			  Map<String,String> headers=new HashMap<String,String>();
			  headers.put("session_token",sessionToken);
	     
		   
		  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		  	
		 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
		 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
		 	{
		 	
		    code=responseObject.jsonPath().getString("data.state.code");
		    tncName=responseObject.jsonPath().getString("data.state.tncName");
		    url=responseObject.jsonPath().getString("data.state.url");
		    uniqueIdentifier=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
		    md5=responseObject.jsonPath().getString("data.state.md5");
		 	}
		 	
			    }
	 	
			  @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC019_GenerateLoanAgreement")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC020_GenerateSanctionLetter()
			    {
			 Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
			 queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
			 queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
			 queryParams.put("tncType", "LOAN_SANCTION_TNC");
			   				 			  
			  Map<String,String> headers=new HashMap<String,String>();
			  headers.put("session_token",sessionToken);
	     
		   
		  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		  	
		 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
		 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
		 	{
		 	
		 	codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
		 	tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
		 	urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
		 	uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
		 	md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
		 	}
			  
		 	
			    }
			  @Test(description = "Accept Loan Agreement",dependsOnMethods = "TC020_GenerateSanctionLetter",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC021_AcceptLoanAgreement()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
			      queryParams.put("solutionTypeLevel2", LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2);
			      queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
					   	
				  
		    	  Map<String,String> headers=new HashMap<String,String>();
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("session_token", sessionToken);
			       headers.put("Content-Type", "application/json");
			        
			       Map<String,Object> body = new HashMap<String, Object>();
			  	   body.put("workflowOperation","LOAN_AGREEMENT_ACCEPTED");
			  	   body.put("LENDING_DYNAMIC_TNC", tncName);
			  	   body.put("KYB_TNC_REF_NO", uniqueIdentifier);
			  	   body.put("TNC_ACCEPTED_CODE", md5);
			  	   body.put("TNC_ACCEPTED_VERSION", 3);
			  	   body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
			  	   body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
			  	   body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
			  	   body.put("SECONDARY_ACCEPTED_TNC_VERSION", 2);
			  	   body.put("WAIT_FOR_EMANDATE", "TRUE");
			  
			  	  
				   
			  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/LoanAgreementAcceptWithsanctionRequest.json";
				 
				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body,requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"815");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC"),tncNameSanctionLetter);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),tncName);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_AGREEMENT_ACCEPTED"),"TRUE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_VERSION"),"3");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC_VERSION"),"2");
				     
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),tncNameSanctionLetter);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_CODE"),tncName);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WAIT_FOR_EMANDATE"),"TRUE");


				        lenderCustomerId=responseObject.jsonPath().getString("LENDER_CUSTOMER_ID");
				        sanctionLetterAcceptanceTimestamp=responseObject.jsonPath().getString("SANCTION_LETTER_TNC_ACCEPTANCE_TIMESTAMP");
				        kybSecondaryTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_SECONDARY_TNC_DISPLAY_URL");
				        loanAgreementDate=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AGREEMENT_DATE");
				        kybTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_TNC_DISPLAY_URL");
				     
				    
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
				  
			    }
			  
			  @Test(description = "Verify Emandate Callback", dependsOnMethods = "TC021_AcceptLoanAgreement", groups = {
				"Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC022_EmandateCallback() throws InterruptedException {
					Map<String, String> queryParams = new HashMap<String, String>();
					queryParams.put("leadId", leadId);
					queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
				
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
				
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("status", "EMANDATE_SUCCESS");
				
					Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
				
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
				
				}
			  
			  @Test(description = "Sheet upload from panel", dependsOnMethods = "TC022_EmandateCallback", groups = {
				"Regression" })
		      @Owner(emailId = "<EMAIL>", isAutomated = true)
		     public void TC023_UploadSheetONPanel() throws InterruptedException, IOException {
				  
				  
		   
		   
		    for(int i=0;i<5;i++)
			 {
				 
		    	 responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				 
		    	 if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_PENDING.getStage()))
				   break;
			 }
		    
		    if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.EMANDATE_SUCCESS.getStage()))
		  
		    {
			TokenXMV tokenXMW = new TokenXMV();
			Response responseObject = MiddlewareServicesObject.v1Token("7771216290", "paytm@123");
			String XMWToken = responseObject.getHeader("Set-Cookie").toString();
		
			System.out.println("XMW token is :" + XMWToken);
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Content-Type", "multipart/form-data");
			headers.put("Cookie", XMWToken);
		
			File fileUpload = new File(
					"src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/BusinessSampleFile.csv");
			FileWriter outputfile = new FileWriter(fileUpload);
			CSVWriter writer = new CSVWriter(outputfile);
			String[] header = { "Lead Id", "Business status ", "Rejection Reason\n" };
			writer.writeNext(header);
			String[] data1 = { leadId, "APPROVED", "N/A" };
			writer.writeNext(data1);
			writer.flush();
			writer.close();
		
			responseObject = lendingBaseClassObject.uploadSheetFromPanel(headers, fileUpload, "BUSINESS_STATUS");
			if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
					.contentEquals(" has been successfully uploaded")) {
				LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
		
			}
			
			
		    }
		    
		    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
		    
		   

	}
			  
			  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC023_UploadSheetONPanel",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC025_LMSDataCallback() throws JSONException
			    {
				  
				  Map<String,String> queryParams=new HashMap<String,String>();
					
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  
		    	   Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       
			      
			  	   
			  	 
			  	 for(int i=0;i<30;i++)
				 {
					 
			  		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					 
			  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
					   break;
				 } 
			  	 
			  	 
			  	 
			  	 //PDC Callback
			  	 
			   
		  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage()))
		  		{
		  	       body.put("workflowOperation","LOAN_APPLICATION_PENDING");
			  	   body.put("PDC_REASON_ID", "7");
			  	   body.put("PDC_USER_MESSAGE", "Disbursal on hold, this could take upto 3 working days for processing");
			  	   body.put("BUSINESS_STATUS", "PENDING_BY_LMS");
			  	   body.put("PDC_SYSTEM_MESSAGE", "Direct Approval of Loan Account is disabled");
			  	   
			  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PDCLoanApplicationPendingCallbackRequest.json";   
		    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
				
		  			
		  		}
			  
		  		
		  		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			  	 
			     if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
			     {		
			    	
				       body.put("workflowOperation","LMS_APPLICATION_APPROVED");
				  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
				  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
				  	   body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
				  	   body.put("LENDER_LOAN_ACCOUNT_NUMBER", "*************");
				  	   
			    	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LMSSubmitApplicationCallbackRequest.json";   
			    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
					
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"330");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_CREATED_ON"),"*************");
						  }
					  
				    
			     }
			     
			     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
			    }  
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC025_LMSDataCallback",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC026_FetchLeadAllData() throws JSONException
			    {
				  
				  for(int i=0;i<25;i++)
				  {
				   
				
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SSFB_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
				  {
					  break;
				  }
				  
				  }
				  
		         
			     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
			    
			
			    }
			  
			
}
