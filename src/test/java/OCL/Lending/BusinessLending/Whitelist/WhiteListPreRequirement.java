package OCL.Lending.BusinessLending.Whitelist;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;

public class WhiteListPreRequirement {


    static String csvFileName="pl_ABFL_1700315975_1.csv";
    static String productTypeMCA="mca";
    static  String productTypePL="pl";
    static String productTypePP="pp";
    static String productTypeEMI="emi";



    //scp cd/Deksptop/MCA_Shivangi_5.csv shivangi.goswami@**************:/tmp
    public static void executeSCPCommand(String username, String host, String privateKeyPath, String localFile, String remoteDir) {
        try {
            System.out.println("Executing SCP Command...");
            System.out.println("Local file: " + localFile);
            System.out.println("Remote directory: " + remoteDir);

            ProcessBuilder processBuilder = new ProcessBuilder(
                    "scp",
                    "-i", privateKeyPath,
                    localFile,
                    username + "@" + host + ":" + remoteDir
            );

            Process process = processBuilder.start();

            BufferedReader stdInput = new BufferedReader(new InputStreamReader(process.getInputStream()));
            BufferedReader stdError = new BufferedReader(new InputStreamReader(process.getErrorStream()));

            // Read the output from the command
            System.out.println("Standard output of the command:\n");
            String line;
            while ((line = stdInput.readLine()) != null) {
                System.out.println(line);
            }

            // Read any errors from the attempted command
            System.out.println("Standard error of the command (if any):\n");
            while ((line = stdError.readLine()) != null) {
                System.out.println(line);
            }

            int exitCode = process.waitFor();
            System.out.println("SCP command exited with code " + exitCode);

        } catch (Exception e) {
            e.printStackTrace();
        }


            try {
                JSch jsch = new JSch();
                jsch.addIdentity(privateKeyPath);
                System.out.println("Attempting to connect to the server...");

                Session session = jsch.getSession(username, host, 22);

                // Avoid asking for key confirmation
                java.util.Properties config = new java.util.Properties();
                config.put("StrictHostKeyChecking", "no");
                session.setConfig(config);

                session.connect();
                System.out.println("Connected to the server successfully.");

                // Assuming you want to execute some command
                String command1 = "cd /tmp"; // Replace with the command you want to run

                String command2 = "aws s3 cp ./"+csvFileName+ " s3://lending-platform/risk/stage/nifi/whitelist/input/"+productTypePL+"/";

                Channel channel = session.openChannel("exec");
                //The && ensures that command2 runs only if command1 is successful
                ((ChannelExec) channel).setCommand((command1 + " && " + command2));

                channel.setInputStream(null);
                ((ChannelExec) channel).setErrStream(System.err);

                InputStream in = channel.getInputStream();
                channel.connect();
                System.out.println("Executing the command: " + command1 + " && " + command2);

                byte[] tmp = new byte[1024];
                while (true) {
                    while (in.available() > 0) {
                        int i = in.read(tmp, 0, 1024);
                        if (i < 0) break;
                        System.out.print(new String(tmp, 0, i));
                    }
                    if (channel.isClosed()) {
                        if (in.available() > 0) continue;
                        System.out.println("Command execution completed with exit status: " + channel.getExitStatus());
                        break;
                    }
                }
                channel.disconnect();
                session.disconnect();
                System.out.println("Disconnected from the server.");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    public static void main(String[] args) {

        String username = "shivangi.goswami"; // SSH username
        String host = "**************";  // Server IP
        String privateKeyPath = "src/test/java/OCL/Lending/BusinessLending/Whitelist/id_rsa"; // Path to your private key
        String localFile = "src/test/java/OCL/Lending/BusinessLending/Whitelist/CSV/"+csvFileName; // Local file path
        String remoteDir = "/tmp/";      // Remote directory path





        executeSCPCommand(username, host, privateKeyPath, localFile, remoteDir);

    }


}