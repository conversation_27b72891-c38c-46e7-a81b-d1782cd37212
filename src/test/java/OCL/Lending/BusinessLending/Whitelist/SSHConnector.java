package OCL.Lending.BusinessLending.Whitelist;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelExec;

import java.io.File;
import java.io.InputStream;

public class SSHConnector {

    public static void main(String[] args) {
        String host = "**************"; // Replace with your server's IP
        String user = "shivangi.goswami";    // Replace with your SSH username


        String privateKey = "src/test/java/OCL/Lending/BusinessLending/Whitelist/id_rsa"; // Path to your private key

        try {
            JSch jsch = new JSch();
            jsch.addIdentity(privateKey);
            System.out.println("Attempting to connect to the server...");

            Session session = jsch.getSession(user, host, 22);

            // Avoid asking for key confirmation
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);

            session.connect();
            System.out.println("Connected to the server successfully.");

            // Assuming you want to execute some command
            String command1 = "cd /tmp"; // Replace with the command you want to run
            String command2="aws s3 cp ./MCA_Shivangi_5.csv s3://lending-platform/risk/stage/nifi/whitelist/input/mca/";
            Channel channel = session.openChannel("exec");
            //The && ensures that command2 runs only if command1 is successful
            ((ChannelExec) channel).setCommand((command1 + " && " + command2));

            channel.setInputStream(null);
            ((ChannelExec) channel).setErrStream(System.err);

            InputStream in = channel.getInputStream();
            channel.connect();
            System.out.println("Executing the command: " + command1 + " && " + command2);

            byte[] tmp = new byte[1024];
            while (true) {
                while (in.available() > 0) {
                    int i = in.read(tmp, 0, 1024);
                    if (i < 0) break;
                    System.out.print(new String(tmp, 0, i));
                }
                if (channel.isClosed()) {
                    if (in.available() > 0) continue;
                    System.out.println("Command execution completed with exit status: " + channel.getExitStatus());
                    break;
                }
            }
            channel.disconnect();
            session.disconnect();
            System.out.println("Disconnected from the server.");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}