package OCL.Lending.BusinessLending.Whitelist;

import io.restassured.RestAssured;
import static io.restassured.RestAssured.given;
import io.restassured.http.ContentType;
import static org.hamcrest.Matchers.*;

public class WhiteListPostAPI {
    public static void main(String[] args) {
        // Set the base URI for REST Assured
        RestAssured.baseURI = "http://10.167.253.150:10055";

        // Parameterize the file name

        String fileName = "pl_ABFL_1700315975_1.csv"; // This can be changed or taken as user input


        // Define the JSON payload with the parameterized file name
        String payload = String.format("{\n" +
                "  \"action\": \"whitelist\",\n" +
                "  \"source\": {\n" +
                "    \"filePath\": \"/tmp/%s\",\n" +
                "    \"headerPresent\": \"true\",\n" +
                "    \"validateData\": \"true\",\n" +
                "    \"destination\": {\n" +
                "      \"name\": \"es\"\n" +
                "    }\n" +
                "  }\n" +
                "}", fileName);

        // Perform the POST request and validate the response
        given()
                .header("cache-control", "no-cache")
                .header("postman-token", "b0379f88-be82-0e81-a74b-1a7954582fae")
                .contentType(ContentType.JSON)
                .body(payload)
                .log().all()  // Log the request
                .when()
                .post("/lending/risk/stage1/internal/v1/whitelistingData")
                .then()
                .log().all()  // Log the response
                .assertThat()
                .statusCode(200) // Asserting the status code
                .body("status", equalTo(200)) // Asserting the status field in the response body
                .body("message", containsString("Whitelist task started with job id:")); // Asserting part of the message field
        // Further validations can be added as needed
    }
}