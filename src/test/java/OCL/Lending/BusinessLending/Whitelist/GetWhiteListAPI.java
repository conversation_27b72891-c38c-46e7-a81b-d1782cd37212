package OCL.Lending.BusinessLending.Whitelist;

import io.restassured.RestAssured;
import static io.restassured.RestAssured.given;



public class GetWhiteListAPI {

    public static void main(String[] args) {
    // Parameterize product_type


    String productType = "mca"; // This can be changed or taken as user input
    String customerId = "1700490671"; // This can also be parameterized if needed



    // Set the base URI for REST Assured
    RestAssured.baseURI = "http://10.167.253.150:10055";

    // Perform the GET request and validate the response
    given()
            .queryParam("product_type", productType)
            .queryParam("customer_id", customerId)
            .log().all()  // Log the request
            .when()
            .get("/lending/risk/stage1/internal/v1/whitelist/details")
            .then()
            .log().all()  // Log the response
            .statusCode(200); // Asserting the status code, replace with the expected code
    // Further validations can be added as needed
}
}