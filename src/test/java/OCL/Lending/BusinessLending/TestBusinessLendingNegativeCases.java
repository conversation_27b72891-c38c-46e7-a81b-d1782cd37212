//package OCL.Lending.BusinessLending;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingConstants;
//import Services.LendingService.LendingLeadStages;
//import Request.MerchantService.v1.TokenXMV;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//
//import com.opencsv.CSVWriter;
//import com.paytm.apitools.util.annotations.Owner;
//import io.restassured.response.Response;
//import org.apache.log4j.Logger;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import java.io.File;
//import java.io.FileWriter;
//import java.io.IOException;
//import java.math.BigInteger;
//import java.sql.SQLException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//public class TestBusinessLendingNegativeCases extends LendingBaseAPI{
//	
//	
//	private static final Logger LOGGER = Logger.getLogger(TestBusinessLendingNegativeCases.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
//
//	String sessionToken = "";
//	String leadId = "";
//	String custId = "";
//	String agentNumber = "8529536254";
//	String agentPassword = "paytm@123";
//	String token = "";
//	String uuid = "";
//	String ckycStage = "";
//	String loanOffered = "";
//	String maxLoanAmount = "";
//	String authorisedMonthlyLimit = "";
//	String stage = "";
//	String code = "";
//	String tncName = "";
//	String url = "";
//	String uniqueIdentifier = "";
//	String md5 = "";
//	String codeSanctionLetter = "";
//	String tncNameSanctionLetter = "";
//	String urlSanctionLetter = "";
//	String uniqueIdentifierSanctionLetter = "";
//	String md5SanctionLetter = "";
//	public static final String WORKFLOW_VERSION = "V2";
//	String LMS_SECRET = "fd61f785-e867-471b-90c6-1447b4331712";
//
//	Map<String, String> commonHeaders;
//
//	@BeforeClass()
//	public void intitializeInputData() throws IOException {
//
//		LOGGER.info(" Before Suite Method for Agent Login ");
//		sessionToken = ApplicantToken(agentNumber, agentPassword);
//		LOGGER.info("Applicant Token for Lending : " + sessionToken);
//		commonHeaders = setcommonHeaders();
//
//	}
//
//	@Test(description = "Delete all existing leads of the number", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC001_DeleteExistingLead_ForMCAClix() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("mobile", agentNumber);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683");
//
//		lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
//	}
//
//	@Test(description = "Verify whether there is any existing lead present or not", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC002_FetchLeadDeatils() {
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//				sessionToken, "LEAD_NOT_PRESENT");
//
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");
//	}
//	
//	@Test(description = "Create MCA Lead without passing entity type INDIVIDUAL", groups = { "Regression" ,"Negative"})
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC003_CreateMCALead_WithoutPassing_EntityType() {
//		
//		LOGGER.info("Not passing query parameter: entityType in URL");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//	
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "loanStaticTnc");
//		body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//		body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//		body.put("LOAN_TENURE", "1095");
//		body.put("LOAN_RATE_OF_INTEREST", "36");
//		body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//		body.put("LOAN_PROCESSING_FEE", "500.0");
//		body.put("LOAN_INCENTIVE", "5000");
//		body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//		body.put("LOAN_INCENTIVE_PERCENTAGE", "0.0");
//		body.put("LOAN_INTEREST_AMOUNT", "50000");
//		body.put("LOAN_AMOUNT_IN_WORDS", "One Lakh only");
//		body.put("MERCHANT_ID", "PLpFDq33777742650473");
//		body.put("USER_IP_ADDRESS", "*************");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("RISK_GRADE", "MCA|CAB40");
//		body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//		body.put("PROCESSING_FEE_RATE", 1.0);
//		body.put("IS_SI_MANDATORY", true);
//		body.put("IS_RESTRICTED_MERCHANT", true);
//		body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//		body.put("BASE_ID", "MCA_1107227275");
//		body.put("IS_EMANDATE_ELIGIBLE", true);
//		body.put("LOAN_MIN_AMOUNT", "10000");
//		body.put("LOAN_MAX_AMOUNT", "500000");
//		body.put("LOAN_TENURE_MIN", "1095");
//		body.put("LOAN_TENURE_MAX", "1095");
//		body.put("PRODUCT_TYPE", "MCA");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("PRODUCT_ID", "5");
//		body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//		
//		
//	}
//
//		@Test(description = "Create MCA Lead without passing solution type business_lending",  dependsOnMethods = "TC003_CreateMCALead_WithoutPassing_EntityType",groups = { "Regression" ,"Negative"})
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC004_CreateMCALead_WithoutPassing_Solution() {
//			
//			LOGGER.info("Not passing query parameter: solution in URL");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("tnCSetName", "loanStaticTnc");
//			body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//			body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//			body.put("LOAN_TENURE", "1095");
//			body.put("LOAN_RATE_OF_INTEREST", "36");
//			body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//			body.put("LOAN_PROCESSING_FEE", "500.0");
//			body.put("LOAN_INCENTIVE", "5000");
//			body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//			body.put("LOAN_INCENTIVE_PERCENTAGE", "0.0");
//			body.put("LOAN_INTEREST_AMOUNT", "50000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "One Lakh only");
//			body.put("MERCHANT_ID", "PLpFDq33777742650473");
//			body.put("USER_IP_ADDRESS", "*************");
//			body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//			body.put("RISK_GRADE", "MCA|CAB40");
//			body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//			body.put("PROCESSING_FEE_RATE", 1.0);
//			body.put("IS_SI_MANDATORY", true);
//			body.put("IS_RESTRICTED_MERCHANT", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//			body.put("BASE_ID", "MCA_1107227275");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("LOAN_MIN_AMOUNT", "10000");
//			body.put("LOAN_MAX_AMOUNT", "500000");
//			body.put("LOAN_TENURE_MIN", "1095");
//			body.put("LOAN_TENURE_MAX", "1095");
//			body.put("PRODUCT_TYPE", "MCA");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("PRODUCT_ID", "5");
//			body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//			Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("Lead creation is blocked in this case");
//			
//			
//		}
//			@Test(description = "Create MCA Lead without passing solutionTypeLevel2 CLIX", dependsOnMethods = "TC004_CreateMCALead_WithoutPassing_Solution",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC005_CreateMCALead_WithoutPassing_SolutionTypeLevel2() {
//				
//				LOGGER.info("Not passing query parameter: solutionTypeLevel2 in URL");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "1095");
//				body.put("LOAN_RATE_OF_INTEREST", "36");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "0.0");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "One Lakh only");
//				body.put("MERCHANT_ID", "PLpFDq33777742650473");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "MCA_1107227275");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "500000");
//				body.put("LOAN_TENURE_MIN", "1095");
//				body.put("LOAN_TENURE_MAX", "1095");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//				
//				
//
//
//	}
//			
//			@Test(description = "Create MCA Lead without passing solutionTypeLevel3 'Unsecured_Short_term_Loan_Simplified'",  dependsOnMethods = "TC005_CreateMCALead_WithoutPassing_SolutionTypeLevel2",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC006_CreateMCALead_WithoutPassing_SolutionTypeLevel3() {
//				
//				LOGGER.info("Not passing query parameter: solutionTypeLevel3 in URL");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "1095");
//				body.put("LOAN_RATE_OF_INTEREST", "36");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "0.0");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "One Lakh only");
//				body.put("MERCHANT_ID", "PLpFDq33777742650473");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "MCA_1107227275");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "500000");
//				body.put("LOAN_TENURE_MIN", "1095");
//				body.put("LOAN_TENURE_MAX", "1095");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//					
//				lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//				
//				
//			}
//			@Test(description = "Create MCA Lead without passing channel 'DIY_P4B_APP'", dependsOnMethods = "TC006_CreateMCALead_WithoutPassing_SolutionTypeLevel3",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC007_CreateMCALead_WithoutPassing_Channel() {
//				
//				LOGGER.info("Not passing query parameter: channel in URL");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "1095");
//				body.put("LOAN_RATE_OF_INTEREST", "36");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "0.0");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "One Lakh only");
//				body.put("MERCHANT_ID", "PLpFDq33777742650473");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "MCA_1107227275");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "500000");
//				body.put("LOAN_TENURE_MIN", "1095");
//				body.put("LOAN_TENURE_MAX", "1095");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//					LOGGER.info("Lead creation is blocked in this case");
//				
//			}
//			
//			
//			@Test(description = "Create MCA Lead without passing header session token", dependsOnMethods = "TC007_CreateMCALead_WithoutPassing_Channel", groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC008_CreateMCALead_WithoutPassing_sessionToken_Header() {
//				
//				LOGGER.info("Not passing header session token");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Content-Type", "application/json;charset=utf-8");
//
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "1095");
//				body.put("LOAN_RATE_OF_INTEREST", "36");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "0.0");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "One Lakh only");
//				body.put("MERCHANT_ID", "PLpFDq33777742650473");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "MCA_1107227275");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "500000");
//				body.put("LOAN_TENURE_MIN", "1095");
//				body.put("LOAN_TENURE_MAX", "1095");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//			
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//				LOGGER.info("Lead creation is blocked in this case");
//			
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing Loan Processing Fees", dependsOnMethods = "TC008_CreateMCALead_WithoutPassing_sessionToken_Header",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC009_CreateMCALead_WithoutPassing_LoanProcessionFees() {
//				
//				LOGGER.info("Not passing Loan Processing Fees");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//			
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_PROCESSING_FEE ");
//			
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing Loan Rate Of Interest", dependsOnMethods = "TC009_CreateMCALead_WithoutPassing_LoanProcessionFees",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC010_CreateMCALead_WithoutPassing_LoanRateOfInterest() {
//				
//				LOGGER.info("Not passing Loan Processing Fees");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//			
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//			
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_RATE_OF_INTEREST ");
//			
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing Loan Amount Number",dependsOnMethods = "TC010_CreateMCALead_WithoutPassing_LoanRateOfInterest", groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC011_CreateMCALead_WithoutPassing_LoanAmountNumber() {
//				
//				LOGGER.info("Not passing Loan Amount Number");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//			
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//			
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_AMOUNT_IN_NUMBER ");
//			
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing Loan Amount Number", dependsOnMethods = "TC011_CreateMCALead_WithoutPassing_LoanAmountNumber",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC012_CreateMCALead_WithoutPassing_LoanTenure() {
//				
//				LOGGER.info("Not passing Loan Tenure");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//			
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//
//			
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_TENURE ");
//			
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing Loan Equated Daily Installment", dependsOnMethods = "TC012_CreateMCALead_WithoutPassing_LoanTenure",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC013_CreateMCALead_WithoutPassing_LoanEquatedDailyInstallment() {
//				
//				LOGGER.info("Not passing Loan Equated Daily Installment");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//			
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//
//			
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_EQUATED_DAILY_INSTALLMENT ");
//			
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing Loan Interest Amount", dependsOnMethods = "TC013_CreateMCALead_WithoutPassing_LoanEquatedDailyInstallment",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC014_CreateMCALead_WithoutPassing_LoanInterestAmount() {
//				
//				LOGGER.info("Not passing Loan Interest Amount");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//			
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_INTEREST_AMOUNT ");
//			
//			
//			}
//			@Test(description = "Create MCA Lead without passing Loan Amount In Words", dependsOnMethods = "TC014_CreateMCALead_WithoutPassing_LoanInterestAmount", groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC015_CreateMCALead_WithoutPassing_LoanAmountInWords() {
//				
//				LOGGER.info("Not passing Loan Amount In Words");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//			
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_AMOUNT_IN_WORDS is null or empty");
//			
//			
//			}
//			@Test(description = "Create MCA Lead without passing IS_PAYTM_VINTAGE_OLDER_THAN_90D",dependsOnMethods = "TC015_CreateMCALead_WithoutPassing_LoanAmountInWords", groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC016_CreateMCALead_WithoutPassing_IS_PAYTM_VINTAGE_OLDER_THAN_90D() {
//				
//				LOGGER.info("Not passing IS_PAYTM_VINTAGE_OLDER_THAN_90D");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//			
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//			
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "IS_PAYTM_VINTAGE_OLDER_THAN_90D is null or empty");
//			
//			
//			}
//			
//			
//			
//			@Test(description = "Create MCA Lead without passing BASE_ID", dependsOnMethods = "TC016_CreateMCALead_WithoutPassing_IS_PAYTM_VINTAGE_OLDER_THAN_90D",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC017_CreateMCALead_WithoutPassing_BASE_ID() {
//				
//				LOGGER.info("Not passing BASE_ID");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//			
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "BASE_ID is null or empty");
//			
//			}
//			@Test(description = "Create MCA Lead without passing PRODUCT_TYPE", dependsOnMethods = "TC017_CreateMCALead_WithoutPassing_BASE_ID",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC018_CreateMCALead_WithoutPassing_PRODUCT_TYPE() {
//				
//				LOGGER.info("Not passing PRODUCT_TYPE");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//				
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				
//				body.put("PRODUCT_VERSION", "1");
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_TYPE is null or empty");
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing PRODUCT_VERSION", dependsOnMethods = "TC018_CreateMCALead_WithoutPassing_PRODUCT_TYPE", groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC019_CreateMCALead_WithoutPassing_PRODUCT_VERSION() {
//				
//				LOGGER.info("Not passing PRODUCT_VERSION");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//				
//
//
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//			
//				body.put("PRODUCT_ID", "5");
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_VERSION is null or empty");
//			
//			}
//			
//			@Test(description = "Create MCA Lead without passing PRODUCT_ID", dependsOnMethods = "TC019_CreateMCALead_WithoutPassing_PRODUCT_VERSION",groups = { "Regression" ,"Negative"})
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC020_CreateMCALead_WithoutPassing_PRODUCT_ID() {
//				
//				LOGGER.info("Not passing PRODUCT_ID");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//				
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("tnCSetName", "loanStaticTnc");
//				body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//				body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//				body.put("LOAN_TENURE", "360");
//				body.put("LOAN_RATE_OF_INTEREST", "25");
//				body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//				body.put("LOAN_PROCESSING_FEE", "500.0");
//				body.put("LOAN_INCENTIVE", "5000");
//				body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//				body.put("LOAN_INCENTIVE_PERCENTAGE", "4");
//				body.put("LOAN_INTEREST_AMOUNT", "50000");
//				body.put("LOAN_AMOUNT_IN_WORDS", "Two Lakh only");
//				body.put("MERCHANT_ID", "YkJqwn15590080691602");
//				body.put("USER_IP_ADDRESS", "*************");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("RISK_GRADE", "MCA|CAB40");
//				body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//				body.put("PROCESSING_FEE_RATE", 1.0);
//				body.put("IS_SI_MANDATORY", true);
//				body.put("IS_RESTRICTED_MERCHANT", true);
//				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//				body.put("BASE_ID", "test_ssfb_11124");
//				body.put("IS_EMANDATE_ELIGIBLE", true);
//				body.put("LOAN_MIN_AMOUNT", "10000");
//				body.put("LOAN_MAX_AMOUNT", "100000");
//				body.put("LOAN_TENURE_MIN", "90");
//				body.put("LOAN_TENURE_MAX", "360");
//				body.put("PRODUCT_TYPE", "MCA");
//				body.put("PRODUCT_VERSION", "1");
//			
//				body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//				body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_mca_clix");
//
//
//				Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_ID is null or empty");
//			
//			}
//				@Test(description = "Create MCA Lead ", dependsOnMethods = "TC020_CreateMCALead_WithoutPassing_PRODUCT_ID", groups = { "Regression" ,"Negative"})
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC021_CreateMCALead_HappyCase() {
//					
//		
//					
//					Map<String, String> queryParams = new HashMap<String, String>();
//					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//					queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//					queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//					queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//					queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//				
//					Map<String, String> headers = new HashMap<String, String>();
//					headers = commonHeaders;
//
//
//
//					Map<String, Object> body = new HashMap<String, Object>();
//					body.put("tnCSetName", "loanStaticTnc");
//					body.put("WORKFLOW_VERSION", LendingConstants.V2_WORKFLOW_VERSION);
//					body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//					body.put("LOAN_TENURE", "1095");
//					body.put("LOAN_RATE_OF_INTEREST", "36");
//					body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "270");
//					body.put("LOAN_PROCESSING_FEE", "500.0");
//					body.put("LOAN_INCENTIVE", "5000");
//					body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
//					body.put("LOAN_INCENTIVE_PERCENTAGE", "0.0");
//					body.put("LOAN_INTEREST_AMOUNT", "50000");
//					body.put("LOAN_AMOUNT_IN_WORDS", "One Lakh only");
//					body.put("MERCHANT_ID", "PLpFDq33777742650473");
//					body.put("USER_IP_ADDRESS", "*************");
//					body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//					body.put("RISK_GRADE", "MCA|CAB40");
//					body.put("IS_ACCEPTANCE_ABOVE_5000", true);
//					body.put("PROCESSING_FEE_RATE", 1.0);
//					body.put("IS_SI_MANDATORY", true);
//					body.put("IS_RESTRICTED_MERCHANT", true);
//					body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
//					body.put("BASE_ID", "MCA_1107227275");
//					body.put("IS_EMANDATE_ELIGIBLE", true);
//					body.put("LOAN_MIN_AMOUNT", "10000");
//					body.put("LOAN_MAX_AMOUNT", "500000");
//					body.put("LOAN_TENURE_MIN", "1095");
//					body.put("LOAN_TENURE_MAX", "1095");
//					body.put("PRODUCT_TYPE", "MCA");
//					body.put("PRODUCT_VERSION", "1");
//					body.put("PRODUCT_ID", "5");
//					body.put("LENDING_DYNAMIC_TNC", "LoanVettedNewTnc");
//					body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_clix");
//				
//					Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//					if (responseObject.getStatusCode() == 200) {
//						LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//						leadId = responseObject.jsonPath().getString("leadId");
//						Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//						
//					}
//
//					else {
//						LOGGER.info("Try to hit the API again");
//						responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, LendingConstants.BUSINESS_LENDING_SOLUTION,LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//						LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//						leadId = responseObject.jsonPath().getString("leadId");
//						Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//					
//					}
//
//					
//				}
//				
//				@Test(description = "Verify the data of lead created", dependsOnMethods = "TC021_CreateMCALead_HappyCase", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC022_FetchTheCreatedLeadDeatils() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, LendingLeadStages.LEAD_CREATED.getStage());
//			custId = responseObject.jsonPath().getString("custId");
//			System.out.println(custId);
//			LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//			Assert.assertEquals(
//					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_ACCEPTANCE_ABOVE_5000"),
//					"true");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_TENURE_MAX"),
//					"1095");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),
//					"STATIC_LOAN_OFFER_TNC");
//			Assert.assertEquals(
//					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ORIGINAL_LOAN_PROCESSING_FEE"),
//					"500.0");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),
//					"50000");
//			Assert.assertEquals(
//					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ORIGINAL_LOAN_RATE_OF_INTEREST"),
//					"36");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_AMOUNT_IN_WORDS"),
//					"One Lakh only");
//			
//			}
//				
//
//				@Test(description = "Verify the callback for PPBL OTP without passing headers", dependsOnMethods = "TC022_FetchTheCreatedLeadDeatils", groups = {
//					"Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC023_PPBLOTPCallback_Without_Passing_Headers() throws InterruptedException {
//				
//				LOGGER.info("Not passing headers");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//				Map<String, String> headers = new HashMap<String, String>();
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "PPBL_TNC_VERIFIED");
//				body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//				body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//				
//				Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//				Thread.sleep(1000);
//				
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//				
//				}
//				
//			@Test(description = "Verify the callback for PPBL OTP without passing leadid", dependsOnMethods = "TC023_PPBLOTPCallback_Without_Passing_Headers", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC024_PPBLOTPCallback_WithoutPassing_LeadId() throws InterruptedException {
//			
//			LOGGER.info("Not passing query parameter: leadId in URL");
//					
//			Map<String, String> queryParams = new HashMap<String, String>();
//			
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "DIY_P4B_APP");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "PPBL_TNC_VERIFIED");
//			body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//			body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//			
//			Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//			Thread.sleep(1000);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			
//			}
//			
//			@Test(description = "Verify the callback for PPBL OTP without passing solution", dependsOnMethods = "TC024_PPBLOTPCallback_WithoutPassing_LeadId",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC025_PPBLOTPCallback_WithoutPassing_solution() throws InterruptedException {
//		
//		LOGGER.info("Not passing query parameter: solution in URL");
//				
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "DIY_P4B_APP");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		Thread.sleep(1000);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		
//		}
//			
//			
//
//			@Test(description = "Verify the callback for PPBL OTP without AuthorizationToken",  dependsOnMethods = "TC025_PPBLOTPCallback_WithoutPassing_solution", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC026_PPBLOTPCallback_WithoutPassing_AuthorizationToken() throws InterruptedException {
//		
//		LOGGER.info("Not passing query parameter: AuthorizationToken in Headers");
//				
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		
//        headers.put("Content-Type","application/json");
//        headers.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
//        headers.put("custId",custId);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		Thread.sleep(1000);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		
//		}
//			@Test(description = "Verify the callback for PPBL OTP without passing custid in headers", dependsOnMethods = "TC026_PPBLOTPCallback_WithoutPassing_AuthorizationToken", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC027_PPBLOTPCallback_Without_Passing_custid_Header() throws InterruptedException {
//				
//		LOGGER.info("Not passing cust id in headers");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization",token);
//        headers.put("Content-Type","application/json");
//	    headers.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
//
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		Thread.sleep(1000);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//
//		
//		}
//			
//			@Test(description = "Verify the callback for PPBL OTP without passing Status Key in Request Body" ,  dependsOnMethods = "TC027_PPBLOTPCallback_Without_Passing_custid_Header", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC028_PPBLOTPCallback_Without_StatusKey_InRequestBody() throws InterruptedException {
//		
//		LOGGER.info("Not passing status in body");
//				
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "DIY_P4B_APP");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		Thread.sleep(1000);
//		
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//
//		
//		}
//			
//			
//		
//			
//			@Test(description = "Verify the callback for PPBL OTP",  dependsOnMethods = "TC028_PPBLOTPCallback_Without_StatusKey_InRequestBody", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC029_PPBLOTPCallback_HappyCase() throws InterruptedException {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "DIY_P4B_APP");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "PPBL_TNC_VERIFIED");
//			body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//			body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//			
//			Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//			Thread.sleep(1000);
//			
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			
//			}
//			
//			@Test(description = "Verify the data of lead created",dependsOnMethods = "TC029_PPBLOTPCallback_HappyCase", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC030_FetchTheCreatedLeadDeatils() {
//		
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//				sessionToken, LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
//		custId = responseObject.jsonPath().getString("custId");
//				
//		}
//			
//			
//			@Test(description = "Add Basic details without passing entity type as INDIVIDUAL in headers",dependsOnMethods = "TC030_FetchTheCreatedLeadDeatils",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC031_AddBasicDetails_WithouPassing_EntityType() {
//				
//			LOGGER.info("Not passing Entity type in URL");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//	
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing SolutionTypeLevel2 as CLIX in headers", dependsOnMethods = "TC031_AddBasicDetails_WithouPassing_EntityType",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC032_AddBasicDetails_WithouPassing_SolutionTypeLevel2() {
//				
//			LOGGER.info("Not passing SolutionTypeLevel2 as CLIX in URL");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//			
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing Solution as business_lending in headers", dependsOnMethods = "TC032_AddBasicDetails_WithouPassing_SolutionTypeLevel2", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC033_AddBasicDetails_WithouPassing_Solution() {
//				
//			LOGGER.info("Not passing Solution as business_lending  in URL");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing SolutionTypeLevel3 as Unsecured_Short_term_Loan_Simplified in headers", dependsOnMethods = "TC033_AddBasicDetails_WithouPassing_Solution",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC034_AddBasicDetails_WithouPassing_SolutionTypeLevel3() {
//				
//			LOGGER.info("Not passing SolutionTypeLevel3 as Unsecured_Short_term_Loan_Simplified in URL");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//			
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing Channel as DIY_P4B_APP in headers",dependsOnMethods = "TC034_AddBasicDetails_WithouPassing_SolutionTypeLevel3", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC035_AddBasicDetails_WithouPassing_Channel() {
//				
//			LOGGER.info("Not passing Channel as DIY_P4B_APP  in URL");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing session Token in Headers",dependsOnMethods = "TC035_AddBasicDetails_WithouPassing_Channel",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC036_AddBasicDetails_WithouPassing_sessionToken_InHeaders() {
//				
//			LOGGER.info("Not passing session Token in Headers");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//		
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing workflowSubOperation in request body",dependsOnMethods = "TC036_AddBasicDetails_WithouPassing_sessionToken_InHeaders",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC037_AddBasicDetails_WithouPassing_workflowSubOperation_InBody() {
//				
//			LOGGER.info("Not passing workflowSubOperation in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			//Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_PROCESSING_FEE ");
//		
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing DOB in request body", dependsOnMethods = "TC037_AddBasicDetails_WithouPassing_workflowSubOperation_InBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC038_AddBasicDetails_WithouPassing_DOB_InBody() {
//				
//			LOGGER.info("Not passing DOB in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//		
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "DOB is null or empty");
//		
//		
//			
//			}
//			
//			//JSON Parsing exception: try to create json at runtime for this
//			
//			/*
//			 * @Test(description =
//			 * "Add Basic details without passing EMAIL in request body", groups = {
//			 * "Regression" })
//			 * 
//			 * @Owner(emailId = "<EMAIL>", isAutomated = true) public
//			 * void TC039_AddBasicDetails_WithouPassing_EMAIL_InBody() {
//			 * 
//			 * LOGGER.info("Not passing EMAIL in request body");
//			 * 
//			 * Map<String, String> queryParams = new HashMap<String, String>();
//			 * queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			 * queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			 * queryParams.put("solutionTypeLevel2",
//			 * LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2); queryParams.put("channel",
//			 * LendingConstants.DIY_P4B_APP_CHANNEL); queryParams.put("solutionTypeLevel3",
//			 * LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			 * 
//			 * Map<String, String> headers = new HashMap<String, String>(); headers =
//			 * commonHeaders;
//			 * 
//			 * Map<String, Object> body = new HashMap<String, Object>();
//			 * body.put("workflowSubOperation", "BASIC_DETAILS"); body.put("DOB",
//			 * LendingConstants.DOB); body.put("PAN", LendingConstants.PAN); //JSON Parsing
//			 * exception: try to create json at runtime for this
//			 * 
//			 * Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams,
//			 * headers, body);
//			 * 
//			 * lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			 * LOGGER.info("displayMessage : " +
//			 * responseObject.jsonPath().getString("displayMessage"));
//			 * Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//			 * "EMAIL is null or empty");
//			 * 
//			 * 
//			 * 
//			 * }
//			 */			
//			@Test(description = "Add Basic details with incorrect format for EMAIL in request body",dependsOnMethods = "TC038_AddBasicDetails_WithouPassing_DOB_InBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC039_AddBasicDetails_WithIncorrectFormatFor_EMAIL_InBody() {
//				
//			LOGGER.info("passing with incorrect format for EMAIL in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", "neeraj.frendz");
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Email");
//		
//		
//			
//			}
//			@Test(description = "Add Basic details without passing PAN in request body",dependsOnMethods = "TC039_AddBasicDetails_WithIncorrectFormatFor_EMAIL_InBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC040_AddBasicDetails_WithouPassing_PAN_InBody() {
//				
//			LOGGER.info("Not passing PAN in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PAN number is null or empty");
//		
//		
//			
//			}
//			
//			@Test(description = "Add Basic details with incorrect format for PAN in request body",dependsOnMethods = "TC040_AddBasicDetails_WithouPassing_PAN_InBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC041_AddBasicDetails_WithIncorrectFormatFor_PAN_InBody() {
//				
//			LOGGER.info("passing with incorrect format for PAN in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", "**********");
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid PAN number");
//		
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing PAN VALUE in request body", dependsOnMethods = "TC041_AddBasicDetails_WithIncorrectFormatFor_PAN_InBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC042_AddBasicDetails_WithouPassing_PAN_VALUE_InBody() {
//				
//			LOGGER.info("Not passing PAN in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", "");
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PAN number is null or empty");
//		
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing EMAIL VALUE in request body", dependsOnMethods = "TC042_AddBasicDetails_WithouPassing_PAN_VALUE_InBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC043_AddBasicDetails_WithouPassing_EMAIL_VALUE_InBody() {
//				
//			LOGGER.info("Not passing EMAIL VALUE in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", "");
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "EMAIL is null or empty");
//		
//		
//			
//			}
//			
//			@Test(description = "Add Basic details without passing DOB VALUE in request body",dependsOnMethods = "TC043_AddBasicDetails_WithouPassing_EMAIL_VALUE_InBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC044_AddBasicDetails_WithouPassing_DOB_VALUE_InBody() {
//				
//			LOGGER.info("Not passing DOB VALUE in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", "");
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "DOB is null or empty");
//		
//		
//			
//			}
//			
//			@Test(description = "Add Basic details with incorrect format for DOB in request body",dependsOnMethods = "TC044_AddBasicDetails_WithouPassing_DOB_VALUE_InBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC045_AddBasicDetails_WithIncorrectFormatFor_DOB_InBody() {
//				
//			LOGGER.info("passing with incorrect format for DOB in request body");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", "25-02-1995");
//			body.put("PAN", "**********");
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Dob");
//		
//		
//			
//			}
//			
//			
//			
//			@Test(description = "Add Basic details",dependsOnMethods = "TC045_AddBasicDetails_WithIncorrectFormatFor_DOB_InBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC046_AddBasicDetails_HappyCase() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "BASIC_DETAILS");
//			body.put("DOB", LendingConstants.DOB);
//			body.put("PAN", LendingConstants.PAN);
//			body.put("EMAIL", LendingConstants.EMAIL);
//			
//			Response responseObject = lendingBaseClassObject.basicDetailsMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("isLeadAlreadyExists"), true);
//			
//			}
//			@Test(description = "Verify the data of lead created", dependsOnMethods = "TC046_AddBasicDetails_HappyCase", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC047_FetchTheCreatedLeadDeatils() {
//		
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//				sessionToken, LendingLeadStages.BASIC_DETAILS.getStage());
//		custId = responseObject.jsonPath().getString("custId");
//				
//		}
//			
//			
//			@Test(description = "Update Pan and dob details without passing lead id in URL ",dependsOnMethods = "TC047_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC048_UpdatePANAndDOBDetails_Without_Passing_leadId() {
//				
//		LOGGER.info("Not passing lead id in URL");
//				
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			
//			@Test(description = "Update Pan and dob details without passing solution in URL ",dependsOnMethods = "TC048_UpdatePANAndDOBDetails_Without_Passing_leadId",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC049_UpdatePANAndDOBDetails_Without_Passing_solution() {
//				
//		LOGGER.info("Not passing solution in URL");
//				
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			
//			
//			@Test(description = "Update Pan and dob details without passing authorization token",dependsOnMethods = "TC049_UpdatePANAndDOBDetails_Without_Passing_solution", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC050_UpdatePANAndDOBDetails_Without_Passing_AuthToken() {
//				
//		LOGGER.info("Not passing authorization token in headers");
//				
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//	
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//			
//			@Test(description = "Update Pan and dob details without passing cust id in headers",dependsOnMethods = "TC050_UpdatePANAndDOBDetails_Without_Passing_AuthToken", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC051_UpdatePANAndDOBDetails_Without_Passing_CustId() {
//				
//		LOGGER.info("Not passing cust id in headers");
//				
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//			@Test(description = "Update Pan and dob details without passing status message in request body",  dependsOnMethods = "TC051_UpdatePANAndDOBDetails_Without_Passing_CustId",groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		public void TC052_UpdatePANAndDOBDetails_Without_Passing_StatusMessage() {
//				
//		LOGGER.info("Not passing without passing status message in request body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//	
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//		
//		}
//			
//			@Test(description = "Update Pan and dob details passing invalid status message in request body", dependsOnMethods = "TC052_UpdatePANAndDOBDetails_Without_Passing_StatusMessage", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC053_UpdatePANAndDOBDetails_Passing_Invalid_StatusMessage() {
//				
//		LOGGER.info("Not passing passing invalid status message in request body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL");
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		verifyResponseCodeAs500InternalServerError(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//		
//		}
//			
//			@Test(description = "Update Pan and dob details without passing status in request body", dependsOnMethods = "TC053_UpdatePANAndDOBDetails_Passing_Invalid_StatusMessage", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC054_UpdatePANAndDOBDetails_Without_Passing_Status() {
//				
//		LOGGER.info("Not passing without passing status in request body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//		
//		}
//			
//			@Test(description = "Update Pan and dob details Passing Status As Failure",  dependsOnMethods = "TC054_UpdatePANAndDOBDetails_Without_Passing_Status", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC055_UpdatePANAndDOBDetails_PassingStatusAs_Failure() {
//		
//		LOGGER.info("Passing Status As Failure in request body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "FAILURE");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//		
//		}
//			
//			@Test(description = "Update Pan and dob details Passing DOB_VALIDATION_STATUS as false",  dependsOnMethods = "TC055_UpdatePANAndDOBDetails_PassingStatusAs_Failure",groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC056_UpdatePANAndDOBDetails_Passing_DOBValidationStatus_AsFalse() {
//						
//		LOGGER.info("Passing DOB_VALIDATION_STATUS as false in request body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "FALSE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());
//		
//		}
//			
//			@Test(description = "Update Pan and dob details", dependsOnMethods = "TC056_UpdatePANAndDOBDetails_Passing_DOBValidationStatus_AsFalse", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC057_UpdatePANAndDOBDetails_HappyCase() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("Cookie", "JSESSIONID=0190B33FF751E0E3ED3DDBF788AEB9EC");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("DOB_VALIDATION_STATUS", "TRUE");
//		body.put("VALIDATED_DOB", "1988-12-18");
//		body.put("VALIDATED_PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//		
//		Response responseObject = lendingBaseClassObject.updatePanAndDOB(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());
//		
//		}
//			@Test(description = "Update PAN and DOB without passing lead Id",dependsOnMethods = "TC057_UpdatePANAndDOBDetails_HappyCase", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC058_UpdateGenderAndPincodeDetails_WithoutPassing_LeadId() {
//			
//			LOGGER.info("Not Passing lead Id in URL");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("status", "SUCCESS");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			}
//			@Test(description = "Update PAN and DOB without passing Solution ",dependsOnMethods = "TC058_UpdateGenderAndPincodeDetails_WithoutPassing_LeadId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC059_UpdateGenderAndPincodeDetails_WithoutPassing_Solution() {
//			
//			LOGGER.info("Not Passing solution in request URL");
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("status", "SUCCESS");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			}
//			@Test(description = "Update PAN and DOB without passing Authorization token in headers ", dependsOnMethods = "TC059_UpdateGenderAndPincodeDetails_WithoutPassing_Solution",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC060_UpdateGenderAndPincodeDetails_Without_Passing_AuthToken() {
//			
//			LOGGER.info("Not PassingAuthorization token in headers");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//		
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("status", "SUCCESS");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//			}
//			@Test(description = "Update PAN and DOB without passing cust id in headers",dependsOnMethods = "TC060_UpdateGenderAndPincodeDetails_Without_Passing_AuthToken", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC061_UpdateGenderAndPincodeDetails_WithoutPassing_CustId() {
//			
//			LOGGER.info("Not Passing cust id in headers");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("status", "SUCCESS");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//			}
//			
//			@Test(description = "Update PAN and DOB without passing status message in Request body", dependsOnMethods = "TC061_UpdateGenderAndPincodeDetails_WithoutPassing_CustId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC062_UpdateGenderAndPincodeDetails_WithoutPassing_StatusMessage_InRequestBody() {
//				
//			LOGGER.info("Not status message in Request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//
//			body.put("status", "SUCCESS");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//			
//			}
//			
//			@Test(description = "Update PAN and DOB  passing invalid status message in Request body", dependsOnMethods = "TC062_UpdateGenderAndPincodeDetails_WithoutPassing_StatusMessage_InRequestBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC063_UpdateGenderAndPincodeDetails_Passing_Invalid_StatusMessage_InRequestBody() {
//				
//			LOGGER.info("Passing invalid status message in Request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL");
//			body.put("status", "SUCCESS");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//			
//			}
//			
//			@Test(description = "Update PAN and DOB without passing status in Request body", dependsOnMethods = "TC063_UpdateGenderAndPincodeDetails_Passing_Invalid_StatusMessage_InRequestBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC064_UpdateGenderAndPincodeDetails_WithoutPassing_Status_InRequestBody() {
//				
//			LOGGER.info("Not passing status in Request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//			
//			}
//			
//			@Test(description = "Update PAN and DOB passing status as Failure in request body",dependsOnMethods = "TC064_UpdateGenderAndPincodeDetails_WithoutPassing_Status_InRequestBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC065_UpdateGenderAndPincodeDetails_PassingStatusAs_Failure() {
//					
//			LOGGER.info("Update PAN and DOB passing status as Failure in request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("status", "FAILURE");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//			}
//			
//			@Test(description = "Update PAN and DOB",dependsOnMethods = "TC065_UpdateGenderAndPincodeDetails_PassingStatusAs_Failure", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC066_UpdateGenderAndPincodeDetails_HappyCase() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("status", "SUCCESS");
//			body.put("BRE_INPUT_GENDER", "Female");
//			body.put("BRE_INPUT_PINCODE", "243001");
//			
//			Response responseObject = lendingBaseClassObject.updateGenderAndPincode(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());
//			
//			}
//			
//			@Test(description = "BRE OTP Verification Without Passing LeadId",dependsOnMethods = "TC066_UpdateGenderAndPincodeDetails_HappyCase",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC067_BREOTPVerification_Without_Passing_LeadId() {
//				
//		LOGGER.info("Update PAN and DOB without passing lead Id in URL");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//	
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_OTP_VERIFIED");
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		
//		}
//			
//			@Test(description = "BRE OTP Verification Without Passing solution", dependsOnMethods = "TC067_BREOTPVerification_Without_Passing_LeadId",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC068_BREOTPVerification_Without_Passing_Solution() {
//				
//		LOGGER.info("Update PAN and DOB without passing solution in URL");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_OTP_VERIFIED");
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		
//		}
//			
//			@Test(description = "BRE OTP Verification Without Passing Authorization Token in headers", dependsOnMethods = "TC068_BREOTPVerification_Without_Passing_Solution",groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC069_BREOTPVerification_Without_Passing_AuthToken() {
//				
//		LOGGER.info("Update PAN and DOB without passing Authorization Token in headers");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_OTP_VERIFIED");
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		
//		}
//			
//			@Test(description = "BRE OTP Verification Without Passing Cust id in headers", dependsOnMethods = "TC069_BREOTPVerification_Without_Passing_AuthToken", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC070_BREOTPVerification_Without_Passing_CustId() {
//				
//		LOGGER.info("Update PAN and DOB without passing cust id in headers");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//	
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_OTP_VERIFIED");
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		
//		}
//			
//			@Test(description = "BRE OTP Verification Without Passing Status In Request Body",dependsOnMethods = "TC070_BREOTPVerification_Without_Passing_CustId",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC071_BREOTPVerification_WithoutPassing_StatusInRequestBody() {
//		
//		LOGGER.info("Update PAN and DOB Without Passing Status In Request Body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//		}
//			
//			
//			@Test(description = "BRE OTP Verification Passing invalid Status In Request Body", dependsOnMethods = "TC071_BREOTPVerification_WithoutPassing_StatusInRequestBody",groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC072_BREOTPVerification_Passing_Invalid_StatusInRequestBody() {
//		
//		LOGGER.info("Update PAN and DOB Passing invalid Status In Request Body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_OTP");
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//		
//		}
//			
//			@Test(description = "BRE OTP Verification Passing blank Status In Request Body",dependsOnMethods = "TC072_BREOTPVerification_Passing_Invalid_StatusInRequestBody", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC073_BREOTPVerification_Passing_Blank_StatusInRequestBody() {
//		
//		LOGGER.info("Update PAN and DOB Passing Blank Status In Request Body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "");
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//		}
//			
//			@Test(description = "BRE OTP Verification", dependsOnMethods = "TC073_BREOTPVerification_Passing_Blank_StatusInRequestBody", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC074_BREOTPVerification_HappyCase() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_OTP_VERIFIED");
//		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//		}
//			
//			@Test(description = "BRE Validation Pending without passing lead id",dependsOnMethods = "TC074_BREOTPVerification_HappyCase",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC075_BREValidationPending_Without_Passing_LeadId() {
//		
//		LOGGER.info("BRE Validation Pending without passing lead id");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			
//			@Test(description = "BRE Validation Pending without passing solution type", dependsOnMethods = "TC075_BREValidationPending_Without_Passing_LeadId", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC076_BREValidationPending_Without_Passing_Solution() {
//		
//		LOGGER.info("BRE Validation Pending without passing solution");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			
//			@Test(description = "BRE Validation Pending without passing Authorization Token in headers", dependsOnMethods = "TC076_BREValidationPending_Without_Passing_Solution", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC077_BREValidationPending_Without_Passing_AuthorizationToken() {
//		
//		LOGGER.info("BRE Validation Pending without passing Authorization Token");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//			
//			@Test(description = "BRE Validation Pending without passing custId in headers",dependsOnMethods = "TC077_BREValidationPending_Without_Passing_AuthorizationToken", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC078_BREValidationPending_Without_Passing_CustId() {
//		
//		LOGGER.info("BRE Validation Pending without passing custId in headers");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//			
//
//			@Test(description = "BRE Validation Pending without passing Status In Request Body",dependsOnMethods = "TC078_BREValidationPending_Without_Passing_CustId",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC079_BREValidationPending_Without_Passing_StatusInRequestBody() {
//		
//		LOGGER.info("BRE Validation Pending without passing Status In Request Body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("custId", custId);
//		headers.put("Content-Type", "application/json");
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//		}
//			
//			@Test(description = "BRE Validation Pending passing Invalid Status In Request Body",dependsOnMethods = "TC079_BREValidationPending_Without_Passing_StatusInRequestBody",   groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC080_BREValidationPending_Passing_Invalid_StatusInRequestBody() {
//		
//		LOGGER.info("BRE Validation Pending without passing Status In Request Body");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_VALIDATION");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//		
//		}
//			
//			@Test(description = "BRE Validation Pending",dependsOnMethods = "TC080_BREValidationPending_Passing_Invalid_StatusInRequestBody",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC081_BREValidationPending_HappyCase() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", "DIY_P4B_APP");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//		Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//		responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//				sessionToken, LendingLeadStages.BRE_VALIDATION_PENDING.getStage());
//		
//		}
//			
//			
//			@Test(description = "Fetch BRE Response without passing solution type", dependsOnMethods = "TC081_BREValidationPending_HappyCase",groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC082_FetchBREResponse_Without_Passing_SolutionType() {
//				
//		LOGGER.info("Fetch BRE Response without passing solution type");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("email", LendingConstants.EMAIL);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			@Test(description = "Fetch BRE Response without passing entity type",dependsOnMethods = "TC082_FetchBREResponse_Without_Passing_SolutionType", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC083_FetchBREResponse_Without_Passing_SolutionType() {
//				
//		LOGGER.info("Fetch BRE Response without passing solution type");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("email", LendingConstants.EMAIL);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			
//			@Test(description = "Fetch BRE Response without passing channel",dependsOnMethods = "TC083_FetchBREResponse_Without_Passing_SolutionType",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC084_FetchBREResponse_Without_Passing_Channel() {
//				
//		LOGGER.info("Fetch BRE Response without passing Channel");
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("email", LendingConstants.EMAIL);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			
//			@Test(description = "Fetch BRE Response without passing session token in headers",dependsOnMethods = "TC084_FetchBREResponse_Without_Passing_Channel",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC085_FetchBREResponse_Without_Passing_SessionToken() {
//				
//		LOGGER.info("Fetch BRE Response without passing session token in headers");
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("email", LendingConstants.EMAIL);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//			
//			@Test(description = "Fetch BRE Response", dependsOnMethods = "TC085_FetchBREResponse_Without_Passing_SessionToken",groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC086_FetchBREResponse() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//			
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//			}
//			
//			@Test(description = "Fetch BRE Response when hitting the API again",dependsOnMethods = "TC086_FetchBREResponse", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC087_FetchBREResponse_When_HittingTheAPIAgain() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Oops! Something went wrong. Please try again after some time.");
//		
//		}
//			
//			@Test(description = "Verify Lead stage",dependsOnMethods = "TC087_FetchBREResponse_When_HittingTheAPIAgain", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC088_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, LendingLeadStages.BRE_RESPONSE_AWAITED.getStage());
//			
//			}
//			
//			@Test(description = "Check BRE Response without passing solution type",dependsOnMethods = "TC088_VerifyLeadStage",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC089_CheckBREResponse_Without_Passing_SolutionType() throws SQLException {
//			
//			LOGGER.info("Check BRE Response without passing solution type");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			}
////			
////			@Test(description = "Check BRE Response without passing solution type level 2",dependsOnMethods = "TC089_CheckBREResponse_Without_Passing_SolutionType",  groups = { "Regression" })
////			@Owner(emailId = "<EMAIL>", isAutomated = true)
////			public void TC090_CheckBREResponse_Without_Passing_SolutionTypeLevel2() throws SQLException {
////			
////			LOGGER.info("Check BRE Response without passing solution type level2");
////			Map<String, String> queryParams = new HashMap<String, String>();
////			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
////		
////			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
////			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
////			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
////			
////			Map<String, String> headers = new HashMap<String, String>();
////			headers.put("session_token", sessionToken);
////			headers.put("Content-Type", "application/json;charset=utf-8");
////			
////			Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
////			
////			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
////			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
////			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We could not fetch your details. Please try again later.");
////			
////		
////			}
//			
//			@Test(description = "Check BRE Response without passing Entity type",dependsOnMethods = "TC089_CheckBREResponse_Without_Passing_SolutionType", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			
//			public void TC091_CheckBREResponse_Without_Passing_EntityType() throws SQLException {
//			
//			LOGGER.info("Check BRE Response without passing entity type");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			}
//			
//			
//			@Test(description = "Check BRE Response without passing channel",dependsOnMethods = "TC091_CheckBREResponse_Without_Passing_EntityType",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			
//			public void TC092_CheckBREResponse_Without_Passing_Channel() throws SQLException {
//			
//			LOGGER.info("Check BRE Response without passing channel");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			}
//			
//		
//			@Test(description = "Check BRE Response without passing session token in headers",dependsOnMethods = "TC092_CheckBREResponse_Without_Passing_Channel", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			
//			public void TC093_CheckBREResponse_Without_Passing_sessionToken() throws SQLException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			}
//			
//			@Test(description = "BRE callback API without passing solution type ",dependsOnMethods = "TC093_CheckBREResponse_Without_Passing_sessionToken",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC094_BRECallbackAPI_Without_Passing_SolutionType() throws SQLException {
//			
//			LOGGER.info("Using Callback API without passing solution type");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//			body.put("status", "SUCCESS");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			}
//			@Test(description = "BRE callback API without passing Lead Id ", dependsOnMethods = "TC094_BRECallbackAPI_Without_Passing_SolutionType",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC095_BRECallbackAPI_Without_Passing_LeadId() throws SQLException {
//			
//			LOGGER.info("Using Callback API without passing  Lead Id");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//		
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//			body.put("status", "SUCCESS");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			}
//			
//			@Test(description = "BRE callback API without passing AuthorizationToken in headers ",dependsOnMethods = "TC095_BRECallbackAPI_Without_Passing_LeadId",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC096_BRECallbackAPI_Without_Passing_Authorization_Token_In_headers() throws SQLException {
//			
//			LOGGER.info("Using Callback API");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//			body.put("status", "SUCCESS");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//			}
//			
//			@Test(description = "BRE callback API without passing cust id in headers ", dependsOnMethods = "TC096_BRECallbackAPI_Without_Passing_Authorization_Token_In_headers",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC097_BRECallbackAPI_Without_Passing_CustId_In_headers() throws SQLException {
//			
//			LOGGER.info("Using Callback API");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//			body.put("status", "SUCCESS");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//			}
//			
//			@Test(description = "BRE callback API without passing status message in request body",dependsOnMethods = "TC097_BRECallbackAPI_Without_Passing_CustId_In_headers", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC098_BRECallbackAPI_Without_Passing_StatusMessage_In_Request_Body() throws SQLException {
//			
//			LOGGER.info("Using Callback API without passing status message in request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//		
//			body.put("status", "SUCCESS");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//			
//			}
//			@Test(description = "BRE callback API passing invalid status message in request body", dependsOnMethods = "TC098_BRECallbackAPI_Without_Passing_StatusMessage_In_Request_Body",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC099_BRECallbackAPI_Passing_Invalid_StatusMessage_In_Request_Body() throws SQLException {
//			
//			LOGGER.info("Using Callback API passing invalid status message in request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE");
//			body.put("status", "SUCCESS");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//			
//			}
//			
//			@Test(description = "BRE callback API without passing status in request body",dependsOnMethods = "TC099_BRECallbackAPI_Passing_Invalid_StatusMessage_In_Request_Body", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC100_BRECallbackAPI_Without_Passing_Status_In_Request_Body() throws SQLException {
//			
//			LOGGER.info("Using Callback API without passing status in request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//		
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//			
//			
//			}
//			
//			@Test(description = "BRE callback API passing BLANK status in request body", dependsOnMethods = "TC100_BRECallbackAPI_Without_Passing_Status_In_Request_Body", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC101_BRECallbackAPI_Without_Passing_Status_In_Request_Body() throws SQLException {
//			
//			LOGGER.info("Using Callback API passing BLANK status in request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//			body.put("status", "");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//			
//			
//			}
//			
//			@Test(description = "BRE callback API passing status as FAILURE in request body", dependsOnMethods = "TC101_BRECallbackAPI_Without_Passing_Status_In_Request_Body",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC102_BRECallbackAPI_Passing_Status_AsFailure_In_Request_Body() throws SQLException {
//			
//			LOGGER.info("BRE callback API passing status as FAILURE in request body");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//			body.put("status", "FAILURE");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//			
//			}
//			
//		
//			@Test(description = "BRE callback API", dependsOnMethods = "TC102_BRECallbackAPI_Passing_Status_AsFailure_In_Request_Body",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC103_BRECallbackAPI() throws SQLException {
//			
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//				
//				
//				Response responseObject = null;
//				
//				// If response of BRE status API is BRE success then get loan amount
//				
//				for(int i=0;i<20;i++)
//					
//				{
//					responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//					LOGGER.info("Status Code : " + responseObject.getStatusCode());
//					
//					if (responseObject.getStatusCode() == 200)
//					{
//						LOGGER.info("BRE Paased");
//						stage = responseObject.jsonPath().getString("stage");
//						loanOffered = responseObject.jsonPath().getString("loanOffered");
//						maxLoanAmount = responseObject.jsonPath().getString("maxLoanAmount");
//						
//						break;
//					
//					}
//					
//					if (responseObject.getStatusCode() == 200
//							&& responseObject.jsonPath().getString("stage").equals("BRE_ERROR"))
//					{
//						LOGGER.info("BRE Error");
//						stage = responseObject.jsonPath().getString("stage");
//					
//						break;
//					
//					}
//					
//					LOGGER.info("Stage received in BRE response"+ responseObject.jsonPath().getString("stage"));
//					
//					
//				}
//				
//				
//				// If response of BRE status API is UPDATE_LOAN_OFFER then verify new offer generated flag
//				// and hit update loan offer API
//				if (responseObject.getStatusCode() == 200
//						&& responseObject.jsonPath().getString("stage").equals("UPDATE_LOAN_OFFER")) 
//				{
//				
//					LOGGER.info("Update loan offer");
//					Assert.assertEquals(responseObject.jsonPath().getString("newOfferGenerated"), "true");
//					UpdateLoanOffer();
//				
//				}
//				
//				// IF response is not BRE success/Update loan offer then their can be two cases
//				else {
//				
//					// 1.Response can be BRE ERROR in that case move the node back to BRE Response awaited
//					// and hit callback API and move lead to BRE success
//					if (responseObject.getStatusCode() == 200
//							&& responseObject.jsonPath().getString("stage").equals("BRE_ERROR"))
//								{
//						           LOGGER.info("Received BRE ERROR");
//									int updatedRows = 0;
//									String currentActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("167", leadId);
//									LOGGER.info("Current Active Node is : " + currentActiveworkflowStatusId);
//							
//									updatedRows = runUpdateQueryOnWorkflowStatus(0, new BigInteger(currentActiveworkflowStatusId));
//									Assert.assertEquals(updatedRows, 1);
//							
//									String previousActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("366", leadId);
//									updatedRows = runUpdateQueryOnWorkflowStatus(1, new BigInteger(previousActiveworkflowStatusId));
//									LOGGER.info("Updated Active Node is : " + previousActiveworkflowStatusId);
//									Assert.assertEquals(updatedRows, 1);
//									
//									
//									BRECallback();
//							
//								}
//					
//
//				
//				
//					// 2.Response can be 500 : unable to fetch the details or 200: BRE Response awaited , in this case lead will still be at BRE REsponse awaited
//					//  so hit the callback API and move lead to BRE success
//				
//						else {
//					     
//							if(responseObject.getStatusCode() == 500
//									&&responseObject.jsonPath().getString("displayMessage").equals("We could not fetch your details. Please try again later.")||responseObject.getStatusCode() == 200
//											&& responseObject.jsonPath().getString("stage").equals("BRE_RESPONSE_AWAITED"))
//								BRECallback();
//							
//				
//					        }
//					
//				
//					   }
//			}
//			
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void UpdateLoanOffer() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "UPDATE_LOAN_OFFER");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "50000");
//			body.put("LOAN_TENURE", "271");
//			body.put("LOAN_RATE_OF_INTEREST", "46");
//			body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "11121");
//			body.put("LOAN_PROCESSING_FEE", "301");
//			body.put("LOAN_INTEREST_AMOUNT", "1231");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Fifty Thousand");
//			body.put("BASE_ID", "1107227275_test");
//			body.put("LOAN_MIN_AMOUNT", "10000.0");
//			body.put("LOAN_MAX_AMOUNT", "200000.0");
//			body.put("LOAN_TENURE_MIN", "91");
//			body.put("LOAN_TENURE_MAX", "271");
//			body.put("RISK_GRADE", "H");
//			body.put("IS_ACCEPTANCE_ABOVE_5000", false);
//			body.put("PROCESSING_FEE_RATE", "6.0");
//			body.put("IS_SI_MANDATORY", false);
//			body.put("IS_RESTRICTED_MERCHANT", false);
//			body.put("IS_EMANDATE_ELIGIBLE", false);
//			body.put("LOAN_INCENTIVE", "5000");
//			body.put("LOAN_INCENTIVE_ELIGIBLE", false);
//			body.put("LOAN_INCENTIVE_PERCENTAGE", "0.2");
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers, body);
//			
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("isLeadAlreadyExists"), true);
//			
//			}
//			
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void BRECallback() {
//			
//				LOGGER.info("Using Callback API");
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("leadId", leadId);
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("custId", custId);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "BRE_SUCCESS");
//				body.put("status", "SUCCESS");
//				body.put("creditScore", "780.3");
//				body.put("lastFetchDate", "1606694400000");
//				body.put("bureau", "CIBIL");
//				body.put("loanOffered", "true");
//				body.put("newOfferGenerated", "false");
//				body.put("rejectionReason", "Rejection reason");
//				body.put("baseId", "MCA_1107227275");
//				body.put("riskGrade", "H");
//				body.put("processingFeeRate", "1.0");
//				body.put("isAcceptanceAbove5000", true);
//				body.put("isSiMandatory", true);
//				body.put("isRestrictedMerchant", false);
//				body.put("isPaytmVintageOlderThan90d", true);
//				body.put("minLoanAmount", "10000");
//				body.put("maxLoanAmount", "500000");
//				body.put("maxTenure", 1095);
//				body.put("minTenure", 1095);
//				body.put("rateOfInterest", 36);
//				body.put("fieldInvestigationNeeded", true);
//				body.put("isHomeFiNeeded", true);
//			
//				Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.getStatusCode() == 200)
//						{
//							stage = responseObject.jsonPath().getString("oeStage");
//							Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//						
//					
//						
//						}
//			
//			}
//			
//			
//			@Test(description = "BRE callback API hitting the API again",dependsOnMethods = "TC103_BRECallbackAPI",   groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC104_BRECallbackAPI_HittingTheAPI_Again() throws SQLException {
//			
//			LOGGER.info("Using Callback API");
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "BRE_SUCCESS");
//			body.put("status", "SUCCESS");
//			body.put("creditScore", "780.3");
//			body.put("lastFetchDate", "1606694400000");
//			body.put("bureau", "CIBIL");
//			body.put("loanOffered", "true");
//			body.put("newOfferGenerated", "false");
//			body.put("rejectionReason", "Rejection reason");
//			body.put("baseId", "1107227275_test");
//			body.put("riskGrade", "H");
//			body.put("processingFeeRate", "2.5");
//			body.put("isAcceptanceAbove5000", true);
//			body.put("isSiMandatory", true);
//			body.put("isRestrictedMerchant", false);
//			body.put("isPaytmVintageOlderThan90d", true);
//			body.put("minLoanAmount", "500");
//			body.put("maxLoanAmount", "50000");
//			body.put("maxTenure", 6);
//			body.put("minTenure", 1);
//			body.put("rateOfInterest", 1);
//			body.put("fieldInvestigationNeeded", true);
//			body.put("isHomeFiNeeded", true);
//		
//			Response responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//		
//			}
//			
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC104_BRECallbackAPI_HittingTheAPI_Again",   groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC105_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, LendingLeadStages.BRE_SUCCESS.getStage());
//			
//			}
//			
//			@Test(description = "OTP Callback without passing solution type", dependsOnMethods = "TC105_VerifyLeadStage",   groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC106_OTPCallback_Without_Passing_Solution_Type() {
//				
//			LOGGER.info("Using Callback API passing solution type in URL");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//		
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "OTP_VERIFIED");
//			body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//			body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//			
//			Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			}
//			
//			@Test(description = "OTP Callback without passing Lead Id", dependsOnMethods = "TC106_OTPCallback_Without_Passing_Solution_Type",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC107_OTPCallback_Without_Passing_LeadId() {
//				
//			LOGGER.info("Using Callback API passing lead Id");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "OTP_VERIFIED");
//			body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//			body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//			
//			Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			}
//			
//			@Test(description = "OTP Callback without passing Authorization Token in headers", dependsOnMethods = "TC107_OTPCallback_Without_Passing_LeadId",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC108_OTPCallback_Without_Passing_AuthorizationToken_InRequestBody() {
//				
//			LOGGER.info("Using Callback API passing Authorization Token in headers");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders("", custId, LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "OTP_VERIFIED");
//			body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//			body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//			
//			Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//			}
//			
//			@Test(description = "OTP Callback without passing status in request body",  dependsOnMethods = "TC108_OTPCallback_Without_Passing_AuthorizationToken_InRequestBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC109_OTPCallback_Without_Passing_Status_In_RequestBody() {
//				
//			LOGGER.info("Using Callback API without passing status in request body");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//            
//            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, LendingConstants.DIY_P4B_APP_CHANNEL);
//			Map<String, Object> body = new HashMap<String, Object>();
//			
//			body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//			body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//			
//			Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//			
//			
//			}
//			@Test(description = "OTP Callback passing invalid status in request body", dependsOnMethods = "TC109_OTPCallback_Without_Passing_Status_In_RequestBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC110_OTPCallback_Passing_Invalid_Status_In_RequestBody() {
//				
//			LOGGER.info("Using Callback API passing invalid status in request body");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//            
//            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, LendingConstants.DIY_P4B_APP_CHANNEL);
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "OTP");
//			body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//			body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//			
//			Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//			
//			
//			}
//			@Test(description = "OTP Callback",dependsOnMethods = "TC110_OTPCallback_Passing_Invalid_Status_In_RequestBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC111_OTPCallback_HappyCase() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, LendingConstants.DIY_P4B_APP_CHANNEL);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "OTP_VERIFIED");
//			body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//			body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//			
//			Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//			
//			verifyResponseCodeAs200OK(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			
//			}
//			
//			
//		
//			@Test(description = "Verify the lead sub stage",dependsOnMethods = "TC111_OTPCallback_HappyCase", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC112_FetchLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, LendingLeadStages.OTP_VERIFIED.getStage());
//			
//			}
//			
//			@Test(description = "Get the CKYC Status of current lead without passing Entity type in URL",dependsOnMethods = "TC112_FetchLeadStage",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC113_CheckCKYCStatus_Without_Passing_EntityType() {
//			
//		LOGGER.info("Get the CKYC Status of current lead without passing Entity type in URL");
//		Map<String, String> queryParams = new HashMap<String, String>();
//
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("dob",LendingConstants. DOB);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		
//		}
//			
//			@Test(description = "Get the CKYC Status of current lead without passing solution type in URL",dependsOnMethods = "TC113_CheckCKYCStatus_Without_Passing_EntityType",  groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC114_CheckCKYCStatus_Without_Passing_SolutionType() {
//				
//		LOGGER.info("Get the CKYC Status of current lead without passing solution type in URL");
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("dob",LendingConstants. DOB);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			@Test(description = "Get the CKYC Status of current lead without passing channel in URL",dependsOnMethods = "TC114_CheckCKYCStatus_Without_Passing_SolutionType",groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC115_CheckCKYCStatus_Without_Passing_Channel() {
//				
//		LOGGER.info("Get the CKYC Status of current lead without passing solution type in URL");
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("dob",LendingConstants. DOB);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//			@Test(description = "Get the CKYC Status of current lead without passing session token in headers",  dependsOnMethods = "TC115_CheckCKYCStatus_Without_Passing_Channel",groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC116_CheckCKYCStatus_Without_Passing_SeesionToken_InHeaders() {
//				
//		LOGGER.info("Get the CKYC Status of current lead without passing solution type in URL");
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("dob",LendingConstants. DOB);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//			@Test(description = "Get the CKYC Status of current lead",dependsOnMethods = "TC116_CheckCKYCStatus_Without_Passing_SeesionToken_InHeaders", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC117_CheckCKYCStatus_HappyCase() {
//				
//	
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("dob",LendingConstants. DOB);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//		
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//		verifyResponseCodeAs200OK(responseObject);
//		ckycStage = responseObject.jsonPath().getString("stage");
//		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//		
//		}
//			
//			@Test(description = "Verify the lead sub stage",dependsOnMethods = "TC116_CheckCKYCStatus_Without_Passing_SeesionToken_InHeaders", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC118_VerifyLeadStage() {
//			
//			lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL, sessionToken, ckycStage);
//			
//			}
//			
//			@Test(description = "Upload selfie passing invalid lead id in query parameters",dependsOnMethods = "TC118_VerifyLeadStage",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC119_UploadSelfie_PassingInvalid_LeadId_InQueryParams() throws InterruptedException {
//			
//			LOGGER.info("Upload selfie passing invalid lead id in query parameters");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", "abc1", custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			
//			}
//			
//			@Test(description = "Upload selfie without passing doc provided in query parameters",dependsOnMethods = "TC119_UploadSelfie_PassingInvalid_LeadId_InQueryParams", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC120_UploadSelfie_Without_Passing_DocProvidedInQueryParams() throws InterruptedException {
//			
//				LOGGER.info("Upload selfie without passing doc provided in query parameters");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			
//			}
//			
//			@Test(description = "Upload selfie without passing solution type in query parameters", dependsOnMethods = "TC120_UploadSelfie_Without_Passing_DocProvidedInQueryParams",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC121_UploadSelfie_Without_Passing_SolutionTypeInQueryParams() throws InterruptedException {
//			
//
//				LOGGER.info("Upload selfie without passing solution type in query parameters");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE,"", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Failed to upload document"));
//			
//			
//			
//			}
//			
//			@Test(description = "Upload selfie without passing solution type level 2 in query parameters",dependsOnMethods = "TC121_UploadSelfie_Without_Passing_SolutionTypeInQueryParams", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC122_UploadSelfie_Without_Passing_SolutionTypeLevel2InQueryParams() throws InterruptedException {
//			
//				LOGGER.info("Upload selfie without passing solution typelevel2 in query parameters");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.BUSINESS_LENDING_SOLUTION,"", sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Failure in creating Document record in DB."));
//			
//			
//			
//			}
//			
//			
//			@Test(description = "Upload selfie without passing solution type level 3 in query parameters",dependsOnMethods = "TC122_UploadSelfie_Without_Passing_SolutionTypeLevel2InQueryParams", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC123_UploadSelfie_Without_Passing_SolutionTypeLevel3InQueryParams() throws InterruptedException {
//				
//				LOGGER.info("Upload selfie without passing solution typelevel3 in query parameters");
//			
//				 Map<String, String> queryParams = new HashMap<String, String>();
//			        queryParams.put("docProvided", "selfie");
//			        queryParams.put("extType", "png");
//			        queryParams.put("leadId", leadId);
//			        queryParams.put("custId", custId);
//			        queryParams.put("entityType", 	LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			        queryParams.put("solutionType",LendingConstants.BUSINESS_LENDING_SOLUTION );
//			        queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//			        queryParams.put("category", "NonTransport");
//			        queryParams.put("solutionTypeLevel3", "");
//
//			        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
//			        Map<String, String> headers = new HashMap<String, String>();
//			        headers.put("Content-Type", "multipart/form-data");
//			        headers.put("session_token", sessionToken);
//			        headers.put("custId",custId);
//
//			        Response responseObject = UploadDocument(queryParams, headers, uploadFile);
//			
//			       lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					
//					lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Failure in creating Document record in DB."));
//			
//			
//			}
//			
//			@Test(description = "Upload selfie without passing session token in headers",dependsOnMethods = "TC123_UploadSelfie_Without_Passing_SolutionTypeLevel3InQueryParams",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC124_UploadSelfie_HappyCase() throws InterruptedException {
//				
//				LOGGER.info("Upload selfie without passing session token in headers");
//			
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("docProvided", "selfie");
//		        queryParams.put("extType", "png");
//		        queryParams.put("leadId", leadId);
//		        queryParams.put("custId", custId);
//		        queryParams.put("entityType", 	LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		        queryParams.put("solutionType",LendingConstants.BUSINESS_LENDING_SOLUTION );
//		        queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		        queryParams.put("category", "NonTransport");
//		        queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");
//
//
//		       
//		        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
//		        Map<String, String> headers = new HashMap<String, String>();
//		        headers.put("Content-Type", "multipart/form-data");
//		       
//		        headers.put("custId",custId);
//
//		        Response responseObject = UploadDocument(queryParams, headers, uploadFile);
//		        lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//			}
//			
//			@Test(description = "Upload selfie without file in body",dependsOnMethods = "TC124_UploadSelfie_HappyCase",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC125_UploadSelfie_WithoutPassing_ImageFile()  {
//				
//				LOGGER.info("Upload selfie without file in body");
//			
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("docProvided", "selfie");
//		        queryParams.put("extType", "png");
//		        queryParams.put("leadId", leadId);
//		        queryParams.put("custId", custId);
//		        queryParams.put("entityType", 	LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		        queryParams.put("solutionType",LendingConstants.BUSINESS_LENDING_SOLUTION );
//		        queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		        queryParams.put("category", "NonTransport");
//		        queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");
//
//		        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
//		       
//		        Map<String, String> headers = new HashMap<String, String>();
//		        headers.put("Content-Type", "multipart/form-data");
//		        headers.put("session_token", sessionToken);
//		        headers.put("custId",custId);
//
//		   
//				try {
//					Response responseObject = UploadDocument(queryParams, headers, uploadFile);
//				} catch (Exception e) {
//					
//					e.printStackTrace();
//				}
//		     
//			
//			}
//			
//			@Test(description = "Upload selfie passing invalid solution type in query parameters", dependsOnMethods = "TC125_UploadSelfie_WithoutPassing_ImageFile",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC126_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams() throws InterruptedException {
//			
//
//				LOGGER.info("Upload selfie passing invalid solution type in query parameters");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE,"postpaid_v2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			
//			
//			
//			
//			}
//			
//			@Test(description = "Upload selfie",dependsOnMethods = "TC126_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC127_UploadSelfie_HappyCase() throws InterruptedException {
//			
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//			uuid = responseObject.jsonPath().getString("uuid");
//			
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//			LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//			
//			}
//			
//			@Test(description = "Upload Customer Photo passing Invalid leadId in URL",dependsOnMethods = "TC127_UploadSelfie_HappyCase", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC128_UploadCustomerPhoto_Paasing_InvalidLeadId_InURL() throws InterruptedException {
//
//			LOGGER.info("Upload selfie without passing solution type in query parameters");
//			
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", "xyz1", custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			
//			}
//			
//			@Test(description = "Upload Customer Photo without passing docprovided in URL",dependsOnMethods = "TC128_UploadCustomerPhoto_Paasing_InvalidLeadId_InURL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC129_UploadCustomerPhoto_WithoutPassing_DocProvided_InURL() throws InterruptedException {
//			
//
//			LOGGER.info("Upload Customer Photo without passing docprovided in URL");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			
//			}
//			
//
//			@Test(description = "Upload Customer Photo without passing solution type in URL",dependsOnMethods = "TC129_UploadCustomerPhoto_WithoutPassing_DocProvided_InURL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC130_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL() throws InterruptedException {
//			
//
//			LOGGER.info("Upload Customer Photo without passing solution type in URL");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, "", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Failed to upload document"));
//		
//			
//			}
//			
//
//			@Test(description = "Upload Customer Photo passing invalid solution type in URL",dependsOnMethods = "TC130_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC131_UploadCustomerPhoto_Passing_InvalidSolutionType_InURL() throws InterruptedException {
//			
//
//			LOGGER.info("Upload Customer Photo without passing solution type in URL");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, "postpaid_v2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failure in creating Document record in DB.");
//			}
//			@Test(description = "Upload Customer Photo without passing solution type level2 in URL",dependsOnMethods = "TC131_UploadCustomerPhoto_Passing_InvalidSolutionType_InURL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC132_UploadCustomerPhoto_WithoutPassing_SolutionTypeLevel2_InURL() throws InterruptedException {
//			
//
//			LOGGER.info("Upload Customer Photo without passing solution type in URL");
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.BUSINESS_LENDING_SOLUTION, "", sessionToken);
//			
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Failure in creating Document record in DB."));
//		
//			
//			}
//			
//			@Test(description = "Upload Customer Photo without passing solution type level3 in URL", dependsOnMethods = "TC132_UploadCustomerPhoto_WithoutPassing_SolutionTypeLevel2_InURL",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC133_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL() throws InterruptedException {
//			
//
//			LOGGER.info("Upload Customer Photo without passing solution type level 3 in URL");
//		
//			
//			 Map<String, String> queryParams = new HashMap<String, String>();
//		        queryParams.put("docProvided", "customerPhoto");
//		        queryParams.put("extType", "png");
//		        queryParams.put("leadId", leadId);
//		        queryParams.put("custId", custId);
//		        queryParams.put("entityType", 	LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		   
//		        queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		        queryParams.put("category", "NonTransport");
//		        queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");
//
//		        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
//		        Map<String, String> headers = new HashMap<String, String>();
//		        headers.put("Content-Type", "multipart/form-data");
//		        headers.put("session_token", sessionToken);
//		        headers.put("custId",custId);
//
//		        Response responseObject = UploadDocument(queryParams, headers, uploadFile);
//				
//				lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				//Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Failure in creating Document record in DB."));
//		
//			
//			}
//			
//			@Test(description = "Upload Customer Photo without passing session token in headers", dependsOnMethods = "TC133_UploadCustomerPhoto_WithoutPassing_SolutionType_InURL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC134_UploadCustomerPhoto_WithoutPassing_SessionToken_InHeaders() throws InterruptedException {
//			
//
//			LOGGER.info("Upload Customer Photo without passing session token in headers");
//		
//			
//			 Map<String, String> queryParams = new HashMap<String, String>();
//		        queryParams.put("docProvided", "customerPhoto");
//		        queryParams.put("extType", "png");
//		        queryParams.put("leadId", leadId);
//		        queryParams.put("custId", custId);
//		        queryParams.put("entityType", 	LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		        queryParams.put("solutionType",LendingConstants.BUSINESS_LENDING_SOLUTION );
//		        queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		        queryParams.put("category", "NonTransport");
//		        queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");
//
//		        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
//		        Map<String, String> headers = new HashMap<String, String>();
//		        headers.put("Content-Type", "multipart/form-data");
//		   
//		        headers.put("custId",custId);
//
//		        Response responseObject = UploadDocument(queryParams, headers, uploadFile);
//				
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//			
//			}
//			
//			@Test(description = "Upload customer photo without file in body",dependsOnMethods = "TC134_UploadCustomerPhoto_WithoutPassing_SessionToken_InHeaders",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC135_UploadCustomerPhoto_WithoutPassing_ImageFile()  {
//				
//				LOGGER.info("Upload customer photo without file in body");
//			
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("docProvided", "customerPhoto");
//		        queryParams.put("extType", "png");
//		        queryParams.put("leadId", leadId);
//		        queryParams.put("custId", custId);
//		        queryParams.put("entityType", 	LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		        queryParams.put("solutionType",LendingConstants.BUSINESS_LENDING_SOLUTION );
//		        queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		        queryParams.put("category", "NonTransport");
//		        queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");
//
//		        File uploadFile = new File(System.getProperty("user.dir")+"/src/test/resources/MerchantService/V2/lending/lead/document/test.jpeg");
//		       
//		        Map<String, String> headers = new HashMap<String, String>();
//		        headers.put("Content-Type", "multipart/form-data");
//		        headers.put("session_token", sessionToken);
//		        headers.put("custId",custId);
//
//		   
//				try {
//					Response responseObject = UploadDocument(queryParams, headers, uploadFile);
//				} catch (Exception e) {
//					
//					e.printStackTrace();
//				}
//		     
//			
//			}
//			@Test(description = "Upload Customer Photo", dependsOnMethods = "TC135_UploadCustomerPhoto_WithoutPassing_ImageFile",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC136_UploadCustomerPhoto_HappyCase() throws InterruptedException {
//			
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, sessionToken);
//			
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//			uuid = responseObject.jsonPath().getString("uuid");
//			
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//			LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//			
//			}
//			
//			@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC136_UploadCustomerPhoto_HappyCase",groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC137_VerifyUploadedDocument() {
//			
//				
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, ckycStage);
//			List<Object> docTypes = responseObject.jsonPath().getList("solutionDocSRO.docType");
//			docTypes.contains("customerPhoto");
//			List<Object> docProvided = responseObject.jsonPath().getList("solutionDocSRO.docProvided");
//			docProvided.contains("others");
//			
//			}
//			
//			@Test(description = "CKYC Callback without passing solution type in URL",dependsOnMethods = "TC137_VerifyUploadedDocument", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC138_CKYCCallback_WithoutPassing_SolutionType_InURL() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//		
//			LOGGER.info("CKYC Callback without passing solution type in URL");
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			
//			
//			}
//			@Test(description = "CKYC Callback without passing LEAD ID in URL", dependsOnMethods = "TC138_CKYCCallback_WithoutPassing_SolutionType_InURL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC139_CKYCCallback_WithoutPassing_LeadId_InURL() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//		
//			LOGGER.info("CKYC Callback without passing solution type in URL");
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			
//			
//			}
//			@Test(description = "CKYC Callback without passing session token in headers", dependsOnMethods = "TC139_CKYCCallback_WithoutPassing_LeadId_InURL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC140_CKYCCallback_WithoutPassing_sessionTokenInHeaders() {
//			
//			LOGGER.info("CKYC Callback without passing session token in headers");	
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);	
//		
//			Map<String, String> headers = new HashMap<String, String>();
//	
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//			
//			
//			}
//			@Test(description = "CKYC Callback without passing cust id in headers",dependsOnMethods = "TC140_CKYCCallback_WithoutPassing_sessionTokenInHeaders", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC141_CKYCCallback_WithoutPassing_custIdInHeaders() {
//			
//			LOGGER.info("CKYC Callback without passing session token in headers");	
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);	
//		
//            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//	
//			headers.put("Content-Type", "application/json");
//			
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//			
//			
//			}
//			
//			@Test(description = "CKYC Callback without passing status in request body",dependsOnMethods = "TC141_CKYCCallback_WithoutPassing_custIdInHeaders",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC142_CKYCCallback_WuthoutPassing_StatusInRequestBody() {
//				
//			LOGGER.info("CKYC Callback without passing status in request body");	
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//		
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//			
//			}
//			
//			
//			@Test(description = "CKYC Callback passing invalid DOB in request body",dependsOnMethods = "TC142_CKYCCallback_WuthoutPassing_StatusInRequestBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC143_CKYCCallback_Passing_InvalidDOBInRequestBody() {
//				
//			LOGGER.info("CKYC Callback passing invalid DOB in request body");	
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", "21-04-1989");
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Dob format");
//			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.CKYC_VERIFIED.getStage());
//		
//			
//			}
//			
//			@Test(description = "CKYC Callback passing blank DOB in request body", dependsOnMethods = "TC143_CKYCCallback_Passing_InvalidDOBInRequestBody",groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC144_CKYCCallback_Passing_BlankDOBInRequestBody() {
//				
//			LOGGER.info("CKYC Callback passing Blank DOB in request body");	
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", "");
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Dob format");
//			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.CKYC_VERIFIED.getStage());
//		
//			
//			}
//			
//			@Test(description = "CKYC Callback passing blank Gender in request body",dependsOnMethods = "TC144_CKYCCallback_Passing_BlankDOBInRequestBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC145_CKYCCallback_Passing_BlankGenderInRequestBody() {
//				
//			LOGGER.info("CKYC Callback passing Blank DOB in request body");	
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Gender is Empty");
//		
//		
//			
//			}
//			
//			@Test(description = "CKYC Callback passing invalid pan format in request body",dependsOnMethods = "TC145_CKYCCallback_Passing_BlankGenderInRequestBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC146_CKYCCallback_Passing_InvalidPanFormatInRequestBody() {
//				
//			LOGGER.info("CKYC Callback passing invalid pan format in request body");	
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", "**********");
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//		
//			
//			}
//			
//		
//			
//			@Test(description = "CKYC Callback passing blank selfie match percentage in request body",dependsOnMethods = "TC145_CKYCCallback_Passing_BlankGenderInRequestBody",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC147_CKYCCallback_Passing_BlankSelfieMatcgPercenatgenRequestBody() {
//			
//				LOGGER.info("CKYC Callback passing blank selfie match percentage in request body");
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//	        lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//			
//			}
//			
//			@Test(description = "CKYC Callback", dependsOnMethods = "TC147_CKYCCallback_Passing_BlankSelfieMatcgPercenatgenRequestBody", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC148_CKYCCallback_HappyCase() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PAN_VERIFIED.getStage());
//			
//			}
//			
//			@Test(description = "CKYC Callback again hit the API afer success response",dependsOnMethods = "TC148_CKYCCallback_HappyCase", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC149_CKYCCallback_AgainHitAfterSuccessCase() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//			body.put("addressline2", "MAYUR VIHAR PHASE 1");
//			body.put("city", "DELHI");
//			body.put("state", "EAST DELHI");
//			body.put("pincode", "110091");
//			body.put("dob", LendingConstants.DOB);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN);
//			body.put("ckycSuccessMode", "OFFLINE_AADHAR");
//			
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//				
//		    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//			
//			}
//			
//			@Test(description = "Verify Lead stage",dependsOnMethods = "TC149_CKYCCallback_AgainHitAfterSuccessCase",  groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC150_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, LendingLeadStages.PAN_VERIFIED.getStage());
//			
//			LOGGER.info("Verify that detials are present in userAdditionalInfo");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
//			LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//			Assert.assertEquals(
//					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),
//					"FALSE");
//			Assert.assertEquals(
//					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),
//					"FALSE");
//			
//			}
//			
//			
//			@Test(description = "CKYC Callback", dependsOnMethods = "TC150_VerifyLeadStage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC151_UpdateKYCNameInSAI() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("leadId", leadId);
//
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "SUCCESS");
//				body.put("CKYC_NAME", "TestBeneficiary");
//
//				Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);
//
//				verifyResponseCodeAs200OK(responseObject);
//
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//				Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PAN_VERIFIED.getStage());
//
//			}
//			
//			
//			@Test(description = "Enter the bank details without passing solution name", dependsOnMethods = "TC151_UpdateKYCNameInSAI", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC152_SaveBankDetails_WithoutPassingSolutionName() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		}
//			
//			
//			@Test(description = "Enter the bank details without passing entity type", dependsOnMethods = "TC152_SaveBankDetails_WithoutPassingSolutionName", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC153_SaveBankDetails_WithoutPassingEntityName() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		}
//		
//			
//			@Test(description = "Enter the bank details without passing channel", dependsOnMethods = "TC153_SaveBankDetails_WithoutPassingEntityName", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC154_SaveBankDetails_WithoutPassingChannel() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		}
//			
////			@Test(description = "Enter the bank details without passing solution type level3", dependsOnMethods = "TC154_SaveBankDetails_WithoutPassingChannel", groups = {
////			"Regression" })
////	@Owner(emailId = "<EMAIL>", isAutomated = true)
////	public void TC155_SaveBankDetails_WithoutPassingSolutionTypeLevel3() {
////		Map<String, String> queryParams = new HashMap<String, String>();
////		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
////		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
////		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
////		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
////
////
////		Map<String, String> headers = new HashMap<String, String>();
////		headers.put("session_token", sessionToken);
////		headers.put("Content-Type", "application/json");
////
////		Map<String, Object> body = new HashMap<String, Object>();
////		body.put("bankName", "PAYTM BANK");
////		body.put("bankAccountNumber", "************");
////		body.put("ifsc", "PYTM0123456");
////		body.put("bankAccountHolderName", "Shivangi Goswami");
////
////		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
////		
////		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
////	
////		}
//			
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC154_SaveBankDetails_WithoutPassingChannel", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC156_SaveBankDetails_WithoutPassingSessionTokenInHeaders() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//			}
//		
//		
//			
//			
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC156_SaveBankDetails_WithoutPassingSessionTokenInHeaders", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC157_SaveBankDetails_WithoutPassingBankAccountNumber() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//		
//		
//			}
//		
//			
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC157_SaveBankDetails_WithoutPassingBankAccountNumber", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC158_SaveBankDetails_WithoutPassingIFSC() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//		
//			}
//		
//
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC158_SaveBankDetails_WithoutPassingIFSC", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC159_SaveBankDetails() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//				queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//				headers.put("Content-Type", "application/json");
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("bankName", "PAYTM BANK");
//				body.put("bankAccountNumber", "************");
//				body.put("ifsc", "PYTM0123456");
//				body.put("bankAccountHolderName", "Shivangi Goswami");
//
//				Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//
//				if (responseObject.getStatusCode() == 200) {
//
//					Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
//
//				}
//
//			else {
//					
//					for (int i = 1; i < 4; i++) {
//						LOGGER.info("Again hitting with same data: retry-count: " + i);
//						responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//						
//						if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
//							break;
//					}
//				
//				}
//				
//			
//					Assert.assertEquals(responseObject.getStatusCode(),200);
//					
//				
//				}
//				
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC159_SaveBankDetails", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC160_SaveAlreadyAddedBankDetails() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//
//		if (responseObject.getStatusCode() == 200) {
//
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
//
//		}
//
//	else {
//			
//			for (int i = 1; i < 4; i++) {
//				LOGGER.info("Again hitting with same data: retry-count: " + i);
//				responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//				
//				if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
//					break;
//			}
//		
//		}
//		
//	
//			Assert.assertEquals(responseObject.getStatusCode(),200);
//			
//		
//		}
//			
//			@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC160_SaveAlreadyAddedBankDetails", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC161_FetchDynamicTnc_WithoutPassingLeadId() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//
//				Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.jsonPath().getString("meta.status").equals("failed")) 
//					Assert.assertEquals(responseObject.jsonPath().getString("meta.failure_reason"), "Could not generate dynamic tnc");
//				
//			}
//			
//	
//			
//			@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC161_FetchDynamicTnc_WithoutPassingLeadId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC162_FetchDynamicTnc() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//
//				Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//
//					code = responseObject.jsonPath().getString("data.state.code");
//					tncName = responseObject.jsonPath().getString("data.state.tncName");
//					url = responseObject.jsonPath().getString("data.state.url");
//					uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//					md5 = responseObject.jsonPath().getString("data.state.md5");
//				}
//
//			}
//			
//			
//			@Test(description = "Fetch Dynamic T and C Sanction Letter", dependsOnMethods = "TC162_FetchDynamicTnc", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC163_FetchDynamicTncSanctionLetter() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("tncType", "LOAN_SANCTION_TNC");
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//
//		LOGGER.info("Status Code : " + responseObject.getStatusCode());
//		
//		if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//
//			codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
//			tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
//			urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
//			uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//			md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
//		}
//
//	}
//			
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC163_FetchDynamicTncSanctionLetter", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC164_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, LendingLeadStages.BANKING_ACTION_DONE.getStage());
//			
//			}
//			
//			@Test(description = "Verify submit application", dependsOnMethods = "TC164_VerifyLeadStage", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC165_SubmitApplication_WithoutPassingQueryParam() throws InterruptedException {
//	
//		Map<String, String> queryParams = new HashMap<String, String>();
//	
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Content-Type", "application/json");
//		headers.put("session_token", sessionToken);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("WAIT_FOR_EMANDATE", "TRUE");
//		body.put("WORKFLOW_VERSION", WORKFLOW_VERSION);
//		body.put("accept", 1);
//		body.put("tncName", tncName);
//		body.put("uniqueIdentifier", uniqueIdentifier);
//		body.put("md5", md5);
//	
//	
//		Response responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body,"TRUE",WORKFLOW_VERSION,true);
//	
//		LOGGER.info("Status Code : " + responseObject.getStatusCode());
//		if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
//		Assert.assertEquals(responseObject.jsonPath().getString("meta.failure_reason"), "Could not save vetted tnc ");
//
//	
//		
//	
//	}
//			@Test(description = "Verify submit application", dependsOnMethods = "TC165_SubmitApplication_WithoutPassingQueryParam", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC166_SubmitApplication_WithoutPassingSessionToken() throws InterruptedException {
//	
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Content-Type", "application/json");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("WAIT_FOR_EMANDATE", "TRUE");
//		body.put("WORKFLOW_VERSION", WORKFLOW_VERSION);
//		body.put("accept", 1);
//		body.put("tncName", tncName);
//		body.put("uniqueIdentifier", uniqueIdentifier);
//		body.put("md5", md5);
//	
//	
//		
//
//		try {
//			Response responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body,"TRUE",WORKFLOW_VERSION,true);
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			lendingBaseClassObject.verifyResponseCodeAs403Forbidden(responseObject);
//			
//		} catch (Exception e) {
//			
//			e.printStackTrace();
//		}
//  
//	
//	}
//			
//			@Test(description = "Verify submit application", dependsOnMethods = "TC166_SubmitApplication_WithoutPassingSessionToken", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC167_SubmitApplication() throws InterruptedException {
//			
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Content-Type", "application/json");
//				headers.put("session_token", sessionToken);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("WAIT_FOR_EMANDATE", "TRUE");
//				body.put("WORKFLOW_VERSION", WORKFLOW_VERSION);
//				body.put("accept", 1);
//				body.put("tncName", tncName);
//				body.put("uniqueIdentifier", uniqueIdentifier);
//				body.put("md5", md5);
//			
//				body.put("accept1", 1);
//				body.put("tncName1", tncNameSanctionLetter);
//			
//				body.put("uniqueIdentifier1", uniqueIdentifierSanctionLetter);
//				body.put("md51", md5SanctionLetter);
//			
//				Response responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body,"TRUE",WORKFLOW_VERSION);
//			
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
//			
//				{
//					LOGGER.info("Try again");
//					responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body);
//					Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//			
//				}
//			
//				else if (responseObject.jsonPath().getString("meta.stage").contentEquals("LMS_POST")) {
//					verifyResponseCodeAs200OK(responseObject);
//					Assert.assertEquals(responseObject.jsonPath().getString("meta.subStage"), "APPLICATION_PENDING");
//					Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//					Assert.assertEquals(responseObject.jsonPath().getString("data.state"), "Loan Application is Accepted");
//			
//				
//			
//			       }
//			}
//			
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC167_SubmitApplication", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC168_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL,
//					sessionToken, LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
//			
//			}
//			
//			
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC168_VerifyLeadStage", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC169_EmandateCallback_WithoutPassingLeadId() throws InterruptedException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//			
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "EMANDATE_SUCCESS");
//			
//				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//			
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			
//			
//			}
//			
//			
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC169_EmandateCallback_WithoutPassingLeadId", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC170_EmandateCallback_WithoutPassingSolutionName() throws InterruptedException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//			
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "EMANDATE_SUCCESS");
//			
//				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//			
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			}
//			
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC170_EmandateCallback_WithoutPassingSolutionName", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC171_EmandateCallback_WithoutPassingJWTToken() throws InterruptedException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", "");
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "EMANDATE_SUCCESS");
//			
//				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//			
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//			
//			}
//			
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC171_EmandateCallback_WithoutPassingJWTToken", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC172_EmandateCallback_WithoutPassingCustIdInHeaders() throws InterruptedException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", "");
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "EMANDATE_SUCCESS");
//			
//				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//			
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//			
//			}
//			
//			
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC172_EmandateCallback_WithoutPassingCustIdInHeaders", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC173_EmandateCallback_PassingInvalidStatus() throws InterruptedException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "EMANDATE_");
//			
//				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//			
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//			
//			}
//			
//			
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC173_EmandateCallback_PassingInvalidStatus", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC174_EmandateCallback_PassingBlankStatus() throws InterruptedException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "");
//			
//				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//			
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//			
//			}
//			
//			
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC174_EmandateCallback_PassingBlankStatus", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC175_EmandateCallback() throws InterruptedException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_SOLUTION);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "EMANDATE_SUCCESS");
//			
//				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//			
//				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			
//			}
//			
//			
//				@Test(description = "Sheet upload from panel", dependsOnMethods = "TC175_EmandateCallback", groups = {
//				"Regression" })
//		      @Owner(emailId = "<EMAIL>", isAutomated = true)
//		     public void TC0176_UploadSheetONPanel() throws InterruptedException, IOException {
//		
//			TokenXMV tokenXMW = new TokenXMV();
//			Response responseObject = MiddlewareServicesObject.v1Token("9560526665", "paytm@123");
//			String XMWToken = responseObject.getHeader("Set-Cookie").toString();
//		
//			System.out.println("XMW token is :" + XMWToken);
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Content-Type", "multipart/form-data");
//			headers.put("Cookie", XMWToken);
//		
//			File fileUpload = new File(
//					"src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/BusinessSampleFile.csv");
//			FileWriter outputfile = new FileWriter(fileUpload);
//			CSVWriter writer = new CSVWriter(outputfile);
//			String[] header = { "Lead Id", "Business status ", "Rejection Reason\n" };
//			writer.writeNext(header);
//			String[] data1 = { leadId, "APPROVED", "N/A" };
//			writer.writeNext(data1);
//			writer.flush();
//			writer.close();
//		
//			responseObject = lendingBaseClassObject.uploadSheetFromPanel(headers, fileUpload, "BUSINESS_STATUS");
//			if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
//					.contentEquals(" has been successfully uploaded")) {
//				LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
//				verifyResponseCodeAs200OK(responseObject);
//				Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
//		
//			}
//	
//	}
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC0176_UploadSheetONPanel", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC0177_VerifyLeadStage() {
//					
//			
//
//			     Response responseObject = null;
//			
//				 Map<String,String> queryParams=new HashMap<String,String>();
//			        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			        queryParams.put("solution",LendingConstants.BUSINESS_LENDING_SOLUTION);
//			        queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
//
//			        Map<String,String> headers=new HashMap<String,String>();
//			        headers.put("session_token",sessionToken);
//
//			for(int i=0;i<6;i++)
//			        {
//			        responseObject= fetchLeadDetails(queryParams, headers);
//
//				        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
//				        {
//				            LOGGER.info("Verify Lead Stage "+responseObject.jsonPath().getString("stage"));
//				           break;
//		
//				        }
//				           
//			        
//			        }
//			
//			  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
//
//				}
//			
//
//
//
//			
//			
//			
//	/**
//	 * Method to set headers which are used in lead creation request
//	 * 
//	 * @return
//	 */
//	public Map<String, String> setcommonHeaders() {
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		return headers;
//	}
//	
//
//}
