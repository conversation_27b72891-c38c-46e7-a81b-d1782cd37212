package OCL.Lending.BusinessLending.Fullerton;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.google.gson.Gson;
import com.opencsv.CSVWriter;
import com.paytm.apitools.util.annotations.Owner;

import Request.MerchantService.v1.TokenXMV;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;

public class TestMCAFullertonUsingDGMode extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(TestMCAFullertonUsingDGMode.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1000376298";
		 String consumerNumber="7042018959";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
		 String uuid="";
		 String md5="";
		 String code="";
		 String tncName="";
		 String url="";
		 String uniqueIdentifier="";
	;
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
		 String kybSecondaryTNCDisplayURL="";
		 String loanAgreementDate="";
		 String kybTNCDisplayURL="";
		 String panNameMatchTimeStamp="";
		 String panNameMatchPercentage="";
		 String breLastFetchDate="";
		 String offerRequest="";
		 

		 Response responseObject= null;
		private String randomBankAccountNumber;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing MCA Fullerton lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDeatils()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();

			  queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
				queryParams.put("leadId",leadId);
				queryParams.put("custId", custId);

			  queryParams.put("leadId",leadId);
			  

			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create Fullerton Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreateFullertonLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
	    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  	   body.put("PRODUCT_TYPE", "MCA");
		  	   body.put("PRODUCT_VERSION", "1");
		  	   body.put("PRODUCT_ID","85");
			   body.put("LOAN_AMOUNT_IN_NUMBER", "20000");
			   body.put("LOAN_INTEREST_AMOUNT", "3850");
			   body.put("LOAN_AMOUNT_IN_WORDS", "Twenty Thousand");
			   body.put("LOAN_MIN_AMOUNT", "15000");
			   body.put("LOAN_MAX_AMOUNT", "30000");
			   body.put("LOAN_TENURE", "450");
		  	   body.put("LOAN_TENURE_MIN", "450");
		  	   body.put("LOAN_TENURE_MAX", "450");
		  	   body.put("LOAN_TENURE_UNIT", "DAY");
			   body.put("LOAN_RATE_OF_INTEREST","30");
		  	   body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "53");
			   body.put("LOAN_PROCESSING_FEE", "600");
			   body.put("LOAN_INCENTIVE", "5000");
			   body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
			   body.put("LOAN_INCENTIVE_PERCENTAGE", "0");
			   body.put("MERCHANT_ID", "EETkYT90127981618110");
			   body.put("BASE_ID", "Shivangi_Fullerton_Digilocker_71e304a8");
			   body.put("LOAN_OFFER_ID", "Shivangi_Fullerton_Digilocker_71e304a8");
			   body.put("RISK_GRADE", "MCA|DRB124");
			   body.put("PROCESSING_FEE_RATE", "3.0");
			   body.put("IS_ACCEPTANCE_ABOVE_5000", true);
			   body.put("IS_SI_MANDATORY", true);
			   body.put("IS_RESTRICTED_MERCHANT", true);
			   body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
			   body.put("IS_EMANDATE_ELIGIBLE", true);
			   body.put("STATIC_TNC_SETNAME", "loanstatictnc");
			   body.put("LENDER_STATIC_TNC_SETNAME", "bl_ckyck_lender_consent_fullerton");
			   body.put("LENDING_DYNAMIC_TNC", "bl_loan_agreement_fullerton");
			   body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_fullerton");
			   body.put("FLOW_TYPE", "RISK");
			   body.put("LENDER_ID", "15");
			   body.put("IS_ADDITIONAL_DATA_REQUIRED", "False");
			   body.put("BUREAU_PRIORITY", "CIBIL,EXPERIAN");
			   body.put("IS_LP_BD_MERGED", "TRUE");
			   body.put("DOB", LendingConstants.DOB_STASHFIN);
			   body.put("PAN", Pan);
			   body.put("EMAIL", Utilities.randomEmailGeneration());
			   body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
				body.put("F_NAME", "TOUCH");
				body.put("M_NAME", "WOOD");
				body.put("L_NAME", "LIMITED");
				body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
				body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");
		  	 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CreateLeadLPBDMergeFullertonRequest.json";
		  	  
		  	  
			   
			
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"85");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"20000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),"3850");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_WORDS"),"Twenty Thousand");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MIN_AMOUNT"),"15000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"30000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"450");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"450");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"450");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"DAY");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"30");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"53");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),"600");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE"),"5000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_ELIGIBLE"),"200");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_PERCENTAGE"),"0");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"EETkYT90127981618110");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"Shivangi_Fullerton_Digilocker_71e304a8");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"Shivangi_Fullerton_Digilocker_71e304a8");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_GRADE"),"MCA|DRB124");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"loanstatictnc");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_fullerton");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"bl_loan_agreement_fullerton");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"bl_sanction_letter_fullerton");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"15");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"),"CIBIL,EXPERIAN");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_LP_BD_MERGED"),"TRUE");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),LendingConstants.DOB_STASHFIN);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateFullertonLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		    
		
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC004_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC005_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			 queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
	    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
			
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowMovementRequired", "false");
			body.put("F_NAME", "BHAIRAVI");
			body.put("L_NAME", "LATASREE");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "600024");
			body.put("PAN", Pan);
			body.put("DOB", "1979-10-05");
			body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
			Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());

			
			}
		  
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC005_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchCIR()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
			   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       Map<String,Object> body=new HashMap<String,Object>();
		       
		       String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
		   
		
		 	  
		 	 for(int i=0;i<10;i++)
			  {
				  responseObject= lendingBaseClassObject.v3FetchCIR(queryParams, headers,body,requestjsonpath);
			
			 
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
		        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


		        bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
		        bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
		        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
		        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
		      
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC006_FetchCIR",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC007_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<23;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_SUCCESS.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
			    
			  {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			       
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
			        
			        //Hit BRE1 Callback
			        
			        LOGGER.info("Callback not coming so hitting BRE1 Callback");
			        
			       responseObject=    lendingBaseClassObject. BRE1CallbackforMCA (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);
			        
			        
	            }
			  
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_SUCCESS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
            }
		   
		    
		       
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC007_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC008_UpdateExistingDetailsInSAI() {
			  Map<String, String> queryParams = new HashMap<String, String>();
				 queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		     	  queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
		    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
				
				
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowMovementRequired", "false");
				body.put("F_NAME", "BHAIRAVI");
				body.put("L_NAME", "LATASREE");
				body.put("GENDER", "FEMALE");
				body.put("PINCODE", "600024");
				body.put("PAN", Pan);
				body.put("DOB", "1979-10-05");
				body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
			
			
			}
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_UpdateExistingDetailsInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC009_FetchLeadAllData() throws JSONException, InterruptedException
		    {

			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
		
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
				
			  {
				  
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");  
				  
			  }
			  
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
					
			  {
				  
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");  
			        
			        responseObject=lendingBaseClassObject.BRE1CallbackforMCA(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);
				  
			        
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");  
			  }
			  
			    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
	  
		    }
		  
		 
		  @Test(description = "Update data in SAI Table", dependsOnMethods = "TC009_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC010_LeadDataUpdateForKYC_InSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			 queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
	    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowMovementRequired", "false");
			body.put("PAN","**********");
			body.put("DOB", "1998-09-12");
			body.put("GENDER", "MALE");
			body.put("NSDL_NAME", "Rohan Shivaji Sonawane");
			
			requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";
			
		    responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
			//Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
			
			
			}
		 
		 @Test(description = "Verify lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC010_LeadDataUpdateForKYC_InSAI")
			@Owner(emailId = "<EMAIL>")
			public void TC011_uploadCustomerPhoto() throws InterruptedException {
				
				Response responseObject = lendingBaseClassObject.KYCImageUpload("customerPhoto", leadId, custId,
						LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2, sessionToken,"others","RohanOfflineAadhaar.jpg");
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
			
				
			}
		 @Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC011_uploadCustomerPhoto")
			@Owner(emailId = "<EMAIL>")
			public void TC012_VerifyUploadedCustomerPhoto() {

				responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				//Customerphoto
				Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
				Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");
						
			}
			
			@Test(description = "Verify the ABFL lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC012_VerifyUploadedCustomerPhoto")
			@Owner(emailId = "<EMAIL>")
			public void TC013_UploadSelfie() throws InterruptedException {
				
				Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
						LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2, sessionToken,"selfie","RohanOfflineAadhaar.jpg");
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
			
				
			}
			
			@Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC013_UploadSelfie", groups = {
			"Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC014_VerifyUploadedSelfie() {

				responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");
					
				}else {
					Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");

					
				}
				
			}
			  @Test(description="Initiate KYC using DG",groups = {"Regression"},dependsOnMethods = "TC014_VerifyUploadedSelfie")
				@Owner(emailId = "<EMAIL>")
				public void TC015_InitiateKYC_UsingDigiLocker() {
					Map<String,String> queryParams=new HashMap<String,String>();
					queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
			  	 
					Map<String,String> headers=new HashMap<String,String>();
					token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
				    headers = LendingBaseAPI.setHeadersReceivedFromFE();
				    headers.put("Authorization", token);
				    headers.put("Content-Type", "application/json");
				    headers.put("custId", custId);
				       
				    Map<String,Object> body=new HashMap<String,Object>();
				    body.put("kycMode","DIGILOCKER");
				  
				    
				    requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";
				    
				    responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
					Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
					
				}
			  
			  
			  @Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC015_InitiateKYC_UsingDigiLocker")
				@Owner(emailId = "<EMAIL>")
					public void TC016_FetchDataPostKYCIntiated() throws JSONException
					{
					  
					 for(int i =0;i<=10;i++) {
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage())) {
						    	 break;
						     }
					 }
				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_INITIATED.getStage()))
				     {
                     //Hit KYC Callback
				        
				        LOGGER.info("Actual Callback not coming from KYC so hitting KYC DG mock Callback");
				        
				        responseObject=    lendingBaseClassObject. KYCCallbackusingDigilocker (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);
				        
			  	   	}
				    
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_COMPLETED.getStage());
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_COMPLETED.getStage());
			  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"375");
			  	   	
				
					}
			  
			  @Test(description = "Verify lead details after KYC",groups={"Regression"},dependsOnMethods = "TC016_FetchDataPostKYCIntiated")
				@Owner(emailId = "<EMAIL>")
					public void TC017_FetchDataKYCInitiate() throws JSONException
					{
					  
					 for(int i =0;i<=25;i++) {
							responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
						     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_REQUESTED.getStage())) {
						    	 break;
						     }
					 }
				    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
				     {
				    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
				  	   	Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");
				  	   	
				  	   	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				  	   	
			  	   	}
				    
			    	Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
				  	  
				
					}
			  @Test(description = "Verify MCA v3 Lead Second BRE callback", dependsOnMethods = "TC017_FetchDataKYCInitiate",groups = {"Regression"})
				@Owner(emailId = "<EMAIL>")
				public void TC018_MCA_Fullerton_SecondBRECallback() {
					
					responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
							LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
							equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
				     {
						Map<String, String> queryParams = new HashMap<String, String>();
						queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
						queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
						queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
						queryParams.put("leadId", leadId);
						
						token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,LendingConstants.JWT_RISK_KEY);
					
						Map<String, String> headers = new HashMap<String, String>();
						headers.put("Authorization", token);
						headers.put("Content-Type", "application/json");
						headers.put("custId", custId);
					
						Map<String, Object> body = new HashMap<String, Object>();
						body.put("workflowOperation", "BRE2_SUCCESS");
						body.put("BRE_NEW_OFFER_GENERATED", "FALSE");
						body.put("OFFER_DETAILS", new Gson().toJsonTree(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_DETAILS")));
						body.put("BRE_LAST_FETCH_DATE", "Wed Apr 19 05:30:00 IST 2023");
						body.put("LENDER_APPLICATION_ID", "11113");
						body.put("BRE2_SUCCESS", "TRUE");
						
				        
						requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/MCAFullertonBRE2CallbackRequest.json";
						responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body,requestBodyJsonPath,"BRE2");
						LOGGER.info("BRE 2 Success with mock callback");
						
				     }	else {
				    	 responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
									LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				    	 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_COMPLETED.getStage());
				    	 LOGGER.info("BRE 2 Success without callback");
				     }
					
					 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_COMPLETED.getStage());
					
					 Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
					 Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"780");
					}
			 
				
				  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC018_MCA_Fullerton_SecondBRECallback",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC019_FetchLead_AfterBRE2() throws JSONException
				    {
					  
					  for(int i=0;i<25;i++)
					  {
					   
					  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
					
					 
					  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
						  
						  
						  break;
					  
					  }
					  
					  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE3_REQUESTED.getStage()))
					  
					  {
						  
						  
						  lendingBaseClassObject. callbackWithOnlyWorkflowoperation(custId,LendingConstants.BUSINESS_LENDING_V3,"BRE3_SUCCESS");
						  
					  }
					  
					  
				   
				    }
				
				 @Test(description = "update kyc name for bank details", dependsOnMethods = "TC019_FetchLead_AfterBRE2", groups = { "Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC020_UpdateKYCNameInSAI() {
					Map<String, String> queryParams = new HashMap<String, String>();
					  queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
			     	  queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
			    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
					
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
					
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("workflowMovementRequired", "false");
					body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
					
					String requestPath="MerchantService/v2/lending/dataUpdate/UpdateKYCNameForBankRequest.json";
					
					Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestPath);
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					
					
					  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
					
					 
					  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
				
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
					
					  System.out.println(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"));
				
					
					}
				 @Test(description = "Enter the bank details", dependsOnMethods = "TC020_UpdateKYCNameInSAI", groups = {
					"Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC021_SaveBankDetails() {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
				queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
		
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("session_token", sessionToken);
				headers.put("Content-Type", "application/json");
				
				randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowOperation", "VERIFY_BANK_DETAILS");
				body.put("EMANDATE_TYPE", "Internet Banking");
				body.put("bankName", "PAYTM BANK");
				body.put("bankAccountNumber",randomBankAccountNumber);
				body.put("ifsc", "PYTM0123456");
				body.put("bankAccountHolderName", "testNameMatch");
				
				Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
				
				if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("baseResponseCode").equals( "BANK_VERIFICATION_SUCCESS")) {
				
					Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");
				
				}
				
				else {
					
					for (int i = 1; i < 6; i++) {
						LOGGER.info("Again hitting with same data: retry-count: " + i);
						responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
						
						if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
							break;
					}
				
				}
				
			
					Assert.assertEquals(responseObject.getStatusCode(),200);
					
				
				}
				 
				 @Test(description = "For Fullerton lead Bank Verification", dependsOnMethods = "TC021_SaveBankDetails",groups = {"Regression"})
					@Owner(emailId = "<EMAIL>")
					public void TC022_FetchLeadPostBankVerification() {
						for(int i=0;i<5;i++) {
						responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
								LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
							if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
								break;
							}
						}
						LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
						Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
					    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
					    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
					    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"PAYTM BANK");
					    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),randomBankAccountNumber);
					    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");
					   
					    
					    
					}	
				 
				  @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC022_FetchLeadPostBankVerification")
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC023_GenerateLoanAgreement()
				    {
				 Map<String,String> queryParams=new HashMap<String,String>();
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
				 queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
				 queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
				
				   				 			  
				  Map<String,String> headers=new HashMap<String,String>();
				  headers.put("session_token",sessionToken);
		     
			   
			  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			  	
			 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
			 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
			 	{
			 	
			    code=responseObject.jsonPath().getString("data.state.code");
			    tncName=responseObject.jsonPath().getString("data.state.tncName");
			    url=responseObject.jsonPath().getString("data.state.url");
			    uniqueIdentifier=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
			    md5=responseObject.jsonPath().getString("data.state.md5");
			 	}
			 	else
			 	{
			 		responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			 		codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
				 	tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
				 	urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
				 	uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
				 	md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
			 		
			 	}
				  
			 	
				    }
		 	
				  @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC023_GenerateLoanAgreement")
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC024_GenerateSanctionLetter()
				    {
				 Map<String,String> queryParams=new HashMap<String,String>();
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
				 queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
				 queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
				 queryParams.put("tncType", "LOAN_SANCTION_TNC");
				   				 			  
				  Map<String,String> headers=new HashMap<String,String>();
				  headers.put("session_token",sessionToken);
		     
			   
			  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			  	
			 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
			 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
			 	{
			 	
			 	codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
			 	tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
			 	urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
			 	uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
			 	md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
			 	}
			 	
			 	else
			 	{
			 		responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			 		codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
				 	tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
				 	urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
				 	uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
				 	md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
			 		
			 	}
				  
			 	
				    }
				  
				  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC024_GenerateSanctionLetter", groups = { "Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC025_UpdateActualPanInSAI() {
					Map<String, String> queryParams = new HashMap<String, String>();
					 queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
			     	  queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
			    	  queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);
				
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
					
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("workflowMovementRequired", "false");
					body.put("F_NAME", "BHAIRAVI");
					body.put("L_NAME", "LATASREE");
					body.put("GENDER", "FEMALE");
					body.put("PINCODE", "600024");
					body.put("PAN", Pan);
					body.put("DOB", "1979-10-05");
					body.put("EMAIL", "<EMAIL>");
					
				    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
					
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					
					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
					Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());

					
					}
				  @Test(description = "Accept Loan Agreement",dependsOnMethods = "TC025_UpdateActualPanInSAI",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC026_AcceptLoanAgreement()
				    {
					  Map<String,String> queryParams=new HashMap<String,String>();
					  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
				      queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
				      queryParams.put("solutionTypeLevel3", LendingConstants.SOLUTION_TYPE_LEVEL_3);
						   	
					  
			    	  Map<String,String> headers=new HashMap<String,String>();
				       headers = LendingBaseAPI.setHeadersReceivedFromFE();
				       headers.put("session_token", sessionToken);
				       headers.put("Content-Type", "application/json");
				        
				       Map<String,Object> body = new HashMap<String, Object>();
				  	   body.put("workflowOperation","LOAN_AGREEMENT_ACCEPTED");
				  	   body.put("LENDING_DYNAMIC_TNC", tncName);
				  	   body.put("KYB_TNC_REF_NO", uniqueIdentifier);
				  	   body.put("TNC_ACCEPTED_CODE", md5);
				  	   body.put("TNC_ACCEPTED_VERSION", 3);
				  	   body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
				  	   body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
				  	   body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
				  	   body.put("SECONDARY_ACCEPTED_TNC_VERSION", 2);
					   body.put("AUTHORIZED_SIGNATORY_NAME", "BENE CUSTOMER NAME");
					   body.put("SETTLEMENT_BANK_ACCOUNT_NAME", "Rohan Shivaji Sonawane");
					   body.put("LENDING_PAN_NAME_QC_REQUIRED", "FALSE");
				  	   body.put("WAIT_FOR_EMANDATE", "TRUE");
				  
				  	  
				  	
				  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/AcceptLoanAgreementWithAuthorizedSignatory.json";
					 
					 for(int i=0;i<2;i++)
					 {
						 
						 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body,requestBodyJsonPath);
						 
						  if(responseObject.getStatusCode()==200)
						   break;
					 }
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"815");
					        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC"),tncNameSanctionLetter);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),tncName);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_AGREEMENT_ACCEPTED"),"TRUE");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_VERSION"),"3");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC_VERSION"),"2");
					     
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),tncNameSanctionLetter);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_CODE"),tncName);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WAIT_FOR_EMANDATE"),"TRUE");


					        lenderCustomerId=responseObject.jsonPath().getString("LENDER_CUSTOMER_ID");
					        sanctionLetterAcceptanceTimestamp=responseObject.jsonPath().getString("SANCTION_LETTER_TNC_ACCEPTANCE_TIMESTAMP");
					        kybSecondaryTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_SECONDARY_TNC_DISPLAY_URL");
					        loanAgreementDate=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AGREEMENT_DATE");
					        kybTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_TNC_DISPLAY_URL");
					     
					    
					    
					      }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
					  
				    }
				  
				  @Test(description = "Verify Emandate Callback", dependsOnMethods = "TC026_AcceptLoanAgreement", groups = {
					"Regression" })
					@Owner(emailId = "<EMAIL>", isAutomated = true)
					public void TC027_EmandateCallback() throws InterruptedException {
						Map<String, String> queryParams = new HashMap<String, String>();
						queryParams.put("leadId", leadId);
						queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
					
						token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
						Map<String, String> headers = new HashMap<String, String>();
						headers.put("Authorization", token);
						headers.put("Content-Type", "application/json");
						headers.put("custId", custId);
					
						Map<String, Object> body = new HashMap<String, Object>();
						body.put("status", "EMANDATE_SUCCESS");
					
						Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
					
						lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					
						Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
					
					}
				  
				  
				  // Add PDC Callback in case application pending callback is not receieved
				  
				  @Test(description = "If PDC Callback is not received manually hit callback",dependsOnMethods = "TC027_EmandateCallback",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC028_VerifyPDCCallback() throws JSONException
				    {
					  
					  Map<String,String> queryParams=new HashMap<String,String>();
						
					  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					  
			    	   Map<String,String> headers=new HashMap<String,String>();
			    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
				       headers = LendingBaseAPI.setHeadersReceivedFromFE();
				       headers.put("Authorization", token);
				       headers.put("Content-Type", "application/json");
				       headers.put("custId", custId);
				       
				       Map<String,Object> body=new HashMap<String,Object>();
				  	 
				  	 for(int i=0;i<10;i++)
					 {
						 
				  		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
						 
				  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_PENDING.getStage()))
						   break;
					 } 
				  	 
				  	 
				  
					 //PDC Callback
				  	 
				   
			  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage()))
			  		{
			  	       body.put("workflowOperation","LOAN_APPLICATION_PENDING");
				  	   body.put("PDC_REASON_ID", "7");
				  	   body.put("PDC_USER_MESSAGE", "Disbursal on hold, this could take upto 3 working days for processing");
				  	   body.put("BUSINESS_STATUS", "PENDING_BY_LMS");
				  	   body.put("PDC_SYSTEM_MESSAGE", "Direct Approval of Loan Account is disabled");
				  	   
				  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PDCLoanApplicationPendingCallbackRequest.json";   
			    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
					
			  			
			  		}
			  		
			  		 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.APPLICATION_PENDING.getStage());
			  		
				    }
				  
				  @Test(description = "Sheet upload from panel", dependsOnMethods = "TC028_VerifyPDCCallback", groups = {
					"Regression" })
			      @Owner(emailId = "<EMAIL>", isAutomated = true)
			     public void TC029_UploadSheetONPanel() throws InterruptedException, IOException {
					  
					  
			   
			   
			    for(int i=0;i<5;i++)
				 {
					 
			    	 responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
					 
			    	 if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.APPLICATION_PENDING.getStage()))
					   break;
				 }
			    
			    if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.EMANDATE_SUCCESS.getStage()))
			  
			    {
				TokenXMV tokenXMW = new TokenXMV();
				Response responseObject = MiddlewareServicesObject.v1Token("7771216290", "paytm@123");
				String XMWToken = responseObject.getHeader("Set-Cookie").toString();
			
				System.out.println("XMW token is :" + XMWToken);
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Content-Type", "multipart/form-data");
				headers.put("Cookie", XMWToken);
			
				File fileUpload = new File(
						"src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/BusinessSampleFile.csv");
				FileWriter outputfile = new FileWriter(fileUpload);
				CSVWriter writer = new CSVWriter(outputfile);
				String[] header = { "Lead Id", "Business status ", "Rejection Reason\n" };
				writer.writeNext(header);
				String[] data1 = { leadId, "APPROVED", "N/A" };
				writer.writeNext(data1);
				writer.flush();
				writer.close();
			
				responseObject = lendingBaseClassObject.uploadSheetFromPanel(headers, fileUpload, "BUSINESS_STATUS");
				if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
						.contentEquals(" has been successfully uploaded")) {
					LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
					Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
			
				}
				
				
			    }
			    
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
			    
			   

		}
				  
				  // LIS CALLBACK
				  
				  @Test(description = "If LIS Callback is not received manually hit callback to move to node 818",dependsOnMethods = "TC029_UploadSheetONPanel",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC030_VerifyLISCallback() throws JSONException
				    {
					  
					  Map<String,String> queryParams=new HashMap<String,String>();
						
					  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					  
			    	   Map<String,String> headers=new HashMap<String,String>();
			    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
				       headers = LendingBaseAPI.setHeadersReceivedFromFE();
				       headers.put("Authorization", token);
				       headers.put("Content-Type", "application/json");
				       headers.put("custId", custId);
				       
				       Map<String,Object> body=new HashMap<String,Object>();
				  	 
				  	 for(int i=0;i<10;i++)
					 {
						 
				  		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
						 
				  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_SUBMIT_SUCCESS.getStage()))
						   break;
					 } 
				  	 
				  	 
				  
					 //LIS Callback
				  	 
				  	responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				   
			  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_DATA_UPDATE_REQUEST_SUCCESS.getStage()))
			  		{
			  	       body.put("workflowOperation","LENDER_FINAL_SANCTION_RECEIVED");
				  	   body.put("SANCTION_ID", "LM00028795");
				  	 
				  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LISCallbackRequest.json";   
			    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
					
			  			
			  		}
			  		
			  		// Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
			  		
				    }
				  
				  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC030_VerifyLISCallback",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC031_LMSDataCallback() throws JSONException
				    {
					  
					  Map<String,String> queryParams=new HashMap<String,String>();
						
					  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					  
			    	   Map<String,String> headers=new HashMap<String,String>();
			    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
				       headers = LendingBaseAPI.setHeadersReceivedFromFE();
				       headers.put("Authorization", token);
				       headers.put("Content-Type", "application/json");
				       headers.put("custId", custId);
				       
				       Map<String,Object> body=new HashMap<String,Object>(); 
				       for(int i=0;i<10;i++){
						responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
						 
				  		if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
						   break;
					 }
				     if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
				     {	
					       body.put("workflowOperation","LMS_APPLICATION_APPROVED");
					  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
					  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
					  	   body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
					  	   body.put("LENDER_LOAN_ACCOUNT_NUMBER", "*************");
					  	   
				    	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LMSSubmitApplicationCallbackRequest.json";   
				    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
						
						  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage()))
							  {
								LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
						        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
						        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
						        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
						        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
						        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
						        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
						        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"330");
						        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
						        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_CREATED_ON"),"*************");
							  }
				     }
				     
				     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
				    }  
				  
				  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC031_LMSDataCallback",groups = {"Regression"})
				  @Owner(emailId = "<EMAIL>",isAutomated = true)
				    public void TC032_FetchLeadAllData() throws JSONException
				    {
					  for(int i=0;i<25;i++)
					  {
					  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
					 
					  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
					  {
						  break;
					  }
					  
					  }	  
				     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
				    }
}