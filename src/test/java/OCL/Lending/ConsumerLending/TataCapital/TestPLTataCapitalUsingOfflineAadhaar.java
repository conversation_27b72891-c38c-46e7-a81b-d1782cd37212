package OCL.Lending.ConsumerLending.TataCapital;

import java.util.HashMap;
import java.util.Map;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;

public class TestPLTataCapitalUsingOfflineAadhaar extends BaseMethod{
	

	private static final Logger LOGGER = LogManager.getLogger(TestPLTataCapitalUsingOfflineAadhaar.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();

		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1700435567";
		 String consumerNumber="8888700008";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
		 String uuid="";
		 String md5="";
		 String code="";
		 String tncName="";
		 String url="";
		 String uniqueIdentifier="";
	;
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
		 String kybSecondaryTNCDisplayURL="";
		 String loanAgreementDate="";
		 String kybTNCDisplayURL="";
		 String panNameMatchTimeStamp="";
		 String panNameMatchPercentage="";
		 String breLastFetchDate="";
		 String loanTenure="";
		String loanAmount="";
		String loanEquatedMonthlyInstallment="";
		String loanRateofInterest="";
		String loanInterestAmount="";
		String loanProcessingFeeRate="";
		String loanDisbursalAmount="";
		String stampDutyCharges="";
		String brokerPeriodInterest="";
		
		 private String randomBankAccountNumber="";
		 private static final boolean MOCK_BRE1 = false;
		 private static final boolean MOCK_BRE2 = true;
		 private static final boolean MOCK_BRE3 = true;
		 

		 Response responseObject= null;
	
		
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing MCA Piramal lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDetails_PL_TCL()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.TCL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDetails_PL_TCL",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();

			  queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
				queryParams.put("leadId",leadId);
				queryParams.put("custId", custId);

			  queryParams.put("leadId",leadId);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create MCA V3 ABFL Lead",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_Create_PL_TCL_Lead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.TCL);
	 
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", "9560096477");
		  	   body.put("LENDING_DYNAMIC_TNC", "tcl_pl_loan_agreement_2");
		  	   body.put("LENDING_DYNAMIC_SECONDARY_TNC", "tcl_pl_sanction_letter");
		  	   body.put("IS_FATHER_NAME_REQUIRED","true");
			   body.put("MARITAL_STATUS", "MARRIED");
			   body.put("IS_BRE3_REQUIRED", "true");
			   body.put("KYC_VERSION", "V3");
			   body.put("KYC_MODES_INITIATE_SEQUENCE", "SEARCH_BY_PAN,SEARCH_BY_AADHAAR,DIGILOCKER");
			   body.put("PRODUCT_ID", "107");
			   body.put("PRODUCT_TYPE", "PL");
		  	   body.put("FLOW_TYPE", "RISK");
		  	   body.put("EMAIL", "<EMAIL>");
		  	   body.put("PRODUCT_VERSION", "1");
			   body.put("LOAN_OFFER_ID","60fc37ad-2800-491e-bcb7-46fe680efd74");
		  	   body.put("BASE_ID", "PL_Tata_Capital_2ef93599");
			   body.put("LENDER_ID", "10");
			   body.put("WHITELISTING_SOURCE", "RISK");
			   body.put("IS_EMANDATE_ELIGIBLE", "true");
			   body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
			   body.put("PINCODE", "560076");
			   body.put("BUREAU_PRIORITY", "EXPERIAN,CIBIL");
			   body.put("LENDER_NAME", "TCL");
			   body.put("IS_RTO_FLOW", "false");
			   body.put("CURRENT_BUREAU", "");
			   
			  
			 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/PLTCLCreateLeadRequest.json";
		  	  
		  	  
			   
			
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),"9560096477");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),"<EMAIL>");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"107");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"MARRIED");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.KYC_MODES_INITIATE_SEQUENCE"),"SEARCH_BY_PAN,SEARCH_BY_AADHAAR,DIGILOCKER");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"tcl_pl_loan_agreement_2");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"tcl_pl_sanction_letter");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"10");
			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_Create_PL_TCL_Lead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.TCL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.TCL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		    
		
		    }
		  
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC005_UpdateLeadBasicDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.TCL);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","OCCUPATION_DETAILS");
		  	   body.put("DOB", "1973-01-01");
		  	   body.put("PAN", Pan);
		  	   body.put("EMAIL", "<EMAIL>");
		  	   body.put("PINCODE", "518401");
		  	   body.put("LOAN_PURPOSE", "Personal use");
		  	   body.put("LOAN_PURPOSE_KEY", "2");
		  	   body.put("GENDER", "Female");
		  	   body.put("STATIC_TNC_SETNAME", "pl_tcl_consent_bureau_kyc");
		  	   body.put("LENDER_STATIC_TNC_SETNAME", "personalloan_oclconsent_tcl_linked");
		  	   body.put("OCCUPATION", "Salaried");
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/OccupationDetailsRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
				        
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
				       
				        
				        leadId=responseObject.jsonPath().getString("leadId");
				        custId=responseObject.jsonPath().getString("custId");
				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
				        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
				  
			    
		     
			  
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_UpdateLeadBasicDetails",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.TCL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.TCL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

		        lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
		        
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
		    
		
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC007_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			  queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.TCL);
	    	
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowMovementRequired", "false");
			body.put("F_NAME", "MAYANK");
			body.put("L_NAME", "SINGHAL");
			body.put("CKYC_F_NAME", "MAYANK");
			body.put("CKYC_L_NAME", "SINGHAL");
			body.put("EMAIL", "<EMAIL>");
			body.put("GENDER", "Male");
			body.put("PINCODE", "711101");
			body.put("PAN", "**********");
			body.put("DOB", "1995-05-22");
			body.put("CKYC_PINCODE", "711101");
			
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/DataUpdateRequest.json";
			
		    responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
			Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());

			
			}
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC007_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC008_FetchCIR()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.TCL);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       
		       String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
		   
		
			   responseObject= lendingBaseClassObject.v1InitiateBureau(queryParams, headers,body,requestjsonpath);
			 	 for(int i=0;i<5;i++)
					  {
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_INITIATED.getStage()))
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_INITIATED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_INITIATED.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"BUREAU_INITIATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"862");

		        
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_FetchCIR",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC009_FetchLeadAllData_AfterBureauSuccess() throws JSONException
		    {
			  
			  for(int i=0;i<55;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.TCL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_GENERATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.TCL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(LendingConstants.IS_MOCK_CALLBACK && MOCK_BRE1 &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage()))
		     {
		    	
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"Male");
				        
				        //Hit BRE1 Callback
				        
				        LOGGER.info("Actual Callback not coming from risk so hitting BRE1 mock Callback");
				        
				        responseObject=    lendingBaseClassObject. BRE1CallbackforTCL (leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.TCL,custId);
				        
				        
		            }
				  
				    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
			  		 
		
		    }
}
