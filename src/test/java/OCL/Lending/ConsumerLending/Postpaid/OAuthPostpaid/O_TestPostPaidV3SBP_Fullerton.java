package OCL.Lending.ConsumerLending.Postpaid.OAuthPostpaid;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.google.gson.Gson;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public class O_TestPostPaidV3SBP_Fullerton extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(O_TestPostPaidV3SBP_Fullerton.class);
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility = new Utilities();

    String sessionToken = "";
    String leadId = "";
    String custId = "";
    String consumerNumber = "";
    String consumerPassword = "paytm@123";
    String token = "";
    String uuid = "";
    String userIPAddress = "";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier = "";
    String md5 = "";
    String staticTncAcceptanceTimeStamp = "";
    String bureauRequest = "";
    String bureauResponse = "";
    String bureauCreditState = "";
    String breCreditScore = "";
    String bureauPullTimeStamp = "";
    String offerDetails;
    String offerId;
    String stringify_json = "{\\\"bureauData\\\":{\\\"bureauName\\\":\\\"CIBIL\\\",\\\"creditScore\\\":738,\\\"pulledAt\\\":1653350400000},\\\"loanDownGradable\\\":false,\\\"newOfferGenerated\\\":false,\\\"offers\\\":[{\\\"baseId\\\":\\\"26b4e451-fc1d-4c8a-bd3e-218f5cb10639\\\",\\\"bureauKicker\\\":false,\\\"convFee\\\":1.0,\\\"customerId\\\":\\\"1002105333\\\",\\\"introductoryOfferText\\\":[],\\\"lenderInfo\\\":{\\\"lender\\\":6,\\\"lenderAddress\\\":\\\"OneIndiabullsCentre,Tower1,18thFloor,JupiterMillCompound841,SenapatiBapatMarg,ElphinstoneRoad,Mumbai400013\\\",\\\"lenderCin\\\":\\\"U65990GJ1991PLC064603\\\",\\\"lenderCode\\\":\\\"ABFL\\\",\\\"lenderLogo\\\":\\\"https://lms-static.paytm.com/lending/images/brand/lender_logo_ABFL.jpg\\\",\\\"lenderName\\\":\\\"ADITYABIRLAFINANCELTD\\\"},\\\"maxAmount\\\":47000.0,\\\"maxTenure\\\":12,\\\"minAmount\\\":47000.0,\\\"minTenure\\\":12,\\\"offerEndDate\\\":1661279400000,\\\"offerId\\\":\\\"26b4e451-fc1d-4c8a-bd3e-218f5cb10639\\\",\\\"offerStartDate\\\":1653503400000,\\\"processingFeePercentage\\\":1.0,\\\"processingFeeText\\\":\\\"ConveniencefeewillbechargedonyourmonthlyspendsusingPaytmPostpaid(e.g.?1.0onspendsof?100)\\\",\\\"productId\\\":\\\"71\\\",\\\"productType\\\":\\\"POSTPAID\\\",\\\"productVariant\\\":\\\"DELITE\\\",\\\"productVariantId\\\":1,\\\"productVersion\\\":\\\"1\\\",\\\"profilesExecuted\\\":[],\\\"riskGrade\\\":\\\"VH\\\",\\\"sourceOfWhitelist\\\":\\\"CIR\\\",\\\"subProductType\\\":\\\"POSTPAID_CONVINENCE_FEE\\\",\\\"tenureUnit\\\":\\\"MONTH\\\"}],\\\"reasons\\\":[],\\\"skipMandate\\\":false,\\\"status\\\":\\\"SUCCESS\\\",\\\"transactionId\\\":\\\"2cf599e7-7901-4f38-acd4-1fba664e070a\\\"}}";
    String PP_PAN_ABFL = "";
    String requestBodyJsonPath = "";

    Map<String, String> commonHeaders;
    Response responseObject = null;

    @BeforeClass()
    public void intitializeInputData() {

        long max = 7_999_999_999L;
        long min = 7_000_000_000L;
        long range = max - min + 1;
        long mobileNumber = (int) (Math.random() * range) + min;
        consumerNumber = String.valueOf(mobileNumber);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", consumerNumber);
        body.put("loginPassword", "paytm@123");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/OAuthUserRandomNumber.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.GenerateNewUser(headers, body);

            if (responseObject.getStatusCode() == 200)
                break;
        }

        GetCustId();
        custId = responseObject.jsonPath().getString("userId");
        System.out.println(custId);

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(String.valueOf(consumerNumber), consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);


    }

    public Response GetCustId() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("userData", consumerNumber);
        body.put("fetch_strategy", "mobile");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/GetCustIdviaMobileNumber.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.FetchMobileNumber(headers, body);

            if (responseObject.getStatusCode() == 200)
                break;
        }

        return responseObject;
    }


    @Test(description = "Verify whether there is any existing postpaid lead present or not", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchLeadDeatils() {

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken, custId);
        System.out.println(custId + "   2");
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Reset the existing lead of the number", dependsOnMethods = "TC001_FetchLeadDeatils", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_DeleteExistingLead() {
        System.out.println(custId + "   3");
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_QA, custId,
                LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);
        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }

    @Test(description = "Create PostPaid Lead with all deatils", dependsOnMethods = "TC002_DeleteExistingLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_CreatePostpaidLead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);
        body.put("BUREAU_PRIORITY", LendingConstants.BUREAU_PRIORITY);
        body.put("LENDING_KYB_SOURCE", LendingConstants.LENDING_KYB_SOURCE);
        body.put("IS_SAVE_STATIC_TNC", "TRUE");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PostPaidV3CreateLead.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);
            System.out.println(responseObject);
            if (responseObject.getStatusCode() == 201)
                break;
        }

        if (responseObject.getStatusCode() == 201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"), "STATIC_LOAN_OFFER_TNC");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"), LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"), "paytm_postpaid_happyloans");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_SAVE_STATIC_TNC"), "TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_KYB_SOURCE"), LendingConstants.LENDING_KYB_SOURCE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"), LendingConstants.BUREAU_PRIORITY);

            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");
            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp = responseObject.jsonPath()
                    .getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC003_CreatePostpaidLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_FetchLeadAllData() {

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION,
                LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken,
                custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Update lead basic details", dependsOnMethods = "TC004_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateLeadBasicDetails() throws InterruptedException {


        PP_PAN_ABFL = UtilitiesObject.randomIndividualPANValueGenerator();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BASIC_DETAILS");
        body.put("DOB", LendingConstants.CKYC_DOB);
        body.put("PAN", PP_PAN_ABFL);
        body.put("EMAIL", LendingConstants.CKYC_EMAIL);
        body.put("PAN_DOB_VALIDATED", "TRUE");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PostpaidV3BasicDetails.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,
                    requestBodyJsonPath);

            if (responseObject.getStatusCode() == 200)
                break;
        }
        Thread.sleep(20000);
        if (responseObject.getStatusCode() == 200
                && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "144");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"), LendingConstants.CKYC_DOB);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"), PP_PAN_ABFL);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"), LendingConstants.CKYC_EMAIL);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_DOB_VALIDATED"), "TRUE");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC005_UpdateLeadBasicDetails", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_FetchLeadAllData() throws InterruptedException {
        Thread.sleep(200);
        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION,
                LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken,
                custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_UpdateBureauDataSetInSAI() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
        queryParams.put("leadId", leadId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,
                LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
        body.put("status", "SUCCESS");
        body.put("F_NAME", "BHAIRAVI");
        body.put("L_NAME", "LATASREE");
        body.put("GENDER", "FEMALE");
        body.put("PINCODE", "600024");
        body.put("PAN", "**********");
        body.put("DOB", "1979-10-05");
        body.put("EMAIL", "<EMAIL>");

        responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body, true);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);


        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());

    }

    @Test(description = "Fetch CIR", dependsOnMethods = "TC007_UpdateBureauDataSetInSAI", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_FetchCIR() throws JSONException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();

        String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
        for (int i = 0; i < 6; i++) {
            responseObject = lendingBaseClassObject.v3FetchCIR(queryParams, headers, body, requestjsonpath);

            if (responseObject.getStatusCode() == 200)

                break;

        }

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BUREAU_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BUREAU_SUCCESS.getStage());
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "194");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"), "TRUE");
        Assert.assertEquals(responseObject.jsonPath().getString("creditState"), "BUREAU_SUCCESS");

    }

    @Test(description = "Verify lead details after Fetch CIR", groups = {"Regression"}, dependsOnMethods = "TC008_FetchCIR")
    @Owner(emailId = "<EMAIL>")
    public void TC009_BRE1Callback() throws JSONException, InterruptedException {

        Thread.sleep(10000);
        for (int i = 0; i <= 15; i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
                break;
            }
        }
        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage())) {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "225");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));

            Thread.sleep(10000);
            for (int i = 0; i < 15; i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OFFER_GENERATED.getStage())) {
                    LOGGER.info("BRE 1 passed without callback");
                    LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE1_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
                    break;
                }
            }
            requestBodyJsonPath = "MerchantService/V1/workflow/lead/callback/PPv3ABFLCallbackRequest.json";
            BRE1Callback(requestBodyJsonPath);
            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OFFER_GENERATED.getStage())) {
                LOGGER.info("BRE 1 passed with callback");
                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
            }
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        }
    }

    public Response BRE1Callback(String requestBodyJsonPath) {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "OFFER_STAGE1_SUCCESS");
        body.put("OFFER_DETAILS", "" + stringify_json + "");
        body.put("OFFER_TIMESTAMP", "1663227274000");
        body.put("BUREAU_PULL_TIMESTAMP", "1663227274000");

        responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath);

        return responseObject;

    }

    @Test(description = "Verify the Postpaid lead data after BRE1 Success", groups = {"Regression"}, dependsOnMethods = "TC009_BRE1Callback")
    @Owner(emailId = "<EMAIL>")
    public void TC010_FetchDataPostBRE1Success() throws JSONException {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"), bureauRequest);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"), bureauResponse);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"), bureauCreditState);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"), breCreditScore);

        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC010_FetchDataPostBRE1Success", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC011_FetchLeadAllData() throws JSONException, InterruptedException {

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

        bureauRequest = responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
        bureauResponse = responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
        bureauCreditState = responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
        leadId = responseObject.jsonPath().getString("leadId");

        bureauPullTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PULL_TIMESTAMP");
        System.out.println("SHIVANGI --" + responseObject.jsonPath().getJsonObject("solutionAdditionalInfo.OFFER_DETAILS"));
        offerDetails = responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_DETAILS");
        JSONObject jsnobject = new JSONObject(offerDetails);

        LOGGER.info("OFFER DETAILS " + offerDetails);
        LOGGER.info("OFFER DETAILS " + jsnobject);

        JSONArray ar = (JSONArray) jsnobject.get("offers");
        offerId = ar.getJSONObject(0).getString("offerId");
        System.out.println(offerId);

        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());

    }

    @Test(description = "User will accept the offer", dependsOnMethods = "TC011_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC012_AcceptOffer() {

        Map<String, String> queryParams = LendingBaseAPI.setcommonQueryParameters(leadId,
                LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "OFFER_ACCEPTED");
        body.put("LOAN_OFFER_ID", offerId);

        responseObject = lendingBaseClassObject.v1WorkflowLeadUpdateOffer(queryParams, headers, body, true);

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "367");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"), offerId);

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC012_AcceptOffer", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC013_FetchLeadAllData() throws JSONException, InterruptedException {

        Thread.sleep(10000);

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "CKYC ID Found Stage", dependsOnMethods = "TC013_FetchLeadAllData", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC014_CKYCIDFound() {

        Map<String, String> queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_JWT_Secret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "SUB-SBP_CKYC_ID_FOUND");
        body.put("CKYC_ID", LendingConstants.CKYC_ID);

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PostpaidV3CKYCIdFound.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.v3CKYCID(queryParams, headers, body, requestBodyJsonPath);

            if (responseObject.getStatusCode() == 200)
                break;
        }

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SBP_CKYC_ID_FOUND.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.SBP_CKYC_ID_FOUND.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.SBP_CKYC_ID_FOUND.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "367");
            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.SBP_CKYC_ID_FOUND.getStage());

    }


    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC014_CKYCIDFound", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC015_UpdateBureauDataSetInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
        queryParams.put("leadId", leadId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
        body.put("status", "SUCCESS");
        body.put("CKYC_PAN", PP_PAN_ABFL);
        body.put("PAN", PP_PAN_ABFL);

        responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.SBP_CKYC_ID_FOUND.getStage());

    }

    @Test(description = "Search By PAN Valiadtion Success", dependsOnMethods = "TC015_UpdateBureauDataSetInSAI", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC016_SBPValidationSuccess() {
        Map<String, String> queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_JWT_Secret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "SUB-SBP_VALIDATION_SUCCESS");
        body.put("CKYC_ID", LendingConstants.CKYC_ID);
        body.put("CKYC_F_NAME", LendingConstants.CKYC_F_NAME);
        body.put("CKYC_M_NAME", LendingConstants.CKYC_M_NAME);
        body.put("CKYC_L_NAME", LendingConstants.CKYC_L_NAME);

        body.put("FATHER_NAME", LendingConstants.FATHER_NAME);
        body.put("MOTHER_NAME", LendingConstants.MOTHER_NAME);
        body.put("MARITAL_STATUS", LendingConstants.MARITAL_STATUS);

        body.put("CKYC_SUCCESS_MODE", LendingConstants.CKYC_SUCCESS_MODE_SBP);
        body.put("CKYC_GENDER", LendingConstants.CKYC_GENDER);
        body.put("CKYC_EMAIL", LendingConstants.CKYC_EMAIL);
        body.put("CKYC_DOB", LendingConstants.CKYC_DOB);
        body.put("CKYC_PAN", PP_PAN_ABFL);
        body.put("addressType", LendingConstants.addressType);
        body.put("addressSubType", LendingConstants.addressSubType);
        body.put("line1", LendingConstants.line1);
        body.put("line2", LendingConstants.line2);
        body.put("line3", LendingConstants.line3);
        body.put("landmark", LendingConstants.landmark);
        body.put("city", LendingConstants.city);
        body.put("state", LendingConstants.state);
        body.put("country", LendingConstants.country);
        body.put("pincode", LendingConstants.pincode);
        body.put("IS_PAN_SEARCH_CKYC_SUCCESS", true);


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PostPaidV3SBPValidationSuccess.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);

            if (responseObject.getStatusCode() == 200)
                break;
        }


        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SBP_VALIDATION_SUCCESS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.SBP_VALIDATION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.SBP_VALIDATION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "367");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_F_NAME"), LendingConstants.CKYC_F_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_M_NAME"), LendingConstants.CKYC_M_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_L_NAME"), LendingConstants.CKYC_L_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FATHER_NAME"), LendingConstants.FATHER_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MOTHER_NAME"), LendingConstants.MOTHER_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"), LendingConstants.MARITAL_STATUS);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_SUCCESS_MODE"), LendingConstants.CKYC_SUCCESS_MODE_SBP);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_GENDER"), LendingConstants.CKYC_GENDER);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_EMAIL"), LendingConstants.CKYC_EMAIL);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_DOB"), LendingConstants.CKYC_DOB);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_SUCCESS_MODE"), LendingConstants.CKYC_SUCCESS_MODE_SBP);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_PAN"), PP_PAN_ABFL);
            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");
        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.SBP_VALIDATION_SUCCESS.getStage());

    }


    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC016_SBPValidationSuccess", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC017_FetchLeadAllData() throws JSONException, InterruptedException {

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION,
                LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(
                    responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Upload selfie", groups = {"Regression"}, dependsOnMethods = "TC017_FetchLeadAllData")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC018_UploadSelfie() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.POSTPAID_V3_SOLUTION,
                LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, sessionToken);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        uuid = responseObject.jsonPath().getString("uuid");

        Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
        LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
        Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);

    }

    @Test(description = "Upload Customer Photo", groups = {"Regression"}, dependsOnMethods = "TC018_UploadSelfie")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC019_UploadCustomerPhoto() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.POSTPAID_V3_SOLUTION,
                LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, sessionToken);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        uuid = responseObject.jsonPath().getString("uuid");

        Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
        LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
        Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
        Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);

    }


    @Test(description = "Verify the  details of Uploaded Document", groups = {"Regression"}, dependsOnMethods = "TC019_UploadCustomerPhoto")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC020_VerifyUploadedDocument() {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION,
                LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        List<Object> docTypes = responseObject.jsonPath().getList("documents.docType");
        Assert.assertTrue(docTypes.contains("selfie"));
        Assert.assertTrue(docTypes.contains("others"));

        List<Object> docProvided = responseObject.jsonPath().getList("documents.docProvided");
        Assert.assertTrue(docProvided.contains("selfie"));
        Assert.assertTrue(docProvided.contains("customerPhoto"));

    }

    @Test(description = "Selfie Match Percantage", dependsOnMethods = "TC020_VerifyUploadedDocument", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC021_SelfieMatch() {
        Map<String, String> queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_JWT_Secret);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "SUB-SBP_SELFIE_MATCH");
        body.put("SELFIE_MATCH_PERCENTAGE", LendingConstants.SELFIE_MATCH_PERCENTAGE_100);


        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PostpaidV3SBPSelfiePercantage.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);

            if (responseObject.getStatusCode() == 200)
                break;
        }


        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_COMPLETED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "375");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SELFIE_MATCH_PERCENTAGE"), LendingConstants.SELFIE_MATCH_PERCENTAGE_100);
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());

    }

    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC021_SelfieMatch", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC022_FetchLeadAllData() throws JSONException, InterruptedException {

        TimeUnit.SECONDS.sleep(60);

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }

    @Test(description = "Offer Stage2 Callback", dependsOnMethods = "TC022_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC023_OfferStage2Callback() throws JSONException, InterruptedException {

        Thread.sleep(10000);

        for (int i = 0; i <= 15; i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_FULLERTON, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
                break;
            }
        }

        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stageId").equals("231")) {

            requestBodyJsonPath = "MerchantService/V1/workflow/lead/callback/PPv3ABFLCallbackRequest.json";
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);

            Map<String, String> headers = new HashMap<String, String>();
            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);
            headers.put("Authorization", token);
            headers.put("custId", custId);
            headers.put("Content-Type", "application/json;charset=utf-8");

            // String offerdetailsStage2 = "{\"loanDownGradable\":false,\"offers\":[{\"baseId\":\"edfb562a-e700-440e-9e38-dba79b497af4\",\"convFee\":0.0,\"customerId\":\"1001408658\",\"introductoryOfferText\":[],\"lenderInfo\":{\"lender\":6,\"lenderAddress\":\"OneIndiabullsCentre,Tower1,18thFloor,JupiterMillCompound841,SenapatiBapatMarg,ElphinstoneRoad,Mumbai400013\",\"lenderCin\":\"U65990GJ1991PLC064603\",\"lenderCode\":\"ABFL\",\"lenderLogo\":\"https://lms-static.paytm.com/lending/images/brand/lender_logo_ABFL.jpg\",\"lenderName\":\"ADITYABIRLAFINANCELTD\"},\"maxAmount\":32500.0,\"maxTenure\":24,\"minAmount\":32500.0,\"minTenure\":24,\"offerEndDate\":1654108200000,\"offerId\":\"edfb562a-e700-440e-9e38-dba79b497af4\",\"offerStartDate\":1646332200000,\"processingFeePercentage\":0.0,\"processingFeeText\":\"ConveniencefeewillbechargedonyourmonthlyspendsusingPaytmPostpaid(e.g.?0.0onspendsof?100)\",\"productId\":\"10010\",\"productType\":\"POSTPAID\",\"productVariant\":\"DELITE\",\"productVariantId\":2,\"productVersion\":\"1\",\"profilesExecuted\":[],\"riskGrade\":\"VL\",\"sourceOfWhitelist\":\"CIR\",\"subProductType\":\"POSTPAID_DELITE\",\"tenureUnit\":\"MONTH\"}],\"skipMandate\":false,\"status\":\"SUCCESS\"}}";

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "OFFER_STAGE2_SUCCESS");
            body.put("LENDER_APPROVED_OFFER", new Gson().toJsonTree(offerDetails));
            body.put("LENDER_APPROVED_OFFER_TIMESTAMP", "1663227274000");
            for (int i = 0; i < 2; i++) {

                String requestBodyPath = "MerchantService/V1/workflow/lead/PostpaidV3OfferStage2Request.json";
                responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyPath);

                if (responseObject.getStatusCode() == 200)
                    break;
            }

            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_APPROVED_OFFER.getStage())) {
                LOGGER.info("BRE 1 passed with callback");
                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LENDER_APPROVED_OFFER.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LENDER_APPROVED_OFFER.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "376");
            }
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        }


    }

    @Test(description = "Fetch Dynamic T and C", groups = {"Regression"}, dependsOnMethods = "TC023_OfferStage2Callback")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC024_FetchDynamicTnc() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);

        Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " + responseObject.getStatusCode());
        if (responseObject.jsonPath().getString("meta.status").equals("success")) {

            code = responseObject.jsonPath().getString("data.state.code");
            tncName = responseObject.jsonPath().getString("data.state.tncName");
            url = responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5 = responseObject.jsonPath().getString("data.state.md5");
        }

    }

    @Test(description = "Accept Loan Agreement", dependsOnMethods = "TC024_FetchDynamicTnc", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC025_AcceptLoanAgreement() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2);


        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
        body.put("LENDING_DYNAMIC_TNC", tncName);
        body.put("KYB_TNC_REF_NO", uniqueIdentifier);
        body.put("TNC_ACCEPTED_CODE", md5);
        body.put("TNC_ACCEPTED_VERSION", 1);
        body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");


        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.v1WorkflowLeadLoanAgreementAccept(queryParams, headers, body, "loan");

            if (responseObject.getStatusCode() == 200)
                break;
        }

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());

    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA", dependsOnMethods = "TC025_AcceptLoanAgreement", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC026_FetchLeadAllData() throws JSONException, InterruptedException {

        Thread.sleep(10000);

        for (int i = 0; i < 10; i++) {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);

            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_OFFER_REQUESTED.getStage()))
                break;

        }

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage())) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));


            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "346");

        }
    }

    @Test(description = "LMS Callback", dependsOnMethods = "TC026_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC027_LMSCallback() {


        for (int i = 0; i < 10; i++) {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION,
                    LendingConstants.CLIX_SOLUTION_TYPE_LEVEL_2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL,
                    LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);

            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").
                    equals(LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage()))
                break;


            else {

                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION,
                        LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);

                Map<String, String> headers = new HashMap<String, String>();
                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);
                headers.put("Authorization", token);
                headers.put("custId", custId);
                headers.put("Content-Type", "application/json;charset=utf-8");


                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "LMS_APPLICATION_APPROVED");
                body.put("LENDER_LOAN_ACCOUNT_NUMBER", "PYTMPPABFL100140865857355");
                body.put("LOAN_ACCOUNT_NUMBER", "80d283c9-7779-417b-9209-ad823ce84a17");
                body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
                body.put("LOAN_ACCOUNT_CREATED_ON", "*************");


                for (int j = 0; j < 2; j++) {

                    requestBodyJsonPath = "MerchantService/V1/workflow/lead/PPV3LMSCallback.json";
                    responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath);

                    if (responseObject.getStatusCode() == 200)
                        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                    Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
                    break;
                }
            }
        }
    }
}