package OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.LeadReset;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import com.goldengate.common.BaseMethod;
import com.opencsv.CSVWriter;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.annotations.Test;

import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class O_BulkReset_Lead_PersonalLoan extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(O_BulkReset_Lead_PersonalLoan.class);
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();

    String consumerNumber = "";
    String requestBodyJsonPath = "";
    String custId = "";
    String sessionToken = "";
    String consumerPassword = "paytm@123";
    String leadId = "";
    String entityType = "INDIVIDUAL";
    String token = "INDIVIDUAL";
    String channel = "PAYTM_APP";
    String loanOfferID = "f2e3eeb1-ba7e-4fc4-9ab5-2613fa5aff72";
    String baseID = "PL_HERO_1002177766_6db0342a";

    Map<String, String> commonHeaders;
    Response responseObject = null;


    @Test(description = "Create Personal Loan Lead with all details.", groups = {"Regression"}, invocationCount = 2)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreatePersonalLoanLead() throws IOException {

        long max = 7_999_999_999L;
        long min = 7_000_000_000L;
        long range = max - min + 1;
        long mobileNumber = (int) (Math.random() * range) + min;
        consumerNumber = String.valueOf(mobileNumber);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", consumerNumber);
        body.put("loginPassword", "paytm@123");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/OAuthUserRandomNumber.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.GenerateNewUser(headers, body);

            if (responseObject.getStatusCode() == 200) break;
        }


        GetCustId();
        custId = responseObject.jsonPath().getString("userId");


        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(String.valueOf(consumerNumber), consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);


        CreatLeadPersonalLoan_1();
        leadId = responseObject.jsonPath().getString("leadId");


        String filePath = "./BulkStageMovementPersonalLoan.csv";
        CSVWriter writer = new CSVWriter(new FileWriter(filePath, true));

        List<String[]> data = new ArrayList<String[]>();
        for (int j = 0; j < 1; j++) {
            data.add(new String[]{leadId, custId,"personal_loan_v3","LEAD_FORCEFULLY_CLOSED","Product"});
        }

        writer.writeAll(data);
        writer.close();


    }

    public Response GetCustId() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("userData", consumerNumber);
        body.put("fetch_strategy", "mobile");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/GetCustIdviaMobileNumber.json";

        for (int k = 0; k < 2; k++) {

            responseObject = lendingBaseClassObject.FetchMobileNumber(headers, body);

            if (responseObject.getStatusCode() == 200) break;
        }
        return responseObject;
    }


    public Response CreatLeadPersonalLoan_1() {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
        queryParams.put("entityType", entityType);
        queryParams.put("channel", channel);

        Map<String, String> header = new HashMap<String, String>();
        header = LendingBaseAPI.setHeadersReceivedFromFE();
        header.put("Authorization", token);
        header.put("Content-Type", "application/json");
        header.put("custid", custId);
        header.put("ipAddress", LendingConstants.IP_ADDRESS);


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);

        body.put("PRODUCT_ID", "25");
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_OFFER_ID", loanOfferID);
        body.put("PRODUCT_VERSION", "1");
        body.put("BASE_ID", baseID);
        body.put("LENDER_ID", "5");
        body.put("WHITELISTING_SOURCE", "RISK");
        body.put("IS_EMANDATE_ELIGIBLE", "true");
        body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
        body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
        body.put("IS_FATHER_NAME_REQUIRED", "true");
        body.put("MARITAL_STATUS", "NOT_KNOWN");
        body.put("IS_BRE3_REQUIRED", "true");
        body.put("PINCODE", "600024");
        body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "FALSE");
        body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
        body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "FALSE");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/CreatePLMigerationHeroLeadRequest.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body, requestBodyJsonPath);
        return responseObject;
    }

}
