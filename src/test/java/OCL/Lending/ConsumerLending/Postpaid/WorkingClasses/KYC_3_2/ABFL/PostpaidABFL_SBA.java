//package OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.KYC_3_2.ABFL;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingConstants;
//import Services.LendingService.LendingLeadStages;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//import com.goldengate.common.BaseMethod;
//import com.google.gson.Gson;
//import com.paytm.apitools.util.annotations.Owner;
//import io.restassured.response.Response;
//import org.apache.log4j.Logger;
//import org.awaitility.Awaitility;
//import org.json.JSONArray;
//import org.json.JSONException;
//import org.json.JSONObject;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.concurrent.TimeUnit;
//
//
//public class PostpaidABFL_SBA extends BaseMethod {
//
//    private static final Logger LOGGER = Logger.getLogger(PostpaidABFL_SBA.class);
//    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
//
//
//    String sessionToken = "";
//    String leadId = "";
//    String custId = "1700041787";
//    String consumerNumber = "7444595555";
//    String consumerPassword = "paytm@123";
//    String token = "";
//    String userIPAddress = "";
//    String code = "";
//    String tncName = "";
//    String url = "";
//    String uniqueIdentifier = "";
//    String md5 = "";
//    String staticTncAcceptanceTimeStamp = "";
//    String bureauRequest = "";
//    String bureauResponse = "";
//    String bureauCreditState = "";
//    String breCreditScore = "";
//    String bureauPullTimeStamp = "";
//    String offerDetails;
//    String offerId;
//    String stringify_json = "{\\\"bureauData\\\":{\\\"bureauName\\\":\\\"CIBIL\\\",\\\"creditScore\\\":738,\\\"pulledAt\\\":1661840851000},\\\"loanDownGradable\\\":false,\\\"newOfferGenerated\\\":false,\\\"offers\\\":[{\\\"baseId\\\":\\\"26b4e451-fc1d-4c8a-bd3e-218f5cb10639\\\",\\\"bureauKicker\\\":false,\\\"convFee\\\":1.0,\\\"customerId\\\":\\\"1002105333\\\",\\\"introductoryOfferText\\\":[],\\\"lenderInfo\\\":{\\\"lender\\\":6,\\\"lenderAddress\\\":\\\"OneIndiabullsCentre,Tower1,18thFloor,JupiterMillCompound841,SenapatiBapatMarg,ElphinstoneRoad,Mumbai400013\\\",\\\"lenderCin\\\":\\\"U65990GJ1991PLC064603\\\",\\\"lenderCode\\\":\\\"ABFL\\\",\\\"lenderLogo\\\":\\\"https://lms-static.paytm.com/lending/images/brand/lender_logo_ABFL.jpg\\\",\\\"lenderName\\\":\\\"ADITYABIRLAFINANCELTD\\\"},\\\"maxAmount\\\":47000.0,\\\"maxTenure\\\":12,\\\"minAmount\\\":47000.0,\\\"minTenure\\\":12,\\\"offerEndDate\\\":1661279400000,\\\"offerId\\\":\\\"26b4e451-fc1d-4c8a-bd3e-218f5cb10639\\\",\\\"offerStartDate\\\":1661840851000,\\\"processingFeePercentage\\\":1.0,\\\"processingFeeText\\\":\\\"ConveniencefeewillbechargedonyourmonthlyspendsusingPaytmPostpaid(e.g.?1.0onspendsof?100)\\\",\\\"productId\\\":\\\"71\\\",\\\"productType\\\":\\\"POSTPAID\\\",\\\"productVariant\\\":\\\"DELITE\\\",\\\"productVariantId\\\":1,\\\"productVersion\\\":\\\"1\\\",\\\"profilesExecuted\\\":[],\\\"riskGrade\\\":\\\"VH\\\",\\\"sourceOfWhitelist\\\":\\\"CIR\\\",\\\"subProductType\\\":\\\"POSTPAID_CONVINENCE_FEE\\\",\\\"tenureUnit\\\":\\\"MONTH\\\"}],\\\"reasons\\\":[],\\\"skipMandate\\\":false,\\\"status\\\":\\\"SUCCESS\\\",\\\"transactionId\\\":\\\"2cf599e7-7901-4f38-acd4-1fba664e070a\\\"}}";
//
//    String requestBodyJsonPath = "";
//
//    Map<String, String> commonHeaders;
//    Response responseObject = null;
//    @BeforeClass()
//    public void intitializeInputData() {
//
//        LOGGER.info(" Before Suite Method for Consumer Login ");
//        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
//        LOGGER.info("Applicant Token for Lending : " + sessionToken);
//    }
//
//    @Test(description = "Verify whether there is any existing postpaid lead present or not", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC001_FetchLeadDeatils() {
//
//        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken, custId);
//
//        if (responseObject.jsonPath().getInt("statusCode") == 200) {
//            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
//
//            leadId = responseObject.jsonPath().getString("leadId");
//        }
//
//        if (responseObject.jsonPath().getInt("statusCode") == 404) {
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//        }
//
//    }
//
//    @Test(description = "Reset the existing lead of the number", dependsOnMethods = "TC001_FetchLeadDeatils", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC002_DeleteExistingLead() {
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams.put("leadId", leadId);
//        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
//        queryParams.put("custId", custId);
//
//        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_QA, custId, LendingConstants.LENDING_QA_SECRET);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Authorization", token);
//        headers.put("custId", custId);
//
//        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
//    }
//
//    @Test(description = "Create PostPaid Lead with all deatils", dependsOnMethods = "TC002_DeleteExistingLead", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC003_CreatePostpaidLead() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
//        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers = LendingBaseAPI.setHeadersReceivedFromFE();
//        headers.put("session_token", sessionToken);
//        headers.put("Content-Type", "application/json");
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowOperation", "CREATE_LEAD");
//        body.put("mobile", consumerNumber);
//        body.put("BUREAU_PRIORITY", LendingConstants.BUREAU_PRIORITY);
//        body.put("LENDING_KYB_SOURCE", LendingConstants.LENDING_KYB_SOURCE);
//        body.put("IS_SUSPEND_LEAD", "TRUE");
//        body.put("IS_CREATE_LEAD", "TRUE");
//        body.put("IS_SAVE_STATIC_TNC", "TRUE");
//        body.put("DOB", LendingConstants.CKYC_DOB);
//        body.put("PAN", LendingConstants.PP_PAN_ABFL_KYC_3_2);
//        body.put("EMAIL", LendingConstants.CKYC_EMAIL);
//        body.put("PAN_DOB_VALIDATED", "TRUE");
//        body.put("IS_RTO_FLOW", "FALSE");
//        body.put("PRODUCT_TYPE","POSTPAID");
//
//        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PostPaidV3CreateLead.json";
//
//        Map<String, String> finalHeaders1 = headers;
//        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
//        {
//            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders1, body, requestBodyJsonPath);
//            return responseObject.getStatusCode()==201;
//        });
//
//        if (responseObject.getStatusCode() == 201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage())) {
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
//            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
//            Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"), "STATIC_LOAN_OFFER_TNC");
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"), LendingConstants.deviceManufacturer);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"), "paytm_postpaid_happyloans");
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_SAVE_STATIC_TNC"), "TRUE");
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_KYB_SOURCE"), LendingConstants.LENDING_KYB_SOURCE);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"), LendingConstants.BUREAU_PRIORITY);
//
//            leadId = responseObject.jsonPath().getString("leadId");
//            custId = responseObject.jsonPath().getString("custId");
//            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
//            staticTncAcceptanceTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
//
//        }
//
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
//
//    }
//
//    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC003_CreatePostpaidLead", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC004_FetchLeadAllData() {
//
//        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken, custId);
//
//        if (responseObject.jsonPath().getInt("statusCode") == 200) {
//            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
//
//            leadId = responseObject.jsonPath().getString("leadId");
//        }
//
//        if (responseObject.jsonPath().getInt("statusCode") == 404) {
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//        }
//
//    }
//
//    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC004_FetchLeadAllData", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC005_UpdateBureauDataSetInSAI() throws InterruptedException {
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
//        queryParams.put("leadId", leadId);
//        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//
//        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Authorization", token);
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowMovementRequired", false);
//        body.put("status", "SUCCESS");
//        body.put("F_NAME", "BHAIRAVI");
//        body.put("L_NAME", "LATASREE");
//        body.put("GENDER", "FEMALE");
//        body.put("PINCODE", "600024");
//        body.put("PAN", "**********");
//        body.put("DOB", "1979-10-05");
//        body.put("EMAIL", "<EMAIL>");
//
//        responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body, true);
//
//        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//
//        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
//
//    }
//
//    @Test(description = "Fetch CIR", dependsOnMethods = "TC005_UpdateBureauDataSetInSAI", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC006_FetchCIR() throws JSONException {
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//                LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", sessionToken);
//        headers.put("Content-Type", "application/json");
//
//        Map<String, Object> body = new HashMap<String, Object>();
//
//        String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
//        for (int i = 0; i < 6; i++) {
//            responseObject = lendingBaseClassObject.v3FetchCIR(queryParams, headers, body, requestjsonpath);
//
//            if (responseObject.getStatusCode() == 200)
//
//                break;
//
//        }
//
//        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BUREAU_SUCCESS.getStage());
//        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BUREAU_SUCCESS.getStage());
//        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
//        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
//        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
//        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//
//        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "194");
//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"), "TRUE");
//        Assert.assertEquals(responseObject.jsonPath().getString("creditState"), "BUREAU_SUCCESS");
//
//    }
//
//    @Test(description = "Verify lead details after Fetch CIR", groups = {"Regression"}, dependsOnMethods = "TC006_FetchCIR")
//    @Owner(emailId = "<EMAIL>")
//    public void TC007_BRE1Callback() throws JSONException, InterruptedException {
//
//        Thread.sleep(10000);
//        for (int i = 0; i <= 15; i++) {
//            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
//
//            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
//                break;
//            }
//        }
//        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage())) {
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_REQUESTED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "225");
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//            for(int i =0;i<15;i++) {
//                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.POSTPAID_V3_SOLUTION,LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OFFER_GENERATED.getStage())) {
//                    LOGGER.info("BRE 1 passed without callback");
//                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
//                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
//                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
//                    break;
//                }
//            }
//            requestBodyJsonPath = "MerchantService/V1/workflow/lead/callback/PPv3ABFLCallbackRequest.json";
//           BRE1Callback(requestBodyJsonPath);
//            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OFFER_GENERATED.getStage())) {
//                LOGGER.info("BRE 1 passed with callback");
//                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
//                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
//                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
//            }
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//        }
//    }
//
//    public Response BRE1Callback(String requestBodyJsonPath) {
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
//        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
//
//        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Authorization", token);
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowOperation", "OFFER_STAGE1_SUCCESS");
//        body.put("OFFER_DETAILS", "" + stringify_json + "");
//        body.put("OFFER_TIMESTAMP", System.currentTimeMillis());
//        body.put("BUREAU_PULL_TIMESTAMP", System.currentTimeMillis());
//
//        responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath);
//
//        return responseObject;
//
//    }
//
//    @Test(description = "Verify the Postpaid lead data after BRE1 Success", groups = {"Regression"}, dependsOnMethods = "TC007_BRE1Callback")
//    @Owner(emailId = "<EMAIL>")
//    public void TC008_FetchDataPostBRE1Success() throws JSONException {
//
//        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
//
//        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
//            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"), bureauRequest);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"), bureauResponse);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"), bureauCreditState);
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"), breCreditScore);
//
//        }
//        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
//    }
//
//    @Test(description = "Verify the current stage of the leadId. ", dependsOnMethods = "TC008_FetchDataPostBRE1Success", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC009_FetchLeadAllData() throws JSONException, InterruptedException {
//
//        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
//
//        if (responseObject.jsonPath().getInt("statusCode") == 200) {
//            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//            leadId = responseObject.jsonPath().getString("leadId");
//        }
//
//        if (responseObject.jsonPath().getInt("statusCode") == 404) {
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//        }
//
//        bureauRequest = responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
//        bureauResponse = responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
//        bureauCreditState = responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
//
//        Thread.sleep(20000);
//
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
//        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
//        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
//        leadId = responseObject.jsonPath().getString("leadId");
//
//        bureauPullTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PULL_TIMESTAMP");
//        System.out.println("Nishant --" + responseObject.jsonPath().getJsonObject("solutionAdditionalInfo.OFFER_DETAILS"));
//        offerDetails = responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_DETAILS");
//        JSONObject jsnobject = new JSONObject(offerDetails);
//
//        LOGGER.info("OFFER DETAILS " + offerDetails);
//        LOGGER.info("OFFER DETAILS " + jsnobject);
//
//        JSONArray ar = (JSONArray) jsnobject.get("offers");
//        offerId = ar.getJSONObject(0).getString("offerId");
//        System.out.println(offerId);
//
//        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OFFER_GENERATED.getStage());
//
//    }
//
//    @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC009_FetchLeadAllData")
//    @Owner(emailId = "<EMAIL>")
//    public void TC010_PPV3ABFL_UpdateLeadDetailsinSAI() {
//        Map<String,String> queryParams=new HashMap<String,String>();
//        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.POSTPAID_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("solutionTypeLevel2", LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2);
//
//        Map<String,String> headers=new HashMap<String,String>();
//        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//        headers = LendingBaseAPI.setHeadersReceivedFromFE();
//        headers.put("Authorization", token);
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowMovementRequired", false);
//        body.put("PAN", "**********");
//        body.put("DOB", "1992-05-01");
//        body.put("GENDER", "MALE");
//        body.put("NSDL_NAME", "Nagu Singh");
//
//        requestBodyJsonPath = "MerchantService/V1/workflow/lead/UpdateSAIRequest.json";
//
//        responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body, requestBodyJsonPath);
//
//        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
//    }
//
//
//    @Test(description = "Selfie Required", dependsOnMethods = "TC010_PPV3ABFL_UpdateLeadDetailsinSAI", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC011_SELFIEREQUIRED() throws JSONException, InterruptedException {
//
//        Thread.sleep(10000);
//        Map<String, String> queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers = LendingBaseAPI.setHeadersReceivedFromFE();
//        headers.put("session_token", sessionToken);
//        headers.put("Content-Type", "application/json");
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowOperation", "SELFIE_REQUIRED");
//        body.put("LOAN_OFFER_ID", offerId);
//
//        responseObject = lendingBaseClassObject.v1WorkflowLeadUpdateOffer(queryParams, headers, body, true);
//
//        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage())) {
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2020");
//            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"), offerId);
//        }
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
//    }
//
//    @Test(description = "Verify the Postpaid lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC011_SELFIEREQUIRED")
//    @Owner(emailId = "<EMAIL>")
//    public void TC012_UploadSelfie() throws InterruptedException {
//
//        Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
//                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,
//                sessionToken,"selfie","SEARCH_BY_PAN.jpeg");
//
//        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//
//        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
//        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
//        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
//        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
//    }
//
//
//    @Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC012_UploadSelfie")
//    @Owner(emailId = "<EMAIL>")
//    public void TC013_VerifyUploadedCustomerPhoto() {
//
//        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.POSTPAID_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//
//        Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
//        Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");
//
//    }
//
//
//
//    @Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC013_VerifyUploadedCustomerPhoto")
//    @Owner(emailId = "<EMAIL>")
//    public void TC014_InitiateKYC_UsingSearchByPan() throws InterruptedException {
//        Map<String,String> queryParams=new HashMap<String,String>();
//        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.POSTPAID_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//
//        Map<String,String> headers=new HashMap<String,String>();
//        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//        headers = LendingBaseAPI.setHeadersReceivedFromFE();
//        headers.put("Authorization", token);
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//
//        Map<String,Object> body=new HashMap<String,Object>();
//        body.put("kycMode","SEARCH_BY_AADHAAR");
//        body.put("aadhaar","2671");
//        body.put("gender","MALE");
//
//
//        requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByAAdhaarRequest.json";
//
//        responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");
//
//        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//        Thread.sleep(10000);
//        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
//        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
//        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");
//    }
//
//    @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC014_InitiateKYC_UsingSearchByPan")
//    @Owner(emailId = "<EMAIL>")
//    public void TC015_PPV3ABFL_UpdateLeadDetailsinSAI() {
//        Map<String,String> queryParams=new HashMap<String,String>();
//        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.POSTPAID_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("solutionTypeLevel2", LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);
//        headers = LendingBaseAPI.setHeadersReceivedFromFE();
//        headers.put("Authorization", token);
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowMovementRequired", false);
//        body.put("PAN", "**********");
//        body.put("DOB", "1973-01-01");
//        body.put("GENDER", "MALE");
//        body.put("NSDL_NAME", "NAGU SINGH");
//
//        requestBodyJsonPath = "MerchantService/V1/workflow/lead/UpdateSAIRequest.json";
//
//        responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body, requestBodyJsonPath);
//
//        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
//    }
//
//    @Test(description = "Offer Stage2 Callback", dependsOnMethods = "TC015_PPV3ABFL_UpdateLeadDetailsinSAI", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC016_OfferStage2Callback() throws JSONException, InterruptedException {
//
//        Thread.sleep(10000);
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);
//        headers.put("Authorization", token);
//        headers.put("custId", custId);
//        headers.put("Content-Type", "application/json;charset=utf-8");
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowOperation", "OFFER_STAGE2_SUCCESS");
//        body.put("LENDER_APPROVED_OFFER", new Gson().toJsonTree(offerDetails));
//        body.put("LENDER_APPROVED_OFFER_TIMESTAMP", System.currentTimeMillis());
//        System.out.println(System.currentTimeMillis());
//        for (int i = 0; i < 2; i++) {
//
//            String requestBodyPath = "MerchantService/V1/workflow/lead/PostpaidV3OfferStage2Request.json";
//            responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyPath);
//
//            if (responseObject.getStatusCode() == 200) break;
//        }
//    }
//
//    @Test(description = "Fetch Dynamic T and C", groups = {"Regression"}, dependsOnMethods = "TC016_OfferStage2Callback")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC017_FetchDynamicTnc() {
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("solutionTypeLevel2", LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", sessionToken);
//
//        Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//
//        LOGGER.info("Status Code : " + responseObject.getStatusCode());
//        if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//
//            code = responseObject.jsonPath().getString("data.state.code");
//            tncName = responseObject.jsonPath().getString("data.state.tncName");
//            url = responseObject.jsonPath().getString("data.state.url");
//            uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//            md5 = responseObject.jsonPath().getString("data.state.md5");
//        }
//
//    }
//
//    @Test(description = "Accept Loan Agreement", dependsOnMethods = "TC017_FetchDynamicTnc", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC018_AcceptLoanAgreement() {
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//        queryParams.put("solutionTypeLevel2", LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2);
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers = LendingBaseAPI.setHeadersReceivedFromFE();
//        headers.put("session_token", sessionToken);
//        headers.put("Content-Type", "application/json");
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
//        body.put("LENDING_DYNAMIC_TNC", tncName);
//        body.put("KYB_TNC_REF_NO", uniqueIdentifier);
//        body.put("TNC_ACCEPTED_CODE", md5);
//        body.put("TNC_ACCEPTED_VERSION", 1);
//        body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");
//
//
//        for (int i = 0; i < 2; i++) {
//
//            responseObject = lendingBaseClassObject.v1WorkflowLeadLoanAgreementAccept(queryParams, headers, body, "loan");
//
//            if (responseObject.getStatusCode() == 200) break;
//        }
//
//        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage())) {
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
//            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
//             Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "350");
//            Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
//
//        }
//        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
//    }
//
//    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA", dependsOnMethods = "TC018_AcceptLoanAgreement", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC019_FetchLeadAllData() throws JSONException, InterruptedException {
//
//        Thread.sleep(10000);
//        TimeUnit.SECONDS.sleep(60);
//        for (int i = 0; i < 10; i++) {
//
//            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
//
//            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_OFFER_REQUESTED.getStage()))
//                break;
//        }
//
//        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.POSTPAID_V3_SOLUTION_LEVEL2, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
//
//        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage())) {
//            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
//            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
//
//            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
//            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");
//
//            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
//            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "346");
//
//        }
//    }
//
//    @Test(description = "LMS Callback", dependsOnMethods = "TC019_FetchLeadAllData", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC020_LMSCallback() {
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.POSTPAID_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//        Map<String, String> headers = new HashMap<String, String>();
//        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);
//        headers.put("Authorization", token);
//        headers.put("custId", custId);
//        headers.put("Content-Type", "application/json;charset=utf-8");
//
//
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("workflowOperation", "LMS_APPLICATION_APPROVED");
//        body.put("LENDER_LOAN_ACCOUNT_NUMBER", "PYTMPPABFL100140865857355");
//        body.put("LOAN_ACCOUNT_NUMBER", "80d283c9-7779-417b-9209-ad823ce84a17");
//        body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//        body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
//
//
//        for (int i = 0; i < 2; i++) {
//
//            requestBodyJsonPath = "MerchantService/V1/workflow/lead/PPV3LMSCallback.json";
//            responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath);
//
//            if (responseObject.getStatusCode() == 200)
//                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
//            break;
//        }
//    }
//}