package OCL.Lending.ConsumerLending.Postpaid.WorkingClasses.LeadReset;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.opencsv.CSVWriter;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.annotations.Test;

import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class
O_BulkReset_Lead_Postpaid extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(O_BulkReset_Lead_Postpaid.class);
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility = new Utilities();

    String consumerNumber = "";
    String requestBodyJsonPath = "";
    String custId = "";
    String sessionToken = "";
    String consumerPassword = "paytm@123";
    String leadId = "";
    String PP_PAN_ABFL = "";
    String token = "";
    String bre1OfferDetails ="{\\\"baseId\\\":\\\"PL_HERO_Shubham_5a60e65f\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"field_investigation_needed\\\":false,\\\"isBSAOffer\\\":false,\\\"isBre2Required\\\":false,\\\"lenderSchemeId\\\":\\\"90\\\",\\\"loanDownGradable\\\":false,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":110000.0,\\\"minLoanAmount\\\":10000.0,\\\"newOfferGenerated\\\":true,\\\"offerEndDate\\\":\\\"Tue Nov 08 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"f3a759d6-cb2a-41e2-a7cb-e38ca493058c\\\",\\\"offerStartDate\\\":\\\"Wed Aug 10 00:00:00 IST 2022\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"78\\\",\\\"productVersion\\\":1,\\\"riskGrade\\\":\\\"M\\\",\\\"riskSegment\\\":\\\"M\\\",\\\"skipMandate\\\":false,\\\"sourceOfWhitelist\\\":\\\"RISK\\\"}";
    String bsa1Config = "{\\\"acceptancePolicy\\\":\\\"AT_LEAST_ONE_TXN_PER_MONTH\\\",\\\"bankingStatementRequiredMonth\\\":4}";
    String bsa1OfferDetails = "{\\\"baseId\\\":\\\"PL_ABFL_1002297970_c02856d0\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"field_investigation_needed\\\":false,\\\"isBSAOffer\\\":true,\\\"isBre2Required\\\":false,\\\"lenderSchemeId\\\":\\\"90\\\",\\\"loanDownGradable\\\":false,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":150000.0,\\\"minLoanAmount\\\":10000.0,\\\"newOfferGenerated\\\":true,\\\"offerEndDate\\\":\\\"Wed Nov 30 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"9999c32b-a2ab-4f52-86e4-0d5bbcb0e749\\\",\\\"offerStartDate\\\":\\\"Thu Sep 01 00:00:00 IST 2022\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"78\\\",\\\"productVersion\\\":1,\\\"riskGrade\\\":\\\"M\\\",\\\"riskSegment\\\":\\\"M\\\",\\\"skipMandate\\\":false,\\\"sourceOfWhitelist\\\":\\\"RISK\\\"}";
    String bureauRequest = "{\\\"email\\\":\\\"<EMAIL>\\\",\\\"pan\\\":\\\"**********\\\",\\\"dob\\\":\\\"05-Oct-1979\\\",\\\"bureau\\\":\\\"CIBIL\\\",\\\"flow\\\":\\\"PL\\\",\\\"pincode\\\":\\\"133001\\\",\\\"first_name\\\":\\\"BHAIRAVI\\\",\\\"last_name\\\":\\\"LATASREE\\\",\\\"mobile_no\\\":\\\"5466672210\\\",\\\"txn_id\\\":\\\"b5da0b63-d636-4e2f-a685-2bf307ef83ef\\\",\\\"tnc_code\\\":\\\"bureau_credit_score\\\",\\\"tnc_version\\\":1}";
    String bureauResponse = "{\\\"refId\\\":\\\"C2ID-240\\\",\\\"statusCode\\\":200,\\\"credit_state\\\":\\\"COMPLETE_PROFILE_FETCHED\\\",\\\"user_bureau_info\\\":[{\\\"name\\\":\\\"CIBIL\\\",\\\"credit_state\\\":\\\"COMPLETE_PROFILE_FETCHED\\\",\\\"credit_score\\\":738}],\\\"user_basic_info\\\":{\\\"first_name\\\":\\\"BHAIRAVI\\\",\\\"last_name\\\":\\\"LATASREE\\\",\\\"dob\\\":\\\"1979-10-04T18:30:00.000+0000\\\",\\\"identification_type\\\":\\\"PAN\\\",\\\"identification_id\\\":\\\"**********\\\"}}";
    String offerDetails = "{\\\"baseId\\\":\\\"mca_fullerton_vinay2_2111_d4fa147e\\\",\\\"bureau\\\":\\\"CIBIL\\\",\\\"bureauKicker\\\":false,\\\"creditScore\\\":738,\\\"field_investigation_needed\\\":false,\\\"incentiveRate\\\":0.0,\\\"isAcceptanceAbove5000\\\":1,\\\"isBSAOffer\\\":false,\\\"isBre2Required\\\":false,\\\"isEmandateEligible\\\":0,\\\"isIncentiveAllowed\\\":0,\\\"isPaytmVintageOlderThan90d\\\":1,\\\"isRenewal\\\":false,\\\"isRestrictedMerchant\\\":0,\\\"isSiMandatory\\\":\\\"1\\\",\\\"lastFetchDate\\\":1660003200000,\\\"loanDownGradable\\\":false,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":50000.0,\\\"maxTenure\\\":360,\\\"minLoanAmount\\\":10000.0,\\\"minTenure\\\":360,\\\"newOfferGenerated\\\":false,\\\"offerEndDate\\\":\\\"Tue Feb 07 00:00:00 IST 2023\\\",\\\"offerId\\\":\\\"mca_fullerton_vinay2_2111_d4fa147e_NEW_OFFER_MCA_fb5fbe1\\\",\\\"offerStartDate\\\":\\\"Mon Feb 21 00:00:00 IST 2022\\\",\\\"pfFeeRate\\\":1.0,\\\"productId\\\":\\\"85\\\",\\\"productVersion\\\":1,\\\"rateOfInterest\\\":30.0,\\\"riskGrade\\\":\\\"MCA|DRB10\\\",\\\"skipMandate\\\":false,\\\"sourceOfWhitelist\\\":\\\"Risk\\\"}";
    String pennyDropData = "{\\\"bankAccount\\\":\\\"************\\\",\\\"pennyDropTimestamp\\\":\\\"2022-06-07 16:31:59.0\\\",\\\"pennyDropStatus\\\":\\\"SUCCESS\\\",\\\"pennyDropName\\\":\\\"Paytm\\\",\\\"source\\\":\\\"LENDING\\\",\\\"upiDetails\\\":{},\\\"ifsc\\\":\\\"PYTM0123456\\\",\\\"pennyDropDataEmpty\\\":false,\\\"upiDataEmpty\\\":true}";
    String supplementaryOfferDetails = "{\\\"action\\\":\\\"SKIP_MANDATE\\\",\\\"baseId\\\":\\\"7a6eb985-d0d1-46a4-9fa6-c281a962531d\\\",\\\"bureau\\\":\\\"CIBIL\\\",\\\"bureauThick\\\":1,\\\"creditScore\\\":807,\\\"field_investigation_needed\\\":false,\\\"incentiveRate\\\":0.0,\\\"isEmandateEligible\\\":1,\\\"isIncentiveAllowed\\\":0,\\\"lastFetchDate\\\":*************,\\\"loanDownGradable\\\":false,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":50000.0,\\\"maxTenure\\\":18,\\\"minLoanAmount\\\":30000.0,\\\"minTenure\\\":12,\\\"newOfferGenerated\\\":false,\\\"ntc\\\":0,\\\"offerId\\\":\\\"e7933ac7-e8c5-435b-9a82-2c4b1908d9cc\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"1000156\\\",\\\"productType\\\":\\\"PL\\\",\\\"productVersion\\\":1,\\\"skipMandate\\\":false}";
    String lenderApprovedOffer= "{\\\"bsaEnabled\\\":false,\\\"loanDownGradable\\\":false,\\\"newOfferGenerated\\\":false,\\\"ntcPaytmThin\\\":false,\\\"offers\\\":[{\\\"baseId\\\":\\\"515a49d3-6c54-4129-bd4e-b9296896adc9\\\",\\\"bureauKicker\\\":false,\\\"convFee\\\":2.5,\\\"customerId\\\":\\\"1002053202\\\",\\\"introductoryOfferText\\\":[],\\\"lenderInfo\\\":{\\\"lender\\\":6,\\\"lenderAddress\\\":\\\"One Indiabulls Centre,Tower 1, 18th Floor, Jupiter Mill Compound 841, Senapati Bapat Marg, Elphinstone Road, Mumbai 400 013\\\",\\\"lenderCin\\\":\\\"U65990GJ1991PLC064603\\\",\\\"lenderCode\\\":\\\"ABFL\\\",\\\"lenderLogo\\\":\\\"https://lms-static.paytm.com/lending/images/brand/lender_logo_ABFL.jpg\\\",\\\"lenderName\\\":\\\"ADITYA BIRLA FINANCE LTD\\\"},\\\"maxAmount\\\":6000.0,\\\"maxTenure\\\":12,\\\"minAmount\\\":6000.0,\\\"minTenure\\\":12,\\\"offerEndDate\\\":1667845800000,\\\"offerId\\\":\\\"515a49d3-6c54-4129-bd4e-b9296896adc9\\\",\\\"offerStartDate\\\":1660069800000,\\\"onboardingType\\\":\\\"PRE_ONBOARDING\\\",\\\"processingFeePercentage\\\":2.5,\\\"processingFeeText\\\":\\\"Convenience fee will be charged on your monthly spends using Paytm Postpaid (e.g. ?2.5 on spends of ?100)\\\",\\\"productId\\\":\\\"10009\\\",\\\"productType\\\":\\\"POSTPAID\\\",\\\"productVariant\\\":\\\"LITE\\\",\\\"productVariantId\\\":1,\\\"productVersion\\\":\\\"1\\\",\\\"profilesExecuted\\\":[],\\\"riskGrade\\\":\\\"M\\\",\\\"sourceOfWhitelist\\\":\\\"RISK\\\",\\\"subProductType\\\":\\\"POSTPAID_CONVINENCE_FEE\\\",\\\"tenureUnit\\\":\\\"MONTH\\\"}],\\\"skipMandate\\\":false,\\\"status\\\":\\\"SUCCESS\\\"}";
    Map<String, String> commonHeaders;
    Response responseObject = null;


    @Test(description = "Create PostPaid Lead with all deatils", groups = {"Regression"}, invocationCount = 10)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_CreatePostpaidLead() throws IOException {

        long max = 5_999_999_999L;
        long min = 5_000_000_000L;
        long range = max - min + 1;
        long mobileNumber = (int) (Math.random() * range) + min;
        consumerNumber = String.valueOf(mobileNumber);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("mobile", consumerNumber);
        body.put("loginPassword", "paytm@123");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/OAuthUserRandomNumber.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.GenerateNewUser(headers, body);

            if (responseObject.getStatusCode() == 200) break;
        }


        GetCustId();
        custId = responseObject.jsonPath().getString("userId");


        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(String.valueOf(consumerNumber), consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);


        CreatLeadPostpaid_1();
        leadId = responseObject.jsonPath().getString("leadId");


        String filePath = "./BulkStageMovementPostpaid.csv";
        CSVWriter writer = new CSVWriter(new FileWriter(filePath, true));

        List<String[]> data = new ArrayList<String[]>();
        for (int j = 0; j < 1; j++) {
            data.add(new String[]{leadId, custId, "postpaid_v3", "LEAD_FORCEFULLY_CLOSED", "Product"});
        }

        writer.writeAll(data);
        writer.close();
    }

    public Response GetCustId() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("userData", consumerNumber);
        body.put("fetch_strategy", "mobile");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/GetCustIdviaMobileNumber.json";

        for (int k = 0; k < 2; k++) {

            responseObject = lendingBaseClassObject.FetchMobileNumber(headers, body);

            if (responseObject.getStatusCode() == 200) break;
        }
        return responseObject;
    }


    public Response CreatLeadPostpaid_1() {


        PP_PAN_ABFL = UtilitiesObject.randomIndividualPANValueGenerator();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.POSTPAID_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        Map<String, String> headers = new HashMap<String, String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);
        body.put("BUREAU_PRIORITY", LendingConstants.BUREAU_PRIORITY);
        body.put("LENDING_KYB_SOURCE", LendingConstants.LENDING_KYB_SOURCE);
        body.put("IS_SAVE_STATIC_TNC", "TRUE");
        body.put("IS_SUSPEND_LEAD", "TRUE");
        body.put("IS_CREATE_LEAD", "TRUE");
        body.put("DOB", LendingConstants.CKYC_DOB);
        body.put("EMAIL", LendingConstants.CKYC_EMAIL);
        body.put("PAN_DOB_VALIDATED", "TRUE");
        body.put("IS_RTO_FLOW", "FALSE");
        body.put("PRODUCT_TYPE","POSTPAID");
        body.put("PAN", PP_PAN_ABFL);

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PostPaidV3CreateLead.json";

        for (int i = 0; i < 2; i++) {

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);
            System.out.println(responseObject);
            if (responseObject.getStatusCode() == 201) break;
        }

        return responseObject;
    }
}