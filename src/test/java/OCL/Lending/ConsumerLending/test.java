package OCL.Lending.ConsumerLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

public class test extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(test.class);
    String solution="personal_loan_v3";
    String solutionTypeLevel2="HERO";
    String entityType="INDIVIDUAL";
    String channel="PAYTM_APP";
    String leadId = "";
    String sessionToken = "";
    String consumerNumber="6669699999";
    String consumerPassword="paytm@123";
    String custId="1002177766";
    String token="";
    String requestBodyJsonPath="";
    String userIPAddress="";
    String staticTncAcceptanceTimeStamp="";
    String Email="";
    String DOB="";
    String PAN="";
    String occupation="";
    String income="";
    String firstName="BHAIRAVI";
    String lastName="LATASREE";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String stringify_json="{\\\"baseId\\\":\\\"PL_HERO_1002177766_6db0342a\\\",\\\"bureau\\\":\\\"CIBIL\\\",\\\"bureauThick\\\":1,\\\"creditScore\\\":808,\\\"field_investigation_needed\\\":false,\\\"incentiveRate\\\":0.0,\\\"isEmandateEligible\\\":1,\\\"isIncentiveAllowed\\\":0,\\\"lastFetchDate\\\":1633046400000,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":150000.0,\\\"maxTenure\\\":18,\\\"minLoanAmount\\\":30000.0,\\\"minTenure\\\":12,\\\"newOfferGenerated\\\":false,\\\"ntc\\\":0,\\\"offerId\\\":\\\"492fb17b-24ce-4e44-946a-37bbe1bc816f\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"25\\\",\\\"productType\\\":\\\"PL\\\",\\\"productVersion\\\":1,\\\"rejectionReason\\\":\\\"\\\",\\\"riskGrade\\\":\\\"VL\\\"}";
    String loanTenure="";
    String loanAmount="";
    String loanEquatedMonthlyInstallment="";
    String loanRateofInterest="";
    String loanInterestAmount="";
    String loanProcessingFeeRate="";
    String loanDisbursalAmount="";
    String stampDutyCharges="";
    String brokerPeriodInterest="";
    String uuidCustomerPhoto = "";
    String uuidSelfie="";
    String loanOfferID="492fb17b-24ce-4e44-946a-37bbe1bc816f";
    String baseID="PL_HERO_1002177766_6db0342a";
    String ckycName="";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier ="";
    String md5 ="";
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String bankName="PAYTM BANK";
    String bankAccountNumber="************";
    String ifsc="PYTM0123456";
    String newBankAccountNumber="************";
    //String newIfsc="PYTM0123456";
    String bankAccountHolderName="Shivangi Goswami";
    String stageLendingURL="";
    String stageOCLUrl="";
    String stageDB ="";


    Response responseObject= null;
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility=new Utilities();


    @BeforeClass()
    @Parameters({"stagingLendingUrl","stagingOCLUrl","DB"})
    public void intitializeInputData(String stagingLendingUrl,String stagingOCLUrl, String DB) {
        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);
        stageLendingURL=stagingLendingUrl;
        stageOCLUrl=stagingOCLUrl;
        stageDB=DB;
        System.out.println(stageLendingURL+" "+stagingOCLUrl+" "+DB);
    }


    @Test(description = "Verify for PL v3 CKYC name update in SAI",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC038_PLv3HERO_VerifyPennydropForNewBankDetails() {
        responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId, LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
        Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.bankAccount"),newBankAccountNumber);
        Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.ifsc"),ifsc);
        Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.source"),"LENDING");
        Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.pennyDropStatus"),"SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("penny_drop_data.pennyDropName"),"Paytm");

    }

    @Test(description = "Verify for PL v3 CKYC name update in SAI",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC038_PLv3HERO_1VerifyPennydropForNewBankDetails() {
        responseObject = lendingBaseClassObject.fetchLeadDetailsBackFillingAPI(leadId, LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId,stageLendingURL);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

    }
}
