package OCL.Lending.ConsumerLending.Insurance;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.TokenXMV;
import Request.MerchantService.v1.sdMerchant.Lead_create;
import Request.MerchantService.v1.sdMerchant.fetchDynamicTnc;
import Request.MerchantService.v1.sdMerchant.saveDynamicTnc_posAgent;
import Request.MerchantService.v1.sdMerchant.sendOtp_posAgent;
import Request.MerchantService.v2.doc.upload.status.fetchDocStatus_posInsurance;
import Request.MerchantService.v2.doc.upload.status.uploadDoc_posAgent;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v5.callback.CallBackv5_posAgent;
import Services.MechantService.MiddlewareServices;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

public class ins_Pos_Agent extends BaseMethod
{



    private static final Logger LOGGER = LogManager.getLogger(ins_Pos_Agent.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();


    public String AgentNo = "6111111150";
    public static String session_token = "";
    public String custId = "1000514855";
    public String token = "";
    public String leadId = "";
    public String solutionType="ins_pos_agent";
    public String pan = "**********";
    public String md5="";
    public String uniqueIdentifier="";
    public String state="";
    public static String XMWToken = "";
    public String highestQualificationDoc =" ";
    public String passportPhotoDoc = "";
    public String aadhaarDoc_frontPhoto="";
    public String aadhaarDoc_backPhoto="";
    public String workflowStatusId="";
    public String leadStage="";
    public String OTP="";





    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getAgentToken() {
        LOGGER.info(" Before Suite Method for Agent Login ");
        session_token = ApplicantToken(AgentNo,"paytm@123");
        LOGGER.info("Applicant Token for INS POS : " + session_token);
    }




    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "jwt token creation")

    public String generateJwtToken() {

        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30"));
        System.out.println("Date is :" + localDateTime);
        String ts = localDateTime.toString();

        Algorithm buildAlgorithm = Algorithm.HMAC256("67232102-1aae-46b2-8b0a-5e672d13b806");
        token = JWT.create().withIssuer("OE")
                .withClaim("clientId", "INSURANCE")
                .withClaim("custId", custId)
                .withClaim("timestamp", ts + "+05:30").sign(buildAlgorithm);
        return token;
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "positive create lead", priority = 1)
    public void createLead_posInsurance() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "ins_pos_agent");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "DIY_P4B_APP");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", session_token);

        Map<String, String> body = new HashMap<String, String>();
        body.put("pan", pan);
        body.put("IS_PAN_VERIFIED", "true");
        body.put("F_NAME", "JB");
        body.put("L_NAME", "singh");
        body.put("EMAIL", "<EMAIL>");
        body.put("TNC_ADDITIONAL_PARAM","STATIC_INSURANCE_POS_AGENT_TNC");
        body.put("tnCSetName","POC_Tnc1");


        Lead_create createLeadObject_posInsurance = new Lead_create(P.TESTDATA.get("PosInsurance"));

        Response responseObject = middlewareServicesObject.CreateLead(createLeadObject_posInsurance, queryParams, headers, body);

        leadId = responseObject.jsonPath().getString("leadId");
        System.out.println("lead id is : " + leadId);


        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);

       // createLeadObject_posInsurance.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLead_LoanTap/leadResponseSchema.json");
    }




    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "fetch doc status", priority = 2)
    public void fetchDocStatus_posInsurance()
    {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", "ins_pos_agent");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("solutionLeadId", leadId);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", session_token);




        fetchDocStatus_posInsurance fetchDocStatusObject_posInsurance = new fetchDocStatus_posInsurance();

        Response responseObject = middlewareServicesObject.fetchDocStatus_posInsurance(fetchDocStatusObject_posInsurance, queryParams, headers);

      //  leadId = responseObject.jsonPath().getString("leadId");
        //System.out.println("lead id is : " + leadId);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
        // fetchDocStatusObject_posInsurance.validateResponseAgainstJSONSchema("MerchantService/V2/doc/upload/status/fetchDocStatus/leadResponseSchema.json");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "upload doc ", priority = 3)
    public void uploadDocStatus_PassportPhoto_posInsurance()
    {
        File file1=new File("/home/<USER>/Documents/OE_API_Automation/oe-api-automation/src/main/resources/cancelCheque.pdf");

        uploadDoc_posAgent uploadDoc_posAgentObject= new uploadDoc_posAgent();
        Response responseObject = middlewareServicesObject.uploadDocStatus_posInsurance(uploadDoc_posAgentObject, "INDIVIDUAL","DIY_P4B_APP","ins_pos_agent",leadId,"passportSizePhoto","passportSizePhoto","1",file1,session_token);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);

    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "upload doc ", priority = 4)
    public void uploadDocStatus_highestQualificationPhoto_posInsurance()
    {

        File file1=new File("/home/<USER>/Documents/OE_API_Automation/oe-api-automation/src/main/resources/cancelCheque.pdf");

        uploadDoc_posAgent uploadDoc_posAgentObject= new uploadDoc_posAgent();
        Response responseObject = middlewareServicesObject.uploadDocStatus_posInsurance(uploadDoc_posAgentObject, "INDIVIDUAL","DIY_P4B_APP","ins_pos_agent",leadId,"highestQualificationCertificate","highestQualificationCertificate","1",file1,session_token);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);

    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "upload doc ", priority = 5)
    public void uploadDocStatus_aadharfrontPhoto_posInsurance()
    {

        File file1=new File("/home/<USER>/Documents/OE_API_Automation/oe-api-automation/src/main/resources/cancelCheque.pdf");

        uploadDoc_posAgent uploadDoc_posAgentObject= new uploadDoc_posAgent();
        Response responseObject = middlewareServicesObject.uploadDocStatus_posInsurance(uploadDoc_posAgentObject, "INDIVIDUAL","DIY_P4B_APP","ins_pos_agent",leadId,"aadhaarFrontPhoto","poi","1",file1,session_token);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);

    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "upload doc ", priority = 6)
    public void uploadDocStatus_aadharrbackPhoto_posInsurance()
    {

        File file1=new File("/home/<USER>/Documents/OE_API_Automation/oe-api-automation/src/main/resources/cancelCheque.pdf");

        uploadDoc_posAgent uploadDoc_posAgentObject= new uploadDoc_posAgent();
        Response responseObject = middlewareServicesObject.uploadDocStatus_posInsurance(uploadDoc_posAgentObject, "INDIVIDUAL","DIY_P4B_APP","ins_pos_agent",leadId,"aadhaarBackPhoto","poi","2",file1,session_token);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);

    }


    @Test(description = "fetch XMW token for OE panel", priority = 7)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void getOE_XMW() {
        TokenXMV tokenXMW = new TokenXMV();
        Response responseObject = middlewareServicesObject.v1Token("9560526665", "paytm@123");
        XMWToken = responseObject.getHeader("Set-Cookie").toString();
        System.out.println("XMW token is :" + XMWToken);

    }

    @Test(priority = 8,description = "Fetch Lead Details",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchLead_posAgent() throws JsonProcessingException {
        FetchLead v1FetchLeadObj = new FetchLead(leadId);
        waitForLoad(5000);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);
        highestQualificationDoc = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[1].uuid").toString();
        passportPhotoDoc = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[2].uuid").toString();
        workflowStatusId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();
        aadhaarDoc_frontPhoto = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[0].uuid").toString();
        aadhaarDoc_backPhoto = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[3].uuid").toString();

       // LOGGER.info("Aadhaar UUID from Response" + aadhaarDoc_frontPhoto);

    /*    ObjectMapper mapper = new ObjectMapper();
        aadhaardocuid = mapper.writeValueAsString(aadhaarDoc);
        LOGGER.info("Aadhaar UUID  after serialization" + aadhaardocuid);*/
        System.out.println("highestQualification uuid "+highestQualificationDoc +"passportPhoto uuid "+passportPhotoDoc +"Aaadhar front uuid "+aadhaarDoc_frontPhoto +" Aadhar back uuid "+aadhaarDoc_backPhoto);
         leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        LOGGER.info("This is Current Lead Stage : " + leadStage);

    }

    @Test(priority = 9,description = "reject lead from panel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void RejectLeadfromPanel_posAgent() throws JsonProcessingException
    {

        if (leadStage.equals("QC_ACTION_PENDING")) {
            EditLead PGProfileAction_Status = new EditLead(leadId, P.TESTDATA.get("EditLead_RejectDoc_posAgent"));
            PGProfileAction_Status.getProperties().setProperty("uuidPassportPhoto", passportPhotoDoc);
            PGProfileAction_Status.getProperties().setProperty("uuidHighestQualification", highestQualificationDoc);
            PGProfileAction_Status.getProperties().setProperty("workflowStatusId", workflowStatusId);
            PGProfileAction_Status.getProperties().setProperty("uuidAadhaar_front", aadhaarDoc_frontPhoto);
            PGProfileAction_Status.getProperties().setProperty("uuidAadhaar_back", aadhaarDoc_backPhoto);

            Response responseObject = middlewareServicesObject.v1EditLeadOE(PGProfileAction_Status, "SUBMIT", "9560526665", "1106992015", XMWToken, "application/json");

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            System.out.println("Status Code is " + StatusCode);
            String LeadStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadStage.contains("QC_REJECTED"));

        } else {
            System.out.println("lead is not in QC action pending state");
        }
    }


    @Test(priority = 10,description = "Fetch Lead Details",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchLeadStatusBeforeUploadDoc_posAgent() throws JsonProcessingException {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj = new FetchLead(leadId);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is : "+leadStage);
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "upload doc ", priority = 11)
    public void uploadDocAgainStatus_highestQualificationPhoto_posInsurance() {
        waitForLoad(5000);
        if (leadStage.equals("NOTIFICATION_SUCCESS"))
        {
            File file1 = new File("/home/<USER>/Documents/OE_API_Automation/oe-api-automation/src/main/resources/cancelCheque.pdf");

            uploadDoc_posAgent uploadDoc_posAgentObject = new uploadDoc_posAgent();
            Response responseObject = middlewareServicesObject.uploadDocStatus_posInsurance(uploadDoc_posAgentObject, "INDIVIDUAL", "DIY_P4B_APP", "ins_pos_agent", leadId, "highestQualificationCertificate", "highestQualificationCertificate", "1", file1, session_token);
            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            System.out.println("Status Code is " + StatusCode);
        }

        else
        {
            System.out.println("lead is not in QC rejected state");
        }
    }



    @Test(priority = 12,description = "Fetch Lead Details",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchLeadAgain_posAgent() throws JsonProcessingException {
        FetchLead v1FetchLeadObj = new FetchLead(leadId);
        waitForLoad(5000);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);
        highestQualificationDoc = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[0].uuid").toString();
        passportPhotoDoc = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[2].uuid").toString();
        workflowStatusId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();

        /*ObjectMapper mapper = new ObjectMapper();
        aadhaardocuid = mapper.writeValueAsString(aadhaarDoc);
        LOGGER.info("Aadhaar UUID  after serialization" + aadhaardocuid);*/

        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        LOGGER.info("This is Current Lead Stage : " + leadStage);

    }



    @Test(priority = 13,description = "Fetch Lead Details & submit on Panel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveSubmitLeadPanel_posAgent() throws JsonProcessingException
    {

        if (leadStage.equals("QC_ACTION_PENDING"))
        {
            EditLead PGProfileAction_Status = new EditLead(leadId, P.TESTDATA.get("EditLeadposAgent"));
            PGProfileAction_Status.getProperties().setProperty("uuidPassportPhoto", passportPhotoDoc);
            PGProfileAction_Status.getProperties().setProperty("uuidHighestQualification", highestQualificationDoc);
            PGProfileAction_Status.getProperties().setProperty("workflowStatusId", workflowStatusId);
            PGProfileAction_Status.getProperties().setProperty("uuidAadhaar_front", aadhaarDoc_frontPhoto);
            PGProfileAction_Status.getProperties().setProperty("uuidAadhaar_back", aadhaarDoc_backPhoto);
          //  PGProfileAction_Status.getProperties().setProperty("uuidAadhaar", aadhaardocuid);

            Response responseObject = middlewareServicesObject.v1EditLeadOE(PGProfileAction_Status, "SUBMIT", "9560526665", "1106992015", XMWToken, "application/json");

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            System.out.println("Status Code is " + StatusCode);
            String LeadStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadStage.contains("QC_ACCEPTED"));

        } else {
            System.out.println("lead is not in QC action pending state");
        }
    }


    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "failure callback from insurance", priority = 14)
    public void callbackv5Rejected_posInsurance()
    {

        waitForLoad(5000);
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", leadId);
        queryParams.put("solution", "ins_pos_agent");
    waitForLoad(20000);

        generateJwtToken();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("Postman-Token", "12b7095a-3029-4d9e-b9ba-f8c1b47bb3d3");
        headers.put("cache-control", "no-cache");
        headers.put("channel", "PAYTM_APP");
        headers.put("custId", custId);

        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "REJECTED");
        body.put("SOLUTION_TYPE_LEVEL_2", "NON_LIFE");


        CallBackv5_posAgent CallBackv5Object_posAgent = new CallBackv5_posAgent();

        Response responseObject = middlewareServicesObject.callbackv5_posInsurance(CallBackv5Object_posAgent, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
        // CallBackv5Object_posAgent.validateResponseAgainstJSONSchema("MerchantService/v5/callback_posAgent/leadResponseSchema.json");
    }



    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "callback from insurance", priority = 15)
    public void callbackv5_posInsurance()
    {

        waitForLoad(5000);
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", leadId);
        queryParams.put("solution", "ins_pos_agent");


        generateJwtToken();
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("Postman-Token", "12b7095a-3029-4d9e-b9ba-f8c1b47bb3d3");
        headers.put("cache-control", "no-cache");
        headers.put("channel", "PAYTM_APP");
        headers.put("custId", custId);

        Map<String, String> body = new HashMap<String, String>();
        body.put("status", "SUCCESS");
        body.put("SOLUTION_TYPE_LEVEL_2", "LIFE");


        CallBackv5_posAgent CallBackv5Object_posAgent = new CallBackv5_posAgent();

        Response responseObject = middlewareServicesObject.callbackv5_posInsurance(CallBackv5Object_posAgent, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
        // CallBackv5Object_posAgent.validateResponseAgainstJSONSchema("MerchantService/v5/callback_posAgent/leadResponseSchema.json");
    }


    @Test(priority = 16,description = "Fetch Lead Details",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchLeadStatusBeforeTNC_posAgent() throws JsonProcessingException {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj = new FetchLead(leadId);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is : "+leadStage);
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "fetch dynamic Tnc", priority = 17)
    public void fetchDynamicTnc_posInsurance() {
        if (leadStage.equals("NOTIFICATION_SUCCESS")) {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", session_token);
            fetchDynamicTnc fetchDynamicTncObject = new fetchDynamicTnc();
            Response responseObject = middlewareServicesObject.fetchDynamicTnc_posInsurance(fetchDynamicTncObject, queryParams, headers);

            md5 = responseObject.jsonPath().getString("md5Key");
            uniqueIdentifier = responseObject.jsonPath().getString("identifier");
            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            System.out.println("Status Code is " + StatusCode);
            // fetchDynamicTncObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/fetchDynamicTnc/leadResponseSchema.json");
        }

        else
        {
            System.out.println("lead has invalid stage!!");
        }
    }



    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "trigger otp", priority = 18)
    public void triggerOtp_posInsurance()
    {

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", session_token);

        sendOtp_posAgent sendOtp_posAgentObject = new sendOtp_posAgent();

        Response responseObject = middlewareServicesObject.sendOtp_posInsurance(sendOtp_posAgentObject, headers);

       state= responseObject.jsonPath().getString("state");
       System.out.println("State is  :"+state);
        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
        // sendOtp_posAgentObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/sendOtp/leadResponseSchema.json");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = "save dynamic tnc", priority = 19)
    public void saveDynamicTnc_posInsurance() throws IOException, JSchException {

        LOGGER.info("MD5 is : " +md5);
        LOGGER.info("State is : " + state);
        LOGGER.info("Unique ID : " +uniqueIdentifier);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("leadId", leadId);
        queryParams.put("md5", md5);
        queryParams.put("identifier", uniqueIdentifier);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", session_token);
        headers.put("Content-Type","application/json");

        ValidateOtp ValidateOTPobj = new ValidateOtp(P.TESTDATA.get("ValidateOtpPosAgent"));
       OTP = getOTP(AgentNo);

        Map<String, String> body = new HashMap<String, String>();
        body.put("state", state);
        body.put("otp", OTP);


        saveDynamicTnc_posAgent saveDynamicTncObject_posAgent = new saveDynamicTnc_posAgent();

        Response responseObject = middlewareServicesObject.saveDynamicTnc_posInsurance(saveDynamicTncObject_posAgent, queryParams, headers, body);

        int StatusCode = responseObject.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        System.out.println("Status Code is " + StatusCode);
        // saveDynamicTncObject_posAgent.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/saveDynamicTnc_posAgent/leadResponseSchema.json");
    }


    @Test(priority = 20,description = "Fetch Lead Details",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchLeadStatusbeforePanelSuccess_posAgent() throws JsonProcessingException {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj = new FetchLead(leadId);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is : "+leadStage);
        Assert.assertTrue(leadStage.contains("BUSINESS_ACTION_PENDING"));
    }

    @Test(priority = 20,description = " submit  lead on Panel",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveSubmitLeadPanelAtBusinessLending_posAgent() throws JsonProcessingException
    {
          waitForLoad(3000);
        if (leadStage.equals("BUSINESS_ACTION_PENDING"))
        {
            EditLead PGProfileAction_Status = new EditLead(leadId, P.TESTDATA.get("EditLeadposAgent"));

            Response responseObject = middlewareServicesObject.v1EditLeadOE(PGProfileAction_Status, "SUBMIT", "9560526665", "1106992015", XMWToken, "application/json");

            int StatusCode = responseObject.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            System.out.println("Status Code is " + StatusCode);
            String LeadStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadStage.contains("PANEL_SUCCESS"));

        } else {
            System.out.println("lead is not in business action pending state");
        }
    }


    @Test(priority = 21,description = "Fetch Lead Details",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void FetchLeadStatusafterPanelSuccess_posAgent() throws JsonProcessingException {
        waitForLoad(3000);
        FetchLead v1FetchLeadObj = new FetchLead(leadId);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);
        leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
        System.out.println("Lead stage is : "+leadStage);
    }

}
