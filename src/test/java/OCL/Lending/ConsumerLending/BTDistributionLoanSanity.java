package OCL.Lending.ConsumerLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class BTDistributionLoanSanity extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(BTDistributionLoanSanity.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1001557776";
		 String consumerNumber="5886709411";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String coApplicantPanValidationTimestamp="";

		 Response responseObject= null;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing stashfin lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDeatils()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("leadId",leadId);
			  queryParams.put("custId", custId);
			  queryParams.put("solution",LendingConstants.BT_DISTRIBUTION);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreateBTDistributionPiramalLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.BT_DISTRIBUTION);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  	   body.put("LENDER_ID", "9");
		  	   body.put("PRODUCT_ID", "100000157");
		  	   body.put("PRODUCT_TYPE","BT");
			   body.put("PRODUCT_VERSION", "1");
			   body.put("FLOW_TYPE", "DISTRIBUTION");
			   body.put("MERCHANT_ID", "CTdjRt55279253855316");
			   body.put("OFFER_START_DATE", "2021-09-13");
			   body.put("OFFER_END_DATE", "2021-12-14");
			   body.put("LOAN_MAX_AMOUNT", "25000");
		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
		  	   body.put("USER_INPUT_F_NAME", "Shivangi");
		  	   body.put("USER_INPUT_M_NAME", "");
			   body.put("USER_INPUT_L_NAME","Goswami");
		  	   body.put("GENDER", "FEMALE");
			   body.put("EQUIFAX_DOB", "1988-12-18");
			   body.put("EQUIFAX_PAN", "**********");
			   body.put("EQUIFAX_EMAIL", "<EMAIL>");
		  	 
			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/BTDistributionRequest.json";
		  	  
		  	  
			   
			
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"9");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"CTdjRt55279253855316");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"100000157");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"BT");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"9");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2021-09-13");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2021-12-14");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"25000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.USER_INPUT_F_NAME"),"Shivangi");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.USER_INPUT_L_NAME"),"Goswami");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EQUIFAX_DOB"),"1988-12-18");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EQUIFAX_PAN"),"**********");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EQUIFAX_EMAIL"),"<EMAIL>");
			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateBTDistributionPiramalLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		    
		
		    }
		  
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC005_UpdateLeadBasicDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","BASIC_DETAILS");
		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
		  	   body.put("CITY","Delhi");
		  	   body.put("PINCODE", "110001");
		  	
	
		       
		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/PiramalBasicDetailRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CITY"),"Delhi");
    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"110001");
      			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
				        Assert.assertEquals(responseObject.jsonPath().getString("leadAdditionalInfo.IS_PAN_DEDUPE_PASSED"),"TRUE");
				        
				        leadId=responseObject.jsonPath().getString("leadId");
				        custId=responseObject.jsonPath().getString("custId");
				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
				    
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				  
			    
		     
			  
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_UpdateLeadBasicDetails",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

		        lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
		        
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		    
		
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC007_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BT_DISTRIBUTION);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "BHAIRAVI");
			body.put("L_NAME", "LATASREE");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "600024");
			body.put("PAN", "**********");
			body.put("DOB", "1979-10-05");
			body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());
			
			}
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC007_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC008_FetchCIR()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		   
		
		 	  
		 	 for(int i=0;i<6;i++)
			  {
				  responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);
			
			 
			  if(responseObject.getStatusCode()==200)
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
		        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


		        bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
		        bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
		        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
		        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
		      
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_FetchCIR",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC009_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
              }
		   
		
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC009_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC010_UpdateExistingDetailsInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BT_DISTRIBUTION);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "TOUCH");
			body.put("L_NAME", "LIMITED");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "110096");
			body.put("PAN", Pan);
			body.put("DOB", LendingConstants.DOB_STASHFIN);
			body.put("EMAIL", Email);
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
			
			}
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC010_UpdateExistingDetailsInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC011_FetchLeadAllData() throws JSONException
		    {
			
			  
			  for(int i=0;i<5;i++)
		       {
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		    	   if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
		    		break;
		       }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
		     {
		    	
		    	   Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
		    	   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
		    	   LOGGER.info("BRE 1 initiated using callback");
		    	   BRE1Callback();
		    	   
		    	    	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");  
				    
				   
				  
			      
              }
	  
		     else
		    	 
		     {
		    	   LOGGER.info("BRE 1 passed without callback");  
		    	   Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_SUCCESS.getStage());
		    	   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"707");
		    	    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		    	   Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
		    	
		     }
		     
		        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
		        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer")); 
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);
		
		    }
		  
		  
		
			public Response BRE1Callback() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BT_DISTRIBUTION);
			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowOperation", "BRE1_SUCCESS");
			
            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/BRE1RequestCallback.json";
	        responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
	        
	        return responseObject;		 		 
				
			}
			
			  @Test(description = "Update lead basic details",dependsOnMethods = "TC011_FetchLeadAllData",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC012_UpdateAdditionalDetailsWithCoApplicantDetails()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
		    	 
				  Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","ADDITIONAL_DETAILS");
			  	   body.put("LOAN_BALANCE_TRANSFER_TYPE", "LAP");
			  	   body.put("LOAN_BALANCE_TRANSFER_BANK", "bandhan bank");
			  	   body.put("LOAN_BALANCE_TRANSFER_DATE", "12/2019");
			  	   body.put("EMPLOYMENT_TYPE","Salaried");
				   body.put("ANNUAL_INCOME","100000");
				   body.put("IS_ITR_FILED","Yes");
				   body.put("PROPERTY_TYPE","Residential");
				   body.put("PROPERTY_VALUE","700000");
				   body.put("LOAN_AMOUNT_REQUESTED","25");
				   body.put("LOAN_TENURE_REQUESTED","10");
				   body.put("PROPERTY_SUB_TYPE","Highrise Apartment");
				   body.put("MONTHLY_INCOME","100100");
				   body.put("INCOME_MODE","BANK");
				   body.put("line1","8A-410/412, DDA JANTA FLATS MAYUR KUNJ");
				   body.put("line2","MAYUR VIHAR PHASE 1");
				   body.put("line3","MAYUR VIHAR");
				   body.put("city","DELHI");
				   body.put("state","EAST DELHI");
				   body.put("pincode","110001");
				   body.put("addressType","RESIDENTIAL");
				   body.put("addressSubType","REGISTERED");
			       
			      requestBodyJsonPath="MerchantService/V1/workflow/lead/PiramalAdditionalDetailsWithCoapplicantDetailsRequest.json";
			      responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"708");
					        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_BALANCE_TRANSFER_TYPE"),"LAP");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_BALANCE_TRANSFER_BANK"),"bandhan bank");
	     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_BALANCE_TRANSFER_DATE"),"12/2019");
	    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMPLOYMENT_TYPE"),"Salaried");
	      			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ANNUAL_INCOME"),"100000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_ITR_FILED"),"Yes");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_TYPE"),"Residential");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_VALUE"),"700000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_REQUESTED"),"25");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_REQUESTED"),"10");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROPERTY_SUB_TYPE"),"Highrise Apartment");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MONTHLY_INCOME"),"100100");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INCOME_MODE"),"BANK");
					        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].addressType"),"RESIDENTIAL");
					        Assert.assertEquals(responseObject.jsonPath().getString("addresses[0].addressSubType"),"REGISTERED");
					     
					    
					    
					      }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DETAILS_SUBMITTED.getStage());
					  
				    
			     
				  
			    }
			  
			  @Test(description = "Update lead basic details",dependsOnMethods = "TC012_UpdateAdditionalDetailsWithCoApplicantDetails",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC013_UpdateCoApplicantDetails()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
		    	 
				  Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","CO_APPLICANT_DETAILS");
			  	   body.put("CO_APPLICANT_NAME", "Shivangi Goswami");
			  	   body.put("CO_APPLICANT_GENDER", "FEMALE");
			  	   body.put("CO_APPLICANT_MOBILE_NUMBER", "9540432921");
			  	   body.put("CO_APPLICANT_PAN","**********");
				   body.put("CO_APPLICANT_RELATION","Father");
				   body.put("CO_APPLICANT_DOB","1960-11-01");
				   body.put("CO_APPLICANT_EMPLOYMENT_TYPE","Salaried");
				   body.put("CO_APPLICANT_MONTHLY_INCOME","700000");
				   body.put("CO_APPLICANT_ANNUAL_INCOME","100100");
				   body.put("CO_APPLICANT_INCOME_MODE","CASH");
				   body.put("CO_APPLICANT_IS_ITR_FILED","No");
				   body.put("IS_CO_APPLICANT_PRESENT","true");
			
			       
			      requestBodyJsonPath="MerchantService/v1/workflow/Lead/Co-applicantDetailsRequest.json";
			      responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_AND_CO_APPLICANT_DETAILS_SUBMITTED.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_AND_CO_APPLICANT_DETAILS_SUBMITTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_AND_CO_APPLICANT_DETAILS_SUBMITTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"709");
					        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_PAN"),"**********");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_NAME"),"Shivangi Goswami");
	     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_EMPLOYMENT_TYPE"),"Salaried");
	    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_RELATION"),"Father");
	      			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_M_NAME"),"WOOD");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_DOB"),"1960-11-01");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_MOBILE_NUMBER"),"9540432921");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_MONTHLY_INCOME"),"700000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_CO_APPLICANT_PRESENT"),"true");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_IS_ITR_FILED"),"No");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_F_NAME"),"TOUCH");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_ANNUAL_INCOME"),"100100");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_NSDL_NAME"),"TOUCH WOOD LIMITED");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_GENDER"),"FEMALE");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_L_NAME"),"LIMITED");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_INCOME_MODE"),"CASH");
					        coApplicantPanValidationTimestamp=responseObject.jsonPath().getString("solutionAdditionalInfo.CO_APPLICANT_PAN_VALIDATION_TIMESTAMP");
					      }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_AND_CO_APPLICANT_DETAILS_SUBMITTED.getStage());
					  
				    
			     
				  
			    }
			  @Test(description = "Update lead basic details",dependsOnMethods = "TC013_UpdateCoApplicantDetails",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC014_AcceptLoanOffer()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
		    	 
				  Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","OFFER_ACCEPTED");
			       
			       responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			    
			       
			       if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
					     
			       {
					  
			    	   Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
			    	   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");
			    	   LOGGER.info("BRE 2 initiated using callback");
			    	   BRE2Callback();
			    	   
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_SUCCESS.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_SUCCESS.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_SUCCESS.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"715");

					      }
					  
			       }
			       
			       for(int i=0;i<20;i++)
			       {
			    	   responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			    	   if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPLICATION_SUCCESS.getStage()))
			    		break;
			       }
			      
			       if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPLICATION_SUCCESS.getStage()))
					     
			       {
					  
			       	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				    
				    
				    
			       }
			       
			        requestBodyJsonPath="MerchantService/v1/workflow/Lead/AcceptOffer.json";
			        responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPLICATION_SUCCESS.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");

					      }
					  
			   	  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
					  
				    
			     
				  
			    }
			  
				public Response BRE2Callback() {
					Map<String, String> queryParams = new HashMap<String, String>();
					queryParams.put("solution", LendingConstants.BT_DISTRIBUTION);
					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
					queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
					
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
					
					Map<String, Object> body = new HashMap<String, Object>();
					body.put("workflowOperation", "BRE2_SUCCESS");
					body.put("BASE_ID", "59426262-fb57-4837-a3a1-a36226b1cc65");
					body.put("LOAN_OFFER_ID", "59426262-fb57-4837-a3a1-a36226b1cc65");
					body.put("LOAN_OFFER_RATE_OF_INTEREST", "12.5");
					body.put("LOAN_OFFER_AMOUNT", "560000");
					body.put("LOAN_OFFER_TENURE_UNIT", "YEAR");
					body.put("LOAN_OFFER_TENURE", "9");
					body.put("LOAN_OFFER_TENURE_AMOUNT", "8662.0");
					
		            requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/BRE2RequestCallback.json";
			        responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
			        
			        return responseObject;		 		 
						
					}
			  
			  @Test(description = "Update lead basic details",dependsOnMethods = "TC014_AcceptLoanOffer",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC015_UpdateToAppointmentBookedFailureNode()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
		    	 
				  Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","APPOINTMENT_DETAILS");
			       body.put("APPOINTMENT_DATE","2022-03-29");
			       body.put("APPOINTMENT_START_TIME","2022-03-29 10:00:00");
			       body.put("APPOINTMENT_END_TIME","2022-03-29 00:00");
			  	  
			      requestBodyJsonPath="MerchantService/v1/workflow/Lead/AppointmentDetailsUpdateRequest.json";
			      responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"718");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPOINTMENT_DATE"),"2022-03-29");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPOINTMENT_START_TIME"),"2022-03-29 10:00:00");
	     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPOINTMENT_END_TIME"),"2022-03-29 00:00");
	    			        
					      }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage());
					  
				    
			     
				  
			    }
			  @Test(description = "Update lead basic details",dependsOnMethods = "TC015_UpdateToAppointmentBookedFailureNode",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC016_UpdateToAppointmentBookedSuccessStage()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
		    	 
				  Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","APPOINTMENT_DETAILS");
			       body.put("APPOINTMENT_DATE","2022-03-29");
			       body.put("APPOINTMENT_START_TIME","2022-03-29 10:00:00");
			       body.put("APPOINTMENT_END_TIME","2022-03-29 11:00:00");
			       
			       for(int i=0;i<15;i++)
			       {
			    	   responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			    	   if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPOINTMENT_BOOKED_FAILURE.getStage()))
			    		break;
			       }
			       
			       requestBodyJsonPath="MerchantService/v1/workflow/Lead/AppointmentDetailsUpdateRequest.json";
				      responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					
					      if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage()))
					    	  
					      { 
					    	  
					      
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"718");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPOINTMENT_DATE"),"2022-03-29");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPOINTMENT_START_TIME"),"2022-03-29 10:00:00");
	     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPOINTMENT_END_TIME"),"2022-03-29 11:00:00");
	     			       applicationId=responseObject.jsonPath().getString("solutionAdditionalInfo.APPLICATION_ID");
					      }
					  
					
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPOINTMENT_REQUESTED.getStage());
					  
				    
			     
				  
			    }
			 
			  @Test(description = "Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC016_UpdateToAppointmentBookedSuccessStage",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC018_CallbackToLoanSanctioned() throws JSONException
			    {
				  
				  Map<String,String> queryParams=new HashMap<String,String>();
					
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
				  
		    	   Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","LOAN_SANCTIONED");
			  	   body.put("APPLICATION_ID", applicationId);
			  	   body.put("LOAN_AMOUNT", "300000");
			  	   body.put("LOAN_TENURE", "36");
			  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
			  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
			  	   body.put("LOAN_RATE_OF_INTEREST", "26");
			  
			       
			      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/PiramalHomeLoanSanctionCallbackRequest.json";
			    
				 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
						 
						
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_SANCTIONED.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"236");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"300000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"36");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"26");
    
					      }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
					  
			    }   
			     
			  @Test(description = "LIS Callback to Loan Disbursed-->237 Stage",dependsOnMethods = "TC018_CallbackToLoanSanctioned",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC019_LISCallbackToLoanDisbursedFromLoanSanctioned() throws JSONException
			    {
				  
				  Map<String,String> queryParams=new HashMap<String,String>();
					
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  queryParams.put("solutionTypeLevel2", LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2);
				  
		    	  Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","LOAN_DISBURSED");
			  	   body.put("APPLICATION_ID", applicationId);
			  	   body.put("LOAN_AMOUNT", "300000");
			  	   body.put("LOAN_TENURE", "36");
			  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
			  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
			  	   body.put("LOAN_RATE_OF_INTEREST", "26");
			  	   body.put("PROCESSING_FEE_RATE", "2");
			  	   body.put("INSTALLMENT_AMOUNT","30000");
			  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
			  	   body.put("LOAN_REPAYMENT_AMOUNT", "288000");
			  	   body.put("LOAN_DISBURSAL_AMOUNT", "290000");
			  	   body.put("LENDER_LAN", "1234567890");
			       
			      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/PiramlLoanDisbursedCallbackRequest.json";
			   

					 for(int i=0;i<2;i++)
					 {
						 
						 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
						 
						  if(responseObject.getStatusCode()==200)
						   break;
					 }
						  
			
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"237");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"300000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"36");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"26");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"2");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"30000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"288000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"290000");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_LAN"),"1234567890");
					    
					      }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
					  
			    }  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC019_LISCallbackToLoanDisbursedFromLoanSanctioned",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC020_FetchLeadAllData() throws JSONException
			    {
				  
				  for(int i=0;i<5;i++)
				  {
				   
				 
				   responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage()))
					  break;
				  
				  }
				  
		         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			     
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"325");
		  
			     
			     }
		  
			    
			
			    }
			  
			  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC020_FetchLeadAllData",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC021_LMSDataCallback() throws JSONException
			    {
				  
				  Map<String,String> queryParams=new HashMap<String,String>();
					
				  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				  
		    	   Map<String,String> headers=new HashMap<String,String>();
		    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
			       headers = LendingBaseAPI.setHeadersReceivedFromFE();
			       headers.put("Authorization", token);
			       headers.put("Content-Type", "application/json");
			       headers.put("custId", custId);
			       
			       Map<String,Object> body=new HashMap<String,Object>();
			       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
			  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
			  	   body.put("LENDER_LAN", "PIRAMAL0001");
			  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
			  	   body.put("ACCOUNT_CREATED_ON", "2022-01-19 13:14:29");
			  	   body.put("LOAN_AMOUNT", 300000);
			  	   
			  	
			      for(int i=0;i<10;i++)
				  {
				   
				
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
				  break;
				  
				  }
			    
		

			     if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage()))
			     {		 
			    	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";   
			    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
					
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"240");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_LAN"),"PIRAMAL0001");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ACCOUNT_CREATED_ON"),"2022-01-19 13:14:29");
						  }
					  
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
					 
			     }
			    }  
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC021_LMSDataCallback",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC022_FetchLeadAllData() throws JSONException
			    {
				  
				  for(int i=0;i<15;i++)
				  {
				   
				
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BT_DISTRIBUTION,LendingConstants.PIRAMAL_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
				  {
					  break;
				  }
				  
				  }
				  
		         
			     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
			    
			
			    }
			  
}

