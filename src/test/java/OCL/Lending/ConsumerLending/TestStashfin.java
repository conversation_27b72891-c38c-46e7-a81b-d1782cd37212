package OCL.Lending.ConsumerLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestStashfin extends BaseMethod{
	
	private static final Logger LOGGER = LogManager.getLogger(TestStashfin.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="";
		 String consumerNumber="7771116469";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String applicationId="";
		 String requestBodyJsonPath="";	
		 String Pan="";
		 String Email="";
		 String DOB="";

		 Response responseObject= null;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
			lendingBaseClassObject.createTokenForStashfinLender();
			lendingBaseClassObject.resetLenderLead(consumerNumber);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing stashfin lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDeatils()
		    {
			  custId="1000552059";
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("leadId",leadId);
			  queryParams.put("custId", custId);
			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreateStashfinLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
			  
	     	  custId="1000552059";
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
		  	   body.put("LENDER_ID", "104");
		  	   body.put("LENDER_NAME", "Stashfin");
		  	   body.put("PRODUCT_TYPE","PL");
		  	   body.put("LOAN_TENURE_MIN", "6");
		  	   body.put("LOAN_TENURE_MAX", "36");
		  	   body.put("OFFER_END_DATE", "2021-12-31");
		  	   body.put("OFFER_START_DATE", "2021-05-26");
		  	   body.put("OFFER_ID", "Stashfin_1000552059");
		  	   body.put("WHITELISTING_SOURCE", "RISK");
		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:49:27.862+05:30");
			   body.put("BASE_ID", "Stashfin_1000552059");
			   body.put("PRODUCT_ID", "1000004");
			   body.put("PRODUCT_VERSION", "1");
			   body.put("RISK_SEGMENT", "A1");
			   body.put("FLOW_TYPE", "DISTRIBUTION");
			   body.put("OBLIGATION", "50000");
			   
			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
		  	  
		  	  
			   
			
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"104");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"Stashfin");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"6");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"36");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2021-12-31");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2021-05-26");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_ID"),"Stashfin_1000552059");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_CREATED_AT"),"2021-07-16T12:49:27.862+05:30");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"Stashfin_1000552059");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"1000004");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_SEGMENT"),"A1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OBLIGATION"),"50000");
			        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		 @Test(description = "Update lead basic details",dependsOnMethods = "TC003_CreateStashfinLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_UpdateLeadBasicDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","BASIC_DETAILS");
		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
		  	   body.put("PAN_DOB_VALIDATED","true");
		  	   body.put("GENDER", "Female");
		  	   body.put("OCCUPATION", "Salaried");
		  	   body.put("PINCODE", "400101");
		  	   body.put("MONTHLY_INCOME", "100000");
	
		       
		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"Female");
    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION"),"Salaried");
    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MONTHLY_INCOME"),"100000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
//				     
				        leadId=responseObject.jsonPath().getString("leadId");
				        custId=responseObject.jsonPath().getString("custId");
				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
				    
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				  
			    
		     
			  
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC004_UpdateLeadBasicDetails",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC005_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"234");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

		        lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
		        applicationId=responseObject.jsonPath().getString("solutionAdditionalInfo.APPLICATION_ID");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
		    
		
		    }
		  
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC005_FetchLeadAllData",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC006_LISCallbackToLoanAccepted() throws JSONException
//		    {
//			  
//			  Map<String,String> queryParams=new HashMap<String,String>();
//				
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			  
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//		       
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	
//		       
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//		   
//
//				 for(int i=0;i<2;i++)
//				 {
//					 
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//					 
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//					  
//		
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"243");
//				        
//				    
//				    
//				      }
//				  
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				  
//		    }   
//		  
		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC005_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC007_LISCallbackToLoanSanctionedFromLoanAccepted() throws JSONException
		    {
			  
			  Map<String,String> queryParams=new HashMap<String,String>();
				
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","LOAN_SANCTIONED");
		  	   body.put("APPLICATION_ID", applicationId);
		  	   body.put("LOAN_AMOUNT", "430799");
		  	   body.put("LOAN_TENURE", "24");
		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
		  	   body.put("PROCESSING_FEE_RATE", "3.0");
		  	   body.put("INSTALLMENT_AMOUNT","25438");
		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
		       
		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_SANCTIONED.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"236");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"430799");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"24");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"3.0");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"25438");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"610512");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");
				        
				        
				    
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
				  
		    }   
		     
		  @Test(description = "LIS Callback to Loan Disbursed-->237 Stage",dependsOnMethods = "TC007_LISCallbackToLoanSanctionedFromLoanAccepted",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC008_LISCallbackToLoanDisbursedFromLoanSanctioned() throws JSONException
		    {
			  
			  Map<String,String> queryParams=new HashMap<String,String>();
				
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","LOAN_DISBURSED");
		  	   body.put("APPLICATION_ID", applicationId);
		  	   body.put("LOAN_AMOUNT", "430799");
		  	   body.put("LOAN_TENURE", "24");
		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
		  	   body.put("PROCESSING_FEE_RATE", "3.0");
		  	   body.put("INSTALLMENT_AMOUNT","25438");
		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
		       
		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanDisbursedCallbackRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"237");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"430799");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"24");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"3.0");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"25438");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"610512");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");
				    
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
				  
		    }  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_LISCallbackToLoanDisbursedFromLoanSanctioned",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC009_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_ACCOUNT_ACKNOWLEDGED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"239");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"430799");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"24");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"3.0");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"25438");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"610512");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");

		     }
	  
		    // responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId); 
		     //Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
		    
		
		    }
		  
		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC009_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC010_LMSDataCallback() throws JSONException
		    {
			  
			  Map<String,String> queryParams=new HashMap<String,String>();
				
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
		  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
		  	   body.put("LOAN_AMOUNT", 430799);
		  	   
		  	  
		       
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
		      
		      for(int i=0;i<10;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
				  break;
			  
			  }

				 
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
					  {
					  
					  for(int i=0;i<2;i++)
						 {
							 
							 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
							 
							  if(responseObject.getStatusCode()==200)
							   break;
						 }
					  
					  
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"240");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");

				    
				    
				      }
				  
			     //   Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
				  
		    }  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC010_LMSDataCallback",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC011_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
			  {
				  break;
			  }
			  
			  }
			  
	         
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
		    
		
		    }
		  
		  

}
