package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.kohsuke.rngom.parse.host.Base;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PLHERO_Compliance_CPV_Address_Consent_Accepted extends BaseMethod {
        private static final Logger LOGGER = LogManager.getLogger(OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.PLHERO_Compliance_CPV_Address_Consent_Accepted.class);
        String solution="personal_loan_v3";
        String solutionTypeLevel2="HERO";
        String entityType="INDIVIDUAL";
        String channel="PAYTM_APP";
        String leadId = "";
        String sessionToken = "";
        String consumerNumber="9921470990";
        String consumerPassword="paytm@123";
        String custId="1701676820";
        String token="";
        String requestBodyJsonPath="";
        String userIPAddress="";
        String staticTncAcceptanceTimeStamp="";
        String Email="<EMAIL>";
        String DOB="1973-01-01";
        String PAN="**********";
        String occupation="";
        String income="";
        String firstName="BHAIRAVI";
        String lastName="LATASREE";
        String bureauRequest="";
        String bureauResponse="";
        String bureauCreditState="";
        String breCreditScore="";
        String stringify_json="{\\\"baseId\\\":\\\"pl_hero_1701676820_24b7e152\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"creditScore\\\":808,\\\"eMandateType\\\":\\\"MANDATORY\\\",\\\"field_investigation_needed\\\":false,\\\"lenderSchemeId\\\":90,\\\"lenderRiskCode\\\":\\\"CL_PL\\\",\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":200000.0,\\\"minLoanAmount\\\":10000.0,\\\"newOfferGenerated\\\":true,\\\"ntc\\\":0,\\\"offerEndDate\\\":\\\"Wed Jun 29 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"747ebf1e-4e79-460e-a7ec-8caf19af0f2b\\\",\\\"offerStartDate\\\":\\\"Thu Mar 31 00:00:00 IST 2022\\\",\\\"offerValidity\\\":90,\\\"paytmThick\\\":0,\\\"productId\\\":\\\"191\\\",\\\"productVersion\\\":1,\\\"riskGrade\\\":\\\"L\\\",\\\"riskSegment\\\":\\\"L\\\",\\\"sourceOfWhitelist\\\":\\\"RISK\\\",\\\"isBre2Required\\\":false,\\\"skipMandate\\\":false,\\\"loanDownGradable\\\":false}";
        String loanTenure="";
        String loanAmount="";
        String loanEquatedMonthlyInstallment="";
        String loanRateofInterest="";
        String loanInterestAmount="";
        String loanProcessingFeeRate="";
        String loanDisbursalAmount="";
        String stampDutyCharges="";
        String brokerPeriodInterest="";
        String uuidCustomerPhoto = "";
        String uuidSelfie="";
        String loanOfferID="669f6d19-ab4a-4fc1-9857-42e9c28e8295";
        String baseID="pl_hero_1701676820_24b7e152";
        String ckycName="";
        String code = "";
        String tncName = "";
        String url = "";
        String uniqueIdentifier ="";
        String md5 ="";
        String codeSanctionLetter = "";
        String tncNameSanctionLetter = "";
        String urlSanctionLetter = "";
        String uniqueIdentifierSanctionLetter = "";
        String md5SanctionLetter = "";
        String bankName="PAYTM BANK";
        String bankAccountNumber="************";
        String ifsc="PYTM0123456";
        String bankAccountHolderName="Bene Customer Name";
        private String randomBankAccountNumber;
        boolean basic_prefilling_flag =true;


        Response responseObject= null;
        LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
        Utilities utility=new Utilities();


        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }



        @Test(description="Verify if there is any existing Personal Loan Migration HERO Lead",groups= {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC001_PLv3HERO_fetchlLead()
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

            }

        }


        @Test(description="Verify reseting existing Personal Loan Migration HERO lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3HERO_fetchlLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC002_PLv3HERO_DeleteExistingLead() {

            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams.put("leadId",leadId);
            queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
            queryParams.put("custId", custId);

            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);

            lendingBaseClassObject.resetLendingLeads(queryParams, headers);

        }

        @Test(description="Create Lead for Personal Loan Migeration HERO",groups={"Regression"},dependsOnMethods = {"TC002_PLv3HERO_DeleteExistingLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC003_PLv3HERO_CreateLead() {

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map <String,String> queryParams= new HashMap<String,String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType",entityType);
            queryParams.put("channel", channel);

            //Headers
            Map <String,String> header = new HashMap<String,String>();
            header = LendingBaseAPI.setHeadersReceivedFromFE();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);
            header.put("ipAddress", LendingConstants.IP_ADDRESS);

            //Body
            Map <String,Object> body = new HashMap<String,Object>();
            body.put("workflowOperation", "CREATE_LEAD");
            body.put("mobile", consumerNumber);

            //solutionAdditionalInfo
            body.put("PRODUCT_ID", "191");
            body.put("PRODUCT_TYPE", "PL");
            body.put("FLOW_TYPE", "RISK");
            body.put("LOAN_OFFER_ID", loanOfferID);
            body.put("PRODUCT_VERSION", "1");
            body.put("BASE_ID", baseID);
            body.put("LENDER_ID", "5");
            body.put("WHITELISTING_SOURCE", "RISK");
            body.put("IS_EMANDATE_ELIGIBLE", "true");
            body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
            body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
            body.put("IS_FATHER_NAME_REQUIRED", "true");
            body.put("MARITAL_STATUS", "NOT_KNOWN");
            body.put("IS_BRE3_REQUIRED", "true");
            body.put("PINCODE", "600024");
            body.put("BUREAU_PRIORITY","EXPERIAN");



            //leadAdditionalInfo
            body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "TRUE");
            body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
            if(basic_prefilling_flag) {
                body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "TRUE");
            }else{
                body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "FALSE");
            }
            requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLMigerationHEROLeadRequest.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
            if (responseObject.getStatusCode() == 201) {
                LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                leadId = responseObject.jsonPath().getString("leadId");
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"191");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
            }

        }

        @Test(description="Verify the PL v3 HERO lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3HERO_CreateLead")
        @Owner(emailId = "<EMAIL>")
        public void TC004_PLV3HERO_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<15;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage()))
                    break;

            }

            if(basic_prefilling_flag) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage())) {
                    LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

                    LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "286");
                }
            }else{
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());

            }


        }


        @Test(description="Add Basic & Occupation details",groups = {"Regression"},dependsOnMethods = "TC004_PLV3HERO_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC005_BD_OD_Details(){
            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map <String,String> queryParams= new HashMap<String,String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType",entityType);
            queryParams.put("channel", channel);
            queryParams.put("leadId",leadId);

            //Headers
            Map <String,String> header = new HashMap<String,String>();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);

            //Body
            Map <String,Object> body = new HashMap<String,Object>();
            body.put("workflowOperation","OCCUPATION_DETAILS");
            body.put("OCCUPATION","Salaried");
            body.put("ANNUAL_INCOME","200000");
            body.put("EMPLOYER_ID","E66767");
            body.put("EMPLOYER_NAME","Paytm");
            body.put("BUSINESS_NAME","Shivangi QR service");
            body.put("DOB",DOB);
            body.put("PAN",PAN);
            body.put("EMAIL",Email);
            body.put("PINCODE","600024");
            body.put("LOAN_PURPOSE","Personal use");
            body.put("LOAN_PURPOSE_KEY","2");
            body.put("GENDER","Female");
            body.put("STATIC_TNC_SETNAME","personalloan_oclconsent_hero");
            body.put("LENDER_STATIC_TNC_SETNAME","pl_hero_ckyc_consent");
            body.put("F_NAME", "TOUCH");
            body.put("M_NAME", "WOOD");
            body.put("L_NAME", "LIMITED");
            body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
            body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateBDODDetails.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
            if(!(responseObject.getStatusCode()==200)){
                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );

            }
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");


        }

        @Test(description="Add Basic & Occupation details",groups = {"Regression"},dependsOnMethods = "TC004_PLV3HERO_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC006_FetchDetailsAfterBD_OD_Details(){
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),PAN);

        }


        @Test(description = "Verify Fetch CIR for PLv3 lead",groups = {"Regression"},dependsOnMethods = "TC006_FetchDetailsAfterBD_OD_Details")
        @Owner(emailId = "<EMAIL>")
        public void TC011_PLv3HERO_FetchCIR() {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF
                    , custId,LendingConstants.LENDING_BFF_SECRET);
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("longitude",LendingConstants.LONGITUDE);
            headers.put("latitude",LendingConstants.LATITUDE);
            headers.put("custId", custId);


            Map<String,Object> body=new HashMap<String,Object>();
            String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
            responseObject= lendingBaseClassObject.v3FetchCIR(queryParams, headers,body,requestjsonpath);


            if(responseObject.getStatusCode()==200) {

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
                //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
                Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");

                bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
                bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
                bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
                breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
            }
        }

        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC011_PLv3HERO_FetchCIR")
        @Owner(emailId = "<EMAIL>")
        public void TC012_PLv3HERO_BRE1Callback() throws JSONException
        {

            for(int i =0;i<=15;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                for(int i =0;i<15;i++) {
                    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
                        LOGGER.info("BRE 1 passed without callback");
                        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                        loanOfferID=responseObject.jsonPath().getString("LOAN_OFFER_ID");
                        break;
                    }
                }
                requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PLv3HEROBRE1CallbackRequest.json";
                BRE1Callback(requestBodyJsonPath);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
                    LOGGER.info("BRE 1 passed with callback");
                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                }
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            }
        }


        @Test(description = "Verify the PLv3 lead data after BRE1 Success",groups = {"Regression"},dependsOnMethods = "TC012_PLv3HERO_BRE1Callback")
        @Owner(emailId = "<EMAIL>")
        public void TC013_PLv3HERO_FetchDataPostBRE1Success() throws JSONException
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"),bureauRequest);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"),bureauResponse);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"),bureauCreditState);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"),breCreditScore);

            }
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
        }

        public Response BRE1Callback(String requestBodyJsonPath) {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", solution);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "BRE1_SUCCESS");
            body.put("BRE1_OFFER_DETAILS", ""+stringify_json+"");
            body.put("LENDING_SCHEME_ID", "90");

            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

            return responseObject;

        }

        @Test(description = "Verify PL v3 HERO lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC013_PLv3HERO_FetchDataPostBRE1Success")
        @Owner(emailId = "<EMAIL>")
        public void TC014_PLv3HERO_LoanOfferAccept() {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation", "OFFER_ACCEPTED");

            //solutionAdditionalInfo
            body.put("LOAN_TENURE", "12");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "70000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Seventy Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "6824");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Six Thousand Eight Hundred twenty four");
            body.put("LOAN_RATE_OF_INTEREST", "30");
            body.put("LOAN_INTEREST_AMOUNT", "990.70");
            body.put("LOAN_PROCESSING_FEE", "3675");
            body.put("PROCESSING_FEE_RATE", "5.25");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "4336");
            body.put("LOAN_DISBURSAL_AMOUNT", "65348");
            body.put("STAMP_DUTY_CHARGES", "200");
            body.put("BROKEN_PERIOD_INTEREST", "116");
            body.put("LENDING_DYNAMIC_SECONDARY_TNC","sanction_letter_pl_hero");
            body.put("LENDING_DYNAMIC_TNC","loan_agreement_pl_hero");
            body.put("LENDER_NAME","HERO");
            body.put("LOAN_PURPOSE_STATIC_TNC_SETNAME","purpose_of_loan_static_tnc_setname");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3HEROLoanOfferAccepted.json";

            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
                loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
                loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
                loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
                loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
                loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
                loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
                stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
                brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
            }
        }




        @Test(description = "Verify the PLv3 lead data after Loan offer accept",groups = {"Regression"},dependsOnMethods = "TC014_PLv3HERO_LoanOfferAccept")
        @Owner(emailId = "<EMAIL>")
        public void TC015_PLv3HERO_FetchDataPostLoanOfferAccept() throws JSONException
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                    /*Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);

                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest);
    */            }
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
        }


        @Test(description = "Verify the HERO lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC015_PLv3HERO_FetchDataPostLoanOfferAccept")
        @Owner(emailId = "<EMAIL>")
        public void TC018_PLv3HERO_UploadSelfie() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken,"selfie","RohanOfflineAadhaar.jpg");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
        }

        @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC018_PLv3HERO_UploadSelfie", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC019_PLv3HERO_VerifyUploadedSelfie() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


            }

        }


        @Test(description = "Update data in SAI Table", dependsOnMethods = "TC019_PLv3HERO_VerifyUploadedSelfie", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC020_LeadDataUpdateForKYC_InSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("leadId", leadId);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired","false");
            body.put("PAN",LendingConstants.OA_KYC_PAN);
            body.put("NSDL_NAME",LendingConstants.OA_NSDL_NAME);
            body.put("DOB",LendingConstants.OA_DOB);
            body.put("GENDER","MALE");

            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCMockData.json";

            responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());

        }





        @Test(description="Initiate KYC using OA",groups = {"Regression"},dependsOnMethods = "TC020_LeadDataUpdateForKYC_InSAI")
        @Owner(emailId = "<EMAIL>")
        public void TC021_InitiateKYC_UsingOfflineAAdhaar() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("shareCode","1234");


            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Callback KYC_ADDRESS_CONSENT_OPTIONAL",groups={"Regression"},dependsOnMethods = "TC021_InitiateKYC_UsingOfflineAAdhaar")
        @Owner(emailId = "<EMAIL>")
        public void TC_022_KYC_ADDRESS_CONSENT_OPTIONAL_Callback() throws JSONException{

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,
                    LendingConstants.JWT_RISK, custId, LendingConstants.JWT_RISK_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "KYC_ADDRESS_CONSENT_OPTIONAL");
            body.put("IS_CPV_ENABLED","true");
            requestBodyJsonPath = "MerchantService/V1/workflow/lead/callback/PL_KYC_ADDRESS_CONSENT_OPTIONAL.json";
            responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath, "BRE2");



        }

        @Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC_022_KYC_ADDRESS_CONSENT_OPTIONAL_Callback")
        @Owner(emailId = "<EMAIL>")
        public void TC023_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=45;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_ADDRESS_CONSENT_OPTIONAL.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_ADDRESS_CONSENT_OPTIONAL.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_ADDRESS_CONSENT_OPTIONAL.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1021");
                Assert.assertEquals(responseObject.jsonPath().getString("IS_CPV_ENABLED"),"true");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            // Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.PAN_VERIFIED.getStage());


        }

        @Test(description = "Verify updating current Address details", dependsOnMethods = "TC023_FetchDataPostKYCIntiated",groups={"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC024_CURRENT_ADDRESS_UPDATED() throws JSONException {
            Map<String, String> queryParams = new HashMap<String, String>();

            queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID,
                    custId, LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "CURRENT_ADDRESS_UPDATED");
            body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "FALSE");
            body.put("pincode", "110999");
            body.put("state", "Delhi");
            body.put("city", "South Delhi");
            body.put("line1", "House number 1111");
            body.put("line2", "KJO Villa");
            body.put("line3", "Gautam Nagar");
            body.put("addressType", "RESIDENTIAL");
            body.put("addressSubType", "CURRENT");
            requestBodyJsonPath = "MerchantService/V1/workflow/lead/PL_CPV/PL_KYC_CPV_CURRENT_ADDRESS_UPDATED.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.CURRENT_ADDRESS_UPDATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1022");
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.CURRENT_ADDRESS_UPDATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("addresses[1].addressSubType"),"CURRENT");
            Assert.assertEquals(responseObject.jsonPath().getString("addresses[1].pincode"),"110049");

        }









}
