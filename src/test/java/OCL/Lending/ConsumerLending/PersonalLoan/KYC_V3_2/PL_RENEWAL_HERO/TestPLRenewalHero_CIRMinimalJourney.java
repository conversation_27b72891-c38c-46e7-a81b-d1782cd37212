package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestPLRenewalHero_CIRMinimalJourney extends BaseMethod{

    private static final Logger LOGGER = LogManager.getLogger(OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.PL_RENEWAL_HERO.TestPLRenewalHero_CIRMinimalJourney.class);
        oAuthServices oAuthServicesObject = new oAuthServices();
        MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        Utilities UtilitiesObject = new Utilities();
        LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
        Utilities utility=new Utilities();

        String sessionToken = "";
        String leadId="";
        String custId="1002012627";
        String consumerNumber="8668010330";
        String consumerPassword="paytm@123";
        String token="";
        String stage="";
        String feStage="";
        String userIPAddress="";
        String loanUserLatitude="";
        String loanUserLongitude="";
        String tncAdditionalParam="";
        String staticTncAcceptanceTimeStamp="";
        String lenderCustomerId="";
        String requestBodyJsonPath="";
        String Pan="**********";
        String Email="";
        String DOB="";
        String applicationId="";
        String bureauRequest="";
        String bureauResponse="";
        String bureauCreditState="";
        String breCreditScore="";
        String PanValidationTimeStamp="";
        String uuid="";
        String md5="";
        String code="";
        String tncName="";
        String url="";
        String uniqueIdentifier="";
        String loanTenure="";
        String loanAmount="";
        String loanEquatedMonthlyInstallment="";
        String loanRateofInterest="";
        String loanInterestAmount="";
        String loanProcessingFeeRate="";
        String loanDisbursalAmount="";
        String stampDutyCharges="";
        String brokerPeriodInterest="";

        String codeSanctionLetter="";
        String tncNameSanctionLetter="";
        String urlSanctionLetter="";
        String uniqueIdentifierSanctionLetter="";
        String md5SanctionLetter="";
        String sanctionLetterAcceptanceTimestamp="";
        String kybSecondaryTNCDisplayURL="";
        String loanAgreementDate="";
        String kybTNCDisplayURL="";
        String panNameMatchTimeStamp="";
        String panNameMatchPercentage="";
        String breLastFetchDate="";
        String loanOfferID="1e059505-410d-4000-b3bb-52fac75289c6";
        String baseID="987ed74c-dbee-4e80-9313-964d66470283";
        String uuidCustomerPhoto;
        String uuidSelfie;
        String ckycName;
        String bankAccountNumber="************";
        String ifsc="PYTM0123456";
        String bankAccountHolderName="Shivangi Goswami";


        Response responseObject= null;



        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }



        @Test(description = "Verify whether there is any existing Renewal lead present or not",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC001_FetchLeadDeatils()
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {


                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


            }

        }


        @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC002_DeleteExistingLead()
        {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams.put("leadId",leadId);
            queryParams.put("solution",LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("custId", custId);

            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);


            lendingBaseClassObject.resetLendingLeads(queryParams, headers);
        }




        @Test(description = "Create PL Renewal Hero Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC003_CreatePLRenewalLead()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams.put("solution",LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custid", custId);
            headers.put("ipAddress", "************");

            Map<String,Object> body = new HashMap<String, Object>();
            body.put("workflowOperation","CREATE_LEAD");
            body.put("mobile", consumerNumber);
            body.put("PRODUCT_ID", "95");
            body.put("PRODUCT_TYPE", "PL");
            body.put("FLOW_TYPE", "RISK");
            body.put("PRODUCT_VERSION", "1");
            body.put("LENDER_ID", "5");
            body.put("LENDER_NAME", "HERO");
            body.put("LOAN_OFFER_ID",loanOfferID);
            body.put("BASE_ID", baseID);
            body.put("WHITELISTING_SOURCE", "RISK");
            body.put("IS_EMANDATE_ELIGIBLE", "true");
            body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
            body.put("IS_FATHER_NAME_REQUIRED", "true");
            body.put("MARITAL_STATUS", "NOT_KNOWN");
            body.put("IS_BRE3_REQUIRED", "true");
            body.put("MIGRATION_TYPE", "PL_RENEWAL");
            body.put("PINCODE", "600024");
            body.put("PARENT_LOAN_ACCOUNT_NUMBER", "PYTMPH255109743");
            body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
            body.put("STATIC_TNC_SETNAME",LendingConstants.PL_V3_STATIC_CONSENT);
            body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "FALSE");
            //Offer Details & CIR Journey Flag will be passed in Request Body
            body.put("LOAN_TENURE", "6");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "24000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Twenty four Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "4418");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Four thousand four hundred eighteen");
            body.put("LOAN_RATE_OF_INTEREST", "35");
            body.put("LOAN_INTEREST_AMOUNT", "3242");
            body.put("LOAN_PROCESSING_FEE", "1200");
            body.put("PROCESSING_FEE_RATE", "5");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1416");
            body.put("LOAN_DISBURSAL_AMOUNT", "22584");
            body.put("STAMP_DUTY_CHARGES", "0");
            body.put("BROKEN_PERIOD_INTEREST", "0");
            body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");
            body.put("IS_CIR_MINIMAL_CLICK_JOURNEY","TRUE");
            body.put("LOAN_PRICING_GRID_VERSION","1684216798");
            body.put("LOAN_MIN_AMOUNT","24000");
            body.put("LOAN_MAX_AMOUNT","100000");
            body.put("RISK_GRADE","VL");
            body.put("IS_RTO_FLOW","TRUE");



            requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLABFLTopupCIRMinimalJourney.json";



            for(int i=0;i<2;i++)
            {

                responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

                if(responseObject.getStatusCode()==201)
                    break;
            }


            if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"95");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"HERO");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),loanOfferID);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),baseID);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PAYTM_VINTAGE_OLDER_THAN_90D"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MIGRATION_TYPE"),"PL_RENEWAL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_LOAN_ACCOUNT_NUMBER"),"PYTMPH255109743");
                //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"loan_agreement_pl_hero");
                //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"sanction_letter_pl_hero");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"personalloan_oclconsent_hero");

                leadId=responseObject.jsonPath().getString("leadId");
                custId=responseObject.jsonPath().getString("custId");
                userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
                staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");



            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());

        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreatePLRenewalLead",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC004_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<15;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL
                        ,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
                    break;

            }

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"804");
            }


            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());


        }

        @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC004_FetchLeadAllData", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC005_UpdateBureauDataSetInSAI() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID,
                    custId,LendingConstants.LMS_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired","false");
            body.put("status","SUCCESS");
            body.put("PAN", "**********");
            body.put("EMAIL","<EMAIL>");
            body.put("DOB","1979-10-05");
            body.put("BUREAU_PRIORITY","EXPERIAN");


            requestBodyJsonPath="MerchantService/v2/lending/dataUpdate/UpdateSAIforHeroDistribution.json";
            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "804");


        }

        @Test(description = "Fetch CIR",dependsOnMethods = "TC005_UpdateBureauDataSetInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC006_FetchCIR()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            headers.put("session_token", sessionToken);
            headers.put("Content-Type", "application/json");

            Map<String,Object> body=new HashMap<String,Object>();

            for(int i=0;i<10;i++)
            {

                requestBodyJsonPath="MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
                responseObject= lendingBaseClassObject.v3FetchCIR(queryParams, headers,body,requestBodyJsonPath);


                if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))

                    break;

            }


            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


            bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
            bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
            bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
            breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");

        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC006_FetchCIR",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC007_FetchLeadAllData() throws JSONException
        {
            for(int i=0;i<39;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_GENERATED.getStage()))
                    break;

            }
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))

            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"Female");

                //Hit BRE1 Callback

                LOGGER.info("Callback not coming so hitting BRE1 Callback");

                responseObject=    lendingBaseClassObject. BRE1CallbackforRenewal (leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,custId);


            }


            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
        }


        @Test(description = "Update KYC mock Data",groups = {"Regression"},dependsOnMethods = "TC007_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC08_PLV3_UpdateSearchByPANMockData(){
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            //queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowMovementRequired","false");
            body.put("status","SUCCESS");
            body.put("PAN","**********");
            body.put("NSDL_NAME","SHAILESH SINGH RAWAT");
            body.put("DOB","1987-07-13");
            body.put("GENDER","MALE");
            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCMockData.json";
            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        }

        @Test(description = "Verify PL Renewal lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC08_PLV3_UpdateSearchByPANMockData")
        @Owner(emailId = "<EMAIL>")
        public void TC009_PLRenewal_LoanOfferAccept() {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID,
                    custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation", "OFFER_ACCEPTED");

            //solutionAdditionalInfo
            body.put("LOAN_TENURE", "6");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "25000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Twenty Five Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "4513");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Four thousand Five hundred thirteen");
            body.put("LOAN_RATE_OF_INTEREST", "28");
            body.put("LOAN_INTEREST_AMOUNT", "346.40");
            body.put("LOAN_PROCESSING_FEE", "1250");
            body.put("PROCESSING_FEE_RATE", "5.0");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1475");
            body.put("LOAN_DISBURSAL_AMOUNT", "23267");
            body.put("STAMP_DUTY_CHARGES", "200");
            body.put("BROKEN_PERIOD_INTEREST", "58");
            body.put("LENDER_STATIC_TNC_SETNAME", LendingConstants.PERSONAL_LOAN_RENEWAL_KYC_CONSENT);

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLV3HeroRenewalRequest.json";

            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
                loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
                loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
                loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
                loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
                loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
                loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
                stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
                brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
            }
        }


        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC009_PLRenewal_LoanOfferAccept",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC010_FetchLeadAllData() throws JSONException, InterruptedException
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))

            {

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);

                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest);

            }

        }



        @Test(description = "Verify the PLv3 lead Upload Selfie photo",groups = {"Regression"},dependsOnMethods = "TC010_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC011_PLv3HERO_UploadSelfiePhoto() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,
                    sessionToken,"selfie","SEARCH_BY_PAN.jpeg");


            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
            uuidSelfie = responseObject.jsonPath().getString("documents.docUUId");

        }

        @Test(description = "Verify the  details of Uploaded Selfie Photo",groups = {"Regression"}, dependsOnMethods = "TC011_PLv3HERO_UploadSelfiePhoto")
        @Owner(emailId = "<EMAIL>")
        public void TC012_PLv3HERO_VerifyUploadedCSelfiePhoto() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));

            Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),uuidSelfie );

        }

        @Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC012_PLv3HERO_VerifyUploadedCSelfiePhoto")
        @Owner(emailId = "<EMAIL>")
        public void TC013_InitiateKYC_UsingSearchByPan() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL
                    ,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("kycMode","SEARCH_BY_PAN");


            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }



        @Test(description = "Verify lead details after kyc initiate",groups={"Regression"},dependsOnMethods = "TC013_InitiateKYC_UsingSearchByPan")
        @Owner(emailId = "<EMAIL>")
        public void TC014_FetchDataPostKYCIntiated() throws JSONException
        {
            for(int i =0;i<=45;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.KYC_COMPLETED.getStage())) {
                    break;
                } else if (responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())){
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_COMPLETED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_COMPLETED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"375");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }


        }

        @Test(description = "Verify lead details after KYC",groups={"Regression"},dependsOnMethods = "TC014_FetchDataPostKYCIntiated")
        @Owner(emailId = "<EMAIL>")
        public void TC015_FetchDataKYCInitiate() throws JSONException
        {

            for(int i =0;i<=25;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOCATION_REQUIRED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOCATION_REQUIRED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOCATION_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"841");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOCATION_REQUIRED.getStage());


        }



        @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC015_FetchDataKYCInitiate",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC016_PLv3ABFL_LocationCaptured() {
            for(int i =0;i<=55;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PERSONAL_LOAN_RENEWAL,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.LOCATION_REQUIRED.getStage())) {
                    break;
                }
            }
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation","LOCATION_CAPTURED");
            requestBodyJsonPath="MerchantService/V1/workflow/lead/LENDER_KYC_DOC_SYNC_SUCCESS_Callback.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        }


        @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC016_PLv3ABFL_LocationCaptured",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC017_PLv3HERO_SecondBRECallback() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
            {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("leadId", leadId);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,LendingConstants.JWT_RISK_KEY);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "BRE2_SUCCESS");
                body.put("BASE_ID", baseID);
                body.put("LOAN_OFFER_ID", loanOfferID);
                body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
                body.put("SKIP_EMANDATE_ELIGIBLE", "false");
                body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

                requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
                responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body,requestBodyJsonPath,"BRE2");
                LOGGER.info("BRE 2 Success with callback");

            } else {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
                LOGGER.info("BRE 2 Success without callback");
            }

        }

        @Test(description = "Verify PL v3 Lead After BRE2 success Callback", dependsOnMethods = "TC017_PLv3HERO_SecondBRECallback", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC018_PLv3HERO_FetchDataAfterBRE2Success() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_COMPLETE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"388");

        }


        @Test(description = "Verify PL v3 Lead If Additional Data is required", dependsOnMethods = "TC018_PLv3HERO_FetchDataAfterBRE2Success",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC019_PLv3HERO_AdditionalIsRequiredorNot() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("leadId", leadId);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "IS_ADDITIONAL_DATA_REQUIRED");
            body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataRequired.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        }

        @Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC019_PLv3HERO_AdditionalIsRequiredorNot",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC020_PLv3HERO_FetchLeadVerifyAdditionalData() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
            }
        }


        @Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "TC020_PLv3HERO_FetchLeadVerifyAdditionalData",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC021_PLv3HERO_AdditionalDataCapture() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
                queryParams.put("leadId", leadId);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                        custId,LendingConstants.LENDING_BFF_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
                body.put("BUSINESS_EMAIL", "<EMAIL>");
                body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
                body.put("FATHER_NAME", "RAM");
                requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";

                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            }

        }


        @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC021_PLv3HERO_AdditionalDataCapture",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC022_PLv3HERO_BRE3Success() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            for(int i =0;i<10;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                    break;
                }
            }
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

        }


        @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC022_PLv3HERO_BRE3Success", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC023_PLv3HERO_UpdateKYCNameInSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS,
                    custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired","false");
            body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
            body.put("PAN", "**********");

            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCNameForBankRequest.json";
            responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BRE3_SUCCESS.getStage());
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            ckycName=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME");
        }


        @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC023_PLv3HERO_UpdateKYCNameInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC024_PLv3HERO_FetchLeadUpdateCKYCinSAI() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);

        }


        @Test(description = "Verify PL v3 Lead the bank details", dependsOnMethods = "TC024_PLv3HERO_FetchLeadUpdateCKYCinSAI", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC025_PLv3HERO_SaveBankDetails() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);
            headers.put("Content-Type", "application/json");

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("bankName", bankName);
            body.put("bankAccountNumber", bankAccountNumber);
            body.put("ifsc", ifsc);
            body.put("bankAccountHolderName", bankAccountHolderName);
            body.put("EMANDATE_TYPE", "Internet Banking");

            responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

            if (responseObject.getStatusCode() == 200) {

                Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
            }

            else {

                for (int i = 1; i < 4; i++) {
                    LOGGER.info("Again hitting with same data: retry-count: " + i);
                    responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);

                    if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
                    {
                        break;
                    }

                }

            }

            responseObject = lendingBaseClassObject.fetchExistingLeadData(LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE ,LendingConstants.PAYTM_APP_CHANNEL ,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);

        }


        @Test(description = "For PL v3 Bank Verification", dependsOnMethods = "TC025_PLv3HERO_SaveBankDetails",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC026_PLv3HERO_FetchLeadPostBankVerification() {
            for(int i=0;i<5;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
                    break;
                }
            }
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

        }



        @Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC026_PLv3HERO_FetchLeadPostBankVerification", groups = {
                "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void TC027_PLv3HERO_EmandateCallback() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS,
                    custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("status", "EMANDATE_SUCCESS");

            Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

        }

        @Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "TC027_PLv3HERO_EmandateCallback",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC028_PLv3HERO_FetchLeadPostEmandate() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
        }


        @Test(description = "For PL v3 Hero Generate Loan Agreement", dependsOnMethods = "TC028_PLv3HERO_FetchLeadPostEmandate", groups = { "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void TC029_PLv3HERO_GenerateLoanAgreement() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);

            Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());
            if (responseObject.jsonPath().getString("meta.status").equals("success")) {

                code = responseObject.jsonPath().getString("data.state.code");
                tncName = responseObject.jsonPath().getString("data.state.tncName");
                url = responseObject.jsonPath().getString("data.state.url");
                uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
                md5 = responseObject.jsonPath().getString("data.state.md5");
            }

        }


        @Test(description = "For PL v3 Hero Generate Sanction Letter", dependsOnMethods = "TC029_PLv3HERO_GenerateLoanAgreement", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC030_PLv3HERO_GenerateSanctionLetter() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            queryParams.put("tncType", "LOAN_SANCTION_TNC");

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);

            Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());
            if (responseObject.jsonPath().getString("meta.status").equals("success")) {

                codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
                tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
                urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
                uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
                md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
            }

        }

        @Test(description = "For PL v3 Hero Verify submit application", dependsOnMethods = "TC030_PLv3HERO_GenerateSanctionLetter", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC031_PLv3HERO_SubmitApplication() throws InterruptedException {

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("session_token", sessionToken);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
            body.put("LENDING_DYNAMIC_TNC", tncName);
            body.put("TNC_ACCEPTED_VERSION", 1);
            body.put("KYB_TNC_REF_NO", uniqueIdentifier);
            body.put("TNC_ACCEPTED_CODE", md5);


            body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
            body.put("SECONDARY_ACCEPTED_TNC_VERSION", 1);

            body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
            body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3LoanAgreementAcceptrequest.json";
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());

            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));

        }

        @Test(description = "For PL v3 Hero Verify Lead stage Post Submit Loan Application", dependsOnMethods = "TC031_PLv3HERO_SubmitApplication", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC032_PLv3HERO_FetchLeadPostSubmitApplication() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
        }











}
