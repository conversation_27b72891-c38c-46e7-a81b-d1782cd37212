package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestPLABFLTopup_OpenMandate extends BaseMethod{

        private static final Logger LOGGER = LogManager.getLogger(OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL_TOPUP.TestPLABFLTopup_OpenMandate.class);

        String entityType="INDIVIDUAL";
        String channel="PAYTM_APP";
        String leadId = "";
        String sessionToken = "";
        String consumerNumber="9755844880";
        String consumerPassword="paytm@123";
        String custId="1002550389";
        String token="";
        String requestBodyJsonPath="";
        String userIPAddress="";
        String staticTncAcceptanceTimeStamp="";
        String Email="";
        String DOB="1973-01-01";
        String PAN="";
        String occupation="";
        String income="";
        String firstName="BHAIRAVI";
        String lastName="LATASREE";
        String bureauRequest="";
        String bureauResponse="";
        String bureauCreditState="";
        String breCreditScore="";
         String stringify_json="{\\\"baseId\\\":\\\"b66d62bc-cb7f-4798-b7ea-18c5a39ca1d6\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"isBre2Required\\\":false,\\\"field_investigation_needed\\\":false,\\\"loanDownGradable\\\":false,\\\"isEmandateEligible\\\":1,\\\"offerEndDate\\\":\\\"Thu Jul 07 00:00:00 IST 2022\\\",\\\"lastFetchDate\\\":1633046400000,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":165000.0,\\\"maxTenure\\\":18,\\\"minLoanAmount\\\":27000.0,\\\"minTenure\\\":12,\\\"newOfferGenerated\\\":true,\\\"offerStartDate\\\":\\\"Tue Jun 07 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"490d8b0b-1956-44a8-ad26-6fdff64ecd96\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"99\\\",\\\"productType\\\":\\\"PL\\\",\\\"productVersion\\\":1,\\\"lenderSchemeId\\\":\\\"54103\\\",\\\"riskGrade\\\":\\\"VL\\\",\\\"riskSegment\\\":\\\"VL\\\",\\\"skipMandate\\\":false,\\\"sourceOfWhitelist\\\":\\\"RISK\\\"}";

         String loanTenure="";
        String loanAmount="";
        String loanEquatedMonthlyInstallment="";
        String loanRateofInterest="";
        String loanInterestAmount="";
        String loanProcessingFeeRate="";
        String loanDisbursalAmount="";
        String stampDutyCharges="";
        String brokerPeriodInterest="";
        String uuidCustomerPhoto = "";
        String uuidSelfie="";
        String loanOfferID="c3c92e4c-1703-4980-9aa5-87ff70f90713";
        String baseID="c3c92e4c-1703-4980-9aa5-87ff70f90713";
        String ckycName="";
        String code = "";
        String tncName = "";
        String url = "";
        String uniqueIdentifier ="";
        String md5 ="";
        String codeSanctionLetter = "";
        String tncNameSanctionLetter = "";
        String urlSanctionLetter = "";
        String uniqueIdentifierSanctionLetter = "";
        String md5SanctionLetter = "";
        String bankName="PAYTM BANK";
        String bankAccountNumber="************";
        String ifsc="PYTM0123456";
        String bankAccountHolderName="Shivangi Goswami";

         Boolean executeWithOutCallback=false;

        private String randomBankAccountNumber;
        Response responseObject= null;
        LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
        Utilities utility=new Utilities();



        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }

        @Test(description="Verify if there is any existing Personal Loan ABFL Topup Lead",groups= {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC001_PLv3_ABFL_TOPUP_fetchlLead()
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,
                    custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

            }

        }


        @Test(description="Verify reseting existing Personal Loan ABFL TOPUP lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3_ABFL_TOPUP_fetchlLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC002_PLv3_ABFL_TOPUP_DeleteExistingLead() {

            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams.put("leadId",leadId);
            queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
            queryParams.put("custId", custId);

            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA,
                    custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);

            lendingBaseClassObject.resetLendingLeads(queryParams, headers);

        }


        @Test(description="Create Lead for Personal Loan Migeration ABFL TOPUP",groups={"Regression"},dependsOnMethods = {"TC002_PLv3_ABFL_TOPUP_DeleteExistingLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC003_PLv3_ABFL_TOPUP_CreateLead() {

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF
                    , custId, LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);
            queryParams.put("entityType", entityType);
            queryParams.put("channel", channel);
            queryParams.put("custId", custId);

            //Headers
            Map<String, String> header = new HashMap<String, String>();
            header = LendingBaseAPI.setHeadersReceivedFromFE();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);
            header.put("ipAddress", LendingConstants.IP_ADDRESS);

            //Body
            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "CREATE_LEAD");
            body.put("mobile", consumerNumber);

            //solutionAdditionalInfo
            body.put("PRODUCT_ID", "99");
            body.put("PRODUCT_TYPE", "PL");
            body.put("FLOW_TYPE", "RISK");
            body.put("LOAN_OFFER_ID", loanOfferID);
            body.put("PRODUCT_VERSION", "1");
            body.put("BASE_ID", baseID);
            body.put("LENDER_ID", "6");
            body.put("WHITELISTING_SOURCE", "RISK");
            body.put("IS_EMANDATE_ELIGIBLE", "true");
            body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
            body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
            body.put("IS_FATHER_NAME_REQUIRED", "false");
            body.put("MARITAL_STATUS", "NOT_KNOWN");
            body.put("IS_BRE3_REQUIRED", "true");
            body.put("PINCODE", "600024");
            body.put("LENDER_NAME", "ABFL");
            body.put("PARENT_LOAN_ACCOUNT_NUMBER", "PYTMPL_ABFL786375637");
            body.put("MIGRATION_TYPE", "TOPUP");
            body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");

            //Offer Details & CIR Journey Flag will be passed in Request Body
            body.put("LOAN_TENURE", "6");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "24000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Twenty four Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "4418");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Four thousand four hundred eighteen");
            body.put("LOAN_RATE_OF_INTEREST", "35");
            body.put("LOAN_INTEREST_AMOUNT", "3242");
            body.put("LOAN_PROCESSING_FEE", "1200");
            body.put("PROCESSING_FEE_RATE", "5");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1416");
            body.put("LOAN_DISBURSAL_AMOUNT", "22584");
            body.put("STAMP_DUTY_CHARGES", "0");
            body.put("BROKEN_PERIOD_INTEREST", "0");
            body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");
            body.put("IS_CIR_MINIMAL_CLICK_JOURNEY","TRUE");
            body.put("LOAN_PRICING_GRID_VERSION","1684216798");
            body.put("LOAN_MIN_AMOUNT","24000");
            body.put("LOAN_MAX_AMOUNT","100000");
            body.put("RISK_GRADE","VL");
            body.put("IS_RTO_FLOW","TRUE");


            requestBodyJsonPath = "MerchantService/V1/workflow/lead/CreatePLABFLTopupCIRMinimalJourney.json";

            for(int i=0;i<10;i++) {
                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body, requestBodyJsonPath);
                if (responseObject.getStatusCode() == 201) {
                    LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                    leadId = responseObject.jsonPath().getString("leadId");
                    Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
                    Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
                    Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "6");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"), "99");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"), "1");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"), "PL");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"), "RISK");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"), "true");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"), "false");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"), "NOT_KNOWN");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"), "true");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"), "600024");
                    break;
                }
            }
        }



        @Test(description="Verify the PL v3 ABFL Topup lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3_ABFL_TOPUP_CreateLead")
        @Owner(emailId = "<EMAIL>")
        public void TC004_PLV3ABFLTopup_FetchLeadAllData() throws JSONException
        {
            for(int i=0;i<35;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, LendingConstants.INDIVIDUAL_ENTITY_TYPE
                        , LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage())) {

                    LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

                    LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_IN_PROGRESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "396");
                    break;
                } else if (responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_LINKING_FAILED)) {
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_LINKING_FAILED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "997");
                    break;
                }

            }
        }

        @Test(description = "Verify Fetch CIR for PLv3 lead",groups = {"Regression"},dependsOnMethods = "TC004_PLV3ABFLTopup_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC005_PLv3ABFL_TOPUP_FetchCIR() {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("longitude",LendingConstants.LONGITUDE);
            headers.put("latitude",LendingConstants.LATITUDE);
            headers.put("custId", custId);


            Map<String,Object> body=new HashMap<String,Object>();
            requestBodyJsonPath="MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";

            responseObject= lendingBaseClassObject.v3FetchCIR(queryParams, headers,body,requestBodyJsonPath);


            if(responseObject.getStatusCode()==200) {

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
                // Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
                Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");

                bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
                bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
                bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
                breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
            }
        }


        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC005_PLv3ABFL_TOPUP_FetchCIR")
        @Owner(emailId = "<EMAIL>")
        public void TC012_PLv3ABFLTOPUP_BRE1Callback() throws JSONException
        {
            for(int i =0;i<=28;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage()) || responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_GENERATED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage())) {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "225");

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                if(executeWithOutCallback){
                    for (int i = 0; i < 35; i++) {
                        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION,
                                LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

                        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().
                                getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
                            LOGGER.info("BRE 1 passed without callback");
                            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
                            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE_COMPLETED.getStage());
                            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
                            break;
                        }
                    }
                }else {
                    BRE1Callback(LendingConstants.PL_V3_SOLUTION);
                    if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().
                            getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
                        LOGGER.info("BRE 1 passed with callback");
                        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE_COMPLETED.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "227");
                    }
                }
            }
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
        }



        public Response BRE1Callback(String solution) {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", solution);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "BRE1_SUCCESS");
            body.put("BRE1_OFFER_DETAILS", ""+stringify_json+"");
            body.put("LENDING_SCHEME_ID", "54103");

            requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/PLHeroTopupCallbackRequest.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

            return responseObject;

            }


@Test(description = "Verify the PLv3 lead data after BRE1 Success",groups = {"Regression"},dependsOnMethods = "TC012_PLv3ABFLTOPUP_BRE1Callback")
        @Owner(emailId = "<EMAIL>")
        public void TC013_PLv3ABFLTOPUP_FetchDataPostBRE1Success() throws JSONException
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            loanOfferID=responseObject.jsonPath().getString("LOAN_OFFER_ID");
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"),bureauRequest);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"),bureauResponse);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"),bureauCreditState);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"),breCreditScore);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"108");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");

            }
            //Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
        }

        @Test(description = "Update KYC mock Data",groups = {"Regression"},dependsOnMethods = "TC013_PLv3ABFLTOPUP_FetchDataPostBRE1Success")
        @Owner(emailId = "<EMAIL>")
        public void TC014_PLV3_UpdateSearchByPANMockData(){
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            //queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowMovementRequired","false");
            body.put("status","SUCCESS");
            body.put("PAN",LendingConstants.OA_KYC_PAN);
            body.put("NSDL_NAME",LendingConstants.OA_NSDL_NAME);
            body.put("DOB",LendingConstants.OA_DOB);
            body.put("GENDER","MALE");
            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCMockData.json";
            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        }



        @Test(description = "Verify PL v3 Hero lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC014_PLV3_UpdateSearchByPANMockData")
        @Owner(emailId = "<EMAIL>")
        public void TC015_PLv3ABFLTOPUP_LoanOfferAccept() {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID,
                    custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation", "OFFER_ACCEPTED");

            //solutionAdditionalInfo
            body.put("LOAN_TENURE", "12");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "70000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Seventy Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "6824");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Six Thousand Eight Hundred twenty four");
            body.put("LOAN_RATE_OF_INTEREST", "30");
            body.put("LOAN_INTEREST_AMOUNT", "990.70");
            body.put("LOAN_PROCESSING_FEE", "3675");
            body.put("PROCESSING_FEE_RATE", "5.25");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "4336");
            body.put("LOAN_DISBURSAL_AMOUNT", "65348");
            body.put("STAMP_DUTY_CHARGES", "200");
            body.put("BROKEN_PERIOD_INTEREST", "116");
            body.put("LENDER_STATIC_TNC_SETNAME","pl_hero_ckyc_consent");
            body.put("LENDING_DYNAMIC_TNC",LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
            body.put("LENDING_DYNAMIC_SECONDARY_TNC",LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3HeroLoanOfferAccepted.json";

            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
                loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
                loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
                loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
                loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
                loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
                loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
                stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
                brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
            }
        }






    @Test(description="Verify the PL v3 ABFL Topup lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC015_PLv3ABFLTOPUP_LoanOfferAccept")
        @Owner(emailId = "<EMAIL>")
        public void TC005_PLV3ABFLTopup_UploadSelfie() throws InterruptedException {
            Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, sessionToken,"selfie","RohanOfflineAadhaar.jpg");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);

        }

        @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC005_PLV3ABFLTopup_UploadSelfie", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC06_PLv3ABFL_TOPUP_VerifyUploadedSelfie() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

            }

        }


        @Test(description = "Update data in SAI Table", dependsOnMethods = "TC06_PLv3ABFL_TOPUP_VerifyUploadedSelfie", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC07_LeadDataUpdateForKYC_InSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("leadId", leadId);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired","false");
            body.put("status", "SUCCESS");
            body.put("PAN",LendingConstants.OA_KYC_PAN);
            body.put("DOB", LendingConstants.OA_DOB);
            body.put("GENDER", "MALE");
            body.put("NSDL_NAME", LendingConstants.OA_NSDL_NAME);

            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCMockData.json";

            responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());

        }


        @Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC07_LeadDataUpdateForKYC_InSAI")
        @Owner(emailId = "<EMAIL>")
        public void TC08_InitiateKYC_UsingSearchByPan() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("kycMode","SEARCH_BY_PAN");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC08_InitiateKYC_UsingSearchByPan")
        @Owner(emailId = "<EMAIL>")
        public void TC09_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=35;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());


        }



        @Test(description = "Initiate KYC for CIR MInimal journey",groups = {"Regression"},dependsOnMethods = "TC09_FetchDataPostKYCIntiated")
        public void TC010_InitiateKYC_UsingOfflineAAdhaar() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("shareCode","1234");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";
            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Verify lead details after KYC",groups={"Regression"},dependsOnMethods = "TC010_InitiateKYC_UsingOfflineAAdhaar")
        @Owner(emailId = "<EMAIL>")
        public void TC011_FetchDataKYCInitiate() throws JSONException
        {
            for(int i =0;i<=25;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,
                        sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().
                        getString("stage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().
                    getString("feStage").equals(LendingLeadStages.BRE2_IN_PROGRESS.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"387");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());

        }


        @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC011_FetchDataKYCInitiate",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC012_SecondBRECallback() {

            for(int i =0;i<=55;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {

                    Map<String, String> queryParams = new HashMap<String, String>();
                    queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                    queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);
                    queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                    queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                    queryParams.put("leadId", leadId);

                    token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK, custId, LendingConstants.JWT_RISK_KEY);

                    Map<String, String> headers = new HashMap<String, String>();
                    headers.put("Authorization", token);
                    headers.put("Content-Type", "application/json");
                    headers.put("custId", custId);

                    Map<String, Object> body = new HashMap<String, Object>();
                    body.put("workflowOperation", "BRE2_SUCCESS");
                    body.put("BASE_ID", baseID);
                    body.put("LOAN_OFFER_ID", loanOfferID);
                    body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
                    body.put("SKIP_EMANDATE_ELIGIBLE", "false");
                    body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

                    requestBodyJsonPath = "MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
                    responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath, "BRE2");
                    LOGGER.info("BRE 2 Success with callback");
                } else {
                    // Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
                    LOGGER.info("BRE 2 Success without callback");
                }
            }

        }

        @Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC012_SecondBRECallback",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC013_FetchLeadVerifyAdditionalData() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
            }
        }


        @Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "TC013_FetchLeadVerifyAdditionalData",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC014_AdditionalDataCapture() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("leadId", leadId);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
                body.put("BUSINESS_EMAIL", "<EMAIL>");
                body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
                body.put("FATHER_NAME", "RAM");
                requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";

                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            }

        }




        @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC014_AdditionalDataCapture",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC015_BRE3Success() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            for(int i =0;i<10;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                    break;
                }
            }
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

        }




        @Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC015_BRE3Success", groups = {
                "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void TC020_EmandateCallback() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("status", "EMANDATE_SUCCESS");

            Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

        }

        @Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "TC020_EmandateCallback",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC021_FetchLeadPostEmandate() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
        }

        @Test(description = "For PL v3 Hero Generate Loan Agreement", dependsOnMethods = "TC021_FetchLeadPostEmandate", groups = { "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void TC022_GenerateLoanAgreement() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);

            Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());
            if (responseObject.jsonPath().getString("meta.status").equals("success")) {

                code = responseObject.jsonPath().getString("data.state.code");
                tncName = responseObject.jsonPath().getString("data.state.tncName");
                url = responseObject.jsonPath().getString("data.state.url");
                uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
                md5 = responseObject.jsonPath().getString("data.state.md5");
            }

        }


        @Test(description = "For PL v3 Hero Generate Sanction Letter", dependsOnMethods = "TC022_GenerateLoanAgreement", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC023_GenerateSanctionLetter() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

            queryParams.put("tncType", "LOAN_SANCTION_TNC");

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);

            Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());
            if (responseObject.jsonPath().getString("meta.status").equals("success")) {

                codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
                tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
                urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
                uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
                md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
            }

        }

        @Test(description = "For PL v3 Hero Verify submit application", dependsOnMethods = "TC023_GenerateSanctionLetter", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC024_SubmitApplication() throws InterruptedException {

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("session_token", sessionToken);
            headers.put("deviceIdentifier","APPLE");

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
            body.put("LENDING_DYNAMIC_TNC", tncName);
            body.put("TNC_ACCEPTED_VERSION", 1);
            body.put("KYB_TNC_REF_NO", uniqueIdentifier);
            body.put("TNC_ACCEPTED_CODE", md5);


            body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
            body.put("SECONDARY_ACCEPTED_TNC_VERSION", 1);

            body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
            body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3LoanAgreementAcceptrequest.json";
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());

            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));

        }

        @Test(description = "For PL v3 Hero Verify Lead stage Post Submit Loan Application", dependsOnMethods = "TC024_SubmitApplication", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC025_PLv3ABFL_FetchLeadPostSubmitApplication() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
        }










}
