package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL;

import OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PLv3_ABFL_InvalidShareCode extends BaseMethod{


        private static final Logger LOGGER = LogManager.getLogger(PLv3_ABFL_InvalidShareCode.class);
        oAuthServices oAuthServicesObject = new oAuthServices();
        MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
        Utilities UtilitiesObject = new Utilities();
        LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
        Utilities utility=new Utilities();

        String sessionToken = "";
        String leadId="";
        String custId="1002455037";
        String consumerNumber="6040066752";
        String consumerPassword="paytm@123";
        String token="";
        String stage="";
        String feStage="";
        String userIPAddress="";
        String loanUserLatitude="";
        String loanUserLongitude="";
        String tncAdditionalParam="";
        String staticTncAcceptanceTimeStamp="";
        String lenderCustomerId="";
        String requestBodyJsonPath="";
        String Pan="**********";
        String Email="";
        String DOB="";
        String applicationId="";
        String bureauRequest="";
        String bureauResponse="";
        String bureauCreditState="";
        String breCreditScore="";
        String PanValidationTimeStamp="";
        String uuid="";
        String md5="";
        String code="";
        String tncName="";
        String url="";
        String uniqueIdentifier="";
        ;
        String codeSanctionLetter="";
        String tncNameSanctionLetter="";
        String urlSanctionLetter="";
        String uniqueIdentifierSanctionLetter="";
        String md5SanctionLetter="";
        String sanctionLetterAcceptanceTimestamp="";
        String kybSecondaryTNCDisplayURL="";
        String loanAgreementDate="";
        String kybTNCDisplayURL="";
        String panNameMatchTimeStamp="";
        String panNameMatchPercentage="";
        String breLastFetchDate="";
        String offerRequest="";


        Response responseObject= null;
        private String randomBankAccountNumber;



        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }



        @Test(description = "Verify whether there is any existing stashfin lead present or not",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC001_FetchLeadDeatils()
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {


                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


            }

        }


        @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC002_DeleteExistingLead()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams.put("solution", LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("leadId",leadId);
            queryParams.put("custId", custId);

            queryParams.put("leadId",leadId);



            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);


            lendingBaseClassObject.resetLendingLeads(queryParams, headers);
        }




        @Test(description = "Create Fullerton Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC003_CreateFullertonLead()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
            queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custid", custId);
            headers.put("ipAddress", "************");

            Map<String,Object> body = new HashMap<String, Object>();
            body.put("workflowOperation","CREATE_LEAD");
            body.put("mobile", consumerNumber);
            body.put("PRODUCT_TYPE", "MCA");
            body.put("PRODUCT_VERSION", "1");
            body.put("PRODUCT_ID","85");
            body.put("LOAN_AMOUNT_IN_NUMBER", "20000");
            body.put("LOAN_INTEREST_AMOUNT", "3850");
            body.put("LOAN_AMOUNT_IN_WORDS", "Twenty Thousand");
            body.put("LOAN_MIN_AMOUNT", "15000");
            body.put("LOAN_MAX_AMOUNT", "30000");
            body.put("LOAN_TENURE", "450");
            body.put("LOAN_TENURE_MIN", "450");
            body.put("LOAN_TENURE_MAX", "450");
            body.put("LOAN_TENURE_UNIT", "DAY");
            body.put("LOAN_RATE_OF_INTEREST","30");
            body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "53");
            body.put("LOAN_PROCESSING_FEE", "600");
            body.put("LOAN_INCENTIVE", "5000");
            body.put("LOAN_INCENTIVE_ELIGIBLE", "200");
            body.put("LOAN_INCENTIVE_PERCENTAGE", "0");
            body.put("MERCHANT_ID", "wqiyTY44202413609711");
            body.put("BASE_ID", "MCA_Shivangi_Automation_1_04f87d33");
            body.put("LOAN_OFFER_ID", "MCA_Shivangi_Automation_1_04f87d33");
            body.put("RISK_GRADE", "MCA|DRB124");
            body.put("PROCESSING_FEE_RATE", "3.0");
            body.put("IS_ACCEPTANCE_ABOVE_5000", true);
            body.put("IS_SI_MANDATORY", true);
            body.put("IS_RESTRICTED_MERCHANT", true);
            body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", true);
            body.put("IS_EMANDATE_ELIGIBLE", true);
            body.put("STATIC_TNC_SETNAME", "loanstatictnc");
            body.put("LENDER_STATIC_TNC_SETNAME", "bl_ckyck_lender_consent_fullerton");
            body.put("LENDING_DYNAMIC_TNC", "bl_loan_agreement_fullerton");
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", "bl_sanction_letter_fullerton");
            body.put("FLOW_TYPE", "RISK");
            body.put("LENDER_ID", "15");
            body.put("IS_ADDITIONAL_DATA_REQUIRED", "False");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/CreateLeadRequestMCAMigration.json";





            for(int i=0;i<2;i++)
            {

                responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

                if(responseObject.getStatusCode()==201)
                    break;
            }


            if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"MCA");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"85");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"20000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),"3850");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_WORDS"),"Twenty Thousand");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MIN_AMOUNT"),"15000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"30000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"450");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"450");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"450");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"DAY");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"30");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_DAILY_INSTALLMENT"),"53");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),"600");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE"),"5000");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_ELIGIBLE"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INCENTIVE_PERCENTAGE"),"0");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MERCHANT_ID"),"wqiyTY44202413609711");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"MCA_Shivangi_Automation_1_04f87d33");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"MCA_Shivangi_Automation_1_04f87d33");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_GRADE"),"MCA|DRB124");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"loanstatictnc");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"),"bl_ckyck_lender_consent_fullerton");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"bl_loan_agreement_fullerton");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"bl_sanction_letter_fullerton");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"15");

                leadId=responseObject.jsonPath().getString("leadId");
                custId=responseObject.jsonPath().getString("custId");
                userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
                staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");



            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());

        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateFullertonLead",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC004_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<15;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
                    break;

            }

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            }


            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());


        }

        @Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC005_UpdateLeadBasicDetails()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation","BASIC_DETAILS");
            body.put("DOB", LendingConstants.DOB_STASHFIN);
            body.put("PAN", Pan);
            body.put("EMAIL", Utilities.randomEmailGeneration());

            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/MCABasicDetailRequest.json";


            for(int i=0;i<2;i++)
            {

                responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

                if(responseObject.getStatusCode()==200)
                    break;
            }


            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);


                leadId=responseObject.jsonPath().getString("leadId");
                custId=responseObject.jsonPath().getString("custId");
                userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
                staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
                Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
                DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
                Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
                PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());




        }


        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_UpdateLeadBasicDetails",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC006_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<15;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
                    break;

            }

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

                lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");

            }


            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());


        }

        @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC007_UpdateBureauDataSetInSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
            queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);


            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired", "false");
            body.put("F_NAME", "BHAIRAVI");
            body.put("L_NAME", "LATASREE");
            body.put("GENDER", "FEMALE");
            body.put("PINCODE", "600024");
            body.put("PAN", Pan);
            body.put("DOB", "1979-10-05");
            body.put("EMAIL", "<EMAIL>");

            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());


        }


        @Test(description = "Fetch CIR",dependsOnMethods = "TC007_UpdateBureauDataSetInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC008_FetchCIR()
        {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            headers.put("session_token", sessionToken);
            headers.put("Content-Type", "application/json");

            Map<String,Object> body=new HashMap<String,Object>();

            String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";



            for(int i=0;i<10;i++)
            {
                responseObject= lendingBaseClassObject.v3FetchCIR(queryParams, headers,body,requestjsonpath);


                if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))

                    break;

            }


            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


            bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
            bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
            bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
            breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");

        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC008_FetchCIR",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC009_FetchLeadAllData() throws JSONException
        {
            for(int i=0;i<13;i++)
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
                    break;

            }
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))

            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");

                //Hit BRE1 Callback

                LOGGER.info("Callback not coming so hitting BRE1 Callback");

                responseObject=    lendingBaseClassObject. BRE1CallbackforMCA (leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);


            }

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_SUCCESS.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
            }



            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
        }

        @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC009_FetchLeadAllData", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC010_UpdateExistingDetailsInSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
            queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);


            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired", "false");
            body.put("F_NAME", "BHAIRAVI");
            body.put("L_NAME", "LATASREE");
            body.put("GENDER", "FEMALE");
            body.put("PINCODE", "600024");
            body.put("PAN", Pan);
            body.put("DOB", "1979-10-05");
            body.put("EMAIL", "<EMAIL>");

            responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");


        }

        @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC010_UpdateExistingDetailsInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>",isAutomated = true)
        public void TC011_FetchLeadAllData() throws JSONException, InterruptedException
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))

            {

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");

            }

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))

            {

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_REQUESTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");

                responseObject=lendingBaseClassObject.BRE1CallbackforMCA(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,custId);


                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());

        }


        @Test(description = "Update data in SAI Table", dependsOnMethods = "TC011_FetchLeadAllData", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC012_LeadDataUpdateForKYC_InSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution",LendingConstants.BUSINESS_LENDING_V3);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2",LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);
            queryParams.put("solutionTypeLevel3",LendingConstants.UNSECURED_SHORT_TERM_LOAN_SIMPLIFIED);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);



            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired", "false");
            body.put("PAN","**********");
            body.put("DOB", "1998-09-12");
            body.put("GENDER", "MALE");
            body.put("NSDL_NAME", "Rohan Shivaji Sonawane");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";

            responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            //Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());


        }

        @Test(description = "Verify lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC012_LeadDataUpdateForKYC_InSAI")
        @Owner(emailId = "<EMAIL>")
        public void TC013_uploadCustomerPhoto() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.KYCImageUpload("customerPhoto", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2, sessionToken,"others","RohanOfflineAadhaar.jpg");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");


        }
        @Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC013_uploadCustomerPhoto")
        @Owner(emailId = "<EMAIL>")
        public void TC014_VerifyUploadedCustomerPhoto() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            //Customerphoto
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");

        }

        @Test(description = "Verify the ABFL lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC014_VerifyUploadedCustomerPhoto")
        @Owner(emailId = "<EMAIL>")
        public void TC015_UploadSelfie() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.BUSINESS_LENDING_V3, LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2, sessionToken,"selfie","RohanOfflineAadhaar.jpg");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");


        }

        @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC015_UploadSelfie", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC016_VerifyUploadedSelfie() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


            }

        }


        @Test(description="Initiate KYC using OA using invalid share code First attempt",groups = {"Regression"},dependsOnMethods = "TC016_VerifyUploadedSelfie")
        @Owner(emailId = "<EMAIL>")
        public void TC017_InitiateKYC_UsingInvalidShareCode_FirstAttempt() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("shareCode","A");


            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC017_InitiateKYC_UsingInvalidShareCode_FirstAttempt")
        @Owner(emailId = "<EMAIL>")
        public void TC018_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=25;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2025");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage());


        }
        @Test(description="Initiate KYC using OA using invalid share code 2nd attempt",groups = {"Regression"},dependsOnMethods = "TC018_FetchDataPostKYCIntiated")
        @Owner(emailId = "<EMAIL>")
        public void TC019_InitiateKYC_UsingInvalidShareCode_SecondAttempt() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("shareCode","A");


            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC019_InitiateKYC_UsingInvalidShareCode_SecondAttempt")
        @Owner(emailId = "<EMAIL>")
        public void TC020_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=25;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2025");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_RETRYABLE_FAILURE.getStage());


        }

        @Test(description="Initiate KYC using OA with Invalid shareCode third attempt",groups = {"Regression"},dependsOnMethods = "TC020_FetchDataPostKYCIntiated")
        @Owner(emailId = "<EMAIL>")
        public void TC021_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_ThirdAttempt() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("shareCode","11");


            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC021_InitiateKYC_UsingOfflineAAdhaar_InvalidShareCode_ThirdAttempt")
        @Owner(emailId = "<EMAIL>")
        public void TC022_FetchDataPostKYCIntiated() throws JSONException
        {

            for(int i =0;i<=25;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.FULLERTON_SOLUTION_TYPE_LEVEL_2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_FAILED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_FAILED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_FAILED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2002");
            Assert.assertEquals(responseObject.jsonPath().getString("KYC_FAILURE_REASON"),"WRONG_ZIP_SHARECODE_COMBINATION");


        }







}
