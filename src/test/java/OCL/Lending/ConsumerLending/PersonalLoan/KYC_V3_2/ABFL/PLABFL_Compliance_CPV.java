package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PLABFL_Compliance_CPV extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(PLABFL_Compliance_CPV.class);
    String solution="personal_loan_v3";
    String solutionTypeLevel2="HERO";
    String entityType="INDIVIDUAL";
    String channel="PAYTM_APP";
    String leadId = "";
    String sessionToken = "";
    String consumerNumber="9921470990";
    String consumerPassword="paytm@123";
    String custId="1701676820";
    String token="";
    String requestBodyJsonPath="";
    String userIPAddress="";
    String staticTncAcceptanceTimeStamp="";
    String Email="<EMAIL>";
    String DOB="1988-12-18";
    String PAN="**********";
    String occupation="";
    String income="";
    String firstName="BHAIRAVI";
    String lastName="LATASREE";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String stringify_json="{\\\"baseId\\\":\\\"pl_hero_1701676820_24b7e152\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"creditScore\\\":808,\\\"eMandateType\\\":\\\"MANDATORY\\\",\\\"field_investigation_needed\\\":false,\\\"lenderSchemeId\\\":90,\\\"lenderRiskCode\\\":\\\"CL_PL\\\",\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":200000.0,\\\"minLoanAmount\\\":10000.0,\\\"newOfferGenerated\\\":true,\\\"ntc\\\":0,\\\"offerEndDate\\\":\\\"Wed Jun 29 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"747ebf1e-4e79-460e-a7ec-8caf19af0f2b\\\",\\\"offerStartDate\\\":\\\"Thu Mar 31 00:00:00 IST 2022\\\",\\\"offerValidity\\\":90,\\\"paytmThick\\\":0,\\\"productId\\\":\\\"191\\\",\\\"productVersion\\\":1,\\\"riskGrade\\\":\\\"L\\\",\\\"riskSegment\\\":\\\"L\\\",\\\"sourceOfWhitelist\\\":\\\"RISK\\\",\\\"isBre2Required\\\":false,\\\"skipMandate\\\":false,\\\"loanDownGradable\\\":false}";
    String loanTenure="";
    String loanAmount="";
    String loanEquatedMonthlyInstallment="";
    String loanRateofInterest="";
    String loanInterestAmount="";
    String loanProcessingFeeRate="";
    String loanDisbursalAmount="";
    String stampDutyCharges="";
    String brokerPeriodInterest="";
    String uuidCustomerPhoto = "";
    String uuidSelfie="";
    String loanOfferID="669f6d19-ab4a-4fc1-9857-42e9c28e8295";
    String baseID="pl_hero_1701676820_24b7e152";
    String ckycName="";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier ="";
    String md5 ="";
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String bankName="PAYTM BANK";
    String bankAccountNumber="************";
    String ifsc="PYTM0123456";
    String bankAccountHolderName="Bene Customer Name";
    private String randomBankAccountNumber;
    boolean basic_prefilling_flag =true;


    Response responseObject= null;
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility=new Utilities();


    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }



    @Test(description="Verify if there is any existing Personal Loan Migration HERO Lead",groups= {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC001_PLv3HERO_fetchlLead()
    {
        responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200)
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId=responseObject.jsonPath().getString("leadId");
        }

        if(responseObject.jsonPath().getInt("statusCode")==404)
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description="Verify reseting existing Personal Loan Migration HERO lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3HERO_fetchlLead"})
    @Owner(emailId = "<EMAIL>")
    public void TC002_PLv3HERO_DeleteExistingLead() {

        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams.put("leadId",leadId);
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);

        lendingBaseClassObject.resetLendingLeads(queryParams, headers);

    }

    @Test(description="Create Lead for Personal Loan Migeration HERO",groups={"Regression"},dependsOnMethods = {"TC002_PLv3HERO_DeleteExistingLead"})
    @Owner(emailId = "<EMAIL>")
    public void TC003_PLv3HERO_CreateLead() {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        //Parameters
        Map <String,String> queryParams= new HashMap<String,String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
        queryParams.put("entityType",entityType);
        queryParams.put("channel", channel);

        //Headers
        Map <String,String> header = new HashMap<String,String>();
        header = LendingBaseAPI.setHeadersReceivedFromFE();
        header.put("Authorization", token);
        header.put("Content-Type", "application/json");
        header.put("custid", custId);
        header.put("ipAddress", LendingConstants.IP_ADDRESS);

        //Body
        Map <String,Object> body = new HashMap<String,Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);

        //solutionAdditionalInfo
        body.put("PRODUCT_ID", "191");
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_OFFER_ID", loanOfferID);
        body.put("PRODUCT_VERSION", "1");
        body.put("BASE_ID", baseID);
        body.put("LENDER_ID", "5");
        body.put("WHITELISTING_SOURCE", "RISK");
        body.put("IS_EMANDATE_ELIGIBLE", "true");
        body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
        body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
        body.put("IS_FATHER_NAME_REQUIRED", "true");
        body.put("MARITAL_STATUS", "NOT_KNOWN");
        body.put("IS_BRE3_REQUIRED", "true");
        body.put("PINCODE", "600024");
        body.put("BUREAU_PRIORITY","EXPERIAN");



        //leadAdditionalInfo
        body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "TRUE");
        body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
        if(basic_prefilling_flag) {
            body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "TRUE");
        }else{
            body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "FALSE");
        }
        requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLMigerationHEROLeadRequest.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
        if (responseObject.getStatusCode() == 201) {
            LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            leadId = responseObject.jsonPath().getString("leadId");
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"191");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
        }

    }

    @Test(description="Verify the PL v3 HERO lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3HERO_CreateLead")
    @Owner(emailId = "<EMAIL>")
    public void TC004_PLV3HERO_FetchLeadAllData() throws JSONException
    {

        for(int i=0;i<15;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage()))
                break;

        }

        if(basic_prefilling_flag) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage())) {
                LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "286");
            }
        }else{
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());

        }


    }


    @Test(description="Add Basic & Occupation details",groups = {"Regression"},dependsOnMethods = "TC004_PLV3HERO_FetchLeadAllData")
    @Owner(emailId = "<EMAIL>")
    public void TC005_BD_OD_Details(){
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                custId,LendingConstants.LENDING_BFF_SECRET);
        //Parameters
        Map <String,String> queryParams= new HashMap<String,String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
        queryParams.put("entityType",entityType);
        queryParams.put("channel", channel);
        queryParams.put("leadId",leadId);

        //Headers
        Map <String,String> header = new HashMap<String,String>();
        header.put("Authorization", token);
        header.put("Content-Type", "application/json");
        header.put("custid", custId);

        //Body
        Map <String,Object> body = new HashMap<String,Object>();
        body.put("workflowOperation","OCCUPATION_DETAILS");
        body.put("OCCUPATION","Salaried");
        body.put("ANNUAL_INCOME","200000");
        body.put("EMPLOYER_ID","E66767");
        body.put("EMPLOYER_NAME","Paytm");
        body.put("BUSINESS_NAME","Shivangi QR service");
        body.put("DOB",DOB);
        body.put("PAN",PAN);
        body.put("EMAIL",Email);
        body.put("PINCODE","600024");
        body.put("LOAN_PURPOSE","Personal use");
        body.put("LOAN_PURPOSE_KEY","2");
        body.put("GENDER","Female");
        body.put("STATIC_TNC_SETNAME","personalloan_oclconsent_hero");
        body.put("LENDER_STATIC_TNC_SETNAME","pl_hero_ckyc_consent");
        body.put("F_NAME", "TOUCH");
        body.put("M_NAME", "WOOD");
        body.put("L_NAME", "LIMITED");
        body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
        body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateBDODDetails.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
        if(!(responseObject.getStatusCode()==200)){
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );

        }
        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");


    }

    @Test(description="Add Basic & Occupation details",groups = {"Regression"},dependsOnMethods = "TC004_PLV3HERO_FetchLeadAllData")
    @Owner(emailId = "<EMAIL>")
    public void TC006_FetchDetailsAfterBD_OD_Details(){
        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),PAN);

    }


    @Test(description = "Verify Fetch CIR for PLv3 lead",groups = {"Regression"},dependsOnMethods = "TC006_FetchDetailsAfterBD_OD_Details")
    @Owner(emailId = "<EMAIL>")
    public void TC011_PLv3HERO_FetchCIR() {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF
                , custId,LendingConstants.LENDING_BFF_SECRET);
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("longitude",LendingConstants.LONGITUDE);
        headers.put("latitude",LendingConstants.LATITUDE);
        headers.put("custId", custId);


        Map<String,Object> body=new HashMap<String,Object>();
        String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
        responseObject= lendingBaseClassObject.v3FetchCIR(queryParams, headers,body,requestjsonpath);


        if(responseObject.getStatusCode()==200) {

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
            //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");

            bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
            bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
            bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
            breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
        }
    }

    @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC011_PLv3HERO_FetchCIR")
    @Owner(emailId = "<EMAIL>")
    public void TC012_PLv3HERO_BRE1Callback() throws JSONException
    {

        for(int i =0;i<=15;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            for(int i =0;i<15;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
                    LOGGER.info("BRE 1 passed without callback");
                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                    loanOfferID=responseObject.jsonPath().getString("LOAN_OFFER_ID");
                    break;
                }
            }
            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PLv3HEROBRE1CallbackRequest.json";
            BRE1Callback(requestBodyJsonPath);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
                LOGGER.info("BRE 1 passed with callback");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
            }
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
        }
    }


    @Test(description = "Verify the PLv3 lead data after BRE1 Success",groups = {"Regression"},dependsOnMethods = "TC012_PLv3HERO_BRE1Callback")
    @Owner(emailId = "<EMAIL>")
    public void TC013_PLv3HERO_FetchDataPostBRE1Success() throws JSONException
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"),bureauRequest);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"),bureauResponse);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"),bureauCreditState);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"),breCreditScore);

        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
    }

    public Response BRE1Callback(String requestBodyJsonPath) {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solution);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BRE1_OFFER_DETAILS", ""+stringify_json+"");
        body.put("LENDING_SCHEME_ID", "90");

        responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;

    }

    @Test(description = "Verify PL v3 HERO lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC013_PLv3HERO_FetchDataPostBRE1Success")
    @Owner(emailId = "<EMAIL>")
    public void TC014_PLv3HERO_LoanOfferAccept() {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation", "OFFER_ACCEPTED");

        //solutionAdditionalInfo
        body.put("LOAN_TENURE", "12");
        body.put("LOAN_TENURE_UNIT", "MONTH");
        body.put("LOAN_AMOUNT_IN_NUMBER", "70000");
        body.put("LOAN_AMOUNT_IN_WORDS", "Seventy Thousand");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "6824");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Six Thousand Eight Hundred twenty four");
        body.put("LOAN_RATE_OF_INTEREST", "30");
        body.put("LOAN_INTEREST_AMOUNT", "990.70");
        body.put("LOAN_PROCESSING_FEE", "3675");
        body.put("PROCESSING_FEE_RATE", "5.25");
        body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "4336");
        body.put("LOAN_DISBURSAL_AMOUNT", "65348");
        body.put("STAMP_DUTY_CHARGES", "200");
        body.put("BROKEN_PERIOD_INTEREST", "116");
        body.put("LENDING_DYNAMIC_SECONDARY_TNC","sanction_letter_pl_hero");
        body.put("LENDING_DYNAMIC_TNC","loan_agreement_pl_hero");
        body.put("LENDER_NAME","HERO");
        body.put("LOAN_PURPOSE_STATIC_TNC_SETNAME","purpose_of_loan_static_tnc_setname");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3HEROLoanOfferAccepted.json";

        responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
            loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
            loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
            loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
            loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
            loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
            loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
            loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
            stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
            brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
        }
    }




    @Test(description = "Verify the PLv3 lead data after Loan offer accept",groups = {"Regression"},dependsOnMethods = "TC014_PLv3HERO_LoanOfferAccept")
    @Owner(emailId = "<EMAIL>")
    public void TC015_PLv3HERO_FetchDataPostLoanOfferAccept() throws JSONException
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                /*Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);

                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest);
*/            }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
    }


    @Test(description = "Verify the HERO lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC015_PLv3HERO_FetchDataPostLoanOfferAccept")
    @Owner(emailId = "<EMAIL>")
    public void TC018_PLv3HERO_UploadSelfie() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken,"selfie","RohanOfflineAadhaar.jpg");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
    }

    @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC018_PLv3HERO_UploadSelfie", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC019_PLv3HERO_VerifyUploadedSelfie() {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

        }else {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


        }

    }


    @Test(description = "Update data in SAI Table", dependsOnMethods = "TC019_PLv3HERO_VerifyUploadedSelfie", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC020_LeadDataUpdateForKYC_InSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId", leadId);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired","false");
        body.put("PAN",LendingConstants.OA_KYC_PAN);
        body.put("NSDL_NAME",LendingConstants.OA_NSDL_NAME);
        body.put("DOB",LendingConstants.OA_DOB);
        body.put("GENDER","MALE");

        requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCMockData.json";

        responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());

    }





    @Test(description="Initiate KYC using OA",groups = {"Regression"},dependsOnMethods = "TC020_LeadDataUpdateForKYC_InSAI")
    @Owner(emailId = "<EMAIL>")
    public void TC021_InitiateKYC_UsingOfflineAAdhaar() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("shareCode","1234");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";

        responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

    }

    @Test(description = "Verify lead details after KYC initaited again",groups={"Regression"},dependsOnMethods = "TC021_InitiateKYC_UsingOfflineAAdhaar")
    @Owner(emailId = "<EMAIL>")
    public void TC022_FetchDataPostKYCIntiated() throws JSONException
    {

        for(int i =0;i<=45;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_ADDRESS_CONSENT_OPTIONAL.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_ADDRESS_CONSENT_OPTIONAL.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_ADDRESS_CONSENT_OPTIONAL.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1021");
            Assert.assertEquals(responseObject.jsonPath().getString("IS_CPV_ENABLED"),"true");
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

        }

        // Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.PAN_VERIFIED.getStage());


    }

    @Test(description = "Verify updating current Address details", dependsOnMethods = "TC022_FetchDataPostKYCIntiated",groups={"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC023_CURRENT_ADDRESS_UPDATED() throws JSONException {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID,
                custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CURRENT_ADDRESS_UPDATED");
        body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "FALSE");
        body.put("pincode", "110049");
        body.put("state", "Delhi");
        body.put("city", "South Delhi");
        body.put("line1", "House number 1111");
        body.put("line2", "KJO Villa");
        body.put("line3", "Gautam Nagar");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType", "CURRENT");
        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PL_CPV/PL_KYC_CPV_CURRENT_ADDRESS_UPDATED.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.CURRENT_ADDRESS_UPDATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1022");
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.CURRENT_ADDRESS_UPDATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("addresses[1].addressSubType"),"CURRENT");
        Assert.assertEquals(responseObject.jsonPath().getString("addresses[1].pincode"),"110049");

    }


    @Test(description = "Update Mock Address for BRE2", groups={"Regression"},dependsOnMethods = "TC023_CURRENT_ADDRESS_UPDATED")
    @Owner(emailId = "<EMAIL>")
    public void TC024_Mock_Address_Update(){
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId", leadId);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired","false");
        body.put("statusMessage","ADDITIONAL_INFO_UPDATE");
        body.put("mobile","9792131516");
        body.put("status","SUCCESS");
        body.put("PAN","**********");
        body.put("F_NAME","JAFAI");
        body.put("PINCODE","560083");
        body.put("L_NAME","VASIM");
        body.put("DOB","1992-10-29");

        body.put("refId","null-118");
        body.put("statusCode","0");
        body.put("pincode","560083");
        body.put("postalCode","560083");
        body.put("state","Karnataka");
        body.put("city","BANGALORE");
        body.put("line1","RUDA AAVAS YOJANA QTR NO 504");
        body.put("line2","PARAPIPALIYA NU PATIYU");
        body.put("line3","JAMNAGAR ROAD PARAPIAPLIYA");
        body.put("latitude","0.0");
        body.put("longitude","0.0");
        body.put("addressType","RESIDENTIAL");
        body.put("addressSubType","PERMANENT");

        requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/MockAddressUpdateforBre2.json";

        responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.CURRENT_ADDRESS_UPDATED.getStage());



    }


    @Test(description = "Verify uploading OVD Documents",groups = {"Regression"},dependsOnMethods = "TC024_Mock_Address_Update")
    @Owner(emailId = "<EMAIL>")
    public void TC025_CURRENT_ADDRESS_OVD_UPLOADED(){
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID,
                custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CURRENT_ADDRESS_OVD_UPLOADED");
        body.put("LOAN_USER_CPV_STATIC_TNC_SETNAME", "cpv_visit_consent_hfcl");
        body.put("docType", "loanUserAddresssProofOVD");
        body.put("docProvided", "loanUserAadhaarPage1");
        body.put("docValue", "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");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PL_CPV/CURRENT_ADDRESS_OVD_UPLOADED.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.CURRENT_ADDRESS_OVD_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1023");
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.CURRENT_ADDRESS_OVD_UPLOADED.getStage());


    }




    @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC025_CURRENT_ADDRESS_OVD_UPLOADED",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC026_PLv3HERO_SecondBRECallback() {

        for(int i =0;i<=55;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
                break;
            }else if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {

                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("leadId", leadId);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,
                        LendingConstants.JWT_RISK, custId, LendingConstants.JWT_RISK_KEY);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "BRE2_SUCCESS");
                body.put("BASE_ID", baseID);
                body.put("LOAN_OFFER_ID", loanOfferID);
                body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
                body.put("SKIP_EMANDATE_ELIGIBLE", "false");
                body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

                requestBodyJsonPath = "MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
                responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath, "BRE2");
                LOGGER.info("BRE 2 Success with callback");

            } else {

                // Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
                LOGGER.info("BRE 2 Success without callback");
            }
        }

    }

      /*  @Test(description = "Verify PL v3 Lead After BRE2 success Callback", dependsOnMethods = "TC026_PLv3HERO_SecondBRECallback", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC027_PLv3HERO_FetchDataAfterBRE2Success() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_COMPLETE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"388");

        }*/


    @Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC026_PLv3HERO_SecondBRECallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC028_PLv3HERO_FetchLeadVerifyAdditionalData() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
        }else {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
        }
    }

    @Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "TC028_PLv3HERO_FetchLeadVerifyAdditionalData",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC030_PLv3HERO_AdditionalDataCapture() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("leadId", leadId);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF
                    , custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
            body.put("BUSINESS_EMAIL", "<EMAIL>");
            body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
            body.put("FATHER_NAME", "RAM");
            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        }

    }




    @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC030_PLv3HERO_AdditionalDataCapture",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC031_PLv3HERO_BRE3Success() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        for(int i =0;i<10;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                break;
            }
        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

    }


    @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC031_PLv3HERO_BRE3Success", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC032_PLv3HERO_UpdateKYCNameInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId", leadId);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                custId,LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired","false");
        body.put("status", "SUCCESS");
        body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
        body.put("PAN", PAN);

        requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
        responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE3_SUCCESS.getStage());
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        ckycName=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME");
    }


    @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC032_PLv3HERO_UpdateKYCNameInSAI",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC033_PLv3HERO_FetchLeadUpdateCKYCinSAI() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);
        Assert.assertEquals(responseObject.jsonPath().getString("CPV_TYPE"),"Compliance");

    }

    @Test(description = "Verify PL v3 Lead the bank details", dependsOnMethods = "TC033_PLv3HERO_FetchLeadUpdateCKYCinSAI", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC034_PLv3HERO_SaveBankDetails() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "VERIFY_BANK_DETAILS");
        body.put("EMANDATE_TYPE", "Internet Banking");
        body.put("bankName", "PAYTM BANK");
        body.put("bankAccountNumber",randomBankAccountNumber);
        body.put("ifsc", "PYTM0123956");
        body.put("bankAccountHolderName", LendingConstants.BANK_NAME_STAGING3);

        Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

        if (responseObject.getStatusCode() == 200) {

            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");

        }

        else {

            for (int i = 1; i < 6; i++) {
                LOGGER.info("Again hitting with same data: retry-count: " + i);
                responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
                    break;
            }

        }


        Assert.assertEquals(responseObject.getStatusCode(),200);

    }


    @Test(description = "For PL v3 Bank Verification", dependsOnMethods = "TC034_PLv3HERO_SaveBankDetails",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC035_PLv3HERO_FetchLeadPostBankVerification() {
        for(int i=0;i<5;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
                break;
            }
        }
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),bankName);
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),bankAccountNumber);
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),ifsc);
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),bankAccountHolderName);


    }



    @Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC035_PLv3HERO_FetchLeadPostBankVerification", groups = {
            "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC036_PLv3HERO_EmandateCallback() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("status", "EMANDATE_SUCCESS");

        Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

    }

    @Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "TC036_PLv3HERO_EmandateCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC037_PLv3HERO_FetchLeadPostEmandate() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
    }

    @Test(description = "For PL v3 Hero Generate Loan Agreement", dependsOnMethods = "TC037_PLv3HERO_FetchLeadPostEmandate", groups = { "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC038_PLv3HERO_GenerateLoanAgreement() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);

        Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " + responseObject.getStatusCode());
        if (responseObject.jsonPath().getString("meta.status").equals("success")) {

            code = responseObject.jsonPath().getString("data.state.code");
            tncName = responseObject.jsonPath().getString("data.state.tncName");
            url = responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5 = responseObject.jsonPath().getString("data.state.md5");
        }

    }


    @Test(description = "For PL v3 Hero Generate Sanction Letter", dependsOnMethods = "TC038_PLv3HERO_GenerateLoanAgreement", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC039_PLv3HERO_GenerateSanctionLetter() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        queryParams.put("tncType", "LOAN_SANCTION_TNC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);

        Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " + responseObject.getStatusCode());
        if (responseObject.jsonPath().getString("meta.status").equals("success")) {

            codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
            tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
            urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
        }

    }


    @Test(description = "For PL v3 Hero Verify submit application", dependsOnMethods = "TC039_PLv3HERO_GenerateSanctionLetter", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC040_PLv3HERO_SubmitApplication() throws InterruptedException {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sessionToken);
        headers.put("deviceIdentifier","APPLE");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
        body.put("LENDING_DYNAMIC_TNC", tncName);
        body.put("TNC_ACCEPTED_VERSION", 1);
        body.put("KYB_TNC_REF_NO", uniqueIdentifier);
        body.put("TNC_ACCEPTED_CODE", md5);


        body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC_VERSION", 1);

        body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);

        requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3LoanAgreementAcceptrequest.json";
        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

        LOGGER.info("Status Code : " + responseObject.getStatusCode());

        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));

    }

    @Test(description = "For PL v3 Hero Verify Lead stage Post Submit Loan Application", dependsOnMethods = "TC040_PLv3HERO_SubmitApplication", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC041_PLv3HERO_FetchLeadPostSubmitApplication() {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
    }








}


