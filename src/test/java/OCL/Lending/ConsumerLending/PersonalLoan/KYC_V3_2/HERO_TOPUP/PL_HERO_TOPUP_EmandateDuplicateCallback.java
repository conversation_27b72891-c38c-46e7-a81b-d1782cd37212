package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO_TOPUP;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PL_HERO_TOPUP_EmandateDuplicateCallback extends BaseMethod{

        private static final Logger LOGGER = LogManager.getLogger(PL_HERO_TOPUP_EmandateDuplicateCallback.class);

        String entityType="INDIVIDUAL";
        String channel="PAYTM_APP";
        String leadId = "";
        String sessionToken = "";
        String consumerNumber="9855944077";
        String consumerPassword="paytm@123";
        String custId="1700162741";
        String token="";
        String requestBodyJsonPath="";
        String userIPAddress="";
        String staticTncAcceptanceTimeStamp="";
        String Email="";
        String DOB="";
        String PAN="";
        String occupation="";
        String income="";
        String firstName="BHAIRAVI";
        String lastName="LATASREE";
        String bureauRequest="";
        String bureauResponse="";
        String bureauCreditState="";
        String breCreditScore="";
        String loanTenure="";
        String loanAmount="";
        String loanEquatedMonthlyInstallment="";
        String loanRateofInterest="";
        String loanInterestAmount="";
        String loanProcessingFeeRate="";
        String loanDisbursalAmount="";
        String stampDutyCharges="";
        String brokerPeriodInterest="";
        String uuidCustomerPhoto = "";
        String uuidSelfie="";
        String loanOfferID="3a3cef8d-0e32-493e-9739-23f3d366cff4";
        String baseID="8948df13-2079-4cab-9796-347064df0e42";
        String ckycName="";
        String code = "";
        String tncName = "";
        String url = "";
        String uniqueIdentifier ="";
        String md5 ="";
        String codeSanctionLetter = "";
        String tncNameSanctionLetter = "";
        String urlSanctionLetter = "";
        String uniqueIdentifierSanctionLetter = "";
        String md5SanctionLetter = "";
        String bankName="PAYTM BANK";
        String bankAccountNumber="************";
        String ifsc="PYTM0123456";
        String bankAccountHolderName="Shivangi Goswami";
        private String randomBankAccountNumber;
        Response responseObject= null;
        LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
        Utilities utility=new Utilities();



        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }



        @Test(description="Verify if there is any existing Personal Loan ABFL Topup Lead",groups= {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC001_PLv3_HERO_TOPUP_fetchlLead()
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

            }

        }


        @Test(description="Verify reseting existing Personal Loan ABFL TOPUP lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3_HERO_TOPUP_fetchlLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC002_PLv3_HERO_TOPUP_DeleteExistingLead() {

            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams.put("leadId",leadId);
            queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
            queryParams.put("custId", custId);

            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);

            lendingBaseClassObject.resetLendingLeads(queryParams, headers);

        }


        @Test(description="Create Lead for Personal Loan Migeration ABFL TOPUP",groups={"Regression"},dependsOnMethods = {"TC002_PLv3_HERO_TOPUP_DeleteExistingLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC003_PLv3_HERO_TOPUP_CreateLead() {

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId, LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
            queryParams.put("entityType", entityType);
            queryParams.put("channel", channel);
            queryParams.put("custId", custId);

            //Headers
            Map<String, String> header = new HashMap<String, String>();
            header = LendingBaseAPI.setHeadersReceivedFromFE();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);
            header.put("ipAddress", LendingConstants.IP_ADDRESS);

            //Body
            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "CREATE_LEAD");
            body.put("mobile", consumerNumber);

            //solutionAdditionalInfo
            body.put("PRODUCT_ID", "192");
            body.put("PRODUCT_TYPE", "PL");
            body.put("FLOW_TYPE", "RISK");
            body.put("LOAN_OFFER_ID", loanOfferID);
            body.put("PRODUCT_VERSION", "1");
            body.put("BASE_ID", baseID);
            body.put("LENDER_ID", "5");
            body.put("WHITELISTING_SOURCE", "RISK");
            body.put("IS_EMANDATE_ELIGIBLE", "true");
            body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
            body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
            body.put("IS_FATHER_NAME_REQUIRED", "false");
            body.put("MARITAL_STATUS", "NOT_KNOWN");
            body.put("IS_BRE3_REQUIRED", "true");
            body.put("PINCODE", "600024");
            body.put("LENDER_NAME", "HERO");
            body.put("PARENT_LOAN_ACCOUNT_NUMBER", "PYTMHET1925635032");
            body.put("MIGRATION_TYPE", "TOPUP");
            body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");
            body.put("LOAN_ACCOUNT_NUMBER", "PYTMHET1925635032");
            //Offer Details & CIR Journey Flag will be passed in Request Body
            body.put("LOAN_TENURE", "6");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "24000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Twenty four Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "4418");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Four thousand four hundred eighteen");
            body.put("LOAN_RATE_OF_INTEREST", "35");
            body.put("LOAN_INTEREST_AMOUNT", "3242");
            body.put("LOAN_PROCESSING_FEE", "1200");
            body.put("PROCESSING_FEE_RATE", "5");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1416");
            body.put("LOAN_DISBURSAL_AMOUNT", "22584");
            body.put("STAMP_DUTY_CHARGES", "0");
            body.put("BROKEN_PERIOD_INTEREST", "0");
            body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");
            body.put("IS_CIR_MINIMAL_CLICK_JOURNEY","TRUE");
            body.put("LOAN_PRICING_GRID_VERSION","1684216798");
            body.put("LOAN_MIN_AMOUNT","24000");
            body.put("LOAN_MAX_AMOUNT","100000");
            body.put("RISK_GRADE","VL");
            body.put("IS_RTO_FLOW","TRUE");



            requestBodyJsonPath = "MerchantService/V1/workflow/lead/CreatePLABFLTopupCIRMinimalJourney.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body, requestBodyJsonPath);
            if (responseObject.getStatusCode() == 201) {
                LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                leadId = responseObject.jsonPath().getString("leadId");
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "5");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"), "192");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"), "1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"), "PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"), "RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"), "true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"), "NOT_KNOWN");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"), "true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"), "600024");
            } else if (responseObject.getStatusCode() == 417) {
                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body, requestBodyJsonPath);
                LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                leadId = responseObject.jsonPath().getString("leadId");
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "5");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"), "192");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"), "1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"), "PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"), "RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"), "true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"), "false");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"), "NOT_KNOWN");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"), "true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"), "600024");

            }
        }



        @Test(description="Verify the PL v3 ABFL Topup lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3_HERO_TOPUP_CreateLead")
        @Owner(emailId = "<EMAIL>")
        public void TC004_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<35;i++)
            {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {
                    break;
                }
            }
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_IN_PROGRESS.getStage());
        }



        @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC05_SecondBRECallback() {

            for(int i =0;i<=5;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {
                    break;}
            }

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK, custId, LendingConstants.JWT_RISK_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "BRE2_SUCCESS");
            body.put("BASE_ID", baseID);
            body.put("LOAN_OFFER_ID", loanOfferID);
            body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
            body.put("SKIP_EMANDATE_ELIGIBLE", "false");
            body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

            requestBodyJsonPath = "MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
            responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath, "BRE2");
            LOGGER.info("BRE 2 Success with callback");



        }

        @Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC05_SecondBRECallback",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC06_FetchLeadVerifyAdditionalData() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
            }
        }


        @Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "TC06_FetchLeadVerifyAdditionalData",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC07_AdditionalDataCapture() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("leadId", leadId);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
                body.put("BUSINESS_EMAIL", "<EMAIL>");
                body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
                body.put("FATHER_NAME", "RAM");
                requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";

                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            }

        }




        @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC07_AdditionalDataCapture",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC08_BRE3Success() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            for(int i =0;i<10;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                    break;
                }
            }
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

        }




        @Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC08_BRE3Success", groups = {
                "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void TC09_EmandateCallback() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("status", "EMANDATE_SUCCESS");

            Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

        }

        @Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "TC09_EmandateCallback",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC010_FetchLeadPostEmandate() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
        }

        @Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC010_FetchLeadPostEmandate", groups = {
                "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void TC011_EmandateCallback() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS,
                    custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("status", "EMANDATE_SUCCESS");

            Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

        }








    }
