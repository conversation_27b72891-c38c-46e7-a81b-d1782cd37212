package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PLv3_ABFL_RTO_FLOW extends BaseMethod {
        private static final Logger LOGGER = LogManager.getLogger(OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL.PLv3_ABFL_RTO_FLOW.class);
        String solution = "personal_loan_v3";
        String solutionTypeLevel2 = "ABFL";
        String entityType = "INDIVIDUAL";
        String channel = "PAYTM_APP";
        String leadId = "";
        String sessionToken = "";
        String consumerNumber = "6812111032";
        String consumerPassword = "paytm@123";
        String custId = "1700315975";
        String token = "";
        String requestBodyJsonPath = "";
        String userIPAddress = "";
        String staticTncAcceptanceTimeStamp = "";
        String Email = "<EMAIL>";
        String DOB = "1973-01-01";
        String PAN = "**********";
        String occupation = "";
        String income = "";
        String firstName = "BHAIRAVI";
        String lastName = "LATASREE";
        String bureauRequest = "";
        String bureauResponse = "";
        String bureauCreditState = "";
        String breCreditScore = "";
        String loanTenure = "";
        String loanAmount = "";
        String loanEquatedMonthlyInstallment = "";
        String loanRateofInterest = "";
        String loanInterestAmount = "";
        String loanProcessingFeeRate = "";
        String loanDisbursalAmount = "";
        String stampDutyCharges = "";
        String brokerPeriodInterest = "";
        String uuidCustomerPhoto = "";
        String uuidSelfie = "";
        String loanOfferID = "7a2a833a-8dd5-43f7-a36d-b7e3ef17b93e";
        String baseID = "RESET_OFFER_efc047cd-84cc-4f14-87e3-7262400772b4";
        String ckycName = "";
        String code = "";
        String tncName = "";
        String url = "";
        String uniqueIdentifier = "";
        String md5 = "";
        String codeSanctionLetter = "";
        String tncNameSanctionLetter = "";
        String urlSanctionLetter = "";
        String uniqueIdentifierSanctionLetter = "";
        String md5SanctionLetter = "";
        String bankName = "PAYTM BANK";
        String bankAccountNumber = "************";
        String ifsc = "PYTM0123456";
        String bankAccountHolderName = "Bene Customer Name";
        private String randomBankAccountNumber;
        boolean basic_prefilling_flag = true;


        Response responseObject = null;
        LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
        Utilities utility = new Utilities();


        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }


        @Test(description = "Verify if there is any existing Personal Loan Migration ABFL Lead", groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC001_PLv3ABFL_fetchlLead() {
            responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken, custId);

            if (responseObject.jsonPath().getInt("statusCode") == 200) {
                LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId = responseObject.jsonPath().getString("leadId");
            }

            if (responseObject.jsonPath().getInt("statusCode") == 404) {
                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

            }

        }


        @Test(description = "Verify reseting existing Personal Loan Migration ABFL lead", groups = {"Regression"}, dependsOnMethods = {"TC001_PLv3ABFL_fetchlLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC002_PLv3ABFL_DeleteExistingLead() {

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("custId", custId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_QA, custId, LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);

            lendingBaseClassObject.resetLendingLeads(queryParams, headers);

        }

        @Test(description = "Create Lead for Personal Loan Migeration ABFL", groups = {"Regression"}, dependsOnMethods = {"TC002_PLv3ABFL_DeleteExistingLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC003_PLv3ABFL_CreateLead() {

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map <String,String> queryParams= new HashMap<String,String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
            queryParams.put("entityType",entityType);
            queryParams.put("channel", channel);

            //Headers
            Map <String,String> header = new HashMap<String,String>();
            header = LendingBaseAPI.setHeadersReceivedFromFE();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);
            header.put("ipAddress", LendingConstants.IP_ADDRESS);

            //Body
            Map <String,Object> body = new HashMap<String,Object>();
            body.put("workflowOperation", "CREATE_LEAD");
            body.put("mobile", consumerNumber);

            //solutionAdditionalInfo
            body.put("PRODUCT_ID", "78");
            body.put("PRODUCT_TYPE", "PL");
            body.put("FLOW_TYPE", "RISK");
            body.put("LOAN_OFFER_ID", loanOfferID);
            body.put("PRODUCT_VERSION", "1");
            body.put("BASE_ID", baseID);
            body.put("LENDER_ID", "6");
            body.put("WHITELISTING_SOURCE", "RISK");
            body.put("IS_EMANDATE_ELIGIBLE", "true");
            body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
            body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
            body.put("IS_FATHER_NAME_REQUIRED", "true");
            body.put("MARITAL_STATUS", "NOT_KNOWN");
            body.put("IS_BRE3_REQUIRED", "true");
            body.put("PINCODE", "600024");
            body.put("BUREAU_PRIORITY","EXPERIAN");
            body.put("IS_RTO_FLOW","TRUE");

            //leadAdditionalInfo
            body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "TRUE");
            body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
            if(basic_prefilling_flag) {
                body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "TRUE");
            }else{
                body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "FALSE");
            }
            requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePL_LPODMergeLead.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
            if (responseObject.getStatusCode() == 201) {
                LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                leadId = responseObject.jsonPath().getString("leadId");
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"6");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"78");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_RTO_FLOW"),"TRUE");
            }


        }

        @Test(description="Verify the PL v3 ABFL lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3ABFL_CreateLead")
        @Owner(emailId = "<EMAIL>")
        public void TC004_FetchLeadAllData() throws JSONException
        {

            for(int i=0;i<15;i++)
            {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage()))
                    break;
            }

            if(basic_prefilling_flag) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage())) {
                    LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

                    LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
                }
            }


        }

        @Test(description="Add Basic & Occupation details",groups = {"Regression"},dependsOnMethods = "TC004_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC005_BD_OD_Details(){
            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map <String,String> queryParams= new HashMap<String,String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
            queryParams.put("entityType",entityType);
            queryParams.put("channel", channel);
            queryParams.put("leadId",leadId);

            //Headers
            Map <String,String> header = new HashMap<String,String>();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);

            //Body
            Map <String,Object> body = new HashMap<String,Object>();
            body.put("workflowOperation","OCCUPATION_DETAILS");
            body.put("OCCUPATION","Salaried");
            body.put("ANNUAL_INCOME","200000");
            body.put("EMPLOYER_ID","E66767");
            body.put("EMPLOYER_NAME","Paytm");
            body.put("BUSINESS_NAME","Shivangi QR service");
            body.put("PINCODE","600024");
            body.put("LOAN_PURPOSE","Personal use");
            body.put("LOAN_PURPOSE_KEY","2");
            body.put("GENDER","Female");
            body.put("STATIC_TNC_SETNAME","personalloan_oclconsent_hero");
            body.put("LENDER_STATIC_TNC_SETNAME","pl_hero_ckyc_consent");
            body.put("F_NAME", "TOUCH");
            body.put("M_NAME", "WOOD");
            body.put("L_NAME", "LIMITED");
            body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
            body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateBDODDetails.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_LINKING_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"999");


        }



        @Test(description="Verify the PL v3 ABFL lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC005_BD_OD_Details")
        @Owner(emailId = "<EMAIL>")
        public void TC006_FetchLeadAllDataPostOccupationDetails() throws JSONException
        {

            for(int i=0;i<15;i++)
            {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().
                        getString("stage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage()))
                    break;
            }

            if(basic_prefilling_flag) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS_REQUIRED.getStage())) {
                    LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

                    LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                }
            }


        }

        @Test(description = "Update data in SAI Table", dependsOnMethods = "TC006_FetchLeadAllDataPostOccupationDetails", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC007_LeadDataUpdateForKYC_InSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("leadId", leadId);
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired","false");
            body.put("PAN",LendingConstants.OA_KYC_PAN);
            body.put("NSDL_NAME",LendingConstants.OA_NSDL_NAME);
            body.put("DOB",LendingConstants.OA_DOB);
            body.put("GENDER","MALE");

            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCMockData.json";

            responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());

        }


        @Test(description = "Verify PL v3 ABFL lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC007_LeadDataUpdateForKYC_InSAI")
        @Owner(emailId = "<EMAIL>")
        public void TC008_PLv3ABFL_LoanOfferAccept() {
            Map<String,String> queryParams=new HashMap<String,String>();

            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID,
                    custId,LendingConstants.LMS_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation", "OFFER_ACCEPTED");

            //solutionAdditionalInfo
            body.put("LOAN_TENURE", "12");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "70000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Seventy Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "6824");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Six Thousand Eight Hundred twenty four");
            body.put("LOAN_RATE_OF_INTEREST", "30");
            body.put("LOAN_INTEREST_AMOUNT", "990.70");
            body.put("LOAN_PROCESSING_FEE", "3675");
            body.put("PROCESSING_FEE_RATE", "5.25");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "4336");
            body.put("LOAN_DISBURSAL_AMOUNT", "65348");
            body.put("STAMP_DUTY_CHARGES", "200");
            body.put("BROKEN_PERIOD_INTEREST", "116");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3HEROLoanOfferAccepted.json";

            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
                loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
                loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
                loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
                loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
                loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
                loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
                stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
                brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
            }
        }



        @Test(description = "Verify the PLv3 lead data after Loan offer accept",groups = {"Regression"},dependsOnMethods = "TC008_PLv3ABFL_LoanOfferAccept")
        @Owner(emailId = "<EMAIL>")
        public void TC009_PLv3ABFL_FetchDataPostLoanOfferAccept() throws JSONException
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);

                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest);
            }
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
        }



        @Test(description = "Verify the ABFL lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC009_PLv3ABFL_FetchDataPostLoanOfferAccept")
        @Owner(emailId = "<EMAIL>")
        public void TC010_PLv3ABFL_UploadSelfie() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken,"selfie",
                    "RohanOfflineAadhaar.jpg");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");
        }

        @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC010_PLv3ABFL_UploadSelfie", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC011_PLv3ABFL_VerifyUploadedSelfie() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


            }

        }

        @Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC011_PLv3ABFL_VerifyUploadedSelfie")
        @Owner(emailId = "<EMAIL>")
        public void TC012_InitiateKYC_UsingSearchByPan() {
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("shareCode","1234");


            requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/OfflineAadharRequest.json";

            responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }

        @Test(description = "Verify lead details after KYC",groups={"Regression"},dependsOnMethods = "TC012_InitiateKYC_UsingSearchByPan")
        @Owner(emailId = "<EMAIL>")
        public void TC013_FetchDataKYCInitiate() throws JSONException
        {

            for(int i =0;i<=25;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOCATION_REQUIRED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOCATION_REQUIRED.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOCATION_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"841");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOCATION_REQUIRED.getStage());


        }



        @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC013_FetchDataKYCInitiate",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC014_PLv3ABFL_LocationCaptured() {
            for(int i =0;i<=55;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.LOCATION_REQUIRED.getStage())) {
                    break;
                }
            }
            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);

            Map<String,String> headers=new HashMap<String,String>();
            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            headers = LendingBaseAPI.setHeadersReceivedFromFE();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation","LOCATION_CAPTURED");
            requestBodyJsonPath="MerchantService/V1/workflow/lead/LENDER_KYC_DOC_SYNC_SUCCESS_Callback.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        }


        @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC014_PLv3ABFL_LocationCaptured",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC015_SecondBRECallback() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
            {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("leadId", leadId);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,
                        LendingConstants.JWT_RISK_KEY);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "BRE2_SUCCESS");
                body.put("BASE_ID", baseID);
                body.put("LOAN_OFFER_ID", loanOfferID);
                body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
                body.put("SKIP_EMANDATE_ELIGIBLE", "false");
                body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

                requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
                responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body,requestBodyJsonPath,"BRE2");
                LOGGER.info("BRE 2 Success with callback");

            } else {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
                LOGGER.info("BRE 2 Success without callback");
            }

        }

        @Test(description = "Verify PL v3 Lead After BRE2 success Callback", dependsOnMethods = "TC015_SecondBRECallback", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC016_FetchDataAfterBRE2Success() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_COMPLETE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"388");

        }


        @Test(description = "Verify PL v3 Lead If Additional Data is required", dependsOnMethods = "TC016_FetchDataAfterBRE2Success",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC017_AdditionalIsRequiredorNot() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("leadId", leadId);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "IS_ADDITIONAL_DATA_REQUIRED");
            body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataRequired.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        }



        @Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC017_AdditionalIsRequiredorNot",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC018_PLv3ABFL_FetchLeadVerifyAdditionalData() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
            }else {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
            }
        }


        @Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "TC018_PLv3ABFL_FetchLeadVerifyAdditionalData",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC019_PLv3ABFL_AdditionalDataCapture() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("leadId", leadId);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                        custId,LendingConstants.LENDING_BFF_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
                body.put("BUSINESS_EMAIL", "<EMAIL>");
                body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
                body.put("FATHER_NAME", "RAM");
                requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";

                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            }

        }


        @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC019_PLv3ABFL_AdditionalDataCapture",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC019_PLv3ABFL_BRE3Success() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            for(int i =0;i<10;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                        LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                    break;
                }
            }
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

        }


        @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC019_PLv3ABFL_BRE3Success", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC020_PLv3ABFL_UpdateKYCNameInSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS,
                    custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired","false");
            body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
            body.put("PAN", "**********");

            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/UpdateKYCNameForBankRequest.json";
            responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BRE3_SUCCESS.getStage());
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            ckycName=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME");
        }


        @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC020_PLv3ABFL_UpdateKYCNameInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC021_PLv3ABFL_FetchLeadUpdateCKYCinSAI() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);

        }


        @Test(description = "Verify PL v3 Lead the bank details", dependsOnMethods = "TC021_PLv3ABFL_FetchLeadUpdateCKYCinSAI", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC022_PLv3ABFL_SaveBankDetails() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);
            headers.put("Content-Type", "application/json");

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("bankName", bankName);
            body.put("bankAccountNumber", bankAccountNumber);
            body.put("ifsc", ifsc);
            body.put("bankAccountHolderName", bankAccountHolderName);
            body.put("EMANDATE_TYPE", "Internet Banking");

            responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

            if (responseObject.getStatusCode() == 200) {

                Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
            }

            else {

                for (int i = 1; i < 4; i++) {
                    LOGGER.info("Again hitting with same data: retry-count: " + i);
                    responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);

                    if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
                    {
                        break;
                    }

                }

            }

            responseObject = lendingBaseClassObject.fetchExistingLeadData(LendingConstants.PL_V3_SOLUTION,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE ,LendingConstants.PAYTM_APP_CHANNEL ,
                    LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL, sessionToken);
        }



}
