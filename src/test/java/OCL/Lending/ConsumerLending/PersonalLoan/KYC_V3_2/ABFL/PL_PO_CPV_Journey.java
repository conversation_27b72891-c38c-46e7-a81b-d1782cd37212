package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.ABFL;

import OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.HERO.PLHERO_Compliance_CPV;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PL_PO_CPV_Journey extends BaseMethod {

        private static final Logger LOGGER = LogManager.getLogger(PL_PO_CPV_Journey.class);

        Response responseObject= null;
        LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
        Utilities utility=new Utilities();

        String solution="pl_po_cpv";
        String entityType="INDIVIDUAL";
        String channel="PAYTM_APP";
        String leadId = "";
        String sessionToken = "";
        String consumerNumber="6812111032";
        String consumerPassword="paytm@123";
        String custId="1700315975";
        String token="";
        String requestBodyJsonPath="";


        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }



        @Test(description="Verify if there is any existing Personal Loan Migration HERO Lead",groups= {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC001_PLPO_fetchlLead()
        {
            responseObject= lendingBaseClassObject.fetchPLPOLeadDetails("", LendingConstants.PL_PO_CPV,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL,
                    LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

            }

        }


        @Test(description="Verify reseting existing Personal Loan Migration HERO lead",groups={"Regression"},dependsOnMethods = {"TC001_PLPO_fetchlLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC002_PLPO_DeleteExistingLead() {

            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams.put("leadId",leadId);
            queryParams.put("solution",LendingConstants.PL_PO_CPV);
            queryParams.put("custId", custId);

            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA,
                    custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);

            lendingBaseClassObject.resetLendingLeads(queryParams, headers);

        }

        @Test(description="Create Lead for Personal Loan Migeration HERO",groups={"Regression"},dependsOnMethods = {"TC002_PLPO_DeleteExistingLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC003_PLPO_CreateLead() {

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF,
                    custId,LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map <String,String> queryParams= new HashMap<String,String>();
            queryParams.put("solution", LendingConstants.PL_PO_CPV);
            queryParams.put("entityType",entityType);
            queryParams.put("channel", channel);

            //Headers
            Map <String,String> header = new HashMap<String,String>();
            header = LendingBaseAPI.setHeadersReceivedFromFE();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);

            //Body
            Map <String,Object> body = new HashMap<String,Object>();
            body.put("workflowOperation", "CREATE_LEAD");
            body.put("mobile", consumerNumber);

            //solutionAdditionalInfo
            body.put("PRODUCT_ID", "78");
            body.put("PARENT_LEAD_ID", "26507e5a-ecca-45a4-8b03-d9a11f349ab2");
            body.put("CPV_TYPE", "Compliance");
            body.put("LENDER_ID", "6");
            body.put("NSDL_NAME", "PL_PO_Lead_3");
            body.put("PRODUCT_TYPE", "PL");
            body.put("FLOW_TYPE", "RISK");
            body.put("addressType", "RESIDENTIAL");
            body.put("addressSubType", "CURRENT");
            body.put("line1", "211 Baker's Street");
            body.put("line2", "Opposite Joy street");
            body.put("line3", "NA");
            body.put("pincode", "110049");
            body.put("city","EAST DELHI");
            body.put("state","DELHI");

            //leadAdditionalInfo
            requestBodyJsonPath="MerchantService/V1/workflow/lead/PL_CPV/CreatePL_PO_CPV_Lead.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
            if (responseObject.getStatusCode() == 201) {
                LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                leadId = responseObject.jsonPath().getString("leadId");
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.CURRENT_ADDRESS_UPDATE_IN_PROGRESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"6");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"78");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CPV_TYPE"),"Compliance");

            }
        }

        @Test(description="Verify the PL v3 HERO lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLPO_CreateLead")
        @Owner(emailId = "<EMAIL>")
        public void TC004_PLPO_FetchLeadAllData() throws JSONException
        {
            for(int i=0;i<15;i++)
            {
                responseObject= lendingBaseClassObject.fetchPLPOLeadDetails(leadId,LendingConstants.PL_PO_CPV,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,
                        LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().
                        getString("stage").equals(LendingLeadStages.PO_CURRENT_ADDRESS_UPDATED.getStage()))
                    break;

            }

        }


    @Test(description="Verify updating CURRENT_ADDRESS_OVD_Documents",groups = {"Regression"},dependsOnMethods = "TC004_PLPO_FetchLeadAllData")
    @Owner(emailId = "<EMAIL>")
    public void TC005_PLPO_CURRENT_ADDRESS_OVD_UPLOADED() throws JSONException{

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_PO_CPV,
                LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID,
                custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CURRENT_ADDRESS_OVD_UPLOADED");
        body.put("LOAN_USER_CPV_STATIC_TNC_SETNAME", "cpv_visit_consent_hfcl");
        body.put("docType", "loanUserAddresssProofOVD");
        body.put("docProvided", "loanUserAadhaarPage1");
        body.put("docValue", "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");

        requestBodyJsonPath = "MerchantService/V1/workflow/lead/PL_CPV/CURRENT_ADDRESS_OVD_UPLOADED.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);

        if(responseObject.getStatusCode()==400){
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);
            if(responseObject.getStatusCode()==400){
                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, requestBodyJsonPath);
            }
        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.CURRENT_ADDRESS_OVD_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1023");
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.PO_CURRENT_ADDRESS_OVD_UPLOADED.getStage());


    }

    @Test(description="Verify the PL v3 HERO lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC005_PLPO_CURRENT_ADDRESS_OVD_UPLOADED")
    @Owner(emailId = "<EMAIL>")
        public void TC006_PLPO_FetchLeadAllData() throws JSONException
        {
            for(int i=0;i<15;i++)
            {
                responseObject= lendingBaseClassObject.fetchPLPOLeadDetails(leadId,LendingConstants.PL_PO_CPV,
                        LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,
                        LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().
                        getString("stage").equals(LendingLeadStages.CPV_INITIATED.getStage())){
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.CPV_INITIATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1025");
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.PO_APPLICATION_UNDER_REVIEW.getStage());
                    break;

                }

            }
        }

        @Test(description="Verify the PL PO CPV Data Captured callback",groups = {"Regression"},dependsOnMethods = "TC006_PLPO_FetchLeadAllData")
        @Owner(emailId = "<EMAIL>")
        public void TC007_PLPO_CPV_DATA_CAPTURE() throws JSONException {

            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_PO_CPV);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,
                    LendingConstants.JWT_RISK, custId, LendingConstants.JWT_RISK_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            //solutionAdditionalInfo
            body.put("workflowOperation", "CPV_DATA_CAPTURED");
            body.put("CPV_AGENT_LATITUDE", "23.046530");
            body.put("CPV_AGENT_LONGITUDE", "23.046530");
            body.put("CPV_AGENT_CUST_ID", "1701676800");
            body.put("CPV_DECISION", "APPROVED");
            body.put("CPV_TIMESTAMP", "1696484093000");
            body.put("CPV_LEAD_ID", "02be720b-8188-3d58-a6c4-b653e585f963");
            body.put("CPV_DATA_CAPTURED", "TRUE");
            body.put("SKIP_EMANDATE_ELIGIBLE", "false");

            //ubmFeedbackInfo
            body.put("CPV Decision", "APPROVED");
            body.put("Name of the person met", "Kalim ahamad");
            body.put("Is the applicant's name and stay confirmed?", "Yes");
            body.put("Is applicant address Incomplete?", "Yes");
            body.put("Please enter complete address", "Latlong is wrong but address is correct");
            body.put("Whom did you met?", "APPROVED");
            body.put("Is applicant Address Traceable ?", "Applicant");
            body.put("The visit is done at", "Office/Shop");
            body.put("Is a visit to the address done in addition to calling?", "No");

            //Documents
            body.put("docType","cpvLocalityPhoto");
            body.put("docProvided","homeFrontPhoto");
            body.put("pageNumber","1");
            body.put("docUUId","ed7dee2f-2655-4276-b503-9c847ecc3b20");
            body.put("dmsChannel","LENDER");
            body.put("status","0");


            requestBodyJsonPath = "MerchantService/V2/lending/dataUpdate/PL_CPV_DATA_CAPTURE_CALLBACK.json";
            responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath);
            LOGGER.info("CPV data Captured with callback");


        }

    @Test(description="Verify the PL v3 HERO lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC007_PLPO_CPV_DATA_CAPTURE")
    @Owner(emailId = "<EMAIL>")
    public void TC008_PLPO_FetchLeadAllData() throws JSONException
    {
        for(int i=0;i<15;i++)
        {
            responseObject= lendingBaseClassObject.fetchPLPOLeadDetails(leadId,LendingConstants.PL_PO_CPV,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,
                    LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().
                    getString("stage").equals(LendingLeadStages.CPV_DATA_CAPTURED.getStage())){
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.CPV_QC_ACTION_PENDING.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1030");
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.PO_APPLICATION_UNDER_REVIEW.getStage());
                break;

            }

        }
    }

    @Test(description="Verify the PL PO lead is in CPV_QC_ACTION_PENDING",groups = {"Regression"},dependsOnMethods = "TC008_PLPO_FetchLeadAllData")
    @Owner(emailId = "<EMAIL>")
    public void TC009_PLPO_CPV_QC() throws JSONException
    {
        for(int i=0;i<15;i++)
        {
            responseObject= lendingBaseClassObject.fetchPLPOLeadDetails(leadId,LendingConstants.PL_PO_CPV,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,
                    LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().
                    getString("stage").equals(LendingLeadStages.CPV_DATA_CAPTURED.getStage())){
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.CPV_DATA_CAPTURED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1027");
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.PO_APPLICATION_UNDER_REVIEW.getStage());
                break;

            }

        }
    }








}
