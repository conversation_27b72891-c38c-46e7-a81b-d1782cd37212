        package OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate;

        import Services.LendingService.LendingBaseAPI;
        import Services.LendingService.LendingConstants;
        import Services.LendingService.LendingLeadStages;
        import Services.Utilities.Utilities;
        import com.goldengate.common.BaseMethod;
        import com.paytm.apitools.util.annotations.Owner;
        import io.restassured.response.Response;
       // import org.apache.log4j.Logger;
        import org.apache.logging.log4j.LogManager;
        import org.apache.logging.log4j.Logger;
        import org.testng.Assert;
        import org.testng.annotations.BeforeClass;
        import org.testng.annotations.Test;

        import java.util.HashMap;
        import java.util.Map;

    public class TestSetMandateOnboardingFlowNewBankAPI extends BaseMethod {

        private static final Logger LOGGER = LogManager.getLogger(OCL.Lending.ConsumerLending.PersonalLoan.KYC_V3_2.SetMandate.TestSetMandateOnboardingFlowNewBankAPI.class);
        Response responseObject= null;
        LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
        Utilities utility=new Utilities();
        String sessionToken = "";
        String leadId = "";
        String custId="**********";
        String token="";
        String consumerNumber="**********";
        String consumerPassword="paytm@123";
        String entityType="INDIVIDUAL";
        String channel="PAYTM_APP";
        String requestBodyJsonPath="";
        String parentLead="910c2f6b-91e3-4b28-bbd9-4dc0fdaa3e95";
        String bankName="PAYTM BANK";
        String bankAccountNumber="************";
        String ifsc="PYTM0123456";
        String bankAccountHolderName="Shivangi Goswami";
        String ckycName="";
        private String randomBankAccountNumber;

        @BeforeClass()
        public void intitializeInputData() {

            LOGGER.info(" Before Suite Method for Consumer Login ");
            sessionToken = ApplicantToken(consumerNumber, consumerPassword);
            LOGGER.info("Applicant Token for Lending : " + sessionToken);

        }

        @Test(description="Verify if there is any existing Set Mandate Onboarding lead",groups= {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC001_PLSetMandate_Onboarding_fetchlLead()
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_SET_MANDATE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200)
            {
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                leadId=responseObject.jsonPath().getString("leadId");
            }

            if(responseObject.jsonPath().getInt("statusCode")==404)
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

            }

        }


        @Test(description="Verify reseting existing Set Mandate Onboarding lead",groups={"Regression"},dependsOnMethods = {"TC001_PLSetMandate_Onboarding_fetchlLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC002_PLSetMandate_Onboarding_DeleteExistingLead() {

            Map<String,String> queryParams=new HashMap<String,String>();
            queryParams.put("custId", custId);
            queryParams.put("solution", LendingConstants.PL_SET_MANDATE);
            queryParams.put("leadId",leadId);

            token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);

            responseObject = lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_SET_MANDATE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

            if (responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer.")){
                lendingBaseClassObject.resetLendingLeads(queryParams, headers);
            }
        }


        @Test(description="Create Lead for Set Mandate Onboarding lead",groups={"Regression"},dependsOnMethods = {"TC002_PLSetMandate_Onboarding_DeleteExistingLead"})
        @Owner(emailId = "<EMAIL>")
        public void TC003_PLSetMandate_Onboarding_CreateLead() {

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
            //Parameters
            Map <String,String> queryParams= new HashMap<String,String>();
            queryParams.put("solution", LendingConstants.PL_SET_MANDATE);
            queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType",entityType);
            queryParams.put("channel", channel);

            //Headers
            Map <String,String> header = new HashMap<String,String>();
            header = LendingBaseAPI.setHeadersReceivedFromFE();
            header.put("Authorization", token);
            header.put("Content-Type", "application/json");
            header.put("custid", custId);
            header.put("ipAddress", LendingConstants.IP_ADDRESS);

            //Body
            Map <String,Object> body = new HashMap<String,Object>();
            body.put("workflowOperation", "CREATE_LEAD");
            body.put("mobile", consumerNumber);

            //solutionAdditionalInfo
            body.put("PARENT_LEAD_ID", parentLead);
            body.put("IS_ONBOARDING_JOURNEY", "TRUE");
            body.put("LUP_ID", "LUP-PTM-18848");

            body.put("CKYC_NAME", "Anmol jain");
            body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
            body.put("PARENT_LENDER_ID", "5");
            body.put("PARENT_PRODUCT_ID", "25");
            body.put("PARENT_PRODUCT_TYPE", "PL");
            body.put("PARENT_PRODUCT_VERSION", "1");
            body.put("FLOW_TYPE", "RISK");
            body.put("LOAN_TENURE", "6");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "9128.00");
            body.put("LOAN_AMOUNT_IN_NUMBER", "50000");




            requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLSetMandateOnboardingLeadRequest.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
            if (responseObject.getStatusCode() == 201) {
                LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                leadId = responseObject.jsonPath().getString("leadId");
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
                Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_ID"),"25");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"25");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_TYPE"),"PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_ONBOARDING_JOURNEY"),"TRUE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_PRODUCT_VERSION"),"1");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_LENDER_ID"),"5");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
            }

        }

        @Test(description = "Verify for Set Mandate Post Onboarding CKYC name update in SAI", dependsOnMethods = "TC003_PLSetMandate_Onboarding_CreateLead", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC004_PLSetMandate_Onboarding_UpdateKYCNameInSAI() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_SET_MANDATE);
            queryParams.put("leadId", leadId);
            //queryParams.put("solutionTypeLevel2", "ABFL");
            queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired", "false");
            //body.put("status", "SUCCESS");
            body.put("CKYC_NAME", LendingConstants.BANK_NAME_STAGING3);
            body.put("PAN", "**********");


            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
            responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            //Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_SET_MANDATE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            ckycName=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME");
        }


        @Test(description = "Verify for  Set Mandate Post Onboarding CKYC name update in SAI", dependsOnMethods = "TC004_PLSetMandate_Onboarding_UpdateKYCNameInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC005_PLSetMandate_Onboarding_FetchLeadUpdateCKYCinSAI() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_SET_MANDATE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);

        }

        @Test(description = "Enter the bank details", dependsOnMethods = "TC005_PLSetMandate_Onboarding_FetchLeadUpdateCKYCinSAI", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void TC006_PLSetMandate_Onboarding_SaveBankDetails() {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_SET_MANDATE);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("custId", custId);

            randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "VERIFY_BANK_DETAILS");
            body.put("EMANDATE_TYPE", "Internet Banking");
            body.put("bankName", "PAYTM BANK");
            body.put("bankAccountNumber",randomBankAccountNumber);
            body.put("ifsc", "PYTM0123456");
            body.put("bankAccountHolderName", "testNameMatch");

            Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

            if (responseObject.getStatusCode() == 200) {

                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");

            }

            else {

                for (int i = 1; i < 6; i++) {
                    LOGGER.info("Again hitting with same data: retry-count: " + i);
                    responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                    if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
                        break;
                }

            }


            Assert.assertEquals(responseObject.getStatusCode(),200);


        }



        @Test(description = "Verify Set Mandate Post Onboarding emandate Callback", dependsOnMethods = "TC006_PLSetMandate_Onboarding_SaveBankDetails", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void TC007_PLSetMandate_Onboarding_Emandatecallback() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_SET_MANDATE);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LENDING, custId,LendingConstants.JWT_LENDING_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("status", "EMANDATE_SUCCESS");
            body.put("UMRN", "123459");
            body.put("MANDATE_EXPIRY_DATE", "2022-08-16 19:25:44.435");
            body.put("MANDATE_MODE", "EM");

            requestBodyJsonPath="MerchantService/v5/callback/EmandateCallbackForPLSetMandate.json";
            responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body,requestBodyJsonPath);
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        }

        @Test(description = "Verify for  Set Mandate Post Onboarding", dependsOnMethods = "TC007_PLSetMandate_Onboarding_Emandatecallback",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void TC008_PLSetMandate_Onboarding_FetchLeadUpdate() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_SET_MANDATE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);

        }




    }
