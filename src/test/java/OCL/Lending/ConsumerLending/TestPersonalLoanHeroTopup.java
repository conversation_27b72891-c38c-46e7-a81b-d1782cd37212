		package OCL.Lending.ConsumerLending;

        import Services.LendingService.LendingBaseAPI;
        import Services.LendingService.LendingConstants;
        import Services.LendingService.LendingLeadStages;
        import Services.Utilities.Utilities;
        import com.goldengate.common.BaseMethod;
        import com.paytm.apitools.util.annotations.Owner;
        import io.restassured.response.Response;
        //import org.apache.log4j.Logger;
		import org.apache.logging.log4j.LogManager;
		import org.apache.logging.log4j.Logger;
        import org.json.JSONException;
        import org.testng.Assert;
        import org.testng.annotations.BeforeClass;
        import org.testng.annotations.Test;

        import java.util.HashMap;
        import java.util.Map;
		
		public class TestPersonalLoanHeroTopup extends BaseMethod {
		
		private static final Logger LOGGER = LogManager.getLogger(TestPersonalLoanHeroTopup.class);
		
		String entityType="INDIVIDUAL";
		String channel="PAYTM_APP";
		String leadId = "";
		String sessionToken = "";
		String consumerNumber="9958479799";
		String consumerPassword="paytm@123";
		String custId="1002336751";
		String token="";
		String requestBodyJsonPath="";
		String userIPAddress="";
		String staticTncAcceptanceTimeStamp="";
		String Email="";
		String DOB="";
		String PAN="";
		String occupation="";
		String income="";
		String firstName="BHAIRAVI";
		String lastName="LATASREE";
		String bureauRequest="";
		String bureauResponse="";
		String bureauCreditState="";
		String breCreditScore="";
		String loanTenure="";
		String loanAmount="";
		String loanEquatedMonthlyInstallment="";
		String loanRateofInterest="";
		String loanInterestAmount="";
		String loanProcessingFeeRate="";
		String loanDisbursalAmount="";
		String stampDutyCharges="";
		String brokerPeriodInterest="";
		String uuidCustomerPhoto = "";
		String uuidSelfie="";
		String loanOfferID="4d7a42d3-8b65-4ff3-9ada-f72b769d7ae8";
		String baseID="8ab5cbac-a243-4f0a-927b-b6c3debdf490";
		String ckycName="";
		String code = "";
		String tncName = "";
		String url = "";
		String uniqueIdentifier ="";
		String md5 ="";
		String codeSanctionLetter = "";
		String tncNameSanctionLetter = "";
		String urlSanctionLetter = "";
		String uniqueIdentifierSanctionLetter = "";
		String md5SanctionLetter = "";
		String bankName="PAYTM BANK";
		String bankAccountNumber="************";
		String ifsc="PYTM0123456";
		String bankAccountHolderName="Shivangi Goswami";
		String stringify_json="{\\\"baseId\\\":\\\"8ab5cbac-a243-4f0a-927b-b6c3debdf490\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"isBre2Required\\\":false,\\\"field_investigation_needed\\\":false,\\\"loanDownGradable\\\":false,\\\"isEmandateEligible\\\":1,\\\"offerEndDate\\\":\\\"Thu Jul 07 00:00:00 IST 2022\\\",\\\"lastFetchDate\\\":*************,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":165000.0,\\\"maxTenure\\\":18,\\\"minLoanAmount\\\":27000.0,\\\"minTenure\\\":12,\\\"newOfferGenerated\\\":true,\\\"offerStartDate\\\":\\\"Tue Jun 07 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"3c0a9af5-6f42-4d66-923a-4100a4b859ce\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"11001\\\",\\\"productType\\\":\\\"PL\\\",\\\"productVersion\\\":1,\\\"lenderSchemeId\\\":\\\"54103\\\",\\\"riskGrade\\\":\\\"VL\\\",\\\"riskSegment\\\":\\\"VL\\\",\\\"skipMandate\\\":false,\\\"sourceOfWhitelist\\\":\\\"RISK\\\"}";
		Response responseObject= null;
		LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
		Utilities utility=new Utilities();
		
		
		@BeforeClass()
		public void intitializeInputData() {
		
		LOGGER.info(" Before Suite Method for Consumer Login ");
		sessionToken = ApplicantToken(consumerNumber, consumerPassword);
		LOGGER.info("Applicant Token for Lending : " + sessionToken);
		 
		}
		
		
		
		@Test(description="Verify if there is any existing Personal Loan HERO Topup Lead",groups= {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC001_PLv3_HERO_TOPUP_fetchlLead()
		{
		responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		   
		    if(responseObject.jsonPath().getInt("statusCode")==200)
		    {
		    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		   Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		     
		   LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
		     
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		     
		      leadId=responseObject.jsonPath().getString("leadId");
		    }
		     
		    if(responseObject.jsonPath().getInt("statusCode")==404)
		    {
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		    Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
		     
		    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		   
		    }
		
		}
		
		
		@Test(description="Verify reseting existing Personal Loan HERO TOPUP lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3_HERO_TOPUP_fetchlLead"})
		@Owner(emailId = "<EMAIL>")
		public void TC002_PLv3_HERO_TOPUP_DeleteExistingLead() {
		
		Map<String,String> queryParams=new HashMap<String,String>();
		queryParams.put("leadId",leadId);
		 queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
		 queryParams.put("custId", custId);
		 
		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("custId", custId);
		 
		lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		   
		}
		
		
		@Test(description="Create Lead for Personal Loan Migeration ABFL",groups={"Regression"},dependsOnMethods = {"TC002_PLv3_HERO_TOPUP_DeleteExistingLead"})
		@Owner(emailId = "<EMAIL>")
		public void TC003_PLv3_HERO_TOPUP_CreateLead() {
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		//Parameters
		Map <String,String> queryParams= new HashMap<String,String>();
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		queryParams.put("entityType",entityType);
		queryParams.put("channel", channel);
		
		//Headers
		Map <String,String> header = new HashMap<String,String>();
		header = LendingBaseAPI.setHeadersReceivedFromFE();
		      header.put("Authorization", token);
		      header.put("Content-Type", "application/json");
		      header.put("custid", custId);
		      header.put("ipAddress", LendingConstants.IP_ADDRESS);
		
		//Body
		Map <String,Object> body = new HashMap<String,Object>();
		body.put("workflowOperation", "CREATE_LEAD");
		body.put("mobile", consumerNumber);
		
		//solutionAdditionalInfo
		body.put("PRODUCT_ID", "11001");
		body.put("PRODUCT_TYPE", "PL");
		body.put("FLOW_TYPE", "RISK");
		body.put("LOAN_OFFER_ID", loanOfferID);
		body.put("PRODUCT_VERSION", "1");
		body.put("BASE_ID", baseID);
		body.put("LENDER_ID", "5");
		body.put("WHITELISTING_SOURCE", "DATABASE");
		body.put("IS_EMANDATE_ELIGIBLE", "true");
		body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
		body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
		body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
		body.put("IS_FATHER_NAME_REQUIRED", "true");
		body.put("MARITAL_STATUS", "NOT_KNOWN");
		body.put("IS_BRE3_REQUIRED", "true");
		body.put("PINCODE", "600024");
		body.put("LENDER_NAME", "HERO");
		body.put("LOAN_ACCOUNT_NUMBER", "PYTMPH255117054");
		body.put("PARENT_LOAN_ACCOUNT_NUMBER", "PYTMPH255117054");
		body.put("MIGRATION_TYPE", "TOPUP");
		body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");
		
		//leadAdditionalInfo
		//body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "TRUE");
		
		requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLHeroTopupRequest.json";
		
		responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
		if (responseObject.getStatusCode() == 201) {
		LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
		Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
		leadId = responseObject.jsonPath().getString("leadId");
		Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
		       Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
		       Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		       Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"11001");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"DATABASE");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
		}
		 
		}
		
		
		@Test(description="Verify the PL v3 Hero Topup lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3_HERO_TOPUP_CreateLead")
		@Owner(emailId = "<EMAIL>")
		   public void TC004_PLV3HeroTopup_FetchLeadAllData() throws JSONException
		   {
		for(int i=0;i<10;i++)
		 {
		 responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
		 if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
		 break;
		 }
		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		 if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
		 {
		    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		     
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
		     
		    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		       
		    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		      Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"804");
		    }
		    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		   }
		
		
		@Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC004_PLV3HeroTopup_FetchLeadAllData")
		@Owner(emailId = "<EMAIL>")
		public void TC005_PLV3Hero_Topup_UpdateLeadDetailsinSAI() {
		Map<String,String> queryParams=new HashMap<String,String>();
		queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		 
		Map<String,String> headers=new HashMap<String,String>();
		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
		   headers = LendingBaseAPI.setHeadersReceivedFromFE();
		   headers.put("Authorization", token);
		   headers.put("Content-Type", "application/json");
		   headers.put("custId", custId);
		     
		   Map<String,Object> body=new HashMap<String,Object>();
		   body.put("statusMessage","ADDITIONAL_INFO_UPDATE");
		   body.put("status","SUCCESS");
		   body.put("bureau", "CIBIL");
		   body.put("PAN", "**********");
		   body.put("F_NAME",firstName);
		   body.put("L_NAME",lastName);
		   body.put("EMAIL","<EMAIL>");
		   body.put("DOB","1979-10-05");
		   body.put("gender","FEMALE");
		   body.put("pincode","600024");
		   
		   responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
		
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		
		}
		
		
		@Test(description = "Verify the PLv3 HERO TOPUP lead data after Updating details in SAI",groups = {"Regression"},dependsOnMethods = "TC005_PLV3Hero_Topup_UpdateLeadDetailsinSAI")
		@Owner(emailId = "<EMAIL>")
		public void TC06_PLv3HERO_Topup_FetchDataPost_SAI_Update() throws JSONException
		{
		 
		    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		   
		    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
		    {
		    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		   Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		     
		   LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
		     
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		       
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"804");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),firstName);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),lastName);
		    }
		    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		    }
		
		
		@Test(description = "Verify Fetch CIR for PLv3 HERO TOPUP lead",groups = {"Regression"},dependsOnMethods = "TC06_PLv3HERO_Topup_FetchDataPost_SAI_Update")
		@Owner(emailId = "<EMAIL>")
		public void TC07_PLv3HERO_TOPUP_FetchCIR() {
		 Map<String,String> queryParams=new HashMap<String,String>();
		
		 queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		 queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		 
		 Map<String,String> headers=new HashMap<String,String>();
		 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		 headers.put("Authorization", token);
		     headers.put("Content-Type", "application/json");
		     headers.put("longitude",LendingConstants.LONGITUDE);
		 headers.put("latitude",LendingConstants.LATITUDE);
		 headers.put("custId", custId);
		     
		     
		     Map<String,Object> body=new HashMap<String,Object>();
		 
		     responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);
		
		
		 if(responseObject.getStatusCode()==200) {
		 
		LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		       Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		       Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		       Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
		       //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
		       Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");
		
		       bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
		       bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
		       bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
		       breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
		 }
		   }
		
		
		
		@Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC07_PLv3HERO_TOPUP_FetchCIR")
		@Owner(emailId = "<EMAIL>")
		public void TC008_PLv3HERO_Topup_BRE1Callback() throws JSONException
		{
		 
		for(int i =0;i<=8;i++) {
		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		
		    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
		    break;
		    }
		}
		    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))
		    {
		    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
		    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");
		   
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		    for(int i =0;i<15;i++) {
		    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
		    LOGGER.info("BRE 1 passed without callback");  
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
		    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
		    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
		    loanOfferID=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID");
		    break;
		    }
		    }
		   BRE1Callback(LendingConstants.PL_V3_SOLUTION);
		   if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
		    LOGGER.info("BRE 1 passed with callback");  
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
		Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
		Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
		    }
		  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		        }
		}
		
		
		public Response BRE1Callback(String solution) {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", solution);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowOperation", "BRE1_SUCCESS");
		body.put("BRE1_OFFER_DETAILS", ""+stringify_json+"");
		body.put("LENDING_SCHEME_ID", "54103");
		
		       requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/PLHeroTopupCallbackRequest.json";
		       responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
		       
		       return responseObject;
		
		}
		
		
		@Test(description = "Verify the PLv3 lead data after BRE1 Success",groups = {"Regression"},dependsOnMethods = "TC008_PLv3HERO_Topup_BRE1Callback")
		@Owner(emailId = "<EMAIL>")
		public void TC009_PLv3HERO_Topup_FetchDataPostBRE1Success() throws JSONException
		{
		 
		    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		   
		    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
		    {
		    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		   Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		     
		   LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
		     
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		       
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"),bureauRequest);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"),bureauResponse);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"),bureauCreditState);
		       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"),breCreditScore);
		
		    }
		    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
		    }
		
		
		
		@Test(description = "Verify PL v3 Hero Topup lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC009_PLv3HERO_Topup_FetchDataPostBRE1Success")
		@Owner(emailId = "<EMAIL>")
		public void TC010_PLv3HERO_TOPUP_LoanOfferAccept() {
		Map<String,String> queryParams=new HashMap<String,String>();
		
		queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		     
		    Map<String,Object> body=new HashMap<String,Object>();
		    body.put("workflowOperation", "OFFER_ACCEPTED");
		   
		    //solutionAdditionalInfo
		    body.put("LOAN_TENURE", "6");
		    body.put("LOAN_TENURE_UNIT", "MONTH");
		    body.put("LOAN_AMOUNT_IN_NUMBER", "47000");
		    body.put("LOAN_AMOUNT_IN_WORDS", "Fourty Seven Thousand");
		    body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "8485");
		    body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Eight thousand four hundred Eighty five");
		    body.put("LOAN_RATE_OF_INTEREST", "28");
		    body.put("LOAN_INTEREST_AMOUNT", "651.70");
		    body.put("LOAN_PROCESSING_FEE", "2350");
		    body.put("PROCESSING_FEE_RATE", "5");
		    body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "2773");
		    body.put("LOAN_DISBURSAL_AMOUNT", "43333");
		    body.put("STAMP_DUTY_CHARGES", "200");
		    body.put("BROKEN_PERIOD_INTEREST", "694");
		    body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");
		   
		   
		    requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3HeroTopupLoanOfferAccepted.json";
		 
		    responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
		   
		    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
		    {
		    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		   Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
		       loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
		       loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
		       loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
		       loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
		       loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
		       loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
		       loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
		       stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
		       brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
		 }
		    }
		     
		
		
		// @Test(description = "Verify the PLv3 lead data after Loan offer accept",groups = {"Regression"},dependsOnMethods = "TC010_PLv3HERO_TOPUP_LoanOfferAccept")
		// @Owner(emailId = "<EMAIL>")
		// public void TC011_PLv3HERO_Topup_FetchDataPostLoanOfferAccept() throws JSONException
		// {
		//  
		//     responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		//    
		//     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
		//     {
		//     LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		//    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		//      
		//    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		//        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
		//      
		//        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		//        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		//        
		//        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
		//        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
		//        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);
		//
		//        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest);
		//     }
		//     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
		//     }
		
		
		@Test(description = "Verify PL v3 Lead Second BRE should be Inititated", dependsOnMethods = "TC010_PLv3HERO_TOPUP_LoanOfferAccept",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC012_PLv3HERO_TOPUP_SecondBREInitiated() {
		
		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		if(!(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
		equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())))
		    {
		for(int i =0;i<8;i++) {
		
		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		if((responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
		equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))) {
		break;
		}
		}
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		   Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		     
		   LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
		     
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		       
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_IN_PROGRESS.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"387");
		    }
		}
		
		@Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC012_PLv3HERO_TOPUP_SecondBREInitiated",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC013_PLv3HERO_TOPUP_SecondBRECallback() {
		
		responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
		equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
		    {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("leadId", leadId);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,LendingConstants.JWT_RISK_KEY);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowOperation", "BRE2_SUCCESS");
		body.put("BASE_ID", baseID);
		body.put("LOAN_OFFER_ID", loanOfferID);
		body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
		body.put("SKIP_EMANDATE_ELIGIBLE", "false");
		body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");
		 
		requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
		responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body,requestBodyJsonPath,"BRE2");
		LOGGER.info("BRE 2 Success with callback");
		
		    } else {
		    responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
		    LOGGER.info("BRE 2 Success without callback");
		    }
		
		}
		
		
		
		// @Test(description = "Verify PL v3 Lead If Additional Data is required", dependsOnMethods = "TC013_PLv3HERO_TOPUP_SecondBRECallback",groups = {"Regression"})
		// @Owner(emailId = "<EMAIL>")
		// public void TC014_PLv3HERO_TOPUP_AdditionalIsRequiredorNot() {
		// Map<String, String> queryParams = new HashMap<String, String>();
		// queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		// queryParams.put("leadId", leadId);
		// queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		// queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		// queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		//
		// token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		//
		// Map<String, String> headers = new HashMap<String, String>();
		// headers.put("Authorization", token);
		// headers.put("Content-Type", "application/json");
		// headers.put("custId", custId);
		//
		// Map<String, Object> body = new HashMap<String, Object>();
		// body.put("workflowOperation", "IS_ADDITIONAL_DATA_REQUIRED");
		// body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");
		//
		// requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataRequired.json";
		//
		// responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);
		//
		// lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		//
		// }
		
		@Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC013_PLv3HERO_TOPUP_SecondBRECallback",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC015_PLv3HERO_TOPUP_FetchLeadVerifyAdditionalData() {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		
		if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
		}else {
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
		}
		}
		       
		
		@Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "TC015_PLv3HERO_TOPUP_FetchLeadVerifyAdditionalData",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC016_PLv3HERO_TOPUP_AdditionalDataCapture() {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		       if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
		equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
		        Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("leadId", leadId);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		//
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
		body.put("BUSINESS_EMAIL", "<EMAIL>");
		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
		body.put("FATHER_NAME", "RAM");
		requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";
		//
		responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);
		//
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
		       Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"386");
		//
		       }
		//        
		}
		//
		
		
		
		
		@Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC016_PLv3HERO_TOPUP_AdditionalDataCapture",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC017_PLv3HERO_TOPUP_BRE3Success() {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		for(int i =0;i<10;i++) {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
		equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
		break;
		}
		}
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
		
		}
		       
		
		@Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC017_PLv3HERO_TOPUP_BRE3Success", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC018_PLv3HERO_TOPUP_UpdateKYCNameInSAI() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("leadId", leadId);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
		body.put("status", "SUCCESS");
		body.put("CKYC_NAME", "Anmol jain");
		body.put("PAN", "**********");
		
		requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
		responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);
		
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BRE3_SUCCESS.getStage());
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		ckycName=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME");
		}
		
		
		@Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC018_PLv3HERO_TOPUP_UpdateKYCNameInSAI",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC019_PLv3HERO_TOPUP_FetchLeadUpdateCKYCinSAI() {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
		   Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);
		
		}
		
		
		@Test(description = "Verify PL v3 Lead the bank details", dependsOnMethods = "TC019_PLv3HERO_TOPUP_FetchLeadUpdateCKYCinSAI", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC020_PLv3HERO_HERO_SaveBankDetails() throws InterruptedException {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json");
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("bankName", bankName);
		body.put("bankAccountNumber", bankAccountNumber);
		body.put("ifsc", ifsc);
		body.put("bankAccountHolderName", bankAccountHolderName);
		body.put("EMANDATE_TYPE", "Internet Banking");
		
		responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);
		
		if (responseObject.getStatusCode() == 200) {
		
		Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
		}
		
		else {
		
		for (int i = 1; i < 4; i++) {
		LOGGER.info("Again hitting with same data: retry-count: " + i);
		responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
		
		if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
		{
		break;
		}
		
		}
		
		}
		
		responseObject = lendingBaseClassObject.fetchExistingLeadData(LendingConstants.PL_V3_SOLUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE ,LendingConstants.PAYTM_APP_CHANNEL ,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP, sessionToken);
		 
		}
		
		
		@Test(description = "For PL v3 Bank Verification", dependsOnMethods = "TC020_PLv3HERO_HERO_SaveBankDetails",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC021_PLv3HERO_TOPUP_FetchLeadPostBankVerification() {
		for(int i=0;i<5;i++) {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
		break;
		}
		}
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
		   //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),bankName);
		   //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),bankAccountNumber);
		   //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),ifsc);
		   //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),bankAccountHolderName);
		   
		   
		}
		
		
		
		@Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC021_PLv3HERO_TOPUP_FetchLeadPostBankVerification", groups = {
		"Regression" })
		@Owner(emailId =  "<EMAIL>")
		public void TC022_PLv3HERO_TOPUP_EmandateCallback() throws InterruptedException {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "EMANDATE_SUCCESS");
		
		Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
		
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		
		}
		
		@Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "TC022_PLv3HERO_TOPUP_EmandateCallback",groups = {"Regression"})
		@Owner(emailId = "<EMAIL>")
		public void TC023_PLv3HERO_TOPUP_FetchLeadPostEmandate() {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
		}
		
		
		@Test(description = "For PL v3 Hero Generate Loan Agreement", dependsOnMethods = "TC023_PLv3HERO_TOPUP_FetchLeadPostEmandate", groups = { "Regression" })
		@Owner(emailId =  "<EMAIL>")
		public void TC024_PLv3HERO_TOPUP_GenerateLoanAgreement() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		
		Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		if (responseObject.jsonPath().getString("meta.status").equals("success")) {
		
		code = responseObject.jsonPath().getString("data.state.code");
		tncName = responseObject.jsonPath().getString("data.state.tncName");
		url = responseObject.jsonPath().getString("data.state.url");
		uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
		md5 = responseObject.jsonPath().getString("data.state.md5");
		}
		
		}
		
		
		@Test(description = "For PL v3 Hero Generate Sanction Letter", dependsOnMethods = "TC024_PLv3HERO_TOPUP_GenerateLoanAgreement", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC025_PLv3HERO_TOPUP_GenerateSanctionLetter() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		
		queryParams.put("tncType", "LOAN_SANCTION_TNC");
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		
		Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		if (responseObject.jsonPath().getString("meta.status").equals("success")) {
		
		codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
		tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
		urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
		uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
		md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
		}
		
		}
		
		@Test(description = "For PL v3 Hero Verify submit application", dependsOnMethods = "TC025_PLv3HERO_TOPUP_GenerateSanctionLetter", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC026_PLv3HERO_TOPUP_SubmitApplication() throws InterruptedException {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("session_token", sessionToken);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
		body.put("LENDING_DYNAMIC_TNC", tncName);
		body.put("TNC_ACCEPTED_VERSION", 1);
		body.put("KYB_TNC_REF_NO", uniqueIdentifier);
		body.put("TNC_ACCEPTED_CODE", md5);
		
		
		body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
		body.put("SECONDARY_ACCEPTED_TNC_VERSION", 1);
		
		body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
		body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
		
		requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3LoanAgreementAcceptrequest.json";
		responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);
		
		LOGGER.info("Status Code : " + responseObject.getStatusCode());
		
		Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		
		}
		
		@Test(description = "For PL v3 Hero Verify Lead stage Post Submit Loan Application", dependsOnMethods = "TC026_PLv3HERO_TOPUP_SubmitApplication", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC027_PLv3HERO_TOPUP_FetchLead_SubmitApplication() {
		
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
		}
		
		
		@Test(description = "For PL v3 Hero Verify PDC Callback", dependsOnMethods = "TC027_PLv3HERO_TOPUP_FetchLead_SubmitApplication", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC028_PLv3HERO_TOPUP_PDCCallback() throws InterruptedException {
		for(int i =0;i<=10;i++) {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
		break;
		}
		
		}
		if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
		LOGGER.info("Loan Application Accepted with PDC Callback ");
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "LOAN_APPLICATION_ACCEPTED");
		body.put("USER_MESSAGE", "Loan disbursal is accepted");
		body.put("LMS_REASON_ID", "0");
		body.put("SYSTEM_MESSAGE", "Direct Approval of Loan Account is accepted");
		
		responseObject = lendingBaseClassObject.pdcLoanApplicationAccepted(queryParams, headers, body,true);
		
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		}
		LOGGER.info("Loan Application Accepted without PDC Callback ");
		}
		
		@Test(description = "For PL v3 Hero Verify Lead stage After PDC Callback", dependsOnMethods = "TC028_PLv3HERO_TOPUP_PDCCallback", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>")
		public void TC029_PLv3HERO_TOPUP_FetchLeadPostPDCCallback() {
		
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
		   Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
		}
		
		
		@Test(description = "For PL v3 Hero Verify if LMS Approve Callback required",groups = {"Regression"},dependsOnMethods = "TC029_PLv3HERO_TOPUP_FetchLeadPostPDCCallback")
		@Owner(emailId = "<EMAIL>")
		public void TC030PLv3HERO_TOPUP_SubmitApplicationLMSApprovedCallback() {
		for(int i=0;i<10;i++) {
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
		break;
		}
		}
		if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_HERO_TOPUP);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", token);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowOperation", "LMS_APPLICATION_APPROVED");
		body.put("LOAN_ACCOUNT_NUMBER", "PYTM"+Utilities.randomNumberGenerator(8));
		body.put("LENDER_LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
		body.put("LENDER_DMS_ID", "");
		body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
		body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
		body.put("LMS_REASON_ID", "");
		
		requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PLv3LMSApproveCallback.json";
		   responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
		}
		responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
		LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		
		
		}
		
		
		}
