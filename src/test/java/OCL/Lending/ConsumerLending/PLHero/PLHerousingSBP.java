package OCL.Lending.ConsumerLending.PLHero;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import OCL.Lending.ConsumerLending.TestPersonalLoanV3Hero;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;


import java.util.HashMap;
import java.util.Map;

public class PLHerousingSBP extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(TestPersonalLoanV3Hero.class);
    String solution="personal_loan_v3";
    String entityType="INDIVIDUAL";
    String channel="PAYTM_APP";
    String consumerNumber="5085091025";
    String custId="1701089144";
    String token="";
    String loanOfferID="pl_hero_1701089144\\n_6c81cc9d";
    String baseID="pl_hero_1701089144\\n_6c81cc9d";
    String requestBodyJsonPath="";
    String leadId = "";
    String sessionToken = "";
    String consumerPassword="paytm@123";
    String userIPAddress="";
    String occupation="";
    String income="";
    String firstName="BHAIRAVI";
    String lastName="LATASREE";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String stringify_json="{\\\"baseId\\\":\\\"PL_HERO_1700162726_07596f6c\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"creditScore\\\":808,\\\"eMandateType\\\":\\\"MANDATORY\\\",\\\"field_investigation_needed\\\":false,\\\"lenderSchemeId\\\":90,\\\"lenderRiskCode\\\":\\\"CL_PL\\\",\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":200000.0,\\\"minLoanAmount\\\":10000.0,\\\"newOfferGenerated\\\":true,\\\"ntc\\\":0,\\\"offerEndDate\\\":\\\"Wed Jun 29 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"203fd47e-992e-41cf-b7a9-0a5b52acae01\\\",\\\"offerStartDate\\\":\\\"Thu Mar 31 00:00:00 IST 2022\\\",\\\"offerValidity\\\":90,\\\"paytmThick\\\":0,\\\"productId\\\":\\\"78\\\",\\\"productVersion\\\":1,\\\"riskGrade\\\":\\\"L\\\",\\\"riskSegment\\\":\\\"L\\\",\\\"sourceOfWhitelist\\\":\\\"RISK\\\",\\\"isBre2Required\\\":false,\\\"skipMandate\\\":false,\\\"loanDownGradable\\\":false}";
    String loanTenure="";
    String loanAmount="";
    String loanEquatedMonthlyInstallment="";
    String loanRateofInterest="";
    String loanInterestAmount="";
    String loanProcessingFeeRate="";
    String loanDisbursalAmount="";
    String stampDutyCharges="";
    String brokerPeriodInterest="";
    String uuidCustomerPhoto = "";
    String uuidSelfie="";
    String ckycName="BENE CUSTOMER NAME";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier ="";
    String md5 ="";
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String bankName="SBI BANK";
    String bankAccountNumber="*********";
    String ifsc="SBIN0002222";
    String bankAccountHolderName="UMESH CHAUHAN";


    Response responseObject= null;
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    
    
    @BeforeClass()    
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);
    }
    
    
    
    
    @Test(description="Verify if there is any existing Personal Loan Hero Lead",groups= {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_fetchLead()
    {
       responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
         
         if(responseObject.jsonPath().getInt("statusCode")==200)
         {
           LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
           Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
             
           LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
             
            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
          
           leadId=responseObject.jsonPath().getString("leadId");
         }
          
         if(responseObject.jsonPath().getInt("statusCode")==404)
         {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
             
            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
        
         }
       
    }
    
    
    
    
    
    
    @Test(description="Verify reseting existing Personal Loan Hero lead",groups={"Regression"},dependsOnMethods = {"PLv3Hero_fetchLead"})
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_ResetLead() {
       
       Map<String,String> queryParams=new HashMap<String,String>();
       queryParams.put("leadId",leadId);
       queryParams.put("custId", custId);
       queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
       
            
       token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
             
       Map<String, String> headers = new HashMap<String, String>();
       headers.put("Authorization", token);
       headers.put("Content-Type", "application/json");
       headers.put("custId", custId);
             
       lendingBaseClassObject.resetLendingLeads(queryParams, headers);
       
           
    }



    @Test(description="Create Lead for Personal Loan Hero",groups={"Regression"},dependsOnMethods = {"PLv3Hero_ResetLead"})
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_CreateLead() {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", entityType);
        queryParams.put("channel", channel);
        queryParams.put("custid", custId);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


        Map<String, String> header = new HashMap<String, String>();
        header = LendingBaseAPI.setHeadersReceivedFromFE();
        header.put("Authorization", token);
        header.put("latitude", LendingConstants.LATITUDE);
        header.put("longitude", LendingConstants.LONGITUDE);
        header.put("ipAddress", LendingConstants.IP_ADDRESS);
        header.put("Content-Type", "application/json");
        header.put("custid", custId);

//Body
        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);

//solutionAdditionalInfo
        body.put("PRODUCT_ID", "191");
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE", "RISK");
        body.put("EMAIL", "<EMAIL>");
        body.put("PRODUCT_VERSION", "1");
        body.put("LOAN_OFFER_ID", loanOfferID);
        body.put("BASE_ID", baseID);
        body.put("LENDER_ID", "5");
        body.put("WHITELISTING_SOURCE", "RISK");
        body.put("IS_EMANDATE_ELIGIBLE", "true");
        body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
        body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
        body.put("IS_FATHER_NAME_REQUIRED", "true");
        body.put("MARITAL_STATUS", "NOT_KNOWN");
        body.put("IS_BRE3_REQUIRED", "true");

//leadAdditionalInfo
        body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
        body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "FALSE");


        requestBodyJsonPath = "MerchantService/V1/Hero/PLHeroCreateLeadRequest.json";


        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body, requestBodyJsonPath);
        if (responseObject.getStatusCode() == 201) {
            LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            leadId = responseObject.jsonPath().getString("leadId");
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "5");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"), "191");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"), "1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"), "PL");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"), "RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"), "true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"), LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"), LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"), "true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"), "NOT_KNOWN");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"), "true");
        }
        
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
    }
    
    
    
    
    
    @Test(description="Verify the PL v3 Hero lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "PLv3Hero_CreateLead")
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_FetchLeadAllData() throws JSONException
    {
      
      for(int i=0;i<15;i++)
      {
       
      responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
      if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
         break;
      
      }
      
      responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
     
      if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
      {
         LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
         Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
          
         LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
         Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
          
         LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
         Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
        
         Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
         Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
         Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
     }

        
     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());

    }
    
    
    
    
    
    
    @Test(description="Update lead occupation details",groups = {"Regression"},dependsOnMethods = "PLv3Hero_FetchLeadAllData")
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_UpdateLeadOccupationDetails() {
       Map<String,String> queryParams=new HashMap<String,String>();
       queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
       queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
       
     
       Map<String,String> headers=new HashMap<String,String>();
       token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("custId", custId);
        headers.put("Content-Type", "application/json");

        
//Body         
        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation","OCCUPATION_DETAILS");
        
//solutionAdditionalInfo        
        body.put("OCCUPATION", LendingConstants.PLv3_OCCUPATION);
        body.put("ANNUAL_INCOME", "200000");
        body.put("EMPLOYER_ID", "105987");
        body.put("EMPLOYER_NAME","Paytm");
        body.put("BUSINESS_NAME","N/A");
        body.put("LOAN_PURPOSE","Personal use");
        body.put("PINCODE","180012");
        body.put("DOB","1984-10-04");
        body.put("PAN", "**********");
        body.put("EMAIL", "<EMAIL>");
        body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");
        body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");
        
        


    
        requestBodyJsonPath="MerchantService/V1/Hero/PLHeroOccupationDetails.json";

        for(int i=0;i<2;i++)
           {
           responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
           if(responseObject.getStatusCode()==200)
           break;
           }
        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
         {
          LOGGER.info("baseResponseCode : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
           
            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");
            occupation=responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION");
            income=responseObject.jsonPath().getString("solutionAdditionalInfo.ANNUAL_INCOME");
         }
      
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());

    }
    
    
    
    
    
    @Test(description = "Verify the PLv3 lead data after updating Occupation Details",groups = {"Regression"},dependsOnMethods = "PLv3Hero_UpdateLeadOccupationDetails")
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_FetchDataPostOccupationDetailUpdate() throws JSONException
    {
      for(int i=0;i<15;i++)
      {
      responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
    
      if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
         break;
      }
      
     responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
     
     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
     {
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
          
        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
          
        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
        
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION"),occupation);
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ANNUAL_INCOME"),income);
     }
     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
     }
    
    
    
    
    
    @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "PLv3Hero_FetchDataPostOccupationDetailUpdate")
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_UpdateLeadDetailsinSAI() {
    	Map<String,String> queryParams=new HashMap<String,String>();
    	queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
    	        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
    	queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

    	Map<String,String> headers=new HashMap<String,String>();
    	token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID,
    	        custId,LendingConstants.LMS_SECRET);
    	headers = LendingBaseAPI.setHeadersReceivedFromFE();
    	headers.put("Authorization", token);
    	headers.put("Content-Type", "application/json");
    	headers.put("custId", custId);

    	Map<String,Object> body=new HashMap<String,Object>();
    	body.put("workflowMovementRequired","false");
    	body.put("status","SUCCESS");
    	body.put("PAN", "**********");
    	body.put("F_NAME",firstName);
    	body.put("L_NAME",lastName);
    	body.put("EMAIL","<EMAIL>");
    	body.put("DOB","1979-10-05");
    	body.put("BUREAU_PRIORITY","EXPERIAN");


    	requestBodyJsonPath="MerchantService/v2/lending/dataUpdate/UpdateSAIforHeroDistribution.json";
    	responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,requestBodyJsonPath);

    	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
    	Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
    	Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());
       
    }
    
    
    
    
    @Test(description = "Verify the PLv3 lead data after Updating details in SAI",groups = {"Regression"},dependsOnMethods = "PLv3Hero_UpdateLeadDetailsinSAI")
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_FetchDataPostSAIlUpdate() throws JSONException
    {
      
     responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
     
     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
     {
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
          
        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
          
        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
        
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
     }
     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
     }
    
    
    
    
    
    @Test(description = "Verify Fetch CIR for PLv3 lead",groups = {"Regression"},dependsOnMethods = "PLv3Hero_FetchDataPostSAIlUpdate")
    @Owner(emailId = "<EMAIL>")
    public void PLv3Hero_FetchCIR() {
        
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();

        String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
        for (int i = 0; i < 6; i++) {
            responseObject = lendingBaseClassObject.v3FetchCIR(queryParams, headers, body, requestjsonpath);

            if (responseObject.getStatusCode() == 200)

                break;

        }

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BUREAU_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE_IN_PROGRESS.getStage());
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "194");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"), "TRUE");
        Assert.assertEquals(responseObject.jsonPath().getString("creditState"), "BUREAU_SUCCESS");
        
        
    }
         
        
    
    
    
    
    
    
    
        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "PLv3Hero_FetchCIR")
        @Owner(emailId = "<EMAIL>")
       public void PLv3Hero_BRE1Callback() throws JSONException
       {
         
        for(int i =0;i<=15;i++) {
             responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
    
               if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
                  break;
               }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))
         {
           Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
              Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");
              
              LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
              for(int i =0;i<15;i++) {
              responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            
                 if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
                    LOGGER.info("BRE 1 passed without callback");  
                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                    loanOfferID=responseObject.jsonPath().getString("LOAN_OFFER_ID");
                    break;
                 }
           }
         requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PLv3ABFLBRE1CallbackRequest.json";
         BRE1Callback(requestBodyJsonPath);
         if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
          LOGGER.info("BRE 1 passed with callback");  
          LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
          Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
          Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
          Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
         }
       LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
             }
       }
        
        
        
        
        
        @Test(description = "Verify the PLv3 lead data after BRE1 Success",groups = {"Regression"},dependsOnMethods = "PLv3Hero_BRE1Callback")
       @Owner(emailId = "<EMAIL>")
       public void PLv3Hero_FetchDataPostBRE1Success() throws JSONException
       {
         
         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
         
         if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
         {
           LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
           Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
             
           LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
             
            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
            
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"),bureauRequest);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"),bureauResponse);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"),bureauCreditState);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"),breCreditScore);
    
         }
         Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
         }
        
        
       
       public Response BRE1Callback(String requestBodyJsonPath) {
          Map<String, String> queryParams = new HashMap<String, String>();
          queryParams.put("solution", solution);
          queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
          queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
          
          token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
          
          Map<String, String> headers = new HashMap<String, String>();
          headers.put("Authorization", token);
          headers.put("Content-Type", "application/json");
          headers.put("custId", custId);
          
          Map<String, Object> body = new HashMap<String, Object>();
          body.put("workflowOperation", "BRE1_SUCCESS");
          body.put("BRE1_OFFER_DETAILS", ""+stringify_json+"");
          body.put("LENDING_SCHEME_ID", "90");
            
            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
            
            return responseObject;           
             
          }
       
       
       
       
       
       
       @Test(description="Update PAN details in SAI for New KYC",groups = {"Regression"},dependsOnMethods = "PLv3Hero_FetchDataPostBRE1Success")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HEROKYC_UpdateLeadDetailsinSAI() {
    	   Map<String,String> queryParams=new HashMap<String,String>();
	    	queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
	    	        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
	    	queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

	    	Map<String,String> headers=new HashMap<String,String>();
	    	token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID,
	    	        custId,LendingConstants.LMS_SECRET);
	    	headers = LendingBaseAPI.setHeadersReceivedFromFE();
	    	headers.put("Authorization", token);
	    	headers.put("Content-Type", "application/json");
	    	headers.put("custId", custId);

	    	Map<String,Object> body=new HashMap<String,Object>();
	    	body.put("workflowMovementRequired", false);
	        body.put("PAN", "**********");
	        body.put("DOB", "1992-04-01");
	        body.put("GENDER", "MALE");
	        body.put("NSDL_NAME", "Mr Rohit dse");


	    	requestBodyJsonPath = "MerchantService/V1/Hero/UpdateLeadWorkflow.json";
	    	responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,requestBodyJsonPath);

	    	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
	        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");


        }
       
       
    
       
       
       @Test(description = "Verify PL v3 HERO lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "PLv3HEROKYC_UpdateLeadDetailsinSAI")
       @Owner(emailId = "<EMAIL>")
       public void PLv3Hero_LoanOfferAccept() {
           Map<String,String> queryParams=new HashMap<String,String>();
             
           queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
           queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
           
           token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
             
           Map<String, String> headers = new HashMap<String, String>();
           headers.put("Authorization", token);
           headers.put("Content-Type", "application/json");
           headers.put("custId", custId);
              
            Map<String,Object> body=new HashMap<String,Object>();
            body.put("workflowOperation", "OFFER_ACCEPTED");
            
            //solutionAdditionalInfo
            body.put("LOAN_TENURE", "24");
            body.put("LOAN_TENURE_UNIT", "MONTH");
            body.put("LOAN_AMOUNT_IN_NUMBER", "310000");
            body.put("LOAN_AMOUNT_IN_WORDS", "Three Lakh Ten Thousand");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "15778");
            body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Fifteen Thousand Seven Hundred Seventy Eight");
            body.put("LOAN_RATE_OF_INTEREST", "20");
            body.put("LOAN_INTEREST_AMOUNT", "11888");
            body.put("LOAN_PROCESSING_FEE", "8370");
            body.put("PROCESSING_FEE_RATE", "2.7");
            body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "9876");
            body.put("LOAN_DISBURSAL_AMOUNT", "299849");
            body.put("STAMP_DUTY_CHARGES", "275");
            body.put("BROKEN_PERIOD_INTEREST", "0");
            body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero");
           body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero");
            
            requestBodyJsonPath="MerchantService/V1/Hero/PLHeroOfferAccepted.json";
             
            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
           
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
            {
              LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
              Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
               
               Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
               Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
               Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
               loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
               loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
               loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
               loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
               loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
               loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
               loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
               stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
               brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
            }
            else
            {
               Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
               Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
               
            }
            }
       
       
       
       
       
       
       @Test(description = "Verify the PLv3 lead data after Loan offer accept",groups = {"Regression"},dependsOnMethods = "PLv3Hero_LoanOfferAccept")
       @Owner(emailId = "<EMAIL>")
       public void PLv3Hero_FetchDataPostLoanOfferAccept() throws JSONException
       {
         
         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
         
         if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
         {
           LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
           Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
             
           LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
             
            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
            
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);
    
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest); 
         }
         Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
         }
       
       
       
       
       
       
       @Test(description = "Verify the PLv3 lead Upload Selfie photo",groups = {"Regression"},dependsOnMethods = "PLv3Hero_FetchDataPostLoanOfferAccept")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_UploadSelfiePhoto() throws InterruptedException {

            Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,
                    sessionToken,"selfie","**********.jpg");


            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
            uuidCustomerPhoto = responseObject.jsonPath().getString("uuid");


        }
        
        
        
        

        @Test(description = "Verify the  details of Uploaded Selfie Photo",groups = {"Regression"}, dependsOnMethods = "PLv3HERO_UploadSelfiePhoto")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_VerifyUploadedCSelfiePhoto() {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));

            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");
           // Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docUUId"),uuidCustomerPhoto );

        }
       
       
       


           
          
       
       
       
       @Test(description="Initiate KYC by Search By PAN",groups = {"Regression"},dependsOnMethods = "PLv3HERO_VerifyUploadedCSelfiePhoto")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HEROKYC_InitiateKYCSearchByPAN() throws InterruptedException {
          Map<String,String> queryParams=new HashMap<String,String>();
          queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                  LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
          queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

          Map<String,String> headers=new HashMap<String,String>();
          token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
          headers = LendingBaseAPI.setHeadersReceivedFromFE();
          headers.put("Authorization", token);
          headers.put("Content-Type", "application/json");
          headers.put("custId", custId);

          Map<String,Object> body=new HashMap<String,Object>();
          body.put("kycMode","SEARCH_BY_PAN");

          requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";

          responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

          lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
          Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
          Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
          Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
          Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

        }
       
       
       
       @Test(description = "Verify lead details after KYC is initiated",groups={"Regression"},dependsOnMethods = "PLv3HEROKYC_InitiateKYCSearchByPAN")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_FetchDataPostKYCInitiated() throws JSONException
        {

            for(int i =0;i<=45;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_IN_PROGRESS.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOCATION_REQUIRED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2020");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOCATION_REQUIRED.getStage());


        }
       
        
        
        
        
        @Test(description="Location Captured",groups = {"Regression"},dependsOnMethods = "PLv3HERO_FetchDataPostKYCInitiated")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_LocationCaptured() throws InterruptedException {
          Map<String,String> queryParams=new HashMap<String,String>();
          queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,
                  LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
          queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

          Map<String,String> headers=new HashMap<String,String>();
          token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
          headers = LendingBaseAPI.setHeadersReceivedFromFE();
          headers.put("Authorization", token);
          headers.put("Content-Type", "application/json");
          headers.put("custId", custId);
          headers.put("latitude", LendingConstants.LATITUDE);
            headers.put("longitude", LendingConstants.LONGITUDE);
            headers.put("ipAddress", LendingConstants.IP_ADDRESS);

          Map<String,Object> body=new HashMap<String,Object>();
          body.put("workflowOperation","LOCATION_CAPTURED");

          requestBodyJsonPath="MerchantService/V1/Hero/PLHeroLocationCaptured.json";

          responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

          lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
          Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
          Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOCATION_CAPTURED.getStage());
          Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_IN_PROGRESS.getStage());
          Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "842");

        }
        
        
  
        
        @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "PLv3HERO_LocationCaptured")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_FetchDataPostLocationCaptured() throws JSONException
        {

            for(int i =0;i<=55;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOCATION_REQUIRED.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_IN_PROGRESS.getStage()))
            {
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"387");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

            }


        }
       
        
        
    
        @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "PLv3HERO_FetchDataPostLocationCaptured",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_SecondBRECallback() {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                     LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
             if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                     equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
             {
                 Map<String, String> queryParams = new HashMap<String, String>();
                 queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                 queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
                 queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                 queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                 queryParams.put("leadId", leadId);

                 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,LendingConstants.JWT_RISK_KEY);

                 Map<String, String> headers = new HashMap<String, String>();
                 headers.put("Authorization", token);
                 headers.put("Content-Type", "application/json");
                 headers.put("custId", custId);

                 Map<String, Object> body = new HashMap<String, Object>();
                 body.put("workflowOperation", "BRE2_SUCCESS");
                 body.put("BASE_ID", baseID);
                 body.put("LOAN_OFFER_ID", loanOfferID);
                 body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
                 body.put("SKIP_EMANDATE_ELIGIBLE", "false");
                 body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

                 requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
                 responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body,requestBodyJsonPath,"BRE2");
                 LOGGER.info("BRE 2 Success with callback");

             } else {
                 responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                         LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
                 LOGGER.info("BRE 2 Success without callback");
             }

         }
        
        
        
        
        
        

        @Test(description = "Verify PL v3 Lead After BRE2 success Callback", dependsOnMethods = "PLv3HERO_SecondBRECallback", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void PLv3HERO_FetchDataAfterBRE2Success() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");

        }


        
        
        

        @Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "PLv3HERO_FetchDataAfterBRE2Success",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_AdditionalDataCapture() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("leadId", leadId);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
                body.put("BUSINESS_EMAIL", "<EMAIL>");
                body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
                body.put("FATHER_NAME", "RAM");
                requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";

                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            }

        }
        
        
        
        
        
        @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "PLv3HERO_AdditionalDataCapture",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_BRE3Success() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            
            for(int i =0;i<10;i++)
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                        equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                    break;
                }
            }
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

        }
        
        


        @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "PLv3HERO_BRE3Success", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>", isAutomated = true)
        public void PLv3HERO_UpdateKYCNameInSAI() {
           Map<String,String> queryParams=new HashMap<String,String>();
          queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
          queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
        
          Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowMovementRequired", false);
            body.put("CKYC_NAME", "BENE CUSTOMER NAME");
            body.put("PAN", "**********");

            
            requestBodyJsonPath = "MerchantService/V1/Hero/UpdateLeadinSai.json";

            responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body, requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE3_SUCCESS.getStage());
          
       }

        
        

        @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "PLv3HERO_UpdateKYCNameInSAI",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_FetchLeadUpdateCKYCinSAI() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);

        }
        
        
        
        
        

        
        


        @Test(description = "Verify PL v3 Lead the bank details", dependsOnMethods = "PLv3HERO_FetchLeadUpdateCKYCinSAI", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_SaveBankDetails() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
          queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


          Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
            body.put("EMANDATE_TYPE", "Internet Banking");
            body.put("bankName", bankName);
            body.put("bankAccountNumber", bankAccountNumber);
            body.put("ifsc", ifsc);
            body.put("bankAccountHolderName", bankAccountHolderName);


            responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);
            
            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");

            
        }
        
        
        
        
       
        
        


        @Test(description = "For PL v3 Bank Verification", dependsOnMethods = "PLv3HERO_SaveBankDetails",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_FetchLeadPostBankVerification() {
            for(int i=0;i<5;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
                    break;
                }
            }
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");


        }
        
        
        



        @Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "PLv3HERO_FetchLeadPostBankVerification", groups = {
                "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void PLv3HERO_EmandateCallback() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("status", "EMANDATE_SUCCESS");

            Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

        }
        
        
        

        @Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "PLv3HERO_EmandateCallback",groups = {"Regression"})
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_FetchLeadPostEmandate() {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
        }
        
        
        


        @Test(description = "For PL v3 Hero Generate Loan Agreement", dependsOnMethods = "PLv3HERO_FetchLeadPostEmandate", groups = { "Regression" })
        @Owner(emailId =  "<EMAIL>")
        public void PLv3HERO_GenerateLoanAgreement() {
           Map<String, String> queryParams = new HashMap<String, String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
          queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

          Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);

            Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());
            if (responseObject.jsonPath().getString("meta.status").equals("success")) {

                code = responseObject.jsonPath().getString("data.state.code");
                tncName = responseObject.jsonPath().getString("data.state.tncName");
                url = responseObject.jsonPath().getString("data.state.url");
                uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
                md5 = responseObject.jsonPath().getString("data.state.md5");
            }
        }
        
        
        
        


        @Test(description = "For PL v3 Hero Generate Sanction Letter", dependsOnMethods = "PLv3HERO_GenerateLoanAgreement", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_GenerateSanctionLetter() {
           Map<String, String> queryParams = new HashMap<String, String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
          queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
          
            queryParams.put("tncType", "LOAN_SANCTION_TNC");

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("session_token", sessionToken);

            Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());
            if (responseObject.jsonPath().getString("meta.status").equals("success")) {

                codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
                tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
                urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
                uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
                md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
            }

        }
        
          
        
        

        @Test(description = "For PL v3 Hero Verify submit application", dependsOnMethods = "PLv3HERO_GenerateSanctionLetter", groups = {
                "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_SubmitApplication() throws InterruptedException {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
           queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("session_token", sessionToken);
            headers.put("deviceIdentifier","Apple");

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
            body.put("LENDING_DYNAMIC_TNC", tncName);
            body.put("TNC_ACCEPTED_VERSION", 1);
            body.put("KYB_TNC_REF_NO", uniqueIdentifier);
            body.put("TNC_ACCEPTED_CODE", md5);


            body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
            body.put("SECONDARY_ACCEPTED_TNC_VERSION", 1);

            body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
            body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
            body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", true);
            

            requestBodyJsonPath = "MerchantService/V1/Hero/PLHeroLoanAgreementAccept.json";
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

            LOGGER.info("Status Code : " + responseObject.getStatusCode());

            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));

        }
        
        
        
        

        @Test(description = "For PL v3 Hero Verify Lead stage Post Submit Loan Application", dependsOnMethods = "PLv3HERO_SubmitApplication", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_FetchLeadPostSubmitApplication() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
        }
        
        
        
        
        @Test(description = "For PL v3 Hero Verify PDC Callback", dependsOnMethods = "PLv3HERO_FetchLeadPostSubmitApplication", groups = {
        "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_PDCCallback() throws InterruptedException {
            for(int i =0;i<=10;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
                    break;
                }

            }
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
                LOGGER.info("Loan Application Accepted with PDC Callback ");
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("leadId", leadId);
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("status", "LOAN_APPLICATION_ACCEPTED");
                body.put("USER_MESSAGE", "Loan disbursal is accepted");
                body.put("LMS_REASON_ID", "0");
                body.put("SYSTEM_MESSAGE", "Direct Approval of Loan Account is accepted");

                responseObject = lendingBaseClassObject.pdcLoanApplicationAccepted(queryParams, headers, body,true);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
            }
            LOGGER.info("Loan Application Accepted without PDC Callback ");
        }
        
        
        
        


        @Test(description = "For PL v3 Hero Verify Lead stage After PDC Callback", dependsOnMethods = "PLv3HERO_PDCCallback", groups = { "Regression" })
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_FetchLeadPostPDCCallback() {

            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"350");
        }
        
        
        


        @Test(description = "For PL v3 Hero Verify if LMS Approve Callback required",groups = {"Regression"},dependsOnMethods = "PLv3HERO_FetchLeadPostPDCCallback")
        @Owner(emailId = "<EMAIL>")
        public void PLv3HERO_SubmitApplicationLMSApprovedCallback() {
            for(int i=0;i<10;i++) {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
                    break;
                }
            }
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("leadId", leadId);
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Content-Type", "application/json");
                headers.put("Authorization", token);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "LMS_APPLICATION_APPROVED");
                body.put("LOAN_ACCOUNT_NUMBER", "PYTM"+Utilities.randomNumberGenerator(8));
                body.put("LENDER_LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
                body.put("LENDER_DMS_ID", "");
                body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
                body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
                body.put("LMS_REASON_ID", "");

                requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PLv3LMSApproveCallback.json";
                responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
            }
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));


        }


          @Test(description = "Verify the lead stage after LMS Approved Callback",dependsOnMethods = "PLv3HERO_SubmitApplicationLMSApprovedCallback",groups = {"Regression"})
         @Owner(emailId = "<EMAIL>")
           public void PLv3HERO_FetchLeadPostLMSCallback() throws JSONException
           {
            for(int i=0;i<25;i++)
            {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BUSINESS_LENDING_V3,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
           
            if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
            {
               break;
            }
            
            }      
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
           }

          

}