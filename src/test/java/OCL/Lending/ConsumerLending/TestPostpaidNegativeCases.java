//package OCL.Lending.ConsumerLending;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.apache.log4j.Logger;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import com.goldengate.common.BaseMethod;
//import com.paytm.apitools.util.annotations.Owner;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingConstants;
//import Services.MechantService.MiddlewareServices;
//
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//import io.restassured.response.Response;
//
//public class TestPostpaidNegativeCases extends BaseMethod {
//
//	private static final Logger LOGGER = Logger.getLogger(TestPostpaidNegativeCases.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
//
//	String sessionToken = "";
//	String leadId = "";
//	String custId = "";
//	String agentNumber = "5432100811";
//	String agentPassword = "test@123";
//	String token = "";
//	String uuid = "";
//	String ckycStage = "";
//	String loanOffered = "true";
//	String maxLoanAmount = "20000";
//	String authorisedMonthlyLimit = "";
//	String stage = "";
//	String code = "";
//	String tncName = "";
//	String url = "";
//	String uniqueIdentifier = "";
//	String md5 = "";
//	String codeSanctionLetter = "";
//	String tncNameSanctionLetter = "";
//	String urlSanctionLetter = "";
//	String uniqueIdentifierSanctionLetter = "";
//	String md5SanctionLetter = "";
//
//	public static final String SOLUTION = "postpaid_v2";
//	public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
//	public static final String ENTITY_TYPE = "INDIVIDUAL";
//	public static final String CHANNEL = "PAYTM_APP";
//	public static final String PAN = "**********";
//	public static final String DOB = "1989-04-21";
//	public static final String EMAIL = "<EMAIL>";
//	public static final String ISSUER = "OE";
//	public static final String CLIENT_ID = "LMS";
//	Map<String, String> commonHeaders;
//
//	@BeforeClass()
//	public void intitializeInputData() {
//
//		LOGGER.info(" Before Suite Method for Agent Login ");
//		sessionToken = ApplicantToken(agentNumber, agentPassword);
//		LOGGER.info("Applicant Token for Lending : " + sessionToken);
//
//		commonHeaders = setcommonHeaders();
//
//	}
//
//	@Test(description = "Delete all existing leads of the number", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC001_DeleteExistingLead() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("mobile", agentNumber);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683");
//
//		lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
//	}
//
//	@Test(description = "Verify whether there is any existing lead present or not", dependsOnMethods = "TC001_DeleteExistingLead", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC002_FetchLeadDeatils() {
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "LEAD_NOT_PRESENT");
//
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");
//	}
//
//	@Test(description = "Create PostPaid Lead without passing entity type INDIVIDUAL", dependsOnMethods = "TC002_FetchLeadDeatils", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC003_CreatePostpaidLead_WithoutPassing_EntityType() {
//
//		LOGGER.info("Not passing query parameter: entityType in URL");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		// queryParams.put("entityType","INDIVIDUAL" );
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//				SOLUTION_TYPE_LEVEL_2);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("Lead creation is blocked in this case");
//
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//					SOLUTION_TYPE_LEVEL_2);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Create PostPaid Lead without passing  Solution as  postpaid_v2", dependsOnMethods = "TC003_CreatePostpaidLead_WithoutPassing_EntityType", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC004_CreatePostpaidLead_WithoutPassing_Solution() {
//
//		LOGGER.info("Not passing query parameter: Solution in URL");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		// queryParams.put("solution",SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//				SOLUTION_TYPE_LEVEL_2);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("Lead creation is blocked in this case");
//
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//					SOLUTION_TYPE_LEVEL_2);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Create PostPaid Lead without passing  Channel as  PAYTM_APP", dependsOnMethods = "TC004_CreatePostpaidLead_WithoutPassing_Solution", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC005_CreatePostpaidLead_WithoutPassing_Channel() {
//
//		LOGGER.info("Not passing query parameter: Channel in URL");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		// queryParams.put("channel",CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//				SOLUTION_TYPE_LEVEL_2);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("Lead creation is blocked in this case");
//
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//					SOLUTION_TYPE_LEVEL_2);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Create PostPaid Lead without passing  SSO token in header", dependsOnMethods = "TC005_CreatePostpaidLead_WithoutPassing_Channel", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC006_CreatePostpaidLead_WithoutPassing_SSOToken() {
//
//		LOGGER.info("Not passing query parameter: SSO token in Header");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		// headers.put("session_token",sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//				SOLUTION_TYPE_LEVEL_2);
//		if (responseObject.getStatusCode() == 401) {
//			LOGGER.info("Lead creation is blocked in this case");
//
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//					SOLUTION_TYPE_LEVEL_2);
//
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Create PostPaid Lead without passing  ContentType  in header", dependsOnMethods = "TC006_CreatePostpaidLead_WithoutPassing_SSOToken", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC007_CreatePostpaidLead_WithoutPassing_ContentType() {
//
//		LOGGER.info("Not passing query parameter: ContentType in Header");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//				SOLUTION_TYPE_LEVEL_2);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("Lead creation is blocked in this case");
//
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//					SOLUTION_TYPE_LEVEL_2);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Create PostPaid Lead without passing LENDER ID in request body ", dependsOnMethods = "TC007_CreatePostpaidLead_WithoutPassing_ContentType", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC008_CreatePostpaidLead_without_passing_LenderId() {
//
//		LOGGER.info("Lender id is missing in Request Body");
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		// body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//				SOLUTION_TYPE_LEVEL_2);
//		if (responseObject.getStatusCode() == 400) {
//			// Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//			// "GST is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//					SOLUTION_TYPE_LEVEL_2);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LENDER_ID is null or empty");
//
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing LOAN_MAX_AMOUNT  in request body ", dependsOnMethods = "TC008_CreatePostpaidLead_without_passing_LenderId", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC009_CreatePostpaidLead_without_passing_LoanMaxAmount() {
//
//		LOGGER.info(" Loan Max Amount  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//				SOLUTION_TYPE_LEVEL_2);
//		if (responseObject.getStatusCode() == 400) {
//			// Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"LOAN_MAX_AMOUNT
//			// is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body, SOLUTION,
//					SOLUTION_TYPE_LEVEL_2);
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			// Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"LOAN_MAX_AMOUNT
//			// is null or empty");
//
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing BASE_ID  in request body ", dependsOnMethods = "TC009_CreatePostpaidLead_without_passing_LoanMaxAmount", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0010_CreatePostpaidLead_without_passing_BaseId() {
//
//		LOGGER.info(" Base id   is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		// body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "BASE_ID is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "BASE_ID is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing Product id  in request body ", dependsOnMethods = "TC0010_CreatePostpaidLead_without_passing_BaseId", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0011_CreatePostpaidLead_without_passing_ProductID() {
//
//		LOGGER.info(" Product id is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		// body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_ID is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_ID is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing WHITELISTING_SOURCE  in request body ", dependsOnMethods = "TC0011_CreatePostpaidLead_without_passing_ProductID", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0012_CreatePostpaidLead_without_passing_WhitelistingSource() {
//
//		LOGGER.info(" WHITELISTING_SOURCE  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		// body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"WHITELISTING_SOURCE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"WHITELISTING_SOURCE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing PRODUCT_VERSION  in request body ", dependsOnMethods = "TC0012_CreatePostpaidLead_without_passing_WhitelistingSource", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0013_CreatePostpaidLead_without_passing_ProductVersion() {
//
//		LOGGER.info(" PRODUCT_VERSION  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		// body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"PRODUCT_VERSION is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"PRODUCT_VERSION is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing PRODUCT_TYPE  in request body ", dependsOnMethods = "TC0013_CreatePostpaidLead_without_passing_ProductVersion", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0014_CreatePostpaidLead_without_passing_ProductType() {
//
//		LOGGER.info(" PRODUCT_TYPE  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		// body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_TYPE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_TYPE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing LENDING_DYNAMIC_TNC  in request body ", dependsOnMethods = "TC0014_CreatePostpaidLead_without_passing_ProductType", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0015_CreatePostpaidLead_without_passing_LENDING_DYNAMIC_TNC() {
//
//		LOGGER.info(" LENDING_DYNAMIC_TNC  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		// body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"LENDING_DYNAMIC_TNC is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"LENDING_DYNAMIC_TNC is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing OFFER_START_DATE  in request body ", dependsOnMethods = "TC0015_CreatePostpaidLead_without_passing_LENDING_DYNAMIC_TNC", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0016_CreatePostpaidLead_without_passing_OFFER_START_DATE() {
//
//		LOGGER.info(" OFFER_START_DATE  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		// body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"OFFER_START_DATE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"OFFER_START_DATE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing OFFER_END_DATE  in request body ", dependsOnMethods = "TC0016_CreatePostpaidLead_without_passing_OFFER_START_DATE", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0017_CreatePostpaidLead_without_passing_OFFER_END_DATE() {
//
//		LOGGER.info(" OFFER_END_DATE  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		// body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"OFFER_END_DATE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"OFFER_END_DATE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing CONVENIENCE_FEE  in request body ", dependsOnMethods = "TC0017_CreatePostpaidLead_without_passing_OFFER_END_DATE", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0018_CreatePostpaidLead_without_passing_CONVENIENCE_FEE() {
//
//		LOGGER.info(" CONVENIENCE_FEE  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		// body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"CONVENIENCE_FEE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"CONVENIENCE_FEE is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing TNC_ADDITIONAL_PARAM  in request body ", dependsOnMethods = "TC0018_CreatePostpaidLead_without_passing_CONVENIENCE_FEE", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0019_CreatePostpaidLead_without_passing_TNC_ADDITIONAL_PARAM() {
//
//		LOGGER.info(" TNC_ADDITIONAL_PARAM  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		// body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"TNC_ADDITIONAL_PARAM is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//					"TNC_ADDITIONAL_PARAM is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead without passing GST  in request body ", dependsOnMethods = "TC0019_CreatePostpaidLead_without_passing_TNC_ADDITIONAL_PARAM", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0020_CreatePostpaidLead_without_passing_GST() {
//
//		LOGGER.info(" GST  is missing in Request Body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		// body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "GST is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "GST is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//	}
//
//	@Test(description = "Create PostPaid Lead with all deatils", dependsOnMethods = "TC0020_CreatePostpaidLead_without_passing_GST", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0021_CreatePostpaidLead() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "Paytm_postpaid_Happyloans");
//		body.put("LOAN_MIN_AMOUNT", "30000");
//		body.put("LOAN_MAX_AMOUNT", "200000");
//		body.put("BASE_ID", "10005155071");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "58");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-08-15");
//		body.put("OFFER_END_DATE", "2021-09-05");
//		body.put("PRODUCT_TYPE", "POSTPAID");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "1234");
//		body.put("CONVENIENCE_FEE", "2.0");
//		body.put("IS_SI_MANDATORY", false);
//		body.put("GST", "18.0");
//		body.put("LENDING_DYNAMIC_TNC", "Postpaid_lender_agreement_clix_DELITE");
//		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pp_clix");
//
//		Response responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 200) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//			leadId = responseObject.jsonPath().getString("leadId");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1sdMerchantLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//			leadId = responseObject.jsonPath().getString("leadId");
//		}
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC0021_CreatePostpaidLead", groups = {
//			"Regression" })
//
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0022_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "LEAD_CREATED");
//
//		LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.GST"), "18.0");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),
//				"STATIC_LOAN_OFFER_TNC");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_ID"), "58");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ORIGINAL_BASE_ID"),
//				"10005155071");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_VERSION"),
//				"1");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OFFER_START_DATE"),
//				"2020-08-15");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LUP_ID"), "1234");
//		Assert.assertEquals(
//				responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),
//				"sanction_letter_pp_clix");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),
//				"Postpaid_lender_agreement_clix_DELITE");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_MAX_AMOUNT"),
//				"200000");
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_MIN_AMOUNT"),
//				"30000");
//
//		Assert.assertEquals(responseObject.jsonPath().getString("mobileNumber"), agentNumber);
//		custId = responseObject.jsonPath().getString("custId");
//
//	}
//
//	@Test(description = "Add Basic details without Pan Number in Request body ", dependsOnMethods = "TC0022_FetchTheCreatedLeadDeatils", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0023_AddBasicDetails_Without_Passing_Pan() {
//
//		LOGGER.info(" Pan Number is missing in Request body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		// body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PAN number is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Add Basic details witih Invalid Pan Number in Request body ", dependsOnMethods = "TC0023_AddBasicDetails_Without_Passing_Pan", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0024_AddBasicDetails_With_Invalid_Pan() {
//
//		LOGGER.info(" Pan Number is Invalid in Request body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		body.put("PAN", "**********");
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid PAN number");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Add Basic details witih Invalid DOB in Request body ", dependsOnMethods = "TC0024_AddBasicDetails_With_Invalid_Pan", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0025_AddBasicDetails_With_Invalid_DOB() {
//
//		LOGGER.info(" DOB is Invalid in Request body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", "19-04-21");
//		body.put("PAN", "**********");
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Dob");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Add Basic details witih Invalid Email in Request body ", dependsOnMethods = "TC0025_AddBasicDetails_With_Invalid_DOB", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0026_AddBasicDetails_With_Invalid_Email() {
//
//		LOGGER.info(" Email is Invalid in Request body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", "1989-04-21");
//		body.put("PAN", "**********");
//		body.put("EMAIL", "nkyyahoo.com");
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Email");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	@Test(description = "Add Basic details without  DOB in Request body ", dependsOnMethods = "TC0026_AddBasicDetails_With_Invalid_Email", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0027_AddBasicDetails_Without_Passing_DOB() {
//
//		LOGGER.info(" DOB is missing in Request body");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		// body.put("DOB", DOB);
//		body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		if (responseObject.getStatusCode() == 400) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "DOB is null or empty");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//	}
//
//	/*
//	 * @Test(description = "Add Basic details without  EMAIL in Request body ",
//	 * dependsOnMethods = "TC009_FetchTheCreatedLeadDeatils", groups = {
//	 * "Regression", "Negative" })
//	 * 
//	 * @Owner(emailId = "<EMAIL>", isAutomated = true) public void
//	 * TC0095_AddBasicDetails_Without_Passing_EMAIL() {
//	 * 
//	 * LOGGER.info(" EMAIL is missing in Request body");
//	 * 
//	 * Map<String, String> queryParams = new HashMap<String, String>();
//	 * queryParams.put("entityType", "INDIVIDUAL"); queryParams.put("solution",
//	 * SOLUTION); queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//	 * queryParams.put("channel", CHANNEL);
//	 * 
//	 * Map<String, String> headers = new HashMap<String, String>(); headers =
//	 * commonHeaders;
//	 * 
//	 * Map<String, Object> body = new HashMap<String, Object>();
//	 * body.put("workflowSubOperation", "BASIC_DETAILS"); body.put("DOB", DOB);
//	 * body.put("PAN", PAN); //body.put("EMAIL", EMAIL);
//	 * 
//	 * Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams,
//	 * headers, body);
//	 * 
//	 * if (responseObject.getStatusCode() == 400) { LOGGER.info("displayMessage : "
//	 * + responseObject.jsonPath().getString("displayMessage"));
//	 * Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//	 * "EMAIL is null or empty");
//	 * LOGGER.info("Lead creation is blocked in this case"); }
//	 * 
//	 * 
//	 * }
//	 */
//
//	@Test(description = "Add Basic details without entityType in Param ", dependsOnMethods = "TC0027_AddBasicDetails_Without_Passing_DOB", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0028_AddBasicDetails_Without_Passing_EntityType() {
//
//		LOGGER.info("Not passing query parameter: entityType in URL");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		// queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//	}
//
//	@Test(description = "Add Basic details without passing solution in param", dependsOnMethods = "TC0028_AddBasicDetails_Without_Passing_EntityType", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0029_AddBasicDetails_Without_Passing_Solution() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		// queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//	}
//
//	@Test(description = "Add Basic details without passing Channel in param", dependsOnMethods = "TC0029_AddBasicDetails_Without_Passing_Solution", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0030_AddBasicDetails_Without_Passing_channel() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		// queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//	}
//
//	@Test(description = "Add Basic details without passing sso token in headers", dependsOnMethods = "TC0030_AddBasicDetails_Without_Passing_channel", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0031_AddBasicDetails_Without_Passing_SSOToken() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		// headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//	}
//
//	@Test(description = "Add Basic details", dependsOnMethods = "TC0031_AddBasicDetails_Without_Passing_SSOToken", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0032_AddBasicDetails() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//		Assert.assertEquals(responseObject.jsonPath().getBoolean("isLeadAlreadyExists"), true);
//
//	}
//
//	@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC0032_AddBasicDetails", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0033_FetchTheLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "BASIC_DETAILS");
//
//		LOGGER.info("Verify that detials are present in userAdditionalInfo");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.DOB"), DOB);
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.PAN"), PAN);
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.EMAIL"), EMAIL);
//
//	}
//
//	@Test(description = "OTP Callback with passing wrong lead id in param", dependsOnMethods = "TC0033_FetchTheLeadDeatils", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0034_OTPCallback_with_passing_wrong_LeadId() {
//
//		LOGGER.info(" Wrong lead id in param");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", "34243243242");
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "PAYTM_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "OTP_VERIFIED");
//		body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//		body.put("OTP_VALIDATION_TIMESTAMP", "2020-03-13T19:31:11.394+05:30");
//
//		Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 404) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Transaction Id not found");
//			LOGGER.info("Lead creation is blocked in this case");
//		}
//
//		lendingBaseClassObject.verifyResponseCodeAs404NotFound(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//	}
//
//	@Test(description = "OTP Callback without passing lead id in param", dependsOnMethods = "TC0034_OTPCallback_with_passing_wrong_LeadId", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0035_OTPCallback_Without_passing_LeadId() {
//
//		LOGGER.info("Not passing query parameter: Lead id ");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		// queryParams.put("leadId",leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "PAYTM_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "OTP_VERIFIED");
//		body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//		body.put("OTP_VALIDATION_TIMESTAMP", "2020-03-13T19:31:11.394+05:30");
//
//		Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//	}
//
//	@Test(description = "OTP Callback without passing solution in param", dependsOnMethods = "TC0035_OTPCallback_Without_passing_LeadId", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0036_OTPCallback_Without_passing_Solution() {
//
//		LOGGER.info("Not passing query parameter: Solution ");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		// queryParams.put("solution",SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "PAYTM_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "OTP_VERIFIED");
//		body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//		body.put("OTP_VALIDATION_TIMESTAMP", "2020-03-13T19:31:11.394+05:30");
//
//		Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//	}
//
//	@Test(description = "OTP Callback without passing jwt token in header", dependsOnMethods = "TC0036_OTPCallback_Without_passing_Solution", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0037_OTPCallback_Without_passing_Jwttoken() {
//
//		LOGGER.info("Not passing query parameter: Solution ");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		// headers=lendingBaseClassObject.setCallbackHeaders(token,custId,"PAYTM_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "OTP_VERIFIED");
//		body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//		body.put("OTP_VALIDATION_TIMESTAMP", "2020-03-13T19:31:11.394+05:30");
//
//		Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		LOGGER.info("OTP Callback is not Successfull in this case");
//
//	}
//
//	@Test(description = "OTP Callback with Invalid Solution in param", dependsOnMethods = "TC0037_OTPCallback_Without_passing_Jwttoken", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0038_OTPCallback_Invalid_solution() {
//
//		LOGGER.info("Invalid solution in param ");
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", "business_lending");
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "PAYTM_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "OTP_VERIFIED");
//		body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//		body.put("OTP_VALIDATION_TIMESTAMP", "2020-03-13T19:31:11.394+05:30");
//
//		Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs404NotFound(responseObject);
//		LOGGER.info("Lead creation is blocked in this case");
//
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//
//	}
//
//	@Test(description = "OTP Callback", dependsOnMethods = "TC0038_OTPCallback_Invalid_solution", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0039_OTPCallback() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "PAYTM_APP");
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "OTP_VERIFIED");
//		body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//		body.put("OTP_VALIDATION_TIMESTAMP", "2020-03-13T19:31:11.394+05:30");
//
//		Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//
//	@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC0039_OTPCallback", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0040_FetchLeadStage() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, "OTP_VERIFIED");
//
//	}
//
//	@Test(description = "Get the CKYC Status without passing entity type in param ", dependsOnMethods = "TC0040_FetchLeadStage", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0041_CheckCKYCStatus_without_passing_EntityType() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		// queryParams.put("entityType","INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", "DIY_P4B_APP");
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info(" Get ckyc status call is not Successfull in this case");
//
//	}
//
//	@Test(description = "Get the CKYC Status without passing Solution in param ", dependsOnMethods = "TC0041_CheckCKYCStatus_without_passing_EntityType", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0042_CheckCKYCStatus_without_passing_Solution() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		// queryParams.put("solution",SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", "DIY_P4B_APP");
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info(" Get ckyc status call is not Successfull in this case");
//
//	}
//
//	@Test(description = "Get the CKYC Status without passing Channel in param ", dependsOnMethods = "TC0042_CheckCKYCStatus_without_passing_Solution", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0043_CheckCKYCStatus_without_passing_Channel() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		// queryParams.put("channel","DIY_P4B_APP");
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info(" Get ckyc status call is not Successfull in this case");
//
//	}
//
//	@Test(description = "Get the CKYC Status without passing Channel in param ", dependsOnMethods = "TC0043_CheckCKYCStatus_without_passing_Channel", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0044_CheckCKYCStatus_without_passing_SolutionTypeLevel2() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		// queryParams.put("channel","DIY_P4B_APP");
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info(" Get ckyc status call is not Successfull in this case");
//
//	}
//
//	@Test(description = "Get the CKYC Status without passing Solution Type level 2 in Param  in param ", dependsOnMethods = "TC0044_CheckCKYCStatus_without_passing_SolutionTypeLevel2", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0045_CheckCKYCStatus_without_passing_SolutionTypeLevel2() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		// queryParams.put("solutionTypeLevel2",SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", "DIY_P4B_APP");
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		// Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"We
//		// could not fetch your KYC status details. Please try again later.");
//		LOGGER.info((responseObject.jsonPath().getString("refId")));
//		LOGGER.info(" Get ckyc status call is not Successfull in this case");
//
//	}
//
//	@Test(description = "Get the CKYC Status without passing sso token  in header ", dependsOnMethods = "TC0045_CheckCKYCStatus_without_passing_SolutionTypeLevel2", groups = {
//			"Regression", "Negative" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0046_CheckCKYCStatus_without_passing_SSOToken() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", "DIY_P4B_APP");
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		// headers.put("session_token",sessionToken);
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		LOGGER.info(" Get ckyc status call is not Successfull in this case");
//
//	}
//
//	@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC0046_CheckCKYCStatus_without_passing_SSOToken", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC0047_CheckCKYCStatus() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", "DIY_P4B_APP");
//		queryParams.put("dob", DOB);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//
//		Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//		verifyResponseCodeAs200OK(responseObject);
//		ckycStage = responseObject.jsonPath().getString("stage");
//		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//
//	}
//
//	@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC0047_CheckCKYCStatus", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC048_VerifyLeadStage() {
//
//		lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL, sessionToken, ckycStage);
//
//	}
//
//	@Test(description = "Upload selfie without passing solution in doc upload call", dependsOnMethods = "TC048_VerifyLeadStage", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC049_UploadSelfie_Without_Passing_Solution() throws InterruptedException {
//
//		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//				"INDIVIDUAL", "", SOLUTION_TYPE_LEVEL_2, sessionToken);
//
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		LOGGER.info("Selfied upload is not allowed if Param/headrs are missing");
//
//	}
//
//	@Test(description = "Upload selfie without passing solution Type level 2 in doc upload call", dependsOnMethods = "TC049_UploadSelfie_Without_Passing_Solution", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC050_UploadSelfie_Without_Passing_SolutionTypeLevel2() throws InterruptedException {
//
//		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//				"INDIVIDUAL", SOLUTION, "", sessionToken);
//
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		LOGGER.info(responseObject.jsonPath().get("displayMessage"));
//		LOGGER.info(responseObject.jsonPath().get("refId"));
//		LOGGER.info("Selfied upload is not allowed if Param/headrs are missing");
//
//	}
//
//	@Test(description = "Upload selfie with Invalid lead id  in doc upload call", dependsOnMethods = "TC050_UploadSelfie_Without_Passing_SolutionTypeLevel2", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC051_UploadSelfie_With_Passing_InvalidLeadid() throws InterruptedException {
//
//		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", "2342244", custId,
//				"INDIVIDUAL", SOLUTION, SOLUTION_TYPE_LEVEL_2, sessionToken);
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Selfied upload is not allowed if Param/headrs are missing");
//
//	}
//
//	@Test(description = "Upload selfie without passing Doc Provided in doc upload call", dependsOnMethods = "TC051_UploadSelfie_With_Passing_InvalidLeadid", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC052_UploadSelfie_Without_Passing_DocProvided() throws InterruptedException {
//
//		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("", "leadid", custId, "INDIVIDUAL",
//				SOLUTION, SOLUTION_TYPE_LEVEL_2, sessionToken);
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("Selfied upload is not allowed if Param/headrs are missing");
//
//	}
//	
//	
//	
//	@Test(description = "Upload selfie without Session token in doc upload call", dependsOnMethods = "TC052_UploadSelfie_Without_Passing_DocProvided", groups = {
//	"Regression" })
//@Owner(emailId = "<EMAIL>", isAutomated = true)
//public void TC053_UploadSelfie_Without_Passing_SessionToken() throws InterruptedException {
//
//Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", "leadid", custId, "INDIVIDUAL",
//		SOLUTION, SOLUTION_TYPE_LEVEL_2, "");
//lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//LOGGER.info("Selfied upload is not allowed if Param/headrs are missing");
//
//}
//	
//	
//	 @Test(description = "Upload selfie", dependsOnMethods = "TC053_UploadSelfie_Without_Passing_SessionToken",groups = {"Regression"})
//	  @Owner(emailId = "<EMAIL>",isAutomated = true)
//	    public void TC054_UploadSelfie() throws InterruptedException
//	    {
//		  
//	  	 Response responseObject= lendingBaseClassObject.utilityForDocumentUpload("selfie",leadId,custId,"INDIVIDUAL",SOLUTION,SOLUTION_TYPE_LEVEL_2,sessionToken);
//	  	
//        verifyResponseCodeAs200OK(responseObject);
//  	    
//       LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Document uploaded successfully.");
//	    Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//	    uuid=responseObject.jsonPath().getString("uuid");
//	    
//	    Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"),false);
//	    LOGGER.info("All docs uploaded : " +responseObject.jsonPath().getBoolean("allDocsUploaded"));
//	    Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"),false);
//	    Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"),true);
//	  	 
//	  	
//	    }
//	
//	
//	 @Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC054_UploadSelfie",groups = {"Regression"})
//	  @Owner(emailId = "<EMAIL>",isAutomated = true)
//	    public void TC055_VerifyUploadedDocument()
//	    {
//		  	      
//		 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE,SOLUTION,CHANNEL,sessionToken,ckycStage);
//		    Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docType"),"selfie");
//		    Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docProvided"),"selfie");
//		    Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docUUId"),uuid);
//	     
//	      
//	    }
//	 @Test(description = "Upload Customer Photo", dependsOnMethods = "TC055_VerifyUploadedDocument",groups = {"Regression"})
//	  @Owner(emailId = "<EMAIL>",isAutomated = true)
//	    public void TC056_UploadCustomerPhoto() throws InterruptedException
//	    {
//		    
//	  	   
//	  	 Response responseObject= lendingBaseClassObject.utilityForDocumentUpload("customerPhoto",leadId,custId,"INDIVIDUAL",SOLUTION,SOLUTION_TYPE_LEVEL_2,sessionToken);
//	  	
//       verifyResponseCodeAs200OK(responseObject);
//  	    
//       LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//	    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Document uploaded successfully.");
//	    Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//	    uuid=responseObject.jsonPath().getString("uuid");
//	    
//	    Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"),false);
//	    LOGGER.info("All docs uploaded : " +responseObject.jsonPath().getBoolean("allDocsUploaded"));
//	    Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"),false);
//	    Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"),true);
//	  	 
//	  	
//	    }
//	 
//	 @Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC056_UploadCustomerPhoto",groups = {"Regression"})
//	  @Owner(emailId = "<EMAIL>",isAutomated = true)
//	    public void TC057_VerifyUploadedDocument()
//	    {
//		  	      
//		 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE,SOLUTION,CHANNEL,sessionToken,ckycStage);
//		List<Object> docTypes=responseObject.jsonPath().getList("solutionDocSRO.docType");
//		docTypes.contains("customerPhoto");
//		List<Object> docProvided=responseObject.jsonPath().getList("solutionDocSRO.docProvided");
//		docProvided.contains("others");
//				     
//	      
//	    }
//
//	@Test(description = "BRE Validation Pending Callback without passing lead id in param",groups = {"Regression"},dependsOnMethods = "TC057_VerifyUploadedDocument")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC058_BREValidationPendingCallback_without_Passing_leadid()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		//queryParams.put("leadId",leadId);
//		queryParams.put("solution",SOLUTION);
//
//		token=lendingBaseClassObject.generateJwtToken(ISSUER,CLIENT_ID,custId);
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers=lendingBaseClassObject.setCallbackHeaders(token,custId,"PAYTM_APP");
//
//		Map<String,Object> body = new HashMap<String, Object>();
//		body.put("status","BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//
//		Response responseObject= lendingBaseClassObject.callbackBREValiadtion(queryParams, headers,body,true);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	}
//
//	@Test(description = "BRE Validation Pending Callback without passing Solution in param",groups = {"Regression"},dependsOnMethods = "TC058_BREValidationPendingCallback_without_Passing_leadid")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC059_BREValidationPendingCallback_without_Passing_Solution()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("leadId",leadId);
//		//queryParams.put("solution",SOLUTION);
//
//		token=lendingBaseClassObject.generateJwtToken(ISSUER,CLIENT_ID,custId);
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers=lendingBaseClassObject.setCallbackHeaders(token,custId,"PAYTM_APP");
//
//		Map<String,Object> body = new HashMap<String, Object>();
//		body.put("status","BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//
//		Response responseObject= lendingBaseClassObject.callbackBREValiadtion(queryParams, headers,body,true);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	}
//
//
//	@Test(description = "BRE Validation Pending Callback without passing token in header",groups = {"Regression"},dependsOnMethods = "TC059_BREValidationPendingCallback_without_Passing_Solution")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC060_BREValidationPendingCallback_without_Passing_token()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("leadId",leadId);
//		queryParams.put("solution",SOLUTION);
//
//		token=lendingBaseClassObject.generateJwtToken(ISSUER,CLIENT_ID,custId);
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers=lendingBaseClassObject.setCallbackHeaders("",custId,"PAYTM_APP");
//
//		Map<String,Object> body = new HashMap<String, Object>();
//		body.put("status","BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//
//		Response responseObject= lendingBaseClassObject.callbackBREValiadtion(queryParams, headers,body,true);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//	}
//
//
//	@Test(description = "BRE Validation Pending Callback without passing customer id in header",groups = {"Regression"},dependsOnMethods = "TC060_BREValidationPendingCallback_without_Passing_token")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC061_BREValidationPendingCallback_without_Passing_CustomerId()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("leadId",leadId);
//		queryParams.put("solution",SOLUTION);
//
//		token=lendingBaseClassObject.generateJwtToken(ISSUER,CLIENT_ID,custId);
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers=lendingBaseClassObject.setCallbackHeaders(token,"","PAYTM_APP");
//
//		Map<String,Object> body = new HashMap<String, Object>();
//		body.put("status","BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//
//		Response responseObject= lendingBaseClassObject.callbackBREValiadtion(queryParams, headers,body,true);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//	}
//
//	@Test(description = "BRE Validation Pending Callback without passing Status in body",groups = {"Regression"},dependsOnMethods = "TC061_BREValidationPendingCallback_without_Passing_CustomerId")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC062_BREValidationPendingCallback_without_Passing_Status_RequestBody()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("leadId",leadId);
//		queryParams.put("solution",SOLUTION);
//
//		token=lendingBaseClassObject.generateJwtToken(ISSUER,CLIENT_ID,custId);
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers=lendingBaseClassObject.setCallbackHeaders(token,custId,"PAYTM_APP");
//
//		Map<String,Object> body = new HashMap<String, Object>();
//		//body.put("status","BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//
//		Response responseObject= lendingBaseClassObject.callbackBREValiadtion(queryParams, headers,body,true);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	}
//
//
//
//
//	@Test(description = "BRE Validation Pending Callback",groups = {"Regression"},dependsOnMethods = "TC062_BREValidationPendingCallback_without_Passing_Status_RequestBody")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC063_BREValidationPendingCallback()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("leadId",leadId);
//		queryParams.put("solution",SOLUTION);
//
//		token=lendingBaseClassObject.generateJwtToken(ISSUER,CLIENT_ID,custId);
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers=lendingBaseClassObject.setCallbackHeaders(token,custId,"PAYTM_APP");
//
//		Map<String,Object> body = new HashMap<String, Object>();
//		body.put("status","BRE_VALIDATION_PENDING");
//		body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//
//		Response responseObject= lendingBaseClassObject.callbackBREValiadtion(queryParams, headers,body,true);
//
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
//
//	}
//
//	@Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC063_BREValidationPendingCallback")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC064_VerifyLeadStage()
//	{
//
//		Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE,SOLUTION,CHANNEL,sessionToken,"BRE_VALIDATION_PENDING");
//
//
//	}
//
//	@Test(description = "Fetch BRE Response call without passing solution in param",groups = {"Regression"},dependsOnMethods = "TC064_VerifyLeadStage")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC065_FetchBREResponse_without_passing_Solution()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("email", EMAIL);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		//queryParams.put("solution",SOLUTION);
//		queryParams.put("channel", "PAYTM_APP");
//
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers.put("session_token",sessionToken);
//		headers.put("Content-Type","application/json;charset=utf-8");
//
//
//		Response responseObject= lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//	}
//
//
//	@Test(description = "Fetch BRE Response call without passing entity type in param",groups = {"Regression"},dependsOnMethods = "TC065_FetchBREResponse_without_passing_Solution")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC066_FetchBREResponse_without_passing_entityType()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		//queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("email", EMAIL);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solution",SOLUTION);
//		queryParams.put("channel", "PAYTM_APP");
//
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers.put("session_token",sessionToken);
//		headers.put("Content-Type","application/json;charset=utf-8");
//
//
//		Response responseObject= lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//	}
//
//
//	@Test(description = "Fetch BRE Response call without passing channel in param",groups = {"Regression"},dependsOnMethods = "TC066_FetchBREResponse_without_passing_entityType")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC067_FetchBREResponse_without_passing_channel()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("email", EMAIL);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solution",SOLUTION);
//		//queryParams.put("channel", "PAYTM_APP");
//
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers.put("session_token",sessionToken);
//		headers.put("Content-Type","application/json;charset=utf-8");
//
//
//		Response responseObject= lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//	}
//
//
//
//
//
//	@Test(description = "Fetch BRE Response",groups = {"Regression"},dependsOnMethods = "TC067_FetchBREResponse_without_passing_channel")
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	public void TC068_FetchBREResponse()
//	{
//		Map<String,String> queryParams=new HashMap<String,String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("email", EMAIL);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("solution",SOLUTION);
//		queryParams.put("channel", "PAYTM_APP");
//
//
//		Map<String,String> headers=new HashMap<String,String>();
//		headers.put("session_token",sessionToken);
//		headers.put("Content-Type","application/json;charset=utf-8");
//
//
//		Response responseObject= lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//	}
//
//
//
//
//	 
//	 
//	 
//	
//	
//
//	/**
//	 * Method to set headers which are used in lead creation request
//	 * 
//	 * @return
//	 */
//	public Map<String, String> setcommonHeaders() {
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		// headers.put("Content-Type", "application/json;charset=utf-8");
//
//		return headers;
//	}
//
//	/**
//	 * Verify Response Code as 200 OK
//	 * 
//	 * @param responseObject
//	 */
//	public void verifyResponseCodeAs200OK(Response responseObject) {
//
//		LOGGER.info("Status Code : " + responseObject.getStatusCode());
//
//		Assert.assertEquals(responseObject.getStatusCode(), 200);
//
//	}
//
//}
