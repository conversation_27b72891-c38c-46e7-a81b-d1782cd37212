//package OCL.Lending.ConsumerLending;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingConstants;
//import Services.LendingService.LendingLeadStages;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//
//import com.opencsv.CSVWriter;
//import com.paytm.apitools.util.annotations.Owner;
//
//import Request.MerchantService.v1.TokenXMV;
//import io.restassured.response.Response;
//import org.apache.log4j.Logger;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import java.io.File;
//import java.io.FileWriter;
//import java.io.IOException;
//import java.sql.SQLException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//public class TestPersonalLoanHeroNegativeCases extends LendingBaseAPI {
//
//	private static final Logger LOGGER = Logger.getLogger(TestPersonalLoanHeroNegativeCases.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
//
//	String sessionToken = "";
//	String leadId = "";
//	String custId = "";
//	String agentNumber = "5123509080";
//	String agentPassword = "paytm@123";
//	String token = "";
//	String uuid = "";
//	String ckycStage = "";
//	String loanOffered = "";
//	String maxLoanAmount = "";
//	String authorisedMonthlyLimit = "";
//	String stage = "";
//	String code = "";
//	String tncName = "";
//	String url = "";
//	String uniqueIdentifier = "";
//	String md5 = "";
//	String codeSanctionLetter = "";
//	String tncNameSanctionLetter = "";
//	String urlSanctionLetter = "";
//	String uniqueIdentifierSanctionLetter = "";
//	String md5SanctionLetter = "";
//	String firstNameAsPerPan;
//	String lastNameAsPerPan;
//	String middleNameAsPerPan;
//	
//	Map<String, String> commonHeaders;
//	
//	@BeforeClass()
//	public void intitializeInputData() throws IOException {
//
//		LOGGER.info(" Before Suite Method for Agent Login ");
//		sessionToken = ApplicantToken(agentNumber, agentPassword);
//		LOGGER.info("Applicant Token for Lending : " + sessionToken);
//		commonHeaders = setcommonHeaders();
//
//	}
//
//		@Test(description = "Delete all existing leads of the number", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC001_DeleteExistingLead() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("mobile", agentNumber);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683");
//	
//			lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
//		}
//		
//		
//		@Test(description = "Verify whether there is any existing lead present or not for personal Loan V2 Solution", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC002_FetchLeadDeatils() {
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.LEAD_NOT_PRESENT.getStage());
//	
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");
//		}
//
//		@Test(description = "Create Personal Loan Lead without passing solution type in query params", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC003_CreatePersonalLoanLead_WithoutPassingSolutionType() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		}
//	
//		
//
//		
//		@Test(description = "Create Personal Loan Lead without passing entity type in query params",  dependsOnMethods = "TC003_CreatePersonalLoanLead_WithoutPassingSolutionType",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC004_CreatePersonalLoanLead_WithoutPassingEntityType() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//		
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		}
//	
//
//		@Test(description = "Create Personal Loan Lead without passing channel in query params", dependsOnMethods = "TC004_CreatePersonalLoanLead_WithoutPassingEntityType",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC005_CreatePersonalLoanLead_WithoutPassingChannel() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType",  LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		}
//		
//		
//		@Test(description = "Create Personal Loan Lead without passing session token in headers",dependsOnMethods = "TC005_CreatePersonalLoanLead_WithoutPassingChannel", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC006_CreatePersonalLoanLeadWithoutPassingSessionToken() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", "");
//			headers.put("Content-Type", "application/json;charset=utf-8");
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		}
//	
//
//		@Test(description = "Create Personal Loan Lead without passing product id in json request",dependsOnMethods = "TC006_CreatePersonalLoanLeadWithoutPassingSessionToken", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC007_CreatePersonalLoanLead_WithoutPassingProductIdInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_ID is null or empty");
//		}
//	
//		@Test(description = "Create Personal Loan Lead without passing product type in json request", dependsOnMethods = "TC007_CreatePersonalLoanLead_WithoutPassingProductIdInRequest",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC008_CreatePersonalLoanLead_WithoutPassingProductTypeInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//		
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_TYPE is null or empty");
//		}
//		@Test(description = "Create Personal Loan Lead without passing product version in request json",dependsOnMethods = "TC008_CreatePersonalLoanLead_WithoutPassingProductTypeInRequest", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC009_CreatePersonalLoanLead_WithoutPassingProductVersionInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//		
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PRODUCT_VERSION is null or empty");
//		}
//		
//		@Test(description = "Create Personal Loan Lead without passing base id in request json",dependsOnMethods = "TC009_CreatePersonalLoanLead_WithoutPassingProductVersionInRequest", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC010_CreatePersonalLoanLead_WithoutPassingLoanBaseIdInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//		
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "BASE_ID is null or empty");
//		}
//		
//		@Test(description = "Create Personal Loan Lead without passing lender id in request json",dependsOnMethods = "TC010_CreatePersonalLoanLead_WithoutPassingLoanBaseIdInRequest",  groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC011_CreatePersonalLoanLead_WithoutPassingLoanLenderIdInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LENDER_ID is null or empty");
//		}
//	
//		@Test(description = "Create Personal Loan Lead without passing whitelisting source in request json", dependsOnMethods = "TC011_CreatePersonalLoanLead_WithoutPassingLoanLenderIdInRequest", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC012_CreatePersonalLoanLead_WithoutPassingWhitelistingSourceInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//		
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "WHITELISTING_SOURCE is null or empty");
//		}
//
//		
//		@Test(description = "Create Personal Loan Lead without passing E-mandate in request json", dependsOnMethods = "TC012_CreatePersonalLoanLead_WithoutPassingWhitelistingSourceInRequest",  groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC013_CreatePersonalLoanLead_WithoutPassingEmandateInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "IS_EMANDATE_ELIGIBLE is null or empty");
//		}
//		
//		@Test(description = "Create Personal Loan Lead without passing paytm vintage value in request json", dependsOnMethods = "TC013_CreatePersonalLoanLead_WithoutPassingEmandateInRequest",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC014_CreatePersonalLoanLead_WithoutPassingPaytmVintageInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//	
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "IS_PAYTM_VINTAGE_OLDER_THAN_90D is null or empty");
//		}
//		
//		@Test(description = "Create Personal Loan Lead without passing lending dynamic tnc value in request json", dependsOnMethods = "TC014_CreatePersonalLoanLead_WithoutPassingPaytmVintageInRequest",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC015_CreatePersonalLoanLead_WithoutPassingLendingTncInRequest() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//		
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LENDING_DYNAMIC_TNC is null or empty");
//		}
//		
//
//		
//		@Test(description = "Create Personal Loan Lead with lender Hero fincorp",dependsOnMethods = "TC015_CreatePersonalLoanLead_WithoutPassingLendingTncInRequest" ,groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC016_CreatePersonalLoanLead() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = commonHeaders;
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("PRODUCT_ID", "11");
//			body.put("PRODUCT_TYPE", "PL");
//			body.put("PRODUCT_VERSION", "1");
//			body.put("LOAN_OFFER_ID", "PL_HERO_11_02_HERO_ad4981b");
//			body.put("BASE_ID", "PL_HERO_11_02");
//			body.put("LENDER_ID", "11");
//			body.put("WHITELISTING_SOURCE", "RISK");
//			body.put("IS_EMANDATE_ELIGIBLE", true);
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//			body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
//			body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
//		
//			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			if (responseObject.getStatusCode() == 200) {
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//				leadId = responseObject.jsonPath().getString("leadId");
//			}
//	
//			else {
//				LOGGER.info("Try to hit the API again");
//				responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//				leadId = responseObject.jsonPath().getString("leadId");
//			}
//	
//		}
//	
//		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC016_CreatePersonalLoanLead", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC017_FetchTheCreatedLeadDeatils() {
//	
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.LEAD_CREATED.getStage());
//			custId = responseObject.jsonPath().getString("custId");
//			
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_ID"),"11");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ORIGINAL_BASE_ID"),"PL_HERO_11_02");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_VERSION"),"1");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BASE_ID"),"PL_HERO_11_02");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDER_ID"),"11");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.SOLUTION_TYPE_LEVEL_2"),"HERO");
//					
//		}
//		
//			@Test(description = "Verify the callback for PPBL OTP without passing lead Id in query params", dependsOnMethods = "TC017_FetchTheCreatedLeadDeatils", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC018_PPBLOTPCallback_WithoutPassingLeadIdInQueryParams() throws InterruptedException {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		
//		}
//		
//			
//			@Test(description = "Verify the callback for PPBL OTP without passing solution in query params", dependsOnMethods = "TC018_PPBLOTPCallback_WithoutPassingLeadIdInQueryParams", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC019_PPBLOTPCallback_WithoutPassingSolutionInQueryParams() throws InterruptedException {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//	
//		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		
//		}
//			
//		
//			@Test(description = "Verify the callback for PPBL OTP without passing jwt token in headers", dependsOnMethods = "TC019_PPBLOTPCallback_WithoutPassingSolutionInQueryParams", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC020_PPBLOTPCallback_WithoutPassingJWTToken() throws InterruptedException {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders("", custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//			
//			@Test(description = "Verify the callback for PPBL OTP without passing cust id in headers", dependsOnMethods = "TC020_PPBLOTPCallback_WithoutPassingJWTToken", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC021_PPBLOTPCallback_WithoutPassingCustIdInHeaders() throws InterruptedException {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, "",  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//	
//			@Test(description = "Verify the callback for PPBL OTP without passing status in request json", dependsOnMethods = "TC021_PPBLOTPCallback_WithoutPassingCustIdInHeaders", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC022_PPBLOTPCallback_WithoutPassingStatus() throws InterruptedException {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//		}
//			
//			@Test(description = "Verify the callback for PPBL OTP passing invalid status in request json", dependsOnMethods = "TC022_PPBLOTPCallback_WithoutPassingStatus", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC023_PPBLOTPCallback_PassingInvalidStatus() throws InterruptedException {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFY");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//		
//		}
//			
//			@Test(description = "Verify the callback for PPBL OTP", dependsOnMethods = "TC023_PPBLOTPCallback_PassingInvalidStatus", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC024_PPBLOTPCallback() throws InterruptedException {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//		
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//		
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//		}
//			
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC024_PPBLOTPCallback", groups = {
//	"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC025_FetchTheCreatedLeadDeatils() {
//	
//	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage( LendingConstants.INDIVIDUAL_ENTITY_TYPE,  LendingConstants.PERSONAL_LOAN_V2_SOLUTION,  LendingConstants.PAYTM_APP_CHANNEL,
//		sessionToken, LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
//	  Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PPBL_TNC_ISSUER_IP"),"127.0.0.1");
//      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PPBL_TNC_TIMESTAMP"),"1584098137622");
//	
//	}
//		
//	
//	@Test(description = "Additional info PAN Update without passing solution type in headers", dependsOnMethods = "TC025_FetchTheCreatedLeadDeatils", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC026_AdditionalInfoPANUpdate_WithoutPassingSolutionTypeInHeaders() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("leadId",leadId );
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//	
//
//	}
//	
//	
//	@Test(description = "Additional info PAN Update without passing leadId in headers", dependsOnMethods = "TC026_AdditionalInfoPANUpdate_WithoutPassingSolutionTypeInHeaders", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC027_AdditionalInfoPANUpdate_WithoutPassingLeadIdInHeaders() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//	
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//	
//
//	}
//	
//	@Test(description = "Additional info PAN Update without passing jwt token in headers", dependsOnMethods = "TC027_AdditionalInfoPANUpdate_WithoutPassingLeadIdInHeaders", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC028_AdditionalInfoPANUpdate_WithoutPassingSolutionTypeInHeaders() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId",leadId );
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders("", custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//
//	
//
//	}
//	
//	
//
//	@Test(description = "Additional info PAN Update without passing custid in headers", dependsOnMethods = "TC028_AdditionalInfoPANUpdate_WithoutPassingSolutionTypeInHeaders", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC029_AdditionalInfoPANUpdate_WithoutPassingCustIdInHeaders() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId",leadId );
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		headers = lendingBaseClassObject.setCallbackHeaders(token, "",  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//
//	
//
//	}
//	
//	
//	@Test(description = "Additional info PAN Update without passing status message in request json", dependsOnMethods = "TC029_AdditionalInfoPANUpdate_WithoutPassingCustIdInHeaders", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC030_AdditionalInfoPANUpdate_WithoutPassingStatusMessageInRequest() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId",leadId );
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//	
//		body.put("status", "SUCCESS");
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Conditions data is NULL");
//	
//
//	
//
//	}
//	
//	@Test(description = "Additional info PAN Update passing invalid status message in request json", dependsOnMethods = "TC030_AdditionalInfoPANUpdate_WithoutPassingStatusMessageInRequest", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC031_AdditionalInfoPANUpdate_PassingInvalidStatusMessageInRequest() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId",leadId );
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO");
//		body.put("status", "SUCCESS");
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Conditions data is NULL");
//	
//
//	}
//	
//	
//	@Test(description = "Additional info PAN Update without passing status message in request json", dependsOnMethods = "TC031_AdditionalInfoPANUpdate_PassingInvalidStatusMessageInRequest", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC032_AdditionalInfoPANUpdate_WithoutPassingStatusInRequest() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId",leadId );
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//	
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//	
//
//	}
//	
//	
//	@Test(description = "Additional info PAN Update  passing invalid status message in request json", dependsOnMethods = "TC032_AdditionalInfoPANUpdate_WithoutPassingStatusInRequest", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC033_AdditionalInfoPANUpdate_PassingInvalidStatusInRequest() {
//		
//		Map<String, String> queryParams = new HashMap<String, String>();
//		
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId",leadId );
//		
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "FAILURE");
//		body.put("IS_PAN_VERIFIED", "TRUE");
//		body.put("IS_PAN_EDITABLE", "FALSE");
//		body.put("PAN_SOURCE", "CIR");
//		body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//		body.put("PAN", "**********");
//		body.put("F_NAME", "TOUCH");
//		body.put("M_NAME", "WOOD");
//		body.put("L_NAME", "LIMITED");
//		body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//	
//
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//	
//
//	}
//	
//			@Test(description = "Additional info PAN Update", dependsOnMethods = "TC033_AdditionalInfoPANUpdate_PassingInvalidStatusInRequest", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC034_AdiitionalInfoPANUpdate() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "SUCCESS");
//				body.put("IS_PAN_VERIFIED", "TRUE");
//				body.put("IS_PAN_EDITABLE", "FALSE");
//				body.put("PAN_SOURCE", "CIR");
//				body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
//				body.put("PAN", "**********");
//				body.put("F_NAME", "TOUCH");
//				body.put("M_NAME", "WOOD");
//				body.put("L_NAME", "LIMITED");
//				body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
//			
//		
//				Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);
//		
//				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//				Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
//		
//			}
//			
//			
//			@Test(description = "Additional info DOB Update without passing solution type", dependsOnMethods = "TC034_AdiitionalInfoPANUpdate", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC035_AdiitionalInfoDOBUpdate_WithoutPassingSolutionType() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//		
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "SUCCESS");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			}
//			@Test(description = "Additional info DOB Update without passing lead Id", dependsOnMethods = "TC035_AdiitionalInfoDOBUpdate_WithoutPassingSolutionType", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC036_AdiitionalInfoDOBUpdate_WithoutPassingLeadId() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//	
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "SUCCESS");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			}
//	
//			@Test(description = "Additional info DOB Update without passing jwt token", dependsOnMethods = "TC036_AdiitionalInfoDOBUpdate_WithoutPassingLeadId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC037_AdiitionalInfoDOBUpdate_WithoutPassingJWTToken() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders("", custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "SUCCESS");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			}
//			
//			@Test(description = "Additional info DOB Update without passing cust id", dependsOnMethods = "TC037_AdiitionalInfoDOBUpdate_WithoutPassingJWTToken", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC038_AdiitionalInfoDOBUpdate_WithoutPassingCustId() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, "",  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "SUCCESS");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			}
//			
//			@Test(description = "Additional info DOB Update without passing status message in json request", dependsOnMethods = "TC038_AdiitionalInfoDOBUpdate_WithoutPassingCustId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC039_AdiitionalInfoDOBUpdate_WithoutPassingStatusMessage() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//	
//				body.put("status", "SUCCESS");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Conditions data is NULL");
//
//
//			}
//	
//			@Test(description = "Additional info DOB Update  passing invalid status message in json request", dependsOnMethods = "TC039_AdiitionalInfoDOBUpdate_WithoutPassingStatusMessage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC040_AdiitionalInfoDOBUpdatePassingInvalidStatusMessage() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO");
//				body.put("status", "SUCCESS");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Conditions data is NULL");
//
//			}
//	
//			@Test(description = "Additional info DOB Update without passing status  in json request", dependsOnMethods = "TC040_AdiitionalInfoDOBUpdatePassingInvalidStatusMessage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC041_AdiitionalInfoDOBUpdate_WithoutPassingStatus() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//				
//			}
//	
//			@Test(description = "Additional info DOB Update  passing invalid status message in json request", dependsOnMethods = "TC041_AdiitionalInfoDOBUpdate_WithoutPassingStatus", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC042_AdiitionalInfoDOBUpdate_PassingInvalidStatusInRequest() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "FAILURE");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//
//
//			}
//	
//			@Test(description = "Additional info DOB Update ", dependsOnMethods = "TC042_AdiitionalInfoDOBUpdate_PassingInvalidStatusInRequest", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC043_AdiitionalInfoDOBUpdate() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
//				
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//				body.put("status", "SUCCESS");
//				body.put("IS_DOB_VERIFIED", "TRUE");
//				body.put("IS_DOB_EDITABLE", "FALSE");
//				body.put("DOB_SOURCE", "CIR");
//				body.put("DOB", "1996-01-12");
//			
//
//				Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);
//
//				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//				Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
//
//			}
//			
//			@Test(description = "Basic details update callback without using solution type", dependsOnMethods = "TC043_AdiitionalInfoDOBUpdate", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC044_BasicDetailsUpdateCallback_WithoutPassingSolutionType() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//			
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without using lead Id", dependsOnMethods = "TC044_BasicDetailsUpdateCallback_WithoutPassingSolutionType", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC045_BasicDetailsUpdateCallback_WithoutPassingLeadId() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without passing Authorization", dependsOnMethods = "TC045_BasicDetailsUpdateCallback_WithoutPassingLeadId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC046_BasicDetailsUpdateCallback_WithoutPassingAuthorization() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", "");
//				headers.put("Content-Type", "application/json;charset=utf-8");
//				headers.put("custId", custId);
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//
//			}
//			
//			@Test(description = "Basic details update callback without passing CustId", dependsOnMethods = "TC046_BasicDetailsUpdateCallback_WithoutPassingAuthorization", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC047_BasicDetailsUpdateCallback_WithoutPassingCustId() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", "");
//				headers.put("Content-Type", "application/json;charset=utf-8");
//				headers.put("custId", "");
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without passing status message in response", dependsOnMethods = "TC047_BasicDetailsUpdateCallback_WithoutPassingCustId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC048_BasicDetailsUpdateCallback_WithoutPassingStatusInJsonRequest() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//
//				
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without passing status message", dependsOnMethods = "TC048_BasicDetailsUpdateCallback_WithoutPassingStatusInJsonRequest", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC049_BasicDetailsUpdateCallback_WithoutPassingStatusMessage() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");				
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Conditions data is NULL");
//
//
//
//				
//
//			}
//			
//			
//			@Test(description = "Basic details update callback passing invalid status message", dependsOnMethods = "TC049_BasicDetailsUpdateCallback_WithoutPassingStatusMessage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC050_BasicDetailsUpdateCallback_PassingInvalidStatusMessage() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//
//				
//
//			}
//	
//			
//			@Test(description = "Basic details update callback passing invalid DOB", dependsOnMethods = "TC049_BasicDetailsUpdateCallback_WithoutPassingStatusMessage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC051_BasicDetailsUpdateCallback_PassingInvalidDOB() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", "12-01-1996");
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Dob");
//
//
//				
//
//			}
//	
//			
//			@Test(description = "Basic details update callback passing empty DOB", dependsOnMethods = "TC051_BasicDetailsUpdateCallback_PassingInvalidDOB", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC052_BasicDetailsUpdateCallback_PassingEmptydDOB() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", "");
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "DOB is null or empty");
//
//
//				
//
//			}
//			
//			@Test(description = "Basic details update callback passing  invalid PAN", dependsOnMethods = "TC052_BasicDetailsUpdateCallback_PassingEmptydDOB", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC053_BasicDetailsUpdateCallback_PassingInvalidPAN() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", "**********");
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid PAN number");
//
//
//				
//
//			}
//			
//			@Test(description = "Basic details update callback passing empty PAN", dependsOnMethods = "TC053_BasicDetailsUpdateCallback_PassingInvalidPAN", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC054_BasicDetailsUpdateCallback_PassingEmptyPAN() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", "");
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "PAN number is null or empty");
//
//
//				
//
//			}
//			
//			@Test(description = "Basic details update callback passing empty Email", dependsOnMethods = "TC054_BasicDetailsUpdateCallback_PassingEmptyPAN", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC055_BasicDetailsUpdateCallback_PassingEmptyEMAIL() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", "");
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "EMAIL is null or empty");
//
//
//				
//
//			}
//			
//			@Test(description = "Basic details update callback passing invalid Email", dependsOnMethods = "TC055_BasicDetailsUpdateCallback_PassingEmptyEMAIL", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC056_BasicDetailsUpdateCallback_PassingEmptyPAN() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", "shivangi.goswami");
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Email");
//
//			}
//			
//			@Test(description = "Basic details update callback without passing loan purpose key", dependsOnMethods = "TC056_BasicDetailsUpdateCallback_PassingEmptyPAN", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC057_BasicDetailsUpdateCallback_WithoutPassingLoanPurposeKey() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL",LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//		
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_PURPOSE_KEY is null or empty");
//
//
//				
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without passing loan purpose", dependsOnMethods = "TC057_BasicDetailsUpdateCallback_WithoutPassingLoanPurposeKey", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC058_BasicDetailsUpdateCallback_WithoutPassingLoanPurpose() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL",LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_PURPOSE is null or empty");
//
//
//				
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without passing loan user latitude", dependsOnMethods = "TC058_BasicDetailsUpdateCallback_WithoutPassingLoanPurpose", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC059_BasicDetailsUpdateCallback_WithoutPassingLoanUserLatitude() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//		
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_USER_LATITUDE is null or empty");
//				
//
//				
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without passing longitude", dependsOnMethods = "TC059_BasicDetailsUpdateCallback_WithoutPassingLoanUserLatitude", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC060_BasicDetailsUpdateCallback_WithoutPassingLoanUserLongitude() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_USER_LONGITUDE is null or empty");
//				
//
//			}
//			
//			@Test(description = "Basic details update callback without passing lender application id", dependsOnMethods = "TC060_BasicDetailsUpdateCallback_WithoutPassingLoanUserLongitude", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC061_BasicDetailsUpdateCallback_WithoutPassingLenderAppilicationId() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//			
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LENDER_APPLICATION_ID is null or empty");
//			
//
//			}
//			
//			
//			@Test(description = "Basic details update callback without passing lender customer Id", dependsOnMethods = "TC061_BasicDetailsUpdateCallback_WithoutPassingLenderAppilicationId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC062_BasicDetailsUpdateCallback_WithoutPassingLenderCustomerId() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);;
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LENDER_CUSTOMER_ID is null or empty");
//			
//
//			}
//			
//			@Test(description = "Basic details update callback without passing lender request id", dependsOnMethods = "TC062_BasicDetailsUpdateCallback_WithoutPassingLenderCustomerId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC063_BasicDetailsUpdateCallback_WithoutPassingLenderRequestId() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//		
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LENDER_REQUEST_ID is null or empty");
//			
//
//			}
//			
//			@Test(description = "Basic details update callback without passing static tnc url", dependsOnMethods = "TC063_BasicDetailsUpdateCallback_WithoutPassingLenderRequestId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC064_BasicDetailsUpdateCallback_WithoutPassingStaticTnc() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//			
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "STATIC_TNC_ACCEPT_URL is null or empty");
//			
//
//			}
//			
//			@Test(description = "Basic details update callback without passing static tnc version", dependsOnMethods = "TC064_BasicDetailsUpdateCallback_WithoutPassingStaticTnc", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC065_BasicDetailsUpdateCallback_WithoutPassingStaticTncVersion() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//		
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "STATIC_TNC_VERSION is null or empty");
//			
//
//			}
//			
//			@Test(description = "Basic details update callback", dependsOnMethods = "TC065_BasicDetailsUpdateCallback_WithoutPassingStaticTncVersion", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC066_BasicDetailsUpdateCallback() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId",leadId );
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "SUCCESS");
//				body.put("statusMessage", "BASIC_DETAILS");
//				body.put("EMAIL", LendingConstants.EMAIL_HERO);
//				body.put("DOB", LendingConstants.DOB_HERO);
//				body.put("PAN", LendingConstants.PAN_HERO);
//				body.put("LOAN_PURPOSE_KEY", "1");
//				body.put("LOAN_PURPOSE", "Medical");
//				body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
//				body.put("LOAN_USER_LATITUDE", "28.5166419");
//				body.put("LOAN_USER_LONGITUDE", "77.0251866");
//				body.put("LOAN_USER_PINCODE", "560076");
//				body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
//				body.put("LENDER_APPLICATION_ID", "11564143");
//				body.put("LENDER_CUSTOMER_ID", "14159653");
//				body.put("LENDER_REQUEST_ID", "12345999");
//				body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
//				body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
//				body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
//				body.put("STATIC_TNC_VERSION", "3");
//				body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//				body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
//				body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");
//
//				Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//
//				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//				Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());
//
//
//				
//
//			}
//				@Test(description = "Verify the data of lead created", dependsOnMethods = "TC066_BasicDetailsUpdateCallback", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC067_FetchTheCreatedLeadDeatils() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.BASIC_DETAILS.getStage());
//
//			       LOGGER.info("Verify that detials are present in userAdditionalInfo");
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.DOB"),LendingConstants.DOB_HERO);
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.PAN"),LendingConstants.PAN_HERO);
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.EMAIL"),LendingConstants.EMAIL_HERO);
//			      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_PAN_VERIFIED"),"TRUE");
//			      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PAN_SOURCE"),"CIR");
//			      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_PAN_EDITABLE"),"FALSE");
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"),"TOUCH WOOD LIMITED");
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"),"TOUCH");
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"),"WOOD");
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"),"LIMITED");
//					
//			}
//				
//				@Test(description = "Add occupation details without passing solution name in query params", dependsOnMethods = "TC067_FetchTheCreatedLeadDeatils", groups = { "Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC068_OccupationDetailsCallback_WithoutPassingSolutionNameInQuery() {
//					Map<String, String> queryParams = new HashMap<String, String>();
//					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				
//					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//					queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//					
//					Map<String, String> headers = new HashMap<String, String>();
//					headers = commonHeaders;
//
//					Map<String, Object> body = new HashMap<String, Object>();
//					body.put("workflowSubOperation", "OCCUPATION_DETAILS");
//					body.put("OCCUPATION_KEY", "1");
//					body.put("OCCUPATION", "Salaried");
//					body.put("EMPLOYER_ID", "24984");
//					body.put("EMPLOYER_NAME","Paytm");
//					body.put("BUSINESS_NAME", "Sai Properties");
//					body.put("ANNUAL_INCOME", "100000");
//					body.put("FATHER_NAME", "Vinay Goswami");
//					body.put("MOTHER_NAME", "Archana Goswami");
//
//					Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body,true);
//
//					lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//
//				}
//				
//				@Test(description = "Add occupation details without passing entity type in query names", dependsOnMethods = "TC068_OccupationDetailsCallback_WithoutPassingSolutionNameInQuery", groups = { "Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC069_OccupationDetailsCallback_WithoutPassingEntityType() {
//					Map<String, String> queryParams = new HashMap<String, String>();
//
//					queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//					queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//					
//					Map<String, String> headers = new HashMap<String, String>();
//					headers = commonHeaders;
//
//					Map<String, Object> body = new HashMap<String, Object>();
//					body.put("workflowSubOperation", "OCCUPATION_DETAILS");
//					body.put("OCCUPATION_KEY", "1");
//					body.put("OCCUPATION", "Salaried");
//					body.put("EMPLOYER_ID", "24984");
//					body.put("EMPLOYER_NAME","Paytm");
//					body.put("BUSINESS_NAME", "Sai Properties");
//					body.put("ANNUAL_INCOME", "100000");
//					body.put("FATHER_NAME", "Vinay Goswami");
//					body.put("MOTHER_NAME", "Archana Goswami");
//
//					Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body,true);
//
//					lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//
//				}
//				
//				@Test(description = "Add occupation details without passing session token in headers", dependsOnMethods = "TC069_OccupationDetailsCallback_WithoutPassingEntityType", groups = { "Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC070_OccupationDetailsCallback_WithoutPassingSessionToken() {
//					Map<String, String> queryParams = new HashMap<String, String>();
//					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//					queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//					queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//					
//					Map<String, String> headers = new HashMap<String, String>();
//					headers.put("session_token", "");
//
//
//					Map<String, Object> body = new HashMap<String, Object>();
//					body.put("workflowSubOperation", "OCCUPATION_DETAILS");
//					body.put("OCCUPATION_KEY", "1");
//					body.put("OCCUPATION", "Salaried");
//					body.put("EMPLOYER_ID", "24984");
//					body.put("EMPLOYER_NAME","Paytm");
//					body.put("BUSINESS_NAME", "Sai Properties");
//					body.put("ANNUAL_INCOME", "100000");
//					body.put("FATHER_NAME", "Vinay Goswami");
//					body.put("MOTHER_NAME", "Archana Goswami");
//
//					Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body,true);
//
//					lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//
//				}
//				
//				@Test(description = "Add occupation details without passing Occupation", dependsOnMethods = "TC070_OccupationDetailsCallback_WithoutPassingSessionToken", groups = { "Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC071_OccupationDetailsCallback_WithoutPassingOccupation() {
//					Map<String, String> queryParams = new HashMap<String, String>();
//					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//					queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//					queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//					
//					Map<String, String> headers = new HashMap<String, String>();
//					headers = commonHeaders;
//
//					Map<String, Object> body = new HashMap<String, Object>();
//					body.put("workflowSubOperation", "OCCUPATION_DETAILS");
//					body.put("OCCUPATION_KEY", "1");
//			
//					body.put("EMPLOYER_ID", "24984");
//					body.put("EMPLOYER_NAME","Paytm");
//					body.put("BUSINESS_NAME", "Sai Properties");
//					body.put("ANNUAL_INCOME", "100000");
//					body.put("FATHER_NAME", "Vinay Goswami");
//					body.put("MOTHER_NAME", "Archana Goswami");
//
//					Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body,true);
//
//					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "OCCUPATION is null or empty");
//			
//					
//					body.remove("OCCUPATION_KEY");
//					body.put("OCCUPATION", "Salaried");
//		
//
//					responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body,true);
//
//					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "OCCUPATION_KEY is null or empty");
//			
//			
//
//				}
//				
//			
//				
//				@Test(description = "Add occupation details", dependsOnMethods = "TC071_OccupationDetailsCallback_WithoutPassingOccupation", groups = { "Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC072_OccupationDetailsCallback() {
//					Map<String, String> queryParams = new HashMap<String, String>();
//					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//					queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//					queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//					
//					Map<String, String> headers = new HashMap<String, String>();
//					headers = commonHeaders;
//
//					Map<String, Object> body = new HashMap<String, Object>();
//					body.put("workflowSubOperation", "OCCUPATION_DETAILS");
//					body.put("OCCUPATION_KEY", "1");
//					body.put("OCCUPATION", "Salaried");
//					body.put("EMPLOYER_ID", "24984");
//					body.put("EMPLOYER_NAME","Paytm");
//					body.put("BUSINESS_NAME", "Sai Properties");
//					body.put("ANNUAL_INCOME", "100000");
//					body.put("FATHER_NAME", "Vinay Goswami");
//					body.put("MOTHER_NAME", "Archana Goswami");
//
//					Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body,true);
//
//					lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//
//				}
//				
//			
//				@Test(description = "Verify the data of lead created", dependsOnMethods = "TC072_OccupationDetailsCallback", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC073_FetchTheCreatedLeadDeatils() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.OCCUPATION_DETAILS.getStage());
//
//			       LOGGER.info("Verify that detials are present in userAdditionalInfo");
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MOTHER_NAME"),"Archana Goswami");
//			      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.FATHER_NAME"),"Vinay Goswami");
//
//					
//			}
//				
//				@Test(description = "BRE OTP Verification", dependsOnMethods = "TC073_FetchTheCreatedLeadDeatils", groups = {
//				"Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC074_BREOTPVerification() {
//				
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//				headers.put("channel", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "BRE_OTP_VERIFIED");
//				body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//
//				Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//
//				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//		}
//				
//				@Test(description = "Verify Lead stage", dependsOnMethods = "TC074_BREOTPVerification",groups = { "Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC075_VerifyLeadStage() {
//
//					Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//							sessionToken, LendingLeadStages.BRE_OTP_VERIFIED.getStage());
//
//					LOGGER.info("Verify that detials are present in userAdditionalInfo");
//					Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
//					Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
//					Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
//					Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
//					
//				}
//				
//		@Test(description = "BRE Validation Pending", dependsOnMethods = "TC075_VerifyLeadStage", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC076_BREValidationPending_WithoutPassingLeadId() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BRE_VALIDATION_PENDING");
//			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//		
//		}
//		
//			@Test(description = "BRE Validation Pending", dependsOnMethods = "TC076_BREValidationPending_WithoutPassingLeadId", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC077_BREValidationPending_WithoutPassingSolutionName() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//	
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BRE_VALIDATION_PENDING");
//			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//			verifyResponseCodeAs400BadRequest(responseObject);
//		
//	
//	}
//				
//			@Test(description = "BRE Validation Pending", dependsOnMethods = "TC077_BREValidationPending_WithoutPassingSolutionName", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC078_BREValidationPending_WithoutPassingAuthorizationToken() {
//				
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			headers.put("Authorization", "");
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BRE_VALIDATION_PENDING");
//			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);		
//			
//			}
//			
//				@Test(description = "BRE Validation Pending", dependsOnMethods = "TC078_BREValidationPending_WithoutPassingAuthorizationToken", groups = {
//				"Regression" })
//				@Owner(emailId = "<EMAIL>", isAutomated = true)
//				public void TC079_BREValidationPending_WithoutPassingCustId() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, "",LendingConstants.LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BRE_VALIDATION_PENDING");
//			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//			verifyResponseCodeAs401Unauthorized(responseObject);
//			
//		}
//				
//				@Test(description = "BRE Validation Pending", dependsOnMethods = "TC079_BREValidationPending_WithoutPassingCustId", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC080_BREValidationPending_PassingInvalidStatus() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BRE_VALIDATION");
//			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//		
//		}
//				
//				
//				@Test(description = "BRE Validation Pending", dependsOnMethods = "TC080_BREValidationPending_PassingInvalidStatus", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC081_BREValidationPending_PassingBlankStatus() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "");
//			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//		}
//				
//				@Test(description = "BRE Validation Pending", dependsOnMethods = "TC081_BREValidationPending_PassingBlankStatus", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC082_BREValidationPending_HappyCase() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BRE_VALIDATION_PENDING");
//			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//		
//			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//		}
//				
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC082_BREValidationPending_HappyCase",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC083_VerifyLeadStage() {
//		
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.BRE_VALIDATION_PENDING.getStage());
//		
//		}
//		
//		 @Test(description = "Fetch BRE Response", dependsOnMethods = "TC083_VerifyLeadStage", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC084_FetchBREResponse_WithoutSolutionName() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//	}
//		 
//		 @Test(description = "Fetch BRE Response", dependsOnMethods = "TC084_FetchBREResponse_WithoutSolutionName", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC085_FetchBREResponse_WithoutPassingEntityType() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//
//	}
//
//		 
//		 @Test(description = "Fetch BRE Response", dependsOnMethods = "TC085_FetchBREResponse_WithoutPassingEntityType", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC086_FetchBREResponse_WithoutPassingChannelName() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	
//		
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	}
//
//		 
//		 @Test(description = "Fetch BRE Response", dependsOnMethods = "TC086_FetchBREResponse_WithoutPassingChannelName", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC087_FetchBREResponse_WithoutPassingSessionToken() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//
//		Map<String, String> headers = new HashMap<String, String>();
//		
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//
//	}
//
//		 
//		 @Test(description = "Fetch BRE Response", dependsOnMethods = "TC087_FetchBREResponse_WithoutPassingSessionToken", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC088_FetchBREResponse_HappyCase() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//
//	}
//    
//		 @Test(description = "Verify Lead stage", dependsOnMethods = "TC088_FetchBREResponse_HappyCase", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC089_VerifyLeadStage() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//				sessionToken, LendingLeadStages.BRE_RESPONSE_AWAITED.getStage());
//
//	}
//		 
//			@Test(description = "Check BRE Response", dependsOnMethods = "TC089_VerifyLeadStage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC090_CheckBREResponse_WithoutPassingSolutionName() throws SQLException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//
//				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//				
//				Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//             
//			}
//
//			@Test(description = "Check BRE Response", dependsOnMethods = "TC089_VerifyLeadStage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC090_CheckBREResponse_WithoutPassingEntityType() throws SQLException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//				
//				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//				
//				Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//             
//			}
//			
//			
//			@Test(description = "Check BRE Response", dependsOnMethods = "TC089_VerifyLeadStage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC090_CheckBREResponse_WithoutPassingChannelName() throws SQLException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//				
//				Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//				
//				lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//             
//			}
//			
//			
//			@Test(description = "Check BRE Response", dependsOnMethods = "TC090_CheckBREResponse_WithoutPassingChannelName", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC091_CheckBREResponse_WithoutPassingSessionToken() throws SQLException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//
//				Map<String, String> headers = new HashMap<String, String>();
//		
//				headers.put("Content-Type", "application/json;charset=utf-8");
//				
//				Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//				
//				lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//             
//			}
//			
//			
//			@Test(description = "Check BRE Response", dependsOnMethods = "TC091_CheckBREResponse_WithoutPassingSessionToken", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC092_CheckBREResponse_HappyCase() throws SQLException {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//
//				
//		        Response responseObject=null;
//				// If response of BRE status API is BRE success then get loan amount
//				
//				for(int i=0;i<20;i++)
//					
//				{
//					responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//					LOGGER.info("Status Code : " + responseObject.getStatusCode());
//					
//					if (responseObject.getStatusCode() == 200
//							&& responseObject.jsonPath().getString("stage").equals("BRE_SUCCESS"))
//					{
//					
//						stage = responseObject.jsonPath().getString("stage");
//						loanOffered = responseObject.jsonPath().getString("loanOffered");
//						maxLoanAmount = responseObject.jsonPath().getString("maxLoanAmount");
//				
//						
//						break;
//					
//					}
//					
//					LOGGER.info("BRE successful");
//					
//					
//				}
//
//				if (responseObject.getStatusCode() == 200
//					&& responseObject.jsonPath().getString("stage").equals("BRE_ERROR"))
//				
//				LOGGER.info("Getting BRE Error");
//						
//				if(responseObject.getStatusCode() == 500
//								&&responseObject.jsonPath().getString("displayMessage").equals("We could not fetch your details. Please try again later."))
//							
//				LOGGER.info("Could not fetch the details");
//						
//						
//				
//				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE_SUCCESS.getStage());
//									
//							
//					}
//
//
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC092_CheckBREResponse_HappyCase", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC093_VerifyLeadStage() {
//
//				Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//						sessionToken, LendingLeadStages.BRE_SUCCESS.getStage());
//
//			}
//			
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC093_VerifyLeadStage", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC094_AcceptLoanOffer_WithoutPassingSolutionName() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC094_AcceptLoanOffer_WithoutPassingSolutionName", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC095_AcceptLoanOffer_WithoutPassingEntityType() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC095_AcceptLoanOffer_WithoutPassingEntityType", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC096_AcceptLoanOffer_WithoutPassingChannel() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC096_AcceptLoanOffer_WithoutPassingChannel", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC097_AcceptLoanOffer_WithoutPassingSessionToken() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//	
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		}
//
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC097_AcceptLoanOffer_WithoutPassingSessionToken", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC098_AcceptLoanOffer_PassingInvalidWorkflowSubOperation() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid request sub operation %s is not supported");
//	
//		}
//
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC098_AcceptLoanOffer_PassingInvalidWorkflowSubOperation", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC099_AcceptLoanOffer_WithoutPassingLoanTenure() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_TENURE ");
//	
//		}
//			 
//			 
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC099_AcceptLoanOffer_WithoutPassingLoanTenure", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC100_AcceptLoanOffer_WithoutPassingLoanTenureUnit() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_TENURE_UNIT is null or empty");
//	
//		}
//			 
//			 
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC100_AcceptLoanOffer_WithoutPassingLoanTenureUnit", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC101_AcceptLoanOffer_WithoutPassingLoanAmountInNumber() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_AMOUNT_IN_NUMBER ");
//	
//		}
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC101_AcceptLoanOffer_WithoutPassingLoanAmountInNumber", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC102_AcceptLoanOffer_WithoutPassingLoanAmountInWords() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_AMOUNT_IN_WORDS is null or empty");
//	
//		}
//
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC102_AcceptLoanOffer_WithoutPassingLoanAmountInWords", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC103_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallment() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_EQUATED_MONTHLY_INSTALLMENT ");
//	
//		}
//
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC103_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallment", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC104_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallmentInWords() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS is null or empty");
//	
//		}
//			 
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC104_AcceptLoanOffer_WithoutPassingLoanEquatedDailyInstallmentInWords", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC105_AcceptLoanOffer_WithoutPassingLoanRateOfInterest() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_RATE_OF_INTEREST ");
//	
//		}
//			 
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC105_AcceptLoanOffer_WithoutPassingLoanRateOfInterest", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC106_AcceptLoanOffer_WithoutPassingLoanProcessingFees() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_PROCESSING_FEE ");
//	
//		}
//			 
//			 
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC106_AcceptLoanOffer_WithoutPassingLoanProcessingFees", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC107_AcceptLoanOffer_WithoutPassingLoanProcessingFeeGST() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "");
//			body.put("LOAN_DISBURSAL_AMOUNT", "9194");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_PROCESSING_FEES_GSTINCLUDED ");
//	
//		}
//			 
//			 
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC107_AcceptLoanOffer_WithoutPassingLoanProcessingFeeGST", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC108_AcceptLoanOffer_WithoutPassingLoanDisbursementAmount() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
//			body.put("LOAN_RATE_OF_INTEREST", "30");
//			body.put("LOAN_PROCESSING_FEE", "500");
//			body.put("PROCESSING_FEE_RATE", "5.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
//			body.put("LOAN_DISBURSAL_AMOUNT", "");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "16");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid value of LOAN_DISBURSAL_AMOUNT ");
//	
//		}
//			 
//			 
//			 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC108_AcceptLoanOffer_WithoutPassingLoanDisbursementAmount", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC109_AcceptLoanOffer_HappyCase() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
//			body.put("LOAN_TENURE", "6");
//			body.put("LOAN_TENURE_UNIT", "MONTH");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "25000");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Twenty Five Thousands");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "4526");
//			body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Four Thousand Five Hundred Twenty Six");
//			body.put("LOAN_RATE_OF_INTEREST", "29");
//			body.put("LOAN_PROCESSING_FEE", "1000");
//			body.put("PROCESSING_FEE_RATE", "4.0");
//			body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1180");
//			body.put("LOAN_DISBURSAL_AMOUNT", "23540");
//			body.put("STAMP_DUTY_CHARGES", "200");
//			body.put("BROKEN_PERIOD_INTEREST", "80");
//			
//		
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("isLeadAlreadyExists"), "true");
//	
//		}
//			 
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC109_AcceptLoanOffer_HappyCase", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC110_VerifyLeadStage() {
//
//				Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//						sessionToken, LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
//
//			}
//			
//				@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC110_VerifyLeadStage", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC111_CheckCKYCStatus_WithoutPassingEntityType() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//		
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//
//				@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC111_CheckCKYCStatus_WithoutPassingEntityType", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC112_CheckCKYCStatus_WithoutPassingSolutionName() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//
//				@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC112_CheckCKYCStatus_WithoutPassingSolutionName", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC113_CheckCKYCStatus_WithoutPassingChannel() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//		}
//
//				@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC113_CheckCKYCStatus_WithoutPassingChannel", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC114_CheckCKYCStatus_WithoutPassingSessionToken() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//
//				@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC114_CheckCKYCStatus_WithoutPassingSessionToken", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC115_CheckCKYCStatus_HappyCase() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//		
//			Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			ckycStage = responseObject.jsonPath().getString("stage");
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//		
//		}
//
//		@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC115_CheckCKYCStatus_HappyCase", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC116_VerifyLeadStage() {
//		
//			lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, sessionToken, ckycStage);
//		
//		}
//		
//		@Test(description = "Upload selfie", dependsOnMethods = "TC116_VerifyLeadStage", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC117_UploadSelfie_WithoutPassingDocProvided() throws InterruptedException {
//		
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		}
//		
//		@Test(description = "Upload selfie", dependsOnMethods = "TC117_UploadSelfie_WithoutPassingDocProvided", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC118_UploadSelfie_WithoutPassingLeadId() throws InterruptedException {
//		
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", "abc", custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		}
//		
//		
//
//	
//		
//		@Test(description = "Upload selfie", dependsOnMethods = "TC118_UploadSelfie_WithoutPassingLeadId", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC119_UploadSelfie_WithoutPassingSolutionType() throws InterruptedException {
//		
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE,"", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
//		
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage").contains("Failed to upload document "),true);
//			Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "0");
//		
//		}
//		
//		@Test(description = "Upload selfie", dependsOnMethods = "TC119_UploadSelfie_WithoutPassingSolutionType", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC120_UploadSelfie_WithoutPassingSolutionTypeLevel2() throws InterruptedException {
//		
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PERSONAL_LOAN_V2_SOLUTION, "", sessionToken);
//		
//			lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failure in creating Document record in DB.");
//		}
//		
//		@Test(description = "Upload selfie without file in body",dependsOnMethods = "TC120_UploadSelfie_WithoutPassingSolutionTypeLevel2",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC121_UploadSelfie_WithoutPassing_ImageFile()  {
//			
//			LOGGER.info("Upload selfie without file in body");
//		
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("docProvided", "selfie");
//	        queryParams.put("extType", "png");
//	        queryParams.put("leadId", leadId);
//	        queryParams.put("custId", custId);
//	        queryParams.put("entityType", 	LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	        queryParams.put("solutionType",LendingConstants.PERSONAL_LOAN_V2_SOLUTION );
//	        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//	        queryParams.put("category", "NonTransport");
//	        queryParams.put("solutionTypeLevel3", "Unsecured_Short_term_Loan_Simplified");
//
//	        File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
//	       
//	        Map<String, String> headers = new HashMap<String, String>();
//	        headers.put("Content-Type", "multipart/form-data");
//	        headers.put("session_token", sessionToken);
//	        headers.put("custId",custId);
//
//	   
//			try {
//				Response responseObject = UploadDocument(queryParams, headers, uploadFile);
//			} catch (Exception e) {
//				
//				e.printStackTrace();
//			}
//	     
//		
//		}
//		
//		@Test(description = "Upload selfie passing invalid solution type in query parameters", dependsOnMethods = "TC121_UploadSelfie_WithoutPassing_ImageFile",groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC122_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams() throws InterruptedException {
//		
//
//			LOGGER.info("Upload selfie passing invalid solution type in query parameters");
//		Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//				LendingConstants.INDIVIDUAL_ENTITY_TYPE,"abc", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
//		
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		
//		
//		
//		
//		}
//		
//		
//		@Test(description = "Upload selfie", dependsOnMethods = "TC122_UploadSelfie_Passing_Invalid_SolutionTypeInQueryParams", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC123_UploadSelfie() throws InterruptedException {
//		
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
//		
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//			uuid = responseObject.jsonPath().getString("uuid");
//		
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//			LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//		
//		}
//		
//		@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC123_UploadSelfie", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC124_VerifyUploadedDocument() {
//		
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, ckycStage);
//			Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docType"), "selfie");
//			Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docProvided"), "selfie");
//			Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docUUId"), uuid);
//		
//		}
//		
//		@Test(description = "Upload Customer Photo", dependsOnMethods = "TC124_VerifyUploadedDocument", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC125_UploadCustomerPhoto() throws InterruptedException {
//		
//			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
//					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
//		
//			verifyResponseCodeAs200OK(responseObject);
//		
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//			uuid = responseObject.jsonPath().getString("uuid");
//		
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//			LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//		
//		}
//
//		@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC125_UploadCustomerPhoto", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC126_VerifyUploadedDocument() {
//		
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, ckycStage);
//			List<Object> docTypes = responseObject.jsonPath().getList("solutionDocSRO.docType");
//			docTypes.contains("customerPhoto");
//			List<Object> docProvided = responseObject.jsonPath().getList("solutionDocSRO.docProvided");
//			docProvided.contains("others");
//		
//		}
//
//			@Test(description = "CKYC Callback", dependsOnMethods = "TC126_VerifyUploadedDocument", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC127_CKYCCallback_WithoutPassingSolutionName() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//			
//				queryParams.put("leadId", leadId);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "CKYC_VERIFIED");
//				body.put("status", "SUCCESS");
//				body.put("cKycId", "4353435454356");
//				body.put("firstName", "TOUCH");
//				body.put("middleName", "WOOD");
//				body.put("thirdName", "LIMITED");
//				body.put("email", "<EMAIL>");
//				body.put("type", "SELFIE");
//				body.put("percentage", "100");
//				body.put("addressline1", "A3, saket");
//				body.put("addressline2", ",148 civil lines");
//				body.put("city", "noida");
//				body.put("state", "UP");
//				body.put("pincode", "201010");
//				body.put("dob", LendingConstants.DOB_HERO);
//				body.put("gender", "male");
//				body.put("pan", LendingConstants.PAN_HERO);
//				body.put("ckycSuccessMode", "SEARCH_BY_AADHAR");
//			
//				Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//				verifyResponseCodeAs400BadRequest(responseObject);
//			}
//			
//			@Test(description = "CKYC Callback", dependsOnMethods = "TC127_CKYCCallback_WithoutPassingSolutionName", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC128_CKYCCallback_WithoutPassingLeadId() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "CKYC_VERIFIED");
//				body.put("status", "SUCCESS");
//				body.put("cKycId", "4353435454356");
//				body.put("firstName", "TOUCH");
//				body.put("middleName", "WOOD");
//				body.put("thirdName", "LIMITED");
//				body.put("email", "<EMAIL>");
//				body.put("type", "SELFIE");
//				body.put("percentage", "100");
//				body.put("addressline1", "A3, saket");
//				body.put("addressline2", ",148 civil lines");
//				body.put("city", "noida");
//				body.put("state", "UP");
//				body.put("pincode", "201010");
//				body.put("dob", LendingConstants.DOB_HERO);
//				body.put("gender", "male");
//				body.put("pan", LendingConstants.PAN_HERO);
//				body.put("ckycSuccessMode", "SEARCH_BY_AADHAR");
//			
//				Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//				
//				verifyResponseCodeAs400BadRequest(responseObject);
//			}
//
//			@Test(description = "CKYC Callback", dependsOnMethods = "TC128_CKYCCallback_WithoutPassingLeadId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC129_CKYCCallback_WithoutPassingAuthorization() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				queryParams.put("leadId", leadId);
//			
//				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", "");
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "CKYC_VERIFIED");
//				body.put("status", "SUCCESS");
//				body.put("cKycId", "4353435454356");
//				body.put("firstName", "TOUCH");
//				body.put("middleName", "WOOD");
//				body.put("thirdName", "LIMITED");
//				body.put("email", "<EMAIL>");
//				body.put("type", "SELFIE");
//				body.put("percentage", "100");
//				body.put("addressline1", "A3, saket");
//				body.put("addressline2", ",148 civil lines");
//				body.put("city", "noida");
//				body.put("state", "UP");
//				body.put("pincode", "201010");
//				body.put("dob", LendingConstants.DOB_HERO);
//				body.put("gender", "male");
//				body.put("pan", LendingConstants.PAN_HERO);
//				body.put("ckycSuccessMode", "SEARCH_BY_AADHAR");
//			
//				Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//			
//				verifyResponseCodeAs401Unauthorized(responseObject);
//			}
//
//		
//		
//		@Test(description = "CKYC Callback", dependsOnMethods = "TC129_CKYCCallback_WithoutPassingAuthorization", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC0130_CKYCCallback() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "CKYC_VERIFIED");
//			body.put("status", "SUCCESS");
//			body.put("cKycId", "4353435454356");
//			body.put("firstName", "TOUCH");
//			body.put("middleName", "WOOD");
//			body.put("thirdName", "LIMITED");
//			body.put("email", "<EMAIL>");
//			body.put("type", "SELFIE");
//			body.put("percentage", "100");
//			body.put("addressline1", "A3, saket");
//			body.put("addressline2", ",148 civil lines");
//			body.put("city", "noida");
//			body.put("state", "UP");
//			body.put("pincode", "201010");
//			body.put("dob", LendingConstants.DOB_HERO);
//			body.put("gender", "male");
//			body.put("pan", LendingConstants.PAN_HERO);
//			body.put("ckycSuccessMode", "SEARCH_BY_AADHAR");
//		
//			Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//		
//			verifyResponseCodeAs200OK(responseObject);
//		
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PAN_VERIFIED.getStage());
//		
//		}
//
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC0130_CKYCCallback", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC131_VerifyLeadStage() {
//		
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.PAN_VERIFIED.getStage());
//		
//			LOGGER.info("Verify that detials are present in userAdditionalInfo");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
//			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
//			LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//			Assert.assertEquals(
//					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),
//					"FALSE");
//			Assert.assertEquals(
//					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),
//					"FALSE");
//		
//		}
//	
//		@Test(description = "Second BRE Initaite without passing lead Id ", dependsOnMethods = "TC131_VerifyLeadStage", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC132_SecondBREInitiateCallback_WithoutPassingLeadId() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "SECOND_BRE_REQUEST_INVOKED");
//		
//		
//			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//		}
//		
//		
////		@Test(description = "Second BRE Initaite ", dependsOnMethods = "TC132_SecondBREInitiateCallback_WithoutPassingLeadId", groups = { "Regression" })
////		@Owner(emailId = "<EMAIL>", isAutomated = true)
////		public void TC133_SecondBREInitiateCallback_WithoutPassingSolutionName() {
////			
////			Map<String, String> queryParams = new HashMap<String, String>();
////			queryParams.put("solution","");
////			queryParams.put("leadId", leadId);
////		
////			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId);
////		
////			Map<String, String> headers = new HashMap<String, String>();
////			headers.put("Authorization", token);
////			headers.put("Content-Type", "application/json");
////			headers.put("custId", custId);
////			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
////		
////			Map<String, Object> body = new HashMap<String, Object>();
////			body.put("status", "SECOND_BRE_REQUEST_INVOKED");
////		
////		
////			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
////		
////
////			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
////			
////		}
//		
//		@Test(description = "Second BRE Initaite ", dependsOnMethods = "TC132_SecondBREInitiateCallback_WithoutPassingLeadId", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC134_SecondBREInitiateCallback_WithoutPassingAuthorizationToken() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution",LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", "");
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "SECOND_BRE_REQUEST_INVOKED");
//		
//		
//			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
//		
//
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		}
//		
//		
//		@Test(description = "Second BRE Initaite ", dependsOnMethods = "TC134_SecondBREInitiateCallback_WithoutPassingAuthorizationToken", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC135_SecondBREInitiateCallback_WithoutPassingCustId() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution",LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, "",LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "SECOND_BRE_REQUEST_INVOKED");
//		
//		
//			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
//		
//
//			lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//			
//		}
//		
//		
//		@Test(description = "Second BRE Initaite ", dependsOnMethods = "TC135_SecondBREInitiateCallback_WithoutPassingCustId", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC136_SecondBREInitiateCallback_PassingInvalidStatus() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution",LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "SECOND_BRE_REQUEST");
//		
//		
//			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
//		
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//			
//		}
//		
//		
//		@Test(description = "Second BRE Initaite ", dependsOnMethods = "TC136_SecondBREInitiateCallback_PassingInvalidStatus", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC137_SecondBREInitiateCallback_PassingBlankStatus() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution",LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "");
//		
//		
//			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
//		
//
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//			
//		}
//		
//		
//		@Test(description = "Second BRE Initaite Callback", dependsOnMethods = "TC137_SecondBREInitiateCallback_PassingBlankStatus", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC138_SecondBREInitiateCallback() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "SECOND_BRE_REQUEST_INVOKED");
//		
//		
//			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
//		
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			
//		}
//		
//		
//		
//		@Test(description = "Second BRE Initaite Callback", dependsOnMethods = "TC138_SecondBREInitiateCallback", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC139_SecondBREInitiateCallback() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "SECOND_BRE_REQUEST_INVOKED");
//		
//		
//			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
//		
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Stage Invalid :: Callback Status [SECOND_BRE_REQUEST_INVOKED] Active Stage [SECOND_BRE_INITIATED]");
//			
//		}
//		
//		
//	@Test(description = "Verify Lead stage", dependsOnMethods = "TC139_SecondBREInitiateCallback", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC140_VerifyLeadStage() {
//	
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//				sessionToken, LendingLeadStages.SECOND_BRE_INITIATED.getStage());
//	
//	
//		
//	}
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC140_VerifyLeadStage", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC141_SecondBREStatusCallback_WithoutPassingLeadId() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", "abc");
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_BRE_SUCCESS");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs404NotFound(responseObject);
//		
//	}
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC141_SecondBREStatusCallback_WithoutPassingLeadId", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC142_SecondBREStatusCallback_WithoutPassingSolutionName() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution","");
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_BRE_SUCCESS");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//	
//
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//		
//	}
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC142_SecondBREStatusCallback_WithoutPassingSolutionName", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC143_SecondBREStatusCallback_WithoutPassingAuthorization() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", "");
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_BRE_SUCCESS");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//	}
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC143_SecondBREStatusCallback_WithoutPassingAuthorization", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC144_SecondBREStatusCallback_WithoutPassingCustId() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, "",LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_BRE_SUCCESS");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//	}
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC144_SecondBREStatusCallback_WithoutPassingCustId", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC145_SecondBREStatusCallback_PassingInvalidStatusMessage() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//	}
//	
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC145_SecondBREStatusCallback_PassingInvalidStatusMessage", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC146_SecondBREStatusCallback_PassingBlankStatusMessage() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//	}
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC146_SecondBREStatusCallback_PassingBlankStatusMessage", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC147_SecondBREStatusCallback_PassingBlankStatus() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_BRE_SUCCESS");
//		body.put("status", "");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	}
//	
//	@Test(description = "Second BRE status Callback", dependsOnMethods = "TC146_SecondBREStatusCallback_PassingBlankStatusMessage", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC148_SecondBREStatusCallback_HappyCase() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_BRE_SUCCESS");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//		
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//	}
//	
//	@Test(description = "Second BRE Initaite Callback", dependsOnMethods = "TC148_SecondBREStatusCallback_HappyCase", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC149_SecondBREStatusCallback_ReHit() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "SECOND_BRE_SUCCESS");
//		body.put("status", "SUCCESS");
//		body.put("BASE_ID", "PL_1001148796");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
//	
//		Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
//		
//	
//		lendingBaseClassObject.verifyResponseCodeAs500InternalServerError(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Update Failed, Please Retry After Some Time");
//		
//	}
//	@Test(description = "Verify Lead stage", dependsOnMethods = "TC149_SecondBREStatusCallback_ReHit", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC150_VerifyLeadStage() {
//	
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//				sessionToken, LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
//	
//	
//		
//	}
//	
//	@Test(description = "Additional Data Capture", dependsOnMethods = "TC150_VerifyLeadStage", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC151_AdditionalDataCapture_WithoutPassingLeadId() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", "");
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "ADDITIONAL_DATA_CAPTURED");
//		body.put("BUSINESS_EMAIL", "<EMAIL>");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
//		body.put("MARITAL_STATUS", "MARRIED");
//	
//	
//		Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs404NotFound(responseObject);
//		
//	}
//	
//	@Test(description = "Additional Data Capture", dependsOnMethods = "TC151_AdditionalDataCapture_WithoutPassingLeadId", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC152_AdditionalDataCapture_WithoutPassingSolutionName() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", "");
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "ADDITIONAL_DATA_CAPTURED");
//		body.put("BUSINESS_EMAIL", "<EMAIL>");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
//		body.put("MARITAL_STATUS", "MARRIED");
//	
//	
//		Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs404NotFound(responseObject);
//		
//	}
//	
//	@Test(description = "Additional Data Capture", dependsOnMethods = "TC152_AdditionalDataCapture_WithoutPassingSolutionName", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC153_AdditionalDataCapture_WithoutPassingAuthorization() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", "");
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "ADDITIONAL_DATA_CAPTURED");
//		body.put("BUSINESS_EMAIL", "<EMAIL>");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
//		body.put("MARITAL_STATUS", "MARRIED");
//	
//	
//		Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//	}
//	
//	@Test(description = "Additional Data Capture", dependsOnMethods = "TC153_AdditionalDataCapture_WithoutPassingAuthorization", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC154_AdditionalDataCapture_WithoutPassingCustId() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", "");
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "ADDITIONAL_DATA_CAPTURED");
//		body.put("BUSINESS_EMAIL", "<EMAIL>");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
//		body.put("MARITAL_STATUS", "MARRIED");
//	
//	
//		Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs401Unauthorized(responseObject);
//		
//	}
//	
//	@Test(description = "Additional Data Capture", dependsOnMethods = "TC154_AdditionalDataCapture_WithoutPassingCustId", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC155_AdditionalDataCapture_WithoutPassingStatus() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "");
//		body.put("BUSINESS_EMAIL", "<EMAIL>");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
//		body.put("MARITAL_STATUS", "MARRIED");
//	
//	
//		Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Failed To Validate Request");
//		
//	}
//	
//	@Test(description = "Additional Data Capture", dependsOnMethods = "TC155_AdditionalDataCapture_WithoutPassingStatus", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC156_AdditionalDataCapture_PassingInvalidStatus() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "ADDITIONAL_");
//		body.put("BUSINESS_EMAIL", "<EMAIL>");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
//		body.put("MARITAL_STATUS", "MARRIED");
//	
//	
//		Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Invalid Status");
//		
//	}
//	
//	@Test(description = "Additional Data Capture", dependsOnMethods = "TC156_AdditionalDataCapture_PassingInvalidStatus", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC157_AdditionalDataCapture_HappyCase() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("leadId", leadId);
//	
//		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "ADDITIONAL_DATA_CAPTURED");
//		body.put("BUSINESS_EMAIL", "<EMAIL>");
//		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
//		body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
//		body.put("MARITAL_STATUS", "MARRIED");
//	
//	
//		Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
//	
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//	}
//	
//	@Test(description = "Verify Lead stage", dependsOnMethods = "TC157_AdditionalDataCapture_HappyCase", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC158_VerifyLeadStage() {
//	
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//				sessionToken, LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
//	
//		LOGGER.info("Verify that detials are present in userAdditionalInfo");
//		Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MARITAL_STATUS"), "MARRIED");
//		
//	}
//	
//			@Test(description = "CKYC name update in SAI", dependsOnMethods = "TC158_VerifyLeadStage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC159_UpdateKYCNameInSAI() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("leadId", leadId);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//			body.put("status", "SUCCESS");
//			body.put("CKYC_NAME", "TestBeneficiary");
//			
//			Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);
//			
//			verifyResponseCodeAs200OK(responseObject);
//			
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
//			
//			}
//			
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC159_UpdateKYCNameInSAI", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC160_SaveBankDetails_WithoutPassingBankAccountNumber() throws InterruptedException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//		
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("bankName", "PAYTM BANK");
//			body.put("bankAccountNumber", "");
//			body.put("ifsc", "PYTM0123456");
//			body.put("bankAccountHolderName", "Shivangi Goswami");
//			body.put("EMANDATE_TYPE", "Internet Banking");
//			
//			Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);
//			
//			lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//
//			}	
//			
//			
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC160_SaveBankDetails_WithoutPassingBankAccountNumber", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC161_SaveBankDetails_WithoutPassingIFSCCode() throws InterruptedException {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//	
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//		body.put("EMANDATE_TYPE", "Internet Banking");
//		
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs400BadRequest(responseObject);
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "We are unable to process your request. Please try again after sometime to continue.");
//
//		}	
//			
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC161_SaveBankDetails_WithoutPassingIFSCCode", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC162_SaveBankDetails() throws InterruptedException {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//	
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//		body.put("EMANDATE_TYPE", "Net Banking");
//		
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);
//		
//		if (responseObject.getStatusCode() == 200) {
//		
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
//			stage=LendingLeadStages.BANKING_ACTION_DONE.getStage();
//		
//		}
//		
//		else {
//			
//			for (int i = 1; i < 4; i++) {
//				LOGGER.info("Again hitting with same data: retry-count: " + i);
//				responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//				
//				if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
//				{
//					stage=responseObject.jsonPath().getString("stage");
//					break;
//				}
//					
//			}
//		
//		}
//
//		}	
//			
//			@Test(description = "Enter the bank details", dependsOnMethods = "TC162_SaveBankDetails", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC163_SaveBankDetails_Rehit() throws InterruptedException {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//	
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//		body.put("EMANDATE_TYPE", "Internet Banking");
//		
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);
//		
//		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Bank details are already verified.");
//		Assert.assertEquals(responseObject.jsonPath().getString("nameMatchSuccess"), "true");
//		
//		
//			}
//			
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC163_SaveBankDetails_Rehit", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC164_VerifyLeadStage() {
//		
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//			sessionToken, stage);
//		Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.EMANDATE_TYPE"),
//				"Net Banking");
//		
//		}
//			
//			
//				@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC164_VerifyLeadStage", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC165_EmandateCallback_WithoutPassingLeadId() throws InterruptedException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", "");
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "EMANDATE_SUCCESS");
//		
//			Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//		
//			verifyResponseCodeAs404NotFound(responseObject);
//		
//		}
//				
//				@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC165_EmandateCallback_WithoutPassingLeadId", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC166_EmandateCallback_WithoutPassingSolutionName() throws InterruptedException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", "");
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "EMANDATE_SUCCESS");
//		
//			Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//		
//			verifyResponseCodeAs404NotFound(responseObject);
//		
//		}
//				
//				@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC166_EmandateCallback_WithoutPassingSolutionName", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC167_EmandateCallback_WithoutPassingAuthorizationToken() throws InterruptedException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", "");
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "EMANDATE_SUCCESS");
//		
//			Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//		
//			verifyResponseCodeAs401Unauthorized(responseObject);
//		
//		}
//				
//				
//				@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC167_EmandateCallback_WithoutPassingAuthorizationToken", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC168_EmandateCallback_WithoutPassingCustId() throws InterruptedException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", "");
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "EMANDATE_SUCCESS");
//		
//			Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//		
//			verifyResponseCodeAs401Unauthorized(responseObject);
//			
//		
//		}
//			
//				@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC168_EmandateCallback_WithoutPassingCustId", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC169_EmandateCallback() throws InterruptedException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "EMANDATE_SUCCESS");
//		
//			Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//		
//			verifyResponseCodeAs200OK(responseObject);
//		
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//		}
//			
//			
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC169_EmandateCallback", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC170_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.EMANDATE_SUCCESS.getStage());
//			
//			}
//			
//			@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC170_VerifyLeadStage", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC171_FetchDynamicTnc_WithoutPassingLeadId() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", "");
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//			
//				Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//				
//				verifyResponseCodeAs200OK(responseObject);
//			
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				Assert.assertEquals( responseObject.jsonPath().getString("meta.status"),"failed") ;
//				
//			
//			}
//			
//			@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC171_FetchDynamicTnc_WithoutPassingLeadId", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC172_FetchDynamicTnc() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//			
//				Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//			
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//			
//					code = responseObject.jsonPath().getString("data.state.code");
//					tncName = responseObject.jsonPath().getString("data.state.tncName");
//					url = responseObject.jsonPath().getString("data.state.url");
//					uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//					md5 = responseObject.jsonPath().getString("data.state.md5");
//				}
//			
//			}
//			
//				@Test(description = "Fetch Dynamic T and C Sanction Letter", dependsOnMethods = "TC172_FetchDynamicTnc", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC173_FetchDynamicTncSanctionLetter_PassingIncorrectTNCType() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("tncType", "LOAN_SANCTION");
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//		
//			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//
//			verifyResponseCodeAs200OK(responseObject);
//		
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			Assert.assertEquals( responseObject.jsonPath().getString("meta.status"),"failed") ;
//		
//			
//			
//		
//		}
//		
//			@Test(description = "Fetch Dynamic T and C Sanction Letter", dependsOnMethods = "TC173_FetchDynamicTncSanctionLetter_PassingIncorrectTNCType", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC174_FetchDynamicTncSanctionLetter() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("tncType", "LOAN_SANCTION_TNC");
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//			
//				Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//			
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//			
//					codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
//					tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
//					urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
//					uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//					md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
//				}
//			
//			}
//			
//			@Test(description = "Verify submit application", dependsOnMethods = "TC174_FetchDynamicTncSanctionLetter", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC175_SubmitApplication_WithoutPassingLeadId() throws InterruptedException {
//			
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", "");
//			
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Content-Type", "application/json");
//				headers.put("session_token", sessionToken);
//			
//				Map<String, Object> body = new HashMap<String, Object>();
//			
//				body.put("tncName", tncName);
//				body.put("accept", 1);
//				body.put("uniqueIdentifier", uniqueIdentifier);
//				body.put("md5", md5);
//			
//				
//				body.put("tncName1", tncNameSanctionLetter);
//				body.put("accept1", 1);
//			
//				body.put("uniqueIdentifier1", uniqueIdentifierSanctionLetter);
//				body.put("md51", md5SanctionLetter);
//				body.put("LOAN_FINAL_BROKEN_PI", "30");
//				body.put("LOAN_FINAL_AMOUNT_PAYABLE", "500");
//				body.put("LOAN_FINAL_DISBURSEMENT_AMOUNT", "22222");
//			
//				Response responseObject = lendingBaseClassObject.submitApplicationHero(queryParams, headers, body,LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			
//				verifyResponseCodeAs200OK(responseObject);
//				
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.jsonPath().getString("meta.status").equals("failed")) 
//					Assert.assertEquals(responseObject.jsonPath().getString("meta.failure_reason"), "Could not save vetted tnc ");
//				
//			
//			
//			}
//			
//				@Test(description = "Verify submit application", dependsOnMethods = "TC175_SubmitApplication_WithoutPassingLeadId", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC176_SubmitApplication() throws InterruptedException {
//		
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Content-Type", "application/json");
//			headers.put("session_token", sessionToken);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//		
//			body.put("tncName", tncName);
//			body.put("accept", 1);
//			body.put("uniqueIdentifier", uniqueIdentifier);
//			body.put("md5", md5);
//		
//			
//			body.put("tncName1", tncNameSanctionLetter);
//			body.put("accept1", 1);
//		
//			body.put("uniqueIdentifier1", uniqueIdentifierSanctionLetter);
//			body.put("md51", md5SanctionLetter);
//			body.put("LOAN_FINAL_BROKEN_PI", "30");
//			body.put("LOAN_FINAL_AMOUNT_PAYABLE", "500");
//			body.put("LOAN_FINAL_DISBURSEMENT_AMOUNT", "22222");
//		
//			Response responseObject = lendingBaseClassObject.submitApplicationHero(queryParams, headers, body,LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
//		
//			{
//				LOGGER.info("Try again");
//				responseObject = lendingBaseClassObject.submitApplicationHero(queryParams, headers, body,LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//		
//			}
//		
//			else if (responseObject.jsonPath().getString("meta.stage").contentEquals("LMS_POST")) {
//				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//				Assert.assertEquals(responseObject.jsonPath().getString("meta.subStage"), "APPLICATION_PENDING");
//				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//				Assert.assertEquals(responseObject.jsonPath().getString("data.state"), "Loan Application is Accepted");
//		
//			}
//		
//		}
//				
//				
//				@Test(description = "Verify submit application", dependsOnMethods = "TC176_SubmitApplication", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC177_SubmitApplication_Rehit() throws InterruptedException {
//		
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Content-Type", "application/json");
//			headers.put("session_token", sessionToken);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//		
//			body.put("tncName", tncName);
//			body.put("accept", 1);
//			body.put("uniqueIdentifier", uniqueIdentifier);
//			body.put("md5", md5);
//		
//			
//			body.put("tncName1", tncNameSanctionLetter);
//			body.put("accept1", 1);
//		
//			body.put("uniqueIdentifier1", uniqueIdentifierSanctionLetter);
//			body.put("md51", md5SanctionLetter);
//			body.put("LOAN_FINAL_BROKEN_PI", "30");
//			body.put("LOAN_FINAL_AMOUNT_PAYABLE", "500");
//			body.put("LOAN_FINAL_DISBURSEMENT_AMOUNT", "22222");
//		
//			Response responseObject = lendingBaseClassObject.submitApplicationHero(queryParams, headers, body,LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
//				Assert.assertEquals(responseObject.jsonPath().getString("meta.failure_reason"), "Could not save vetted tnc ");
//			
//		
//			
//		
//		}
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC177_SubmitApplication_Rehit", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC178_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//					sessionToken, LendingLeadStages.APPLICATION_PENDING.getStage());
//			
//			}
//			
//				@Test(description = "Sheet upload from panel", dependsOnMethods = "TC178_VerifyLeadStage", groups = {
//				"Regression" })
//		      @Owner(emailId = "<EMAIL>", isAutomated = true)
//		     public void TC179_UploadSheetONPanel() throws InterruptedException, IOException {
//		
//			TokenXMV tokenXMW = new TokenXMV();
//			Response responseObject = MiddlewareServicesObject.v1Token("9560526665", "paytm@123");
//			String XMWToken = responseObject.getHeader("Set-Cookie").toString();
//		
//			System.out.println("XMW token is :" + XMWToken);
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Content-Type", "multipart/form-data");
//			headers.put("Cookie", XMWToken);
//		
//			File fileUpload = new File(
//					"src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/BusinessSampleFile.csv");
//			FileWriter outputfile = new FileWriter(fileUpload);
//			CSVWriter writer = new CSVWriter(outputfile);
//			String[] header = { "Lead Id", "Business status ", "Rejection Reason\n" };
//			writer.writeNext(header);
//			String[] data1 = { leadId, "APPROVED", "N/A" };
//			writer.writeNext(data1);
//			writer.flush();
//			writer.close();
//		
//			responseObject = lendingBaseClassObject.uploadSheetFromPanel(headers, fileUpload, "BUSINESS_STATUS");
//			if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
//					.contentEquals(" has been successfully uploaded")) {
//				LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
//				verifyResponseCodeAs200OK(responseObject);
//				Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
//		
//			}
//		
//		}
//	
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC179_UploadSheetONPanel", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC180_VerifyLeadStage() {
//				
//		
//		
//		     Response responseObject = null;
//		
//			 Map<String,String> queryParams=new HashMap<String,String>();
//		        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//		        queryParams.put("solution",LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//		
//		        Map<String,String> headers=new HashMap<String,String>();
//		        headers.put("session_token",sessionToken);
//		
//		for(int i=0;i<6;i++)
//		        {
//		        responseObject= fetchLeadDetails(queryParams, headers);
//		
//			        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
//			        {
//			            LOGGER.info("Verify Lead Stage "+responseObject.jsonPath().getString("stage"));
//			           break;
//		
//			        }
//			           
//		        
//		        }
//		
//		  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
//		
//			}
//	
//				
//			
//	/**
//	 * Method to set headers which are used in lead creation request
//	 * 
//	 * @return
//	 */
//	public Map<String, String> setcommonHeaders() {
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json;charset=utf-8");
//
//		return headers;
//	}
//	
//}
