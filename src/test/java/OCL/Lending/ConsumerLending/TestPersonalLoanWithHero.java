package OCL.Lending.ConsumerLending;

import Request.MerchantService.v1.TokenXMV;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.opencsv.CSVWriter;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestPersonalLoanWithHero extends LendingBaseAPI {

	private static final Logger LOGGER = LogManager.getLogger(TestPersonalLoanWithHero.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();

	String sessionToken = "";
	String leadId = "";
	String custId = "";
	String agentNumber = "5123509080";
	String agentPassword = "paytm@123";
	String token = "";
	String uuid = "";
	String ckycStage = "";
	String loanOffered = "";
	String maxLoanAmount = "";
	String authorisedMonthlyLimit = "";
	String stage = "";
	String code = "";
	String tncName = "";
	String url = "";
	String uniqueIdentifier = "";
	String md5 = "";
	String codeSanctionLetter = "";
	String tncNameSanctionLetter = "";
	String urlSanctionLetter = "";
	String uniqueIdentifierSanctionLetter = "";
	String md5SanctionLetter = "";
	String firstNameAsPerPan;
	String lastNameAsPerPan;
	String middleNameAsPerPan;
	String LMS_SECRET = "fd61f785-e867-471b-90c6-1447b4331712";
	
	Map<String, String> commonHeaders;
	
	@BeforeClass()
	public void intitializeInputData() throws IOException {

		LOGGER.info(" Before Suite Method for Agent Login ");
		sessionToken = ApplicantToken(agentNumber, agentPassword);
		LOGGER.info("Applicant Token for Lending : " + sessionToken);
		commonHeaders = setcommonHeaders();

	}

	@Test(description = "Delete all existing leads of the number", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC001_DeleteExistingLead() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("mobile", agentNumber);

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683");

		lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
	}

	@Test(description = "Verify whether there is any existing lead present or not for personal Loan V2 Solution", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC002_FetchLeadDeatils() {
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
				sessionToken, LendingLeadStages.LEAD_NOT_PRESENT.getStage());

		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");
	}

	@Test(description = "Create Personal Loan Lead with lender Hero fincorp", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC003_CreatePersonalLoanLead() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("PRODUCT_ID", "11");
		body.put("PRODUCT_TYPE", "PL");
		body.put("PRODUCT_VERSION", "1");
		body.put("BASE_ID", "PL_1001148796");
		body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
		body.put("LENDER_ID", "11");
		body.put("WHITELISTING_SOURCE", "RISK");
		body.put("IS_EMANDATE_ELIGIBLE", true);
		body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
		body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_hero_v2");
		body.put("LENDING_DYNAMIC_TNC", "loan_agreement_pl_hero_v2");
	
		Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		if (responseObject.getStatusCode() == 200) {
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
			leadId = responseObject.jsonPath().getString("leadId");
		}

		else {
			LOGGER.info("Try to hit the API again");
			responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
			leadId = responseObject.jsonPath().getString("leadId");
		}

	}

	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC003_CreatePersonalLoanLead", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC004_FetchTheCreatedLeadDeatils() {

		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
				sessionToken, LendingLeadStages.LEAD_CREATED.getStage());
		custId = responseObject.jsonPath().getString("custId");
		
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_ID"),"11");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ORIGINAL_BASE_ID"),"PL_1001148796");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_VERSION"),"1");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BASE_ID"),"PL_1001148796");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDER_ID"),"11");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.SOLUTION_TYPE_LEVEL_2"),"HERO");
				
	}
	
			@Test(description = "Verify the callback for PPBL OTP", dependsOnMethods = "TC004_FetchTheCreatedLeadDeatils", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC005_PPBLOTPCallback() throws InterruptedException {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
		
		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "PPBL_TNC_VERIFIED");
		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
		
		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
		
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		
		}
				
		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC005_PPBLOTPCallback", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC006_FetchTheCreatedLeadDeatils() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage( LendingConstants.INDIVIDUAL_ENTITY_TYPE,  LendingConstants.PERSONAL_LOAN_V2_SOLUTION,  LendingConstants.PAYTM_APP_CHANNEL,
			sessionToken, LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
		  Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PPBL_TNC_ISSUER_IP"),"127.0.0.1");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PPBL_TNC_TIMESTAMP"),"1584098137622");
		
		}
		@Test(description = "Additional info PAN Update", dependsOnMethods = "TC006_FetchTheCreatedLeadDeatils", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC007_AdiitionalInfoPANUpdate() {
			
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			queryParams.put("leadId",leadId );
			
			token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("IS_PAN_VERIFIED", "TRUE");
			body.put("IS_PAN_EDITABLE", "FALSE");
			body.put("PAN_SOURCE", "CIR");
			body.put("PAN_VALIDATION_TIMESTAMP", "2021-02-12T13:25:28.887+05:30");
			body.put("PAN", LendingConstants.PAN_HERO);
			body.put("F_NAME", "TOUCH");
			body.put("M_NAME", "WOOD");
			body.put("L_NAME", "LIMITED");
			body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
		

			Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body);

			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PPBL_TNC_VERIFIED.getStage());

		}
		
		@Test(description = "Additional info DOB Update", dependsOnMethods = "TC007_AdiitionalInfoPANUpdate", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC008_AdiitionalInfoDOBUpdate() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			queryParams.put("leadId",leadId );
			
			token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = lendingBaseClassObject.setCallbackHeaders(token, custId,  LendingConstants.PAYTM_APP_CHANNEL);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("IS_DOB_VERIFIED", "TRUE");
			body.put("IS_DOB_EDITABLE", "FALSE");
			body.put("DOB_SOURCE", "CIR");
			body.put("DOB", LendingConstants.DOB_HERO);
		

			Response responseObject = lendingBaseClassObject.updateAdditionalDOBDetails(queryParams, headers, body, true);

			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PPBL_TNC_VERIFIED.getStage());

		}
	@Test(description = "Basic details update callback", dependsOnMethods = "TC008_AdiitionalInfoDOBUpdate", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC009_BasicDetailsUpdateCallback() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("leadId",leadId );
		
		Map<String, String> headers = new HashMap<String, String>();
		headers = commonHeaders;

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "SUCCESS");
		body.put("statusMessage", "BASIC_DETAILS");
		body.put("EMAIL", LendingConstants.EMAIL_HERO);
		body.put("DOB", LendingConstants.DOB_HERO);
		body.put("PAN", LendingConstants.PAN_HERO);
		body.put("LOAN_PURPOSE_KEY", "1");
		body.put("LOAN_PURPOSE", "Medical");
		body.put("LOAN_PURPOSE_DESCRIPTION", "Some medical reasons");
		body.put("LOAN_USER_LATITUDE", "28.637012");
		body.put("LOAN_USER_LONGITUDE", "77.334832");
		body.put("LOAN_USER_PINCODE", "201010");
		body.put("LOAN_USER_ADDRESS_TYPE", "RESIDENTIAL");
		body.put("LENDER_APPLICATION_ID", "11564143");
		body.put("LENDER_CUSTOMER_ID", "14159653");
		body.put("LENDER_REQUEST_ID", "12345999");
		body.put("LENDER_APPLICATION_TIMESTAMP", "2021-02-12 18:42:08.943IST");
		body.put("STATIC_TNC_SETNAME", "loanStaticTnc");
		body.put("STATIC_TNC_ACCEPT_URL", "https://p-y.tm/xxxxxx");
		body.put("STATIC_TNC_VERSION", "3");
		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
		body.put("STATIC_TNC_ACCEPTANCE_IP", "***********");
		body.put("STATIC_TNC_ACCEPTANCE_TIMESTAMP", "2021-02-12T16:28:28.887+05:30");

		Response responseObject = lendingBaseClassObject.addBasicDetailsHero(queryParams, headers, body,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BASIC_DETAILS.getStage());


		

	}
		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC009_BasicDetailsUpdateCallback", groups = {
		"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC010_FetchTheCreatedLeadDeatils() {
	
	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
			sessionToken, LendingLeadStages.BASIC_DETAILS.getStage());

	       LOGGER.info("Verify that detials are present in userAdditionalInfo");
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.DOB"),LendingConstants.DOB_HERO);
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.PAN"),LendingConstants.PAN_HERO);
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.EMAIL"),LendingConstants.EMAIL_HERO);
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_PAN_VERIFIED"),"TRUE");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PAN_SOURCE"),"CIR");
	      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_PAN_EDITABLE"),"FALSE");
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"),"TOUCH WOOD LIMITED");
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"),"TOUCH");
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"),"WOOD");
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"),"LIMITED");
			
	}
		@Test(description = "Add occupation details", dependsOnMethods = "TC010_FetchTheCreatedLeadDeatils", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC011_OccupationDetailsCallback() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers = commonHeaders;

			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowSubOperation", "OCCUPATION_DETAILS");
			body.put("OCCUPATION_KEY", "1");
			body.put("OCCUPATION", "Salaried");
			body.put("EMPLOYER_ID", "24984");
			body.put("EMPLOYER_NAME","Paytm");
			body.put("BUSINESS_NAME", "Sai Properties");
			body.put("ANNUAL_INCOME", "100000");
			body.put("FATHER_NAME", "Vinay Goswami");
			body.put("MOTHER_NAME", "Archana Goswami");

			Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body,true);

			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
	

		}
		
		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC011_OccupationDetailsCallback", groups = {
		"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC012_FetchTheCreatedLeadDeatils() {
	
	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
			sessionToken, LendingLeadStages.OCCUPATION_DETAILS.getStage());

	       LOGGER.info("Verify that detials are present in userAdditionalInfo");
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MOTHER_NAME"),"Archana Goswami");
	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.FATHER_NAME"),"Vinay Goswami");

			
	}
		
		@Test(description = "BRE OTP Verification", dependsOnMethods = "TC012_FetchTheCreatedLeadDeatils", groups = {
		"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC013_BREOTPVerification() {
		
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);

		Map<String, String> headers = new HashMap<String, String>();
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		headers.put("channel", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);

		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "BRE_OTP_VERIFIED");
		body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");

		Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);

		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC013_BREOTPVerification", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC014_VerifyLeadStage() {

			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.BRE_OTP_VERIFIED.getStage());

			LOGGER.info("Verify that detials are present in userAdditionalInfo");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
			
		}
		
				@Test(description = "BRE Validation Pending", dependsOnMethods = "TC014_VerifyLeadStage", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC015_BREValidationPending() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		
			Map<String, String> headers = new HashMap<String, String>();
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("status", "BRE_VALIDATION_PENDING");
			body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
		
			Response responseObject = lendingBaseClassObject.callbackBREValiadtion(queryParams, headers, body, true);
		
			verifyResponseCodeAs200OK(responseObject);
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC015_BREValidationPending",groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC016_VerifyLeadStage() {
		
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.BRE_VALIDATION_PENDING.getStage());
		
		}
		
		 @Test(description = "Fetch BRE Response", dependsOnMethods = "TC016_VerifyLeadStage", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC017_FetchBREResponse() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");

		Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);

		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

	}

	@Test(description = "Verify Lead stage", dependsOnMethods = "TC017_FetchBREResponse", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC018_VerifyLeadStage() {

		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
				sessionToken, LendingLeadStages.BRE_RESPONSE_AWAITED.getStage());

	}

	@Test(description = "Check BRE Response", dependsOnMethods = "TC018_VerifyLeadStage", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC019_CheckBREResponse() throws SQLException {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");

		
        Response responseObject=null;
		// If response of BRE status API is BRE success then get loan amount
		
		for(int i=0;i<20;i++)
			
		{
			responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			
			if (responseObject.getStatusCode() == 200
					&& responseObject.jsonPath().getString("stage").equals("BRE_SUCCESS"))
			{
			
				stage = responseObject.jsonPath().getString("stage");
				loanOffered = responseObject.jsonPath().getString("loanOffered");
				maxLoanAmount = responseObject.jsonPath().getString("maxLoanAmount");
		
				
				break;
			
			}
			
			
		}

		if (responseObject.getStatusCode() == 200
			&& responseObject.jsonPath().getString("stage").equals("BRE_ERROR"))
		
		LOGGER.info("Getting BRE Error");
				
		if(responseObject.getStatusCode() == 500
						&&responseObject.jsonPath().getString("displayMessage").equals("We could not fetch your details. Please try again later."))
					
		LOGGER.info("Could not fetch the details");
				
				
		
		Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE_SUCCESS.getStage());
							
					
			}

	

	@Test(description = "Verify Lead stage", dependsOnMethods = "TC019_CheckBREResponse", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC020_VerifyLeadStage() {

		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
				sessionToken, LendingLeadStages.BRE_SUCCESS.getStage());

	}
	

		 @Test(description = "Loan Offer accpeted", dependsOnMethods = "TC020_VerifyLeadStage", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC021_AcceptLoanOffer() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		
	
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("workflowSubOperation", "LOAN_OFFER_ACCEPTED");
		body.put("LOAN_TENURE", "6");
		body.put("LOAN_TENURE_UNIT", "MONTH");
		body.put("LOAN_AMOUNT_IN_NUMBER", "10000");
		body.put("LOAN_AMOUNT_IN_WORDS", "Ten  Thousands");
		body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "1816");
		body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "One Thousand Eight Hundred Sixteen");
		body.put("LOAN_RATE_OF_INTEREST", "30");
		body.put("LOAN_PROCESSING_FEE", "500");
		body.put("PROCESSING_FEE_RATE", "5.0");
		body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "590");
		body.put("LOAN_DISBURSAL_AMOUNT", "9194");
		body.put("STAMP_DUTY_CHARGES", "200");
		body.put("BROKEN_PERIOD_INTEREST", "16");
		
	
		Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers,body,true,true);
	
		lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
		stage = responseObject.jsonPath().getString("oeStage");
	
	}

		 
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC021_AcceptLoanOffer", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC022_VerifyLeadStage() {

			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());

		}
		
				@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC022_VerifyLeadStage", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC023_CheckCKYCStatus() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
			queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
			headers.put("Content-Type", "application/json;charset=utf-8");
		
			Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
		
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			ckycStage = responseObject.jsonPath().getString("stage");
			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
		
		}

			@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC023_CheckCKYCStatus", groups = {
					"Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC024_VerifyLeadStage() {
			
				lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, sessionToken, ckycStage);
			
			}

		@Test(description = "Upload selfie", dependsOnMethods = "TC024_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC025_UploadSelfie() throws InterruptedException {
		
			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
		
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
			uuid = responseObject.jsonPath().getString("uuid");
		
			Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
			LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
			Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
			Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
		
		}

		@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC025_UploadSelfie", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC026_VerifyUploadedDocument() {
		
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, ckycStage);
			Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docType"), "selfie");
			Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docProvided"), "selfie");
			Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docUUId"), uuid);
		
		}

		@Test(description = "Upload Customer Photo", dependsOnMethods = "TC026_VerifyUploadedDocument", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC027_UploadCustomerPhoto() throws InterruptedException {
		
			Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
					LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
		
			verifyResponseCodeAs200OK(responseObject);
		
			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
			uuid = responseObject.jsonPath().getString("uuid");
		
			Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
			LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
			Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
			Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
		
		}

		@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC027_UploadCustomerPhoto", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC028_VerifyUploadedDocument() {
		
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, ckycStage);
			List<Object> docTypes = responseObject.jsonPath().getList("solutionDocSRO.docType");
			docTypes.contains("customerPhoto");
			List<Object> docProvided = responseObject.jsonPath().getList("solutionDocSRO.docProvided");
			docProvided.contains("others");
		
		}

			@Test(description = "CKYC Callback", dependsOnMethods = "TC028_VerifyUploadedDocument", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC029_CKYCCallback() {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
				queryParams.put("leadId", leadId);
			
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
			
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
			
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("statusMessage", "CKYC_VERIFIED");
				body.put("status", "SUCCESS");
				body.put("cKycId", "4353435454356");
				body.put("firstName", "TOUCH");
				body.put("middleName", "WOOD");
				body.put("thirdName", "LIMITED");
				body.put("email", "<EMAIL>");
				body.put("type", "SELFIE");
				body.put("percentage", "100");
				body.put("addressline1", "A3, saket");
				body.put("addressline2", ",148 civil lines");
				body.put("city", "noida");
				body.put("state", "UP");
				body.put("pincode", "201010");
				body.put("dob", LendingConstants.DOB_HERO);
				body.put("gender", "male");
				body.put("pan", LendingConstants.PAN_HERO);
				body.put("ckycSuccessMode", "SEARCH_BY_AADHAR");
			
				Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
			
				verifyResponseCodeAs200OK(responseObject);
			
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
				Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PAN_VERIFIED.getStage());
			
			}

		@Test(description = "Verify Lead stage", dependsOnMethods = "TC029_CKYCCallback", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC030_VerifyLeadStage() {
		
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.PAN_VERIFIED.getStage());
		
			LOGGER.info("Verify that detials are present in userAdditionalInfo");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
			LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
			Assert.assertEquals(
					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),
					"FALSE");
			Assert.assertEquals(
					responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),
					"FALSE");
		
		}
		
		@Test(description = "Second BRE Initaite Callback", dependsOnMethods = "TC030_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC031_SecondBREInitiateCallback() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			queryParams.put("leadId", leadId);
		
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("status", "SECOND_BRE_REQUEST_INVOKED");
		
		
			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body);
		
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC031_SecondBREInitiateCallback", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC032_VerifyLeadStage() {
		
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.SECOND_BRE_INITIATED.getStage());
		
		
			
		}
		
		@Test(description = "Second BRE status Callback", dependsOnMethods = "TC032_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC033_SecondBREStatusCallback() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			queryParams.put("leadId", leadId);
		
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "SECOND_BRE_SUCCESS");
			body.put("status", "SUCCESS");
			body.put("BASE_ID", "PL_1001148796");
			body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
			body.put("CAPTURE_ADDITIONAL_DATA", "TRUE");
			body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", "FALSE");
		
		
			Response responseObject = lendingBaseClassObject.updateAdditionalPanDetails(queryParams, headers, body, LendingConstants.PERSONAL_LOAN_V2_SOLUTION,true);
		
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC033_SecondBREStatusCallback", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC034_VerifyLeadStage() {
		
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
		
		
			
		}
		
		@Test(description = "Additional Data Capture", dependsOnMethods = "TC034_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC035_AdditionalDataCapture() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
			queryParams.put("leadId", leadId);
		
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("status", "ADDITIONAL_DATA_CAPTURED");
			body.put("BUSINESS_EMAIL", "<EMAIL>");
			body.put("LOAN_OFFER_ID", "PL_1001148796_NEW_OFFER_c04bbba");
			body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
			body.put("MARITAL_STATUS", "MARRIED");
		
		
			Response responseObject = lendingBaseClassObject.secondBREInitiateCallback(queryParams, headers, body,true);
		
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC035_AdditionalDataCapture", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC036_VerifyLeadStage() {
		
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
		
			LOGGER.info("Verify that detials are present in userAdditionalInfo");
			Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.MARITAL_STATUS"), "MARRIED");
			
		}
		
		@Test(description = "CKYC name update in SAI", dependsOnMethods = "TC036_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC037_UpdateKYCNameInSAI() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("leadId", leadId);
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
		body.put("status", "SUCCESS");
		body.put("CKYC_NAME", "TestBeneficiary");
		
		Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);
		
		verifyResponseCodeAs200OK(responseObject);
		
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
		
		}
		
		@Test(description = "Enter the bank details", dependsOnMethods = "TC037_UpdateKYCNameInSAI", groups = {
			"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC038_SaveBankDetails() throws InterruptedException {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
	
		
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json");
		
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("bankName", "PAYTM BANK");
		body.put("bankAccountNumber", "************");
		body.put("ifsc", "PYTM0123456");
		body.put("bankAccountHolderName", "Shivangi Goswami");
		body.put("EMANDATE_TYPE", "Internet Banking");
		
		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);
		
		if (responseObject.getStatusCode() == 200) {
		
			Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
			stage=LendingLeadStages.BANKING_ACTION_DONE.getStage();
		
		}
		
		else {
			
			for (int i = 1; i < 4; i++) {
				LOGGER.info("Again hitting with same data: retry-count: " + i);
				responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
				
				if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
				{
					stage=responseObject.jsonPath().getString("stage");
					break;
				}
					
			}
		
		}
		
//		
//		if(responseObject.getStatusCode() == 202)
//			
//		{
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), false);
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Name match failed");
//			Assert.assertEquals(responseObject.jsonPath().getString("stage"), "BANK_VALIDATION_PENDING");
//			stage=responseObject.jsonPath().getString("stage");
//		
//			  queryParams.put("docProvided","cancelledChequePhoto");
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			  queryParams.put("solutionType",LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
//			  queryParams.put("docType","cancelledChequePhoto");
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL); 
//			  queryParams.put("solutionLeadId",leadId);
//		
//			
//			 headers.put("Content-Type", "multipart/form-data");
//			 headers.put("session_token",sessionToken);
//			  
//			  File uploadFile = new File("src/test/resources/MerchantService/v2/lending/lead/document/test.jpeg");
//		
//			 responseObject=lendingBaseClassObject.uploadCancelledCheque(queryParams, headers,uploadFile);
//		
//			 lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			  
//			  Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"),true);
//			
//		}
		
	
			Assert.assertEquals(responseObject.getStatusCode(),200);
		
		
			
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC038_SaveBankDetails", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC039_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
			sessionToken, stage);
		
		}
		
		
			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC039_VerifyLeadStage", groups = {
			"Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC040_EmandateCallback() throws InterruptedException {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("leadId", leadId);
		queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
	
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LMS_SECRET);
	
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type", "application/json");
		headers.put("custId", custId);
	
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("status", "EMANDATE_SUCCESS");
	
		Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
	
		verifyResponseCodeAs200OK(responseObject);
	
		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
	
	}
			@Test(description = "Verify Lead stage", dependsOnMethods = "TC040_EmandateCallback", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC041_VerifyLeadStage() {
			
			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
					sessionToken, LendingLeadStages.EMANDATE_SUCCESS.getStage());
			
			}
			
		@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC041_VerifyLeadStage", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC042_FetchDynamicTnc() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
		
			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.jsonPath().getString("meta.status").equals("success")) {
		
				code = responseObject.jsonPath().getString("data.state.code");
				tncName = responseObject.jsonPath().getString("data.state.tncName");
				url = responseObject.jsonPath().getString("data.state.url");
				uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
				md5 = responseObject.jsonPath().getString("data.state.md5");
			}
		
		}
		
	
		@Test(description = "Fetch Dynamic T and C Sanction Letter", dependsOnMethods = "TC042_FetchDynamicTnc", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC043_FetchDynamicTncSanctionLetter() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
			queryParams.put("tncType", "LOAN_SANCTION_TNC");
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
		
			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.jsonPath().getString("meta.status").equals("success")) {
		
				codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
				tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
				urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
				uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
				md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
			}
		
		}
		
		@Test(description = "Verify submit application", dependsOnMethods = "TC043_FetchDynamicTncSanctionLetter", groups = {
				"Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC044_SubmitApplication() throws InterruptedException {
		
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId", leadId);
		
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sessionToken);
		
			Map<String, Object> body = new HashMap<String, Object>();
		
			body.put("tncName", tncName);
			body.put("accept", 1);
			body.put("uniqueIdentifier", uniqueIdentifier);
			body.put("md5", md5);
		
			
			body.put("tncName1", tncNameSanctionLetter);
			body.put("accept1", 1);
		
			body.put("uniqueIdentifier1", uniqueIdentifierSanctionLetter);
			body.put("md51", md5SanctionLetter);
			body.put("LOAN_FINAL_BROKEN_PI", "30");
			body.put("LOAN_FINAL_AMOUNT_PAYABLE", "500");
			body.put("LOAN_FINAL_DISBURSEMENT_AMOUNT", "22222");
		
			Response responseObject = lendingBaseClassObject.submitApplicationHero(queryParams, headers, body,LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		
			LOGGER.info("Status Code : " + responseObject.getStatusCode());
			if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
		
			{
				LOGGER.info("Try again");
				responseObject = lendingBaseClassObject.submitApplicationHero(queryParams, headers, body,LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
		
			}
		
			else if (responseObject.jsonPath().getString("meta.stage").contentEquals("LMS_POST")) {
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("meta.subStage"), "APPLICATION_PENDING");
				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
				Assert.assertEquals(responseObject.jsonPath().getString("data.state"), "Loan Application is Accepted");
		
			}
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC044_SubmitApplication", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC045_VerifyLeadStage() {
		
		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
				sessionToken, LendingLeadStages.APPLICATION_PENDING.getStage());
		
		}
		
//		@Test(description = "Verify PDC Callback", dependsOnMethods = "TC045_VerifyLeadStage", groups = {
//		"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC046_PDCCallback() throws InterruptedException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
//		
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "LOAN_APPLICATION_ACCEPTED");
//			body.put("USER_MESSAGE", "Loan disbursal is accepted");
//			body.put("LMS_REASON_ID", "0");
//			body.put("SYSTEM_MESSAGE", "Direct Approval of Loan Account is accepted");
//			
//		
//			Response responseObject = lendingBaseClassObject.pdcLoanApplicationAccepted(queryParams, headers, body,true);
//		
//			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		
//		}
				@Test(description = "Sheet upload from panel", groups = {
				"Regression" })
		      @Owner(emailId = "<EMAIL>", isAutomated = true)
		     public void TC046_UploadSheetONPanel() throws InterruptedException, IOException {
		
			TokenXMV tokenXMW = new TokenXMV();
			Response responseObject = MiddlewareServicesObject.v1Token("**********", "paytm@123");
			String XMWToken = responseObject.getHeader("Set-Cookie").toString();
		
			System.out.println("XMW token is :" + XMWToken);
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Content-Type", "multipart/form-data");
			headers.put("Cookie", XMWToken);
		
			File fileUpload = new File(
					"src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/BusinessSampleFile.csv");
			FileWriter outputfile = new FileWriter(fileUpload);
			CSVWriter writer = new CSVWriter(outputfile);
			String[] header = { "Lead Id", "Business status ", "Rejection Reason\n" };
			writer.writeNext(header);
			String[] data1 = { "e3fefd5a-4e54-4024-8c7a-5e9238ba3ef3", "APPROVED", "N/A" };
			writer.writeNext(data1);
			writer.flush();
			writer.close();
		
			responseObject = lendingBaseClassObject.uploadSheetFromPanel(headers, fileUpload, "BUSINESS_STATUS");
			if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
					.contentEquals(" has been successfully uploaded")) {
				LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
				verifyResponseCodeAs200OK(responseObject);
				Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
		
			}
		
		}
		
		@Test(description = "Verify Lead stage", dependsOnMethods = "TC046_UploadSheetONPanel", groups = { "Regression" })
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC047_VerifyLeadStage() {
				
		
		
		     Response responseObject = null;
		
			 Map<String,String> queryParams=new HashMap<String,String>();
		        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		        queryParams.put("solution",LendingConstants.PERSONAL_LOAN_V2_SOLUTION);
		        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		
		        Map<String,String> headers=new HashMap<String,String>();
		        headers.put("session_token",sessionToken);
		
		for(int i=0;i<6;i++)
		        {
		        responseObject= fetchLeadDetails(queryParams, headers);
		
			        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
			        {
			            LOGGER.info("Verify Lead Stage "+responseObject.jsonPath().getString("stage"));
			           break;
		
			        }
			           
		        
		        }
		
		  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
		
			}

		
	/**
	 * Method to set headers which are used in lead creation request
	 * 
	 * @return
	 */
	public Map<String, String> setcommonHeaders() {

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("session_token", sessionToken);
		headers.put("Content-Type", "application/json;charset=utf-8");

		return headers;
	}
	
}
