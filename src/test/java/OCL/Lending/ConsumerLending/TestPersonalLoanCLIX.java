//package OCL.Lending.ConsumerLending;
//
//import java.io.File;
//import java.io.FileWriter;
//import java.io.IOException;
//import java.math.BigInteger;
//import java.sql.SQLException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.apache.log4j.Logger;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import com.opencsv.CSVWriter;
//import com.paytm.apitools.util.annotations.Owner;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingLeadStages;
//import Request.MerchantService.v1.TokenXMV;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//import io.restassured.response.Response;
//
//public class TestPersonalLoanCLIX extends LendingBaseAPI {
//
//	private static final Logger LOGGER = Logger.getLogger(TestPersonalLoanCLIX.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
//
//	String sessionToken = "";
//	String leadId = "";
//	String custId = "";
//	String agentNumber = "9717017397";
//	String agentPassword = "paytm@123";
//	String token = "";
//	String uuid = "";
//	String ckycStage = "";
//	String loanOffered = "";
//	String maxLoanAmount = "";
//	String authorisedMonthlyLimit = "";
//	String stage = "";
//	String code = "";
//	String tncName = "";
//	String url = "";
//	String uniqueIdentifier = "";
//	String md5 = "";
//	String codeSanctionLetter = "";
//	String tncNameSanctionLetter = "";
//	String urlSanctionLetter = "";
//	String uniqueIdentifierSanctionLetter = "";
//	String md5SanctionLetter = "";
//	String firstNameAsPerPan;
//	String lastNameAsPerPan;
//	String middleNameAsPerPan;
//	
//	
//
//	public static final String SOLUTION = "personal_loan";
//	public static final String SOLUTION_TYPE_LEVEL_2 = "CLIX";
//	public static final String ENTITY_TYPE = "INDIVIDUAL";
//	public static final String CHANNEL = "PAYTM_APP";
//	public static final String PAN = "**********";
//	public static final String DOB = "1989-04-21";
//	public static final String EMAIL = "<EMAIL>";
//	public static final String ISSUER = "OE";
//	public static final String CLIENT_ID = "LMS";
//	Map<String, String> commonHeaders;
//	
//
//	@BeforeClass()
//	public void intitializeInputData() throws IOException {
//
//		LOGGER.info(" Before Suite Method for Agent Login ");
//		sessionToken = ApplicantToken(agentNumber, agentPassword);
//		LOGGER.info("Applicant Token for Lending : " + sessionToken);
//		commonHeaders = setcommonHeaders();
//
//	}
//
//	@Test(description = "Delete all existing leads of the number", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC001_DeleteExistingLead() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("mobile", agentNumber);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683");
//
//		lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
//	}
//
//	@Test(description = "Verify whether there is any existing lead present or not", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC002_FetchLeadDeatils() {
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, LendingLeadStages.LEAD_NOT_PRESENT.getStage());
//
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data Not present for customer");
//	}
//
//	@Test(description = "Create Personal Loan Lead with lender CLIX", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC003_CreatePersonalLoanLead() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("tnCSetName", "PersonalLoan_OCLConsent");
//		body.put("LOAN_PROCESSING_FEE", "200");
//		body.put("PROCESSING_FEE_RATE", "2.25");
//		body.put("LOAN_RATE_OF_INTEREST", "20.0");
//		body.put("LOAN_INTEREST_AMOUNT", "123");
//		body.put("LOAN_AMOUNT_IN_NUMBER", "90000");
//		body.put("LOAN_AMOUNT_IN_WORDS", "Ninety  Thousands Rupees only");
//		body.put("LOAN_MIN_AMOUNT", "10000");
//		body.put("LOAN_MAX_AMOUNT", "195000");
//		body.put("LOAN_TENURE", "365");
//		body.put("LOAN_TENURE_UNIT", "MONTHLY");
//		body.put("LOAN_TENURE_MIN", "6");
//		body.put("LOAN_TENURE_MAX", "36");
//		body.put("LOAN_DUE_DATE", "5");
//		body.put("BASE_ID", "automation_pl09");
//		body.put("LENDER_ID", "2");
//		body.put("PRODUCT_ID", "60");
//		body.put("RISK_GRADE", "H");
//		body.put("LENDER_RISK_CODE", "CL_PL|A38B12C5D5");
//		body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//		body.put("WHITELISTING_SOURCE", "RISK");
//		body.put("OFFER_START_DATE", "2020-12-20");
//		body.put("OFFER_END_DATE", "2021-03-20");
//		body.put("PRODUCT_TYPE", "PL");
//		body.put("PRODUCT_VERSION", "1");
//		body.put("LUP_ID", "2121");
//		body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//		body.put("BUSINESS_EMAIL_VERIFICATION_REQUIRED", true);
//		body.put("IS_EMANDATE_ELIGIBLE", true);
//		body.put("LOAN_INCENTIVE_PERCENTAGE", "2");
//		body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "24");
//	    body.put("LENDING_DYNAMIC_SECONDARY_TNC", "sanction_letter_pl_clix");
//		body.put("LENDING_DYNAMIC_TNC", "loan_agreement_PL_clix");
//
//		Response responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body);
//		if (responseObject.getStatusCode() == 200) {
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//			leadId = responseObject.jsonPath().getString("leadId");
//		}
//
//		else {
//			LOGGER.info("Try to hit the API again");
//			responseObject = lendingBaseClassObject.v1ConsumerLead(queryParams, headers, body);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Lead successfully created.");
//			leadId = responseObject.jsonPath().getString("leadId");
//		}
//
//	}
//
//	@Test(description = "Verify the data of lead created", dependsOnMethods = "TC003_CreatePersonalLoanLead", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC004_FetchTheCreatedLeadDeatils() {
//
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, LendingLeadStages.LEAD_CREATED.getStage());
//		custId = responseObject.jsonPath().getString("custId");
//				
//	}
//
//	@Test(description = "Verify the callback for PPBL OTP", dependsOnMethods = "TC003_CreatePersonalLoanLead", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC005_PPBLOTPCallback() throws InterruptedException {
//
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = lendingBaseClassObject.setCallbackHeaders(token, custId, CHANNEL);
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "PPBL_TNC_VERIFIED");
//		body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//		body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//
//		Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//	
//		verifyResponseCodeAs200OK(responseObject);
//		LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//	
//		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC005_PPBLOTPCallback", groups = {
//		"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC006_FetchTheCreatedLeadDeatils() {
//	
//	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//			sessionToken, LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
//	custId = responseObject.jsonPath().getString("custId");
//			
//	}
//
//	@Test(description = "Add Basic details", dependsOnMethods = "TC005_PPBLOTPCallback", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC007_AddBasicDetails() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//		queryParams.put("channel", CHANNEL);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers = commonHeaders;
//
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("workflowSubOperation", "BASIC_DETAILS");
//		body.put("DOB", DOB);
//		body.put("PAN", PAN);
//		body.put("EMAIL", EMAIL);
//		body.put("LOAN_PURPOSE", "Personal use lending");
//
//		Response responseObject = lendingBaseClassObject.addBasicDetails(queryParams, headers, body,SOLUTION);
//
//		verifyResponseCodeAs200OK(responseObject);
//
//		Assert.assertEquals(responseObject.jsonPath().getString("mobileNumber"), agentNumber);
//		firstNameAsPerPan=responseObject.jsonPath().getString("firstNameAsPerPan");
//		middleNameAsPerPan=responseObject.jsonPath().getString("middleNameAsPerPan");
//		lastNameAsPerPan=responseObject.jsonPath().getString("lastNameAsPerPan");
//		
//
//	}
//		@Test(description = "Verify the data of lead created", dependsOnMethods = "TC007_AddBasicDetails", groups = {
//		"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC008_FetchTheCreatedLeadDeatils() {
//	
//	Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//			sessionToken, LendingLeadStages.BASIC_DETAILS.getStage());
//	custId = responseObject.jsonPath().getString("custId");
//	       LOGGER.info("Verify that detials are present in userAdditionalInfo");
//	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.DOB"),DOB);
//	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.PAN"),PAN);
//	      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.EMAIL"),EMAIL);
//			
//	}
//		@Test(description = "Verify Email Callback", dependsOnMethods = "TC007_AddBasicDetails", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC009_EmailCallback() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", SOLUTION);
//					
//			token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, CHANNEL);
//
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BUSINESS_EMAIL_VERIFIED");
//			body.put("BUSINESS_EMAIL", EMAIL);
//			body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "true");
//		
//			Response responseObject = lendingBaseClassObject.verifyEmailCallback(queryParams, headers, body,SOLUTION);
//
//			verifyResponseCodeAs200OK(responseObject);
//
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//	
//
//		}
//			@Test(description = "Verify the data of lead created", dependsOnMethods = "TC009_EmailCallback", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC010_FetchTheCreatedLeadDeatils() {
//		
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, LendingLeadStages.BUSINESS_EMAIL_VERIFIED.getStage());
//	    Assert.assertEquals(responseObject.jsonPath().getString("solution.businessEmail"),EMAIL);
//				
//		}
//			
//			@Test(description = "Update Additional Details", dependsOnMethods = "TC009_EmailCallback", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC011_UpdateAdditionalDetails() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//			     queryParams.put("solution", SOLUTION);
//			 	queryParams.put("entityType","INDIVIDUAL");
//				queryParams.put("channel", CHANNEL);
//				queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//						
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = commonHeaders;
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("workflowSubOperation", "ADDITIONAL_DETAILS");
//				body.put("MONTHLY_INCOME", "1000");
//				body.put("OCCUPATION", "Sofware Engineer");
//				body.put("EMPLOYER_CODE", "22955");
//				body.put("BUSINESS_EMAIL", EMAIL);
//				body.put("REFERENCE_NAME", "Pankaj kumar");
//				body.put("EMPLOYER_NAME", "One 97 communication Private Limited");
//				body.put("REFERENCE_PHONE", "8178361968");
//				body.put("IS_BUSINESS_EMAIL_REQUIRED", "true");
//			
//				Response responseObject = lendingBaseClassObject.updateBasicDetails(queryParams, headers, body);
//
//				verifyResponseCodeAs200OK(responseObject);
//
//				Assert.assertEquals(responseObject.jsonPath().getString("firstNameAsPerPan"), firstNameAsPerPan);
//				Assert.assertEquals(responseObject.jsonPath().getString("middleNameAsPerPan"), middleNameAsPerPan);
//				Assert.assertEquals(responseObject.jsonPath().getString("lastNameAsPerPan"), lastNameAsPerPan);
//				Assert.assertEquals(responseObject.jsonPath().getString("mobileNumber"), agentNumber);
//			}
//			
//			@Test(description = "Verify the data of lead created", dependsOnMethods = "TC011_UpdateAdditionalDetails", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC012_FetchTheCreatedLeadDeatils() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//					sessionToken, LendingLeadStages.ADDITIONAL_DETAILS.getStage());
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.MONTHLY_INCOME"), "1000");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OCCUPATION"), "Sofware Engineer");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.EMPLOYER_CODE"), "22955");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BUSINESS_EMAIL"), EMAIL);
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.REFERENCE_NAME"), "Pankaj kumar");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.EMPLOYER_NAME"), "One 97 communication Private Limited");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.REFERENCE_PHONE"), "8178361968");
//			Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.IS_BUSINESS_EMAIL_REQUIRED"), "true");
//	
//			}
//			
//			@Test(description = "OTP Callback", dependsOnMethods = "TC011_UpdateAdditionalDetails", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC013_OTPCallback() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("leadId", leadId);
//				queryParams.put("solution", SOLUTION);
//
//				token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers = lendingBaseClassObject.setCallbackHeaders(token, custId, CHANNEL);
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("status", "OTP_VERIFIED");
//				body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//				body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//
//				Response responseObject = lendingBaseClassObject.callbackOTP(queryParams, headers, body);
//
//				verifyResponseCodeAs200OK(responseObject);
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//			}
//
//			@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC013_OTPCallback", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC014_FetchLeadStage() {
//
//				Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//						sessionToken, LendingLeadStages.OTP_VERIFIED.getStage());
//
//			}
//
//			@Test(description = "Get the CKYC Status of current lead", dependsOnMethods = "TC013_OTPCallback", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC015_CheckCKYCStatus() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("entityType", "INDIVIDUAL");
//				queryParams.put("solution", SOLUTION);
//				queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//				queryParams.put("channel", CHANNEL);
//				
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("session_token", sessionToken);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//
//				Response responseObject = lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//
//				verifyResponseCodeAs200OK(responseObject);
//				ckycStage = responseObject.jsonPath().getString("stage");
//				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//
//			}
//
//			@Test(description = "Verify the lead sub stage", dependsOnMethods = "TC015_CheckCKYCStatus", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC016_VerifyLeadStage() {
//
//				lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL, sessionToken, ckycStage);
//
//			}
//
//			@Test(description = "Upload selfie", dependsOnMethods = "TC015_CheckCKYCStatus", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC017_UploadSelfie() throws InterruptedException {
//
//				Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
//						"INDIVIDUAL", SOLUTION, SOLUTION_TYPE_LEVEL_2, sessionToken);
//
//				verifyResponseCodeAs200OK(responseObject);
//
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//				uuid = responseObject.jsonPath().getString("uuid");
//
//				Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//				LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//				Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//				Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//
//			}
//
//			@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC017_UploadSelfie", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC018_VerifyUploadedDocument() {
//
//				Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//						sessionToken, ckycStage);
//				Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docType"), "selfie");
//				Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docProvided"), "selfie");
//				Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docUUId"), uuid);
//
//			}
//
//			@Test(description = "Upload Customer Photo", dependsOnMethods = "TC017_UploadSelfie", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC019_UploadCustomerPhoto() throws InterruptedException {
//
//				Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
//						"INDIVIDUAL", SOLUTION, SOLUTION_TYPE_LEVEL_2, sessionToken);
//
//				verifyResponseCodeAs200OK(responseObject);
//
//				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
//				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//				uuid = responseObject.jsonPath().getString("uuid");
//
//				Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
//				LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
//				Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
//				Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
//
//			}
//
//			@Test(description = "Verify the  details of Uploaded Document", dependsOnMethods = "TC019_UploadCustomerPhoto", groups = {
//					"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC020_VerifyUploadedDocument() {
//
//				Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//						sessionToken, ckycStage);
//				List<Object> docTypes = responseObject.jsonPath().getList("solutionDocSRO.docType");
//				docTypes.contains("customerPhoto");
//				List<Object> docProvided = responseObject.jsonPath().getList("solutionDocSRO.docProvided");
//				docProvided.contains("others");
//
//			}
//
//			@Test(description = "CKYC Callback", dependsOnMethods = "TC019_UploadCustomerPhoto", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC021_CKYCCallback() {
//				Map<String, String> queryParams = new HashMap<String, String>();
//				queryParams.put("solution", SOLUTION);
//				queryParams.put("leadId", leadId);
//
//				token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//				Map<String, String> headers = new HashMap<String, String>();
//				headers.put("Authorization", token);
//				headers.put("Content-Type", "application/json");
//				headers.put("custId", custId);
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "CKYC_VERIFIED");
//				body.put("status", "SUCCESS");
//				body.put("cKycId", "4353435454356");
//				body.put("firstName", "TOUCH");
//				body.put("middleName", "WOOD");
//				body.put("thirdName", "LIMITED");
//				body.put("email", "<EMAIL>");
//				body.put("type", "SELFIE");
//				body.put("percentage", "100");
//				body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//				body.put("addressline2", "MAYUR VIHAR PHASE 1");
//				body.put("city", "DELHI");
//				body.put("state", "EAST DELHI");
//				body.put("pincode", "110091");
//				body.put("dob", DOB);
//				body.put("gender", "male");
//				body.put("pan", PAN);
//				body.put("ckycSuccessMode", "SEARCH_BY_AADHAR");
//
//				Response responseObject = lendingBaseClassObject.ckycCallback(queryParams, headers, body);
//
//				verifyResponseCodeAs200OK(responseObject);
//
//				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//				Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.PAN_VERIFIED.getStage());
//
//			}
//
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC021_CKYCCallback", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC022_VerifyLeadStage() {
//
//				Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//						sessionToken, LendingLeadStages.PAN_VERIFIED.getStage());
//
//				LOGGER.info("Verify that detials are present in userAdditionalInfo");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
//				LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//				Assert.assertEquals(
//						responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),
//						"FALSE");
//				Assert.assertEquals(
//						responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),
//						"FALSE");
//
//			}
//			
//			@Test(description = "BRE OTP Verification", dependsOnMethods = "TC021_CKYCCallback", groups = {
//			"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC023_BREOTPVerification() {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", SOLUTION);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//			headers.put("Authorization", token);
//			headers.put("Content-Type", "application/json");
//			headers.put("custId", custId);
//			headers.put("channel", CHANNEL);
//	
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "BRE_OTP_VERIFIED");
//			body.put("ALTERNATE_MOBILE_NUMBER", "9716954395");
//	
//			Response responseObject = lendingBaseClassObject.callbackBREOTP(queryParams, headers, body);
//	
//			verifyResponseCodeAs200OK(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//
//	}
//			
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC023_BREOTPVerification", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC024_VerifyLeadStage() {
//
//				Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//						sessionToken, LendingLeadStages.BRE_OTP_VERIFIED.getStage());
//
//				LOGGER.info("Verify that detials are present in userAdditionalInfo");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"), "LIMITED");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"), "TOUCH");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"), "WOOD");
//				Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
//				LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//				Assert.assertEquals(
//						responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),
//						"FALSE");
//				Assert.assertEquals(
//						responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),
//						"FALSE");
//
//			}
//
//			 @Test(description = "Add Correspondence, Current and Permanent Address",dependsOnMethods = "TC023_BREOTPVerification",groups = {"Regression"})
//			  @Owner(emailId = "<EMAIL>",isAutomated = true)
//			    public void TC025_AddAddress()
//			    {
//				  Map<String,String> queryParams=new HashMap<String,String>();
//				   queryParams.put("entityType", "INDIVIDUAL");
//			       queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//				   queryParams.put("solution",SOLUTION);
//				   queryParams.put("channel", "PAYTM_APP");
//				    	    	  
//		    		    	 			 			  
//		    	   Map<String,String> headers=new HashMap<String,String>();
//		    	   headers=setcommonHeaders();
//			     
//		    	   Map<String,Object> body = new HashMap<String, Object>();
//			  	   body.put("workflowSubOperation","ADDRESS_DETAILS");
//			  	   body.put("pincode", "124103");
//			  	   body.put("state", "New Delhi");
//			  	   body.put("city", "Delhi");
//			  	   body.put("line1", "testline1");
//			  	   body.put("line2", "testline2");
//			  	   body.put("line3", "testline3");
//			  	   body.put("landmark", "NEAR MOTHER DAIRY");
//			  	   body.put("addressType", "RESIDENTIAL");
//			  	   body.put("addressSubType", "CORRESPONDENCE");
//			  	 
//			  	   body.put("country1", "INDIA");
//			  	   body.put("pincode1", "122001");
//			  	   body.put("state1", "UP");
//			  	   body.put("city1", "NOIDA");
//			  	   body.put("line11", "H No 661 FF");
//			  	   body.put("line21", "Sector 9");
//			  	   body.put("line31", "testline3");
//			  	   body.put("landmark", "555 A");
//			  	   body.put("addressType1", "RESIDENTIAL");
//			  	   body.put("addressSubType1", "PERMANENT");
//			  	  
//			  	   	   
//			  	
//			  	   Response responseObject= lendingBaseClassObject.addAddressDetails(queryParams, headers,body);
//			  	  
//			  	   verifyResponseCodeAs200OK(responseObject);
//				
//			  	    Assert.assertEquals(responseObject.jsonPath().getString("firstNameAsPerPan"), firstNameAsPerPan);
//					Assert.assertEquals(responseObject.jsonPath().getString("middleNameAsPerPan"), middleNameAsPerPan);
//					Assert.assertEquals(responseObject.jsonPath().getString("lastNameAsPerPan"), lastNameAsPerPan);
//					Assert.assertEquals(responseObject.jsonPath().getString("mobileNumber"), agentNumber);
//			  		     		
//				 		   
//			    }
//			 
//			 @Test(description = "Verify Lead stage",dependsOnMethods = "TC025_AddAddress",groups = {"Regression"})
//			  @Owner(emailId = "<EMAIL>",isAutomated = true)
//			    public void TC026_VerifyLeadStage()
//			    {
//				  	      
//				 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE,SOLUTION,CHANNEL,sessionToken, LendingLeadStages.ADDRESS_DETAILS.getStage());
//			   
//			    } 
//			 
//			 @Test(description = "Fetch BRE Response", dependsOnMethods = "TC025_AddAddress", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC027_FetchBREResponse() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("solution", SOLUTION);
//			queryParams.put("entityType", "INDIVIDUAL");
//			queryParams.put("channel", CHANNEL);
//			
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//
//			Response responseObject = lendingBaseClassObject.getBREStatus(queryParams, headers);
//
//			//verifyResponseCodeAs200OK(responseObject);
//
//		}
//
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC027_FetchBREResponse", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC028_VerifyLeadStage() {
//
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//					sessionToken, LendingLeadStages.BRE_RESPONSE_AWAITED.getStage());
//
//		}
//
//		@Test(description = "Check BRE Response", dependsOnMethods = "TC027_FetchBREResponse", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC029_CheckBREResponse() throws SQLException {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", SOLUTION);
//			queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("entityType", "INDIVIDUAL");
//			queryParams.put("channel", CHANNEL);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//
//			Response responseObject = lendingBaseClassObject.checkBREStatus(queryParams, headers);
//
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//
//			// If response of BRE status API is BRE success then validate loan amount
//
//			if (responseObject.getStatusCode() == 200
//					&& responseObject.jsonPath().getString("stage").equals("BRE_SUCCESS")) {
//				stage = responseObject.jsonPath().getString("stage");
//				loanOffered = responseObject.jsonPath().getString("loanOffered");
//				maxLoanAmount = responseObject.jsonPath().getString("maxLoanAmount");
//		
//			}
//
//			// If response of BRE status API is UPDATE_LOAN_OFFER then validate loan amount
//			// and hit update loan offer API
//			if (responseObject.getStatusCode() == 200
//					&& responseObject.jsonPath().getString("stage").equals("UPDATE_LOAN_OFFER")) {
//
//				Assert.assertEquals(responseObject.jsonPath().getString("newOfferGenerated"), "true");
//				UpdateLoanOffer();
//
//			}
//			// IF response is not BRE success then their can be two cases
//			else {
//
//				// 1.Response can be BRE ERROR in that case move the node back to BRE Response
//				// awaited and hit callback API and move lead to BRE success
//				if (responseObject.getStatusCode() == 200
//						&& responseObject.jsonPath().getString("stage").equals("BRE_ERROR")) {
//					int updatedRows = 0;
//					String currentActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("167", leadId);
//					LOGGER.info("Current Active Node is : " + currentActiveworkflowStatusId);
//
//					updatedRows = runUpdateQueryOnWorkflowStatus(0, new BigInteger(currentActiveworkflowStatusId));
//					Assert.assertEquals(updatedRows, 1);
//
//					String previousActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("366", leadId);
//					updatedRows = runUpdateQueryOnWorkflowStatus(1, new BigInteger(previousActiveworkflowStatusId));
//					LOGGER.info("Updated Active Node is : " + previousActiveworkflowStatusId);
//					Assert.assertEquals(updatedRows, 1);
//
//				}
//
//				// 2.Response can be unable to fetch the details , in this case lead will still
//				// be at BRE REsponse awaited so hit the callback API and move lead to BRE
//				// success
//				else {
//
//					LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),
//							"We could not fetch your details. Please try again later.");
//
//				}
//
//				LOGGER.info("Using Callback API");
//
//				queryParams.put("solution", SOLUTION);
//				queryParams.put("leadId", leadId);
//				token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//
//				headers.put("Authorization", token);
//				headers.put("custId", custId);
//				headers.put("Content-Type", "application/json;charset=utf-8");
//
//				Map<String, Object> body = new HashMap<String, Object>();
//				body.put("statusMessage", "BRE_SUCCESS");
//				body.put("status", "SUCCESS");
//				body.put("creditScore", "780.3");
//				body.put("lastFetchDate", "1606694400000");
//				body.put("bureau", "CIBIL");
//				body.put("loanOffered", "true");
//				body.put("newOfferGenerated", "false");
//				body.put("rejectionReason", "Rejection reason");
//				body.put("baseId", "automation_pl09");
//				body.put("riskGrade", "H");
//				body.put("processingFeeRate", "2.25");
//				body.put("isAcceptanceAbove5000", true);
//				body.put("isSiMandatory", true);
//				body.put("isRestrictedMerchant", false);
//				body.put("isPaytmVintageOlderThan90d", true);
//				body.put("minLoanAmount", "10000");
//				body.put("maxLoanAmount", "195000");
//				body.put("maxTenure", 36);
//				body.put("minTenure", 6);
//				body.put("rateOfInterest", 20);
//				body.put("fieldInvestigationNeeded", true);
//				body.put("isHomeFiNeeded", true);
//
//				responseObject = lendingBaseClassObject.breCallbackMCA(queryParams, headers, body);
//				LOGGER.info("Status Code : " + responseObject.getStatusCode());
//				if (responseObject.getStatusCode() == 200) {
//					stage = responseObject.jsonPath().getString("oeStage");
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//					//UpdateLoanOffer();
//
//				}
//
//			}
//
//		}
//
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void UpdateLoanOffer() {
//
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("solution", SOLUTION);
//			queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//			queryParams.put("entityType", "INDIVIDUAL");
//			queryParams.put("channel", CHANNEL);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("workflowSubOperation", "UPDATE_LOAN_OFFER");
//			body.put("LOAN_AMOUNT_IN_NUMBER", "90000");
//			body.put("LOAN_TENURE", "365");
//			body.put("LOAN_RATE_OF_INTEREST", "20");
//			body.put("LOAN_EQUATED_DAILY_INSTALLMENT", "100");
//			body.put("LOAN_PROCESSING_FEE", "200");
//			body.put("LOAN_INTEREST_AMOUNT", "123");
//			body.put("LOAN_AMOUNT_IN_WORDS", "Ninty Thousand");
//			body.put("BASE_ID", "automation_pl09");
//			body.put("LOAN_MIN_AMOUNT", "10000.0");
//			body.put("LOAN_MAX_AMOUNT", "90000.0");
//			body.put("LOAN_TENURE_MIN", "18");
//			body.put("LOAN_TENURE_MAX", "18");
//			body.put("RISK_GRADE", "H");
//			body.put("IS_ACCEPTANCE_ABOVE_5000", false);
//			body.put("PROCESSING_FEE_RATE", "2.25");
//			body.put("IS_SI_MANDATORY", false);
//			body.put("IS_RESTRICTED_MERCHANT", false);
//			body.put("IS_EMANDATE_ELIGIBLE", false);
//			body.put("LOAN_INCENTIVE", "5000");
//			body.put("LOAN_INCENTIVE_ELIGIBLE", false);
//			body.put("LOAN_INCENTIVE_PERCENTAGE", "0.2");
//			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", false);
//
//			Response responseObject = lendingBaseClassObject.updateLoanOffer(queryParams, headers, body);
//
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("isLeadAlreadyExists"), true);
//
//		}
//
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC029_CheckBREResponse", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC030_VerifyLeadStage() {
//
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//					sessionToken, LendingLeadStages.BRE_SUCCESS.getStage());
//
//		}
//		@Test(description = "CKYC name update in SAI", dependsOnMethods = "TC029_CheckBREResponse", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC031_UpdateKYCNameInSAI() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("leadId", leadId);
//		
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
//		body.put("status", "SUCCESS");
//		body.put("CKYC_NAME", "TestBeneficiary");
//		
//		Response responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body);
//		
//		verifyResponseCodeAs200OK(responseObject);
//		
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//		Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BRE_SUCCESS.getStage());
//		
//		}
//		
//		@Test(description = "Enter the bank details", dependsOnMethods = "TC031_UpdateKYCNameInSAI", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC032_SaveBankDetails() {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("solution", SOLUTION);
//		queryParams.put("entityType", "INDIVIDUAL");
//		queryParams.put("channel", CHANNEL);
//		queryParams.put("solutionTypeLevel2", SOLUTION_TYPE_LEVEL_2);
//	
//		
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("session_token", sessionToken);
//		headers.put("Content-Type", "application/json");
//		
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("bankName", "PAYTM BANK");
//		body.put("bankAccountNumber", "************");
//		body.put("ifsc", "PYTM0123456");
//		body.put("bankAccountHolderName", "Shivangi Goswami");
//		
//		Response responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//		
//		if (responseObject.getStatusCode() == 200) {
//		
//			Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
//		
//		}
//		
//		else {
//			
//			for (int i = 1; i < 4; i++) {
//				LOGGER.info("Again hitting with same data: retry-count: " + i);
//				responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
//				
//				if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
//					break;
//			}
//		
//		}
//		
//	
//			Assert.assertEquals(responseObject.getStatusCode(),200);
//			
//		
//		}
//		
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC032_SaveBankDetails", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC033_VerifyLeadStage() {
//		
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, LendingLeadStages.BANKING_ACTION_DONE.getStage());
//		
//		}
//		
//		
//			@Test(description = "Verify Emandate Callback", dependsOnMethods = "TC032_SaveBankDetails", groups = {
//			"Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void TC034_EmandateCallback() throws InterruptedException {
//		Map<String, String> queryParams = new HashMap<String, String>();
//		queryParams.put("leadId", leadId);
//		queryParams.put("solution", SOLUTION);
//	
//		token = lendingBaseClassObject.generateJwtToken(ISSUER, CLIENT_ID, custId);
//	
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		headers.put("Content-Type", "application/json");
//		headers.put("custId", custId);
//	
//		Map<String, Object> body = new HashMap<String, Object>();
//		body.put("status", "EMANDATE_SUCCESS");
//	
//		Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
//	
//		verifyResponseCodeAs200OK(responseObject);
//	
//		Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//	
//	}
//			@Test(description = "Verify Lead stage", dependsOnMethods = "TC034_EmandateCallback", groups = { "Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC035_VerifyLeadStage() {
//			
//			Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//					sessionToken, LendingLeadStages.EMANDATE_SUCCESS.getStage());
//			
//			}
//		@Test(description = "Fetch Dynamic T and C", dependsOnMethods = "TC034_EmandateCallback", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC036_FetchDynamicTnc() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//		
//			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//		
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//		
//				code = responseObject.jsonPath().getString("data.state.code");
//				tncName = responseObject.jsonPath().getString("data.state.tncName");
//				url = responseObject.jsonPath().getString("data.state.url");
//				uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//				md5 = responseObject.jsonPath().getString("data.state.md5");
//			}
//		
//		}
//		
//	
//		@Test(description = "Fetch Dynamic T and C Sanction Letter", dependsOnMethods = "TC036_FetchDynamicTnc", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC037_FetchDynamicTncSanctionLetter() {
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("tncType", "LOAN_SANCTION_TNC");
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//		
//			Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//		
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			if (responseObject.jsonPath().getString("meta.status").equals("success")) {
//		
//				codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
//				tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
//				urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
//				uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//				md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
//			}
//		
//		}
//		
//		@Test(description = "Verify submit application", dependsOnMethods = "TC037_FetchDynamicTncSanctionLetter", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC038_SubmitApplication() throws InterruptedException {
//		
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//		
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Content-Type", "application/json");
//			headers.put("session_token", sessionToken);
//		
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("accept", 1);
//			body.put("tncName", tncName);
//			body.put("uniqueIdentifier", uniqueIdentifier);
//			body.put("md5", md5);
//		
//			body.put("accept1", 1);
//			body.put("tncName1", tncNameSanctionLetter);
//		
//			body.put("uniqueIdentifier1", uniqueIdentifierSanctionLetter);
//			body.put("md51", md5SanctionLetter);
//		
//			Response responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body);
//		
//			LOGGER.info("Status Code : " + responseObject.getStatusCode());
//			if (responseObject.jsonPath().getString("meta.status").contentEquals("failed"))
//		
//			{
//				LOGGER.info("Try again");
//				responseObject = lendingBaseClassObject.submitApplication(queryParams, headers, body);
//				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//		
//			}
//		
//			else if (responseObject.jsonPath().getString("meta.stage").contentEquals("LMS_POST")) {
//				verifyResponseCodeAs200OK(responseObject);
//				Assert.assertEquals(responseObject.jsonPath().getString("meta.subStage"), "APPLICATION_PENDING");
//				Assert.assertEquals(responseObject.jsonPath().getString("meta.status"), "success");
//				Assert.assertEquals(responseObject.jsonPath().getString("data.state"), "Loan Application is Pending");
//		
//			}
//		
//		}
//		
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC038_SubmitApplication", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC039_VerifyLeadStage() {
//		
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(ENTITY_TYPE, SOLUTION, CHANNEL,
//				sessionToken, LendingLeadStages.APPLICATION_PENDING.getStage());
//		
//		}
//		
//		@Test(description = "Sheet upload from panel",dependsOnMethods = "TC039_VerifyLeadStage", groups = {
//				"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC040_UploadSheetONPanel() throws InterruptedException, IOException {
//		
//			TokenXMV tokenXMW = new TokenXMV();
//			Response responseObject = MiddlewareServicesObject.v1Token("9560526665", "paytm@123");
//			String XMWToken = responseObject.getHeader("Set-Cookie").toString();
//		
//			System.out.println("XMW token is :" + XMWToken);
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Content-Type", "multipart/form-data");
//			headers.put("Cookie", XMWToken);
//		
//			File fileUpload = new File(
//					"src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/BusinessSampleFile.csv");
//			FileWriter outputfile = new FileWriter(fileUpload);
//			CSVWriter writer = new CSVWriter(outputfile);
//			String[] header = {"Lead Id", "Business status ", "Rejection Reason\n" };
//			writer.writeNext(header);
//			String[] data1 = { leadId, "APPROVED", "N/A" };
//			writer.writeNext(data1);
//			writer.flush();
//			writer.close();
//		
//			responseObject = lendingBaseClassObject.uploadSheetFromPanel(headers, fileUpload, "BUSINESS_STATUS");
//			if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
//					.contentEquals(" has been successfully uploaded")) {
//				LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
//				verifyResponseCodeAs200OK(responseObject);
//				Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
//		
//			}
//		
//		}
//		
//		@Test(description = "Verify Lead stage", dependsOnMethods = "TC040_UploadSheetONPanel", groups = { "Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC041_VerifyLeadStage() {
//			
//		
//			
//		     Response responseObject = null;
//		
//			 Map<String,String> queryParams=new HashMap<String,String>();
//		        queryParams.put("entityType",ENTITY_TYPE );
//		        queryParams.put("solution",SOLUTION);
//		        queryParams.put("channel",CHANNEL);
//		
//		        Map<String,String> headers=new HashMap<String,String>();
//		        headers.put("session_token",sessionToken);
//		
//		for(int i=0;i<6;i++)
//		        {
//		        responseObject= fetchLeadDetails(queryParams, headers);
//		
//			        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
//			        {
//			            LOGGER.info("Verify Lead Stage "+responseObject.jsonPath().getString("stage"));
//			           break;
//		
//			        }
//			           
//		        
//		        }
//		
//		  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
//		
//			}
//		
//			 
//		/**
//		 * Method to set headers which are used in lead creation request
//		 * 
//		 * @return
//		 */
//		public Map<String, String> setcommonHeaders() {
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("session_token", sessionToken);
//			headers.put("Content-Type", "application/json;charset=utf-8");
//
//			return headers;
//		}
//
//}
