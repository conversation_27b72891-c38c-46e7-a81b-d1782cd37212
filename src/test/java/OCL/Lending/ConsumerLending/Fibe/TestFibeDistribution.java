package OCL.Lending.ConsumerLending.Fibe;

import OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;


public class TestFibeDistribution extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(TestFibeDistribution.class);
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Properties prop = this.configProperties();

    String sessionToken = "";
    String leadId = "";
    String custId = "1001279552";
    String consumerNumber = "8010630049";
    String consumerPassword = "paytm@123";
    String token = "";
    String stage = "";
    String feStage = "";
    String userIPAddress = "";
    String loanUserLatitude = "";
    String loanUserLongitude = "";
    String tncAdditionalParam = "";
    String staticTncAcceptanceTimeStamp = "";
    String lenderCustomerId = "";
    String requestBodyJsonPath = "";
    String Pan = "**********";
    String Email = "";
    String DOB = "";
    String applicationId = "";
    String bureauRequest = "";
    String bureauResponse = "";
    String bureauCreditState = "";
    String breCreditScore = "";
    String PanValidationTimeStamp = "";
    String uuid = "";
    String md5 = "";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier = "";
    ;
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String sanctionLetterAcceptanceTimestamp = "";
    String kybSecondaryTNCDisplayURL = "";
    String loanAgreementDate = "";
    String kybTNCDisplayURL = "";
    String panNameMatchTimeStamp = "";
    String panNameMatchPercentage = "";
    String breLastFetchDate = "";
    String loanTenure = "";
    String loanAmount = "";
    String loanEquatedMonthlyInstallment = "";
    String loanRateofInterest = "";
    String loanInterestAmount = "";
    String loanProcessingFeeRate = "";
    String loanDisbursalAmount = "";
    String stampDutyCharges = "";
    String brokerPeriodInterest = "";

    private String randomBankAccountNumber = "";
    private static final boolean MOCK_BRE1 = false;

    private static final boolean MOCK_LIS_CREATE_APPLICATION = true;
    private static final boolean MOCK_LENDER_BRE = true;
    private static final boolean MOCK_LOAN_ONBOARDING = true;
    private static final boolean MOCK_LOAN_DISBURSEMENT = true;

    Response responseObject = null;
    private String lenderApplicationId;
    private String currentBureau;

    @BeforeClass()
    public void intitializeInputData() throws IOException {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);


    }

    @Test(description = "Verify whether there is any existing PL Fibe distribution", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchLeadDetails_PL_FibeDistribution() {

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken, custId);

        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {


            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


        }

    }


    @Test(description = "Reset the existing lead of the number", dependsOnMethods = "TC001_FetchLeadDetails_PL_FibeDistribution", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_DeleteExistingLead() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId", leadId);
        queryParams.put("custId", custId);

        queryParams.put("leadId", leadId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_QA, custId, LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);


        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }


    @Test(description = "Create MCA V3 ABFL Lead", dependsOnMethods = "TC002_DeleteExistingLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_Create_PL_FIBE_DISTRIBUTION_Lead() {

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);


        Map<String, String> headers = new HashMap<String, String>();
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", "9004083236");
        body.put("PRODUCT_ID", "214");
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE", "DISTRIBUTION");
        body.put("PRODUCT_VERSION", "1");
        body.put("LOAN_OFFER_ID", "PL_FIBE_shivangi_OE1_41b71414");
        body.put("BASE_ID", "PL_FIBE_shivangi_OE1_41b71414");
        body.put("WHITELISTING_SOURCE", "RISK");
        body.put("LENDER_NAME", "FIBE");
        body.put("LENDER_ID", "13");
        body.put("BUREAU_PRIORITY", "EXPERIAN");


        Map<String, String> finalHeaders = headers;

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, prop.getProperty("CreateLeadForFibe"));
            final boolean status = responseObject.getStatusCode() == 201;
            return status;
        });

        if (responseObject.getStatusCode() == 201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"), "9004083236");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"), "214");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"), "PL");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"), "DISTRIBUTION");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"), "1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"), "PL_FIBE_shivangi_OE1_41b71414");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"), "PL_FIBE_shivangi_OE1_41b71414");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"), "RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"), "FIBE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"), "EXPERIAN");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "13");

            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());

    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA", dependsOnMethods = "TC003_Create_PL_FIBE_DISTRIBUTION_Lead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_FetchLeadAllData() throws JSONException {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
            if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());

            return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage());
        });
        LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

        LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");

    }

    @Test(description = "Update lead basic details", dependsOnMethods = "TC004_FetchLeadAllData", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_UpdateLeadOccupationDetails() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);


        Map<String, String> headers = new HashMap<String, String>();
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("ipAddress", "***************");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "OCCUPATION_DETAILS");
        body.put("DOB", "1973-01-01");
        body.put("PAN", Pan);
        body.put("EMAIL", "<EMAIL>");
        body.put("STATIC_TNC_SETNAME", "fibe_platform_tnc");
        body.put("LENDER_STATIC_TNC_SETNAME", "fibe_bd_tnc");
        body.put("IS_FIELD_EDITED", "FALSE");
        body.put("PAN_DOB_VALIDATED", "TRUE");
        body.put("OCCUPATION", "Salaried");
        body.put("MONTHLY_INCOME", "40000");
        body.put("LOAN_PURPOSE", "Personal Use");
        body.put("PINCODE", "411015");
        body.put("F_NAME", "TOUCH");
        body.put("M_NAME", "WOOD");
        body.put("L_NAME", "LIMITED");
        body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
        body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");


        Map<String, String> finalHeaders = headers;

        Map<String, String> finalQueryParams = queryParams;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(finalQueryParams, finalHeaders, body, prop.getProperty("OccupationDetailsForFibe"));
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage())) {
            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "260");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_ACCEPTANCE_IP"), "***************");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"), "TOUCH WOOD LIMITED");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"), "TOUCH");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"), "WOOD");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"), "LIMITED");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_SETNAME"), "fibe_bd_tnc");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"), "fibe_platform_tnc");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_ACCEPTANCE_DEVICE_ID"), "Xiaomi");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"), "Redmi");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), "77.4977");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"), "411015");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MONTHLY_INCOME"), "40000");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"), "<EMAIL>");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"), "1995-02-25");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"), "DISTRIBUTION");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"), "EXPERIAN");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), "27.2046");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION"), "Salaried");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "13");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"), "FIBE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"), Pan);
            leadId = responseObject.jsonPath().getString("leadId");
            custId = responseObject.jsonPath().getString("custId");
            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
            Email = responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
            DOB = responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
            Pan = responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
            PanValidationTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());

    }

    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC005_UpdateLeadOccupationDetails", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UpdateBureauDataSetInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("F_NAME", "Nimesh");
        body.put("L_NAME", "Hande");
        body.put("PAN", "**********");
        body.put("DOB", "1981-11-21");
        body.put("EMAIL", "<EMAIL>");

        responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body, true);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());


    }

    @Test(description = "Fetch CIR", dependsOnMethods = "TC006_UpdateBureauDataSetInSAI", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_FetchCIR_Async() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();

        Map<String, String> finalHeaders = headers;

        Map<String, String> finalQueryParams = queryParams;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1InitiateBureau(finalQueryParams, finalHeaders, body, prop.getProperty("AsyncBureau"));
            final boolean status = responseObject.getStatusCode() == 200;
            return status;
        });


        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BUREAU_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BUREAU_INITIATED.getStage());
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
        LOGGER.info("statusCode : " + responseObject.jsonPath().getString("statusCode"));
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BUREAU_INITIATED");
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "862");


    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA", dependsOnMethods = "TC007_FetchCIR_Async", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_FetchLeadAllData_ForBureauSuccess() throws JSONException {

        try {

            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE1_SUCCESS.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_SUCCESS.getStage());
            });

        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }


        if (LendingConstants.IS_MOCK_CALLBACK && MOCK_BRE1 && responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage())) {

            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE1_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "705");

            //Hit BRE1 Callback

            LOGGER.info("Actual Callback not coming from risk so hitting BRE1 mock Callback");
            responseObject = lendingBaseClassObject.BRE1CallbackGeneric(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.FIBE_DISTRIBUTION, custId, prop.getProperty("LISCreateApplicationRequest"));


        }
        try {


            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage());
            });
        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }


        if (LendingConstants.IS_MOCK_CALLBACK && MOCK_LIS_CREATE_APPLICATION && responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage())) {

            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "438");

            // Hit LIS Create application Callback

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("LENDER_APPLICATION_ID", "28241816rcuovu");
            body.put("LOAN_SANCTION_DATE", "2023-09-11");
            body.put("LOAN_MAX_AMOUNT", "159000.0");
            body.put("LOAN_MIN_AMOUNT", "5000.0");
            body.put("LENDER_CREATE_APPLICATION_SUCCESS", "TRUE");
            body.put("NACH_AMOUNT", "1457614");


            LOGGER.info("Actual Callback not coming from risk so hitting LIS  mock Callback");
            responseObject = lendingBaseClassObject.lisCreateApplicationFibe(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, custId, "LENDER_CREATE_APPLICATION_SUCCESS", prop.getProperty("LISCreateApplicationRequest"), body);


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LIS_CREATE_APPLICATION_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE_COMPLETED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "440");
        currentBureau = responseObject.jsonPath().getString("solutionAdditionalInfo.CURRENT_BUREAU");
        lenderApplicationId = responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_APPLICATION_ID");

    }

    @Test(description = "Verify Loan Offer Accepted", groups = {"Regression"}, dependsOnMethods = "TC008_FetchLeadAllData_ForBureauSuccess")
    @Owner(emailId = "<EMAIL>")
    public void TC009_LoanOfferAccept() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "OFFER_ACCEPTED");

        //solutionAdditionalInfo
        body.put("LOAN_TENURE", "15");
        body.put("LOAN_TENURE_UNIT", "MONTH");
        body.put("LOAN_AMOUNT_IN_NUMBER", "50000");
        body.put("LOAN_AMOUNT_IN_WORDS", "Fifty Thousand");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "3724");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Three thousand seven hundred  twenty four");
        body.put("LOAN_RATE_OF_INTEREST", "17");
        body.put("LOAN_INTEREST_AMOUNT", "48800");
        body.put("LOAN_PROCESSING_FEE", "1000");
        body.put("PROCESSING_FEE_RATE", "2.0");
        body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1180");
        body.put("LOAN_DISBURSAL_AMOUNT", "48800");
        body.put("STAMP_DUTY_CHARGES", "20.00");
        body.put("BROKEN_PERIOD_INTEREST", "0");
        body.put("OFFER_ACCEPTANCE_TIMESTAMP", "1671667200000");
        body.put("LOAN_PURPOSE_STATIC_TNC_SETNAME", "purpose_of_loan_static_tnc_setname");

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, prop.getProperty("LoanOfferAcceptanceRequest"));

        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage())) {
            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "367");
            loanTenure = responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
            loanAmount = responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
            loanEquatedMonthlyInstallment = responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
            loanRateofInterest = responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
            loanInterestAmount = responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
            loanProcessingFeeRate = responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
            loanDisbursalAmount = responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
            stampDutyCharges = responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
            brokerPeriodInterest = responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
        }

    }

    @Test(description = "Verify the ABFL lead Upload SELFIE ", groups = {"Regression"}, dependsOnMethods = "TC009_LoanOfferAccept")
    @Owner(emailId = "<EMAIL>")
    public void TC012_UploadSelfie() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, sessionToken, "selfie", "SEARCH_BY_PAN.jpeg");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2021");


    }

    @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC012_UploadSelfie", groups = {
            "Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC013_VerifyUploadedSelfie() {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        if (responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

        } else {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


        }

    }


    @Test(description = "Initiate KYC using SBP", groups = {"Regression"}, dependsOnMethods = "TC013_VerifyUploadedSelfie")
    @Owner(emailId = "<EMAIL>")
    public void TC014_InitiateKYC_UsingSearchByPan() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);

        Map<String, String> headers = new HashMap<String, String>();
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("kycMode", "SEARCH_BY_PAN");

        responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body, prop.getProperty("SearchByPanRequest"), "V2");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

    }

    @Test(description = "Verify lead details after kyc initiate", groups = {"Regression"}, dependsOnMethods = "TC014_InitiateKYC_UsingSearchByPan")
    @Owner(emailId = "<EMAIL>")
    public void TC015_FetchDataPostKYCIntiated() throws JSONException {

        try {


            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage());
            });
        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }


        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_INITIATED.getStage())) {
            LOGGER.info("Actual Callback not coming from KYC so hitting KYC SBP mock Callback");

            responseObject = lendingBaseClassObject.KYCCallbackusingSearchBYPan(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.DIY_P4B_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.FIBE_DISTRIBUTION, custId);

        }
        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "375");

    }

    @Test(description = "Verify ADR", groups = {"Regression"}, dependsOnMethods = "TC015_FetchDataPostKYCIntiated")
    @Owner(emailId = "<EMAIL>")
    public void TC016_AdditionalDataCapture() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("IPAddress", "***********");
        headers.put("deviceIdentifier", "OE Automation Test shivangi");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");

        //solutionAdditionalInfo
        body.put("COMPANY_NAME", "shivangi enterprises");
        body.put("MARITAL_STATUS", "MARRIED");
        body.put("line1", "D 1 PLOT NUMBER 325 SUKHWANI PARK BUILDING");
        body.put("line2", "NORTH MAIN ROAD KOREGAON PARK");
        body.put("city", "Mumbai");
        body.put("state", "Maharashtra");
        body.put("pincode", "401105");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType", "CORRESPONDENCE");
        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, prop.getProperty("AdditionalDataCaptured"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "386");


    }

    @Test(description = "Verify Penny drop Callback", groups = {"Regression"}, dependsOnMethods = "TC016_AdditionalDataCapture")
    @Owner(emailId = "<EMAIL>")
    public void TC017_PennyDropCallback() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("IPAddress", "***********");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BANK_VERIFICATION_SUCCESS");

        //solutionAdditionalInfo
        body.put("bankName", "State bank of India");
        body.put("bankAccountNumber", "****************");
        body.put("ifsc", "SBIN0012014");
        body.put("bankAccountHolderName", "shivangi");
        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, prop.getProperty("PennyDropRequest"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "428");


    }

    @Test(description = "Verify E-mandate  Callback", groups = {"Regression"}, dependsOnMethods = "TC017_PennyDropCallback")
    @Owner(emailId = "<EMAIL>")
    public void TC017_EmandateCallback() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.FIBE_DISTRIBUTION);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("IPAddress", "***********");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "EMANDATE_SUCCESS");

        //solutionAdditionalInfo
        body.put("UMRN", "HSBC90877221123");
        body.put("MANDATE_EXPIRY_DATE", "2026-08-26T08:38:12.000Z");
        body.put("MANDATE_MODE", "EN");
        body.put("MANDATE_SETUP_TIMESTAMP", "1694422092000");
        body.put("MANDATE_START_DATE", "2023-09-13");
        body.put("MANDATE_SUBSCRIPTION_ID", "409789113816");
        body.put("MANDATE_FREQUENCY_UNIT", "ONDEMAND");
        body.put("MANDATE_NACH_AMOUNT", "60000.00");
        body.put("MANDATE_PAYMENT_MODE", "DEBIT_CARD");
        body.put("MANDATE_STATUS", "AUTHORIZED");
        body.put("MANDATE_SUB_STATUS", "NPCI_PENDING");
        body.put("MANDATE_AMOUNT_TYPE", "VARIABLE");

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body, prop.getProperty("EmandateRequest"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "557");



    }

    @Test(description = "Verify lead details after e-mandate", groups = {"Regression"}, dependsOnMethods = "TC017_EmandateCallback")
    @Owner(emailId = "<EMAIL>")
    public void TC018_FetchDataMandate() throws JSONException {

        try {


            Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
            {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_BASIC_DATA, sessionToken, custId);
                if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_DATA_UPDATE_SUCCESS.getStage()))
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LIS_DATA_UPDATE_SUCCESS.getStage());
                return responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_DATA_UPDATE_SUCCESS.getStage());
            });
        } catch (Exception e) {
            System.out.println("FAILED");
            e.printStackTrace();
        }


        if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_DATA_UPDATE_REQUEST_SUCCESS.getStage())) {
            LOGGER.info("Actual Callback not coming from LIS so hitting LIS mock Callback");

            responseObject = lendingBaseClassObject.callbackWithOnlyWorkflowOperation(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.FIBE_DISTRIBUTION,custId, prop.getProperty("LenderApplicatedUpdatedWorkflowOperation"),prop.getProperty("LISDataUpdateCallback"));

        }
        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LIS_DATA_UPDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "261");

    }

}











