package OCL.Lending.ConsumerLending.PersonalLoanLTFS;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import io.restassured.response.Response;

public class LTFS extends BaseMethod{


    private static final Logger LOGGER = LogManager.getLogger(LTFS.class);
    LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();

    String sessionToken = "";
    String leadId="";
    String custId="1702093868";
    String consumerNumber="7775555595";
    String consumerPassword="paytm@123";
    String token="";
    String userIPAddress="";
    String staticTncAcceptanceTimeStamp="";
    String lenderCustomerId="";
    String requestBodyJsonPath="";
    String Email="";
    String DOB="";
    String PanValidationTimeStamp="";
    String md5="";
    String code="";
    String tncName="";
    String url="";
    String uniqueIdentifier="";
    String codeSanctionLetter="";
    String tncNameSanctionLetter="";
    String urlSanctionLetter="";
    String uniqueIdentifierSanctionLetter="";
    String md5SanctionLetter="";
    String sanctionLetterAcceptanceTimestamp="";
    String kybSecondaryTNCDisplayURL="";
    String loanAgreementDate="";
    String kybTNCDisplayURL="";
    String loanTenure="";
    String loanAmount="";
    String loanEquatedMonthlyInstallment="";
    String loanRateofInterest="";
    String loanInterestAmount="";
    String loanProcessingFeeRate="";
    String loanDisbursalAmount="";
    String stampDutyCharges="";
    String brokerPeriodInterest="";

    private String randomBankAccountNumber="";
    private static final boolean MOCK_BRE1 = true;
    private static final boolean MOCK_BRE2 = true;
    private static final boolean MOCK_BRE3 = true;


    Response responseObject= null;

    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }
    

    @Test(description = "Verify whether there is any existing PL LTFS lead present or not",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_FetchLeadDetails()
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200)
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId=responseObject.jsonPath().getString("leadId");
        }

        if(responseObject.jsonPath().getInt("statusCode")==404)
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
        }

    }


    @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDetails",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_DeleteExistingLead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId",leadId);
        queryParams.put("custId", custId);
        queryParams.put("leadId",leadId);

        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);
        lendingBaseClassObject.resetLendingLeads(queryParams, headers);
    }


    @Test(description = "Create PL LTFS Lead",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_Create_PL_LTFS_Lead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", LendingConstants.LTFS_IP_ADDRESS);

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", consumerNumber);
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE", "RISK");
        body.put("PRODUCT_VERSION", "1");
        body.put("LENDER_ID", LendingConstants.LTFS_LENDER_ID);
        body.put("WHITELISTING_SOURCE", LendingConstants.WHITELISTING_SOURCE);
        body.put("BUREAU_PRIORITY", LendingConstants.BUREAU_PRIORITY);
        body.put("PINCODE", LendingConstants.LTFS_PINCODE);
        body.put("IS_BRE3_REQUIRED", LendingConstants.TRUE);
        body.put("IS_FATHER_NAME_REQUIRED",LendingConstants.TRUE);
        body.put("IS_EMANDATE_ELIGIBLE", LendingConstants.TRUE);
        body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", LendingConstants.TRUE);
        body.put("IS_RTO_FLOW", LendingConstants.FALSE);
        body.put("PRODUCT_ID", LendingConstants.LTFS_PRODUCT_ID);
        body.put("LOAN_OFFER_ID",LendingConstants.LTFS_LOAN_OFFER_ID);
        body.put("BASE_ID", LendingConstants.LTFS_BASE_ID);
        body.put("MARITAL_STATUS", LendingConstants.MARITAL_STATUS);
        body.put("LENDER_NAME", LendingConstants.LTFS_LENDER_NAME);
        body.put("LENDING_DYNAMIC_TNC", LendingConstants.LTFS_LENDING_DYNAMIC_TNC);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.LTFS_LENDING_DYNAMIC_SECONDARY_TNC);


        requestBodyJsonPath= "MerchantService/V1/workflow/lead/LTFS/LTFSCreateLeadRequest.json";

        Map<String, String> finalHeaders1 = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders1,body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode()==201;
            return status;
        });


        if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),LendingConstants.LTFS_PRODUCT_ID);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),LendingConstants.TRUE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),LendingConstants.TRUE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),LendingConstants.MARITAL_STATUS);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),LendingConstants.LTFS_LENDING_DYNAMIC_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),LendingConstants.LTFS_LENDING_DYNAMIC_SECONDARY_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),LendingConstants.LTFS_LENDER_ID);

            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");
            userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());

    }


    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC003_Create_PL_LTFS_Lead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_FetchLeadAllData() throws JSONException
    {
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
    }


    @Test(description = "Update Lead Basic Details and Occupation Details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_BD_OD()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation","OCCUPATION_DETAILS");
        body.put("OCCUPATION", LendingConstants.PLv3_OCCUPATION);
        body.put("DOB", LendingConstants.LTFS_DOB);
        body.put("PAN", LendingConstants.LTFS_PAN);
        body.put("EMAIL", LendingConstants.LTFS_EMAIL);
        body.put("PINCODE", LendingConstants.LTFS_PINCODE);
        body.put("LOAN_PURPOSE", LendingConstants.LTFS_LOAN_PURPOSE);
        body.put("GENDER", LendingConstants.LTFS_GENDER);
        body.put("STATIC_TNC_SETNAME", LendingConstants.LTFS_STATIC_TNC_SETNAME);
        body.put("LENDER_STATIC_TNC_SETNAME", LendingConstants.LTFS_LENDER_STATIC_TNC_SETNAME);
        body.put("BUSINESS_NAME", LendingConstants.LTFS_BUSINESS_NAME);
        body.put("EMPLOYER_NAME", LendingConstants.EMPLOYER_NAME);
        body.put("ANNUAL_INCOME", LendingConstants.ANNUAL_INCOME);
        body.put("EMPLOYER_ID", LendingConstants.EMPLOYER_ID);
        body.put("LOAN_PURPOSE_KEY", "2");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/OccupationDetailsRequest.json";

        Map<String, String> finalQueryParams = queryParams;
        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(finalQueryParams, finalHeaders,body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });


        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);


            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");
            userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
            Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
            DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
            PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");

        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
    }


    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC005_BD_OD",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_FetchLeadAllData() throws JSONException
    {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });


        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),LendingConstants.LTFS_EMAIL);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),LendingConstants.LTFS_DOB);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),LendingConstants.LTFS_PAN);
            lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
    }

    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC006_FetchLeadAllData", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_UpdateBureauDataSetInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("F_NAME", "BHAIRAVI");
        body.put("L_NAME", "LATASREE");
        body.put("EMAIL", "<EMAIL>");
        body.put("GENDER", "FEMALE");
        body.put("PINCODE", "600024");
        body.put("PAN", "**********");
        body.put("DOB", "1979-10-05");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/LTFS/FetchCIRDataUpdateRequest.json";


        Map<String, String> finalHeaders1 = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });


        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());
    }



    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC007_UpdateBureauDataSetInSAI",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_FetchLeadAllData() throws JSONException
    {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });


        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
        {

            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
    }


    @Test(description = "Fetch CIR",dependsOnMethods = "TC008_FetchLeadAllData",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_FetchCIR()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);

        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String,Object> body=new HashMap<String,Object>();

        String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";


        Map<String, String> finalHeaders1 = headers;
        Map<String, String> finalQueryParams = queryParams;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.v1InitiateBureau(finalQueryParams, headers,body,requestjsonpath);
            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });


        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_INITIATED.getStage());
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"BUREAU_INITIATED");
        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"862");


    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL DATA",dependsOnMethods = "TC009_FetchCIR",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC010_FetchLeadAllData_AfterBureauSuccess() throws JSONException, InterruptedException {

        Thread.sleep(10000);

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });


        if(LendingConstants.IS_MOCK_CALLBACK && MOCK_BRE1 &&responseObject.jsonPath().getInt("statusCode")==200 &&
                responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),
                    LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");

            //Hit BRE1 Callback

            LOGGER.info("Actual Callback not coming from risk so hitting BRE1 mock Callback");

            responseObject=    lendingBaseClassObject.BRE1CallbackforLTFS(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,custId);
        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
    }

    @Test(description = "Update KYC data set in SAI Table", dependsOnMethods = "TC010_FetchLeadAllData_AfterBureauSuccess", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC011_UpdateKYCDataSetInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("GENDER", LendingConstants.NS_GENDER);
        body.put("NSDL_NAME", LendingConstants.NS_NSDL_NAME);
        body.put("PAN", LendingConstants.NS_PAN);
        body.put("DOB", LendingConstants.NS_DOB);

        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/KYCDataUpdateRequest.json";
        responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);
        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OFFER_GENERATED.getStage());
    }


    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC011_UpdateKYCDataSetInSAI",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC012_FetchLeadAllData() throws JSONException
    {

        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,
                    LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            final boolean status = responseObject.getStatusCode()==200;
            return status;
        });


        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
        {

            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LIS_SUBMIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"818");
        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
    }



    @Test(description = "Verify Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC010_UpdateKYCDataSetInSAI")
    @Owner(emailId = "<EMAIL>")
    public void TC011_LoanOfferAccept() {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation", "OFFER_ACCEPTED");

        //solutionAdditionalInfo
        body.put("LOAN_TENURE", "18");
        body.put("LOAN_TENURE_UNIT", "MONTH");
        body.put("LOAN_AMOUNT_IN_NUMBER", "90000");
        body.put("LOAN_AMOUNT_IN_WORDS", "Ninety Thousand");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "6003");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Six thousand three");
        body.put("LOAN_RATE_OF_INTEREST", "24");
        body.put("LOAN_INTEREST_AMOUNT", "1003");
        body.put("LOAN_PROCESSING_FEE", "4050");
        body.put("PROCESSING_FEE_RATE", "4.5");
        body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "4779");
        body.put("LOAN_DISBURSAL_AMOUNT", "85121");
        body.put("STAMP_DUTY_CHARGES", "100");
        body.put("BROKEN_PERIOD_INTEREST", "0");
        body.put("LOAN_PRICING_GRID_VERSION", "1684846444");
        body.put("TOTAL_AMOUNT_PAID_BY_BORROWER", "112933");
        body.put("ANNUAL_PERCENTAGE_RATE", "31.7");
        body.put("LOAN_TOTAL_INTEREST_AMOUNT", "18054");
        body.put("LOAN_OTHER_UPFRONT_CHARGES", "4879");
        body.put("LOAN_PURPOSE_STATIC_TNC_SETNAME", "purpose_of_loan_static_tnc_setname_");
        body.put("OFFER_ACCEPTANCE_TIMESTAMP", "1685903400000");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/LoanOfferAcceptanceRequest.json";

        responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
            loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
            loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
            loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
            loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
            loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
            loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
            loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
            stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
            brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
        }
    }

    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC011_LoanOfferAccept",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC012_FetchLeadAllData_AfterOfferAcceptance() throws JSONException
    {

        for(int i=0;i<55;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_SUBMIT_SUCCESS.getStage()))
                break;

        }

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_SUBMIT_SUCCESS.getStage()))
        {

            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LIS_SUBMIT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"818");


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LIS_SUBMIT_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"818");


    }


    @Test(description = "Verify the ABFL lead Upload SELFIE ",groups = {"Regression"},dependsOnMethods = "TC012_FetchLeadAllData_AfterOfferAcceptance")
    @Owner(emailId = "<EMAIL>")
    public void TC013_UploadSelfie() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.KYCImageUpload("selfie", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION, LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2, sessionToken,"selfie","SEARCH_BY_PAN_TCL.jpg");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");


    }

    @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC013_UploadSelfie", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC014_VerifyUploadedSelfie() {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

        }else {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


        }

    }

    @Test(description="Initiate KYC using SBP",groups = {"Regression"},dependsOnMethods = "TC014_VerifyUploadedSelfie")
    @Owner(emailId = "<EMAIL>")
    public void TC015_InitiateKYC_UsingSearchByPan() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("kycMode","SEARCH_BY_PAN");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";

        responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath,"V2");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

    }

    @Test(description = "Verify lead details afterkyc initiate",groups={"Regression"},dependsOnMethods = "TC015_InitiateKYC_UsingSearchByPan")
    @Owner(emailId = "<EMAIL>")
    public void TC016_FetchDataPostKYCIntiated() throws JSONException
    {

        for(int i =0;i<=35;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_COMPLETED.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_INITIATED.getStage()))
        {
            //Hit KYC Callback

            LOGGER.info("Actual Callback not coming from KYC so hitting KYC SBP mock Callback");

            responseObject=    lendingBaseClassObject.KYCCallbackusingSearchBYPan(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,custId);

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_COMPLETED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_COMPLETED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"375");


    }


    @Test(description = "Update address details", dependsOnMethods = "TC016_FetchDataPostKYCIntiated", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC017_UpdateKYCAddress() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("refId", "OtNq-188");
        body.put("statusCode", "0");
        body.put("pincode", "560076");
        body.put("postalCode", "0");
        body.put("state", "KL");
        body.put("city", "KENDRAPARA");
        body.put("line1", "KENDRAPARA");
        body.put("line2", "KENDRAPARA");
        body.put("line3", "KENDRAPARA");
        body.put("latitude", "0.0");
        body.put("longitude", "0.0");
        body.put("addressType", "RESIDENTIAL");
        body.put("addressSubType", "PERMANENT");
        body.put("status", "0");
        body.put("qcRequired", "false");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/PermanentResidentialAddressUpdateRequest.json";

        responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_COMPLETED.getStage());


    }

    @Test(description = "Confirm the address", dependsOnMethods = "TC017_UpdateKYCAddress", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC018_AddressConfirmation() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);
        queryParams.put("leadId",leadId);


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "ADDRESS_CONFIRMED");
        body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/AddressConfirmationRequest.json";

        responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_ADDRESS_CONSENT_ACCEPTED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.ADDRESS_CONFIRMED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2030");

    }



    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC018_AddressConfirmation",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC019_FetchLeadAllData_AfterAddressConfirmation() throws JSONException
    {

        for(int i=0;i<55;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_SUCCESS.getStage()))
                break;

        }

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(LendingConstants.IS_MOCK_CALLBACK && MOCK_BRE2 &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE2_REQUESTED.getStage()))
        {

            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE2_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"711");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"MALE");

            //Hit BRE2 Callback

            LOGGER.info("Actual Callback not coming from risk so hitting BRE1 mock Callback");

            responseObject=    lendingBaseClassObject. BRE2CallbackforTCL (leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,custId);


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE2_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"715");


    }

    @Test(description = "Capture additional details", dependsOnMethods = "TC019_FetchLeadAllData_AfterAddressConfirmation", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC020_CaptureAdditionalData() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
        body.put("BUSINESS_EMAIL", "<EMAIL>");
        body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
        body.put("FATHER_NAME", "Vinay Mohan Goswami");
        body.put("ANNUAL_INCOME", "100000");
        body.put("EMPLOYER_NAME", "Paytm");
        body.put("BUSINESS_NAME", "None");
        body.put("INCOME_SOURCE", "salary");
        body.put("OTHER_COMPANY_NAME", "paytm");
        body.put("addressType", "BUSINESS");
        body.put("addressSubType", "CORRESPONDENCE");
        body.put("line1", "House Number A-3");
        body.put("line2", "Saket Colony");
        body.put("line3", "148 Civil lines, Chowki chauraha");
        body.put("landmark", "Near Harshaimal shyamlal jewellers");
        body.put("city", "Bareilly");
        body.put("state", "UTTAR PRADESH");
        body.put("pincode", "560076");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/AdditionalDataRequiredRequest.json";

        responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.ADDITIONAL_DATA_CAPTURED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BRE3_IN_PROGRESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "386");

    }

    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC020_CaptureAdditionalData",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC021_FetchLeadAllData_AfterAdditionalDataCaptured() throws JSONException
    {

        for(int i=0;i<55;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE3_SUCCESS.getStage()))
                break;

        }

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(LendingConstants.IS_MOCK_CALLBACK && MOCK_BRE3 &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE3_REQUESTED.getStage()))
        {

            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_IN_PROGRESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1521");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"MALE");

            //Hit BRE3 Callback

            LOGGER.info("Actual Callback not coming from risk so hitting BRE3 mock Callback");

            responseObject=    lendingBaseClassObject. BRE3CallbackforTCL (leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,custId);


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");


    }

    @Test(description = "Update KYC data set in SAI Table", dependsOnMethods = "TC021_FetchLeadAllData_AfterAdditionalDataCaptured", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC022_UpdateKYCNameInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowMovementRequired", "false");
        body.put("CKYC_NAME","BENE CUSTOMER NAME");
        body.put("PAN", LendingConstants.LTFS_PAN);



        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/UpdatePanAndCKYCNameRequest.json";

        responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
        // responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BRE3_SUCCESS.getStage());


    }

    @Test(description = "Enter the bank details", dependsOnMethods = "TC022_UpdateKYCNameInSAI", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC023_SaveBankDetails() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        randomBankAccountNumber=Utilities.generateRandomBankAccountNumber();

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "VERIFY_BANK_DETAILS");
        body.put("EMANDATE_TYPE", "Internet Banking");
        body.put("bankName", "Allahabad  BANK");
        body.put("bankAccountNumber",randomBankAccountNumber);
        body.put("ifsc", "ALLA0001234");
        body.put("bankAccountHolderName", LendingConstants.BANK_NAME_STAGING3);

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        Response responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

        if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("baseResponseCode").equals( "BANK_VERIFICATION_SUCCESS")) {

            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "BANK_VERIFICATION_SUCCESS");

        }

        else {

            for (int i = 1; i < 6; i++) {
                LOGGER.info("Again hitting with same data: retry-count: " + i);
                responseObject = lendingBaseClassObject.saveBankDetailsNewAPI(queryParams, headers, body);

                if (responseObject.jsonPath().getString("baseResponseCode").equals("BANK_VERIFICATION_SUCCESS"))
                    break;
            }

        }


        Assert.assertEquals(responseObject.getStatusCode(),200);
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BANKING_ACTION_DONE.getStage());




    }

    @Test(description = "For ABFL lead Bank Verification", dependsOnMethods = "TC023_SaveBankDetails",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC024_FetchLeadPostBankVerification() {
        for(int i=0;i<5;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
                break;
            }
        }
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"Allahabad  BANK");
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),randomBankAccountNumber);
        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"ALLA0001234");



    }

    @Test(description = "Verify  Lead Emandate Callback", dependsOnMethods = "TC024_FetchLeadPostBankVerification", groups = {
            "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC025_EmandateCallback() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token",sessionToken);
        headers.put("Content-Type", "application/json");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "EMANDATE_SUCCESS");
        body.put("UMRN", "KKBK1234567890");

        body.put("MANDATE_EXPIRY_DATE", "2026-02-06T07:32:11.000");

        body.put("MANDATE_SETUP_TIMESTAMP", "1671667200000");

        body.put("MANDATE_MODE", "EN");

        body.put("EMANDATE_SUCCESS", "TRUE");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/TCL/EmandateCallbackRequest.json";

        responseObject = lendingBaseClassObject.UpdateLead(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "557");

    }

    @Test(description = "Emandate Callback Stage Verification", dependsOnMethods = "TC025_EmandateCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC026_FetchLeadPostEmandate() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.UMRN"),"KKBK1234567890");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MANDATE_EXPIRY_DATE"),"2026-02-06T07:32:11.000");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MANDATE_SETUP_TIMESTAMP"),"1671667200000");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MANDATE_MODE"),"EN");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMANDATE_SUCCESS"),"TRUE");



    }

    @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC026_FetchLeadPostEmandate")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC027_GenerateLoanAgreement()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);



        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token",sessionToken);


        Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " +responseObject.getStatusCode());

        switch(responseObject.jsonPath().getString("meta.status"))
        {

            case "success":
                code=responseObject.jsonPath().getString("data.state.code");
                tncName=responseObject.jsonPath().getString("data.state.tncName");
                url=responseObject.jsonPath().getString("data.state.url");
                uniqueIdentifier=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
                md5=responseObject.jsonPath().getString("data.state.md5");

                break;

            case "failed":
                responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
                break;


        }




    }

    @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC027_GenerateLoanAgreement")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC028_GenerateSanctionLetter()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);
        queryParams.put("tncType", "LOAN_SANCTION_TNC");

        Map<String,String> headers=new HashMap<String,String>();
        headers.put("session_token",sessionToken);


        Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " +responseObject.getStatusCode());
        if(responseObject.jsonPath().getString("meta.status").equals("success"))
        {

            codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
            tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
            urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
        }


    }


    @Test(description = "Accept Loan Agreement",dependsOnMethods = "TC028_GenerateSanctionLetter",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC029_AcceptLoanAgreement()
    {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2);



        Map<String,String> headers=new HashMap<String,String>();
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","LOAN_AGREEMENT_ACCEPTED");
        body.put("LENDING_DYNAMIC_TNC", tncName);
        body.put("KYB_TNC_REF_NO", uniqueIdentifier);
        body.put("TNC_ACCEPTED_CODE", md5);
        body.put("TNC_ACCEPTED_VERSION", 3);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
        body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC_VERSION", 2);
        body.put("OTP_VERIFICATION_TIMESTAMP", "2023-06-07 20:16:07.951");
        body.put("OTP_VERIFICATION_IP", "***********");



        requestBodyJsonPath="MerchantService/V1/workflow/TCL/LoanAgreementAcceptRequest.json";

        for(int i=0;i<2;i++)
        {

            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body,requestBodyJsonPath);

            if(responseObject.getStatusCode()==200)
                break;
        }


        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC"),tncNameSanctionLetter);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),tncName);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_AGREEMENT_ACCEPTED"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_VERSION"),"3");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.SECONDARY_ACCEPTED_TNC_VERSION"),"2");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),tncNameSanctionLetter);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ACCEPTED_CODE"),tncName);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OTP_VERIFICATION_TIMESTAMP"),"2023-06-07 20:16:07.951");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OTP_VERIFICATION_IP"),"***********");



            lenderCustomerId=responseObject.jsonPath().getString("LENDER_CUSTOMER_ID");
            sanctionLetterAcceptanceTimestamp=responseObject.jsonPath().getString("SANCTION_LETTER_TNC_ACCEPTANCE_TIMESTAMP");
            kybSecondaryTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_SECONDARY_TNC_DISPLAY_URL");
            loanAgreementDate=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AGREEMENT_DATE");
            kybTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_TNC_DISPLAY_URL");



        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());

    }

    @Test(description = "Emandate Callback Stage Verification", dependsOnMethods = "TC029_AcceptLoanAgreement",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC030_FetchLeadPostAgreementAcceptance() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {



            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.PREDISBURSAL_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"312");

        }

        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage())) {



            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");

        }

    }

    @Test(description = "If PDC Callback is not received manually hit callback",dependsOnMethods = "TC030_FetchLeadPostAgreementAcceptance",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC031_VerifyPDCCallback() throws JSONException
    {

        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();

        for(int i=0;i<10;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage()))
                break;
        }



        //PDC Callback


        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage()))
        {
            body.put("workflowOperation","LOAN_APPLICATION_ACCEPTED");
            body.put("PDC_REASON_ID", "7");
            body.put("PDC_USER_MESSAGE", "Disbursal Done");
            body.put("BUSINESS_STATUS", "APPROVED_BY_LMS");
            body.put("PDC_SYSTEM_MESSAGE", "Direct Approval of Loan Account is enabled");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PDCLoanApplicationPendingCallbackRequest.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());

    }



    @Test(description = "Update lead details",dependsOnMethods = "TC031_VerifyPDCCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC032_VerifyLeadStage_LMSSubmitApplicationJob()
    {

        for(int i=0;i<40;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);


            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
                break;

        }



        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"346");

    }
    @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC032_VerifyLeadStage_LMSSubmitApplicationJob",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC033_LMSDataCallback() throws JSONException
    {

        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_JWT_Secret);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        for(int i=0;i<10;i++){
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
                break;
        }
        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage()))
        {
            body.put("workflowOperation","LMS_APPLICATION_APPROVED");
            body.put("LOAN_ACCOUNT_NUMBER", "PYTMPL_TCL"+Utilities.randomLendingLoanAccountNumberGenerator());
            body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
            body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
            body.put("LENDER_LOAN_ACCOUNT_NUMBER", "DXW-M10126-*********");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LMSSubmitApplicationCallbackRequest.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

            if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage()))
            {
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
                Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.APPLICATION_SUBMISSION_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"330");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
                Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_CREATED_ON"),"*************");
            }
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
    }

    @Test(description = "Verify the lead data using fetch Stratgey FETCH_STRATEGY_ALL_DATA",dependsOnMethods = "TC033_LMSDataCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC034_FetchLeadAllData() throws JSONException
    {
        for(int i=0;i<35;i++)
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.LTFS_SOLUTION_TYPE_LEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
            {
                break;
            }

        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
    }


}
