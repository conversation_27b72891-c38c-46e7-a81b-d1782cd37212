//package OCL.Lending.ConsumerLending;
//
//import java.math.BigInteger;
//import java.sql.SQLException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.apache.log4j.Logger;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import com.goldengate.common.BaseMethod;
//import com.paytm.apitools.util.annotations.Owner;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingConstants;
//import Services.LendingService.LendingLeadStages;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//import io.restassured.response.Response;
//
//public class TestPostpaidABFLDelite extends BaseMethod{
//	
//	private static final Logger LOGGER = Logger.getLogger(TestPostpaidABFLDelite.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
//		
//		 String sessionToken = "";
//		 String leadId="";
//		 String custId="";
//		 String agentNumber="7773330159";
//		 String agentPassword="paytm@123";
//		 String token="";
//		 String uuid="";
//		 String ckycStage="";
//		 String loanOffered="true";
//		 String maxLoanAmount="100000";
//		 String authorisedMonthlyLimit="";
//		 String stage="";
//		 String code="";
//		 String tncName="";
//		 String url="";
//		 String uniqueIdentifier="";
//		 String md5="";
//		 String codeSanctionLetter="";
//		 String tncNameSanctionLetter="";
//		 String urlSanctionLetter="";
//		 String uniqueIdentifierSanctionLetter="";
//		 String md5SanctionLetter="";
//		 
//	
//		 Map<String,String> commonHeaders;
//		 
//		@BeforeClass()
//		 public void intitializeInputData() {
//		
//			LOGGER.info(" Before Suite Method for Agent Login ");
//			sessionToken = ApplicantToken(agentNumber, agentPassword);
//			LOGGER.info("Applicant Token for Lending : " + sessionToken);
//		   
//			commonHeaders=setcommonHeaders();
//
//		}
//		 @Test(description = "Delete all existing leads of the number",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC001_DeleteExistingLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("mobile",agentNumber);
//			 			  
//	    	  Map<String,String> headers=new HashMap<String,String>();
//		      headers.put("Cookie", "JSESSIONID=8FCAC982A2C354041DC99E61E5A08683")   ;
//		     
//		      lendingBaseClassObject.merchantServiceDeleteAllLeadsV2(queryParams, headers);
//		    }
//		 
//		 @Test(description = "Verify whether there is any existing lead present or not",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC002_FetchLeadDeatils()
//		    {
//		      Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.LEAD_NOT_PRESENT.getStage());
//		     
//		      LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//		      Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data Not present for customer");
//		    }
//		
//		 @Test(description = "Create PostPaid Lead with all deatils",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC003_CreatePostpaidLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//	    	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  
//	    	  Map<String,String> headers=new HashMap<String,String>();
//		       headers = commonHeaders;
//		        
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("tnCSetName","Paytm_postpaid_Happyloans");
//		  	   body.put("LOAN_MIN_AMOUNT", "10000");
//		  	   body.put("LOAN_MAX_AMOUNT", "100000");
//		  	   body.put("BASE_ID", "RTO_POSTPAID_c291c361cd824ce196de1e3160773f64");
//			   body.put("LENDER_ID", "5");
//			   body.put("PRODUCT_ID", "10010");
//			   body.put("TNC_ADDITIONAL_PARAM", "STATIC_LOAN_OFFER_TNC");
//			   body.put("WHITELISTING_SOURCE", "CIR");
//			   body.put("OFFER_START_DATE", "2021-03-11");
//			   body.put("OFFER_END_DATE", "2021-06-09");
//			   body.put("PRODUCT_TYPE", "POSTPAID");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("LUP_ID", "1234");
//			   body.put("CONVENIENCE_FEE", "2.0");
//			   body.put("IS_SI_MANDATORY", false);
//			   body.put("GST", "18.0");
//			   body.put("LENDING_DYNAMIC_TNC", "postpaid_lender_agreement_abfl_delite");
//			   body.put("PPBL_CONSENT_REQUIRED", true);
//			   
//			  Response responseObject= lendingBaseClassObject.v1sdMerchantLead(queryParams, headers,body,true,LendingConstants.POSTPAID_V2_SOLUTION);
//				  if(responseObject.getStatusCode()==200)
//				  {
//					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Lead successfully created.");
//			        leadId=responseObject.jsonPath().getString("leadId");
//			      }
//			  
//				  else
//				  {
//				  LOGGER.info("Try to hit the API again");
//				  responseObject= lendingBaseClassObject.v1sdMerchantLead(queryParams, headers,body,true,LendingConstants.POSTPAID_V2_SOLUTION);
//				  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			      Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Lead successfully created.");
//			      leadId=responseObject.jsonPath().getString("leadId");
//				  }
//			  
//		    }
//		 
//		 @Test(description = "Verify the data of lead created",groups = {"Regression"}, dependsOnMethods = "TC003_CreatePostpaidLead")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC004_FetchTheCreatedLeadDeatils()
//		    {
//			 
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken, LendingLeadStages.LEAD_CREATED.getStage());
//		     
//		      LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.GST"),"18.0");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_ID"),"10010");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.ORIGINAL_BASE_ID"),"RTO_POSTPAID_c291c361cd824ce196de1e3160773f64");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PRODUCT_VERSION"),"1");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.OFFER_START_DATE"),"2021-03-11");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LUP_ID"),"1234");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.PPBL_CONSENT_REQUIRED"),"true");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"postpaid_lender_agreement_abfl_delite");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"100000");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_MIN_AMOUNT"),"10000");
//
//		  
//		      Assert.assertEquals(responseObject.jsonPath().getString("mobileNumber"),agentNumber);
//		      custId=responseObject.jsonPath().getString("custId");
//		   
//		    }
//		 
//			 @Test(description = "Verify the callback for PPBL OTP", dependsOnMethods = "TC004_FetchTheCreatedLeadDeatils", groups = {
//				"Regression" })
//			@Owner(emailId = "<EMAIL>", isAutomated = true)
//			public void TC005_PPBLOTPCallback() throws InterruptedException {
//			
//			Map<String, String> queryParams = new HashMap<String, String>();
//			queryParams.put("leadId", leadId);
//			queryParams.put("solution", LendingConstants.POSTPAID_V2_SOLUTION);
//			queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//			
//			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId);
//			
//			Map<String, String> headers = new HashMap<String, String>();
//			headers = lendingBaseClassObject.setCallbackHeaders(token, custId, "DIY_P4B_APP");
//			
//			Map<String, Object> body = new HashMap<String, Object>();
//			body.put("status", "PPBL_TNC_VERIFIED");
//			body.put("PPBL_TNC_TIMESTAMP", "1584098137622");
//			body.put("PPBL_TNC_ISSUER_IP", "127.0.0.1");
//			
//			Response responseObject = lendingBaseClassObject.callbackPPBLOTP(queryParams, headers, body);
//			Thread.sleep(1000);
//			
//			verifyResponseCodeAs200OK(responseObject);
//			LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
//			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//			
//			}
//			 
//			@Test(description = "Verify the data of lead created", dependsOnMethods = "TC005_PPBLOTPCallback", groups = {
//			"Regression" })
//		@Owner(emailId = "<EMAIL>", isAutomated = true)
//		public void TC006_FetchTheCreatedLeadDeatils() {
//		
//		Response responseObject = lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.POSTPAID_V2_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL,
//				sessionToken, LendingLeadStages.PPBL_TNC_VERIFIED.getStage());
//		custId = responseObject.jsonPath().getString("custId");
//				
//		}
//			
//		 @Test(description = "Add Basic details",groups = {"Regression"}, dependsOnMethods = "TC006_FetchTheCreatedLeadDeatils")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC007_AddBasicDetails() throws InterruptedException
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//	    	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			 			  
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  headers = commonHeaders;
//		     
//	    	   Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowSubOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_ABFL_DELITE);
//		  	   body.put("PAN", LendingConstants.PAN_ABFL_DELITE);
//		  	   body.put("EMAIL", LendingConstants.EMAIL_ABFL_DELITE);
//		  	
//		  	   Response responseObject= lendingBaseClassObject.addBasicDetails(queryParams, headers,body);
//		  	  
//		  	 lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//		       Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//		       Assert.assertEquals(responseObject.jsonPath().getBoolean("isLeadAlreadyExists"),true);
//		     		
//			 		   
//		    }
//		 
//		 @Test(description = "Verify the lead sub stage",groups = {"Regression"}, dependsOnMethods = "TC007_AddBasicDetails")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC008_FetchTheLeadDeatils()
//		    {
//			  
//		      Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.BASIC_DETAILS.getStage());
//		      
//		      LOGGER.info("Verify that detials are present in userAdditionalInfo");
//		      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.DOB"),LendingConstants.DOB_ABFL_DELITE);
//		      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.PAN"),LendingConstants.PAN_ABFL_DELITE);
//		      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.EMAIL"),LendingConstants.EMAIL_ABFL_DELITE);
//		  
//		    
//		   
//		    }
//		 
//		 
//		 
//		 @Test(description = "OTP Callback",groups = {"Regression"},dependsOnMethods = "TC008_FetchTheLeadDeatils")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC009_OTPCallback()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("leadId",leadId);
//			  queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//			  
//			  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId);
//	    				 			  
//			  Map<String,String> headers=new HashMap<String,String>();
//		     headers=lendingBaseClassObject.setCallbackHeaders(token,custId,"PAYTM_APP");
//		     
//		     Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("status","OTP_VERIFIED");
//		  	   body.put("OTP_VALIDATION_ISSUER_IP", "*************");
//		  	   body.put("OTP_VALIDATION_TIMESTAMP", "1584098137623");
//		  	   
//		  	 Response responseObject= lendingBaseClassObject.callbackOTP(queryParams, headers,body);
//		  	
//		  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//		  	 LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//		     Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
//		  	 
//		  	
//		    }
//		 
//		 @Test(description = "Verify the lead sub stage",groups = {"Regression"},dependsOnMethods = "TC009_OTPCallback")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC010_FetchLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.OTP_VERIFIED.getStage());
//		     
//		      
//		    }
//		 
//		 @Test(description = "Get the CKYC Status of current lead",groups = {"Regression"},dependsOnMethods = "TC010_FetchLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC011_CheckCKYCStatus()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			  queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//			  queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
//			  queryParams.put("dob",LendingConstants.DOB_ABFL_DELITE);
//			  
//			  Map<String,String> headers=new HashMap<String,String>();
//			    headers.put("session_token",sessionToken);
//		     
//		   
//		  	   
//		  	 Response responseObject= lendingBaseClassObject.checkCKYCStatus(queryParams, headers);
//		  	
//		  	 verifyResponseCodeAs200OK(responseObject);
//		  	ckycStage=responseObject.jsonPath().getString("stage");
//			 Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//		  	 
//		  	
//		    }
//		 @Test(description = "Verify the lead sub stage",groups = {"Regression"},dependsOnMethods = "TC011_CheckCKYCStatus")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC012_VerifyLeadStage()
//		    {
//			  	      
//		   lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,ckycStage);
//		     
//		      
//		    }
//		 
//		 @Test(description = "Upload selfie",groups = {"Regression"},dependsOnMethods = "TC012_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC013_UploadSelfie() throws InterruptedException
//		    {
//			    
//		  	   
//		  	 Response responseObject= lendingBaseClassObject.utilityForDocumentUpload("selfie",leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,sessionToken);
//		  	
//             verifyResponseCodeAs200OK(responseObject);
//	   	    
//	        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//		    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Document uploaded successfully.");
//		    Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//		    uuid=responseObject.jsonPath().getString("uuid");
//		    
//		    Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"),false);
//		    LOGGER.info("All docs uploaded : " +responseObject.jsonPath().getBoolean("allDocsUploaded"));
//		    Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"),false);
//		    Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"),true);
//		  	 
//		  	
//		    }
//		 
//		 @Test(description = "Verify the  details of Uploaded Document",groups = {"Regression"},dependsOnMethods = "TC013_UploadSelfie")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC014_VerifyUploadedDocument()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,ckycStage);
//			    Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docType"),"selfie");
//			    Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docProvided"),"selfie");
//			    Assert.assertEquals(responseObject.jsonPath().getString("solutionDocSRO[0].docUUId"),uuid);
//		     
//		      
//		    }
//		 @Test(description = "Upload Customer Photo",groups = {"Regression"},dependsOnMethods = "TC014_VerifyUploadedDocument")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC015_UploadCustomerPhoto() throws InterruptedException
//		    {
//			    
//		  	   
//		  	 Response responseObject= lendingBaseClassObject.utilityForDocumentUpload("customerPhoto",leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,sessionToken);
//		  	
//		  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//	   	    
//	        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//		    Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Document uploaded successfully.");
//		    Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//		    uuid=responseObject.jsonPath().getString("uuid");
//		    
//		    Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"),false);
//		    LOGGER.info("All docs uploaded : " +responseObject.jsonPath().getBoolean("allDocsUploaded"));
//		    Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"),false);
//		    Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"),true);
//		  	 
//		  	
//		    }
//		 
//		 @Test(description = "Verify the  details of Uploaded Document",groups = {"Regression"},dependsOnMethods = "TC015_UploadCustomerPhoto")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC016_VerifyUploadedDocument()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,ckycStage);
//			List<Object> docTypes=responseObject.jsonPath().getList("solutionDocSRO.docType");
//			docTypes.contains("customerPhoto");
//			List<Object> docProvided=responseObject.jsonPath().getList("solutionDocSRO.docProvided");
//			docProvided.contains("others");
//					     
//		      
//		    }
//		 
//		 @Test(description = "CKYC Callback",groups = {"Regression"},dependsOnMethods = "TC016_VerifyUploadedDocument")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC017_CKYCCallback()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//	    	  queryParams.put("leadId",leadId);
//	    	  
//	    	  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId);
//	    	 			 			  
//	    	   Map<String,String> headers=new HashMap<String,String>();
//	    	    headers.put("Authorization",token);
//			    headers.put("Content-Type","application/json");
//		        headers.put("custId",custId);
//		     
//	    	   Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("statusMessage","CKYC_VERIFIED");
//		  	   body.put("status", "SUCCESS");
//		  	   body.put("cKycId", "4353435454356");
//		  	   body.put("firstName", "TOUCH");
//		  	   body.put("middleName", "WOOD");
//		  	   body.put("thirdName", "LIMITED");
//		  	   body.put("email", "<EMAIL>");
//		  	   body.put("type", "SELFIE");
//		  	   body.put("percentage", "99");
//		  	   body.put("addressline1", "8A-410/412, DDA JANTA FLATS, MAYUR KUNJ");
//		  	   body.put("addressline2", "MAYUR VIHAR PHASE 1");
//		  	   body.put("city", "DELHI");
//		  	   body.put("state", "EAST DELHI");
//		  	   body.put("pincode", "110091");
//		  	   body.put("dob", LendingConstants.DOB_ABFL_DELITE);
//		  	   body.put("gender", "Female");
//		  	   body.put("pan", LendingConstants.PAN_ABFL_DELITE);
//		  	   body.put("ckycSuccessMode", "SEARCH_BY_AADHAR");
//		  	   	   
//		  	
//		  	   Response responseObject= lendingBaseClassObject.ckycCallback(queryParams, headers,body);
//		  	  
//		  	 lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//			
//		       Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
//		       Assert.assertEquals(responseObject.jsonPath().getString("oeStage"),LendingLeadStages.PAN_VERIFIED.getStage());
//		     		
//			 		   
//		    }
//		 
//	
//		 
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC017_CKYCCallback")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC018_VerifyLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.PAN_VERIFIED.getStage());
//			 
//			 LOGGER.info("Verify that detials are present in userAdditionalInfo");
//		      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.L_NAME"),"LIMITED");
//		      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.F_NAME"),"TOUCH");
//		      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.M_NAME"),"WOOD");
//		      Assert.assertEquals(responseObject.jsonPath().getString("userAdditionalInfo.NSDL_NAME"),"TOUCH WOOD LIMITED");
//		 	  LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_IMAGE_QC_REQUIRED"),"FALSE");
//		      Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LENDING_PAN_NAME_QC_REQUIRED"),"FALSE");
//		  
//			     
//		      
//		    }
//		 
//		 @Test(description = "BRE Validation Pending Callback",groups = {"Regression"},dependsOnMethods = "TC018_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC019_BREValidationPendingCallback()
//		    {
//		 Map<String,String> queryParams=new HashMap<String,String>();
//		  queryParams.put("leadId",leadId);
//		  queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//		  
//		  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId);
//  				 			  
//		  Map<String,String> headers=new HashMap<String,String>();
//	     headers=lendingBaseClassObject.setCallbackHeaders(token,custId,"PAYTM_APP");
//	     
//	     Map<String,Object> body = new HashMap<String, Object>();
//	  	   body.put("status","BRE_VALIDATION_PENDING");
//	  	   body.put("BRE_VALIDATION_PENDING_REASON", "No Not Matched");
//
//	  	 Response responseObject= lendingBaseClassObject.callbackBREValiadtion(queryParams, headers,body,true);
//	  	
//	  	 verifyResponseCodeAs200OK(responseObject);
//	  	 LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//	     Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
//		 
//		    }
//		 
//		 
//		 
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC019_BREValidationPendingCallback")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC020_VerifyLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.BRE_VALIDATION_PENDING.getStage());
//			 
//		 
//			     
//		      
//		    }
//		 
//		 @Test(description = "Fetch BRE Response",groups = {"Regression"},dependsOnMethods = "TC020_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC021_FetchBREResponse()
//		    {
//		 Map<String,String> queryParams=new HashMap<String,String>();
//		    queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	     
//	        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//		    queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//		    queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		  
//		   				 			  
//		  Map<String,String> headers=new HashMap<String,String>();
//		  headers.put("session_token",sessionToken);
//		   headers.put("Content-Type","application/json;charset=utf-8");
//	     
//	   
//	  	 Response responseObject= lendingBaseClassObject.getBREStatus(queryParams, headers);
//	  	
//	  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//	  			 
//		    }
//		 	 
//		 
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC021_FetchBREResponse")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC022_VerifyLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.BRE_RESPONSE_AWAITED.getStage());
//			 
//		 
//		    } 
//		
//		 @Test(description = "Check BRE Response",groups = {"Regression"},dependsOnMethods = "TC022_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC023_CheckBREResponse() throws InterruptedException, SQLException
//		    {
//		  Map<String,String> queryParams=new HashMap<String,String>();
//		    queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//	        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//		    queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//		    queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//		  
//		   				 			  
//		  Map<String,String> headers=new HashMap<String,String>();
//		  headers.put("session_token",sessionToken);
//		  
//		   Response responseObject=null;
//			   
//	  	 
//	  	 //Needed as on staging callback from ABFL takes around 2-3 minutes
//		 
//		  //wait time of 1 minute
//		  Thread.sleep(60000);
//		  
//		  for(int i=0;i<30;i++)
//				
//			{
//		   responseObject= lendingBaseClassObject.checkBREStatus(queryParams, headers);
//		
//			LOGGER.info("Status Code : " +responseObject.getStatusCode());
//			if(responseObject.getStatusCode()==200)
//			{
//				
//				LOGGER.info("BRE Passed");
//				 stage=responseObject.jsonPath().getString("stage");
//				 loanOffered=responseObject.jsonPath().getString("loanOffered");
//				 maxLoanAmount=responseObject.jsonPath().getString("maxLoanAmount");
//				 authorisedMonthlyLimit=responseObject.jsonPath().getString("authorisedMonthlyLimit");
//				 break;
//				
//			}
//			
//			
//			}
//		  
//		  if (responseObject.getStatusCode() == 200
//					&& responseObject.jsonPath().getString("stage").equals("BRE_ERROR"))
//						{
//							int updatedRows = 0;
//							String currentActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("356", leadId);
//							LOGGER.info("Current Active Node is : " + currentActiveworkflowStatusId);
//					
//							updatedRows = lendingBaseClassObject.runUpdateQueryOnWorkflowStatus(0, new BigInteger(currentActiveworkflowStatusId));
//							Assert.assertEquals(updatedRows, 1);
//					
//							String previousActiveworkflowStatusId = lendingBaseClassObject.getIdOfWorkflowNode("366", leadId);
//							updatedRows = lendingBaseClassObject.runUpdateQueryOnWorkflowStatus(1, new BigInteger(previousActiveworkflowStatusId));
//							LOGGER.info("Updated Active Node is : " + previousActiveworkflowStatusId);
//							Assert.assertEquals(updatedRows, 1);
//							
//							
//						    LOGGER.info("Using Callback API");
//						     
//					   	    queryParams.put("entityType", "INDIVIDUAL");
//					        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//						    queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//						    queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//						    queryParams.put("leadId", leadId);
//						    
//							  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId);
//						
//							headers.put("Authorization",token);
//							headers.put("custId",custId);
//							headers.put("Content-Type","application/json;charset=utf-8");
//						  
//							 Map<String,Object> body = new HashMap<String, Object>();
//						  	 body.put("statusMessage","BRE_SUCCESS");
//						  	 body.put("status", "SUCCESS");
//							 body.put("creditScore", "780.3");
//							 body.put("lastFetchDate", "1584098137622");
//							 body.put("bureau", "CIBIL");
//							 body.put("loanOffered", "true");
//							 body.put("minLoanAmount", "10000");
//							 body.put("maxLoanAmount", "60000");
//							 body.put("rejectionReason", "Rejection reason");
//							 body.put("authorisedMonthlyLimit", "120000");
//					     
//							 responseObject= lendingBaseClassObject.breCallback(queryParams, headers,body);
//							 LOGGER.info("Status Code : " +responseObject.getStatusCode());
//							 if(responseObject.getStatusCode()==200)
//							 {
//								 stage=responseObject.jsonPath().getString("oeStage");
//								Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
//								 
//							 }
//					
//						}
//		
//			
//		  
//			     
//				if(responseObject.getStatusCode() == 500
//						&&responseObject.jsonPath().getString("displayMessage").equals("We could not fetch your details. Please try again later.")||responseObject.getStatusCode() == 200
//								&&responseObject.jsonPath().getString("stage").equals("BRE_RESPONSE_AWAITED"))
//				{
//					LOGGER.info("Using BRE Callback API");
//			     
//		   	    queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		        queryParams.put("solutionTypeLevel2",  LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//			    queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//			    queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			    queryParams.put("leadId", leadId);
//			    
//				  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId);
//			
//				headers.put("Authorization",token);
//				headers.put("custId",custId);
//				headers.put("Content-Type","application/json;charset=utf-8");
//			  
//				 Map<String,Object> body = new HashMap<String, Object>();
//			  	 body.put("statusMessage","BRE_SUCCESS");
//			  	 body.put("status", "SUCCESS");
//				 body.put("creditScore", "722.0");
//				 body.put("lastFetchDate", "1616630400000");
//				 body.put("bureau", "CIBIL");
//				 body.put("loanOffered", "true");
//				 body.put("minLoanAmount", "10000");
//				 body.put("maxLoanAmount", "100000");
//				 body.put("rejectionReason", "Rejection reason");
//				 body.put("authorisedMonthlyLimit", "");
//		     
//				 responseObject= lendingBaseClassObject.breCallback(queryParams, headers,body);
//				 LOGGER.info("Status Code : " +responseObject.getStatusCode());
//				 if(responseObject.getStatusCode()==200)
//				 {
//					 stage=responseObject.jsonPath().getString("oeStage");
//					Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
//					 
//				 }
//				
//	
//		        }
//				
//				
//		    }
//			
//			
//				
//			
//	  			 
//
//		 
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC023_CheckBREResponse")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC024_VerifyLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.BRE_SUCCESS.getStage());
//			 LOGGER.info("Verify that detials are present in solutionAdditionalInfo");
//		     // Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.BRE_LOAN_OFFERED"),loanOffered);
//		     // Assert.assertEquals(responseObject.jsonPath().getString("solution.solutionAdditionalInfo.LOAN_MAX_AMOUNT"),maxLoanAmount);
//		   
//		    } 
//		 @Test(description = "Add Correspondence, Current and Permanent Address",groups = {"Regression"},dependsOnMethods = "TC024_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC025_AddAddress()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			   queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//		       queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
//			   queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//			   queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
//			    	    	  
//	    		    	 			 			  
//	    	   Map<String,String> headers=new HashMap<String,String>();
//	    	   headers=setcommonHeaders();
//		     
//	    	   Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowSubOperation","LOAN_OFFER_ACCEPTED");
//		  	   body.put("addressType", "RESIDENTIAL");
//		  	   body.put("addressSubType", "CORRESPONDENCE");
//		  	   body.put("country", "INDIA");
//		  	   body.put("pincode", "124103");
//		  	   body.put("state", "New Delhi");
//		  	   body.put("city", "Delhi");
//		  	   body.put("line1", "testline1");
//		  	   body.put("line2", "testline2");
//		  	   body.put("line3", "testline3");
//		  	   body.put("district", "53 A");
//		  	   body.put("addressType1", "RESIDENTIAL");
//		  	   body.put("addressSubType1", "PERMANENT");
//		  	   body.put("country1", "INDIA");
//		  	   body.put("pincode1", "122001");
//		  	   body.put("state1", "UP");
//		  	   body.put("city1", "NOIDA");
//		  	   body.put("line11", "H No 661 FF");
//		  	   body.put("line21", "Sector 9");
//		  	   body.put("line31", "testline3");
//		  	   body.put("district1", "555 A");
//		  	   body.put("addressType2", "RESIDENTIAL");
//		  	   body.put("addressSubType2", "CURRENT");
//		  	   body.put("country2", "INDIA");
//		  	   body.put("pincode2", "243001");
//		  	   body.put("state2", "UP");
//		  	   body.put("city2", "BAREILLY");
//		  	   body.put("line12", "A-3");
//		  	   body.put("line22", "Saket, 148 civil lines");
//		  	   body.put("line32", "Near Axis Bank");
//		  	   body.put("district2", "Bareilly");
//		  	   	   
//		  	
//		  	   Response responseObject= lendingBaseClassObject.addAddress(queryParams, headers,body);
//		  	  
//		  	   verifyResponseCodeAs200OK(responseObject);
//			
//		       Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//		  		     		
//			 		   
//		    }
//		 
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC025_AddAddress")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC026_VerifyLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.LEAD_POSTED.getStage());
//		   
//		    } 
//		 
//		 @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC026_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC027_FetchDynamicTnc()
//		    {
//		 Map<String,String> queryParams=new HashMap<String,String>();
//		    queryParams.put("leadId", leadId);
//		   				 			  
//		  Map<String,String> headers=new HashMap<String,String>();
//		  headers.put("session_token",sessionToken);
//     
//	   
//	  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//	  	
//	 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
//	 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
//	 	{
//	 	
//	    code=responseObject.jsonPath().getString("data.state.code");
//	    tncName=responseObject.jsonPath().getString("data.state.tncName");
//	    url=responseObject.jsonPath().getString("data.state.url");
//	    uniqueIdentifier=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
//	    md5=responseObject.jsonPath().getString("data.state.md5");
//	 	}
//	 	
//	 		 	
//	 
//		    }
//		 	 
//		 
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC027_FetchDynamicTnc")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC028_VerifyLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.LEAD_POSTED.getStage());
//			 
//		 
//		    } 
//		 @Test(description = "Fetch Dynamic T and C Sanction Letter",groups = {"Regression"},dependsOnMethods = "TC028_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC029_FetchDynamicTncSanctionLetter()
//		    {
//		 Map<String,String> queryParams=new HashMap<String,String>();
//		    queryParams.put("leadId", leadId);
//		    queryParams.put("tncType", "LOAN_SANCTION_TNC");
//		   				 			  
//		  Map<String,String> headers=new HashMap<String,String>();
//		  headers.put("session_token",sessionToken);
//    
//	   
//	  	 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
//	  	
//	 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
//	 	if(responseObject.jsonPath().getString("meta.failure_reason").equals("failed"))
//	 	 
//	 		Assert.assertEquals(responseObject.jsonPath().getString("meta."),"Invalid Request Received due to missing Tnc or Config : LOAN_SANCTION_TNC");
//	 	
//	 
//		    }
//		 	 
//		 
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC029_FetchDynamicTncSanctionLetter")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC030_VerifyLeadStage()
//		    {
//			  	      
//			 Response responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.LEAD_POSTED.getStage());
//			 
//		 
//		    } 
//		 
//		 @Test(description = "Verify submit application",groups = {"Regression"},dependsOnMethods = "TC030_VerifyLeadStage")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC031_SubmitApplication()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			   queryParams.put("leadId", leadId);
//		   	 			 			  
//	    	   Map<String,String> headers=new HashMap<String,String>();
//	    	   headers.put("Content-Type","application/json");
//	    	   headers.put("session_token",sessionToken);
//	    	   headers.put("latitude","678676766");
//	    	   headers.put("longitude","4356464545");
//	    	   headers.put("ipAddress","********");
//	    	   headers.put("androidId","23");
//	    	   headers.put("browserName","mozilla");
//	    	   headers.put("browserVersion","21.122");
//		     
//	    	   Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("tncName",tncName);
//		  	   body.put("accept", "1");
//		  	   body.put("uniqueIdentifier", uniqueIdentifier);
//		  	   body.put("md5", md5);
//		  	   
//		  	  
//		  	
//		  	   Response responseObject= lendingBaseClassObject.submitApplication(queryParams, headers,body,true,true);
//		  	  
//		  	   LOGGER.info("Status Code : " +responseObject.getStatusCode());
//		  	   if(responseObject.jsonPath().getString("meta.stage").contentEquals("TNC_POST"))
//		  		   
//		  	   {
//		  		   LOGGER.info("Try again");
//		  		   responseObject= lendingBaseClassObject.submitApplication(queryParams, headers,body);
//		  		   
//		  	   }
//		  	   
//		  	   else if(responseObject.jsonPath().getString("meta.stage").contentEquals("LMS_POST"))
//		  	   {
//		  		 verifyResponseCodeAs200OK(responseObject) ;
//		  		  Assert.assertEquals(responseObject.jsonPath().getString("meta.subStage"),"APPLICATION_PENDING"); 
//		  		  Assert.assertEquals(responseObject.jsonPath().getString("meta.status"),"success"); 
//		  		  Assert.assertEquals(responseObject.jsonPath().getString("data.state"),"Loan Application is Accepted");
//		  		   
//		  	   }
//			
//		     
//		  		     		
//			 		   
//		    }
//		 
//				
//		 @Test(description = "Verify loan status callback",groups = {"Regression"},dependsOnMethods = "TC031_SubmitApplication")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC032_LoanStatusCallbackAfterSubmitApplication() throws InterruptedException
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			   queryParams.put("leadId", leadId);
//			   queryParams.put("solution", LendingConstants.POSTPAID_V2_SOLUTION);
//		   	 			 			  
//	    	   Map<String,String> headers=new HashMap<String,String>();
//	    		  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId);
//				
//				headers.put("Authorization",token);
//				headers.put("custId",custId);
//				headers.put("Content-Type","application/json;charset=utf-8");
//				headers.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//				
//	    	   Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("status","SUCCESS");
//		  	   
//		  	 Response responseObject;
//		  	 Thread.sleep(10000);
//		  	   		  	   	   	   
//		  	   for(int i=1;i<=10;i++)
//		  	   {
//		  		 responseObject= lendingBaseClassObject.fetchTheCurrentLeadStage(LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.POSTPAID_V2_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,sessionToken,LendingLeadStages.KAFKA_PUSH_SUCCESS.getStage());
//		  		  if(responseObject.jsonPath().getString("stage").equals("KAFKA_PUSH_SUCCESS"))
//		  		  {
//		  			
//		  		    responseObject= lendingBaseClassObject.loanApplicationCallback(queryParams, headers,body,"Success");
//		  	
//		  	        LOGGER.info("Status Code : " +responseObject.getStatusCode());
//		  	              if(responseObject.getStatusCode()==200)
//		  	                  {
//			                      Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully"); 
//		  		   
//		  	                   }
//		  	   
//		  	        break;
//		  	   
//		  		  }
//		  	   }
//		
//		    }
//		  
//		 @Test(description = "Verify Lead stage",groups = {"Regression"},dependsOnMethods = "TC032_LoanStatusCallbackAfterSubmitApplication")
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC033_VerifyLeadStage()
//		    {
//			
//	        	 Map<String,String> queryParams=new HashMap<String,String>();
//				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//				  queryParams.put("solution",LendingConstants.POSTPAID_V2_SOLUTION);
//		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//				 			  
//		    	  Map<String,String> headers=new HashMap<String,String>();
//		    	  headers.put("session_token",sessionToken);
//		    	  
//		    	  for(int i=1;i<=10;i++)
//			         {
//			      Response responseObject= lendingBaseClassObject.fetchLeadDetails(queryParams, headers);
//			     
//			       if(responseObject.jsonPath().getString("stage").equals("LEAD_NOT_PRESENT"))
//			       {
//			      LOGGER.info("Verify Lead Stage "+responseObject.jsonPath().getString("stage"));
//			      Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
//			    
//			       }
//			   break;
//			 
//	         }
//		   
//		    } 
//		
//		 
//		 /**
//		  * Method to set headers which are used in lead creation request
//		  * @return
//		  */
//		   public Map<String, String> setcommonHeaders() {
//
//
//			    Map<String, String> headers = new HashMap<String, String>();
//			    headers.put("session_token",sessionToken);
//			    headers.put("Content-Type","application/json;charset=utf-8");
//			  
//			    return headers;
//			       }
//		 /**
//		    * Verify  Response Code as 200 OK
//		    * @param responseObject
//		    */
//			public void verifyResponseCodeAs200OK(Response responseObject) {
//				
//				LOGGER.info("Status Code : " +responseObject.getStatusCode());
//		    	
//		    	Assert.assertEquals(responseObject.getStatusCode(),200);
//		    	 
//		    }
//}
//
//
