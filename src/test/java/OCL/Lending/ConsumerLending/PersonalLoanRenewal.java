package OCL.Lending.ConsumerLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import OCL.Lending.BusinessLending.Fullerton.TestMCAFullertonV3Workflow;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class PersonalLoanRenewal  extends BaseMethod{
	
	
	private static final Logger LOGGER = LogManager.getLogger(TestMCAFullertonV3Workflow.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1002012627";
		 String consumerNumber="8668010330";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
		 String uuid="";
		 String md5="";
		 String code="";
		 String tncName="";
		 String url="";
		 String uniqueIdentifier="";
		 String loanTenure="";
			String loanAmount="";
			String loanEquatedMonthlyInstallment="";
			String loanRateofInterest="";
			String loanInterestAmount="";
			String loanProcessingFeeRate="";
			String loanDisbursalAmount="";
			String stampDutyCharges="";
			String brokerPeriodInterest="";

		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
		 String kybSecondaryTNCDisplayURL="";
		 String loanAgreementDate="";
		 String kybTNCDisplayURL="";
		 String panNameMatchTimeStamp="";
		 String panNameMatchPercentage="";
		 String breLastFetchDate="";
		 String loanOfferID="1e059505-410d-4000-b3bb-52fac75289c6";
		 String baseID="987ed74c-dbee-4e80-9313-964d66470283";
		 String uuidCustomerPhoto;
		 String uuidSelfie;
		 String ckycName;
		 

		 Response responseObject= null;
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing Renewal lead present or not",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDeatils()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("leadId",leadId);
			  queryParams.put("solution",LendingConstants.PERSONAL_LOAN_RENEWAL);
			  queryParams.put("custId", custId);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create PL Renewal Hero Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_CreatePLRenewalLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.PERSONAL_LOAN_RENEWAL);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
	    	
			
	    	   Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", consumerNumber);
		  		body.put("PRODUCT_ID", "95");
				body.put("PRODUCT_TYPE", "PL");
				body.put("FLOW_TYPE", "RISK");
				body.put("PRODUCT_VERSION", "1");
				body.put("LENDER_ID", "5");
				body.put("LENDER_NAME", "HERO");
				body.put("LOAN_OFFER_ID",loanOfferID);
				body.put("BASE_ID", baseID);
				body.put("WHITELISTING_SOURCE", "RISK");
				body.put("IS_EMANDATE_ELIGIBLE", "true");
				body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
				body.put("IS_FATHER_NAME_REQUIRED", "true");
				body.put("MARITAL_STATUS", "NOT_KNOWN");
				body.put("IS_BRE3_REQUIRED", "true");
				body.put("MIGRATION_TYPE", "PL_RENEWAL");
				body.put("PINCODE", "600024");
				body.put("PARENT_LOAN_ACCOUNT_NUMBER", "PYTMPH255109743");
				body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
				body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
				body.put("STATIC_TNC_SETNAME",LendingConstants.PL_V3_STATIC_CONSENT);
				body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "FALSE");
		  	 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/PLRenewalLeadCreationRequest.json";
		  	  
		  	  
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"95");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"HERO");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),loanOfferID);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),baseID);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PAYTM_VINTAGE_OLDER_THAN_90D"),"true");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MIGRATION_TYPE"),"PL_RENEWAL");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PARENT_LOAN_ACCOUNT_NUMBER"),"PYTMPH255109743");
			        //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),"loan_agreement_pl_hero");
			        //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"sanction_letter_pl_hero");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"personalloan_oclconsent_hero");
			      
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreatePLRenewalLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"804");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
		    
		
		    }
		  
		 
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC004_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC005_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "BHAIRAVI");
			body.put("L_NAME", "LATASREE");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "600024");
			body.put("PAN", "**********");
			body.put("DOB", "1979-10-05");
			body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage());
			
			}
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC005_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchCIR()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		   
		
		 	  
		 	 for(int i=0;i<10;i++)
			  {
				  responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);
			
			 
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_SUCCESS.getStage()))
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
		        Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");


		        bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
		        bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
		        bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
		        breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
		      
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC006_FetchCIR",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC007_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<39;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_GENERATED.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))
			    
			  {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			       
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
			        
			        //Hit BRE1 Callback
			        
			        LOGGER.info("Callback not coming so hitting BRE1 Callback");
			        
			        responseObject=    lendingBaseClassObject. BRE1CallbackforRenewal (leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,custId);
			        
			        
	            }
			  
		     
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC007_FetchLeadAllData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC008_UpdateExistingDetailsInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("F_NAME", "TOUCH");
			body.put("L_NAME", "LIMITED");
			body.put("GENDER", "FEMALE");
			body.put("PINCODE", "110096");
			body.put("PAN", "**********");
			body.put("DOB", LendingConstants.DOB_STASHFIN);
			body.put("EMAIL", Email);
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
			
			}
		  
		  @Test(description = "Verify PL Renewal lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC008_UpdateExistingDetailsInSAI")
			@Owner(emailId = "<EMAIL>")
			public void TC009_PLRenewal_LoanOfferAccept() {
				 Map<String,String> queryParams=new HashMap<String,String>();
					
				 queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				 queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
				 
				 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
				 Map<String, String> headers = new HashMap<String, String>();
				 headers.put("Authorization", token);
				 headers.put("Content-Type", "application/json");
				 headers.put("custId", custId);
			       
			     Map<String,Object> body=new HashMap<String,Object>();
			     body.put("workflowOperation", "OFFER_ACCEPTED");
			     
			     //solutionAdditionalInfo
			     body.put("LOAN_TENURE", "6");
			     body.put("LOAN_TENURE_UNIT", "MONTH");
			     body.put("LOAN_AMOUNT_IN_NUMBER", "25000");
			     body.put("LOAN_AMOUNT_IN_WORDS", "Twenty Five Thousand");
			     body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "4513");
			     body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Four thousand Five hundred thirteen");
			     body.put("LOAN_RATE_OF_INTEREST", "28");
			     body.put("LOAN_INTEREST_AMOUNT", "346.40");
			     body.put("LOAN_PROCESSING_FEE", "1250");
			     body.put("PROCESSING_FEE_RATE", "5.0");
			     body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1475");
			     body.put("LOAN_DISBURSAL_AMOUNT", "23267");
			     body.put("STAMP_DUTY_CHARGES", "200");
			     body.put("BROKEN_PERIOD_INTEREST", "58");
			     body.put("LENDER_STATIC_TNC_SETNAME", LendingConstants.PERSONAL_LOAN_RENEWAL_KYC_CONSENT);
			     
			     requestBodyJsonPath="MerchantService/V1/workflow/lead/PLV3HeroRenewalRequest.json";
				   
			     responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				    	
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
			        loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
			        loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
			        loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
			        loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
			        loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
			        loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
			        loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
			        stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
			        brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
				  }
			     }
			     	
		  
		  //do kyc
		  //fetch lead
		  //bre2 callback
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC009_PLRenewal_LoanOfferAccept",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC010_FetchLeadAllData() throws JSONException, InterruptedException
		    {

			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
				
			  {
				  
				  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_PENDING.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);
			
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest); 
				  
			  }
			  
		    }
		  
		  @Test(description = "Verify the PLv3 lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC010_FetchLeadAllData")
			@Owner(emailId = "<EMAIL>")
			public void TC011_PLv3HERO_UploadCustomerPhoto() throws InterruptedException {
				
				Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
						LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_RENEWAL, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
				uuidCustomerPhoto = responseObject.jsonPath().getString("uuid");
			
				Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
				LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
				Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
				Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
				
			}
			
			@Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC011_PLv3HERO_UploadCustomerPhoto")
			@Owner(emailId = "<EMAIL>")
			public void TC012_PLv3HERO_VerifyUploadedCustomerPhoto() {

				responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				//Customerphoto
				Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
				Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");
				Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docUUId"),uuidCustomerPhoto );
						
			}
			
			
			
			@Test(description = "Verify the PLv3 lead Upload Selfie ",groups = {"Regression"},dependsOnMethods = "TC012_PLv3HERO_VerifyUploadedCustomerPhoto")
			@Owner(emailId = "<EMAIL>")
			public void TC013_PLv3HERO_UploadSelfie() throws InterruptedException {
				
				Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
						LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PERSONAL_LOAN_RENEWAL, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Document uploaded successfully.");
				Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);
				uuidSelfie = responseObject.jsonPath().getString("uuid");
			
				Assert.assertEquals(responseObject.jsonPath().getBoolean("stageBumped"), false);
				LOGGER.info("All docs uploaded : " + responseObject.jsonPath().getBoolean("allDocsUploaded"));
				Assert.assertEquals(responseObject.jsonPath().getBoolean("allDocsUploaded"), false);
				Assert.assertEquals(responseObject.jsonPath().getBoolean("currentDocPersisted"), true);
			
			}
			
			
			
			@Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC013_PLv3HERO_UploadSelfie", groups = {
			"Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC014_PLv3HERO_VerifyUploadedSelfie() {

				responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

				LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
				if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docUUId"), uuidSelfie);
				}else {
					Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");
					Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docUUId"), uuidSelfie);
					
				}
				
			}
			
		 
		 @Test(description = "CKYC Callback",groups = {"Regression"},dependsOnMethods = "TC014_PLv3HERO_VerifyUploadedSelfie")
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC015_CKYCCallback()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("solution",LendingConstants.PERSONAL_LOAN_RENEWAL);
	    	  queryParams.put("leadId",leadId);
	    	  
	    	  token=lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER,LendingConstants.LMS_CLIENT_ID,custId,LendingConstants.LMS_SECRET);
	    	 			 			  
	    	   Map<String,String> headers=new HashMap<String,String>();
	    	    headers.put("Authorization",token);
			    headers.put("Content-Type","application/json");
		        headers.put("custId",custId);
		     
	    	   Map<String,Object> body = new HashMap<String, Object>();
	    		 body.put("CKYC_PAN", "**********");
			  	 body.put("CKYC_DOB", "1979-10-05");
			  	 body.put("CKYC_ATTEMPT_TIMESTAMP", "1633347388366");
			  	 body.put("CKYC_RESPONSE_TIMESTAMP", "1633347388370");
			  	 body.put("CKYC_ID", "43534354543561");
			  	 body.put("RELIGION", "HINDU");
			  	 body.put("MARITAL_STATUS", "MARRIED");
			  	 body.put("SPOUSE_NAME", "ABC XYZ");
			  	 body.put("FATHER_NAME", "ABC XYZ");
			  	 body.put("MOTHER_NAME", "ABC XYZ");
			  	 body.put("AADHAAR_REF_NO", "ABC XYZ");
		  	   body.put("statusMessage","CKYC_VERIFIED");
		  	   body.put("status", "SUCCESS");
		  	   body.put("firstName", "TOUCH");
		  	   body.put("thirdName", "LIMITED");
		  	   body.put("email", "<EMAIL>");
		  	   body.put("type", "SELFIE");
		  	   body.put("percentage", "100");
		  	   body.put("addressline1", "D-602, D Wing");
		  	   body.put("addressline2", "Andheri East");
		  	   body.put("city", "noida");
		  	   body.put("state", "UP");
		  	   body.put("pincode", "600024");
		  	   body.put("dob", "1984-08-11");
		  	   body.put("gender", "MALE");
		  	   body.put("pan", "**********");
		  	   body.put("isPanSearchCkycSuccess", true);
		  	   body.put("ckycSuccessMode", "SEARCH_BY_PAN");
			  
			
		  	  requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/KYCCallbackForFullertonRequest.json";
		  	
		  	   Response responseObject= lendingBaseClassObject.ckycCallback(queryParams, headers,body, requestBodyJsonPath,  true);
		  	  
		  	   lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
		       Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"Data updated successfully");
		       Assert.assertEquals(responseObject.jsonPath().getString("oeStage"),LendingLeadStages.PAN_VERIFIED.getStage());
		     		
			 		   
		    }
		 
		  @Test(description = "Update lead details",dependsOnMethods = "TC015_CKYCCallback",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC016_VerifyLeadStage()
		    {
			  
			  for(int i=0;i<50;i++)
			  {
			   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage()))
				  break;
			  
			  }
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
			    
			  {
			     LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"387");  
			        
			        //Hit BRE2 Callback
                  LOGGER.info("Callback not coming so hitting BRE2 Callback");
			        
			      responseObject=lendingBaseClassObject. BRE2CallbackforRenewal (leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,custId);
			        
			        
	            }
			  
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SECOND_BRE_SUCCESS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_COMPLETE.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"388");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
           }
		   
		
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
			 
		    }
		  
			@Test(description = "Verify PL v3 Lead If Additional Data is required", dependsOnMethods = "TC016_VerifyLeadStage",groups = {"Regression"})
			@Owner(emailId = "<EMAIL>")
			public void TC017_PLv3HERO_AdditionalIsRequiredorNot() {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
				queryParams.put("leadId", leadId);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
			
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
			
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
			
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowOperation", "IS_ADDITIONAL_DATA_REQUIRED");
				body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");
				
				 if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SECOND_BRE_SUCCESS.getStage()))
			     {
					 requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataRequired.json";
						
						responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);
					
						lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
	           }
				
				
		
			}
			
			@Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC017_PLv3HERO_AdditionalIsRequiredorNot",groups = {"Regression"})
			@Owner(emailId = "<EMAIL>")
			public void TC018_PLv3HERO_FetchLeadVerifyAdditionalData() {
				
				
				 for(int i=0;i<55;i++)
				  {
				   
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANK_VALIDATION_PENDING.getStage()))
					  break;
				  
				  }
				 
			
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage()))
				 
				  {
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
				}
				  
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANK_VALIDATION_PENDING.getStage()))
						 
				  {
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"558");
			        
			        //HIT SAVE BANK API
				}
				  
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANK_VERIFICATION_FAILURE.getStage()))
						 
				  {
					Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VERIFICATION_FAILURE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_VERIFICATION_FAILURE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"796");
			        
			        //HIT SAVE BANK API
				}
			}
			
			@Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC018_PLv3HERO_FetchLeadVerifyAdditionalData", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC019_PLv3HERO_UpdateKYCNameInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("CKYC_NAME", "testNameMatch");
			body.put("PAN", "**********");
			
			requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
			responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
			responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
					LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			ckycName=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME");
			}
			
			
			@Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC019_PLv3HERO_UpdateKYCNameInSAI",groups = {"Regression"})
			@Owner(emailId = "<EMAIL>")
			public void TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI() {
				responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
						LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				
				//Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
			    //Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_VALIDATION_PENDING.getStage());
			    //Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"558");
			    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);
				
			}
			
			
			@Test(description = "Verify PL v3 Lead the bank details", dependsOnMethods = "TC020_PLv3HERO_FetchLeadUpdateCKYCinSAI", groups = {
			"Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC021_PLv3HERO_SaveBankDetails() throws InterruptedException {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

		
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("session_token", sessionToken);
				headers.put("Content-Type", "application/json");
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("bankName", "ICICI BANK LIMITED");
				body.put("bankAccountNumber", "************");
				body.put("ifsc", "ICIC0006461");
				body.put("bankAccountHolderName", "testNameMatch");
				body.put("EMANDATE_TYPE", "Internet Banking");
				
				responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);
				
				if (responseObject.getStatusCode() == 200) {
				
					Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
					}
				
				else {
					
					for (int i = 1; i < 4; i++) {
						LOGGER.info("Again hitting with same data: retry-count: " + i);
						responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
						
						if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
						{
							break;
						}
							
					}
				
				}
				
				
				
						   
			}


			
			@Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC021_PLv3HERO_SaveBankDetails", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC022_PLv3HERO_UpdateKYCNameInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
			queryParams.put("leadId", leadId);
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
			body.put("status", "SUCCESS");
			body.put("CKYC_NAME", "testNameMatch");
			body.put("PAN", "**********");
			
			requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
			responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			
			LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
		    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
		    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
		    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"ICICI BANK LIMITED");
		    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"************");
		    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"ICIC0006461");
		    Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),"testNameMatch");
			}
		 
			@Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC022_PLv3HERO_UpdateKYCNameInSAI", groups = {
			"Regression" })
			@Owner(emailId =  "<EMAIL>")
			public void TC023_PLv3HERO_EmandateCallback() throws InterruptedException {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("leadId", leadId);
				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
			
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
			
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
			
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("status", "EMANDATE_SUCCESS");
			
				Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);
			
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
			
			}
			
			@Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "TC023_PLv3HERO_EmandateCallback",groups = {"Regression"})
			@Owner(emailId = "<EMAIL>")
			public void TC024_PLv3HERO_FetchLeadPostEmandate() {
				responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
						LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
			}
				
			
			@Test(description = "For PL v3 Hero Generate Loan Agreement", dependsOnMethods = "TC024_PLv3HERO_FetchLeadPostEmandate", groups = { "Regression" })
			@Owner(emailId =  "<EMAIL>")
			public void TC025_PLv3HERO_GenerateLoanAgreement() {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("leadId", leadId);
				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

			
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("session_token", sessionToken);
			
				Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				if (responseObject.jsonPath().getString("meta.status").equals("success")) {
			
					code = responseObject.jsonPath().getString("data.state.code");
					tncName = responseObject.jsonPath().getString("data.state.tncName");
					url = responseObject.jsonPath().getString("data.state.url");
					uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
					md5 = responseObject.jsonPath().getString("data.state.md5");
				}
			
			}
			
		
			@Test(description = "For PL v3 Hero Generate Sanction Letter", dependsOnMethods = "TC025_PLv3HERO_GenerateLoanAgreement", groups = {
					"Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC026_PLv3HERO_GenerateSanctionLetter() {
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("leadId", leadId);
				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

				queryParams.put("tncType", "LOAN_SANCTION_TNC");
			
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("session_token", sessionToken);
			
				Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());
				if (responseObject.jsonPath().getString("meta.status").equals("success")) {
			
					codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
					tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
					urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
					uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
					md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
				}
			
			}
			
			@Test(description = "For PL v3 Hero Verify submit application", dependsOnMethods = "TC026_PLv3HERO_GenerateSanctionLetter", groups = {
					"Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC027_PLv3HERO_SubmitApplication() throws InterruptedException {
			
				Map<String, String> queryParams = new HashMap<String, String>();
				queryParams.put("leadId", leadId);
				queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
				queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
				queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
			
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Content-Type", "application/json");
				headers.put("session_token", sessionToken);
			
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
				body.put("LENDING_DYNAMIC_TNC", tncName);
				body.put("TNC_ACCEPTED_VERSION", 1);
				body.put("KYB_TNC_REF_NO", uniqueIdentifier);
				body.put("TNC_ACCEPTED_CODE", md5);
			
				
				body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
				body.put("SECONDARY_ACCEPTED_TNC_VERSION", 1);
			
				body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
				body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
				
				requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3LoanAgreementAcceptrequest.json";
				responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);
			
				LOGGER.info("Status Code : " + responseObject.getStatusCode());

				Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
			
			}
			
			@Test(description = "For PL v3 Hero Verify Lead stage Post Submit Loan Application", dependsOnMethods = "TC027_PLv3HERO_SubmitApplication", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC028_PLv3HERO_FetchLeadPostSubmitApplication() {
			
				responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
						LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				
			
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
			 
			}

		
			
			@Test(description = "For PL v3 Hero Verify PDC Callback", dependsOnMethods = "TC028_PLv3HERO_FetchLeadPostSubmitApplication", groups = {
			"Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC029_PLv3HERO_PDCCallback() throws InterruptedException {
				
		
			  	 
			       for(int i =0;i<=30;i++) {
						responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
								LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
							if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
								break;
							}
							
						}
			  	 
			  	 
			  	 
				if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
					LOGGER.info("Loan Application Accepted with PDC Callback "); 
					Map<String, String> queryParams = new HashMap<String, String>();
					queryParams.put("leadId", leadId);
					queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
			
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
						
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Authorization", token);
					headers.put("Content-Type", "application/json");
					headers.put("custId", custId);
						
					Map<String, Object> body = new HashMap<String, Object>();
				
				  	       body.put("workflowOperation","LOAN_APPLICATION_ACCEPTED");
					  	   body.put("PDC_REASON_ID", "7");
					  	   body.put("PDC_USER_MESSAGE", "Disbursal on hold, this could take upto 3 working days for processing");
					  	   body.put("BUSINESS_STATUS", "PENDING_BY_LMS");
					  	   body.put("PDC_SYSTEM_MESSAGE", "Direct Approval of Loan Account is accepted");
					  	   
					  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PDCLoanApplicationPendingCallbackRequest.json";   
				    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
						
				  
			  			
		  		}
				
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"350");
		  		
		  		
			}
			
			@Test(description = "For PL v3 Hero Verify Lead stage After PDC Callback", dependsOnMethods = "TC029_PLv3HERO_PDCCallback", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>")
			public void TC030_PLv3HERO_FetchLeadPostPDCCallback() {
			
				responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
						LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"350");
			}
			
			
			@Test(description = "For PL v3 Hero Verify if LMS Approve Callback required",dependsOnMethods = "TC030_PLv3HERO_FetchLeadPostPDCCallback",groups = {"Regression"})
			@Owner(emailId = "<EMAIL>")
			public void TC031PLv3HERO_SubmitApplicationLMSApprovedCallback() {
			
				
				for(int i=0;i<10;i++) {
					responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
							LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
						if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
							break;
						}
					}
				if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
					Map<String, String> queryParams = new HashMap<String, String>();
					queryParams.put("leadId", leadId);
					queryParams.put("solution", LendingConstants.PERSONAL_LOAN_RENEWAL);
					queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
					queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
					queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
				
					token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);
					
					Map<String, String> headers = new HashMap<String, String>();
					headers.put("Content-Type", "application/json");
					headers.put("Authorization", token);
					 headers.put("custId", custId);
					 
					Map<String, Object> body = new HashMap<String, Object>();
					 body.put("workflowOperation","LMS_APPLICATION_APPROVED");
				  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
				  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
				  	   body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
				  	   body.put("LENDER_LOAN_ACCOUNT_NUMBER", "*************");
				  	   
			    	 requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LMSSubmitApplicationCallbackRequest.json";   
			    	 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
					
					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage()))
						  {
							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
//					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
//					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"330");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_STATUS"),"ACTIVE");
					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_ACCOUNT_CREATED_ON"),"*************");
						  }
			     }
			     
			     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LMS_CALLBACK_SUCCESS.getStage());
			    }  
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC031PLv3HERO_SubmitApplicationLMSApprovedCallback",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC032_FetchLeadAllData() throws JSONException
			    {
				  for(int i=0;i<25;i++)
				  {
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PERSONAL_LOAN_RENEWAL,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
				  {
					  break;
				  }
				  
				  }	  
			     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
			    }

}
