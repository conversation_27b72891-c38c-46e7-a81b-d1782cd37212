        package OCL.Lending.ConsumerLending;

        import Services.LendingService.LendingBaseAPI;
        import Services.LendingService.LendingConstants;
        import Services.LendingService.LendingLeadStages;
        import Services.Utilities.Utilities;
        import com.goldengate.common.BaseMethod;
        import com.paytm.apitools.util.annotations.Owner;
        import io.restassured.response.Response;
        //import org.apache.log4j.Logger;
        import org.apache.logging.log4j.LogManager;
        import org.apache.logging.log4j.Logger;
        import org.json.JSONException;
        import org.testng.Assert;
        import org.testng.annotations.BeforeClass;
        import org.testng.annotations.Test;

        import java.util.HashMap;
        import java.util.Map;

        public class TestPersonalLoanABFLTOPUP extends BaseMethod {



            private static final Logger LOGGER = LogManager.getLogger(TestPersonalLoanABFLTOPUP.class);

            String entityType="INDIVIDUAL";
            String channel="PAYTM_APP";
            String leadId = "";
            String sessionToken = "";
            String consumerNumber="9755844880";
            String consumerPassword="paytm@123";
            String custId="1002550389";
            String token="";
            String requestBodyJsonPath="";
            String userIPAddress="";
            String staticTncAcceptanceTimeStamp="";
            String Email="";
            String DOB="";
            String PAN="";
            String occupation="";
            String income="";
            String firstName="BHAIRAVI";
            String lastName="LATASREE";
            String bureauRequest="";
            String bureauResponse="";
            String bureauCreditState="";
            String breCreditScore="";
            String loanTenure="";
            String loanAmount="";
            String loanEquatedMonthlyInstallment="";
            String loanRateofInterest="";
            String loanInterestAmount="";
            String loanProcessingFeeRate="";
            String loanDisbursalAmount="";
            String stampDutyCharges="";
            String brokerPeriodInterest="";
            String uuidCustomerPhoto = "";
            String uuidSelfie="";
            String loanOfferID="490d8b0b-1956-44a8-ad26-6fdff64ecd96";
            String baseID="b66d62bc-cb7f-4798-b7ea-18c5a39ca1d6";
            String ckycName="";
            String code = "";
            String tncName = "";
            String url = "";
            String uniqueIdentifier ="";
            String md5 ="";
            String codeSanctionLetter = "";
            String tncNameSanctionLetter = "";
            String urlSanctionLetter = "";
            String uniqueIdentifierSanctionLetter = "";
            String md5SanctionLetter = "";
            String bankName="PAYTM BANK";
            String bankAccountNumber="************";
            String ifsc="PYTM0123456";
            String bankAccountHolderName="Shivangi Goswami";
            String stringify_json="{\\\"baseId\\\":\\\"b66d62bc-cb7f-4798-b7ea-18c5a39ca1d6\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"isBre2Required\\\":false,\\\"field_investigation_needed\\\":false,\\\"loanDownGradable\\\":false,\\\"isEmandateEligible\\\":1,\\\"offerEndDate\\\":\\\"Thu Jul 07 00:00:00 IST 2022\\\",\\\"lastFetchDate\\\":*************,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":165000.0,\\\"maxTenure\\\":18,\\\"minLoanAmount\\\":27000.0,\\\"minTenure\\\":12,\\\"newOfferGenerated\\\":true,\\\"offerStartDate\\\":\\\"Tue Jun 07 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"490d8b0b-1956-44a8-ad26-6fdff64ecd96\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"99\\\",\\\"productType\\\":\\\"PL\\\",\\\"productVersion\\\":1,\\\"lenderSchemeId\\\":\\\"54103\\\",\\\"riskGrade\\\":\\\"VL\\\",\\\"riskSegment\\\":\\\"VL\\\",\\\"skipMandate\\\":false,\\\"sourceOfWhitelist\\\":\\\"RISK\\\"}";
            Response responseObject= null;
            LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
            Utilities utility=new Utilities();


            @BeforeClass()
            public void intitializeInputData() {

                LOGGER.info(" Before Suite Method for Consumer Login ");
                sessionToken = ApplicantToken(consumerNumber, consumerPassword);
                LOGGER.info("Applicant Token for Lending : " + sessionToken);

            }



            @Test(description="Verify if there is any existing Personal Loan ABFL Topup Lead",groups= {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC001_PLv3_ABFL_TOPUP_fetchlLead()
            {
                responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200)
                {
                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

                    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

                    leadId=responseObject.jsonPath().getString("leadId");
                }

                if(responseObject.jsonPath().getInt("statusCode")==404)
                {
                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

                    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

                }

            }


            @Test(description="Verify reseting existing Personal Loan ABFL TOPUP lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3_ABFL_TOPUP_fetchlLead"})
            @Owner(emailId = "<EMAIL>")
            public void TC002_PLv3_ABFL_TOPUP_DeleteExistingLead() {

                Map<String,String> queryParams=new HashMap<String,String>();
                queryParams.put("leadId",leadId);
                queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
                queryParams.put("custId", custId);

                token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("custId", custId);

                lendingBaseClassObject.resetLendingLeads(queryParams, headers);

            }


            @Test(description="Create Lead for Personal Loan Migeration ABFL TOPUP",groups={"Regression"},dependsOnMethods = {"TC002_PLv3_ABFL_TOPUP_DeleteExistingLead"})
            @Owner(emailId = "<EMAIL>")
            public void TC003_PLv3_ABFL_TOPUP_CreateLead() {

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);
                //Parameters
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);
                queryParams.put("entityType", entityType);
                queryParams.put("channel", channel);
                queryParams.put("custId", custId);

                //Headers
                Map<String, String> header = new HashMap<String, String>();
                header = LendingBaseAPI.setHeadersReceivedFromFE();
                header.put("Authorization", token);
                header.put("Content-Type", "application/json");
                header.put("custid", custId);
                header.put("ipAddress", LendingConstants.IP_ADDRESS);

                //Body
                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "CREATE_LEAD");
                body.put("mobile", consumerNumber);

                //solutionAdditionalInfo
                body.put("PRODUCT_ID", "99");
                body.put("PRODUCT_TYPE", "PL");
                body.put("FLOW_TYPE", "RISK");
                body.put("LOAN_OFFER_ID", loanOfferID);
                body.put("PRODUCT_VERSION", "1");
                body.put("BASE_ID", baseID);
                body.put("LENDER_ID", "6");
                body.put("WHITELISTING_SOURCE", "RISK");
                body.put("IS_EMANDATE_ELIGIBLE", "true");
                body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
                body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
                body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
                body.put("IS_FATHER_NAME_REQUIRED", "false");
                body.put("MARITAL_STATUS", "NOT_KNOWN");
                body.put("IS_BRE3_REQUIRED", "true");
                body.put("PINCODE", "600024");
                body.put("LENDER_NAME", "ABFL");
                // body.put("LOAN_ACCOUNT_NUMBER", "PYTMPL_ABFL786375637");
                body.put("PARENT_LOAN_ACCOUNT_NUMBER", "PYTMPL_ABFL786375637");
                body.put("MIGRATION_TYPE", "TOPUP");
                body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");


                requestBodyJsonPath = "MerchantService/V1/workflow/lead/CreatePLABFLTopupRequest.json";

                responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body, requestBodyJsonPath);
                if (responseObject.getStatusCode() == 201) {
                    LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                    leadId = responseObject.jsonPath().getString("leadId");
                    Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
                    Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
                    Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "6");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"), "99");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"), "1");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"), "PL");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"), "RISK");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"), "true");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"), "false");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"), "NOT_KNOWN");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"), "true");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"), "600024");
                } else if (responseObject.getStatusCode() == 417) {
                    responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body, requestBodyJsonPath);
                    LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
                    leadId = responseObject.jsonPath().getString("leadId");
                    Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "201");
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_CREATED");
                    Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.LEAD_CREATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.LEAD_CREATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "112");
                    Assert.assertEquals(responseObject.jsonPath().getString("mobile"), consumerNumber);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"), "6");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"), LendingConstants.LONGITUDE);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"), LendingConstants.LATITUDE);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"), "99");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"), "1");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"), "PL");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"), "RISK");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"), "true");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"), LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"), "false");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"), "NOT_KNOWN");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"), "true");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"), "600024");

                }
            }



            @Test(description="Verify the PL v3 ABFL Topup lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3_ABFL_TOPUP_CreateLead")
            @Owner(emailId = "<EMAIL>")
            public void TC004_PLV3ABFLTopup_FetchLeadAllData() throws JSONException
            {
                for(int i=0;i<30;i++)
                {
                    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DATA_PREFILL_SUCCESS.getStage()))
                        break;
                }
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.DATA_PREFILL_SUCCESS.getStage()))
                {
                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1310");
                }
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());
            }

            @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC004_PLV3ABFLTopup_FetchLeadAllData")
            @Owner(emailId = "<EMAIL>")
            public void TC005_PLV3ABFL_Topup_UpdateLeadDetailsinSAI() {
                Map<String,String> queryParams=new HashMap<String,String>();
                queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

                Map<String,String> headers=new HashMap<String,String>();
                token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
                headers = LendingBaseAPI.setHeadersReceivedFromFE();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String,Object> body=new HashMap<String,Object>();
                body.put("workflowMovementRequired","false");
                body.put("status","SUCCESS");
                body.put("bureau", "CIBIL");
                body.put("PAN", "**********");
                body.put("F_NAME",firstName);
                body.put("L_NAME",lastName);
                body.put("EMAIL","<EMAIL>");
                body.put("DOB","1979-10-05");
                body.put("gender","FEMALE");
                body.put("pincode","600024");

                responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
               // Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
               // Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());

            }


            @Test(description = "Verify the PLv3 ABFL TOPUP lead data after Updating details in SAI",groups = {"Regression"},dependsOnMethods = "TC005_PLV3ABFL_Topup_UpdateLeadDetailsinSAI")
            @Owner(emailId = "<EMAIL>")
            public void TC06_PLv3ABFL_Topup_FetchDataPost_SAI_Update() throws JSONException
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDING_LIS_SUBMIT_APPLICATION_SUCCESS.getStage()))
                {
                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"1310");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),firstName);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),lastName);
                }
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.DATA_PREFILL_SUCCESS.getStage());
            }


            @Test(description = "Verify Fetch CIR for PLv3 ABFL TOPUP lead",groups = {"Regression"},dependsOnMethods = "TC06_PLv3ABFL_Topup_FetchDataPost_SAI_Update")
            @Owner(emailId = "<EMAIL>")
            public void TC07_PLv3ABFL_TOPUP_FetchCIR() {
                Map<String,String> queryParams=new HashMap<String,String>();

                queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

                Map<String,String> headers=new HashMap<String,String>();
                token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("longitude",LendingConstants.LONGITUDE);
                headers.put("latitude",LendingConstants.LATITUDE);
                headers.put("custId", custId);


                Map<String,Object> body=new HashMap<String,Object>();

                responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);


                if(responseObject.getStatusCode()==200) {

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
                    Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
                    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");
                    //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
                    Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");

                    bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
                    bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
                    bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
                    breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
                }
            }



            @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC07_PLv3ABFL_TOPUP_FetchCIR")
            @Owner(emailId = "<EMAIL>")
            public void TC008_PLv3ABFL_Topup_BRE1Callback() throws JSONException
            {

                for(int i =0;i<=8;i++) {
                    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
                        break;
                    }
                }
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))
                {
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                    for(int i =0;i<15;i++) {
                        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
                            LOGGER.info("BRE 1 passed without callback");
                            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                            loanOfferID=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID");
                            break;
                        }
                    }
                    BRE1Callback(LendingConstants.PL_V3_SOLUTION);
                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
                        LOGGER.info("BRE 1 passed with callback");
                        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                    }
                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                }
            }


            public Response BRE1Callback(String solution) {
                Map<String, String> queryParams = new HashMap<String, String>();
                queryParams.put("solution", solution);
                queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowOperation", "BRE1_SUCCESS");
                body.put("BRE1_OFFER_DETAILS", ""+stringify_json+"");
                body.put("LENDING_SCHEME_ID", "54103");

                requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/PLHeroTopupCallbackRequest.json";
                responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

                return responseObject;

            }


            @Test(description = "Verify the PLv3 lead data after BRE1 Success",groups = {"Regression"},dependsOnMethods = "TC008_PLv3ABFL_Topup_BRE1Callback")
            @Owner(emailId = "<EMAIL>")
            public void TC009_PLv3ABFL_Topup_FetchDataPostBRE1Success() throws JSONException
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
                {
                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"),bureauRequest);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"),bureauResponse);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"),bureauCreditState);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"),breCreditScore);

                }
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
            }



            @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC009_PLv3ABFL_Topup_FetchDataPostBRE1Success")
            @Owner(emailId = "<EMAIL>")
            public void TC010_PLV3ABFL_Topup_UpdatePANinSAI() {
                Map<String,String> queryParams=new HashMap<String,String>();
                queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

                Map<String,String> headers=new HashMap<String,String>();
                token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
                headers = LendingBaseAPI.setHeadersReceivedFromFE();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

//                Map<String,Object> body=new HashMap<String,Object>();
//                body.put("statusMessage","ADDITIONAL_INFO_UPDATE");
//                body.put("status","SUCCESS");
//                body.put("bureau", "CIBIL");
//                body.put("PAN", "**********");
//                body.put("F_NAME",firstName);
//                body.put("L_NAME",lastName);
//                body.put("EMAIL","<EMAIL>");
//                body.put("DOB","1979-10-05");
//                body.put("gender","FEMALE");
//                body.put("pincode","600024");
//
//                responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);


                Map<String, Object> body = new HashMap<String, Object>();
                body.put("workflowMovementRequired", "false");
                body.put("F_NAME", firstName);
                body.put("L_NAME", lastName);
                body.put("GENDER", "FEMALE");
                body.put("PINCODE", "600024");
                body.put("PAN", "**********");
                body.put("DOB", "1979-10-05");
                body.put("EMAIL", "<EMAIL>");

                responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
//                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
//                Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.OFFER_GENERATED.getStage());

            }



            @Test(description = "Verify PL v3 ABFL Topup lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC010_PLV3ABFL_Topup_UpdatePANinSAI")
            @Owner(emailId = "<EMAIL>")
            public void TC011_PLv3ABFL_TOPUP_LoanOfferAccept() {
                Map<String,String> queryParams=new HashMap<String,String>();

                queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

                token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String,Object> body=new HashMap<String,Object>();
                body.put("workflowOperation", "OFFER_ACCEPTED");

                //solutionAdditionalInfo
                body.put("LOAN_TENURE", "6");
                body.put("LOAN_TENURE_UNIT", "MONTH");
                body.put("LOAN_AMOUNT_IN_NUMBER", "47000");
                body.put("LOAN_AMOUNT_IN_WORDS", "Fourty Seven Thousand");
                body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "8485");
                body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Eight thousand four hundred Eighty five");
                body.put("LOAN_RATE_OF_INTEREST", "28");
                body.put("LOAN_INTEREST_AMOUNT", "651.70");
                body.put("LOAN_PROCESSING_FEE", "2350");
                body.put("PROCESSING_FEE_RATE", "5");
                body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "2773");
                body.put("LOAN_DISBURSAL_AMOUNT", "43333");
                body.put("STAMP_DUTY_CHARGES", "200");
                body.put("BROKEN_PERIOD_INTEREST", "694");
                body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");


                requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3HeroTopupLoanOfferAccepted.json";

                responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
                {
                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                    loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
                    loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
                    loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
                    loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
                    loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
                    loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
                    loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
                    stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
                    brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
                }
            }



            @Test(description = "Verify the PLv3 lead data after Loan offer accept",groups = {"Regression"},dependsOnMethods = "TC011_PLv3ABFL_TOPUP_LoanOfferAccept")
            @Owner(emailId = "<EMAIL>")
            public void TC012_PLv3ABFL_TOPUP_FetchDataPostLoanOfferAccept() throws JSONException
            {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
                {
                    LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
                    Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

                    LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);

                    Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest);
                }

                for(int i =0;i<15;i++) {
                    responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);

                    if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_REQUIRED.getStage())) {
                        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_REQUIRED.getStage());
                        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");

                    }
                }
            }



            @Test(description="Initiate New KYC by Search By PAN",groups = {"Regression"},dependsOnMethods = "TC012_PLv3ABFL_TOPUP_FetchDataPostLoanOfferAccept")
            @Owner(emailId = "<EMAIL>")
            public void TC013_PLv3ABFL_TOPUPNewKYC_InitiateKYCSearchByPAN() {
                Map<String,String> queryParams=new HashMap<String,String>();
                queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

                Map<String,String> headers=new HashMap<String,String>();
                token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
                headers = LendingBaseAPI.setHeadersReceivedFromFE();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String,Object> body=new HashMap<String,Object>();
                body.put("kycMode","SEARCH_BY_PAN");


                requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";

                responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
                Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

            }

            @Test(description="Fetch lead post KYC initiate",groups = {"Regression"},dependsOnMethods = "TC013_PLv3ABFL_TOPUPNewKYC_InitiateKYCSearchByPAN")
            @Owner(emailId = "<EMAIL>")
            public void TC014_PLv3ABFL_TOPUPNewKYC_FetchLeadPostKYCInitiate() {

                for(int i =0;i<=45;i++) {
                    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
                        break;
                    }
                }
                Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_FAILED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_FAILED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2002");
            }
            @Test(description="Update PAN details in SAI for New KYC",groups = {"Regression"},dependsOnMethods = "TC014_PLv3ABFL_TOPUPNewKYC_FetchLeadPostKYCInitiate")
            @Owner(emailId = "<EMAIL>")
            public void TC015_PLv3ABFL_TOPUPNewKYC_UpdateLeadDetailsinSAI() {
                Map<String,String> queryParams=new HashMap<String,String>();
                queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

                Map<String,String> headers=new HashMap<String,String>();
                token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
                headers = LendingBaseAPI.setHeadersReceivedFromFE();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String,Object> body=new HashMap<String,Object>();
                body.put("statusMessage","ADDITIONAL_INFO_UPDATE");
                body.put("status","SUCCESS");
                body.put("PAN", "**********");
                body.put("DOB","1973-01-01");
                body.put("GENDER","MALE");
                body.put("NSDL_NAME","Nagu Singh");

                requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";

                responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");


            }

            @Test(description="Initiate New KYC by Search By PAN",groups = {"Regression"},dependsOnMethods = "TC015_PLv3ABFL_TOPUPNewKYC_UpdateLeadDetailsinSAI")
            @Owner(emailId = "<EMAIL>")
            public void TC016_PLv3ABFL_TOPUPNewKYC_InitiateKYCSearchByAadhar() {
                Map<String,String> queryParams=new HashMap<String,String>();
                queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);

                Map<String,String> headers=new HashMap<String,String>();
                token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
                headers = LendingBaseAPI.setHeadersReceivedFromFE();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                headers.put("custId", custId);

                Map<String,Object> body=new HashMap<String,Object>();
                body.put("kycMode","SEARCH_BY_AADHAAR");
                body.put("aadhaar","2671");
                body.put("gender","MALE");



                requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByAAdhaarRequest.json";

                responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath);

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
                Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
                Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

            }


            @Test(description = "Verify lead details after KYC is initiated",groups={"Regression"},dependsOnMethods = "TC016_PLv3ABFL_TOPUPNewKYC_InitiateKYCSearchByAadhar")
            @Owner(emailId = "<EMAIL>")
            public void TC017_PLv3ABFL_TOPUPFetchDataPostKYCInitiated() throws JSONException
            {

                for(int i =0;i<=45;i++) {
                    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage())) {
                        break;
                    }
                }
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
                {
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2020");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

                }

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());


            }

            @Test(description = "Verify the PLv3 lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC017_PLv3ABFL_TOPUPFetchDataPostKYCInitiated")
            @Owner(emailId = "<EMAIL>")
            public void TC018_PLv3ABFL_TOPUP_UploadCustomerPhoto() throws InterruptedException {

                Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
                        LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, sessionToken,"others");

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
                Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);


            }

            @Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC018_PLv3ABFL_TOPUP_UploadCustomerPhoto")
            @Owner(emailId = "<EMAIL>")
            public void TC019_PLv3_ABFL_TOPUP_VerifyUploadedCustomerPhoto() {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                //Customerphoto
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
                Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");

            }


            @Test(description = "Verify the PLv3 lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC019_PLv3_ABFL_TOPUP_VerifyUploadedCustomerPhoto")
            @Owner(emailId = "<EMAIL>")
            public void TC020_PLv3ABFL_TOPUP_UploadSelfie() throws InterruptedException {

                Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
                        LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, sessionToken,"selfie");

                lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
                Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);


            }

            @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC020_PLv3ABFL_TOPUP_UploadSelfie", groups = {
                    "Regression" })
            @Owner(emailId = "<EMAIL>")
            public void TC021_PLv3ABFL_TOPUP_VerifyUploadedSelfie() {

                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
                if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
                    Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
                    Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

                }else {
                    Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
                    Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


                }

            }

            @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC021_PLv3ABFL_TOPUP_VerifyUploadedSelfie")
            @Owner(emailId = "<EMAIL>")
            public void TC022_PLv3ABFL_TOPUP_FetchDataPostKYCIntiated() throws JSONException
            {

                for(int i =0;i<=35;i++) {
                    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_UPLOADED.getStage())) {
                        break;
                    }
                }
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_UPLOADED.getStage()))
                {
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

                }




            }


            @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC022_PLv3ABFL_TOPUP_FetchDataPostKYCIntiated")
            @Owner(emailId = "<EMAIL>")
            public void TC023_PLv3ABFL_TOPUP_FetchDataPostSelfieUploaded() throws JSONException
            {

                for(int i =0;i<=55;i++) {
                    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {
                        break;
                    }
                }
                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_IN_PROGRESS.getStage()))
                {
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"387");

                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

                }

                //Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());


            }



            @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC023_PLv3ABFL_TOPUP_FetchDataPostSelfieUploaded",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC024_PLv3ABFL_TOPUP_SecondBRECallback() {

                for(int i =0;i<=55;i++) {
                    responseObject = lendingBaseClassObject.fetchLeadDetails(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP, LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                            LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_ALL_DATA, sessionToken, custId);
                    if (responseObject.jsonPath().getInt("statusCode") == 200 && responseObject.jsonPath().getString("stage").
                            equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {

                        Map<String, String> queryParams = new HashMap<String, String>();
                        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
                        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP);
                        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
                        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
                        queryParams.put("leadId", leadId);

                        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK, custId, LendingConstants.JWT_RISK_KEY);

                        Map<String, String> headers = new HashMap<String, String>();
                        headers.put("Authorization", token);
                        headers.put("Content-Type", "application/json");
                        headers.put("custId", custId);

                        Map<String, Object> body = new HashMap<String, Object>();
                        body.put("workflowOperation", "BRE2_SUCCESS");
                        body.put("BASE_ID", baseID);
                        body.put("LOAN_OFFER_ID", loanOfferID);
                        body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
                        body.put("SKIP_EMANDATE_ELIGIBLE", "false");
                        body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

                        requestBodyJsonPath = "MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
                        responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body, requestBodyJsonPath, "BRE2");
                        LOGGER.info("BRE 2 Success with callback");

                    } else {

                        // Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
                        LOGGER.info("BRE 2 Success without callback");
                    }
                }

            }

            @Test(description = "Verify PL v3 Lead After BRE2 success Callback", dependsOnMethods = "TC024_PLv3ABFL_TOPUP_SecondBRECallback", groups = { "Regression" })
            @Owner(emailId = "<EMAIL>", isAutomated = true)
            public void TC025_PLv3ABFL_TOPUP_FetchDataAfterBRE2Success() {

                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

                LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

            }



            @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC025_PLv3ABFL_TOPUP_FetchDataAfterBRE2Success",groups = {"Regression"})
            @Owner(emailId = "<EMAIL>")
            public void TC026_PLv3ABFL_TOPUP_BRE3Success() {
                responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                        LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
                for(int i =0;i<10;i++) {
                    responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL_2_ABFL_TOPUP,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                            LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
                    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                            equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                        break;
                    }
                }
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

            }




        }

