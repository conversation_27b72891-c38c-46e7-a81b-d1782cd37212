//package OCL.Lending.ConsumerLending;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingConstants;
//import Services.LendingService.LendingLeadStages;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//import com.goldengate.common.BaseMethod;
//import com.paytm.apitools.util.annotations.Owner;
////import com.paytm.framework.reporting.Reporter;
//import io.restassured.response.Response;
//import org.apache.log4j.Logger;
//import org.json.JSONException;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import java.util.HashMap;
//import java.util.Map;
//
//public class TestCitiBankRegressionFlow  extends BaseMethod{
//
//
//
//	private static final Logger LOGGER = Logger.getLogger(TestStashfin.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
//	Utilities utility=new Utilities();
//
//		 String sessionToken = "";
//		 String leadId="";
//		 String custId="**********";
//		 String consumerNumber="**********";
//		 String consumerPassword="paytm@123";
//		 String token="";
//		 String stage="";
//		 String feStage="";
//		 String userIPAddress="";
//		 String loanUserLatitude="";
//		 String loanUserLongitude="";
//		 String tncAdditionalParam="";
//		 String staticTncAcceptanceTimeStamp="";
//		 String lenderCustomerId="";
//		 String requestBodyJsonPath="";
//		 String Pan="";
//		 String Email="";
//		 String DOB="";
//		 String applicationId="ALWP0076040821";
//
//		 Response responseObject= null;
//
//
//
//		@BeforeClass()
//		 public void intitializeInputData() {
//
//			LOGGER.info(" Before Suite Method for Consumer Login ");
//			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
//			LOGGER.info("Applicant Token for Lending : " + sessionToken);
//
//		}
//
//
//
//		@Test(description = "Verify whether there is any existing stashfin lead present or not",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC001_FetchLeadDeatils()
//		    {
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200)
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
//
//		       leadId=responseObject.jsonPath().getString("leadId");
//		     }
//
//		     if(responseObject.jsonPath().getInt("statusCode")==404)
//		     {
//
//
//			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
//
//		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//
//		     }
//
//		    }
//
//
//		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC002_DeleteExistingLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("leadId",leadId);
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("custId", custId);
//
//			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//
//
//			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
//		    }
//
//
//
//
//		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC003_CreateCitiBankLead_WithoutPassingBaseId()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//		  	   body.put("BASE_ID", "");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LENDER_ID", "14");
//		  	   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//		  	   body.put("OFFER_START_DATE", "2021-09-01");
//		  	   body.put("LOAN_MAX_AMOUNT", "20000.0");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//		  	   body.put("CUSTOMER_NAME", "Shivangi");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"BASE_ID is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//
//
//
//	@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC003_CreateCitiBankLead_WithoutPassingBaseId",groups = {"Regression"})
//	@Owner(emailId = "<EMAIL>",isAutomated = true)
//	  public void TC004_CreateCitiBankLead_WithoutPassingProductId()
//	  {
//		  Map<String,String> queryParams=new HashMap<String,String>();
//
//		  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//		  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//		  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//		  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//		  Map<String,String> headers=new HashMap<String,String>();
//		  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//	     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//	     headers.put("Authorization", token);
//	     headers.put("Content-Type", "application/json");
//	     headers.put("custid", custId);
//	     headers.put("ipAddress", "************");
//
//	     Map<String,Object> body = new HashMap<String, Object>();
//		   body.put("workflowOperation","CREATE_LEAD");
//		   body.put("mobile", consumerNumber);
//		   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//		   body.put("BASE_ID", "Test_1001700370");
//		   body.put("PRODUCT_ID", "");
//		   body.put("PRODUCT_VERSION", "1");
//		   body.put("PRODUCT_TYPE","PL");
//		   body.put("LENDER_ID", "14");
//		   body.put("LENDER_NAME", "CITI_BANK");
//		   body.put("RISK_SEGMENT", "CL_PL");
//		   body.put("FLOW_TYPE", "DISTRIBUTION");
//		   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//		   body.put("OFFER_END_DATE", "2021-10-02");
//		   body.put("OFFER_START_DATE", "2021-09-01");
//		   body.put("LOAN_MAX_AMOUNT", "20000.0");
//		   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//		   body.put("CUSTOMER_NAME", "Shivangi");
//		   body.put("APPLICATION_ID", applicationId);
//
//		   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//		   for(int i=0;i<2;i++)
//			 {
//
//			   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"PRODUCT_ID is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//
//
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC004_CreateCitiBankLead_WithoutPassingProductId",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC005_CreateCitiBankLead_WithoutPassingProductVersion()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"PRODUCT_VERSION is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC005_CreateCitiBankLead_WithoutPassingProductVersion",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC006_CreateCitiBankLead_WithoutPassingProductType()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"PRODUCT_TYPE is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC006_CreateCitiBankLead_WithoutPassingProductType",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC007_CreateCitiBankLead_WithoutPassingLenderId()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"LENDER_ID is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC007_CreateCitiBankLead_WithoutPassingLenderId",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC008_CreateCitiBankLead_WithoutPassingRiskSegment()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"RISK_SEGMENT is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC008_CreateCitiBankLead_WithoutPassingRiskSegment",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC009_CreateCitiBankLead_WithoutPassingFlowType()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"FLOW_TYPE is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC009_CreateCitiBankLead_WithoutPassingFlowType",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"LOAN_MAX_AMOUNT is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC010_CreateCitiBankLead_WithoutPassingLoanMaxAmount",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Invalid value of LOAN_RATE_OF_INTEREST ");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC011_CreateCitiBankLead_WithoutPassingLoanRateOfInterest",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"STATIC_TNC_SETNAME is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC012_CreateCitiBankLead_WithoutPassingStaticTncSetName",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"LENDER_STATIC_TNC_SETNAME is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC013_CreateCitiBankLead_WithoutPassingLenderTncSetName",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC014_CreateCitiBankLead_WithoutPassingApplicationId()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", "");
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"APPLICATION_ID is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC014_CreateCitiBankLead_WithoutPassingApplicationId",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC015_CreateCitiBankLead_WithoutPassingOfferURL()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"OFFER_URL is null or empty");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC015_CreateCitiBankLead_WithoutPassingOfferURL",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC016_CreateCitiBankLead_PassingInvalidSolutionName()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution","CitiBanker");
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Invalid SolutionType");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC016_CreateCitiBankLead_PassingInvalidSolutionName",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC017_CreateCitiBankLead_PassingInvalidChannelName()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel","test");
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Invalid Channel");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC017_CreateCitiBankLead_PassingInvalidChannelName",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2","abcd");
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Invalid SolutionTypeLevel2");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		@Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC018_CreateCitiBankLead_PassingInvalidSolutionTypeLevel2",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>",isAutomated = true)
//		  public void TC019_CreateCitiBankLead_PassingInvalidEntityType()
//		  {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType","abcd" );
//			  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//			  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//			  Map<String,String> headers=new HashMap<String,String>();
//			  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		     headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		     headers.put("Authorization", token);
//		     headers.put("Content-Type", "application/json");
//		     headers.put("custid", custId);
//		     headers.put("ipAddress", "************");
//
//		     Map<String,Object> body = new HashMap<String, Object>();
//			   body.put("workflowOperation","CREATE_LEAD");
//			   body.put("mobile", consumerNumber);
//			   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//			   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//			   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//			   body.put("LENDER_ID", "14");
//			   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//			   body.put("OFFER_START_DATE", "2021-09-01");
//			   body.put("LOAN_MAX_AMOUNT", "20000.0");
//			   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//			   body.put("CUSTOMER_NAME", "Shivangi");
//			   body.put("APPLICATION_ID", applicationId);
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Invalid EntityType");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//			    }
//
//		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC019_CreateCitiBankLead_PassingInvalidEntityType",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC020_CreateCitiBankLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//		  	   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LENDER_ID", "14");
//		  	   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//		  	   body.put("OFFER_START_DATE", "2021-09-01");
//		  	   body.put("LOAN_MAX_AMOUNT", "20000.0");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//		  	   body.put("CUSTOMER_NAME", "Shivangi");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==201)
//				   break;
//			 }
//
//
//			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
//				  {
//					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
//			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
//			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"Test_1001700370");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"********");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"14");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"CITI_BANK");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_SEGMENT"),"CL_PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_URL"),"https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2021-10-02");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2021-09-01");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"20000.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"16.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CUSTOMER_NAME"),"Shivangi");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPLICATION_ID"),applicationId);
//
//			        leadId=responseObject.jsonPath().getString("leadId");
//			        custId=responseObject.jsonPath().getString("custId");
//			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
//			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
//
//
//
//			      }
//
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//
//		    }
//
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC020_CreateCitiBankLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC021_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage()))
//				  break;
//
//			  }
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage()))
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"327");
//		     }
//
//
//		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//
//
//		    }
//
//		  @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC021_FetchLeadAllData",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC022_CreateLeadAgain()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//		  	   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LENDER_ID", "14");
//		  	   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//		  	   body.put("OFFER_START_DATE", "2021-09-01");
//		  	   body.put("LOAN_MAX_AMOUNT", "20000.0");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//		  	   body.put("CUSTOMER_NAME", "Shivangi");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//
//
//			   for(int i=0;i<2;i++)
//				 {
//
//				   responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("We already have an application for you in our system. Please contact us from 24*7 help section on the app. "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_ALREADY_EXISTS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    }
//		  @Test(description = "LIS Callback to Loan Rejection-->245 Node",dependsOnMethods = "TC022_CreateLeadAgain",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC023_LISCallbackToLoanRejection() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_REJECTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_REJECTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_REJECTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"245");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_REJECTED.getStage());
//
//		    }
//
//		  @Test(description = "Verify whether there is any existing stashfin lead present or not",dependsOnMethods = "TC023_LISCallbackToLoanRejection",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC024_FetchLeadDeatils()
//		    {
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200)
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
//
//		       leadId=responseObject.jsonPath().getString("leadId");
//		     }
//
//		     if(responseObject.jsonPath().getInt("statusCode")==404)
//		     {
//
//
//			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
//
//		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//
//		     }
//
//		    }
//
//
//		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC024_FetchLeadDeatils",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC025_DeleteExistingLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("leadId",leadId);
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("custId", custId);
//
//			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//
//
//			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
//		    }
//
//		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC025_DeleteExistingLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC026_CreateCitiBankLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//		  	   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LENDER_ID", "14");
//		  	   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//		  	   body.put("OFFER_START_DATE", "2021-09-01");
//		  	   body.put("LOAN_MAX_AMOUNT", "20000.0");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//		  	   body.put("CUSTOMER_NAME", "Shivangi");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==201)
//				   break;
//			 }
//
//
//			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
//				  {
//					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
//			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
//			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"Test_1001700370");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"********");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"14");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"CITI_BANK");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_SEGMENT"),"CL_PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_URL"),"https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2021-10-02");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2021-09-01");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"20000.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"16.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CUSTOMER_NAME"),"Shivangi");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPLICATION_ID"),applicationId);
//
//			        leadId=responseObject.jsonPath().getString("leadId");
//			        custId=responseObject.jsonPath().getString("custId");
//			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
//			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
//
//
//
//			      }
//
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC026_CreateCitiBankLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC027_LISCallbackToLoanAccepted() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"243");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//
//		    }
//		  @Test(description = "LIS Callback to Loan Rejection-->243-->245 Node",dependsOnMethods = "TC027_LISCallbackToLoanAccepted",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC028_LISCallbackToLoanRejection() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_REJECTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_REJECTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_REJECTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"245");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_REJECTED.getStage());
//
//		    }
//
//		  @Test(description = "Verify whether there is any existing stashfin lead present or not",dependsOnMethods = "TC028_LISCallbackToLoanRejection",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC029_FetchLeadDeatils()
//		    {
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200)
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
//
//		       leadId=responseObject.jsonPath().getString("leadId");
//		     }
//
//		     if(responseObject.jsonPath().getInt("statusCode")==404)
//		     {
//
//
//			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
//
//		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//
//		     }
//
//		    }
//
//
//		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC029_FetchLeadDeatils",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC030_DeleteExistingLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("leadId",leadId);
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("custId", custId);
//
//			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//
//
//			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
//		    }
//
//		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC030_DeleteExistingLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC031_CreateCitiBankLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//		  	   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LENDER_ID", "14");
//		  	   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//		  	   body.put("OFFER_START_DATE", "2021-09-01");
//		  	   body.put("LOAN_MAX_AMOUNT", "20000.0");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//		  	   body.put("CUSTOMER_NAME", "Shivangi");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==201)
//				   break;
//			 }
//
//
//			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
//				  {
//					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
//			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
//			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"Test_1001700370");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"********");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"14");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"CITI_BANK");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_SEGMENT"),"CL_PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_URL"),"https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2021-10-02");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2021-09-01");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"20000.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"16.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CUSTOMER_NAME"),"Shivangi");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPLICATION_ID"),applicationId);
//
//			        leadId=responseObject.jsonPath().getString("leadId");
//			        custId=responseObject.jsonPath().getString("custId");
//			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
//			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
//
//
//
//			      }
//
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//
//		    }
//
//
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC031_CreateCitiBankLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC032_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage()))
//				  break;
//
//			  }
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage()))
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"327");
//		     }
//
//
//		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC032_FetchLeadAllData",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC033_LISCallbackToLoanAccepted() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"243");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//
//		    }
//		  @Test(description = "LIS Callback to Loan Processing error from Loan Accepted-->243 Stage",dependsOnMethods = "TC033_LISCallbackToLoanAccepted",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC034_LISCallbackToLoanProcessingError() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_PROCESSING_ERROR");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LENDER_APPLICATION_FAILURE_RESPONSE", "Invalid Loan Amount");
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/LoanProcessingErrorRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_PROCESSING_ERROR.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_PROCESSING_ERROR.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_PROCESSING_ERROR.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"248");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_PROCESSING_ERROR.getStage());
//
//		    }
//		  @Test(description = "LIS Callback to Loan Rejection-->248-->245 Node",dependsOnMethods = "TC034_LISCallbackToLoanProcessingError",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC035_LISCallbackToLoanRejection() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_REJECTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_REJECTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_REJECTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"245");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_REJECTED.getStage());
//		    }
//
//		  @Test(description = "Verify whether there is any existing stashfin lead present or not",dependsOnMethods = "TC035_LISCallbackToLoanRejection",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC036_FetchLeadDeatils()
//		    {
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200)
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
//
//		       leadId=responseObject.jsonPath().getString("leadId");
//		     }
//
//		     if(responseObject.jsonPath().getInt("statusCode")==404)
//		     {
//
//
//			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
//
//		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//
//		     }
//
//		    }
//
//
//		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC036_FetchLeadDeatils",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC037_DeleteExistingLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("leadId",leadId);
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("custId", custId);
//
//			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//
//
//			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
//		    }
//
//		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC037_DeleteExistingLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC038_CreateCitiBankLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_CITIBANK);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_STATIC_TNC_SETNAME", "citi_distribution_consent");
//		  	   body.put("BASE_ID", "Test_1001700370");
//			   body.put("PRODUCT_ID", "********");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LENDER_ID", "14");
//		  	   body.put("LENDER_NAME", "CITI_BANK");
//			   body.put("RISK_SEGMENT", "CL_PL");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OFFER_URL", "https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			   body.put("OFFER_END_DATE", "2021-10-02");
//		  	   body.put("OFFER_START_DATE", "2021-09-01");
//		  	   body.put("LOAN_MAX_AMOUNT", "20000.0");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "16.0");
//		  	   body.put("CUSTOMER_NAME", "Shivangi");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//			   requestBodyJsonPath="MerchantService/V1/workflow/lead/CitiBankCreateLeadRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==201)
//				   break;
//			 }
//
//
//			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
//				  {
//					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
//			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
//			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"Test_1001700370");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"********");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"14");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"CITI_BANK");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_SEGMENT"),"CL_PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_URL"),"https://www.citibank.co.in/ssjsps/COPSInter.jsp?offerCode=9lWdhIwzrSHS9rl");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2021-10-02");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2021-09-01");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_MAX_AMOUNT"),"20000.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"16.0");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CUSTOMER_NAME"),"Shivangi");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.APPLICATION_ID"),applicationId);
//
//			        leadId=responseObject.jsonPath().getString("leadId");
//			        custId=responseObject.jsonPath().getString("custId");
//			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
//			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
//
//
//
//			      }
//
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//
//		    }
//
//
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC038_CreateCitiBankLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC039_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage()))
//				  break;
//
//			  }
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage()))
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"327");
//		     }
//
//
//		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_APPLICATION_REQUEST_SUCCESS.getStage());
//
//
//		    }
//
//		  @Test(description = "LIS Callback ",dependsOnMethods = "TC039_FetchLeadAllData",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC040_LISCallbackWithInvalidSolutionName() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,"abc",LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==404)
//					   break;
//				 }
//
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"404");
//
//
//
//
//		    }
//
//		  @Test(description = "LIS Callback ",dependsOnMethods = "TC040_LISCallbackWithInvalidSolutionName",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC041_LISCallbackWithInvalidLeadId() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters("abc",LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid request. Request doesn't match with existing lead"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//
//
//
//		    }
//
//
//		  @Test(description = "LIS Callback ",dependsOnMethods = "TC041_LISCallbackWithInvalidLeadId",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC043_LISCallbackWithInvalidEntityType() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,"abcd");
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid EntityType"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//
//		    }
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC043_LISCallbackWithInvalidEntityType",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC044_LISCallbackToLoanAccepted() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"243");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//
//		    }
//		  @Test(description = "LIS Callback to Loan Disbursed-->237 Stage",dependsOnMethods = "TC044_LISCallbackToLoanAccepted",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC045_LISCallbackToLoanDisbursedFromLoanAccepted() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "403734");
//		  	   body.put("LOAN_TENURE", "13");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "MONTHLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "2");
//		  	   body.put("PROCESSING_FEE_RATE", "3");
//		  	   body.put("INSTALLMENT_AMOUNT","31420");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "408460");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "318049");
//			   body.put("LOAN_DISBURSAL_DATE", "2021-08-09");
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/CitiBankLoanDisbursedRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"237");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"403734");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"13");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"MONTHLY");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"2");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"31420");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"408460");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"318049");
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC045_LISCallbackToLoanDisbursedFromLoanAccepted",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC046_LISCallbackWithInvalidWorkflowOperation() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Unable to find any allowable node Id"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//
//
//		    }
//
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC046_LISCallbackWithInvalidWorkflowOperation",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC047_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage()))
//				  break;
//
//			  }
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage()))
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"325");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"403734");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"13");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"MONTHLY");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"2");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"31420");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"408460");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"318049");
//
//		     }
//
//
//		  //   Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
//
//
//		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC047_FetchLeadAllData",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC048_LMSDataCallbackWithInvalidSolutionName() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,"abc",LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==404)
//					   break;
//				 }
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"404");
//				   }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC048_LMSDataCallbackWithInvalidSolutionName",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC049_LMSDataCallbackWithInvalidLeadId() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters("abc",LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid request. Request doesn't match with existing lead"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC049_LMSDataCallbackWithInvalidLeadId",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC050_LMSDataCallbackWithInvalidChannel() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,"abc",LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid Channel"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC050_LMSDataCallbackWithInvalidChannel",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC051_LMSDataCallbackWithInvalidEntityType() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,"abc");
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==400)
//					   break;
//				 }
//
//
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid EntityType"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//		    }
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC051_LMSDataCallbackWithInvalidEntityType",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC052_LMSDataCallback() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//			      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//
//			      for(int i=0;i<5;i++)
//				  {
//
//				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
//					  break;
//
//				  }
//
//
//
//
//					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_LOAN_ACCOUNT_ACKNOWLEDGED.getStage()))
//						  {
//
//						  for(int i=0;i<2;i++)
//							 {
//
//								 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//								  if(responseObject.getStatusCode()==200)
//								   break;
//							 }
//
//
//							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
//					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
//					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"240");
//					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"318049");
//
//
//
//					      }
//
//				      //  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
//
//		    }
//
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC052_LMSDataCallback",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC053_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_CITIBANK,LendingConstants.PL_DISTRIBUTION__CITIBANK_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
//			  {
//				  break;
//			  }
//
//			  }
//
//
//		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
//
//
//		    }
//
//		  }
//
//
