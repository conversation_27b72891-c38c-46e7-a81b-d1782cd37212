package OCL.Lending.ConsumerLending;


import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestPersonalLoanV3HeroSBA extends BaseMethod{


    private static final Logger LOGGER = LogManager.getLogger(TestPersonalLoanV3HeroSBA.class);
    String solution="personal_loan_v3";
    String solutionTypeLevel2="HERO";
    String entityType="INDIVIDUAL";
    String channel="PAYTM_APP";
    String leadId = "";
    String sessionToken = "";
    String consumerNumber="9108999800";
    String consumerPassword="paytm@123";
    String custId="1002450647";
    String token="";
    String requestBodyJsonPath="";
    String userIPAddress="";
    String staticTncAcceptanceTimeStamp="";
    String Email="";
    String DOB="";
    String PAN="";
    String occupation="";
    String income="";
    String firstName="BHAIRAVI";
    String lastName="LATASREE";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String stringify_json="{\\\"baseId\\\":\\\"PL_HERO_1002450647_09128290\\\",\\\"bureau\\\":\\\"CIBIL\\\",\\\"bureauThick\\\":1,\\\"creditScore\\\":808,\\\"field_investigation_needed\\\":false,\\\"incentiveRate\\\":0.0,\\\"isEmandateEligible\\\":1,\\\"isIncentiveAllowed\\\":0,\\\"lastFetchDate\\\":1633046400000,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":150000.0,\\\"maxTenure\\\":18,\\\"minLoanAmount\\\":30000.0,\\\"minTenure\\\":12,\\\"newOfferGenerated\\\":false,\\\"ntc\\\":0,\\\"offerId\\\":\\\"a8355995-bdb0-4b4c-910a-a6400010d6e0\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"60\\\",\\\"productType\\\":\\\"PL\\\",\\\"productVersion\\\":1,\\\"rejectionReason\\\":\\\"\\\",\\\"riskGrade\\\":\\\"VL\\\"}";
    String loanTenure="";
    String loanAmount="";
    String loanEquatedMonthlyInstallment="";
    String loanRateofInterest="";
    String loanInterestAmount="";
    String loanProcessingFeeRate="";
    String loanDisbursalAmount="";
    String stampDutyCharges="";
    String brokerPeriodInterest="";
    String uuidCustomerPhoto = "";
    String uuidSelfie="";
    String loanOfferID="a8355995-bdb0-4b4c-910a-a6400010d6e0";
    String baseID="PL_HERO_1002450647_09128290";
    String ckycName="";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier ="";
    String md5 ="";
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String bankName="PAYTM BANK";
    String bankAccountNumber="************";
    String ifsc="PYTM0123456";
    String bankAccountHolderName="Shivangi Goswami";


    Response responseObject= null;
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility=new Utilities();


    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }



    @Test(description="Verify if there is any existing Personal Loan Migration Hero Lead",groups= {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC001_PLv3Hero_fetchlLead()
    {
        responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200)
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId=responseObject.jsonPath().getString("leadId");
        }

        if(responseObject.jsonPath().getInt("statusCode")==404)
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description="Verify reseting existing Personal Loan Migration Hero lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3Hero_fetchlLead"})
    @Owner(emailId = "<EMAIL>")
    public void TC002_PLv3Hero_DeleteExistingLead() {

        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("custId", custId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId",leadId);

        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);

        responseObject = lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if (responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer.")){
            lendingBaseClassObject.resetLendingLeads(queryParams, headers);
        }
    }

    @Test(description="Create Lead for Personal Loan Migeration Hero",groups={"Regression"},dependsOnMethods = {"TC002_PLv3Hero_DeleteExistingLead"})
    @Owner(emailId = "<EMAIL>")
    public void TC003_PLv3Hero_CreateLead() {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        //Parameters
        Map <String,String> queryParams= new HashMap<String,String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
        queryParams.put("entityType",entityType);
        queryParams.put("channel", channel);

        //Headers
        Map <String,String> header = new HashMap<String,String>();
        header = LendingBaseAPI.setHeadersReceivedFromFE();
        header.put("Authorization", token);
        header.put("Content-Type", "application/json");
        header.put("custid", custId);
        header.put("ipAddress", LendingConstants.IP_ADDRESS);

        //Body
        Map <String,Object> body = new HashMap<String,Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", "9871054942");

        //solutionAdditionalInfo
        body.put("PRODUCT_ID", "25");
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_OFFER_ID", loanOfferID);
        body.put("PRODUCT_VERSION", "1");
        body.put("BASE_ID", baseID);
        body.put("LENDER_ID", "5");
        body.put("WHITELISTING_SOURCE", "RISK");
        body.put("IS_EMANDATE_ELIGIBLE", "true");
        body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
        body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
        body.put("IS_FATHER_NAME_REQUIRED", "true");
        body.put("MARITAL_STATUS", "NOT_KNOWN");
        body.put("IS_BRE3_REQUIRED", "true");
        body.put("PINCODE", "600024");

        //leadAdditionalInfo
        body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "FALSE");
        body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "TRUE");
        body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "FALSE");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLMigerationHeroLeadRequest.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
        if (responseObject.getStatusCode() == 201) {
            LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            leadId = responseObject.jsonPath().getString("leadId");
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),"9871054942");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"25");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
        }

    }

    @Test(description="Verify the PL v3 Hero lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3Hero_CreateLead")
    @Owner(emailId = "<EMAIL>")
    public void TC004_PLV3Hero_FetchLeadAllData() throws JSONException
    {

        for(int i=0;i<15;i++)
        {

            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
                break;

        }

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
        }


        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());

    }


    @Test(description="Update lead basic details",groups = {"Regression"},dependsOnMethods = "TC004_PLV3Hero_FetchLeadAllData")
    @Owner(emailId = "<EMAIL>")
    public void TC005_PLV3Hero_UpdateLeadBasicDetails() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("ipAddress", LendingConstants.IP_ADDRESS);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation","BASIC_DETAILS");
        body.put("DOB", LendingConstants.DOB_PLv3_HERO);
        body.put("PAN", "**********");
        body.put("EMAIL", Utilities.randomEmailGeneration());
        body.put("GENDER","Female");
        body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");
        body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");
        body.put("PINCODE", "600024");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/BasicDetailUpdatePersonalLoanMigrationRequest.json";


        for(int i=0;i<2;i++)
        {
            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
            if(responseObject.getStatusCode()==200)
                break;
        }
        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
        {
            LOGGER.info("baseResponseCode : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),"9871054942");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");

            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");
            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
            Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
            DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
            PAN=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());

    }


    @Test(description = "Verify the Personal Loan Migration lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC005_PLV3Hero_UpdateLeadBasicDetails")
    @Owner(emailId = "<EMAIL>")
    public void TC006_PLv3HERO_FetchDataPostBasicDetailUpdate() throws JSONException
    {
        for(int i=0;i<15;i++)
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
                break;
        }

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),PAN);
        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
    }



    @Test(description="Update lead occupation details",groups = {"Regression"},dependsOnMethods = "TC006_PLv3HERO_FetchDataPostBasicDetailUpdate")
    @Owner(emailId = "<EMAIL>")
    public void TC007_PLV3Hero_UpdateLeadOccupationDetails() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("ipAddress", LendingConstants.IP_ADDRESS);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation","OCCUPATION_DETAILS");
        body.put("OCCUPATION", LendingConstants.PLv3_OCCUPATION);
        body.put("ANNUAL_INCOME", "200000");
        body.put("EMPLOYER_ID", "E66767");
        body.put("EMPLOYER_NAME","Paytm");
        body.put("BUSINESS_NAME","Shivangi QR service");
        body.put("LOAN_PURPOSE","Personal use");
        body.put("LOAN_PURPOSE_KEY","2");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/OccupationDetailsPersonalLoanMigrationHero.json";


        for(int i=0;i<2;i++)
        {
            responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
            if(responseObject.getStatusCode()==200)
                break;
        }
        if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
        {
            LOGGER.info("baseResponseCode : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),"9871054942");

            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");
            userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
            occupation=responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION");
            income=responseObject.jsonPath().getString("solutionAdditionalInfo.ANNUAL_INCOME");
        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());

    }




    @Test(description = "Verify the PLv3 lead data after updating Occupation Details",groups = {"Regression"},dependsOnMethods = "TC007_PLV3Hero_UpdateLeadOccupationDetails")
    @Owner(emailId = "<EMAIL>")
    public void TC008_PLv3HERO_FetchDataPostOccupationDetailUpdate() throws JSONException
    {
        for(int i=0;i<15;i++)
        {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
                break;
        }

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION"),occupation);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ANNUAL_INCOME"),income);
        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
    }



    @Test(description="Update lead details in SAI",groups = {"Regression"},dependsOnMethods = "TC008_PLv3HERO_FetchDataPostOccupationDetailUpdate")
    @Owner(emailId = "<EMAIL>")
    public void TC009_PLV3Hero_UpdateLeadDetailsinSAI() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("statusMessage","ADDITIONAL_INFO_UPDATE");
        body.put("status","SUCCESS");
        body.put("bureau", "CIBIL");
        body.put("PAN", "**********");
        body.put("F_NAME",firstName);
        body.put("L_NAME",lastName);
        body.put("EMAIL","<EMAIL>");
        body.put("DOB","1979-10-05");
        body.put("gender","FEMALE");
        body.put("pincode","600024");

        responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());

    }

    @Test(description = "Verify the PLv3 lead data after Updating details in SAI",groups = {"Regression"},dependsOnMethods = "TC009_PLV3Hero_UpdateLeadDetailsinSAI")
    @Owner(emailId = "<EMAIL>")
    public void TC010_PLv3HERO_FetchDataPostSAIlUpdate() throws JSONException
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),firstName);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),lastName);
        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
    }


    @Test(description = "Verify Fetch CIR for PLv3 lead",groups = {"Regression"},dependsOnMethods = "TC010_PLv3HERO_FetchDataPostSAIlUpdate")
    @Owner(emailId = "<EMAIL>")
    public void TC011_PLv3HERO_FetchCIR() {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("longitude",LendingConstants.LONGITUDE);
        headers.put("latitude",LendingConstants.LATITUDE);
        headers.put("custId", custId);


        Map<String,Object> body=new HashMap<String,Object>();

        responseObject= lendingBaseClassObject.v2FetchCIR(queryParams, headers,body);


        if(responseObject.getStatusCode()==200) {

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"194");

            //Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_BUREAU_TYPE"),"CIBIL");

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_CHECK_DONE"),"TRUE");
            Assert.assertEquals(responseObject.jsonPath().getString("creditState"),"BUREAU_SUCCESS");

            bureauRequest=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST");
            bureauResponse=responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE");
            bureauCreditState=responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE");
            breCreditScore= responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE");
        }
    }

    @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC011_PLv3HERO_FetchCIR")
    @Owner(emailId = "<EMAIL>")
    public void TC012_PLv3HERO_BRE1Callback() throws JSONException
    {

        for(int i =0;i<=8;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OFFER_REQUESTED.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_IN_PROGRESS.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_REQUESTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"225");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            for(int i =0;i<15;i++) {
                responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

                if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage())) {
                    LOGGER.info("BRE 1 passed without callback");
                    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
                    Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
                    break;
                }
            }
            BRE1Callback(LendingConstants.PL_V3_SOLUTION);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE_COMPLETED.getStage())) {
                LOGGER.info("BRE 1 passed with callback");
                LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
                Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
                Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
            }
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
        }
    }


    @Test(description = "Verify the PLv3 lead data after BRE1 Success",groups = {"Regression"},dependsOnMethods = "TC012_PLv3HERO_BRE1Callback")
    @Owner(emailId = "<EMAIL>")
    public void TC013_PLv3HERO_FetchDataPostBRE1Success() throws JSONException
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE1_SUCCESS.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OFFER_GENERATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE1_SUCCESS.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"227");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_REQUEST"),bureauRequest);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_RESPONSE"),bureauResponse);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CREDIT_STATE"),bureauCreditState);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BRE_CREDIT_SCORE"),breCreditScore);

        }
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
    }

    public Response BRE1Callback(String solution) {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", solution);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "BRE1_SUCCESS");
        body.put("BRE1_OFFER_DETAILS", ""+stringify_json+"");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PLv3HeroBRE1CallbackRequest.json";
        responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);

        return responseObject;

    }





    @Test(description = "Verify PL v3 Hero lead Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC013_PLv3HERO_FetchDataPostBRE1Success")
    @Owner(emailId = "<EMAIL>")
    public void TC014_PLv3HERO_LoanOfferAccept() {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation", "OFFER_ACCEPTED");

        //solutionAdditionalInfo
        body.put("LOAN_TENURE", "12");
        body.put("LOAN_TENURE_UNIT", "MONTH");
        body.put("LOAN_AMOUNT_IN_NUMBER", "70000");
        body.put("LOAN_AMOUNT_IN_WORDS", "Seventy Thousand");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "6824");
        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Six Thousand Eight Hundred twenty four");
        body.put("LOAN_RATE_OF_INTEREST", "30");
        body.put("LOAN_INTEREST_AMOUNT", "990.70");
        body.put("LOAN_PROCESSING_FEE", "3675");
        body.put("PROCESSING_FEE_RATE", "5.25");
        body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "4336");
        body.put("LOAN_DISBURSAL_AMOUNT", "65348");
        body.put("STAMP_DUTY_CHARGES", "200");
        body.put("BROKEN_PERIOD_INTEREST", "116");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3HeroLoanOfferAccepted.json";

        responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
            loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
            loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
            loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
            loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
            loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
            loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
            loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
            stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
            brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
        }
    }



    @Test(description = "Verify the PLv3 lead data after Loan offer accept",groups = {"Regression"},dependsOnMethods = "TC014_PLv3HERO_LoanOfferAccept")
    @Owner(emailId = "<EMAIL>")
    public void TC015_PLv3HERO_FetchDataPostLoanOfferAccept() throws JSONException
    {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200 || responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()) ||
                responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_REQUIRED.getStage()))

        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

            //Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            //Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
            //Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),loanRateofInterest);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT"),loanInterestAmount);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),loanProcessingFeeRate);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),loanDisbursalAmount);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),loanTenure);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),stampDutyCharges);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),loanAmount);
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),loanEquatedMonthlyInstallment);

            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST"),brokerPeriodInterest);
        }
        //Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
    }



    @Test(description="Initiate New KYC by Search By PAN",groups = {"Regression"},dependsOnMethods = "TC015_PLv3HERO_FetchDataPostLoanOfferAccept")
    @Owner(emailId = "<EMAIL>")
    public void TC016_PLv3HERONewKYC_InitiateKYCSearchByPAN() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("kycMode","SEARCH_BY_PAN");


        requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByPanRequest.json";

        responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

    }

    @Test(description="Fetch lead post KYC initiate",groups = {"Regression"},dependsOnMethods = "TC016_PLv3HERONewKYC_InitiateKYCSearchByPAN")
    @Owner(emailId = "<EMAIL>")
    public void TC017_PLv3HERONewKYC_FetchLeadPostKYCInitiate() {

        for(int i =0;i<=45;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_FAILED.getStage())) {
                break;
            }
        }
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_FAILED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_FAILED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2002");
    }


    @Test(description="Update PAN details in SAI for New KYC",groups = {"Regression"},dependsOnMethods = "TC017_PLv3HERONewKYC_FetchLeadPostKYCInitiate")
    @Owner(emailId = "<EMAIL>")
    public void TC018_PLv3HERONewKYC_UpdateLeadDetailsinSAI() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("statusMessage","ADDITIONAL_INFO_UPDATE");
        body.put("status","SUCCESS");
        body.put("PAN", "**********");
        body.put("DOB","1973-01-01");
        body.put("GENDER","MALE");
        body.put("NSDL_NAME","Nagu Singh");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/UpdateSAIRequest.json";

        responseObject = lendingBaseClassObject.updateLenderDataSetSetInSAI(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");


    }

    @Test(description="Initiate New KYC by Search By PAN",groups = {"Regression"},dependsOnMethods = "TC018_PLv3HERONewKYC_UpdateLeadDetailsinSAI")
    @Owner(emailId = "<EMAIL>")
    public void TC019_PLv3HERONewKYC_InitiateKYCSearchByAadhar() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("kycMode","SEARCH_BY_AADHAAR");
        body.put("aadhaar","2671");
        body.put("gender","MALE");



        requestBodyJsonPath="MerchantService/V1/workflow/lead/Initiate/KYC/SearchByAAdhaarRequest.json";

        responseObject = lendingBaseClassObject.initiateKYCUsingSearchByPan(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.KYC_INITIATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "2000");

    }


    @Test(description = "Verify lead details after KYC is initiated",groups={"Regression"},dependsOnMethods = "TC019_PLv3HERONewKYC_InitiateKYCSearchByAadhar")
    @Owner(emailId = "<EMAIL>")
    public void TC020_PLv3HERO_FetchDataPostKYCInitiated() throws JSONException
    {

        for(int i =0;i<=45;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_REQUIRED.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2020");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_REQUIRED.getStage());


    }



    @Test(description = "Verify the PLv3 lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC020_PLv3HERO_FetchDataPostKYCInitiated")
    @Owner(emailId = "<EMAIL>")
    public void TC021_PLv3HERO_UploadCustomerPhoto() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("customerPhoto", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken,"others");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);


    }

    @Test(description = "Verify the  details of Uploaded Customer Photo",groups = {"Regression"}, dependsOnMethods = "TC021_PLv3HERO_UploadCustomerPhoto")
    @Owner(emailId = "<EMAIL>")
    public void TC022_PLv3HERO_VerifyUploadedCustomerPhoto() {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        //Customerphoto
        Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "others");
        Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "customerPhoto");

    }


    @Test(description = "Verify the PLv3 lead Upload customer photo",groups = {"Regression"},dependsOnMethods = "TC022_PLv3HERO_VerifyUploadedCustomerPhoto")
    @Owner(emailId = "<EMAIL>")
    public void TC023_PLv3HERO_UploadSelfie() throws InterruptedException {

        Response responseObject = lendingBaseClassObject.utilityForDocumentUpload("selfie", leadId, custId,
                LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PL_V3_SOLUTION, LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken,"selfie");

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
        Assert.assertEquals(responseObject.jsonPath().getString("leadId"), leadId);


    }

    @Test(description = "Verify the details of Uploaded Selfie", dependsOnMethods = "TC023_PLv3HERO_UploadSelfie", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC024_PLv3HERO_VerifyUploadedSelfie() {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        if(responseObject.jsonPath().getString("documents[0].docType").equals("selfie")) {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[0].docProvided"), "selfie");

        }else {
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docType"), "selfie");
            Assert.assertEquals(responseObject.jsonPath().getString("documents[1].docProvided"), "selfie");


        }

    }

    @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC024_PLv3HERO_VerifyUploadedSelfie")
    @Owner(emailId = "<EMAIL>")
    public void TC025_PLv3HERO_FetchDataPostKYCIntiated() throws JSONException
    {

        for(int i =0;i<=35;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_SELFIE_UPLOADED.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.KYC_SELFIE_UPLOADED.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_SELFIE_UPLOADED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"2021");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

        }




    }


    @Test(description = "Verify lead details after Fetch CIR",groups={"Regression"},dependsOnMethods = "TC025_PLv3HERO_FetchDataPostKYCIntiated")
    @Owner(emailId = "<EMAIL>")
    public void TC026_PLv3HERO_FetchDataPostSelfieUploaded() throws JSONException
    {

        for(int i =0;i<=55;i++) {
            responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);

            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BRE2_IN_PROGRESS.getStage()))
        {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"387");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));

        }

        //Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());


    }





//
//		@Test(description = "Verify PL v3 Lead Second BRE should be Inititated", dependsOnMethods = "TC024_PLv3HERO_FetchDataPostSelfieUploaded",groups = {"Regression"})
//		@Owner(emailId = "<EMAIL>")
//		public void TC025_PLv3HERO_SecondBREInitiated() {
//
//			responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
//					LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//			if(!(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
//					equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage())))
//			{
//				for(int i =0;i<8;i++) {
//
//					responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
//							LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//					if((responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
//							equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))) {
//						break;
//					}
//				}
//				LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//				Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//				Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//
//				LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//				Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_INITIATED.getStage());
//				Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
//				Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"387");
//			}
//		}

    @Test(description = "Verify PL v3 Lead Second BRE callback", dependsOnMethods = "TC026_PLv3HERO_FetchDataPostSelfieUploaded",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC027_PLv3HERO_SecondBRECallback() {

        responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                equals(LendingLeadStages.SECOND_BRE_INITIATED.getStage()))
        {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("leadId", leadId);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_RISK,custId,LendingConstants.JWT_RISK_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "BRE2_SUCCESS");
            body.put("BASE_ID", baseID);
            body.put("LOAN_OFFER_ID", loanOfferID);
            body.put("IS_EMAIL_VERIFICATION_MANDATORY", "true");
            body.put("SKIP_EMANDATE_ELIGIBLE", "false");
            body.put("IS_OFFER_DOWNGRADE_AVAILABLE", "false");

            requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3BRE2CallbackRequest.json";
            responseObject = lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers, body,requestBodyJsonPath,"BRE2");
            LOGGER.info("BRE 2 Success with callback");

        } else {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
            LOGGER.info("BRE 2 Success without callback");
        }

    }

    @Test(description = "Verify PL v3 Lead After BRE2 success Callback", dependsOnMethods = "TC027_PLv3HERO_SecondBRECallback", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC028_PLv3HERO_FetchDataAfterBRE2Success() {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));

        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.SECOND_BRE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_COMPLETE.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"388");

    }


    @Test(description = "Verify PL v3 Lead If Additional Data is required", dependsOnMethods = "TC028_PLv3HERO_FetchDataAfterBRE2Success",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC029_PLv3HERO_AdditionalIsRequiredorNot() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId", leadId);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "IS_ADDITIONAL_DATA_REQUIRED");
        body.put("IS_CURRENT_ADDRESS_SAME_AS_PERMANENT_ADDRESS", "TRUE");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataRequired.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

    }

    @Test(description = "Verify PL v3 Lead Additional Data is required", dependsOnMethods = "TC029_PLv3HERO_AdditionalIsRequiredorNot",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC030_PLv3HERO_FetchLeadVerifyAdditionalData() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"318");
        }else {
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.ADDITIONAL_DATA_NOT_REQUIRED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"317");
        }
    }


    @Test(description = "Verify PL v3 Lead Additional Data is added", dependsOnMethods = "TC030_PLv3HERO_FetchLeadVerifyAdditionalData",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC031_PLv3HERO_AdditionalDataCapture() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                equals(LendingLeadStages.ADDITIONAL_DATA_REQUIRED.getStage())) {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("leadId", leadId);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "ADDITIONAL_DATA_CAPTURED");
            body.put("BUSINESS_EMAIL", "<EMAIL>");
            body.put("BUSINESS_EMAIL_VERIFICATION_STATUS", "TRUE");
            body.put("FATHER_NAME", "RAM");
            requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3AdditionalDataCaptured.json";

            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        }

    }




    @Test(description = "Verify PL v3 Lead BRE3 Success", dependsOnMethods = "TC031_PLv3HERO_AdditionalDataCapture",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC032_PLv3HERO_BRE3Success() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        for(int i =0;i<10;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").
                    equals(LendingLeadStages.BRE3_SUCCESS.getStage())) {
                break;
            }
        }
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");

    }


    @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC032_PLv3HERO_BRE3Success", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC033_PLv3HERO_UpdateKYCNameInSAI() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId", leadId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("statusMessage", "ADDITIONAL_INFO_UPDATE");
        body.put("status", "SUCCESS");
        body.put("CKYC_NAME", "Anmol jain");
        body.put("PAN", "**********");

        requestBodyJsonPath="MerchantService/V2/lending/dataUpdate/PLv3updateKYCNameINSai.json";
        responseObject = lendingBaseClassObject.updateCKYCNameInSAI(queryParams, headers, body,requestBodyJsonPath);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        Assert.assertEquals(responseObject.jsonPath().getString("oeStage"), LendingLeadStages.BRE3_SUCCESS.getStage());
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        ckycName=responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME");
    }


    @Test(description = "Verify for PL v3 CKYC name update in SAI", dependsOnMethods = "TC033_PLv3HERO_UpdateKYCNameInSAI",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC034_PLv3HERO_FetchLeadUpdateCKYCinSAI() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),ckycName);

    }


    @Test(description = "Verify PL v3 Lead the bank details", dependsOnMethods = "TC034_PLv3HERO_FetchLeadUpdateCKYCinSAI", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC035_PLv3HERO_SaveBankDetails() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);
        headers.put("Content-Type", "application/json");

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("bankName", bankName);
        body.put("bankAccountNumber", bankAccountNumber);
        body.put("ifsc", ifsc);
        body.put("bankAccountHolderName", bankAccountHolderName);
        body.put("EMANDATE_TYPE", "Internet Banking");

        responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body, true);

        if (responseObject.getStatusCode() == 200) {

            Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
        }

        else {

            for (int i = 1; i < 4; i++) {
                LOGGER.info("Again hitting with same data: retry-count: " + i);
                responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);

                if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
                {
                    break;
                }

            }

        }

        responseObject = lendingBaseClassObject.fetchExistingLeadData(LendingConstants.PL_V3_SOLUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE ,LendingConstants.PAYTM_APP_CHANNEL ,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO, sessionToken);

    }


    @Test(description = "For PL v3 Bank Verification", dependsOnMethods = "TC035_PLv3HERO_SaveBankDetails",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC036_PLv3HERO_FetchLeadPostBankVerification() {
        for(int i=0;i<5;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANKING_ACTION_DONE.getStage())) {
                break;
            }
        }
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE3_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"394");
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),bankName);
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),bankAccountNumber);
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),ifsc);
        //Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),bankAccountHolderName);


    }



    @Test(description = "Verify PL v3 Lead Emandate Callback", dependsOnMethods = "TC036_PLv3HERO_FetchLeadPostBankVerification", groups = {
            "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC037_PLv3HERO_EmandateCallback() throws InterruptedException {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("status", "EMANDATE_SUCCESS");

        Response responseObject = lendingBaseClassObject.getEmandateCallback(queryParams, headers, body);

        lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);

        Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");

    }

    @Test(description = "For PL v3 Emandate Callback Stage Verification", dependsOnMethods = "TC037_PLv3HERO_EmandateCallback",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC038_PLv3HERO_FetchLeadPostEmandate() {
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
    }


    @Test(description = "For PL v3 Hero Generate Loan Agreement", dependsOnMethods = "TC038_PLv3HERO_FetchLeadPostEmandate", groups = { "Regression" })
    @Owner(emailId =  "<EMAIL>")
    public void TC039_PLv3HERO_GenerateLoanAgreement() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);

        Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " + responseObject.getStatusCode());
        if (responseObject.jsonPath().getString("meta.status").equals("success")) {

            code = responseObject.jsonPath().getString("data.state.code");
            tncName = responseObject.jsonPath().getString("data.state.tncName");
            url = responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifier = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5 = responseObject.jsonPath().getString("data.state.md5");
        }

    }


    @Test(description = "For PL v3 Hero Generate Sanction Letter", dependsOnMethods = "TC039_PLv3HERO_GenerateLoanAgreement", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC040_PLv3HERO_GenerateSanctionLetter() {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        queryParams.put("tncType", "LOAN_SANCTION_TNC");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", sessionToken);

        Response responseObject = lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);

        LOGGER.info("Status Code : " + responseObject.getStatusCode());
        if (responseObject.jsonPath().getString("meta.status").equals("success")) {

            codeSanctionLetter = responseObject.jsonPath().getString("data.state.code");
            tncNameSanctionLetter = responseObject.jsonPath().getString("data.state.tncName");
            urlSanctionLetter = responseObject.jsonPath().getString("data.state.url");
            uniqueIdentifierSanctionLetter = responseObject.jsonPath().getString("data.state.uniqueIdentifier");
            md5SanctionLetter = responseObject.jsonPath().getString("data.state.md5");
        }

    }

    @Test(description = "For PL v3 Hero Verify submit application", dependsOnMethods = "TC040_PLv3HERO_GenerateSanctionLetter", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC041_PLv3HERO_SubmitApplication() throws InterruptedException {

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", leadId);
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", sessionToken);

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("workflowOperation", "LOAN_AGREEMENT_ACCEPTED");
        body.put("LENDING_DYNAMIC_TNC", tncName);
        body.put("TNC_ACCEPTED_VERSION", 1);
        body.put("KYB_TNC_REF_NO", uniqueIdentifier);
        body.put("TNC_ACCEPTED_CODE", md5);


        body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC_VERSION", 1);

        body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
        body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);

        requestBodyJsonPath="MerchantService/V1/workflow/lead/PLv3LoanAgreementAcceptrequest.json";
        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers, body,requestBodyJsonPath);

        LOGGER.info("Status Code : " + responseObject.getStatusCode());

        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));

    }

    @Test(description = "For PL v3 Hero Verify Lead stage Post Submit Loan Application", dependsOnMethods = "TC041_PLv3HERO_SubmitApplication", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC042_PLv3HERO_FetchLeadPostSubmitApplication() {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
    }



    @Test(description = "For PL v3 Hero Verify PDC Callback", dependsOnMethods = "TC042_PLv3HERO_FetchLeadPostSubmitApplication", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC043_PLv3HERO_PDCCallback() throws InterruptedException {
        for(int i =0;i<=10;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
                break;
            }

        }
        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.PREDISBURSAL_INITIATED.getStage())) {
            LOGGER.info("Loan Application Accepted with PDC Callback ");
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");
            headers.put("custId", custId);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("status", "LOAN_APPLICATION_ACCEPTED");
            body.put("USER_MESSAGE", "Loan disbursal is accepted");
            body.put("LMS_REASON_ID", "0");
            body.put("SYSTEM_MESSAGE", "Loan disbursal is accepted");

            responseObject = lendingBaseClassObject.pdcLoanApplicationAccepted(queryParams, headers, body,true);

            lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
            Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data updated successfully");
        }
        LOGGER.info("Loan Application Accepted without PDC Callback ");
    }

    @Test(description = "For PL v3 Hero Verify Lead stage After PDC Callback", dependsOnMethods = "TC043_PLv3HERO_PDCCallback", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>")
    public void TC044_PLv3HERO_FetchLeadPostPDCCallback() {

        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_APPLICATION_ACCEPTED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"350");
    }


    @Test(description = "For PL v3 Hero Verify if LMS Approve Callback required",groups = {"Regression"},dependsOnMethods = "TC044_PLv3HERO_FetchLeadPostPDCCallback")
    @Owner(emailId = "<EMAIL>")
    public void TC045_PLv3HERO_SubmitApplicationLMSApprovedCallback() {
        for(int i=0;i<10;i++) {
            responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                    LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
            if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
                break;
            }
        }
        if(responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LMS_SUBMIT_APPLICATION_SUCCESS.getStage())) {
            Map<String, String> queryParams = new HashMap<String, String>();
            queryParams.put("leadId", leadId);
            queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
            queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
            queryParams.put("channel", LendingConstants.PAYTM_APP_CHANNEL);
            queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);

            token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.JWT_LMS, custId,LendingConstants.JWT_LMS_KEY);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", token);

            Map<String, Object> body = new HashMap<String, Object>();
            body.put("workflowOperation", "LMS_APPLICATION_APPROVED");
            body.put("LOAN_ACCOUNT_NUMBER", "PYTM"+Utilities.randomNumberGenerator(8));
            body.put("LENDER_LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
            body.put("LENDER_DMS_ID", "");
            body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
            body.put("LOAN_ACCOUNT_CREATED_ON", "*************");
            body.put("LMS_REASON_ID", "");

            requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/PLv3LMSApproveCallback.json";
            responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
        }
        responseObject = lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,
                LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));


    }





}
