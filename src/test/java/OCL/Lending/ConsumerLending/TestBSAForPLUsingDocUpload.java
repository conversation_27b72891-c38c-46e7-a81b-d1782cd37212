package OCL.Lending.ConsumerLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestBSAForPLUsingDocUpload extends BaseMethod{

	private static final Logger LOGGER = LogManager.getLogger(TestBSAForPLUsingDocUpload.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
	Utilities utility=new Utilities();
	
	 String sessionToken = "";
	 String leadId="";
	 String custId="1001990272";
	 String consumerNumber="5000005000";
	 String consumerPassword="paytm@123";
	 String token="";
	 String stage="";
	 String feStage="";
	 String userIPAddress="";
	 String loanUserLatitude="";
	 String loanUserLongitude="";
	 String tncAdditionalParam="";
	 String staticTncAcceptanceTimeStamp="";
	 String lenderCustomerId="";
	 String requestBodyJsonPath="";	
	 String Pan="";
	 String Email="";
	 String DOB="";
	 String applicationId="";
	 String bureauRequest="";
	 String bureauResponse="";
	 String bureauCreditState="";
	 List<String> result=new ArrayList<String>();
	 String uuid="";

	 Response responseObject= null;
		 
	
	 
	@BeforeClass()
	 public void intitializeInputData() {
	
		LOGGER.info(" Before Suite Method for Consumer Login ");
		sessionToken = ApplicantToken(consumerNumber, consumerPassword);
		LOGGER.info("Applicant Token for Lending : " + sessionToken);
	   
	}
	
	
	
	@Test(description = "Verify whether there is any existing BSA PL lead present or not",groups = {"Regression"})
	  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC001_FetchLeadDeatils()
	    {
	
         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
	     
	     if(responseObject.jsonPath().getInt("statusCode")==200)
	     {
	    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		      
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
	        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
		      
	       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
	       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
	      
	       leadId=responseObject.jsonPath().getString("leadId");
	     }
	      
	     if(responseObject.jsonPath().getInt("statusCode")==404)
	     {
	    	
		      
		  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
	        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
		      
	      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
	      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
	      
	    
	     }
	
	    }
	
	
	 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
	  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC002_DeleteExistingLead()
	    {
		  Map<String,String> queryParams=new HashMap<String,String>();
		  queryParams.put("leadId",leadId);
		  queryParams.put("custId",custId);
		  queryParams.put("solution",LendingConstants.BSA_PL);
		  
		 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
			
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("custId", custId);
		  
	   
		lendingBaseClassObject.resetLendingLeads(queryParams, headers);
	    }
	 
	

	
	 @Test(description = "Create BSA PL Lead with all deatils",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
	  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC003_CreateHLDistributionPiramalLead()
	    {
		  Map<String,String> queryParams=new HashMap<String,String>();
		
		  queryParams.put("solution",LendingConstants.BSA_PL);
		  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
    	  queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
		
    	  Map<String,String> headers=new HashMap<String,String>();
	       headers = LendingBaseAPI.setHeadersReceivedFromFE();
	       headers.put("session_token", sessionToken);
	       headers.put("Content-Type", "application/json");
	       headers.put("custid", custId);
	       headers.put("ipAddress", "************");
	        
	       Map<String,Object> body = new HashMap<String, Object>();
	  	   body.put("workflowOperation","CREATE_LEAD");
	  	   body.put("mobile", consumerNumber);
	  	   body.put("parentLeadId", "0aae22a0-6c8e-418a-b1da-23e26fe9f3c1");
	  	   body.put("PRODUCT_TYPE", "PL");
	  	   body.put("OCCUPATION", "Salaried");
	  	   body.put("PRODUCT_ID","2000151");
		   body.put("CKYC_NAME", "testNameMatch");
		   body.put("NSDL_NAME", "testNameMatch");
		   body.put("BUSINESS_NAME", "Software infrasystems");
		   body.put("ACCEPTANCE_POLICY", "AT_LEAST_ONE_TXN_PER_MONTH");
		   body.put("BANK_STATEMENTS_START_DATE", "2021-10");
		   body.put("BANK_STATEMENTS_END_DATE", "2022-03");
	  	   body.put("BANK_STATEMENTS_REQUIRED_MONTHS", "6");
	  	 
	  	 
		   requestBodyJsonPath="MerchantService/v1/workflow/Lead/BSAPLRequest.json";
	  	 
		 
		 for(int i=0;i<2;i++)
		 {
			 
			 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
			 
			  if(responseObject.getStatusCode()==201)
			   break;
		 }
			  

		  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			  {
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ACCEPTANCE_POLICY"),"AT_LEAST_ONE_TXN_PER_MONTH");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_STATEMENTS_REQUIRED_MONTHS"),"6");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUSINESS_NAME"),"Software infrasystems");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CKYC_NAME"),"testNameMatch");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"2000151");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),"Redmi");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),"77.0900393");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),"testNameMatch");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_STATEMENTS_END_DATE"),"2022-03");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_STATEMENTS_START_DATE"),"2021-10");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),"28.66494");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION"),"Salaried");

		        
		        leadId=responseObject.jsonPath().getString("leadId");
		        custId=responseObject.jsonPath().getString("custId");
		        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
		        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
		     
		    
		    
		      }
		  
	        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		  
	    }
	 
	  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_CreateHLDistributionPiramalLead",groups = {"Regression"})
	  @Owner(emailId = "<EMAIL>",isAutomated = true)
	    public void TC004_FetchLeadAllDataUsingBASICDATA() throws JSONException
	    {
		  
		  
		  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
		
	     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
	     {
	    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		      
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
	        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
		      
	       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
	       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
	        
	       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
	        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
	        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
	     }
  
	      	
	     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
	    
	
	    }
			  @Test(description = "Enter the bank details", dependsOnMethods = "TC004_FetchLeadAllDataUsingBASICDATA", groups = {
				"Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC005_SaveBankDetails() {
			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("solution", LendingConstants.BSA_PL);
			queryParams.put("entityType", LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			queryParams.put("channel", LendingConstants.DIY_P4B_APP_CHANNEL);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("session_token", sessionToken);
			headers.put("Content-Type", "application/json");
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("bankName", "PAYTM BANK");
			body.put("bankAccountNumber", "************");
			body.put("ifsc", "PYTM0123456");
			body.put("bankAccountHolderName", "Shivangi Goswami");
			body.put("EMANDATE_TYPE", "Internet Banking");
			
			responseObject = lendingBaseClassObject.saveBankDetailsV2API(queryParams, headers, body, true);
			
			if (responseObject.getStatusCode() == 200) {
			
				Assert.assertEquals(responseObject.jsonPath().getBoolean("nameMatchSuccess"), true);
			
			}
			
			else {
				
				for (int i = 1; i < 4; i++) {
					LOGGER.info("Again hitting with same data: retry-count: " + i);
					responseObject = lendingBaseClassObject.saveBankDetails(queryParams, headers, body);
					
					if (responseObject.jsonPath().getString("statusCode").contentEquals("200"))
						break;
				}
			
			}
			
		
				Assert.assertEquals(responseObject.getStatusCode(),200);
				
			
			}
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_SaveBankDetails",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC006_FetchLeadAllDataUsingALLDATA() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankName"),"PAYTM BANK");
			        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountNumber"),"************");
			        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.ifsc"),"PYTM0123456");
			        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.bankAccountHolderName"),"testNameMatch");
			        Assert.assertEquals(responseObject.jsonPath().getString("bankDetail.skipBankDetails"),"false");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			
			    }
			  
			  @Test(description = "Upload Bank Proof 1",groups = {"Regression"},dependsOnMethods = "TC006_FetchLeadAllDataUsingALLDATA")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC007_UploadFirstBankingProof() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Oct Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement1","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			    result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC007_UploadFirstBankingProof",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC008_VerifyUploadedDocIsVisibleInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docProvided"),"BankStatement1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.pageNumber"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),result.get(0));
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.status"),"0");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			
	    }
			  
			  @Test(description = "Upload Bank Proof 1",groups = {"Regression"},dependsOnMethods = "TC008_VerifyUploadedDocIsVisibleInFetchLead")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC009_UploadAlreadyUploadedDocument_CanBeUploadedAgain() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Oct Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement1","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			  	result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC009_UploadAlreadyUploadedDocument_CanBeUploadedAgain",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC010_VerifyUploadedDocDetailsAreUpdatedInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docProvided"),"BankStatement1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.pageNumber"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),result.get(1));
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.status"),"0");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			  @Test(description = "Upload Bank Proof 2",groups = {"Regression"},dependsOnMethods = "TC010_VerifyUploadedDocDetailsAreUpdatedInFetchLead")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC011_UploadSecondBankingProof() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Nov Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement2","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			  	result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC011_UploadSecondBankingProof",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC012_VerifyUploadedDocIsVisibleInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docProvided"),"BankStatement2");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.pageNumber"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),result.get(2));
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.status"),"0");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			  @Test(description = "Upload Bank Proof 3",groups = {"Regression"},dependsOnMethods = "TC012_VerifyUploadedDocIsVisibleInFetchLead")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC013_UploadThirdankingProof() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Dec Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement3","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			    result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC013_UploadThirdankingProof",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC014_VerifyUploadedDocIsVisibleInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docProvided"),"BankStatement3");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.pageNumber"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),result.get(3));
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.status"),"0");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			  
			  @Test(description = "Upload Bank Proof 4",groups = {"Regression"},dependsOnMethods = "TC014_VerifyUploadedDocIsVisibleInFetchLead")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC015_UploadFourthBankingProof() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Jan Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement4","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			  	result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC015_UploadFourthBankingProof",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC016_VerifyUploadedDocIsVisibleInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docProvided"),"BankStatement4");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.pageNumber"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),result.get(4));
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.status"),"0");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			
			  @Test(description = "Upload Bank Proof 5",groups = {"Regression"},dependsOnMethods = "TC016_VerifyUploadedDocIsVisibleInFetchLead")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC017_UploadFifthBankingProof() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Feb Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement5","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			  	result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC017_UploadFifthBankingProof",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC018_VerifyUploadedDocIsVisibleInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docProvided"),"BankStatement5");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.pageNumber"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),result.get(5));
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.status"),"0");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			  @Test(description = "Upload Bank Proof 6",groups = {"Regression"},dependsOnMethods = "TC018_VerifyUploadedDocIsVisibleInFetchLead")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC019_UploadSixthBankingProof() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Mar Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement6","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			  	result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC019_UploadSixthBankingProof",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC020_VerifyUploadedDocIsVisibleInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertTrue(responseObject.jsonPath().getList("documents.docProvided").contains("BankStatement1,BankStatement2,BankStatement3,BankStatement4,BankStatement5"));
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docUUId"),result.get(6));
			        result.remove(0);
			        Assert.assertTrue(responseObject.jsonPath().getList("documents.docUUId").contains(result));
			       
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			  
			  @Test(description = "Delete The first uploaded doc",dependsOnMethods = "TC020_VerifyUploadedDocIsVisibleInFetchLead",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC021_VerifyDocumentDeletion() throws JSONException
			    {
				  
				  Map<String,String> queryParams=new HashMap<String,String>();
					
				  queryParams.put("solution",LendingConstants.BSA_PL);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
		    	  queryParams.put("leadId",leadId);
				
		    	token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.BSA_CLIENT_ID, custId,LendingConstants.BSA_SECRET);	
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
			    headers.put("Content-Type", "application/json");
			    headers.put("custid", custId);
			      
			        
			       Map<String,Object> body = new HashMap<String, Object>();
			  	   body.put("docType","bankProof");
			  	   body.put("docProvided", "BankStatement6");
			  	   body.put("docUUID", result.get(6));
			  	   body.put("status", "6");
			  	   
				   requestBodyJsonPath="MerchantService/v2/lending/lead/document/DeleteDocumentRequest.json";
			  	 
				  responseObject= lendingBaseClassObject.documentStatus(queryParams, headers,body, requestBodyJsonPath);
					 
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			     
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertTrue(responseObject.jsonPath().getList("documents.docProvided").contains("BankStatement1,BankStatement2,BankStatement3,BankStatement4,BankStatement5"));
			        Assert.assertTrue(responseObject.jsonPath().getList("documents.docUUId").contains(result));
			       
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			  
			  @Test(description = "Upload Bank Proof 6",groups = {"Regression"},dependsOnMethods = "TC021_VerifyDocumentDeletion")
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC022_UploadSixthBankingProof() throws InterruptedException
			    {
				    String path="src/test/resources/MerchantService/v2/lending/lead/document/Mar Statement.pdf";
			  	   
			  	 Response responseObject= lendingBaseClassObject.docUploadUsingToolsAPI(path,leadId,custId,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.BSA_PL,"BankStatement6","bankProof");
			  	
			  	lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
		   	   
			  	result.add(responseObject.jsonPath().getString("result"));
			
			    }
			  
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC022_UploadSixthBankingProof",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC023_VerifyUploadedDocIsVisibleInFetchLead() throws JSONException
			    {
				  
				  
				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"428");
			        Assert.assertEquals(responseObject.jsonPath().getString("documents.docType"),"bankProof");
			        Assert.assertTrue(responseObject.jsonPath().getList("documents.docProvided").contains("BankStatement1,BankStatement2,BankStatement3,BankStatement4,BankStatement5"));
			        Assert.assertTrue(responseObject.jsonPath().getList("documents.docUUId").contains(result));
			       
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANKING_ACTION_DONE.getStage());
			    
			    }
			  
			  @Test(description = "Move to doc uploaded stage",dependsOnMethods = "TC023_VerifyUploadedDocIsVisibleInFetchLead",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC024_VerifyDocumentUploadedStage()
			    {
				  Map<String,String> queryParams=new HashMap<String,String>();
				
				  queryParams.put("solution",LendingConstants.BSA_PL);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
		    	  queryParams.put("leadId",leadId);
				
		    	  Map<String,String> headers=new HashMap<String,String>();
			  
			       headers.put("session_token", sessionToken);
			       headers.put("Content-Type", "application/json");
			       headers.put("custid", custId);
		
			        
			       Map<String,Object> body = new HashMap<String, Object>();
			  	   body.put("workflowOperation","DOCUMENT_UPLOADED");
			   
			  	  requestBodyJsonPath="MerchantService/v1/workflow/Lead/UpdateLeadWithWorkflowOPSRequest.json";
					 
				responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  

				  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.DOCUMENT_UPLOADED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.DOCUMENT_UPLOADED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"114");
				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMANDATE_TYPE"),"Internet Banking");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LATEST_PENNYDROP_STATUS"),"true");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.CANCELLED_CHEQUE_QC_REQUIRED"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_NAME_MATCH_PERCENTAGE"),"100");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_NAME_MATCH_STATUS_ID"),"1534");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_BANK_NAME_QC_REQUIRED"),"FALSE");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),"77.0900393");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),"testNameMatch");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BANK_STATEMENTS_END_DATE"),"2022-03");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_PENNYDROP_SUCCESSFUL"),"true");

				    
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.DOCUMENT_UPLOADED.getStage());
				  
			    }
			  
			  @Test(description = "Get BSA Doc Upload callback",dependsOnMethods = "TC024_VerifyDocumentUploadedStage",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC025_MoveTheLead_To_BSADocUploadStage() throws JSONException
			    {
				  
				 responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.DOCUMENT_UPLOADED.getStage()))
					
				  {
					  
					  Map<String,String> queryParams=new HashMap<String,String>();
						
					  queryParams.put("solution",LendingConstants.BSA_PL);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
			    	  queryParams.put("leadId",leadId);
					
			    	  Map<String,String> headers=new HashMap<String,String>();
				  
				       headers.put("session_token", sessionToken);
				       headers.put("Content-Type", "application/json");
				       headers.put("custid", custId);
			
				        
				       Map<String,Object> body = new HashMap<String, Object>();
				  	   body.put("workflowOperation","BSA_DOC_UPLOAD_SUCCESS");
				   
				  	  requestBodyJsonPath="MerchantService/v1/workflow/Lead/UpdateLeadWithWorkflowOPSRequest.json";
						 
					  responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
						 
						  
				  }
					  
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BSA_DOC_UPLOAD_SUCCESS.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BSA_DOC_UPLOAD_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BSA_DOC_UPLOAD_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"270");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BSA_DOC_UPLOAD_SUCCESS.getStage());
			    
			
			    }
			  
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC025_MoveTheLead_To_BSADocUploadStage",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC026_MoveTheLead_To_BankVerificationSuccessStage() throws JSONException
			    {
				  
				 responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BSA_DOC_UPLOAD_SUCCESS.getStage()))
					
				  {
					  
					  Map<String,String> queryParams=new HashMap<String,String>();
						
					  queryParams.put("solution",LendingConstants.BSA_PL);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
			    	  queryParams.put("leadId",leadId);
					
			    	  Map<String,String> headers=new HashMap<String,String>();
				  
				       headers.put("session_token", sessionToken);
				       headers.put("Content-Type", "application/json");
				       headers.put("custid", custId);
			
				        
				       Map<String,Object> body = new HashMap<String, Object>();
				  	   body.put("workflowOperation","BANK_STATEMENT_VERIFICATION_SUCCESS");
				   
				  	  requestBodyJsonPath="MerchantService/v1/workflow/Lead/UpdateLeadWithWorkflowOPSRequest.json";
						 
					  responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
						 
						  
				  }
					  
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BANK_STATEMENT_VERIFICATION_SUCCESS.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BANK_STATEMENT_VERIFICATION_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_STATEMENT_VERIFICATION_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"274");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BANK_STATEMENT_VERIFICATION_SUCCESS.getStage());
			    
			
			    }  
			 
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC026_MoveTheLead_To_BankVerificationSuccessStage",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC027_MoveTheLead_To_BSACompleted() throws JSONException
			    {
				  
				 responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				
				 
				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BANK_STATEMENT_VERIFICATION_SUCCESS.getStage()))
					
				  {
					  
					  Map<String,String> queryParams=new HashMap<String,String>();
						
					  queryParams.put("solution",LendingConstants.BSA_PL);
					  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
			    	  queryParams.put("channel",LendingConstants.DIY_P4B_APP_CHANNEL);
			    	  queryParams.put("leadId",leadId);
					
			    	  Map<String,String> headers=new HashMap<String,String>();
				  
				       headers.put("session_token", sessionToken);
				       headers.put("Content-Type", "application/json");
				       headers.put("custid", custId);
			
				        
				       Map<String,Object> body = new HashMap<String, Object>();
				  	   body.put("workflowOperation","COMPLETE_BSA");
				   
				  	  requestBodyJsonPath="MerchantService/v1/workflow/Lead/UpdateLeadWithWorkflowOPSRequest.json";
						 
					  responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
						 
						  
				  }
					  
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BSA_PROCESS_COMPLETED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				      
				    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				      
			       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
			       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
			        
			       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BSA_PROCESS_COMPLETED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BSA_PROCESS_COMPLETED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"283");
			     }
		  
			      	
			     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BSA_PROCESS_COMPLETED.getStage());
			    
			
			    }  
			 
			  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC027_MoveTheLead_To_BSACompleted",groups = {"Regression"})
			  @Owner(emailId = "<EMAIL>",isAutomated = true)
			    public void TC028_FetchLeadAllData() throws JSONException
			    {
				  
				  for(int i=0;i<5;i++)
				  {
				   
				
			     responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.BSA_PL,"",LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
				 
				  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
				  {
					  break;
				  }
				  
				  }
				  
		         
			     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
			    
			
			    }
			
}
