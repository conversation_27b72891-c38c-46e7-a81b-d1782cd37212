package OCL.Lending.ConsumerLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestRedisLockCheckPL extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(TestRedisLockCheckPL.class);
    String solution="personal_loan_v3";
    String solutionTypeLevel2="ABFL";
    String entityType="INDIVIDUAL";
    String channel="PAYTM_APP";
    String leadId = "";
    String sessionToken = "";
    String consumerNumber="6660099999";
    String consumerPassword="paytm@123";
    String custId="1002181808";
    String token="";
    String requestBodyJsonPath="";
    String userIPAddress="";
    String staticTncAcceptanceTimeStamp="";
    String Email="";
    String DOB="";
    String PAN="";
    String occupation="";
    String income="";
    String firstName="BHAIRAVI";
    String lastName="LATASREE";
    String bureauRequest="";
    String bureauResponse="";
    String bureauCreditState="";
    String breCreditScore="";
    String stringify_json="{\\\"baseId\\\":\\\"PL_ABFL_1002181808_a5b3b52b\\\",\\\"bureauKicker\\\":false,\\\"bureauThick\\\":1,\\\"creditScore\\\":808,\\\"eMandateType\\\":\\\"MANDATORY\\\",\\\"field_investigation_needed\\\":false,\\\"lenderSchemeId\\\":90,\\\"lenderRiskCode\\\":\\\"CL_PL\\\",\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":200000.0,\\\"minLoanAmount\\\":10000.0,\\\"newOfferGenerated\\\":true,\\\"ntc\\\":0,\\\"offerEndDate\\\":\\\"Wed Jun 29 00:00:00 IST 2022\\\",\\\"offerId\\\":\\\"d9d83b10-9afd-4394-bfe0-8905033ce2c1\\\",\\\"offerStartDate\\\":\\\"Thu Mar 31 00:00:00 IST 2022\\\",\\\"offerValidity\\\":90,\\\"paytmThick\\\":0,\\\"productId\\\":\\\"78\\\",\\\"productVersion\\\":1,\\\"riskGrade\\\":\\\"L\\\",\\\"riskSegment\\\":\\\"L\\\",\\\"sourceOfWhitelist\\\":\\\"RISK\\\",\\\"isBre2Required\\\":false,\\\"skipMandate\\\":false,\\\"loanDownGradable\\\":false}";
    String loanTenure="";
    String loanAmount="";
    String loanEquatedMonthlyInstallment="";
    String loanRateofInterest="";
    String loanInterestAmount="";
    String loanProcessingFeeRate="";
    String loanDisbursalAmount="";
    String stampDutyCharges="";
    String brokerPeriodInterest="";
    String uuidCustomerPhoto = "";
    String uuidSelfie="";
    String loanOfferID="8e235b37-d638-454a-ab54-1664aec4bd36";
    String baseID="PL_ABFL_1002018119_10b8e9eb";
    String ckycName="";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier ="";
    String md5 ="";
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String bankName="PAYTM BANK";
    String bankAccountNumber="************";
    String ifsc="PYTM0123456";
    String bankAccountHolderName="Shivangi Goswami";


    Response responseObject= null;
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
    Utilities utility=new Utilities();


    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }



    @Test(description="Verify if there is any existing Personal Loan Migration ABFL Lead",groups= {"Regression"})
    @Owner(emailId = "<EMAIL>")
    public void TC001_PLv3ABFL_fetchlLead()
    {
        responseObject= lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);

        if(responseObject.jsonPath().getInt("statusCode")==200)
        {
            LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId=responseObject.jsonPath().getString("leadId");
        }

        if(responseObject.jsonPath().getInt("statusCode")==404)
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));

        }

    }


    @Test(description="Verify reseting existing Personal Loan Migration ABFL lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3ABFL_fetchlLead"})
    @Owner(emailId = "<EMAIL>")
    public void TC002_PLv3ABFL_DeleteExistingLead() {

        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams.put("leadId",leadId);
        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("custId", custId);

        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", token);
        headers.put("custId", custId);

        lendingBaseClassObject.resetLendingLeads(queryParams, headers);

    }

    @Test(description="Create Lead for Personal Loan Migeration ABFL",groups={"Regression"},dependsOnMethods = {"TC002_PLv3ABFL_DeleteExistingLead"})
    @Owner(emailId = "<EMAIL>")
    public void TC003_PLv3ABFL_CreateLead() {

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        //Parameters
        Map <String,String> queryParams= new HashMap<String,String>();
        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);
        queryParams.put("entityType",entityType);
        queryParams.put("channel", channel);

        //Headers
        Map <String,String> header = new HashMap<String,String>();
        header = LendingBaseAPI.setHeadersReceivedFromFE();
        header.put("Authorization", token);
        header.put("Content-Type", "application/json");
        header.put("custid", custId);
        header.put("ipAddress", LendingConstants.IP_ADDRESS);

        //Body
        Map <String,Object> body = new HashMap<String,Object>();
        body.put("workflowOperation", "CREATE_LEAD");
        body.put("mobile", consumerNumber);

        //solutionAdditionalInfo
        body.put("PRODUCT_ID", "25");
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE", "RISK");
        body.put("LOAN_OFFER_ID", loanOfferID);
        body.put("PRODUCT_VERSION", "1");
        body.put("BASE_ID", baseID);
        body.put("LENDER_ID", "5");
        body.put("WHITELISTING_SOURCE", "RISK");
        body.put("IS_EMANDATE_ELIGIBLE", "true");
        body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
        body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_TNC);
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_ABFL_LENDING_DYNAMIC_SECONDARY_TNC);
        body.put("IS_FATHER_NAME_REQUIRED", "true");
        body.put("MARITAL_STATUS", "NOT_KNOWN");
        body.put("IS_BRE3_REQUIRED", "true");
        body.put("PINCODE", "600024");


        //leadAdditionalInfo
        body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "TRUE");
        body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
        body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "TRUE");

        requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLMigerationABFLLeadRequest.json";

        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
        if (responseObject.getStatusCode() == 201) {
            LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            leadId = responseObject.jsonPath().getString("leadId");
         }

    }

    @Test(description="Update lead basic details",groups = {"Regression"},dependsOnMethods = "TC003_PLv3ABFL_CreateLead")
    @Owner(emailId = "<EMAIL>")
    public void TC005_PLV3ABFL_UpdateLeadBasicDetails() {
        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
        queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_ABFL);

        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);
        headers.put("ipAddress", LendingConstants.IP_ADDRESS);

        Map<String,Object> body=new HashMap<String,Object>();
        body.put("workflowOperation","BASIC_DETAILS");
        body.put("DOB", LendingConstants.DOB_PLv3_HERO);
        body.put("PAN", "**********");
        body.put("EMAIL", Utilities.randomEmailGeneration());

        body.put("GENDER","Female");
        body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");
        body.put("LENDER_STATIC_TNC_SETNAME", "kyc_lender_consent_pl_risk_abfl");
        body.put("PINCODE", "600024");
        requestBodyJsonPath = "MerchantService/V1/workflow/lead/BasicDetailUpdatePersonalLoanMigrationRequest.json";

        for(int i =0;i<=10;i++) {
       responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);

    }

    }

}
