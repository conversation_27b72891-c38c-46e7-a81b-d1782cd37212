package OCL.Lending.ConsumerLending;

import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class TestPersonalLoanHeroPanDedupefailed extends BaseMethod {
	

	private static final Logger LOGGER = LogManager.getLogger(TestPersonalLoanHeroPanDedupefailed.class);
	String solution="personal_loan_v3";
	String solutionTypeLevel2="HERO";
	String entityType="INDIVIDUAL";
	String channel="PAYTM_APP";
	String leadId = "";
	String sessionToken = "";
	String consumerNumber="9108951999";
	String consumerPassword="paytm@123";
	String custId="1002368419";
	String token="";
	String requestBodyJsonPath="";
	String userIPAddress="";
	String staticTncAcceptanceTimeStamp="";
	String Email="";
	String DOB="";
	String PAN="";
	String occupation="";
	String income="";
	String firstName="BHAIRAVI";
	String lastName="LATASREE";
	String bureauRequest="";
	String bureauResponse="";
	String bureauCreditState="";
	String breCreditScore="";
	String stringify_json="{\\\"baseId\\\":\\\"PL_HERO_1002368419_e82bc8e1\\\",\\\"bureau\\\":\\\"CIBIL\\\",\\\"bureauThick\\\":1,\\\"creditScore\\\":808,\\\"field_investigation_needed\\\":false,\\\"incentiveRate\\\":0.0,\\\"isEmandateEligible\\\":1,\\\"isIncentiveAllowed\\\":0,\\\"lastFetchDate\\\":1633046400000,\\\"loan_offered\\\":true,\\\"maxLoanAmount\\\":150000.0,\\\"maxTenure\\\":18,\\\"minLoanAmount\\\":30000.0,\\\"minTenure\\\":12,\\\"newOfferGenerated\\\":false,\\\"ntc\\\":0,\\\"offerId\\\":\\\"19e0e4ea-0c4b-40d8-b522-99b092dce1e3\\\",\\\"paytmThick\\\":0,\\\"productId\\\":\\\"25\\\",\\\"productType\\\":\\\"PL\\\",\\\"productVersion\\\":1,\\\"rejectionReason\\\":\\\"\\\",\\\"riskGrade\\\":\\\"VL\\\"}";
	String loanTenure="";
	String loanAmount="";
	String loanEquatedMonthlyInstallment="";
	String loanRateofInterest="";
	String loanInterestAmount="";
	String loanProcessingFeeRate="";
	String loanDisbursalAmount="";
	String stampDutyCharges="";
	String brokerPeriodInterest="";
	String uuidCustomerPhoto = "";
	String uuidSelfie="";
	String loanOfferID="19e0e4ea-0c4b-40d8-b522-99b092dce1e3";
	String baseID="PL_HERO_1002368419_e82bc8e1";
	String ckycName="";
	String code = "";
	String tncName = "";
	String url = "";
	String uniqueIdentifier ="";
	String md5 ="";
	String codeSanctionLetter = "";
	String tncNameSanctionLetter = "";
	String urlSanctionLetter = "";
	String uniqueIdentifierSanctionLetter = "";
	String md5SanctionLetter = "";
	String bankName="PAYTM BANK";
	String bankAccountNumber="************";
	String ifsc="PYTM0123456";
	String bankAccountHolderName="Shivangi Goswami";

	
	Response responseObject= null;
	LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();
	Utilities utility=new Utilities();
	
	
	@BeforeClass()
	 public void intitializeInputData() {
	
		LOGGER.info(" Before Suite Method for Consumer Login ");
		sessionToken = ApplicantToken(consumerNumber, consumerPassword);
		LOGGER.info("Applicant Token for Lending : " + sessionToken);
	   
	}
	
	

	@Test(description="Verify if there is any existing Personal Loan Migration Hero Lead",groups= {"Regression"})
	@Owner(emailId = "<EMAIL>")
	public void TC001_PLv3Hero_fetchlLead()
	{
		responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
	     
	     if(responseObject.jsonPath().getInt("statusCode")==200)
	     {
	    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
		    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		      
		    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
	        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
		      
	        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
	        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
	      
	       leadId=responseObject.jsonPath().getString("leadId");
	     }
	      
	     if(responseObject.jsonPath().getInt("statusCode")==404)
	     {
	    	 LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
	    	 Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
		      
	    	 LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
	    	 Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
	    
	     }
		
	}
	
	
	@Test(description="Verify reseting existing Personal Loan Migration Hero lead",groups={"Regression"},dependsOnMethods = {"TC001_PLv3Hero_fetchlLead"})
	@Owner(emailId = "<EMAIL>")
	public void TC002_PLv3Hero_DeleteExistingLead() {
		
		Map<String,String> queryParams=new HashMap<String,String>();

		queryParams.put("custId", custId);
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("leadId",leadId);
			  
		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("custId", custId);
		
		responseObject = lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
	     
		if (responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer.")){
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		}
	}
	
	@Test(description="Create Lead for Personal Loan Migeration Hero",groups={"Regression"},dependsOnMethods = {"TC002_PLv3Hero_DeleteExistingLead"})
	@Owner(emailId = "<EMAIL>")
	public void TC003_PLv3Hero_CreateLead() {
		
		token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		//Parameters
		Map <String,String> queryParams= new HashMap<String,String>();
		queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
		queryParams.put("solutionTypeLevel2",LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
		queryParams.put("entityType",entityType);
		queryParams.put("channel", channel);
		
		//Headers
		Map <String,String> header = new HashMap<String,String>();
		 header = LendingBaseAPI.setHeadersReceivedFromFE();
	       header.put("Authorization", token);
	       header.put("Content-Type", "application/json");
	       header.put("custid", custId);
	       header.put("ipAddress", LendingConstants.IP_ADDRESS);
		
		//Body
		Map <String,Object> body = new HashMap<String,Object>();
		body.put("workflowOperation", "CREATE_LEAD");
		body.put("mobile", consumerNumber);
		
			//solutionAdditionalInfo
			body.put("PRODUCT_ID", "25");
			body.put("PRODUCT_TYPE", "PL");
			body.put("FLOW_TYPE", "RISK");
			body.put("LOAN_OFFER_ID", loanOfferID);
			body.put("PRODUCT_VERSION", "1");
			body.put("BASE_ID", baseID);
			body.put("LENDER_ID", "5");
			body.put("WHITELISTING_SOURCE", "RISK");
			body.put("IS_EMANDATE_ELIGIBLE", "true");
			body.put("IS_PAYTM_VINTAGE_OLDER_THAN_90D", "true");
			body.put("LENDING_DYNAMIC_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
			body.put("LENDING_DYNAMIC_SECONDARY_TNC", LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
			body.put("IS_FATHER_NAME_REQUIRED", "true");
			body.put("MARITAL_STATUS", "NOT_KNOWN");
			body.put("IS_BRE3_REQUIRED", "true");
			body.put("PINCODE", "600024");
		
			//leadAdditionalInfo
			body.put("IS_LIS_ADDRESS_UPDATE_REQUIRED", "FALSE");
			body.put("IS_KYC_DATA_PRE_FILLING_SUPPORTED", "FALSE");
			body.put("IS_BASIC_DETAILS_PRE_FILLING_SUPPORTED", "FALSE");
			
		requestBodyJsonPath="MerchantService/V1/workflow/lead/CreatePLMigerationHeroLeadRequest.json";
	
		responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, header, body,requestBodyJsonPath );
		if (responseObject.getStatusCode() == 201) {
			LOGGER.info("baseResponseCode : " + responseObject.jsonPath().getString("baseResponseCode"));
			Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			leadId = responseObject.jsonPath().getString("leadId");
			Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
	        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
	        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
	        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
	        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
	        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
	        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"5");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"25");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_EMANDATE_ELIGIBLE"),"true");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_TNC);
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),LendingConstants.PL_V3_HERO_LENDING_DYNAMIC_SECONDARY_TNC);
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_FATHER_NAME_REQUIRED"),"true");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MARITAL_STATUS"),"NOT_KNOWN");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.IS_BRE3_REQUIRED"),"true");
	        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PINCODE"),"600024");
		}
   
	}
	
	@Test(description="Verify the PL v3 Hero lead data using fetch Stratgey ALL_DATA",groups = {"Regression"},dependsOnMethods = "TC003_PLv3Hero_CreateLead")
	@Owner(emailId = "<EMAIL>")
    public void TC004_PLV3Hero_FetchLeadAllData() throws JSONException
    {
	  
	  for(int i=0;i<15;i++)
	  {
	   
	  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
	  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
		  break;
	  
	  }
	  
	  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.SOLUTION_TYPE_LEVEL2_HERO,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
     
	  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
	  {
    	 LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
    	 Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
	      
    	 LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
    	 Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
	      
    	 LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
    	 Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
        
    	 Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
    	 Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
       	 Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
     }

      	
     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());

    }
	
	
	@Test(description="Update lead basic details",groups = {"Regression"},dependsOnMethods = "TC004_PLV3Hero_FetchLeadAllData")
	@Owner(emailId = "<EMAIL>")
	public void TC005_PLV3Hero_PAN_DEDUPE_FAILED() {
		Map<String,String> queryParams=new HashMap<String,String>();
		queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
		queryParams.put("solutionTypeLevel2", LendingConstants.SOLUTION_TYPE_LEVEL2_HERO);
  	 
		Map<String,String> headers=new HashMap<String,String>();
		token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
	    headers = LendingBaseAPI.setHeadersReceivedFromFE();
	    headers.put("Authorization", token);
	    headers.put("Content-Type", "application/json");
	    headers.put("custId", custId);
	    headers.put("ipAddress", LendingConstants.IP_ADDRESS);
	       
	    Map<String,Object> body=new HashMap<String,Object>();
	    body.put("workflowOperation","BASIC_DETAILS");
	    body.put("DOB", LendingConstants.DOB_PLv3_HERO);
	    body.put("PAN", "**********");
	    body.put("EMAIL", Utilities.randomEmailGeneration());
	    body.put("GENDER","Female");
	    body.put("STATIC_TNC_SETNAME", "personalloan_oclconsent_hero");
	    body.put("LENDER_STATIC_TNC_SETNAME", "pl_hero_ckyc_consent");
	    body.put("PINCODE", "600024");
	
	
	    requestBodyJsonPath="MerchantService/V1/workflow/lead/BasicDetailUpdatePersonalLoanMigrationRequest.json";
	   

	    for(int i=0;i<2;i++)
	    	{
	    	responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
	    	if(responseObject.getStatusCode()==200)
	    	break;
	    	}
	    if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_REJECTED.getStage()))
		  {
			LOGGER.info("baseResponseCode : " +responseObject.jsonPath().getString("baseResponseCode"));
	       Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
	        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
	        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
	        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.PAN_DEDUPE_FAILED.getStage());
	        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_REJECTED.getStage());
	        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"545");
	       
	}
	
	
	

	}
}
