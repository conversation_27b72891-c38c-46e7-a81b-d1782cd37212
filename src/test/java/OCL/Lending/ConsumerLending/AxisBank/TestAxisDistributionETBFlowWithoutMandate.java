package OCL.Lending.ConsumerLending.AxisBank;

import java.util.HashMap;
import java.util.Map;

//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;

import OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import io.restassured.response.Response;

public class TestAxisDistributionETBFlowWithoutMandate  extends BaseMethod{


	private static final Logger LOGGER = LogManager.getLogger(TestAxisDistributionETBFlowWithoutMandate.class);
	oAuthServices oAuthServicesObject = new oAuthServices();
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	Utilities UtilitiesObject = new Utilities();
	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();

		
		 String sessionToken = "";
		 String leadId="";
		 String custId="1701474494";
		 String consumerNumber="8888701036";
		 String consumerPassword="paytm@123";
		 String token="";
		 String stage="";
		 String feStage="";
		 String userIPAddress="";
		 String loanUserLatitude="";
		 String loanUserLongitude="";
		 String tncAdditionalParam="";
		 String staticTncAcceptanceTimeStamp="";
		 String lenderCustomerId="";
		 String requestBodyJsonPath="";	
		 String Pan="**********";
		 String Email="";
		 String DOB="";
		 String applicationId="";
		 String bureauRequest="";
		 String bureauResponse="";
		 String bureauCreditState="";
		 String breCreditScore="";
		 String PanValidationTimeStamp="";
		 String uuid="";
		 String md5="";
		 String code="";
		 String tncName="";
		 String url="";
		 String uniqueIdentifier="";
	;
		 String codeSanctionLetter="";
		 String tncNameSanctionLetter="";
		 String urlSanctionLetter="";
		 String uniqueIdentifierSanctionLetter="";
		 String md5SanctionLetter="";
		 String sanctionLetterAcceptanceTimestamp="";
		 String kybSecondaryTNCDisplayURL="";
		 String loanAgreementDate="";
		 String kybTNCDisplayURL="";
		 String panNameMatchTimeStamp="";
		 String panNameMatchPercentage="";
		 String breLastFetchDate="";
		 String loanTenure="";
		String loanAmount="";
		String loanEquatedMonthlyInstallment="";
		String loanRateofInterest="";
		String loanInterestAmount="";
		String loanProcessingFeeRate="";
		String loanDisbursalAmount="";
		String stampDutyCharges="";
		String brokerPeriodInterest="";
		
		 private String randomBankAccountNumber="";
		 private static final boolean MOCK_BRE1 = false;
	
		private static final boolean MOCK_LIS_CREATE_APPLICATION = true;
		private static final boolean MOCK_LENDER_BRE = true;
		private static final boolean MOCK_LOAN_ONBOARDING = true;
		private static final boolean MOCK_LOAN_DISBURSEMENT = true;

		 Response responseObject= null;
		private String lenderApplicationId;
	
	
		
			 
		
		 
		@BeforeClass()
		 public void intitializeInputData() {
		
			LOGGER.info(" Before Suite Method for Consumer Login ");
			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
			LOGGER.info("Applicant Token for Lending : " + sessionToken);
		   
		}
		
		
		
		@Test(description = "Verify whether there is any existing PL Axis distribution",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC001_FetchLeadDetails_PL_AXIS()
		    {
		
	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200)
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
		      
		       leadId=responseObject.jsonPath().getString("leadId");
		     }
		      
		     if(responseObject.jsonPath().getInt("statusCode")==404)
		     {
		    	
			      
			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
			      
		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
		      
		    
		     }
		
		    }
		
		
		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDetails_PL_AXIS",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC002_DeleteExistingLead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();

			  queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
				queryParams.put("leadId",leadId);
				queryParams.put("custId", custId);

			  queryParams.put("leadId",leadId);
			  
			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
				
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("custId", custId);
			  
		   
			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
		    }
		 
		
	
		
		 @Test(description = "Create MCA V3 ABFL Lead",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC003_Create_PL_AXIS_DISTRIBUTION_Lead()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.AXIS_DISTRIBUTION);
	 
			
	    	  Map<String,String> headers=new HashMap<String,String>();
	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custid", custId);
		       headers.put("ipAddress", "************");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","CREATE_LEAD");
		  	   body.put("mobile", "9004083236");
		  	   body.put("PRODUCT_ID", "109");
		  	   body.put("PRODUCT_TYPE", "PL");
		  	   body.put("FLOW_TYPE","DISTRIBUTION");
			   body.put("MARITAL_STATUS", "DISTRIBUTION");
			   body.put("PRODUCT_VERSION", "1");
			   body.put("LOAN_OFFER_ID", "PL_AXIS_OE_Distribution_687e56f5");
			   body.put("BASE_ID", "PL_AXIS_OE_Distribution_687e56f5");
			   body.put("WHITELISTING_SOURCE", "RISK");
			   body.put("LENDING_DYNAMIC_SECONDARY_TNC", "axis_kfs");
		  	   body.put("LENDER_NAME", "AXIS");
		  	   body.put("BUREAU_PRIORITY", "EXPERIAN");
			   body.put("LENDER_ID","12");
		  	 
			   
			 
			   requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/CreateLeadRequestforAxis.json";
		  	  
		  	  
			   
			
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==201)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),"9004083236");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"109");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"PL_AXIS_OE_Distribution_687e56f5");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"PL_AXIS_OE_Distribution_687e56f5");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"axis_kfs");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"AXIS");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"),"EXPERIAN");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"12");
		        
			        leadId=responseObject.jsonPath().getString("leadId");
			        custId=responseObject.jsonPath().getString("custId");
			     
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
			  
		    }
		 
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_Create_PL_AXIS_DISTRIBUTION_Lead",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC004_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
		    
		
		    }
		  
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC005_UpdateLeadBasicDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
			
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       headers.put("ipAddress","***************");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","BASIC_DETAILS");
		  	   body.put("DOB", "1973-01-01");
		  	   body.put("PAN", Pan);
		  	   body.put("EMAIL", "<EMAIL>");
		  	   body.put("STATIC_TNC_SETNAME", "axis_platform_tnc");
		  	   body.put("LENDER_STATIC_TNC_SETNAME", "axis_bd_tnc");
		  	   body.put("IS_FIELD_EDITED", "FALSE");
		  	   body.put("PAN_DOB_VALIDATED", "FALSE");
				body.put("F_NAME", "TOUCH");
				body.put("M_NAME", "WOOD");
				body.put("L_NAME", "LIMITED");
				body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
				body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");


				requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/BasicDetailsForAxisRequest.json";
		   

				 for(int i=0;i<4;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
				        
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_ACCEPTANCE_IP"),"***************");
				       Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),"Nimesh WOOD Hande");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),"Nimesh");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),"WOOD");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),"Hande");
				       
				        
				        leadId=responseObject.jsonPath().getString("leadId");
				        custId=responseObject.jsonPath().getString("custId");
				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
				        PanValidationTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");
				    
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
				  
			    
		     
			  
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC005_UpdateLeadBasicDetails",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC006_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
		     {
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);

		        lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
		        
		     }
	  
		      	
		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
		    
		
		    }
		  
		  
		  
		  @Test(description = "Update lead basic details",dependsOnMethods = "TC006_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC007_UpdateOccupationDetails()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
	    	 
			  Map<String,String> headers=new HashMap<String,String>();
	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("Authorization", token);
		       headers.put("Content-Type", "application/json");
		       headers.put("custId", custId);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       body.put("workflowOperation","OCCUPATION_DETAILS");
		  	   body.put("OCCUPATION", "Salaried");
		  	   body.put("GENDER", "FEMALE");
		  	   body.put("ANNUAL_INCOME", "400000");
		  	   body.put("EMPLOYER_ID", "1000");
		  	   body.put("EMPLOYER_NAME", "test");
		  	   body.put("line1", "A-03 saket");
		  	   body.put("line2", "148 Civil Lines");
		  	   body.put("line3", "near Axis Bank");
		  	   body.put("city", "Bareilly");
		  	   body.put("state", "Uttar Pradesh");
		  	  body.put("pincode", "243001");
		  	  body.put("addressType", "RESIDENTIAL");
		  	  body.put("addressSubType", "CORRESPONDENCE");
		      requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/OccupationDetailsRequest.json";
		   

				 for(int i=0;i<2;i++)
				 {
					 
					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
					 
					  if(responseObject.getStatusCode()==200)
					   break;
				 }
					  
		
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.OCCUPATION_DETAILS.getStage()))
					  {
						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"260");
				        
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION"),"Salaried");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"FEMALE");
     			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.ANNUAL_INCOME"),"400000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMPLOYER_ID"),"1000");
				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMPLOYER_NAME"),"test");
				        
				      }
				  
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.OCCUPATION_DETAILS.getStage());
				  
			 
		    }
		  
		  @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC007_UpdateOccupationDetails", groups = { "Regression" })
			@Owner(emailId = "<EMAIL>", isAutomated = true)
			public void TC008_UpdateBureauDataSetInSAI() {
			Map<String, String> queryParams = new HashMap<String, String>();
			 queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
	     	  queryParams.put("solutionTypeLevel2",LendingConstants.AXIS_DISTRIBUTION);
	    
			
			
			token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization", token);
			headers.put("Content-Type", "application/json");
			headers.put("custId", custId);
			
			Map<String, Object> body = new HashMap<String, Object>();
			body.put("workflowMovementRequired", "false");
			body.put("F_NAME", "Nimesh");
			body.put("L_NAME", "Hande");
			body.put("PAN", "**********");
			body.put("DOB", "1981-11-21");
			body.put("EMAIL", "<EMAIL>");
			
		    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
			
			lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
			
			Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
			Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.OCCUPATION_DETAILS.getStage());

			
			}
		  
		  @Test(description = "Fetch CIR",dependsOnMethods = "TC008_UpdateBureauDataSetInSAI",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC009_FetchCIR_Async()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
			  queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
	    	 
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       
		       String requestjsonpath = "MerchantService/V2/lending/lead/fetchCIR/FetchCIRRequest.json";
		   
		
			   responseObject= lendingBaseClassObject.v1InitiateBureau(queryParams, headers,body,requestjsonpath);
			 	 for(int i=0;i<5;i++)
					  {
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BUREAU_INITIATED.getStage()))
				  
				  break;
			  
			  }
		 	 
		 	
				LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BUREAU_INITIATED.getStage());
			    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BUREAU_INITIATED.getStage());
		        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
		        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"BUREAU_INITIATED");
		        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
		       
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"862");

		        
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC009_FetchCIR_Async",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC010_FetchLeadAllData_AfterBureauSuccess() throws JSONException
		    {
			  
			  for(int i=0;i<55;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(LendingConstants.IS_MOCK_CALLBACK && MOCK_BRE1 &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BRE1_REQUESTED.getStage()))
		     {
		    	
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BRE1_REQUESTED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"705");
				        
				        //Hit BRE1 Callback
				        
				        LOGGER.info("Actual Callback not coming from risk so hitting BRE1 mock Callback");
				        
				    	String offerDetails= "{\"baseId\":\"PL_AXIS_shivangi_QA1_b9e0bb0a\",\"bureauKicker\":false,\"bureauThick\":0,\"field_investigation_needed\":false,\"flow\":\"RISK\",\"isBSAOffer\":false,\"isBre2Required\":false,\"isPaytmActiveUser\":true,\"isPaytmVintage\":true,\"loanDownGradable\":false,\"loan_offered\":true,\"newOfferGenerated\":true,\"offerId\":\"PL_AXIS_shivangi_QA1_b9e0bb0a\",\"paytmThick\":0,\"productId\":\"109\",\"productVersion\":1,\"skipMandate\":false,\"sourceOfWhitelist\":\"RISK\"}";
				        
				        responseObject=    lendingBaseClassObject. BRE1CallbackGeneric (leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.AXIS_DISTRIBUTION,custId,offerDetails);
				        
				        
		            }
				  
				    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"438");
			  		 
		
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC010_FetchLeadAllData_AfterBureauSuccess",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC011_FetchLeadAllData_BRE1Success() throws JSONException
		    {
			  
			  for(int i=0;i<55;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_CREATE_APPLICATION_SUCCESS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(LendingConstants.IS_MOCK_CALLBACK && MOCK_LIS_CREATE_APPLICATION &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage()))
		     {
		    	
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LIS_CREATE_APPLICATION_INITIATED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"438");
				        
				        //Hit LIS Create application Callback
				        
				        LOGGER.info("Actual Callback not coming from risk so hitting LIS  mock Callback");
				        
				      
				        responseObject=    lendingBaseClassObject. lisCreateApplicationAtLenderEnd (leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.AXIS_DISTRIBUTION,custId,"LENDER_APPLICATION_CREATED");
				        
				        
		            }
		     
		     for(int i=0;i<55;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_BRE_SUCCESS.getStage()))
				  break;
			  
			  }
		     
            
		     
		     if(LendingConstants.IS_MOCK_CALLBACK && MOCK_LENDER_BRE &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_BRE_INITIATED.getStage()))
		     {
		    	
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_BRE_INITIATED.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_IN_PROGRESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"442");
				        
				        //Hit Lender BRE Callback
				        
				        LOGGER.info("Actual Callback not coming from risk so hitting LIS  mock Callback");
				        
				      
				        responseObject=    lendingBaseClassObject. lenderBRECallback (leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.AXIS_DISTRIBUTION,custId,"LENDER_BRE_SUCCESS");
				        
				        
		            }
		     
		   
			  
				  
				    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_BRE_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BRE_COMPLETED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"247");
			     

 			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_BRE_SUCCESS"),"TRUE");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.USER_TYPE"),"ETB");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_APPLICATION_CREATED"),"TRUE");
			  		 
		
		    }
		  
		  @Test(description = "Verify Loan Offer Accepted",groups={"Regression"},dependsOnMethods = "TC011_FetchLeadAllData_BRE1Success")
			@Owner(emailId = "<EMAIL>")
			public void TC012_LoanOfferAccept() {
				 Map<String,String> queryParams=new HashMap<String,String>();
					
				 queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
				 queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
				 
				 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
					
				 Map<String, String> headers = new HashMap<String, String>();
				 headers.put("Authorization", token);
				 headers.put("Content-Type", "application/json");
				 headers.put("custId", custId);
			       
			     Map<String,Object> body=new HashMap<String,Object>();
			     body.put("workflowOperation", "OFFER_ACCEPTED");
			     
			     //solutionAdditionalInfo
			     body.put("LOAN_TENURE", "15");
			     body.put("LOAN_TENURE_UNIT", "MONTH");
			     body.put("LOAN_AMOUNT_IN_NUMBER", "50000");
			     body.put("LOAN_AMOUNT_IN_WORDS", "Fifty Thousand");
			     body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "3724");
			     body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT_IN_WORDS", "Three thousand seven hundred  twenty four");
			     body.put("LOAN_RATE_OF_INTEREST", "17");
			     body.put("LOAN_INTEREST_AMOUNT", "48800");
			     body.put("LOAN_PROCESSING_FEE", "1000");
			     body.put("PROCESSING_FEE_RATE", "2.0");
			     body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1180");
			     body.put("LOAN_DISBURSAL_AMOUNT", "48800");
			     body.put("STAMP_DUTY_CHARGES", "20.00");
			     body.put("BROKEN_PERIOD_INTEREST", "0");
			     body.put("OFFER_ACCEPTANCE_TIMESTAMP", "1671667200000");
			     body.put("FIRST_EMI_DATE", "2023-09-10");
		
			     
			     requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/LoanOfferAcceptanceRequest.json";
				   
			     responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
				    	
			     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage()))
			     {
			    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
				    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_OFFER_ACCEPTED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"367");
			        loanTenure=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE");
			        loanAmount=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER");
			        loanEquatedMonthlyInstallment=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT");
			        loanRateofInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST");
			        loanInterestAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_INTEREST_AMOUNT");
			        loanProcessingFeeRate= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE");
			        loanDisbursalAmount= responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT");
			        stampDutyCharges= responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES");
			        brokerPeriodInterest= responseObject.jsonPath().getString("solutionAdditionalInfo.BROKEN_PERIOD_INTEREST");
				  }
			     
			     lenderApplicationId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_APPLICATION_ID");
			     }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC012_LoanOfferAccept",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC013_FetchLeadAllData() throws JSONException
		    {
			  
			  for(int i=0;i<15;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.KYC_IN_PROGRESS.getStage()))
				  break;
			  
			  }
			  
	         
		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			      
			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
			      
		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
		        
		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
		       
		
		    }
		 
		  
		  @Test(description = "Initiate KYC for user",dependsOnMethods = "TC013_FetchLeadAllData",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC014_InitiateKYCForETBUser()
		    {
			  token = lendingBaseClassObject.generateJwtTokenWithoutClientId(LendingConstants.ISSUER, custId,LendingConstants.KYC_SECRET);
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("customerId", custId);
		       headers.put("leadId", leadId);
		       headers.put("productId", "109");
		       headers.put("client", "OE");
		       headers.put("Content-Type", "application/json");
		       headers.put("Authorization", token);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       
		       body.put("USER_TYPE", "ETB");
		       body.put("LENDER_APPLICATION_ID", lenderApplicationId);
		       body.put("isKycReinitiated", "false");
		      
		       
		       String requestjsonpath = "MerchantService/V1/workflow/lead/Axis/ETBKYCInitiateRequest.json";
		   
		
			   responseObject= lendingBaseClassObject.initiateKYCForETBUserAxisBank(headers,body,requestjsonpath);
			 
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("status").equals("INITIATED") ) 
			  
			  Assert.assertEquals(responseObject.jsonPath().getString("action"),"OTP_REQUIRED");
			  
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
		       
			  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.KYC_IN_PROGRESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"396");
			  
			
				  
				  
		    }
		  
		  
		
		  @Test(description = "Submit KYC for user",dependsOnMethods = "TC014_InitiateKYCForETBUser",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC015_SubmitKYCForETBUser()
		    {
			  token = lendingBaseClassObject.generateJwtTokenWithoutClientId(LendingConstants.ISSUER, custId,LendingConstants.KYC_SECRET);
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("customerId", custId);
		       headers.put("leadId", leadId);
		       headers.put("productId", "109");
		       headers.put("client", "OE");
		       headers.put("Content-Type", "application/json");
		       headers.put("Authorization", token);
		       
		       Map<String,Object> body=new HashMap<String,Object>();
		       
		       String requestjsonpath = "MerchantService/V1/workflow/lead/Axis/ETBKYCSubmitRequest.json";
		       body.put("action", "OTP_CAPTURED");
		       body.put("otp", "123456");
		       body.put("deviceInfo", "122345");
		       body.put("ipAddress", "**************");
		      
			   responseObject= lendingBaseClassObject.submitKYCForETBUserAxisBank(headers,body,requestjsonpath);
			
			   
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("status").equals("IN_PROGRESS") ) 
			  {
				  
				    Assert.assertEquals(responseObject.jsonPath().getString("status"), "IN_PROGRESS") ;
			  }
			  
			  responseObject=    lendingBaseClassObject. KYCCallbackusingDigilocker (leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.DIY_P4B_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.AXIS_DISTRIBUTION,custId);
		        
			
				  
				  
		    }
		  
		  @Test(description = "Status KYC for user",dependsOnMethods = "TC014_InitiateKYCForETBUser",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC016_KYCStatusForETBUser()
		    {
			  token = lendingBaseClassObject.generateJwtTokenWithoutClientId(LendingConstants.ISSUER, custId,LendingConstants.KYC_SECRET);
			   Map<String,String> headers=new HashMap<String,String>();
		       headers.put("customerId", custId);
		       headers.put("leadId", leadId);
		       headers.put("productId", "109");
		       headers.put("client", "OE");
		       headers.put("Content-Type", "application/json");
		       headers.put("Authorization", token);
		       
		       for(int i=0;i<35;i++)
				  {
				   
		    	   responseObject= lendingBaseClassObject.getStatusKYCForETBUserAxisBank(headers);
				
				 
				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("status").equals("SUCCESS"))
					  break;
				  
				  }
		       
		       
		      
		      
			   
			  
			  
				  
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC016_KYCStatusForETBUser",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC017_FetchLead() throws JSONException
		    {
			
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			   
				Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SKIPPED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SKIPPED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"816");
		  }

		  
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC017_FetchLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC018_EmandateCallback() throws JSONException
//		    {
//			  
//			  for(int i=0;i<15;i++)
//			  {
//			   
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//			
//			 
//			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.EMANDATE_REQUIRED.getStage()))
//				  break;
//			  
//			  }
//			  
//	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//		     
//		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.EMANDATE_REQUIRED.getStage()))
//		     {
//		    	
//				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//					      
//					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//					      
//				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//				       
//				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_REQUIRED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"815");
//				        
//				        //Hit E-mandate Callback
//				        Map<String,String> queryParams=new HashMap<String,String>();
//						
//				        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//						 queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
//						 
//						 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
//							
//						 Map<String, String> headers = new HashMap<String, String>();
//						 headers.put("Authorization", token);
//						 headers.put("Content-Type", "application/json");
//						 headers.put("custId", custId);
//					       
//					     Map<String,Object> body=new HashMap<String,Object>();
//					     body.put("workflowOperation", "EMANDATE_SUCCESS");
//					     
//					     //solutionAdditionalInfo
//					     body.put("EMANDATE_REF_ID", "YUYIU");
//					     body.put("EMANDATE_STATIC_TNC_SETNAME", "axis_mandate_tnc");
//					     body.put("EMANDATE_STATIC_TNC_ACCEPTANCE_TIMESTAMP", "125153");
//					     body.put("EMANDATE_STATIC_TNC_VERSION", "1");
//					     body.put("EMANDATE_STATIC_TNC_ACCEPTANCE_IP", "********");
//					     body.put("EMANDATE_STATIC_TNC_DEVICE_ID", "1222");
//					     body.put("bankName", "city bank");
//					     body.put("bankAccountNumber", "***********");
//					     body.put("ifsc", "CITI0000002");
//					     body.put("bankAccountHolderName", "Shivangi Goswami");
//					     body.put("status", "0");
//					     body.put("skipBankDetails", "false");
//					     body.put("impsNotSupported", "false");
//					     
//					     requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/E-mandateCallbackRequest.json";
//						   
//					     responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//						    	
//		     }
//		     
//		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.EMANDATE_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"557");
//		  		
//		  
//		    
//		     
//		    }
		  
		     @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC017_FetchLead", groups = { "Regression" })
				@Owner(emailId = "<EMAIL>", isAutomated = true)
				public void TC019_UpdatePanOnLead() {
				Map<String, String> queryParams = new HashMap<String, String>();
				 queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
				  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
		    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
		     	  queryParams.put("solutionTypeLevel2",LendingConstants.AXIS_DISTRIBUTION);
		    
				
				
				token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
				
				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization", token);
				headers.put("Content-Type", "application/json");
				headers.put("custId", custId);
				
				Map<String, Object> body = new HashMap<String, Object>();
				body.put("workflowMovementRequired", "false");
				body.put("F_NAME", "Nimesh");
				body.put("L_NAME", "Hande");
				body.put("PAN", "**********");
				body.put("DOB", "1981-11-21");
				body.put("EMAIL", "<EMAIL>");
				
			    responseObject = lendingBaseClassObject.updateBureauDataSetInSAI(queryParams, headers, body,true);
				
				lendingBaseClassObject.verifyResponseCodeAs200OK(responseObject);
				
				Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"), "Data present for customer");
				Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.EMANDATE_SKIPPED.getStage());

				
				}

		  
		  @Test(description = "Fetch Dynamic T and C",groups = {"Regression"},dependsOnMethods = "TC019_UpdatePanOnLead")
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC020_GenerateSanctionLetter()
		    {
		 Map<String,String> queryParams=new HashMap<String,String>();
		  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
		 queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
		 queryParams.put("tncType", "LOAN_SANCTION_TNC");
		   				 			  
		  Map<String,String> headers=new HashMap<String,String>();
		  headers.put("session_token",sessionToken);
     
	   
	  
	  	
	 	LOGGER.info("Status Code : " +responseObject.getStatusCode());
	 	
	 	for(int i=0;i<4;i++)
		  {
	 		
	 		 Response responseObject= lendingBaseClassObject.fetchDynamicTnc(queryParams, headers);
		   
	 	if(responseObject.jsonPath().getString("meta.status").equals("success"))
	 	{
	 	
	 	codeSanctionLetter=responseObject.jsonPath().getString("data.state.code");
	 	tncNameSanctionLetter=responseObject.jsonPath().getString("data.state.tncName");
	 	urlSanctionLetter=responseObject.jsonPath().getString("data.state.url");
	 	uniqueIdentifierSanctionLetter=responseObject.jsonPath().getString("data.state.uniqueIdentifier");
	 	md5SanctionLetter=responseObject.jsonPath().getString("data.state.md5");
	 	
	 	break;
	 	}
		  
	 	
		  }
	 	
		    }
		  
		
		  @Test(description = "Accept Loan Agreement",dependsOnMethods = "TC020_GenerateSanctionLetter",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC021_AcceptLoanAgreement()
		    {
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);	 
		      queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
	
				   	
			  
	    	  Map<String,String> headers=new HashMap<String,String>();
		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
		       headers.put("session_token", sessionToken);
		       headers.put("Content-Type", "application/json");
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("workflowOperation","LOAN_AGREEMENT_ACCEPTED");
		  	   body.put("LENDING_DYNAMIC_SECONDARY_TNC", tncNameSanctionLetter);
		  	   body.put("KYB_SECONDARY_TNC_REF_NO", uniqueIdentifierSanctionLetter);
		  	   body.put("SECONDARY_ACCEPTED_TNC", md5SanctionLetter);
		  	   body.put("SECONDARY_ACCEPTED_TNC_VERSION", 2);
		  	   body.put("REVIEW_PAGE_STATIC_TNC_SETNAME", "axis_review_page_tnc");
		  	   body.put("REVIEW_PAGE_ETB_STATIC_TNC_SETNAME", "axis_rp_etb_tnc");
		  	 body.put("LOAN_PROCESSING_FEES_GSTINCLUDED", "1180");
		  	 body.put("LOAN_PROCESSING_FEE", "1000");
		  	 body.put("LOAN_PROCESSING_FEE_GST", "180");
		  	 body.put("FIRST_EMI_DATE", "2023-09-10");
		  	 body.put("LOAN_AMOUNT_IN_NUMBER", "50000");
		  	 body.put("LOAN_TOTAL_INTEREST_AMOUNT", "7290");
		  	 body.put("STAMP_DUTY_CHARGES", "20");
		  	 body.put("LOAN_DISBURSAL_AMOUNT", "48800");
		  	 body.put("TOTAL_AMOUNT_PAID_BY_BORROWER", "57290");
		  	 body.put("EAPR", "24.89");
		  	 body.put("LOAN_TENURE", "15");
			 body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "3819");
		  	 
		  
		  	  
			   
		  	 requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/LoanAgreementAcceptRequest.json";
			 
			 for(int i=0;i<2;i++)
			 {
				 
				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body,requestBodyJsonPath);
				 
				  if(responseObject.getStatusCode()==200)
				   break;
			 }
				  
	
			  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
				  {
					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_AGREEMENT_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"353");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.REVIEW_PAGE_STATIC_TNC_SETNAME"),"axis_review_page_tnc");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.REVIEW_PAGE_ETB_STATIC_TNC_SETNAME"),"axis_rp_etb_tnc");
			     
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEES_GSTINCLUDED"),"1180");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE"),"1000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_PROCESSING_FEE_GST"),"180");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FIRST_EMI_DATE"),"2023-09-10");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT_IN_NUMBER"),"50000");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TOTAL_INTEREST_AMOUNT"),"7290");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STAMP_DUTY_CHARGES"),"20");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"48800");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TOTAL_AMOUNT_PAID_BY_BORROWER"),"57290");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EAPR"),"24.89");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"15");
			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_EQUATED_MONTHLY_INSTALLMENT"),"3819");
			        
			        sanctionLetterAcceptanceTimestamp=responseObject.jsonPath().getString("SANCTION_LETTER_TNC_ACCEPTANCE_TIMESTAMP");
			        kybSecondaryTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_SECONDARY_TNC_DISPLAY_URL");
			        loanAgreementDate=responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AGREEMENT_DATE");
			        kybTNCDisplayURL=responseObject.jsonPath().getString("solutionAdditionalInfo.KYB_TNC_DISPLAY_URL");
			     
			    
			    
			      }
			  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.INITIATE_LENDER_SUBMIT.getStage());
			  
		    }
		  

		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC021_AcceptLoanAgreement",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC022_CheckLoanOnboardingCallback() throws JSONException
		    {
			  
			  for(int i=0;i<25;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(LendingConstants.IS_MOCK_CALLBACK && MOCK_LOAN_ONBOARDING &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
		     {
		    	
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"234");
				  		   
				        //Hit LoanOnboarding Callback
				        Map<String,String> queryParams=new HashMap<String,String>();
						
				        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						 queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
						 
						 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
							
						 Map<String, String> headers = new HashMap<String, String>();
						 headers.put("Authorization", token);
						 headers.put("Content-Type", "application/json");
						 headers.put("custId", custId);
					       
					     Map<String,Object> body=new HashMap<String,Object>();
					     body.put("workflowOperation", "LOAN_ONBOARDING_SUCCESS");
					     
					     //solutionAdditionalInfo
					     body.put("LOAN_RATE_OF_INTEREST", "17.0");
					     body.put("LENDER_LOAN_ACCOUNT_NUMBER", "DPTPL100336510A");
					     body.put("LOAN_TENURE", "15");
					     body.put("LOAN_TENURE_UNIT", "MONTH");
					     body.put("LOAN_AMOUNT_IN_NUMBER", "50000");
					     body.put("LOAN_DISBURSAL_AMOUNT", "48800");
					 
					     
					     requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/LoanOnboardingSuccessCallbackRequest.json";
						   
					     responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
						    	
					     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
					     {
				  
				    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"290");
			  		 
		
		               }
	     
		     }      
		            
				  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"290");
		  			 
		
		    }
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC022_CheckLoanOnboardingCallback",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC023_CheckLoanDisbursementCallback() throws JSONException
		    {
			  
			  for(int i=0;i<25;i++)
			  {
			   
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			
			 
			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
				  break;
			  
			  }
			  
	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
		     
		     if(LendingConstants.IS_MOCK_CALLBACK && MOCK_LOAN_DISBURSEMENT &&responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage()))
		     {
		    	
				    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
					    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
					      
					    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
					      
				       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
				       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
				       
				       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ONBOARDING_SUCCESS.getStage());
				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"290");
				  		   
				        //Hit LoanODisbursement Callback
				        Map<String,String> queryParams=new HashMap<String,String>();
						
				        queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
						 queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);
						 
						 token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId,LendingConstants.LMS_SECRET);
							
						 Map<String, String> headers = new HashMap<String, String>();
						 headers.put("Authorization", token);
						 headers.put("Content-Type", "application/json");
						 headers.put("custId", custId);
					       
					     Map<String,Object> body=new HashMap<String,Object>();
					     body.put("workflowOperation", "LOAN_DISBURSED");
					     
					     //solutionAdditionalInfo
					     body.put("LOAN_UTR_NO", "OEMock1111");
					     body.put("LOAN_ACCOUNT_CREATED_ON", "2023-09-01");
					     body.put("LOAN_DISBURSAL_DATE", "2023-09-01");
					     body.put("LOAN_DISBURSED", "TRUE");
		
					 
					     
					     requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/LoanDisbursalCallbackRequest.json";
						   
					     responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
						    	
					     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
					     {
				  
				    Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"237");
			  		 
		
		               }
	     
		     }      
		            
				  
		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"237");
		  			 
		
		    }
		  
		  
		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC023_CheckLoanDisbursementCallback",groups = {"Regression"})
		  @Owner(emailId = "<EMAIL>",isAutomated = true)
		    public void TC024_FetchLeadAllData() throws JSONException
		    {
			  for(int i=0;i<35;i++)
			  {
			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
			 
			  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
			  {
				  break;
			  }
			  
			  }	  
		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
		    }
		  
		  

}
