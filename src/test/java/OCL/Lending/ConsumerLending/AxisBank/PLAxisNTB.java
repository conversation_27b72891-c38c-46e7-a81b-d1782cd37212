package OCL.Lending.ConsumerLending.AxisBank;

import OCL.Lending.ConsumerLending.TataCapital.TestPLTataCapitalUsingSBP;
import Services.LendingService.LendingBaseAPI;
import Services.LendingService.LendingConstants;
import Services.LendingService.LendingLeadStages;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.awaitility.Awaitility;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class PLAxisNTB extends BaseMethod {


    private static final Logger LOGGER = LogManager.getLogger(TestPLTataCapitalUsingSBP.class);
    oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
    LendingBaseAPI lendingBaseClassObject = new LendingBaseAPI();


    String sessionToken = "";
    String leadId = "";
    String custId = "1701474494";
    String consumerNumber = "8888701036";
    String consumerPassword = "paytm@123";
    String token = "";
    String stage = "";
    String feStage = "";
    String userIPAddress = "";
    String loanUserLatitude = "";
    String loanUserLongitude = "";
    String tncAdditionalParam = "";
    String staticTncAcceptanceTimeStamp = "";
    String lenderCustomerId = "";
    String requestBodyJsonPath = "";
    String Pan = "**********";
    String Email = "";
    String DOB = "";
    String applicationId = "";
    String bureauRequest = "";
    String bureauResponse = "";
    String bureauCreditState = "";
    String breCreditScore = "";
    String PanValidationTimeStamp = "";
    String uuid = "";
    String md5 = "";
    String code = "";
    String tncName = "";
    String url = "";
    String uniqueIdentifier = "";
    ;
    String codeSanctionLetter = "";
    String tncNameSanctionLetter = "";
    String urlSanctionLetter = "";
    String uniqueIdentifierSanctionLetter = "";
    String md5SanctionLetter = "";
    String sanctionLetterAcceptanceTimestamp = "";
    String kybSecondaryTNCDisplayURL = "";
    String loanAgreementDate = "";
    String kybTNCDisplayURL = "";
    String panNameMatchTimeStamp = "";
    String panNameMatchPercentage = "";
    String breLastFetchDate = "";
    String loanTenure = "";
    String loanAmount = "";
    String loanEquatedMonthlyInstallment = "";
    String loanRateofInterest = "";
    String loanInterestAmount = "";
    String loanProcessingFeeRate = "";
    String loanDisbursalAmount = "";
    String stampDutyCharges = "";
    String brokerPeriodInterest = "";

    private String randomBankAccountNumber = "";
    private static final boolean MOCK_BRE1 = false;

    private static final boolean MOCK_LIS_CREATE_APPLICATION = false;
    private static final boolean MOCK_LENDER_BRE = false;
    private static final boolean MOCK_LOAN_ONBOARDING = true;
    private static final boolean MOCK_LOAN_DISBURSEMENT = true;

    Response responseObject = null;
    private String lenderApplicationId;


    @BeforeClass()
    public void intitializeInputData() {

        LOGGER.info(" Before Suite Method for Consumer Login ");
        sessionToken = ApplicantToken(consumerNumber, consumerPassword);
        LOGGER.info("Applicant Token for Lending : " + sessionToken);

    }


    @Test(description = "Verify whether there is any existing PL Axis distribution", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_FetchLeadDetails_PL_AXIS() {

        responseObject = lendingBaseClassObject.fetchLeadDetails("", LendingConstants.PL_V3_SOLUTION, LendingConstants.AXIS_DISTRIBUTION, LendingConstants.INDIVIDUAL_ENTITY_TYPE, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS, sessionToken, custId);
        if (responseObject.jsonPath().getInt("statusCode") == 200) {
            leadId = responseObject.jsonPath().getString("data.leadId");
            LOGGER.info("Lead Id for PL Axis : " + leadId);

            LOGGER.info("Verify Status " + responseObject.jsonPath().getString("status"));
            Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");

            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("baseResponseCode"));
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "FETCH_LEAD_EXISTS_SUCCESS");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));

            leadId = responseObject.jsonPath().getString("leadId");
        }

        if (responseObject.jsonPath().getInt("statusCode") == 404) {


            LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("stage"));
            Assert.assertEquals(responseObject.jsonPath().getString("stage"), "LEAD_NOT_PRESENT");

            LOGGER.info("StatusCode : " + responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));


        }

    }

    @Test(description = "Reset the existing lead of the number", dependsOnMethods = "TC001_FetchLeadDetails_PL_AXIS", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_DeleteExistingLead() {
        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("solution", LendingConstants.PL_V3_SOLUTION);
        queryParams.put("leadId", leadId);
        queryParams.put("custId", custId);

        queryParams.put("leadId", leadId);

        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LMS_CLIENT_ID, custId, LendingConstants.LMS_SECRET);

    }
    @Test(description = "Create MCA V3 ABFL Lead",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_Create_PL_AXIS_DISTRIBUTION_Lead()
    {
        Map<String,String> queryParams=new HashMap<String,String>();

        queryParams.put("solution",LendingConstants.PL_V3_SOLUTION);
        queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
        queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
        queryParams.put("solutionTypeLevel2",LendingConstants.AXIS_DISTRIBUTION);


        Map<String,String> headers=new HashMap<String,String>();
        token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custid", custId);
        headers.put("ipAddress", "************");

        Map<String,Object> body = new HashMap<String, Object>();
        body.put("workflowOperation","CREATE_LEAD");
        body.put("mobile", "9004083236");
        body.put("PRODUCT_ID", "109");
        body.put("PRODUCT_TYPE", "PL");
        body.put("FLOW_TYPE","DISTRIBUTION");
        body.put("MARITAL_STATUS", "DISTRIBUTION");
        body.put("PRODUCT_VERSION", "1");
        body.put("LOAN_OFFER_ID", "PL_AXIS_OE_Distribution_687e56f5");
        body.put("BASE_ID", "PL_AXIS_OE_Distribution_687e56f5");
        body.put("WHITELISTING_SOURCE", "RISK");
        body.put("LENDING_DYNAMIC_SECONDARY_TNC", "axis_kfs");
        body.put("LENDER_NAME", "AXIS");
        body.put("BUREAU_PRIORITY", "EXPERIAN");
        body.put("LENDER_ID","12");
      requestBodyJsonPath="MerchantService/V1/workflow/lead/Axis/CreateLeadRequestforAxis.json";

        Map<String, String> finalHeaders = headers;
        Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1,TimeUnit.SECONDS).until(() ->
        {
            responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, finalHeaders, body, requestBodyJsonPath);
            final boolean status = responseObject.getStatusCode()==201;
            return status;
        });



        if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
        {
            LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
            Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
            Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
            Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
            Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
            Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
            Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
            Assert.assertEquals(responseObject.jsonPath().getString("mobile"),"9004083236");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"109");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_OFFER_ID"),"PL_AXIS_OE_Distribution_687e56f5");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"PL_AXIS_OE_Distribution_687e56f5");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDING_DYNAMIC_SECONDARY_TNC"),"axis_kfs");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"AXIS");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BUREAU_PRIORITY"),"EXPERIAN");
            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"12");

            leadId=responseObject.jsonPath().getString("leadId");
            custId=responseObject.jsonPath().getString("custId");


        }

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());

    }

    @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC003_Create_PL_AXIS_DISTRIBUTION_Lead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_FetchLeadAllData() throws JSONException
    {

    responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_V3_SOLUTION,LendingConstants.AXIS_DISTRIBUTION,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
    if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
    {
        LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");

        LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");

        LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));

        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
    }

    Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());


}

@Test(description = "Update lead basic details",dependsOnMethods = "TC004_FetchLeadAllData",groups = {"Regression"})
@Owner(emailId = "<EMAIL>",isAutomated = true)
public void TC005_UpdateLeadBasicDetails() {

// create a map for query parameters
    Map<String, String> queryParams = new HashMap<String, String>();

// call  method  to set common query parameters
    queryParams = LendingBaseAPI.setcommonQueryParameters(leadId, LendingConstants.PL_V3_SOLUTION, LendingConstants.PAYTM_APP_CHANNEL, LendingConstants.INDIVIDUAL_ENTITY_TYPE);
    queryParams.put("solutionTypeLevel2", LendingConstants.AXIS_DISTRIBUTION);


    Map<String, String> headers = new HashMap<String, String>();
    token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);
    headers = LendingBaseAPI.setHeadersReceivedFromFE();
    headers.put("Authorization", token);
    headers.put("Content-Type", "application/json");
    headers.put("custId", custId);
    headers.put("ipAddress", "***************");

    Map<String, Object> body = new HashMap<String, Object>();
    body.put("workflowOperation", "BASIC_DETAILS");
    body.put("DOB", "1973-01-01");
    body.put("PAN", Pan);
    body.put("EMAIL", "<EMAIL>");
    body.put("STATIC_TNC_SETNAME", "axis_platform_tnc");
    body.put("LENDER_STATIC_TNC_SETNAME", "axis_bd_tnc");
    body.put("IS_FIELD_EDITED", "FALSE");
    body.put("PAN_DOB_VALIDATED", "FALSE");
    body.put("F_NAME", "TOUCH");
    body.put("M_NAME", "WOOD");
    body.put("L_NAME", "LIMITED");
    body.put("NSDL_NAME", "TOUCH WOOD LIMITED");
    body.put("PAN_VALIDATION_TIMESTAMP", "1697703898");


    requestBodyJsonPath = "MerchantService/V1/workflow/lead/Axis/BasicDetailsForAxisRequest.json";

    Map<String, String> finalHeaders = headers;
    //craete awaitility method
    Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(1, TimeUnit.SECONDS).until(() ->
    {
        responseObject = lendingBaseClassObject.v1WorkflowLeadStashfin(finalHeaders, finalHeaders, body, requestBodyJsonPath);
        final boolean status = responseObject.getStatusCode() == 200;
        return status;
    });


    if (responseObject.getStatusCode() == 200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage())) {
        LOGGER.info("displayMessage : " + responseObject.jsonPath().getString("displayMessage"));
        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"), "200");
        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"), "LEAD_UPDATED");
        Assert.assertEquals(responseObject.jsonPath().getString("status"), "SUCCESS");
        Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("feStage"), LendingLeadStages.BASIC_DETAILS.getStage());
        Assert.assertEquals(responseObject.jsonPath().getString("stageId"), "144");

        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_STATIC_TNC_ACCEPTANCE_IP"), "***************");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"), "Nimesh WOOD Hande");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"), "Nimesh");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"), "WOOD");
        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"), "Hande");


        leadId = responseObject.jsonPath().getString("leadId");
        custId = responseObject.jsonPath().getString("custId");
        userIPAddress = responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
        staticTncAcceptanceTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
        Email = responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
        DOB = responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
        Pan = responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
        PanValidationTimeStamp = responseObject.jsonPath().getString("solutionAdditionalInfo.PAN_VALIDATION_TIMESTAMP");

    }

    Assert.assertEquals(responseObject.jsonPath().getString("stage"), LendingLeadStages.BASIC_DETAILS.getStage());



}

    @Test(description = "Update Bureau Pull data set in SAI Table", dependsOnMethods = "TC007_UpdateOccupationDetails", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_UpdateBureauDataSetInSAI() {

        //create method to set query parametersAND  headers for data update in SAI table
        Map<String, String> queryParams = new HashMap<String, String>();
        Map<String, String> headers = new HashMap<String, String>();
        Map<String, Object> body = new HashMap<String, Object>();
        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);
        headers = LendingBaseAPI.setHeadersReceivedFromFE();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        headers.put("custId", custId);


        token = lendingBaseClassObject.generateJwtToken(LendingConstants.ISSUER, LendingConstants.LENDING_BFF, custId, LendingConstants.LENDING_BFF_SECRET);






    }



}
