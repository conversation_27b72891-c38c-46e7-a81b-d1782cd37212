//package OCL.Lending.ConsumerLending;
//
//import Services.LendingService.LendingBaseAPI;
//import Services.LendingService.LendingConstants;
//import Services.LendingService.LendingLeadStages;
//import Services.MechantService.MiddlewareServices;
//import Services.Utilities.Utilities;
//import Services.oAuth.oAuthServices;
//import com.goldengate.common.BaseMethod;
//import com.paytm.apitools.util.annotations.Owner;
//import com.paytm.framework.reporting.Reporter;
//import io.restassured.response.Response;
//import org.apache.log4j.Logger;
//import org.json.JSONException;
//import org.testng.Assert;
//import org.testng.annotations.BeforeClass;
//import org.testng.annotations.Test;
//
//import java.util.HashMap;
//import java.util.Map;
//
//public class TestStashfinNegativeCases extends BaseMethod{
//
//	private static final Logger LOGGER = Logger.getLogger(TestStashfinNegativeCases.class);
//	oAuthServices oAuthServicesObject = new oAuthServices();
//	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
//	Utilities UtilitiesObject = new Utilities();
//	LendingBaseAPI lendingBaseClassObject=new LendingBaseAPI();
//	Utilities utility=new Utilities();
//
//		 String sessionToken = "";
//		 String leadId="";
//		 String custId="1000650218";
//		 String consumerNumber="7772226001";
//		 String consumerPassword="paytm@123";
//		 String token="";
//		 String stage="";
//		 String feStage="";
//		 String userIPAddress="";
//		 String loanUserLatitude="";
//		 String loanUserLongitude="";
//		 String tncAdditionalParam="";
//		 String staticTncAcceptanceTimeStamp="";
//		 String lenderCustomerId="";
//		 String applicationId="";
//		 String requestBodyJsonPath="";
//		 String Pan="";
//		 String Email="";
//		 String DOB="";
//
//		 Response responseObject= null;
//
//
//
//		@BeforeClass()
//		 public void intitializeInputData() {
//
//			LOGGER.info(" Before Suite Method for Consumer Login ");
//			sessionToken = ApplicantToken(consumerNumber, consumerPassword);
//			LOGGER.info("Applicant Token for Lending : " + sessionToken);
//			lendingBaseClassObject.createTokenForStashfinLender();
//			lendingBaseClassObject.resetLenderLead(consumerNumber);
//
//		}
//
//
//
//		@Test(description = "Verify whether there is any existing stashfin lead present or not",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC001_FetchLeadDeatils()
//		    {
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails("",LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_CHECK_LEAD_EXISTS,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200)
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_EXISTS_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead exists for customer."));
//
//		       leadId=responseObject.jsonPath().getString("leadId");
//		     }
//
//		     if(responseObject.jsonPath().getInt("statusCode")==404)
//		     {
//
//
//			  LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("stage"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),"LEAD_NOT_PRESENT");
//
//		      LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		      Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data Not present for customer"));
//
//
//		     }
//
//		    }
//
//
//		 @Test(description = "Reset the existing lead of the number",dependsOnMethods = "TC001_FetchLeadDeatils",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC002_DeleteExistingLead()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//			  queryParams.put("leadId",leadId);
//
//			 token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_QA, custId,LendingConstants.LENDING_QA_SECRET);
//
//			Map<String, String> headers = new HashMap<String, String>();
//			headers.put("Authorization", token);
//			headers.put("custId", custId);
//
//
//			lendingBaseClassObject.resetLendingLeads(queryParams, headers);
//		    }
//
//
//
//
//		 @Test(description = "Create Stashfin Lead without passing mobile number which is a mandatory field",dependsOnMethods = "TC002_DeleteExistingLead",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC003_CreateStashfinLead_WithoutPassingMobileNumber()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile","" );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		  	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"Mobile number is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//
//		 @Test(description = "Create Stashfin Lead without passing Static tnc setname which is a mandatory field",dependsOnMethods = "TC003_CreateStashfinLead_WithoutPassingMobileNumber",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC004_CreateStashfinLead_WithoutPassingStaticTNCSetName()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		  	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//			   for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"STATIC_TNC_SETNAME is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing Obligation which is a mandatory field",dependsOnMethods = "TC004_CreateStashfinLead_WithoutPassingStaticTNCSetName",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC005_CreateStashfinLead_WithoutPassingObligation()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		  	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"OBLIGATION is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing Product Type which is a mandatory field",dependsOnMethods = "TC005_CreateStashfinLead_WithoutPassingObligation",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC006_CreateStashfinLead_WithoutPassingProductType()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		  	   body.put("PRODUCT_TYPE","");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"PRODUCT_TYPE is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing Flow Type which is a mandatory field",dependsOnMethods = "TC006_CreateStashfinLead_WithoutPassingProductType",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC007_CreateStashfinLead_WithoutPassingFlowType()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		 	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"FLOW_TYPE is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing Lender Id which is a mandatory field",dependsOnMethods = "TC007_CreateStashfinLead_WithoutPassingFlowType",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC008_CreateStashfinLead_WithoutPassingLenderId()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		 	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"LENDER_ID is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing Base Id which is a mandatory field",dependsOnMethods = "TC008_CreateStashfinLead_WithoutPassingLenderId",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC009_CreateStashfinLead_WithoutPassingBaseId()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		 	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"BASE_ID is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing Offer Id which is a mandatory field",dependsOnMethods = "TC009_CreateStashfinLead_WithoutPassingBaseId",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC010_CreateStashfinLead_WithoutPassingOfferId()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		 	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"OFFER_ID is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing User Ip address which is a mandatory field",dependsOnMethods = "TC010_CreateStashfinLead_WithoutPassingOfferId",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC011_CreateStashfinLead_WithoutPassingUserIPAddress()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		 	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"USER_IP_ADDRESS is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing User location latitude which is a mandatory field",dependsOnMethods = "TC011_CreateStashfinLead_WithoutPassingUserIPAddress",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC012_CreateStashfinLead_WithoutPassingLocationLatitude()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//	;
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//		       headers.replace("latitude", "");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		 	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"LOAN_USER_LATITUDE is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead without passing User location longitude which is a mandatory field",dependsOnMethods = "TC012_CreateStashfinLead_WithoutPassingLocationLatitude",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC013_CreateStashfinLead_WithoutPassingLocationLongitude()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//		       headers.replace("longitude", "");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile",consumerNumber );
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		 	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//				   break;
//			 }
//
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//					//LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertEquals(responseObject.jsonPath().getString("internalMessage"),"LOAN_USER_LONGITUDE is null or empty");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//		    }
//
//		 @Test(description = "Create Stashfin Lead with all deatils",dependsOnMethods = "TC013_CreateStashfinLead_WithoutPassingLocationLongitude",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC014_CreateStashfinLead_HappyCase()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		  	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==201)
//				   break;
//			 }
//
//
//			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
//				  {
//					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead created successfully."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
//			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_CREATED");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
//			        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_SETNAME"),"distribution_ocl_consent");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_ID"),"104");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_NAME"),"Stashfin");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_TYPE"),"PL");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MIN"),"6");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_MAX"),"36");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_END_DATE"),"2021-12-31");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_START_DATE"),"2021-05-26");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_ID"),"Stashfin_1000650218");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.WHITELISTING_SOURCE"),"RISK");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OFFER_CREATED_AT"),"2021-07-16T12:51:42.492+05:30");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.BASE_ID"),"Stashfin_1000650218");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_ID"),"1000004");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PRODUCT_VERSION"),"1");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.RISK_SEGMENT"),"A1");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.FLOW_TYPE"),"DISTRIBUTION");
//			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OBLIGATION"),"50000");
//
//			        leadId=responseObject.jsonPath().getString("leadId");
//			        custId=responseObject.jsonPath().getString("custId");
//			        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
//			        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
//
//
//
//			      }
//
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//
//		    }
//		 @Test(description = "Create Stashfin Lead when one lead already exists",dependsOnMethods = "TC014_CreateStashfinLead_HappyCase",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC015_CreateStashfinLead_WhenOneLeadAlreadyExists()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams.put("solution",LendingConstants.PL_DISTRIBUTION_STASHFIN);
//			  queryParams.put("entityType",LendingConstants.INDIVIDUAL_ENTITY_TYPE );
//	    	  queryParams.put("channel",LendingConstants.PAYTM_APP_CHANNEL);
//	     	  queryParams.put("solutionTypeLevel2",LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	  token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custid", custId);
//		       headers.put("ipAddress", "************");
//
//		       Map<String,Object> body = new HashMap<String, Object>();
//		  	   body.put("workflowOperation","CREATE_LEAD");
//		  	   body.put("mobile", consumerNumber);
//		  	   body.put("STATIC_TNC_SETNAME", "distribution_ocl_consent");
//		  	   body.put("LENDER_ID", "104");
//		  	   body.put("LENDER_NAME", "Stashfin");
//		  	   body.put("PRODUCT_TYPE","PL");
//		  	   body.put("LOAN_TENURE_MIN", "6");
//		  	   body.put("LOAN_TENURE_MAX", "36");
//		  	   body.put("OFFER_END_DATE", "2021-12-31");
//		  	   body.put("OFFER_START_DATE", "2021-05-26");
//		  	   body.put("OFFER_ID", "Stashfin_1000650218");
//		  	   body.put("WHITELISTING_SOURCE", "RISK");
//		  	   body.put("OFFER_CREATED_AT", "2021-07-16T12:51:42.492+05:30");
//			   body.put("BASE_ID", "Stashfin_1000650218");
//			   body.put("PRODUCT_ID", "1000004");
//			   body.put("PRODUCT_VERSION", "1");
//			   body.put("RISK_SEGMENT", "A1");
//			   body.put("FLOW_TYPE", "DISTRIBUTION");
//			   body.put("OBLIGATION", "50000");
//
//			   requestBodyJsonPath="MerchantService/v1/workflow/Lead/StashfinLeadCreationRequest.json";
//
//
//
//
//
//			 for(int i=0;i<2;i++)
//			 {
//
//				 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==200)
//				   break;
//			 }
//
//
//			  if(responseObject.getStatusCode()==201 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_CREATED.getStage()))
//				  {
//					LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("We already have an application for you in our system. Please contact us from 24*7 help section on the app."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"201");
//			        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_ALREADY_EXISTS");
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LEAD_CREATED.getStage());
//			        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"112");
//			        Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
//
//			      }
//
//		        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_CREATED.getStage());
//
//		    }
//		 @Test(description = "Update lead basic details when Pan is empty",dependsOnMethods = "TC015_CreateStashfinLead_WhenOneLeadAlreadyExists",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC016_UpdateLeadBasicDetails_PassPanAsEmpty()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", "");
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Salaried");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("PAN number is null or empty"));
//			        //Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("PAN number is null or empty "));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//
//		 @Test(description = "Update lead basic details Pass incorrect Pan",dependsOnMethods = "TC016_UpdateLeadBasicDetails_PassPanAsEmpty",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC017_UpdateLeadBasicDetails_PassIncorrectPan()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", "**********");
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Salaried");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//				responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//						 if(responseObject.getStatusCode()==400)
//						 {
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid PAN number"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//						 }
//		    }
//
//
//		 @Test(description = "Update lead basic details when DOB is empty",dependsOnMethods = "TC017_UpdateLeadBasicDetails_PassIncorrectPan",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC018_UpdateLeadBasicDetails_PassDOBAsEmpty()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", "");
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Self-Employed");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("DOB is null or empty"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//		 @Test(description = "Update lead basic details when DOB format is incorrect",dependsOnMethods = "TC018_UpdateLeadBasicDetails_PassDOBAsEmpty",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC019_UpdateLeadBasicDetails_PassIncorrectDOBFormat()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", "25-02-1995");
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Self-Employed");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid Dob"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//		 @Test(description = "Update lead basic details when Email format is incorrect",dependsOnMethods = "TC019_UpdateLeadBasicDetails_PassIncorrectDOBFormat",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC020_UpdateLeadBasicDetails_PassIncorrectEmailFormat()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", "shivangi#");
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Self-Employed");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid Email"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//		 @Test(description = "Update lead basic details when Email is empty",dependsOnMethods = "TC020_UpdateLeadBasicDetails_PassIncorrectEmailFormat",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC021_UpdateLeadBasicDetails_PassEmailAsEmpty()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", "");
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Self-Employed");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("EMAIL is null or empty"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//
//		 @Test(description = "Update lead basic details when Gender is empty",dependsOnMethods = "TC021_UpdateLeadBasicDetails_PassEmailAsEmpty",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC022_UpdateLeadBasicDetails_PassGenderAsEmpty()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "");
//		  	   body.put("OCCUPATION", "Self-Employed");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Gender is null or empty"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//		 @Test(description = "Update lead basic details when Gender is invalid anything apart from male and female",dependsOnMethods = "TC022_UpdateLeadBasicDetails_PassGenderAsEmpty",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC023_UpdateLeadBasicDetails_PassInvalidGender()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Test");
//		  	   body.put("OCCUPATION", "Self-Employed");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid Gender"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//		 @Test(description = "Update lead basic details when occupation is empty",dependsOnMethods = "TC023_UpdateLeadBasicDetails_PassInvalidGender",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC024_UpdateLeadBasicDetails_WhenOccupationIsEmpty()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			       // Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("OCCUPATION is null or empty"));
//			      //  Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//		 @Test(description = "Update lead basic details when pincode is invalid",dependsOnMethods = "TC024_UpdateLeadBasicDetails_WhenOccupationIsEmpty",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC025_UpdateLeadBasicDetails_WhenPincodeIsInvalid()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Male");
//		  	   body.put("OCCUPATION", "self-employed");
//		  	   body.put("PINCODE", "40010");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		     requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//			responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					 if(responseObject.getStatusCode()==400)
//					 {
//
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//			        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//			        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//			        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//			        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Invalid pincode"));
//			        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//				    }
//
//		    }
//
//		 @Test(description = "Update lead basic details",dependsOnMethods = "TC025_UpdateLeadBasicDetails_WhenPincodeIsInvalid",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC026_UpdateLeadBasicDetails()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Salaried");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Lead updated successfully."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.BASIC_DETAILS.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"144");
//				        Assert.assertEquals(responseObject.jsonPath().getString("mobile"),consumerNumber);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.TNC_ADDITIONAL_PARAM"),"STATIC_LOAN_OFFER_TNC");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LEAD_CREATION_DEVICE_MANUFACTURER"),LendingConstants.deviceManufacturer);
//    			        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.GENDER"),"Female");
//   			            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.OCCUPATION"),"Salaried");
//   			            Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.MONTHLY_INCOME"),"100000");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LONGITUDE"),LendingConstants.LONGITUDE);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_USER_LATITUDE"),LendingConstants.LATITUDE);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.NSDL_NAME"),LendingConstants.NSDL_NAME);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.F_NAME"),LendingConstants.F_NAME);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.M_NAME"),LendingConstants.M_NAME);
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.L_NAME"),LendingConstants.L_NAME);
//
//				        leadId=responseObject.jsonPath().getString("leadId");
//				        custId=responseObject.jsonPath().getString("custId");
//				        userIPAddress=responseObject.jsonPath().getString("solutionAdditionalInfo.USER_IP_ADDRESS");
//				        staticTncAcceptanceTimeStamp=responseObject.jsonPath().getString("solutionAdditionalInfo.STATIC_TNC_ACCEPTANCE_TIMESTAMP");
//				        Email=responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL");
//				        DOB=responseObject.jsonPath().getString("solutionAdditionalInfo.DOB");
//				        Pan=responseObject.jsonPath().getString("solutionAdditionalInfo.PAN");
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.BASIC_DETAILS.getStage());
//
//		    }
//
//
//
//		 @Test(description = "Update lead basic details",dependsOnMethods = "TC026_UpdateLeadBasicDetails",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC027_UpdateLeadBasicDetails_WhenleadIsAlreadyAtBasicDetailStage()
//		    {
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_BFF, custId,LendingConstants.LENDING_BFF_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","BASIC_DETAILS");
//		  	   body.put("DOB", LendingConstants.DOB_STASHFIN);
//		  	   body.put("PAN", utility.randomIndividualPANValueGenerator());
//		  	   body.put("EMAIL", Utilities.randomEmailGeneration());
//		  	   body.put("PAN_DOB_VALIDATED","true");
//		  	   body.put("GENDER", "Female");
//		  	   body.put("OCCUPATION", "Salaried");
//		  	   body.put("PINCODE", "400101");
//		  	   body.put("MONTHLY_INCOME", "100000");
//
//		  	  requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinBasicDetailsRequest.json";
//
//				responseObject= lendingBaseClassObject.v1WorkflowLeadStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//						 if(responseObject.getStatusCode()==400)
//						 {
//
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Reporter.report.info("displayMessage : " +responseObject.jsonPath().getString("internalMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Oops! Something went wrong. Please try again after some time."));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//				        Assert.assertTrue(responseObject.jsonPath().getString("internalMessage").contains("Unable to find any allowable node Id"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"ERROR");
//
//					    }
//
//			    }
//
//		 @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC027_UpdateLeadBasicDetails_WhenleadIsAlreadyAtBasicDetailStage",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC028_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
//				  break;
//
//			  }
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage()))
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"234");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.EMAIL"),Email);
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.DOB"),DOB);
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PAN"),Pan);
//
//		        lenderCustomerId=responseObject.jsonPath().getString("solutionAdditionalInfo.LENDER_CUSTOMER_ID");
//		        applicationId=responseObject.jsonPath().getString("solutionAdditionalInfo.APPLICATION_ID");
//		     }
//
//
//		     Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LENDER_SUBMIT_SUCCESS.getStage());
//
//
//		    }
//
//
//
//
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC028_FetchLeadAllData",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC029_LISCallbackToLoanAccepted_WithInvalidWorkflowOperation() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED_OFFER_GENERATED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//		      responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//				  if(responseObject.getStatusCode()==400)
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid WorkflowOperation"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//					  }
//
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC029_LISCallbackToLoanAccepted_WithInvalidWorkflowOperation",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC030_LISCallbackToLoanAccepted() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//		      responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"243");
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCEPTED.getStage());
//
//		    }
//
//
//		  @Test(description = "LIS Callback to Loan Accepted-->243 Stage",dependsOnMethods = "TC030_LISCallbackToLoanAccepted",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC031_LISCallbackToLoanAccepted() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCEPTED");
//		  	   body.put("APPLICATION_ID", applicationId);
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLoanOfferGeneratedCallbackRequest.json";
//
//		      responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.BASIC_DETAILS.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Unable to find any allowable node Id"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//                      }
//
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC031_LISCallbackToLoanAccepted",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC032_LISCallbackToLoanSanctioned_WithoutpassingApplicationId() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", "");
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("APPLICATION_ID is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC032_LISCallbackToLoanSanctioned_WithoutpassingApplicationId",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC033_LISCallbackToLoanSanctioned_WithoutpassingLoanAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//			  queryParams.put("solutionTypeLevel2","STASHFIN");
//
//			  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_AMOUNT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC033_LISCallbackToLoanSanctioned_WithoutpassingLoanAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC034_LISCallbackToLoanSanctioned_WithoutpassingLoanTenure() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid value of LOAN_TENURE"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC034_LISCallbackToLoanSanctioned_WithoutpassingLoanTenure",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC035_LISCallbackToLoanSanctioned_WithoutpassingLoanTenureUnit() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_TENURE_UNIT is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC035_LISCallbackToLoanSanctioned_WithoutpassingLoanTenureUnit",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC036_LISCallbackToLoanSanctioned_WithoutpassingLoanRateOfInterestUnit() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_RATE_OF_INTEREST_UNIT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC036_LISCallbackToLoanSanctioned_WithoutpassingLoanRateOfInterestUnit",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC037_LISCallbackToLoanSanctioned_WithoutpassingLoanRateOfInterest() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid value of LOAN_RATE_OF_INTEREST"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC037_LISCallbackToLoanSanctioned_WithoutpassingLoanRateOfInterest",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC038_LISCallbackToLoanSanctioned_WithoutpassingProcessingFeeRate() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("PROCESSING_FEE_RATE is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC038_LISCallbackToLoanSanctioned_WithoutpassingProcessingFeeRate",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC039_LISCallbackToLoanSanctioned_WithoutpassingInstallmentAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("INSTALLMENT_AMOUNT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC039_LISCallbackToLoanSanctioned_WithoutpassingInstallmentAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC040_LISCallbackToLoanSanctioned_WithoutpassingInstallmentFrequency() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("INSTALLMENT_FREQUENCY is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC040_LISCallbackToLoanSanctioned_WithoutpassingInstallmentFrequency",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC041_LISCallbackToLoanSanctioned_WithoutpassingLoanRepaymentAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_REPAYMENT_AMOUNT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC041_LISCallbackToLoanSanctioned_WithoutpassingLoanRepaymentAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC042_LISCallbackToLoanSanctioned_WithoutpassingLoanRepaymentAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid value of LOAN_DISBURSAL_AMOUNT"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC042_LISCallbackToLoanSanctioned_WithoutpassingLoanRepaymentAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC043_LISCallbackToLoanSanctionedFromLoanAccepted() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_SANCTIONED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_SANCTIONED.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"236");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"430799");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"24");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"3.0");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"25438");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"610512");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");
//
//
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_SANCTIONED.getStage());
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC043_LISCallbackToLoanSanctionedFromLoanAccepted",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC044_LISCallbackToLoanDisbursed_WithoutpassingApplicationId() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", "");
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("APPLICATION_ID is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC044_LISCallbackToLoanDisbursed_WithoutpassingApplicationId",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC045_LISCallbackToLoanDisbursed_WithoutpassingLoanAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_AMOUNT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC045_LISCallbackToLoanDisbursed_WithoutpassingLoanAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC046_LISCallbackToLoanDisbursed_WithoutpassingLoanTenure() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid value of LOAN_TENURE"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC046_LISCallbackToLoanDisbursed_WithoutpassingLoanTenure",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC047_LISCallbackToLoanDisbursed_WithoutpassingLoanTenureUnit() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_TENURE_UNIT is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC047_LISCallbackToLoanDisbursed_WithoutpassingLoanTenureUnit",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC048_LISCallbackToLoanDisbursed_WithoutpassingLoanRateOfInterestUnit() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_RATE_OF_INTEREST_UNIT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC048_LISCallbackToLoanDisbursed_WithoutpassingLoanRateOfInterestUnit",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC049_LISCallbackToLoanDisbursed_WithoutpassingLoanRateOfInterest() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid value of LOAN_RATE_OF_INTEREST"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC049_LISCallbackToLoanDisbursed_WithoutpassingLoanRateOfInterest",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC050_LISCallbackToLoanDisbursed_WithoutpassingProcessingFeeRate() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("PROCESSING_FEE_RATE is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC050_LISCallbackToLoanDisbursed_WithoutpassingProcessingFeeRate",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC051_LISCallbackToLoanDisbursed_WithoutpassingInstallmentAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("INSTALLMENT_AMOUNT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC051_LISCallbackToLoanDisbursed_WithoutpassingInstallmentAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC052_LISCallbackToLoanDisbursed_WithoutpassingInstallmentFrequency() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("INSTALLMENT_FREQUENCY is null or empty"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC052_LISCallbackToLoanDisbursed_WithoutpassingInstallmentFrequency",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC053_LISCallbackToLoanDisbursed_WithoutpassingLoanRepaymentAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_REPAYMENT_AMOUNT is null or empty "));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Sanctioned-->236 Stage",dependsOnMethods = "TC053_LISCallbackToLoanDisbursed_WithoutpassingLoanRepaymentAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC054_LISCallbackToLoanDisbursed_WithoutpassingLoanRepaymentAmount() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanSanctionedCallbackRequest.json";
//
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//
//				  if(responseObject.getStatusCode()==400 )
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid value of LOAN_DISBURSAL_AMOUNT"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//				      }
//
//		    }
//
//		  @Test(description = "LIS Callback to Loan Disbursed-->237 Stage",dependsOnMethods = "TC054_LISCallbackToLoanDisbursed_WithoutpassingLoanRepaymentAmount",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC055_LISCallbackToLoanDisbursedFromLoanSanctioned() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LIS, custId,LendingConstants.LENDING_LIS_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_DISBURSED");
//		  	   body.put("APPLICATION_ID", applicationId);
//		  	   body.put("LOAN_AMOUNT", "430799");
//		  	   body.put("LOAN_TENURE", "24");
//		  	   body.put("LOAN_TENURE_UNIT", "MONTHS");
//		  	   body.put("LOAN_RATE_OF_INTEREST_UNIT", "ANNUALLY");
//		  	   body.put("LOAN_RATE_OF_INTEREST", "3.0");
//		  	   body.put("PROCESSING_FEE_RATE", "3.0");
//		  	   body.put("INSTALLMENT_AMOUNT","25438");
//		  	   body.put("INSTALLMENT_FREQUENCY", "MONTHLY");
//		  	   body.put("LOAN_REPAYMENT_AMOUNT", "610512");
//		  	   body.put("LOAN_DISBURSAL_AMOUNT", "417875");
//
//		      requestBodyJsonPath="MerchantService/v1/workflow/Lead/callback/StashfinLoanDisbursedCallbackRequest.json";
//
//
//				 for(int i=0;i<2;i++)
//				 {
//
//					 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//					  if(responseObject.getStatusCode()==200)
//					   break;
//				 }
//
//
//				  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
//					  {
//						LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//				        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//				        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//				        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//				        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
//				        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"237");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"430799");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"24");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"3.0");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"25438");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"610512");
//				        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");
//
//
//				      }
//
//			        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_DISBURSED.getStage());
//
//		    }
//
//
//
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC055_LISCallbackToLoanDisbursedFromLoanSanctioned",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC056_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_ACCOUNT_ACKNOWLEDGED.getStage()))
//				  break;
//
//			  }
//
//	         responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_ALL_DATA,sessionToken,custId);
//
//		     if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("feStage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
//		     {
//		    	LOGGER.info("Verify Status "+responseObject.jsonPath().getString("status"));
//			    Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//
//			    LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("baseResponseCode"));
//		        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"FETCH_LEAD_SUCCESS");
//
//		       LOGGER.info("StatusCode : " +responseObject.jsonPath().getString("displayMessage"));
//		       Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer."));
//
//		       Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_DISBURSED.getStage());
//		        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"239");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_AMOUNT"),"430799");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE"),"24");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_TENURE_UNIT"),"MONTHS");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST_UNIT"),"ANNUALLY");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_RATE_OF_INTEREST"),"3.0");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.PROCESSING_FEE_RATE"),"3.0");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_AMOUNT"),"25438");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.INSTALLMENT_FREQUENCY"),"MONTHLY");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_REPAYMENT_AMOUNT"),"610512");
//		        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");
//
//		     }
//
//
//		   //  Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_ACKNOWLEDGED.getStage());
//
//
//		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC056_FetchLeadAllData",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC057_LMSDataCallback_PassingEmptyLoanAccountNumber() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER", "");
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//		   	   responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//		   	   if(responseObject.getStatusCode()==400 )
//			   	 {
//					    	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_ACCOUNT_NUMBER is null or empty"));
//					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//			   	 }
//
//
//		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC057_LMSDataCallback_PassingEmptyLoanAccountNumber",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC058_LMSDataCallback_PassingEmptyLenderLan() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER",Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//		   	   responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//		   	   if(responseObject.getStatusCode()==400 )
//			   	 {
//					    	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LENDER_LAN is null or empty"));
//					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//			   	 }
//
//
//		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC058_LMSDataCallback_PassingEmptyLenderLan",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC059_LMSDataCallback_PassingEmptyAccountStatus() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER",Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//		   	   responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//		   	   if(responseObject.getStatusCode()==400 )
//			   	 {
//					    	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("ACCOUNT_CREATED_ON is null or empty "));
//					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//			   	 }
//
//
//		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC059_LMSDataCallback_PassingEmptyAccountStatus",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC060_LMSDataCallback_PassingEmptyAccountCreationDate() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER",Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//		   	   responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//		   	   if(responseObject.getStatusCode()==400 )
//			   	 {
//					    	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("LOAN_ACCOUNT_STATUS is null or empty "));
//					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
//
//			   	 }
//
//
//		    }
//
////		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC060_LMSDataCallback_PassingEmptyAccountCreationDate",groups = {"Regression"})
////		  @Owner(emailId = "<EMAIL>",isAutomated = true)
////		    public void TC061_LMSDataCallback_PassingEmpLoanAmount() throws JSONException
////		    {
////
////			  Map<String,String> queryParams=new HashMap<String,String>();
////
////			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
////
////	    	  Map<String,String> headers=new HashMap<String,String>();
////	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
////		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
////		       headers.put("Authorization", token);
////		       headers.put("Content-Type", "application/json");
////		       headers.put("custId", custId);
////
////		       Map<String,Object> body=new HashMap<String,Object>();
////		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
////		  	   body.put("LOAN_ACCOUNT_NUMBER",Utilities.randomLendingLoanAccountNumberGenerator());
////		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
////		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
////		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
////		  	   body.put("LOAN_AMOUNT", "");
////
////
////
////		      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
////		   	   responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
////
////		   	   if(responseObject.getStatusCode()==400 )
////			   	 {
////					    	LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
////					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Invalid value of LOAN_AMOUNT"));
////					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"400");
////
////			   	 }
////
////
////		    }
//
//		  @Test(description = "LMS Callback to move to node 240",dependsOnMethods = "TC060_LMSDataCallback_PassingEmptyAccountCreationDate",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC062_LMSDataCallback() throws JSONException
//		    {
//
//			  Map<String,String> queryParams=new HashMap<String,String>();
//
//			  queryParams=LendingBaseAPI.setcommonQueryParameters(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.INDIVIDUAL_ENTITY_TYPE);
//
//	    	  Map<String,String> headers=new HashMap<String,String>();
//	    	   token = lendingBaseClassObject.generateJwtToken( LendingConstants.ISSUER,  LendingConstants.LENDING_LMS_DISTRIBUTION, custId,LendingConstants.LENDING_LMS_DISTRIBUTION_SECRET);
//		       headers = LendingBaseAPI.setHeadersReceivedFromFE();
//		       headers.put("Authorization", token);
//		       headers.put("Content-Type", "application/json");
//		       headers.put("custId", custId);
//
//		       Map<String,Object> body=new HashMap<String,Object>();
//		       body.put("workflowOperation","LOAN_ACCOUNT_CREATED");
//		  	   body.put("LOAN_ACCOUNT_NUMBER", Utilities.randomLendingLoanAccountNumberGenerator());
//		  	   body.put("LENDER_LAN", "SETL20071BIOD748379");
//		  	   body.put("LOAN_ACCOUNT_STATUS", "ACTIVE");
//		  	   body.put("ACCOUNT_CREATED_ON", "2021-07-19 13:14:29");
//		  	   body.put("LOAN_AMOUNT", 430799);
//
//
//
//			      requestBodyJsonPath="MerchantService/V1/workflow/lead/callback/StashfinLMSDataSubmitCallbackRequest.json";
//
//			      for(int i=0;i<10;i++)
//				  {
//
//				  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//				  if(responseObject.jsonPath().getInt("statusCode")==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
//					  break;
//
//				  }
//
//
//
//
//					  if(responseObject.getStatusCode()==200 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LOAN_DISBURSED.getStage()))
//						  {
//
//						  for(int i=0;i<2;i++)
//							 {
//
//								 responseObject= lendingBaseClassObject.v1WorkflowLeadCallbackStashfin(queryParams, headers,body, requestBodyJsonPath);
//
//								  if(responseObject.getStatusCode()==200)
//								   break;
//							 }
//
//
//							LOGGER.info("displayMessage : " +responseObject.jsonPath().getString("displayMessage"));
//					        Assert.assertTrue(responseObject.jsonPath().getString("displayMessage").contains("Data present for customer"));
//					        Assert.assertEquals(responseObject.jsonPath().getString("statusCode"),"200");
//					        Assert.assertEquals(responseObject.jsonPath().getString("baseResponseCode"),"LEAD_UPDATED");
//					        Assert.assertEquals(responseObject.jsonPath().getString("status"),"SUCCESS");
//					        Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
//					        Assert.assertEquals(responseObject.jsonPath().getString("feStage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
//					        Assert.assertEquals(responseObject.jsonPath().getString("stageId"),"240");
//					        Assert.assertEquals(responseObject.jsonPath().getString("solutionAdditionalInfo.LOAN_DISBURSAL_AMOUNT"),"417875");
//
//
//
//					      }
//
//				     //   Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LOAN_ACCOUNT_CREATED.getStage());
//
//		    }
//
//		  @Test(description = "Verify the lead data using fetch Stratgey ALL_DATA",dependsOnMethods = "TC062_LMSDataCallback",groups = {"Regression"})
//		  @Owner(emailId = "<EMAIL>",isAutomated = true)
//		    public void TC063_FetchLeadAllData() throws JSONException
//		    {
//
//			  for(int i=0;i<15;i++)
//			  {
//
//			  responseObject= lendingBaseClassObject.fetchLeadDetails(leadId,LendingConstants.PL_DISTRIBUTION_STASHFIN,LendingConstants.PL_DISTRIBUTION_SOLUTIONTYPELEVEL2,LendingConstants.INDIVIDUAL_ENTITY_TYPE,LendingConstants.PAYTM_APP_CHANNEL,LendingConstants.FETCH_STRATEGY_BASIC_DATA,sessionToken,custId);
//
//
//			  if(responseObject.jsonPath().getInt("statusCode")==404 && responseObject.jsonPath().getString("stage").equals(LendingLeadStages.LEAD_NOT_PRESENT.getStage()))
//			  {
//				  break;
//			  }
//
//			  }
//
//
//		     Assert.assertEquals(responseObject.jsonPath().getString("stage"),LendingLeadStages.LEAD_NOT_PRESENT.getStage());
//
//
//		    }
//
//
//
//}
