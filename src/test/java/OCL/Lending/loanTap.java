//package OCL.Lending;
//
//import Request.MerchantService.v1.sdMerchant.Lead_fetch;
//import Request.MerchantService.v1.sdMerchant.AdditionalDetails;
//import Request.MerchantService.v1.sdMerchant.Lead_create;
//import Request.MerchantService.v5.callback.lmsCallBack_loanTap;
//import com.auth0.jwt.JWT;
//import com.auth0.jwt.algorithms.Algorithm;
//import com.paytm.apitools.core.P;
//import io.restassured.response.Response;
//import Services.MechantService.MiddlewareServices;
//import com.goldengate.common.BaseMethod;
//import com.paytm.apitools.util.annotations.Owner;
//import org.apache.log4j.Logger;
//import org.testng.Assert;
//import org.testng.annotations.BeforeSuite;
//import org.testng.annotations.BeforeTest;
//import org.testng.annotations.Test;
//
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.util.HashMap;
//import java.util.Map;
//
//
//public class loanTap extends BaseMethod {
//
//
//
//    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
//    private static final Logger LOGGER = Logger.getLogger(loanTap.class);
//
//    public String AgentNo = "6111111137";
//    public static String session_token = "";
//    public String relatedBusinessUuid = "";
//    public String custId = "1000512625";
//    public String token = "";
//    public String MERCHANT_ID = "ZtXuWb27053526903950";
//    public String leadId = "";
//    public String pan = "**********";
//
//
//    @BeforeTest
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void getAgentToken() {
//        LOGGER.info(" Before Suite Method for Agent Login ");
//        session_token = ApplicantToken(AgentNo,"paytm@123");
//        LOGGER.info("Applicant Token for Loan Tap : " + session_token);
//
//    }
//
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "jwt token creation")
//
//    public String generateJwtToken() {
//
//        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of("GMT+05:30"));
//        System.out.println("Date is :" + localDateTime);
//        String ts = localDateTime.toString();
//
//        Algorithm buildAlgorithm = Algorithm.HMAC256("fd61f785-e867-471b-90c6-1447b4331712");
//        token = JWT.create().withIssuer("OE")
//                .withClaim("clientId", "LMS")
//                .withClaim("custId", custId)
//                .withClaim("timestamp", ts + "+05:30").sign(buildAlgorithm);
//        return token;
//    }
//
//
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "fetch lead invalid solution", priority = 1)
//    public void fetchLead_LoanTap_invalidSolution() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", session_token);
//
//
//        Lead_fetch fetchLead_loanTap = new Lead_fetch();
//
//        Response responseObject = middlewareServicesObject.FetchLead(fetchLead_loanTap,queryParams, headers);
//
//        //System.out.println("Response Fields :fetchLeadObject " + fetchLeadObject.toString());
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "fetch lead invalid entityType", priority = 1)
//    public void fetchLead_LoanTap_invalidEntityType() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL1");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", session_token);
//
//
//        Lead_fetch fetchLead_loanTap = new Lead_fetch();
//
//        Response responseObject = middlewareServicesObject.FetchLead(fetchLead_loanTap,queryParams, headers);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "invalid tnc set name in create lead", priority = 2)
//    public void createLead_LoanTap_invalidTNCSetName() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("tnCSetName", "loanStatic");
//        body.put("TNC_ACCEPTED_CODE", "24");
//        body.put("TNC_ACCEPTED_VERSION", "5");
//
//
//        Lead_create createLeadObject_loanTap = new Lead_create(P.TESTDATA.get("LoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(createLeadObject_loanTap, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " tnc accepted version as null in create lead", priority = 2)
//    public void createLead_LoanTap_TNCAcceptedVersionAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("tnCSetName", "loanStaticTnc");
//        body.put("TNC_ACCEPTED_CODE", "24");
//        body.put("TNC_ACCEPTED_VERSION", "");
//
//
//        Lead_create createLeadObject_loanTap = new Lead_create(P.TESTDATA.get("LoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(createLeadObject_loanTap, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " invalid tnc Set name  in create lead", priority = 2)
//    public void createLead_LoanTap_incorrectTNCName() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("tnCSetName", "true");
//        body.put("TNC_ACCEPTED_CODE", "24");
//        body.put("TNC_ACCEPTED_VERSION", "5");
//
//
//        Lead_create createLeadObject_loanTap = new Lead_create(P.TESTDATA.get("LoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(createLeadObject_loanTap, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 500);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " tnc accepted code as null in create lead", priority = 2)
//    public void createLead_LoanTap_TNCAcceptedCodeAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("tnCSetName", "loanStaticTnc");
//        body.put("TNC_ACCEPTED_CODE", "");
//        body.put("TNC_ACCEPTED_VERSION", "5");
//
//
//        Lead_create createLeadObject_loanTap = new Lead_create(P.TESTDATA.get("LoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(createLeadObject_loanTap, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide invalid value for isPartialSave", priority = 3)
//    public void panVerification_LoanTap_invalidValueForIsPartialSave() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("isPartialSave", "true");
//        body.put("workflowSubOperation", "PAN_VERIFIED");
//        body.put("pan", pan);
//
//
//        Lead_create panVerifiedObject_loanTap = new Lead_create(P.TESTDATA.get("PanVerifiedLoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(panVerifiedObject_loanTap, queryParams, headers, body);
//
//        //System.out.println("Response Fields :fetchLeadObject " + fetchLeadObject.toString());
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide invalid value for workflowSuboperation in body", priority = 3)
//    public void panVerification_LoanTap_invalidWorkflowSubOperation() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("isPartialSave", "false");
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("pan", pan);
//
//
//        Lead_create panVerifiedObject_loanTap = new Lead_create(P.TESTDATA.get("PanVerifiedLoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(panVerifiedObject_loanTap, queryParams, headers, body);
//
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide duplicate pan in body", priority = 3)
//    public void panVerification_LoanTap_duplicatePan() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("isPartialSave", "false");
//        body.put("workflowSubOperation", "PAN_VERIFIED");
//        body.put("pan", "**********");
//
//
//        Lead_create panVerifiedObject_loanTap = new Lead_create(P.TESTDATA.get("PanVerifiedLoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(panVerifiedObject_loanTap, queryParams, headers, body);
//
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide invalid workflow suboperation in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_invalidWorkflowSuboperation() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "ADDRESS_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "FEMALE");
//        body.put("DOB", "1989-12-31");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide email as null in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_EmailAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "");
//        body.put("GENDER", "FEMALE");
//        body.put("DOB", "1989-12-31");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide gender as null in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_GenderAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "");
//        body.put("DOB", "1989-12-31");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide incorrect gender in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_incorrectGenderValuePassed() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "FEMALE1");
//        body.put("DOB", "1989-12-31");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide incorrect DOB in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_incorrectDOBValuePassed() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "FEMALE");
//        body.put("DOB", "1989-12-32");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide incorrect gender in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_DOBAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "FEMALE");
//        body.put("DOB", "");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide invalid year in business   in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_invalidYearInBusiness() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "FEMALE");
//        body.put("DOB", "1989-12-31");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9tyh");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide itype of business as null  in personal details", priority = 4)
//    public void updatePersonalDetails_LoanTap_TypeOfBusinessAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "FEMALE");
//        body.put("DOB", "1989-12-31");
//        body.put("TYPE_OF_BUSINESS", "");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide invalid lender product id address details", priority = 5)
//    public void updateAddressDetails_LoanTap_invalidLenderProductId() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "ADDRESS_DETAILS");
//        body.put("shopRelatedBusinessUuid", "e90a26c3-66c4-4b60-a54a-c0ff010aabfb");
//        body.put("LENDER_ID", "23");
//        body.put("LENDER_PRODUCT_ID", "6");
//        body.put("LENDER_NAME", "LoanTap");
//        body.put("BRE_CREDIT_SCORE", "710");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdateAddress"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide invalid lender name in address details", priority = 5)
//    public void updateAddressDetails_LoanTap_invalidLenderName() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "ADDRESS_DETAILS");
//        body.put("shopRelatedBusinessUuid", "e90a26c3-66c4-4b60-a54a-c0ff010aabfb");
//        body.put("LENDER_ID", "23");
//        body.put("LENDER_PRODUCT_ID", "5");
//        body.put("LENDER_NAME", "LoanTap1");
//        body.put("BRE_CREDIT_SCORE", "710");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdateAddress"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide lender name as null in address details", priority = 5)
//    public void updateAddressDetails_LoanTap_LenderNameAsNull() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "ADDRESS_DETAILS");
//        body.put("shopRelatedBusinessUuid", "e90a26c3-66c4-4b60-a54a-c0ff010aabfb");
//        body.put("LENDER_ID", "23");
//        body.put("LENDER_PRODUCT_ID", "5");
//        body.put("LENDER_NAME", "");
//        body.put("BRE_CREDIT_SCORE", "710");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdateAddress"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide  lender product id as null address details", priority = 5)
//    public void updateAddressDetails_LoanTap_LenderProductIdasNull() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "ADDRESS_DETAILS");
//        body.put("shopRelatedBusinessUuid", "e90a26c3-66c4-4b60-a54a-c0ff010aabfb");
//        body.put("LENDER_ID", "23");
//        body.put("LENDER_PRODUCT_ID", "");
//        body.put("LENDER_NAME", "LoanTap");
//        body.put("BRE_CREDIT_SCORE", "710");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdateAddress"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " cust id  as null in address details", priority = 5)
//    public void updateAddressDetails_LoanTap_CustIdasNull() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", "");
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "ADDRESS_DETAILS");
//        body.put("shopRelatedBusinessUuid", "e90a26c3-66c4-4b60-a54a-c0ff010aabfb");
//        body.put("LENDER_ID", "23");
//        body.put("LENDER_PRODUCT_ID", "5");
//        body.put("LENDER_NAME", "LoanTap");
//        body.put("BRE_CREDIT_SCORE", "710");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdateAddress"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//
//        Assert.assertEquals(StatusCode, 401);
//        System.out.println("Status Code is " + StatusCode);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " workflow suboperation as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_workflowSubopertaionAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " shopRelatedBusinessUuid as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_shopRelatedBusinessUuidAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " LoanAmountInNumber as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_LoanAmountInNumberAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " LoanTenure as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_LoanTenureAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " LoanProcessingFee as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_LoanProcessingFeeAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " LOAN_EQUATED_MONTHLY_INSTALLMENT as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_LOAN_EQUATED_MONTHLY_INSTALLMENTAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " LOAN_TENURE_UNIT as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_LOAN_TENURE_UNITAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " MERCHANT_ID as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_MERCHANT_IDAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", "");
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " COMBINATION_ID as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_COMBINATION_IDAsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", "UMP00168704350555437");
//        body.put("COMBINATION_ID", "");
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = " solutionTypeLevel3 as null in loan offer submit", priority = 6)
//    public void loanOffer_LoanTap_solutionTypeLevel3AsNull() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", "a10f9e9b-ff33-47d5-9c8f-05cfa96a7f32");
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", "UMP00168704350555437");
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//
//
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "auth as null in lms callback", priority = 7)
//    public void lmsCallBack_LoanTap_AuthAsNull() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("leadId", "7d1677e2-908d-483e-b2d6-634e50be1f6c");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("channel", "DIY_P4B_APP");
//        headers.put("Authorization", "");
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("status", "DECLINED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject = new lmsCallBack_loanTap();
//        Response responseObject = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 401);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "CustId as null in lms callback", priority = 7)
//    public void lmsCallBack_LoanTap_CustIdAsNull() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("leadId", "7d1677e2-908d-483e-b2d6-634e50be1f6c");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", " ");
//        headers.put("channel", "DIY_P4B_APP");
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("status", "DECLINED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject = new lmsCallBack_loanTap();
//        Response responseObject = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 401);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "LeadId as null in lms callback", priority = 7)
//    public void lmsCallBack_LoanTap_leadIdAsNull() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("leadId", "");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("channel", "DIY_P4B_APP");
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("status", "DECLINED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject = new lmsCallBack_loanTap();
//        Response responseObject = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 404);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "Status body as null in lms callback", priority = 7)
//    public void lmsCallBack_LoanTap_StatusAsNull() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("leadId", "7d1677e2-908d-483e-b2d6-634e50be1f6c");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("channel", "DIY_P4B_APP");
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("status", "");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject = new lmsCallBack_loanTap();
//        Response responseObject = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "Status  as rejected  in lms callback", priority = 7)
//    public void lmsCallBack_LoanTap_StatusAsRejected() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("leadId", "7d1677e2-908d-483e-b2d6-634e50be1f6c");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("channel", "DIY_P4B_APP");
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("status", "REJECTED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject = new lmsCallBack_loanTap();
//        Response responseObject = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 404);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "invalid status in lms callback", priority = 7)
//    public void lmsCallBack_LoanTap_StatusIsInvalid() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("leadId", "7d1677e2-908d-483e-b2d6-634e50be1f6c");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("channel", "DIY_P4B_APP");
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("status", "REJECTED_Done");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject = new lmsCallBack_loanTap();
//        Response responseObject = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject, queryParams, headers, body);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//    }
//
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "positive fetch lead", priority = 8)
//    public void fetchLead_LoanTap() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", session_token);
//
//
//        Lead_fetch fetchLead_loanTap = new Lead_fetch();
//
//        Response responseObject = middlewareServicesObject.FetchLead(fetchLead_loanTap,queryParams, headers);
//        custId= responseObject.jsonPath().getString("custId");
//        System.out.println("Cust Id :"+custId);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        //   fetchLead_loanTap.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/fetchLead_LoanTap/leadResponseSchema.json");
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "positive create lead", priority = 9)
//    public void createLead_LoanTap() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("tnCSetName", "loanStaticTnc");
//        body.put("TNC_ACCEPTED_CODE", "24");
//        body.put("TNC_ACCEPTED_VERSION", "5");
//
//
//        Lead_create createLeadObject_loanTap = new Lead_create(P.TESTDATA.get("LoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(createLeadObject_loanTap, queryParams, headers, body);
//
//        leadId = responseObject.jsonPath().getString("leadId");
//        System.out.println("lead id is : " + leadId);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        createLeadObject_loanTap.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/createLead_LoanTap/leadResponseSchema.json");
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "fetch pan ", priority = 9)
//    public void fetchLead_LoanTap_fetchPan()
//    {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", session_token);
//
//
//        Lead_fetch fetchLead_loanTap = new Lead_fetch();
//
//        Response responseObject = middlewareServicesObject.FetchLead(fetchLead_loanTap,queryParams, headers);
//        pan=responseObject.jsonPath().getJsonObject("business.pan").toString();
//
//        System.out.println("PAN :"+pan);
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        //   fetchLead_loanTap.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/fetchLead_LoanTap/leadResponseSchema.json");
//    }
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "provide correct pan & verify it", priority = 10)
//    public void panVerification_LoanTap() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("Postman-Token", "b925529f-6b8f-4115-9ada-d51dc1cfd014");
//        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("isPartialSave", "false");
//        body.put("workflowSubOperation", "PAN_VERIFIED");
//        body.put("pan", pan);
//
//
//        Lead_create panVerifiedObject_loanTap = new Lead_create(P.TESTDATA.get("PanVerifiedLoanTap"));
//
//        Response responseObject = middlewareServicesObject.CreateLead(panVerifiedObject_loanTap, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        //panVerifiedObject_loanTap.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/panVerified_loanTap/leadResponseSchema.json");
//    }
//
//
//
//
//
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "fetch name as per pan ", priority = 11)
//    public void fetchLead_LoanTap_fetchnameAsPerPan() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", session_token);
//
//
//        Lead_fetch fetchLead_loanTap = new Lead_fetch();
//
//        Response responseObject = middlewareServicesObject.FetchLead(fetchLead_loanTap,queryParams, headers);
//
//        String nameAsPerPan=responseObject.jsonPath().getString("nameAsPerPan");
//
//        if(nameAsPerPan.equals("TOUCH WOOD LIMITED"))
//        {
//            System.out.println("Name As Per Pan is :" +nameAsPerPan);
//        }
//        else
//        {
//            System.out.println("Something went wrong");
//        }
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        //   fetchLead_loanTap.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/fetchLead_LoanTap/leadResponseSchema.json");
//    }
//
//
//
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "update correct personal details", priority = 11)
//    public void updatePersonalDetails_LoanTap() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "PERSONAL_DETAILS");
//        body.put("EMAIL", "<EMAIL>");
//        body.put("GENDER", "FEMALE");
//        body.put("DOB", "1989-12-31");
//        body.put("TYPE_OF_BUSINESS", "foodpanda");
//        body.put("YEARS_IN_BUSINESS", "9");
//
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdatePersonalDetails"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        relatedBusinessUuid = responseObject.jsonPath().getString("relatedBusinessUuid");
//        System.out.println("Related Business Uuid is : " + relatedBusinessUuid);
//
//       // additionalDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/additionalDetails/updatePersonalDetails/leadResponseSchema.json");
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "update correct address details", priority = 12)
//    public void updateAddressDetails_LoanTap() {
//
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "ADDRESS_DETAILS");
//        body.put("shopRelatedBusinessUuid", relatedBusinessUuid);
//        body.put("LENDER_ID", "23");
//        body.put("LENDER_PRODUCT_ID", "5");
//        body.put("LENDER_NAME", "LoanTap");
//        body.put("BRE_CREDIT_SCORE", "710");
//
//        AdditionalDetails additionalDetailsResponseObject = new AdditionalDetails(P.TESTDATA.get("UpdateAddress"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(additionalDetailsResponseObject, queryParams, headers, body);
//
//
//        int StatusCode = responseObject.getStatusCode();
//
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        relatedBusinessUuid = responseObject.jsonPath().getString("relatedBusinessUuid");
//        System.out.println("Related Business Uuid is : " + relatedBusinessUuid);
//       // additionalDetailsResponseObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/additionalDetails/updateAddressDetails/leadResponseSchema.json");
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "validate lender name ", priority = 13)
//    public void fetchLead_LoanTap_ValidateLenderName()
//    {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", session_token);
//
//
//        Lead_fetch fetchLead_loanTap = new Lead_fetch();
//
//        Response responseObject = middlewareServicesObject.FetchLead(fetchLead_loanTap,queryParams, headers);
//
//        String LENDER_NAME=responseObject.jsonPath().getJsonObject("solution.solutionAdditionalInfo.LENDER_NAME").toString();
//
//        if(LENDER_NAME.equals("LoanTap"))
//        {
//            System.out.println("Lender Name :" +LENDER_NAME);
//        }
//        else
//        {
//            System.out.println("Something went wrong");
//        }
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        //   fetchLead_loanTap.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/fetchLead_LoanTap/leadResponseSchema.json");
//    }
//
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "loan offer submit", priority = 13)
//    public void loanOffer_LoanTap() {
//
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//        queryParams.put("solutionTypeLevel3", "LoanTap");
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("session_token", session_token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("workflowSubOperation", "OFFER_DETAILS");
//        body.put("shopRelatedBusinessUuid", relatedBusinessUuid);
//        body.put("LOAN_AMOUNT_IN_NUMBER", "100000");
//        body.put("LOAN_TENURE", "9");
//        body.put("LOAN_PROCESSING_FEE", "500");
//        body.put("LOAN_EQUATED_MONTHLY_INSTALLMENT", "500");
//        body.put("LOAN_TENURE_UNIT", "MONTHLY");
//        body.put("MERCHANT_ID", MERCHANT_ID);
//        body.put("COMBINATION_ID", "5");
//
//
//        AdditionalDetails loanOfferObject = new AdditionalDetails(P.TESTDATA.get("LoanOffer"));
//
//        Response responseObject = middlewareServicesObject.AdditionalDetails(loanOfferObject, queryParams, headers, body);
//
//        //System.out.println("Response Fields :fetchLeadObject " + fetchLeadObject.toString());
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        relatedBusinessUuid = responseObject.jsonPath().getString("relatedBusinessUuid");
//        System.out.println("Related Business Uuid is : " + relatedBusinessUuid);
//        loanOfferObject.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/additionalDetails/loanOffer/leadResponseSchema.json");
//
//        waitForLoad(5000);
//
//
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "lms callback", priority = 14)
//    public void lmsCallBack_LoanTap() {
//        waitForLoad(5000);
//        generateJwtToken();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("leadId", leadId);
//
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
//        headers.put("custId", custId);
//        headers.put("channel", "DIY_P4B_APP");
//        headers.put("Authorization", token);
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("status", "DECLINED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject = new lmsCallBack_loanTap();
//        Response responseObject = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject, queryParams, headers, body);
//
//
//        Map<String, String> body1 = new HashMap<String, String>();
//        body1.put("status", "APPLICATION_SUBMITTED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject1 = new lmsCallBack_loanTap();
//        Response responseObject1 = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject1, queryParams, headers, body1);
//
//        Map<String, String> body2 = new HashMap<String, String>();
//        body2.put("status", "DOCUMENT_VERIFICATION_PENDING");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject2 = new lmsCallBack_loanTap();
//        Response responseObject2 = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject2, queryParams, headers, body2);
//
//
//        Map<String, String> body5 = new HashMap<String, String>();
//        body5.put("status", "DOCUMENT_REJECTED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject5 = new lmsCallBack_loanTap();
//        Response responseObject5 = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject5, queryParams, headers, body5);
//
//
//        Map<String, String> body3 = new HashMap<String, String>();
//        body3.put("status", "DOCUMENT_VERIFIED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject3 = new lmsCallBack_loanTap();
//        Response responseObject3 = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject3, queryParams, headers, body3);
//
//
//        Map<String, String> body4 = new HashMap<String, String>();
//        body4.put("status", "LOAN_DISBURSED");
//        lmsCallBack_loanTap lmsCallBack_loanTapObject4 = new lmsCallBack_loanTap();
//        Response responseObject4 = middlewareServicesObject.lmsCallback_loanTap(lmsCallBack_loanTapObject4, queryParams, headers, body4);
//
//        int StatusCode = responseObject4.getStatusCode();
//
//        Assert.assertEquals(StatusCode, 400);
//        System.out.println("Status Code is " + StatusCode);
//
//        if (StatusCode == 200)
//        {
//            System.out.println("Lead Successfully closed ");
//        }
//
//        lmsCallBack_loanTapObject.validateResponseAgainstJSONSchema("MerchantService.v5/callback/lmsCallBack_loanTap/leadResponseSchema.json");
//    }
//
//
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = "positive fetch lead after lead closed", priority = 15)
//    public void fetchLead_LoanTap_AfterLeadClosed() {
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        queryParams.put("solution", "business_lending");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("channel", "DIY_P4B_APP");
//        queryParams.put("solutionTypeLevel2", "LOAN_MARKETPLACE");
//
//        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("session_token", session_token);
//
//
//        Lead_fetch fetchLead_loanTap = new Lead_fetch();
//
//        Response responseObject = middlewareServicesObject.FetchLead(fetchLead_loanTap,queryParams, headers);
//
//        String lead_Closed=responseObject.jsonPath().getString("stage");
//
//        if(lead_Closed.equals("LEAD_SUCCESSFULLY_CLOSED"))
//        {
//            System.out.println("Lead Successfully Closed");
//        }
//        else
//        {
//            System.out.println("Lead is not Closed");
//        }
//
//        int StatusCode = responseObject.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//        System.out.println("Status Code is " + StatusCode);
//        //   fetchLead_loanTap.validateResponseAgainstJSONSchema("MerchantService/v1/sdMerchant/lead/fetchLead_LoanTap/leadResponseSchema.json");
//    }
//
//}
