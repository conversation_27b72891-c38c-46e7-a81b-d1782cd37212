package OCL.QRCodeDetails;

import Request.QRcode.EditQRCodeDetailsRequest;
import Services.QR.QRServices;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

public class EditQRCodeDetailsTests extends BaseMethod {

    private static final Logger logger = LoggerFactory.getLogger(EditQRCodeDetailsTests.class);
    QRServices QRServicesObj = new QRServices();
    String RequestPath = "QRService/EditQRCodeDetailsRequest.json";

    @Test(priority = 1, groups = {"Regression"}, description = "Update QR Code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void editQRCodeDetailsTest() {
        // Create and set up the request

        EditQRCodeDetailsRequest editQrCode = new EditQRCodeDetailsRequest(P.TESTDATA.get("EditQRDetails"));

        //Setting Headers
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientId", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");


        Map<String,String >body=new HashMap<>();
        body.put("vpa","paytm.us7namhn@pty");
        body.put("operationType","QR_CODE");
         System.out.println(body);
        // Call the API and log response
        Response res = QRServicesObj.EditQRCodeDetailsRequest(editQrCode,headers,body);
        res.prettyPrint();

       System.out.println(res.getStatusCode());
        // Validate response
        Assert.assertEquals(res.getStatusCode(), 200, "Expected status code is 200");
        String editResponseMessage = res.jsonPath().getString("response.editResponse");

        // Assertion
        Assert.assertEquals(editResponseMessage, "QR Code Details updated successfully",
                "Edit response message does not match!");
    }


    @Test(priority = 2, groups = {"Regression"}, description = "VPA is empty String")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void EditQRCodeDetailsTest_VPA_IS_NULL()
    {
        EditQRCodeDetailsRequest editQrCode = new EditQRCodeDetailsRequest(P.TESTDATA.get("EditQRDetails"));

       //setting headers
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientId", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        //Setting VPA as a Empty String
        Map<String,String>body=new HashMap<>();
        body.put("vpa","");
        body.put("operationType","QR_CODE");

        Response res = QRServicesObj.EditQRCodeDetailsRequest(editQrCode,headers,body);
        Assert.assertEquals(res.getStatusCode(), 200, "Expected status code is 200");
        // Extract 'statusMessage' from response
        String actualStatusMessage = res.jsonPath().getString("statusMessage");

        // Assertion
        Assert.assertEquals(actualStatusMessage, "vpa is mandatory",
                "Status message does not match!");

    }

    @Test(priority = 2, groups = {"Regression"}, description = "operationType is empty String")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void EditQRCodeDetailsTest_OperationType_IS_NULL()
    {
        EditQRCodeDetailsRequest editQrCode = new EditQRCodeDetailsRequest(P.TESTDATA.get("EditQRDetails"));

        //setting headers
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientId", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        //Setting VPA as a Empty String
        Map<String,String>body=new HashMap<>();
        body.put("operationType","");

        Response res = QRServicesObj.EditQRCodeDetailsRequest(editQrCode,headers,body);
        Assert.assertEquals(res.getStatusCode(), 400, "Expected status code is 400");
        // Extract 'statusMessage' from response
        String actualStatusMessage = res.jsonPath().getString("error");

        // Assertion
        Assert.assertEquals(actualStatusMessage, "Bad Request",
                "Status message does not match!");
        System.out.println("Validated the EDC QR flow for null case");

    }

    public JsonNode ReadFile(String FilePath) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(FilePath);
            if (inputStream == null) {
                logger.error("Error: File not found - {}", FilePath);
                return null;
            }
            return objectMapper.readTree(inputStream);
        } catch (IOException e) {
            logger.error("Failed to read file: " + FilePath, e);
            return null;
        }
    }


}
