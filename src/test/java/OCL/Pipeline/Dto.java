package OCL.Pipeline;


import com.paytm.apitools.core.AbstractApiV2;
import com.paytm.apitools.util.mysql.DBBase;

    public class Dto {

        private AbstractApiV2 apiObject;
        private String jwtTokenValue;
        private DBBase dbBase =null;


        public String getJwtTokenValue() {
            return jwtTokenValue;
        }

        public void setJwtTokenValue(String jwtTokenValue) {
            this.jwtTokenValue = jwtTokenValue;
        }

        public DBBase getDbBase() {
            return dbBase;
        }

        public void setDbBase(DBBase dbBase) {
            this.dbBase = dbBase;
        }

        public AbstractApiV2 getApiObject() {
            return apiObject;
        }

        public void setApiObject(AbstractApiV2 apiObject) {
            this.apiObject = apiObject;
        }

    }





