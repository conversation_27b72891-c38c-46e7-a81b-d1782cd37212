package OCL.Pipeline;


import io.restassured.response.Response;

import java.util.ArrayList;
import java.util.List;

public class Pipeline {
    private List<Step> pipelineSteps = new ArrayList<>();

    Dto dto;

    public Dto getDto() {
        return dto;
    }

    public void setDto(Dto dto) {
        this.dto = dto;
    }
    public void addStep(Step step) {
        pipelineSteps.add(step);
    }
    public void addStep(List<Step> steps) {
        pipelineSteps=steps;
    }


    public List<Response> execute() throws Exception{
        List<Response> listOfResponses = new ArrayList<>();
        for (Step step : pipelineSteps) {
            Response response =step.execute(dto,dto.getApiObject());
            listOfResponses.add(response);
        }
        return  listOfResponses;
    }
}

