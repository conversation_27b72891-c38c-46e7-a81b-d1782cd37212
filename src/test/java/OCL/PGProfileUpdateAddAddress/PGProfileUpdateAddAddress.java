package OCL.PGProfileUpdateAddAddress;

import OCL.PG.FlowCreateTerminalInPG;
import Request.MerchantService.v1.profile.update.AddAddressPGProfileUpdate;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class PGProfileUpdateAddAddress extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowCreateTerminalInPG.class);

    public static String MobileNumber = "9358752666";
    public static String Token = "";
    public static String AgentToken = "";
    public static String TID = "";
    public static String SerialNumber = "";
    public static String ModelName = "";
    public static String TerminalStatus = "";
    public static String version = "5.1.6";
    public static String mid = "FNrNTk11503758289999";

    @Test(priority = 0, description = "Create Token for User", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_MerchantLogin() throws SQLException {
        Token = ApplicantToken(MobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);
    }

    @Test(priority = 0, description = "Add address with invalid mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_AddAddressWithInvalidMid()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "aabcd");
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with empty line1", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_AddAddressWithEmptyLine1()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid latitude", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_AddAddressWithInvalidLatitude()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "0");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid longitude", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_AddAddressWithInvalidLongitude()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "0");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid pincode", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_AddAddressWithInvalidPincode()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014344");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid shopname", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_AddAddressWithInvalidShopName()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store @123");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid landmark", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_AddAddressWithInvalidLandmark()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School @123");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with non-individual entity", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_AddAddressWithNonIndividualEntity()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL1");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 0, description = "Add address with empty entity", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_AddAddressWithEmptyEntity()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid address in line1", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_AddAddressWithInvalidAddressInLine1()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265  @123");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid address in line2", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_AddAddressWithInvalidAddressInLine2()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij @123");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid address in line3 ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_AddAddressWithInvalidAddressInLine3()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar @123");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with city empty", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_AddAddressWithCityEmpty()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with state empty ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_AddAddressWithStateEmpty()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address when lead alreday exist ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_AddAddressWhenLeadAlreadyExist()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address when address already exist in boss on the mid", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_AddAddressWhenAddressExistOnMidInBoss()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", "HZdTfI78920174028104");
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid solution type ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_AddAddressWithInvalidSolutionType()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update1");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with empty solution type ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_AddAddressWithEmptySolutionType()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }
    @Test(priority = 0, description = "Add address with invalid solution sub type ", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_AddAddressWithInvalidSolutionSubType()
    {

        AddAddressPGProfileUpdate AddAddressObj = new AddAddressPGProfileUpdate();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("session_token", Token);

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "pg_profile_update");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "DIY_P4B_APP");
        params.put("solutionSubType", "ADD_ADDRESS1");

        Map<String, String> body = new HashMap<>();
        body.put("mid", mid);
        body.put("line1", "b-265");
        body.put("line2", "brij");
        body.put("line3", "vihar");
        body.put("latitude", "28.30");
        body.put("longitude", "77.23");
        body.put("city", "Ghaziabad");
        body.put("state", "Uttar Pradesh");
        body.put("pincode", "201014");
        body.put("formattedAddress", "b-265,brij vihar,Ghaziabad");
        body.put("shopName", "Prashar General Store");
        body.put("landmark", "Bal Bharti School");
        body.put("newAddAddressFlow", "true");

        Response respObj = middlewareServicesObject.addAddressPgProfileUpdate(AddAddressObj, params, headers, body);
        int StatusCode = respObj.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }
}
