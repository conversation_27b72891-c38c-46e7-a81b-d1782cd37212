package OCL.EmbeddedInsurance;


import Request.EmbeddedInsurance.MerchantDetailsAVS;
import Services.EmbeddedInsurance.EmbeddedInsuranceMiddleware;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class GetMerchantDetailsAVS extends BaseMethod {

    EmbeddedInsuranceMiddleware embeddedInsuranceMiddleware =new EmbeddedInsuranceMiddleware();

    private static final Logger LOGGER = LogManager.getLogger(GetMerchantDetailsAVS.class);

    @Test(priority = 0, description = "Get merchant details with valid MID and valid insurer id of LG insurer ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "HHUvZN79586515049019");
        queryParam.put("insurerId", "572020");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Get merchant details with valid MID and valid insurer id of LG insurer" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "Get merchant details with valid MID and valid insurer id of OneAssist insurer ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "JvSHUw41297169016368");
        queryParam.put("insurerId", "571490");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Get merchant details with valid MID and valid insurer id of OneAssist insurer" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "Get merchant details with valid MID and valid insurer id of OnsiteGo insurer ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "571557");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Get merchant details with valid MID and valid insurer id of OnsiteGo insurer" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "Get merchant details with valid MID and valid insurer id of OnsiteGo insurer ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "571557");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Get merchant details with valid MID and valid insurer id of OnsiteGo insurer" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "Get merchant details with invalid MID and valid insurer id of OnsiteGo insurer ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw1780657648705488");
        queryParam.put("insurerId", "571557");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Get merchant details with invalid MID and valid insurer id of OnsiteGo insurer" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 404);

    }

    @Test(priority = 0, description = "Get merchant details with valid MID and invalid insurer id of OnsiteGo insurer ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Get merchant details with valid MID and invalid insurer id of OnsiteGo insurer" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 404);

    }

    @Test(priority = 0, description = "Hit API without token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
       // headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API without token" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API without clientID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        //headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API without clientID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API without requestID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
       // headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API without requestID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API with invalid token ", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o222");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API with invalid token " + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API with invalid clientID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","AB");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API with invalid clientID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "Hit API with invalid requestID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "pmibnw17806576487054");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","BD");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API with invalid requestID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 404);

    }

    @Test(priority = 0, description = "Hit API without MID in query param", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        //queryParam.put("mid", "");
        queryParam.put("insurerId", "572020");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API without MID in query param" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 500);

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "FAILURE");
        Assert.assertEquals(jsObj2.get("resultCode"), "E0010");
        Assert.assertEquals(jsObj2.get("resultMsg"), "System error");

    }


    @Test(priority = 0, description = "Hit API with empty MID value", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "");
        queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API with empty MID value" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 404);

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "FAILURE");
        Assert.assertEquals(jsObj2.get("resultCode"), "E0004");
        Assert.assertEquals(jsObj2.get("resultMsg"), "No relation found for given mid and items.addons.info.id");

    }

    @Test(priority = 0, description = "Hit API without insurer id", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "JvSHUw41297169016368");
       // queryParam.put("insurerId", "5715573");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API without insurer id" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 200);


    }


    @Test(priority = 0, description = "Hit API without clientID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "JvSHUw41297169016368");
        queryParam.put("insurerId", "571490");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
      //  headers.put("clientId","");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API without clientID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "FAILURE");
        Assert.assertEquals(jsObj2.get("resultCode"), "E0001");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Mandatory header(s) are missing [clientId]");


    }

    @Test(priority = 0, description = "Hit API with empty clientID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_018_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "JvSHUw41297169016368");
        queryParam.put("insurerId", "571490");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","");
        headers.put("requestId","OE");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API with empty clientID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "FAILURE");
        Assert.assertEquals(jsObj2.get("resultCode"), "E0001");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Mandatory header(s) are missing [clientId]");


    }

    @Test(priority = 0, description = "Hit API with empty requestID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_019_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "JvSHUw41297169016368");
        queryParam.put("insurerId", "571490");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API with empty requestID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "FAILURE");
        Assert.assertEquals(jsObj2.get("resultCode"), "E0001");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Mandatory header(s) are missing [requestId]");


    }

    @Test(priority = 0, description = "Hit API without requestID", groups = {
            "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_020_GetMerchantDetailsAVS() throws Exception {
        MerchantDetailsAVS merchantDetailsAVS = new MerchantDetailsAVS(P.TESTDATA.get("CreateRequestGetMerchantDetailsAVS"));
        Map<String, String> jwtParams = new HashMap<>();

        Map<String, String> queryParam = new HashMap<String, String>();
        queryParam.put("mid", "JvSHUw41297169016368");
        queryParam.put("insurerId", "571490");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("token","eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Ik9FIiwiZXhwIjoxNjkyNzAyNjU5fQ.od9ym8Ti3KyDw2zXjYRA7txbTms0ygoOlUqu3-OUa-o");
        headers.put("clientId","OE");
        headers.put("requestId","");



        Response respObj = embeddedInsuranceMiddleware.AVSGetdDetails(merchantDetailsAVS,queryParam , headers);
        LOGGER.info("Hit API without requestID" + respObj.statusCode());
        Assert.assertEquals(respObj.statusCode(), 400);

        JSONParser parser = new JSONParser();

        JSONObject jsObj1 = (JSONObject) parser.parse(respObj.getBody().asString());

        JSONObject jsObj2 = (JSONObject) jsObj1.get("resultInfo");
        Assert.assertEquals(jsObj2.get("resultStatus"), "FAILURE");
        Assert.assertEquals(jsObj2.get("resultCode"), "E0001");
        Assert.assertEquals(jsObj2.get("resultMsg"), "Mandatory header(s) are missing [requestId]");


    }


}

