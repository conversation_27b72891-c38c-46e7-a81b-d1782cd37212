package OCL.QRIntegration;

import OCL.Subscription.CreateLUCSubscription;
import Request.QRService.ActiveVPA;
import Request.QRService.GenerateVPA;
import Request.Subscription.CreateSubscription;
import Services.MechantService.MiddlewareServices;
import Services.QR.QRServices;
import Services.Subscription.SubscriptionPlan;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class GenerateAndActivateVPA extends BaseMethod
{
    QRServices QRServicesObj = new QRServices();
    private static final Logger LOGGER = LogManager.getLogger(GenerateAndActivateVPA.class);

    public static String OperationType = "CREATE";
    public static String BusinessType = "UPI_QR_CODE";
    public static String VPA = "";

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", BusinessType);
        body.put("bankName", "PTYES");
        body.put("operationType", OperationType);
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
      String  VPAOld = QRServicesObjResp.jsonPath().getString("response.vpa");
        VPA=VPAOld.replaceAll("[\\[\\](){}]","");
        System.out.println("The VPA is :" +VPA);
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("SUCCESS"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("SUCCESS"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with invalid business type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE1");
        body.put("bankName", "PTYES");
        body.put("operationType", OperationType);
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("Paytm codec not found for the input business type."));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("FAILURE"));
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with empty business type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", " ");
        body.put("bankName", "PTYES");
        body.put("operationType", OperationType);
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("Paytm codec not found for the input business type."));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("FAILURE"));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with invalid bank name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "PTYES1");
        body.put("operationType", OperationType);
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/v5/generateQrCode"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with empty bank name")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", " ");
        body.put("operationType", OperationType);
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/v5/generateQrCode"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with invalid operation type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "PTYES");
        body.put("operationType", "create1");
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/v5/generateQrCode"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with empty operation type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "PTYES");
        body.put("operationType", " ");
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/v5/generateQrCode"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with invalid operation type1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "PTYES");
        body.put("operationType", "CREATE");
        body.put("operationType1", "QR_CODE1");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/v5/generateQrCode"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertEquals(StatusCode, 400);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with empty operation type1")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "PTYES");
        body.put("operationType", "CREATE");
        body.put("operationType1", " ");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/v5/generateQrCode"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with MAP operation type ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "PTYES");
        body.put("operationType", "MAP");
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("Atleast one of vpa, qrCodeId, stickerId should be present in the request in case of operation type is MAP only."));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("FAILURE"));
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with AXIF bank ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "AXIF");
        body.put("operationType", "CREATE");
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("SUCCESS"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("SUCCESS"));
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with PAYTM bank ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "AXIF");
        body.put("operationType", "CREATE");
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("SUCCESS"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("SUCCESS"));
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with YESF bank ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "AXIF");
        body.put("operationType", "CREATE");
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("SUCCESS"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("SUCCESS"));
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Generate VPA with FINOF bank ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14() {

        GenerateVPA GenerateVPAObj = new GenerateVPA(P.TESTDATA.get("GenerateVPARequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "AXIF");
        body.put("operationType", "CREATE");
        body.put("operationType1", "QR_CODE");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("SUCCESS"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("SUCCESS"));
        Assert.assertEquals(StatusCode, 200);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Map VPA with PTYES bank ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15() {

        GenerateVPA GenerateVPAObj1 = new GenerateVPA(P.TESTDATA.get("GenerateVPAMapQRRequest"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("businessType", "UPI_QR_CODE");
        body.put("bankName", "PTYES");
        body.put("operationType", "MAP");
        body.put("operationType1", "QR_CODE");
        body.put("posId", "3647474");
        body.put("vpa", VPA);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("hash", "d67d25073a05b3b47cfdc5e16f78dea39cee9d57c6a7a523321b3dd6dc975f94");
        headers.put("clientid", "a5516f104428408fb6051f833c9bb9e0");
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.generateAndActivateVPA(GenerateVPAObj1, headers, body);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("statusMessage").contains("SUCCESS"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("status").contains("SUCCESS"));
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Activate VPA with PTYES bank")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16() {

        ActiveVPA ActiveVPAObj = new ActiveVPA();
String UpdatedVPA=VPA.replace("@","%40");

        Map<String, String> params = new HashMap<String, String>();
        params.put("vpa", UpdatedVPA);
        params.put("status", "ACTIVE");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.activateVPA(ActiveVPAObj, headers, params);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Activate VPA with PTYES bank with invalid status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17() {

        ActiveVPA ActiveVPAObj = new ActiveVPA();
        String UpdatedVPA=VPA.replace("@","%40");

        Map<String, String> params = new HashMap<String, String>();
        params.put("vpa", UpdatedVPA);
        params.put("status", "ACTIVE1");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.activateVPA(ActiveVPAObj, headers, params);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Internal Server Error"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/debug/vpa/map"));
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Activate VPA with PTYES bank with empty status")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18() {

        ActiveVPA ActiveVPAObj = new ActiveVPA();
        String UpdatedVPA=VPA.replace("@","%40");

        Map<String, String> params = new HashMap<String, String>();
        params.put("vpa", UpdatedVPA);
        params.put("status", " ");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.activateVPA(ActiveVPAObj, headers, params);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Internal Server Error"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/debug/vpa/map"));
        Assert.assertEquals(StatusCode, 500);

    }
    @Test(priority = 1, groups = {"Regression"}, description = "Activate VPA with PTYES bank with vpa not passed in the request")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19() {

        ActiveVPA ActiveVPAObj = new ActiveVPA();
        String UpdatedVPA=VPA.replace("@","%40");

        Map<String, String> params = new HashMap<String, String>();
        params.put("status", "ACTIVE");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Response QRServicesObjResp = QRServicesObj.activateVPA(ActiveVPAObj, headers, params);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/debug/vpa/map"));
        Assert.assertEquals(StatusCode, 400);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Activate VPA with PTYES bank with status not passed in the request")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20() {

        ActiveVPA ActiveVPAObj = new ActiveVPA();
        String UpdatedVPA=VPA.replace("@","%40");

        Map<String, String> params = new HashMap<String, String>();
        params.put("vpa", UpdatedVPA);


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");


        Response QRServicesObjResp = QRServicesObj.activateVPA(ActiveVPAObj, headers, params);
        int StatusCode = QRServicesObjResp.getStatusCode();
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("error").contains("Bad Request"));
        Assert.assertTrue(QRServicesObjResp.jsonPath().getString("path").contains("/qrcode/debug/vpa/map"));
        Assert.assertEquals(StatusCode, 400);

    }
}
