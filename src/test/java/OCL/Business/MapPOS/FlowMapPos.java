package OCL.Business.MapPOS;

import Request.MerchantService.v1.EDC.FetchPayment;
import Request.MerchantService.v1.Resources.Values.BrandAssociation;
import Request.MerchantService.v1.Resources.Values.StoreCategory;
import Request.MerchantService.v3.*;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.PGP.PGPServices;
import Services.Utilities.TestBase;
import Services.Wallet.WalletServices;
import com.goldengate.common.BaseMethod;
import com.google.zxing.NotFoundException;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowMapPos extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    WalletServices walletServicesObj = new WalletServices();
    PGPServices pgpServicesObj = new PGPServices();

    private static final Logger LOGGER = LogManager.getLogger(FlowMapPos.class);

    public static String AgentToken = "";
    public static String CustId = "1001844592";
    public static String mobileNo = "5555505151";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String Pan = "";
    public static String leadId = "";
    public static String UserMID = "";
    public static String State = "";
    public static String SolutionId = "";
    public static String OTP ="";
    public static String version = "5.0.9";
    public static String EntityType = "";
    public static String QrCodeId = "";
    public static String QrBase64 = "";
    public static String pathQrCode = "output.jpg";
    public static String EdcPayMID = "";
    public static String EdcPayMGUID = "";
    public static String EdcOrderId = "";
    public static String EdcAmount = "";
    public static String PositivePgResponse = "01";
    public static String FailurePgResponse = "501";
    public static String StatusCodePG = "";
    public static String PaymentStatus = "";
    public static String solution_type="map_pos";



    @BeforeTest
    public void AgentLoginMapPos() throws Exception {
        LOGGER.info("Before MAP POS Test, Agent Login");
        AgentToken = AgentSessionToken("8010630022", "paytm@123");
        establishConnectiontoServer(AgentToken,5);
        //  AgentToken = CommonAgentToken;
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(7000);
      /*  TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='map_pos';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);
    }

    @Test(priority = 185,groups = {"Regression"},description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPositiveSendOtpBusiness()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"UNKNOWN","company_onboard",AgentToken,version,mobileNo,"company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
       Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
     //   CustId = SendOtpResp.jsonPath().getString("custId");
     //   LOGGER.info("Cust Id is : " + CustId);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 186,description = "Positive Get Business",dependsOnMethods ="MapPosPositiveSendOtpBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPositiveGetBusiness()
    {
        GetBusinessv3 getBusObj = new GetBusinessv3();
        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);
        kybBusinessId = getBusResp.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        LOGGER.info("KYB ID is : " + kybBusinessId);
        businessName = getBusResp.jsonPath().getJsonObject("businesses[0].businessName").toString();
        LOGGER.info("Business Name is : " + businessName);
        Pan=getBusResp.jsonPath().getJsonObject("pan");
        LOGGER.info(" PAN is : " + Pan);
        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode,200);
        getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");
    }
    @Test(priority = 187,description = "Positive Get Business Profile",dependsOnMethods ="MapPosPositiveGetBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPositiveGetBusinessProfile()
    {
        BusinessProfile v3BusPro = new BusinessProfile();
        Response v3BusProRes = middlewareServicesObject.v3BusinessProfile(v3BusPro,CustId,
                leadId,kybBusinessId,AgentToken,version);
        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        EntityType = v3BusProRes.jsonPath().getJsonObject("businessSRO.entityType").toString();
        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }

    @Test(priority = 188,groups = {"Regression"},description = "Send OTP for Lead Creation",dependsOnMethods = "MapPosPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPositiveSendOtpCreate()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,EntityType,"map_pos",AgentToken,version,mobileNo,"map_edc");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }

    @Test(priority = 189,description = "Positive Validate OTP MapPos",dependsOnMethods ="MapPosPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPositiveValidateOtpCreate() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateMapPos"));
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);

        //OTP = getOTP(mobileNo);
      //  OTP="888888";
//        String OTP=getOTPFromSellerPanel(mobileNo);
        String OTP ="888888";
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityType,"map_pos",AgentToken,version,mobileNo,"merchant",State,OTP);
        int StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        SolutionId = validateOtp.jsonPath().getString("leadId");
        LOGGER.info("Solution Lead ID is : " + SolutionId);
        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }

    @Test(priority = 190,description = "Positive Get Merchant for MAP POS",dependsOnMethods ="MapPosPositiveValidateOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPositiveGetMerchant()
    {
        GetMerchant getMerchObj = new GetMerchant(CustId);

        getMerchObj.addParameter("leadId",SolutionId);

        Response getMerchResp = middlewareServicesObject.v3GetMerchant(getMerchObj,EntityType,"map_pos",AgentToken,version);

        int StatusCode = getMerchResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        //getMerchObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetMerchant/GetMerchantResponseSchema.json");

    }

    @Test(priority = 191,description = "Positive Submit Lead Details for 500K",dependsOnMethods ="MapPosPositiveGetMerchant",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPositiveSubmitLead()
    {
        SubmitMerchant v3SubmitObj = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMapPos"));
        v3SubmitObj.addParameter("leadId",SolutionId);

        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(v3SubmitObj,mobileNo,mobileNo,"ANDB0002029",version,EntityType,"map_pos",AgentToken);

        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String ExpectedMsg = "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later";
        String ActualMsg = submitMerchResponse.jsonPath().getString("message");
        Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

        v3SubmitObj.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");
    }

    @Test(priority = 192,description = "Fetch Payment Status",dependsOnMethods ="MapPosPositiveSubmitLead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosFetchPaymentStatus()
    {
//        FetchPayment v1FetchPayment = new FetchPayment();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//        queryParams.put("leadId",SolutionId);
//        queryParams.put("generateQR","true");
//
//        Response v1EdcFetchPayment = middlewareServicesObject.v1EdcPayment(v1FetchPayment,queryParams,AgentToken,version);
//
//        int StatusCode = v1EdcFetchPayment.getStatusCode();
////        Assert.assertEquals(StatusCode, 200);
//
//        PaymentStatus = v1EdcFetchPayment.jsonPath().getJsonObject("paymentDone").toString();
//        LOGGER.info("This is Payment Status : " + PaymentStatus);
//
//        if (!PaymentStatus.equals("true"))
//        {
//            QrBase64 = v1EdcFetchPayment.jsonPath().getJsonObject("qrCodeBase64").toString();
//            LOGGER.info("This is Base64 of QR Code : " + QrCodeId);
//
//            PaymentStatus = v1EdcFetchPayment.jsonPath().getJsonObject("paymentDone").toString();
//            LOGGER.info("This is Payment New Status : " + PaymentStatus);
//        }
//
//        else
//        {
//            LOGGER.info(" Payment already Done ");
//        }
//
//        v1FetchPayment.validateResponseAgainstJSONSchema("MerchantService/V1/EDC/FetchPayment/FetchPaymentResponseSchema.json");

    }

    @Test(priority = 193, description = "Extracting QR Code ID",dependsOnMethods = "MapPosFetchPaymentStatus")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosExtractQrCodeId() throws IOException, NotFoundException
    {
        if (!PaymentStatus.equals("true"))
        {
            QrCodeId = QrCodeExtractor(QrBase64,pathQrCode);
            LOGGER.info("QR Code ID found inside MAP EDC Test : " + QrCodeId);
        }
        else
        {
            LOGGER.info(" Payment already Done ");
        }

    }

    @Test(priority = 194, description = "Fetching QR Code Details from Wallet",dependsOnMethods = "MapPosExtractQrCodeId")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public  void MapPosFetchQrDetails()
    {
//        if (!PaymentStatus.equals("true"))
//        {
//            FetchQrDetails(QrCodeId);
//            EdcPayMID = PayMID;
//            LOGGER.info("MID to Pay : " + EdcPayMID);
//
//            EdcPayMGUID = PayMGUID;
//            LOGGER.info("MGUID to Pay : " + EdcPayMGUID);
//
//            EdcAmount = Amount;
//            LOGGER.info("Amount to be Payed : " + EdcAmount);
//
//            EdcOrderId = OrderId;
//            LOGGER.info("OrderId Generated : " + EdcOrderId);
//        }
//        else
//        {
//            LOGGER.info(" Payment already Done ");
//        }


    }

    @Test(priority = 195, description = "Making Payment for Map Edc Lead",dependsOnMethods = "MapPosFetchQrDetails")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosPayMerchant() throws IOException, NotFoundException
    {
//        if (!PaymentStatus.equals("true"))
//        {
//            StatusCodePG = PayMerchant(EdcOrderId, EdcAmount, EdcPayMID,"FalseOrderCreated");
//            LOGGER.info("This is PG Status Code is : " + StatusCodePG);
//            MapPosFetchPaymentStatus();
//
//            for (int i = 0; i < 4; ++i) {
//                if (!StatusCodePG.equals(PositivePgResponse)) {
//                    MapPosFetchPaymentStatus();
//                    MapPosExtractQrCodeId();
//                    MapPosFetchQrDetails();
//                    StatusCodePG = PayMerchant(EdcOrderId, EdcAmount, EdcPayMID,"FalseOrderCreated");
//                } else {
//                    LOGGER.info("Payment Successfully Done for LeadID : " + SolutionId);
//                    break;
//                }
//
//            }
//        }
//
//        else
//        {
//            LOGGER.info("Payment already Done ");
//        }

    }

    @Test(priority = 196, description = "Fetching Brand Association for Map Pos",dependsOnMethods = "MapPosPayMerchant")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosFetchBrandAssociation()
    {
//        BrandAssociation v1FetchBrand = new BrandAssociation();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        Response v1FetchBrandResp = middlewareServicesObject.v1FetchBrandAssociation(v1FetchBrand,queryParams,AgentToken,version);
//
//        int StatusCode = v1FetchBrandResp.getStatusCode();
//        Assert.assertEquals(StatusCode,200);

//        v1FetchBrand.validateResponseAgainstJSONSchema("MerchantService/V1/Resources/Values/BrandAssociation/BrandAssociationResponseSchema.json");
    }

    @Test(priority = 197, description = "Fetching Store Category for Map Pos",dependsOnMethods = "MapPosFetchBrandAssociation")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapPosFetchStoreCategory()
    {
//        StoreCategory v1FetchStoreCat = new StoreCategory();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        Response v1fetcgStoreCatResp = middlewareServicesObject.v1FetchStoreCategory(v1FetchStoreCat,queryParams,AgentToken,version);
//
//        int StatusCode = v1fetcgStoreCatResp.getStatusCode();
//        Assert.assertEquals(StatusCode,200);

//        v1FetchStoreCat.validateResponseAgainstJSONSchema("MerchantService/V1/Resources/Values/StoreCategory/StoreCategoryResponseSchema.json");

    }
}
