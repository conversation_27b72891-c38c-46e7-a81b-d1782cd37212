package OCL.Business.MerchantCommonOnboard;

import Request.MerchantService.v1.MerchantCommonOnboard.FetchScreenDetails;
import Request.MerchantService.v3.SendOtp;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowMerchantCommonOnboardIndividual extends BaseMethod {
    private static final Logger LOGGER = LogManager.getLogger(FlowMerchantCommonOnboardIndividual.class);

    //  private static final Logger LOGGER = Logger.getLogger(OCL.Business.AssistedMerchantOnboard.FlowAssistedMerchantOnboardFixed.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    Faker GenerateFake = new Faker();

    public static String AgentToken = "6d21d26e-3e1a-4465-a101-7ee9c9697500";
    public static String CustId = "";
    public static String mobileNo = "8545749272";
    public static String newState = "";
    public static String version = "7.1.9";
    public static String OTP = "";
    public static String PAN = "";
    public static String leadId = "";
    public static String RRBUUID = "";
    public static String nameMatchStatus = "";
    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";
    public static String LeadStagePanel = "";
    public static String WorkFlowId = "";
    public static String XMWToken = "";
    public static String MID = "";
    public static String content_type = "application/json; charset=UTF-8";

    @BeforeTest
    public void AgentLoginAssistedMerchant() {

        /*Response responseObject=middlewareServicesObject.v1Token("9560526665","paytm@123");
        XMWCookie=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWCookie);*/

        LOGGER.info("Before Unlimited Test, Agent Login");
        //AgentToken = CommonAgentToken;
        AgentToken = AgentSessionToken("7771110007", "paytm@123");
    }


    @Test(description = "Create Applicant on Oauth", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC000_CreateApplicantOauth() {
        //generating new Number
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + mobileNo);
        CreateApplicantOauth(mobileNo);
    }

    @Test(description = "FetchScreen details", dependsOnMethods = "TC000_CreateApplicantOauth", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_fetchScreenDetails() {

        FetchScreenDetails fetchScreenDetails = new FetchScreenDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy", "AUDIO_DETAILS");


        Response fetchScreenResponse = middlewareServicesObject.fetchScreenDetails(fetchScreenDetails, queryParams, "4.6.5", AgentToken);
        System.out.println("--------------------");
        System.out.println(AgentToken);
        System.out.println("--------------------");
        int statuscode = fetchScreenResponse.getStatusCode();

        Assert.assertEquals(statuscode, 200);

    }

    @Test(description = "Fetch ScreenDetails Without SessionToken", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_fetchScreenDetailsWithoutToken() {

        FetchScreenDetails fetchScreenDetails = new FetchScreenDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy", "AUDIO_DETAILS");


        Response fetchScreenResponse = middlewareServicesObject.fetchScreenDetails(fetchScreenDetails, queryParams, version, "");

        int statuscode = fetchScreenResponse.getStatusCode();

        Assert.assertEquals(statuscode, 401);


    }

    @Test(description = "FetchScreen details", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_fetchScreenDetailsWrongToken() {

        FetchScreenDetails fetchScreenDetails = new FetchScreenDetails();

        Map<String, String> queryParams = new HashMap<>();
        String wrongToken = "edfdf";
        Response fetchScreenResponse = middlewareServicesObject.fetchScreenDetails(fetchScreenDetails, queryParams, version, wrongToken);

        int statuscode = fetchScreenResponse.getStatusCode();
        System.out.println("--------------------");
        System.out.println(wrongToken);
        System.out.println("--------------------");
        //Assert.assertTrue(fetchScreenResponse.jsonPath().getString("errorCode").contains("VERSION_FAILURE"));
        Assert.assertEquals(statuscode, 410);
    }

    @Test(description = "Fetch ScreenDetails Without Version", groups = {"Regression"})
    public void TC004_fetchScreenDetailsWithoutVersion() {

        FetchScreenDetails fetchScreenDetails = new FetchScreenDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy", "");

        Response fetchScreenResponse = middlewareServicesObject.fetchScreenDetails(fetchScreenDetails, queryParams, "", AgentToken);

        int statuscode = fetchScreenResponse.getStatusCode();

        Assert.assertTrue(fetchScreenResponse.jsonPath().getString("errorCode").contains("VERSION_FAILURE"));


    }

    @Test(description = "Fetch ScreenDetails Without Version", groups = {"Regression"})
    public void TC005_fetchScreenDetailsWrongVersion() {

        FetchScreenDetails fetchScreenDetails = new FetchScreenDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy", "");
        String wrongVersion = "1.67";
        Response fetchScreenResponse = middlewareServicesObject.fetchScreenDetails(fetchScreenDetails, queryParams, wrongVersion, AgentToken);

        int statuscode = fetchScreenResponse.getStatusCode();

        Assert.assertTrue(fetchScreenResponse.jsonPath().getString("errorCode").contains("VERSION_FAILURE"));

    }

    @Test(description = "FetchScreenDetails Without QueryParam", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_fetchScreenDetailsWithoutQueryParam() {

        FetchScreenDetails fetchScreenDetails = new FetchScreenDetails();

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("fetchStrategy", "AUDIO_DETAILS");
        Response fetchScreenResponse = middlewareServicesObject.fetchScreenDetails(fetchScreenDetails, queryParams, "4.6.5", AgentToken);

        int statuscode = fetchScreenResponse.getStatusCode();

        Assert.assertEquals(statuscode, 200);

    }

    @Test(description = "Empty Mobile Number")
    public void TC007_emptyMobileSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg1 = "Please provide a Paytm registered mobile number.";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, "", "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg1));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Mobile number is passed as NULL")
    public void TC008_mobileAsNullSendOTP() {
        SendOtp SendOTPobj = new SendOtp();
        String expectedMsg2 = "Please provide a Paytm registered mobile number";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, "null", "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg2));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Mobile number passed is less then 10 digits")
    public void TC009_mobileNoValidationSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg3 = "Please provide a Paytm registered mobile number";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, "12345", "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
//        Assert.assertTrue(actualMsg.contains(expectedMsg3));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Where User Type is invalid")
    public void TC0010_invalidUserTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg4 = "Invalid action";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, mobileNo, "merchan");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
       // Assert.assertTrue(actualMsg.contains(expectedMsg4));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Where User Type is of different solution type")
    public void TC0011_diffUserTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, mobileNo, "company");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Where Entity type is invalid")
    public void TC0012_invalidEntityTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        // String expectedMsg = "Invalid Entity Type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDI", "merchant_common_onboard", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200, 400);
    }

    @Test(description = "Where Entity type is empty")
    public void TC0013_emptyEntityTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        //String expectedMsg = "Invalid Entity Type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "", "merchant_common_onboard", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Where Entity type different than required for solution")
    public void TC0014_diffEntityTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        //String expectedMsg = "Invalid Entity Type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "PUBLIC_LIMITED", "merchant_common_onboard", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Where solution type is invalid")
    public void TC0015_invalidSolutionTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg5 = "Invalid solution type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onbo", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
      //  Assert.assertTrue(actualMsg.contains(expectedMsg5));
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(description = "Where solution type is different")
    public void TC0016_diffSolutionTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg6 = "Invalid solution type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "company_onboard", AgentToken, version, mobileNo, "merchant");
        SendOTPobj.setHttpStatus(respnoseObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(respnoseObject.jsonPath().getString("message"));
        SendOTPobj.setState(respnoseObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(respnoseObject.jsonPath().getString("status"));
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
     //   Assert.assertTrue(actualMsg.contains(expectedMsg6));
        Assert.assertEquals(StatusCode, 200);
    }

    //V3 Send OTP API
    @Test(description = "Positive SendOTP", groups = {"Regression"})
    public void TC0017_PositiveSendOtpSendOTP() {

        SendOtp SendOTPobj = new SendOtp();

        String expectedErrorMsg = "Otp sent to phone";
        LOGGER.info("This is Token " + AgentToken);
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, mobileNo, "common_merchant");
        SendOTPobj.setHttpStatus(respnoseObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(respnoseObject.jsonPath().getString("message"));
        SendOTPobj.setState(respnoseObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(respnoseObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + respnoseObject.jsonPath().getString("state"));
        newState = respnoseObject.jsonPath().getString("state");
        LOGGER.info("New State " + newState);
        String actualErrorMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
       // Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode, 200);
      //  SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");
        //How to validate Schema ??
    }
    @Test(description = "Where Entity type is invalid")
    public void TC00012_invalidEntityTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        // String expectedMsg = "Invalid Entity Type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDI", "merchant_common_onboard", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200, 400);
    }

    @Test(description = "Where Entity type is empty")
    public void TC00013_emptyEntityTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        //String expectedMsg = "Invalid Entity Type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "", "merchant_common_onboard", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        // Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Where Entity type different than required for solution")
    public void TC00014_diffEntityTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        //String expectedMsg = "Invalid Entity Type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "PUBLIC_LIMITED", "merchant_common_onboard", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        //Assert.assertTrue(actualMsg.contains(expectedMsg));
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(description = "Where solution type is invalid")
    public void TC00015_invalidSolutionTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg5 = "Invalid solution type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onbo", AgentToken, version, mobileNo, "merchant");
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
     //   Assert.assertTrue(actualMsg.contains(expectedMsg5));
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(description = "Where solution type is different")
    public void TC00016_diffSolutionTypeSendOTP() {
        SendOtp SendOTPobj = new SendOtp();

        String expectedMsg6 = "Invalid solution type";
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "company_onboard", AgentToken, version, mobileNo, "merchant");
        SendOTPobj.setHttpStatus(respnoseObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(respnoseObject.jsonPath().getString("message"));
        SendOTPobj.setState(respnoseObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(respnoseObject.jsonPath().getString("status"));
        String actualMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
     //   Assert.assertTrue(actualMsg.contains(expectedMsg6));
        Assert.assertEquals(StatusCode, 200);
    }

    //V3 Send OTP API
    @Test(description = "Positive SendOTP", groups = {"Regression"})
    public void TC00017_PositiveSendOtpSendOTP() {

        SendOtp SendOTPobj = new SendOtp();

        String expectedErrorMsg = "Otp sent to phone";
        LOGGER.info("This is Token " + AgentToken);
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, mobileNo, "common_merchant");
        SendOTPobj.setHttpStatus(respnoseObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(respnoseObject.jsonPath().getString("message"));
        SendOTPobj.setState(respnoseObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(respnoseObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + respnoseObject.jsonPath().getString("state"));
        newState = respnoseObject.jsonPath().getString("state");
        LOGGER.info("New State " + newState);
        String actualErrorMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        //Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode, 200);
     //   SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");
        //How to validate Schema ??
    }

    @Test(description = "Positive SendOTP", groups = {"Regression"})
    public void TC00018_PositiveSendOtpSendOTP() {

        SendOtp SendOTPobj = new SendOtp();

        String expectedErrorMsg = "Otp sent to phone";
        LOGGER.info("This is Token " + AgentToken);
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, mobileNo, "common_merchant");
        SendOTPobj.setHttpStatus(respnoseObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(respnoseObject.jsonPath().getString("message"));
        SendOTPobj.setState(respnoseObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(respnoseObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + respnoseObject.jsonPath().getString("state"));
        newState = respnoseObject.jsonPath().getString("state");
        LOGGER.info("New State " + newState);
        String actualErrorMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        //Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode, 200);
        //   SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");
        //How to validate Schema ??
    }
    @Test(description = "Positive SendOTP", groups = {"Regression"})
    public void TC00019_PositiveSendOtpSendOTP() {

        SendOtp SendOTPobj = new SendOtp();

        String expectedErrorMsg = "Otp sent to phone";
        LOGGER.info("This is Token " + AgentToken);
        Response respnoseObject = middlewareServicesObject.v3SentOtp(SendOTPobj, "INDIVIDUAL", "merchant_common_onboard", AgentToken, version, mobileNo, "common_merchant");
        SendOTPobj.setHttpStatus(respnoseObject.jsonPath().getString("httpStatus"));
        SendOTPobj.setMessage(respnoseObject.jsonPath().getString("message"));
        SendOTPobj.setState(respnoseObject.jsonPath().getString("state"));
        SendOTPobj.setStatus(respnoseObject.jsonPath().getString("status"));
        LOGGER.info("state" + " " + respnoseObject.jsonPath().getString("state"));
        newState = respnoseObject.jsonPath().getString("state");
        LOGGER.info("New State " + newState);
        String actualErrorMsg = respnoseObject.jsonPath().getString("message");
        int StatusCode = respnoseObject.getStatusCode();
        //Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        Assert.assertEquals(StatusCode, 200);
        //   SendOTPobj.validateResponseAgainstJSONSchema("MerchantService/V3/SendOtp/SendOtpResponseSchema.json");
        //How to validate Schema ??
    }

  }