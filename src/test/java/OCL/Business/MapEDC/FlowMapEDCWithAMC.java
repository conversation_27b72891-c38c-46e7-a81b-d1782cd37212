package OCL.Business.MapEDC;

import Request.MerchantService.v1.EDC.Edc;
import Request.MerchantService.v1.EDC.EdcPost;
import Request.MerchantService.v1.EDC.FetchPayment;
import Request.MerchantService.v1.EDC.ResendOtp;
import Request.MerchantService.v1.QnA.FetchQnA;
import Request.MerchantService.v1.TokenXMV;
import Request.MerchantService.v3.*;
import Request.PGP.AppPay;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.PGP.PGPServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import com.google.zxing.NotFoundException;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowMapEDCWithAMC extends BaseMethod {
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    WalletServices walletServicesObj = new WalletServices();
    PGPServices pgpServicesObj = new PGPServices();

    private static final Logger LOGGER = LogManager.getLogger(FlowMapEDCWithAMC.class);

    public static String QrCodeId = "";
    public static String AgentToken = "";
//    public static String CustId = "1002370271";
//    public static String mobileNo = "7770008881";
    public static String CustId = "1704128579";
    public static String mobileNo = "6543122222";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String UserMID = "";
    public static String State = "";
    public static String SolutionId = "";
    public static String OTP = "888888";
    public static String version = "7.1.7";
    public static String EntityType = "";
    public static String QrBase64 = "";
    public static String pathQrCode = "output.jpg";
    public static String EdcPayMID = "";
    public static String EdcPayMGUID = "";
    public static String EdcOrderId = "";
    public static String EdcAmount = "";
    public static String PositivePgResponse = "01";
    public static String FailurePgResponse = "501";
    public static String StatusCodePG = "";
    public static String PaymentStatus = "";
    public static String RentalType = "";
    public static String PlanPrice = "";
    public static String PlanUsageDeposite = "";
    public static String PlanAMC = "";
    public static String PlanEmiRental = "";
    public static String PlanEmiOneTime = "";
    public static String PlanSignUpFee = "";
    public static String MdrOtpText = "";
    public static String PgMid = "";
    public static String PgOrderId = "";
    public static String PgTxsAmount = "";
    public static Map getResponsePricing = new HashMap<>();
    public static String PricingComponentDeserialized = "";
    public static String SecurityDep = "";
    public static String AddressUuid = null;
    public static String OmsFlow = "";
    public static String pgRequestID = "";
    public static String stage = "";
    public static String PaymentToken = "";
    public static String solution_type="map_edc";


    @BeforeTest
    public void AgentLoginMapEdc() throws Exception {
        LOGGER.info("Before MAP EDC Test, Agent Login");
        AgentToken = AgentSessionToken("8010630022", "paytm@123");
        establishConnectiontoServer(AgentToken,5);
        LOGGER.info("Agent Token  for map EDC : " + AgentToken);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(3000);
       /* TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + mobileNo + "' and status = '0' and solution_type='map_edc';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);

    }

    @Test(priority = 0, groups = {"Regression"}, description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_MapEdcPositiveSendOtpBusiness() {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp, "UNKNOWN", "company_onboard", AgentToken, version, mobileNo, "company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
      //  CustId = SendOtpResp.jsonPath().getString("custId");
    //    LOGGER.info("Cust Id is : " + CustId);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, description = "Positive Get Business", dependsOnMethods = "TC001_MapEdcPositiveSendOtpBusiness", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_MapEdcPositiveGetBusiness() {
        GetBusinessv3 getBusObj = new GetBusinessv3();
        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj, AgentToken, version, CustId);
        kybBusinessId = getBusResp.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        LOGGER.info("KYB ID is : " + kybBusinessId);
        businessName = getBusResp.jsonPath().getJsonObject("businesses[0].businessName").toString();
        LOGGER.info("Business Name is : " + businessName);
        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode, 200);
     //   getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");
    }

    @Test(priority = 0, description = "Positive Get Business Profile", dependsOnMethods = "TC002_MapEdcPositiveGetBusiness", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC003_MapEdcPositiveGetBusinessProfile() {
        BusinessProfile v3BusPro = new BusinessProfile();
        Response v3BusProRes = middlewareServicesObject.v3BusinessProfile(v3BusPro, CustId,
                mobileNo, kybBusinessId, AgentToken, version);
        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
      EntityType = v3BusProRes.jsonPath().getJsonObject("businessSRO.entityType").toString();
        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }

    @Test(priority = 0, description = "Fetch Applicant's MID", dependsOnMethods = "TC003_MapEdcPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC004_MapEdcPositiveFetchMID() {
        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid, AgentToken, version, CustId, "EDC");
       UserMID = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString();
        LOGGER.info("Applicant's MID is : " + UserMID);
        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode, 200);
      //  v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Send OTP for Lead Creation", dependsOnMethods = "TC004_MapEdcPositiveFetchMID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC005_MapEdcPositiveSendOtpCreate() {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp, EntityType, "map_edc", AgentToken, version, mobileNo, "map_edc");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 0, description = "Positive Validate OTP MapEdc", dependsOnMethods = "TC004_MapEdcPositiveFetchMID", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_MapEdcPositiveValidateOtpCreate() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpMapEdc"));
       validateOtpObj.getProperties().setProperty("kybId", kybBusinessId);
       validateOtpObj.getProperties().setProperty("mid", UserMID);

        //OTP = getOTP(mobileNo);
       // OTP = "888888";
//        String OTP=getOTPFromSellerPanel(mobileNo);
        String OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj, EntityType, "map_edc", AgentToken, version, mobileNo, "merchant", State, OTP);
        int StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        SolutionId = validateOtp.jsonPath().getString("leadId");
        LOGGER.info("Solution Lead ID is : " + SolutionId);

        for (int i = 0; i <= 3; i++) {

            if (SolutionId == null || SolutionId.isEmpty() || StatusCode != 200) {
                LOGGER.info("Lead Id is null, Inside loop : " + i);
                TC005_MapEdcPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("validateOtpMapEdc"));
                ValidateOTPobjErr.getProperties().setProperty("kybId", kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("mid", UserMID);
              //  OTP = getOTP(mobileNo);
                OTP = "888888";
                LOGGER.info("This is OTP : " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr, EntityType, "map_edc", AgentToken, version, mobileNo, "merchant", State, OTP);
                SolutionId = ResNewObj.jsonPath().getString("leadId");
                StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
            } else {
                LOGGER.info("This is Lead ID : " + SolutionId);
                Assert.assertNotEquals(SolutionId, null);
                LOGGER.info("CustId " + CustId);
                Assert.assertNotEquals(CustId, null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }

        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }


    @Test(priority = 0, description = "Fetch EDC Plan", dependsOnMethods = "TC006_MapEdcPositiveValidateOtpCreate", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_MapEdcFetchPlans() throws JsonProcessingException {
        Edc v1EdcObj = new Edc();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", SolutionId);

        Response v1EdcFetchPlan = middlewareServicesObject.v1Edc(v1EdcObj, queryParams, AgentToken, version);

        boolean GPRS;

        if (v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.Linux") == null) {
            RentalType = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.GPRS.D190[0].rentalType").toString();
            PlanPrice = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.GPRS.D190[0].price").toString();
            MdrOtpText = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.GPRS.D190[0].mdrOtpText").toString();
            getResponsePricing = v1EdcFetchPlan.jsonPath().getMap("typeToModelPriceMap.GPRS.D190[0].pricingComponents");
            GPRS = true;
        } else {
            RentalType = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.Linux.D190[0].rentalType").toString();
            PlanPrice = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.Linux.D190[0].price").toString();
            MdrOtpText = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.Linux.D190[0].mdrOtpText").toString();
            getResponsePricing = v1EdcFetchPlan.jsonPath().getMap("typeToModelPriceMap.Linux.D190[0].pricingComponents");
            GPRS = false;
        }


        if (getResponsePricing != null) {
            ObjectMapper mapper = new ObjectMapper();
            PricingComponentDeserialized = mapper.writeValueAsString(getResponsePricing);
        } else {
            LOGGER.info("This is the state of GPRS Flag : " + GPRS);
            if (!GPRS) {
                SecurityDep = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.Linux.D190[0].securityDeposit").toString();
            } else {
                SecurityDep = v1EdcFetchPlan.jsonPath().getJsonObject("typeToModelPriceMap.GPRS.D190[0].securityDeposit").toString();
            }
            double pri = Double.parseDouble(PlanPrice) + Double.parseDouble(SecurityDep);
            PlanPrice = String.valueOf(pri);
        }

        LOGGER.info("Rental type : " + RentalType + "\n Plan Price : " + PlanPrice + "\n MDR Text : " + MdrOtpText + "\n Usage Deposite : " + PlanUsageDeposite
                + "\n Plan AMC : " + PlanAMC + "\n EMI Rental : " + PlanEmiRental + "\n EMI One Time : " + PlanEmiOneTime + "\n Plan Sign Up : " + PlanSignUpFee);

        int StatusCode = v1EdcFetchPlan.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


        //v1EdcObj.validateResponseAgainstJSONSchema("MerchantService/V1/EDC/GetEDCPlan/GetEdcPlanResponseSchema.json");

    }

    @Test(priority = 0, description = "Fetch Lead Details Map EDC Lead", dependsOnMethods = "TC007_MapEdcFetchPlans", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_MapEdcFetchLeadDetails() {
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch, EntityType, "map_edc", AgentToken, version);

        AddressUuid = resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.addressUuid");
        LOGGER.info("Address UUID : " + AddressUuid);

        OmsFlow = resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.solutionAdditionalInfo.IS_OMS_FLOW").toString();
    }

    @Test(priority = 0, description = "Update Map EDC Lead", dependsOnMethods = "TC008_MapEdcFetchLeadDetails", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC009_MapEdcUpdateLead() {

        if (getResponsePricing != null) {
            Map<String, String> queryParams = new HashMap<String, String>();

            Map<String, String> body = new HashMap<String, String>();

            EdcPost v1EdcObj = new EdcPost(P.TESTDATA.get("SelectPlanForMapEDC"));
            LOGGER.info("If Pricing component is not null");
            body.put("leadId", SolutionId);
           /* body.put("price", PlanPrice);
            body.put("pricingComponents", PricingComponentDeserialized);
            body.put("rentalType", RentalType);
            body.put("otpText", MdrOtpText);*/
            LOGGER.info("Request body of Update Map Edc : " + body);
            Response v1EdcUpdateLead = middlewareServicesObject.v1UpdateEdc(v1EdcObj, queryParams, body, AgentToken, version);
            int StatusCode = v1EdcUpdateLead.getStatusCode();
            Assert.assertEquals(StatusCode, 200);
            if(StatusCode !=200)
            {
                Response v1EdcUpdateLead1 = middlewareServicesObject.v1UpdateEdc(v1EdcObj, queryParams, body, AgentToken, version);
                int StatusCode1 = v1EdcUpdateLead1.getStatusCode();
                Assert.assertEquals(StatusCode1, 200);
            }

            String ExpectedMsg = "Success";
            String ActualMsg = v1EdcUpdateLead.jsonPath().getJsonObject("displayMessage").toString();
           // Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

           // v1EdcObj.validateResponseAgainstJSONSchema("MerchantService/V1/EDC/UpdateEdcLead/UpdateEdcLeadResponseSchema.json");

        } else {
            Map<String, String> queryParams = new HashMap<String, String>();

            Map<String, String> body = new HashMap<String, String>();

            EdcPost v1EdcObj = new EdcPost(P.TESTDATA.get("UpdateEdcLead"));
            LOGGER.info("Else Pricing component is null");
            body.put("leadId", SolutionId);
            body.put("price", PlanPrice);
            body.put("rentalType", RentalType);
            body.put("otpText", MdrOtpText);
            body.put("securityDeposit", SecurityDep);
            LOGGER.info("Request body of Update Map Edc : " + body);
            Response v1EdcUpdateLead = middlewareServicesObject.v1UpdateEdc(v1EdcObj, queryParams, body, AgentToken, version);
            int StatusCode = v1EdcUpdateLead.getStatusCode();
            Assert.assertEquals(StatusCode, 200);

            String ExpectedMsg = "Success";
            String ActualMsg = v1EdcUpdateLead.jsonPath().getJsonObject("displayMessage").toString();
            Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

         //   v1EdcObj.validateResponseAgainstJSONSchema("MerchantService/V1/EDC/UpdateEdcLead/UpdateEdcLeadResponseSchema.json");

        }

    }

    @Test(priority = 0, description = "Fetch Payment Status", dependsOnMethods = "TC009_MapEdcUpdateLead", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0010_MapEdcFetchPaymentStatus() {
        FetchPayment v1FetchPayment = new FetchPayment();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("leadId", SolutionId);
        queryParams.put("generateQR", "true");

        Response v1EdcFetchPayment = middlewareServicesObject.v1EdcPayment(v1FetchPayment, queryParams, AgentToken, version);

        int StatusCode = v1EdcFetchPayment.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        PaymentStatus = v1EdcFetchPayment.jsonPath().getJsonObject("paymentDone").toString();
        LOGGER.info("This is Payment Status : " + PaymentStatus);

       /* if (!PaymentStatus.equals("true")) {
            QrBase64 = v1EdcFetchPayment.jsonPath().getJsonObject("qrCodeBase64").toString();
            LOGGER.info("This is Base64 of QR Code : " + QrBase64);

            PaymentStatus = v1EdcFetchPayment.jsonPath().getJsonObject("paymentDone").toString();
            LOGGER.info("This is Payment New Status : " + PaymentStatus);

            if (OmsFlow.equalsIgnoreCase("true")) {
                PgMid = v1EdcFetchPayment.jsonPath().getJsonObject("MID").toString();
                LOGGER.info("This is PG MID : " + PgMid);

                PgOrderId = v1EdcFetchPayment.jsonPath().getJsonObject("ORDER_ID").toString();
                LOGGER.info("This is PG OrderId : " + PgOrderId);

                PgTxsAmount = v1EdcFetchPayment.jsonPath().getJsonObject("TXN_AMOUNT").toString();
                LOGGER.info("This is PG Transaction Amount : " + PgTxsAmount);
            }
        } else {
            LOGGER.info(" Payment already Done ");
        }
*/
        //v1FetchPayment.validateResponseAgainstJSONSchema("MerchantService/V1/EDC/FetchPayment/FetchPaymentResponseSchema.json");

    }

    @Test(priority = 0, description = "Extracting QR Code ID", dependsOnMethods = "TC0010_MapEdcFetchPaymentStatus")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0011_MapEdcExtractQrCodeId() throws IOException, NotFoundException {
        if (!PaymentStatus.equals("true") && !OmsFlow.equalsIgnoreCase("true")) {
            QrCodeId = QrCodeExtractor(QrBase64, pathQrCode);
            LOGGER.info("QR Code ID found inside MAP EDC Test : " + QrCodeId);
            Assert.assertNotNull(QrCodeId);
            Assert.assertFalse(QrCodeId.isEmpty());
        } else {
            LOGGER.info(" Payment already Done ");
        }

    }

    @Test(priority = 0, description = "Fetching QR Code Details from Wallet", dependsOnMethods = "TC0011_MapEdcExtractQrCodeId")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0012_MapEdcFetchQrDetails() {
        if (!PaymentStatus.equals("true") && !OmsFlow.equalsIgnoreCase("true")) {
            FetchQrDetails(QrCodeId);
            EdcPayMID = PayMID;
            LOGGER.info("MID to Pay : " + EdcPayMID);

            EdcPayMGUID = PayMGUID;
            LOGGER.info("MGUID to Pay : " + EdcPayMGUID);

            EdcAmount = Amount;
            LOGGER.info("Amount to be Payed : " + EdcAmount);

            EdcOrderId = OrderId;
            LOGGER.info("OrderId Generated : " + EdcOrderId);
        } else {
            LOGGER.info(" Payment already Done ");
        }


    }

   /* @Test(priority = 0, description = "Making Payment for Map Edc Lead", dependsOnMethods = "TC0012_MapEdcFetchQrDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0013_MapEdcPayMerchant() throws IOException, NotFoundException {
        if (!PaymentStatus.equals("true")) {
            for (int i = 0; i < 4; ++i) {

                if (!StatusCodePG.equals(PositivePgResponse) && !PaymentStatus.equals("true")) {
                    if (OmsFlow.equalsIgnoreCase("true")) {
                        StatusCodePG = PayMerchant(PgOrderId, PgTxsAmount, PgMid, "TrueOrderCreated");
                    } else {
                        TC0011_MapEdcExtractQrCodeId();
                        TC0012_MapEdcFetchQrDetails();
                        StatusCodePG = PayMerchant(EdcOrderId, EdcAmount, EdcPayMID, "FalseOrderCreated");
                    }
                    TC0010_MapEdcFetchPaymentStatus();
                } else if (StatusCodePG.equals(PositivePgResponse) && !PaymentStatus.equals("true")) {
                    TC0010_MapEdcFetchPaymentStatus();
                } else {
                    LOGGER.info("Payment Successfully Done for LeadID : " + SolutionId);
                    break;
                }

            }
        } else {
            LOGGER.info("Payment already Done ");
        }

        Assert.assertEquals(PaymentStatus, "true");
    }*/

    @Test(priority = 0, description = "Payment for Map EDC Lead", dependsOnMethods = "TC0012_MapEdcFetchQrDetails", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC013_MapEdcPayment() {
        waitForLoad(3000);
        PaymentToken=FetchPaymentToken();
        AppPay payObj =new AppPay(P.TESTDATA.get("NewPaymentBodyRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("orderId", PgOrderId);
        body.put("customerId", CustId);
        body.put("mid", PgMid);
        body.put("txnAmount", PgTxsAmount);
        body.put("token", PaymentToken);
        Response resObj=pgpServicesObj.payMerchant(payObj,PaymentToken,body);

        int StatusCode = resObj.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 0, description = "Fetch Lead Details Map EDC Lead", dependsOnMethods = "TC013_MapEdcPayment", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC014_MapEdcFetchLeadDetailsAfterPayment() {
         System.out.println("test");
//        waitForLoad(3000);
//        Response resObjGetMerchant = FetchPanelLeadviaLeadID(SolutionId);
//        stage = resObjGetMerchant.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
//        LOGGER.info("lead stage  is : " + stage);

    }

    @Test(priority = 0, description = "Fetch Lead Details Map EDC Lead", dependsOnMethods = "TC014_MapEdcFetchLeadDetailsAfterPayment", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC015_MapEdcFetchLeadDetailsForPgReferenceId() {
        if (stage.equals("OMS_ORDER_STATUS_SUCCESS")) {
            Response resObjGetMerchant = FetchPanelLeadviaLeadID(SolutionId);
            pgRequestID = resObjGetMerchant.jsonPath().getJsonObject("leadDetails.additionalDetails.pgReferenceId").toString();
            LOGGER.info("PG Request id is : " + pgRequestID);
        } else {
            LOGGER.info("Lead is not in correct stage");
        }

    }

    @Test(priority = 0, description = "PG Callback for EDC", dependsOnMethods = "TC015_MapEdcFetchLeadDetailsForPgReferenceId")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0016_ManualPGCallbackForEdc() {
        ManualPgCallBack(CustId, pgRequestID, UserMID);

    }

    @Test(priority = 0, description = "Fetch QNA for Map Edc Lead", dependsOnMethods = "TC0016_ManualPGCallbackForEdc")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0017_MapEdcFetchQnA() {
        FetchQnA v1FetchQnaObj = new FetchQnA();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "map_edc");
        queryParams.put("entityType", EntityType);
        queryParams.put("questionType", "additional");

        Response v1FetchQnaResp = middlewareServicesObject.v1FetchQnA(v1FetchQnaObj, queryParams, AgentToken, version);

        int StatusCode = v1FetchQnaResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }
//
//    @Test(priority = 0, description = "MAP EDC Device", dependsOnMethods = "TC0017_MapEdcFetchQnA")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC0018_MapEdcMachineMapping() {
//        waitForLoad(5000);
//        LOGGER.info("Mapping EDC Machine ");
//        EdcPost v1EdcObj = new EdcPost(P.TESTDATA.get("MapEdcMachine"));
//
//        Integer SerialNo = Utilities.randomNumberGenerator(6);
//        String MapSerialNo = "AASHIT" + SerialNo;
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("leadId", SolutionId);
//        body.put("serialNo", MapSerialNo);
//
//        Response v1EdcMapMachine = middlewareServicesObject.v1UpdateEdc(v1EdcObj, queryParams, body, AgentToken, version);
//
//        int StatusCode = v1EdcMapMachine.getStatusCode();
//
//        LOGGER.info("This is Serial No of EDC : " + MapSerialNo);
//        LOGGER.info("This is Status Code : " + StatusCode);
//
//        for (int i = 0; i < 3; ++i) {
//            LOGGER.info(" Inside Loop and value of counter is : " + i);
//            if (StatusCode == 500) {
//                waitForLoad(1000);
//                EdcPost v1EdcObjLoop = new EdcPost(P.TESTDATA.get("MapEdcMachine"));
//                LOGGER.info(" Inside IF condiiton of loop ");
//
//                SerialNo = Utilities.randomNumberGenerator(5);
//                MapSerialNo = "AASHIT" + SerialNo;
//
//                Map<String, String> queryLoop = new HashMap<String, String>();
//
//                Map<String, String> bodyLoop = new HashMap<String, String>();
//                bodyLoop.put("leadId", SolutionId);
//                bodyLoop.put("serialNo", MapSerialNo);
//
//                LOGGER.info(" Calling API inside Loop");
//                Response v1EdcMapMachineLoop = middlewareServicesObject.v1UpdateEdc(v1EdcObjLoop, queryLoop, bodyLoop, AgentToken, version);
//                LOGGER.info(" API has been called inside Loop");
//                StatusCode = v1EdcMapMachineLoop.getStatusCode();
//                LOGGER.info(" Status Code of inside loop : " + StatusCode);
//
//            } else {
//                LOGGER.info(" EDC Machine Mapped Successfully ");
//                Assert.assertEquals(StatusCode, 200);
//                break;
//            }
//        }
//
//        Assert.assertEquals(StatusCode, 200);
//    }
//
//    @Test(priority = 0, description = "Resend OTP for EDC", dependsOnMethods = "TC0018_MapEdcMachineMapping")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC0019_MapEdcResendOtp() {
//        ResendOtp v1ResendOtpobj = new ResendOtp();
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("leadId", SolutionId);
//
//        Response v1ResendOtpresp = middlewareServicesObject.v1ResendOtp(v1ResendOtpobj, queryParams, body, AgentToken, version);
//
//        int StatusCode = v1ResendOtpresp.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//
//        String ExpectedMsg = "OTP sent successfully";
//        String ActualMsg = v1ResendOtpresp.jsonPath().getJsonObject("displayMessage");
//        Assert.assertTrue(ExpectedMsg.contains(ActualMsg));
//
//    }
//
//    @Test(priority = 0, description = "Finish Mapping Process", dependsOnMethods = "TC0019_MapEdcResendOtp")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC0020_MapEdcFinishMapping() {
//        EdcPost v1EdcObj = new EdcPost(P.TESTDATA.get("FinishMapping"));
//
//        Map<String, String> queryParams = new HashMap<String, String>();
//
//        Map<String, String> body = new HashMap<String, String>();
//        body.put("leadId", SolutionId);
//
//        Response v1FinishMapping = middlewareServicesObject.v1UpdateEdc(v1EdcObj, queryParams, body, AgentToken, version);
//
//        int StatusCode = v1FinishMapping.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
//
//        LOGGER.info(" Lead Updated After Successful Mapping of EDC ");
//    }
//
//    @Test(priority = 0, description = "Fetch Lead Details Map EDC Lead after mapping", dependsOnMethods = "TC0020_MapEdcFinishMapping", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC021_MapEdcFetchLeadDetailsAfterMapping() {
//        waitForLoad(5000);
//        Response resObjGetMerchant = FetchPanelLeadviaLeadID(SolutionId);
//        stage = resObjGetMerchant.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
//        LOGGER.info("lead stage  is : " + stage);
//        LOGGER.info("Please upload AMC file");
//
//    }
//
//    @Test(priority = 0, description = "Sheet upload from panel", dependsOnMethods = "TC021_MapEdcFetchLeadDetailsAfterMapping", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC022_AMCFileRejectedFromPanel() throws InterruptedException, IOException {
//
//        if (stage.equals("AMC_ACTIVATION_PENDING")) {
//            TokenXMV tokenXMW = new TokenXMV();
//            Response responseObject = middlewareServicesObject.v1Token("9560526665", "paytm@123");
//            String XMWToken = responseObject.getHeader("Set-Cookie").toString();
//
//            System.out.println("XMW token is :" + XMWToken);
//            Map<String, String> headers = new HashMap<String, String>();
//            headers.put("Content-Type", "multipart/form-data");
//            headers.put("Cookie", XMWToken);
//
//            File fileUpload = new File(
//                    "src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/AMCSampleFileForMapEDC.csv");
//            FileWriter outputfile = new FileWriter(fileUpload);
//            CSVWriter writer = new CSVWriter(outputfile);
//            String[] header = {"leadId", "Insurance Date", "Policy Id", "Policy Expiry Date", "Policy Partner", "Policy Status\n"};
//            writer.writeNext(header);
//            String[] data1 = {SolutionId, "2020-06-18", "45600", "2020-06-18", "PartnerName,"};
//            writer.writeNext(data1);
//            writer.flush();
//            writer.close();
//
//            responseObject = uploadDocFromPanel(headers, fileUpload, "AMC_UPLOAD");
//        }
//    }
//
//    @Test(priority = 0, description = "Sheet upload from panel", dependsOnMethods = "TC022_AMCFileRejectedFromPanel", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC023_UploadAMCFileFromPanel() throws InterruptedException, IOException
//    {
//
//        if (stage.equals("AMC_ACTIVATION_PENDING"))
//        {
//            TokenXMV tokenXMW = new TokenXMV();
//            Response responseObject = middlewareServicesObject.v1Token("9560526665", "paytm@123");
//            String XMWToken = responseObject.getHeader("Set-Cookie").toString();
//
//            System.out.println("XMW token is :" + XMWToken);
//            Map<String, String> headers = new HashMap<String, String>();
//            headers.put("Content-Type", "multipart/form-data");
//            headers.put("Cookie", XMWToken);
//
//            File fileUpload = new File(
//                    "src/test/resources/MerchantService/OE/Panel/v1/fileProcess/upload/AMCSampleFileForMapEDC.csv");
//            FileWriter outputfile = new FileWriter(fileUpload);
//            CSVWriter writer = new CSVWriter(outputfile);
//            String[] header = {"leadId", "Insurance Date", "Policy Id", "Policy Expiry Date", "Policy Partner", "Policy Status\n"};
//            writer.writeNext(header);
//            String[] data1 = {SolutionId, "2020-06-18", "45600", "2020-06-18", "PartnerName", "active"};
//            writer.writeNext(data1);
//            writer.flush();
//            writer.close();
//
//            responseObject = uploadDocFromPanel(headers, fileUpload, "AMC_UPLOAD");
//            if (responseObject.jsonPath().getString("fileStatuses.statusMessage")
//                    .contentEquals(" has been successfully uploaded"))
//            {
//                LOGGER.info("File has been :" + responseObject.jsonPath().getString("fileStatuses.statusMessage"));
//                Assert.assertEquals(responseObject.jsonPath().getString("fileStatuses.state"), 1);
//
//            }
//
//
//        }
//        else {
//            LOGGER.info("Lead is not in AMC stage");
//        }
//    }
//
//    @Test(priority = 0, description = "Fetch Lead Details Map EDC Lead after mapping", dependsOnMethods = "TC023_UploadAMCFileFromPanel", groups = {"Regression"})
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC024_MapEdcFetchLeadDetailsAfterAMCFileUpload() {
//        waitForLoad(5000);
//        Response resObjGetMerchant = FetchPanelLeadviaLeadID(SolutionId);
//        stage = resObjGetMerchant.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
//        LOGGER.info("lead stage  is : " + stage);
//        Assert.assertEquals(stage,"LEAD_SUCCESSFULLY_CLOSED");
//
//    }


}


