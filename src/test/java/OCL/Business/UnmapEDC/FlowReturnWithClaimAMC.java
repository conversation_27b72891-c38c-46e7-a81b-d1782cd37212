package OCL.Business.UnmapEDC;

import Request.MerchantService.v1.EDC.CreateUnmapEdcLead;
import Request.MerchantService.v1.EDC.unmapEDCMachine;
import Request.MerchantService.v1.EDC.validateEDCQr;
import Request.MerchantService.v1.QnA.FetchQnA;
import Request.MerchantService.v1.Resources.GetMappedDataByType.UnmapEDCReturnReasonsWithClaimAMC;
import Request.MerchantService.v1.merchant.fetchAllTerminal;
import Request.MerchantService.v3.*;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.google.zxing.NotFoundException;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowReturnWithClaimAMC extends BaseMethod {
    MiddlewareServices middlewareServicesObject;
    private static final Logger LOGGER = LogManager.getLogger(FlowReturnWithClaimAMC.class);

    public FlowReturnWithClaimAMC() {
        try {
            middlewareServicesObject = new MiddlewareServices();
            LOGGER.info("Successfully initialized FlowReturnWithClaimAMC");
        } catch (Exception e) {
            LOGGER.error("Error initializing FlowReturnWithClaimAMC: " + e.getMessage());
        }
    }

    public static String AgentToken = "";
    public static String mobileNo = "7770008841";
    public static String version = "5.0.8";
    public static String UserMID = "";
    public static String CustId = "1002359412";
    public static String OTP = "888888";
    public static String State = "";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String EntityType = "";
    public static String EdcSerialNumber = "";
    public static String EdcModelName = "";
    public static String EdcVendorName = "";
    public static String state = "";
    public static String PgTxsAmount="600";
    public static String StatusCodePG = "";
    public static String EdcOldTID="";
    String solution_type="unmap_edc";



    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginUnmapEDC() throws Exception
    {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token  for Unmap EDC : " + AgentToken);
//        establishConnectiontoServer(AgentToken,5);
        waitForLoad(3000);
        /*TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='unmap_edc';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);


    }

    @Test(priority = 1, groups = {"Regression"}, description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcReturnPositiveSendOtpBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        SendOtp v3SendOtp1 = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp1, "UNKNOWN", "company_onboard", AgentToken, version, mobileNo, "company");
        String expectedErrorMsg = "SUCCESS";
       String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
    //    LOGGER.info("Merchant Cust Id is : " + CustId);
       int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 2, description = "Positive Validate OTP for Unmapedc", dependsOnMethods = "UnMapEdcReturnPositiveSendOtpBusiness", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnmapEDCReturnPositiveValidateOtp() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        waitForLoad(2000);
        ValidateOtp validateOtpObj1 = new ValidateOtp(P.TESTDATA.get("ValidateOtpUnmapEDC"));

       // OTP = getOTP(mobileNo);
      //  String OTP=getOTPFromSellerPanel(mobileNo);
        //    OTP="888888";
      //  LOGGER.info("This is OTP " + OTP);
        Response validateOtp1 = middlewareServicesObject.v3ValidateOtp(validateOtpObj1, "INDIVIDUAL", "company_onboard", AgentToken, version, mobileNo, "company", State, OTP);
        int StatusCode = validateOtp1.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }


    @Test(priority = 3, description = "Positive Get Business", groups = {"Regression"}, dependsOnMethods = "UnmapEDCReturnPositiveValidateOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcReturnPositiveGetBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        GetBusinessv3 getBusObj1 = new GetBusinessv3();
        System.out.println("Merchant Cust id is : " + CustId);
        Response getBusResp1 = middlewareServicesObject.v3GetBusiness(getBusObj1, AgentToken, version, CustId);
        kybBusinessId = getBusResp1.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        LOGGER.info("KYB ID is : " + kybBusinessId);
        businessName = getBusResp1.jsonPath().getJsonObject("businesses[0].businessName").toString();
        LOGGER.info("Business Name is : " + businessName);
        int statusCode = getBusResp1.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        getBusObj1.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");
    }

    @Test(priority = 4, description = "Fetch Applicant's MID", dependsOnMethods = "UnMapEdcReturnPositiveGetBusiness", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcPositiveFetchMID() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid1 = new MID();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "unmap_edc");
        System.out.println("Merchant Cust id is :" + CustId);
        Response v3FetchMIDResp1 = middlewareServicesObject.v3FetchMID(v3FetchMid1, AgentToken, version, CustId, "EDC");
        if (v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].mid").toString() == null)
        {
            UnMapEdcPositiveFetchMID();
        }
        UserMID = v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].mid").toString();
        EntityType = v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].entityType").toString();
        LOGGER.info("Applicant's MID is : " + UserMID);
        int statusCode = v3FetchMIDResp1.getStatusCode();
        Assert.assertEquals(statusCode, 200);
        v3FetchMid1.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }


    @Test(priority = 5, description = "Fetch unmap edc return with claim amc reasons", groups = {"Regression"}, dependsOnMethods = "UnMapEdcPositiveFetchMID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcReturnWithClaimAMCReasons() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        try {
            UnmapEDCReturnReasonsWithClaimAMC UnmapEDCReturnReasonsObject1 = new UnmapEDCReturnReasonsWithClaimAMC();
            Response getMerchantResponse = middlewareServicesObject.v1UnmapEdcReturnReasonsWithClaimAMC(UnmapEDCReturnReasonsObject1, AgentToken, version);
            int statusCode = getMerchantResponse.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        } catch (Exception e) {
            LOGGER.error("Error in UnMapEdcReturnWithClaimAMCReasons: " + e.getMessage());
            // Skip this test but don't fail the entire test suite
            LOGGER.info("Skipping this test and continuing with the test suite");
        }
    }

    @Test(priority = 6, description = "Positive unmap edc create lead for claim amc", groups = {"Regression"}, dependsOnMethods = "UnMapEdcReturnWithClaimAMCReasons")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcReturnWithClaimAMCCreateLead() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        CreateUnmapEdcLead CreateUnmapEdcLeadObject = new CreateUnmapEdcLead(P.TESTDATA.get("UnmapEDCCreateLeadRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", "PROPRIETORSHIP");
        body.put("userCustId", CustId);
        body.put("userMobile", mobileNo);
        body.put("mid", UserMID);
        body.put("kybId", kybBusinessId);
        Response getMerchantResponse = middlewareServicesObject.v1UnmapEdcCreateLead(CreateUnmapEdcLeadObject, body, AgentToken, version);
        leadId = getMerchantResponse.jsonPath().getString("leadId");
        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 7, description = "Positive Get Merchant details", groups = {"Regression"}, dependsOnMethods = "UnMapEdcReturnWithClaimAMCCreateLead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcReturnPositiveGetMerchantDetails() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        GetMerchant getMerchantobj = new GetMerchant(CustId);
        Response getMerchantResponse = middlewareServicesObject.v3GetMerchantUnmapEDC(getMerchantobj, EntityType, "unmap_edc", AgentToken, version, leadId, kybBusinessId);
        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 8, description = "Fetch qna for unmap edc", dependsOnMethods = "UnMapEdcReturnPositiveGetMerchantDetails", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcFetchQnA() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        FetchQnA v1FetchQnaObj1 = new FetchQnA();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "unmap_edc");
        queryParams.put("entityType", "PROPRIETORSHIP");
        queryParams.put("questionType", "additional");

        Response v1FetchQnaResp1 = middlewareServicesObject.v1FetchQnA(v1FetchQnaObj1, queryParams, AgentToken, version);

        int StatusCode = v1FetchQnaResp1.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }


    @Test(priority = 9, description = "Fetch all terminal from pg", dependsOnMethods = "UnMapEdcFetchQnA", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnmapEDCfetchAllTerminalFromPG() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        PGToken = ApplicantToken("**********", "paytm@123");
        LOGGER.info("Token for PG : " + PGToken);

        Response res=OnboardBanksONPG(PGToken,UserMID,"HEDC");
        if(res.getStatusCode()==400)
        {
            OnboardBanksONPG(PGToken,UserMID,"HEDC");
        }
     //   Response resp=CreateTerminalInPG(UserMID);
        String SerialNumberNew=res.jsonPath().getJsonObject("request.serialNo").toString();
        CreateSubscription(UserMID,CustId,SerialNumberNew,"AMC",mobileNo,"12M",java.time.LocalDate.now().toString());

        fetchAllTerminal fetchAllTerminalObject = new fetchAllTerminal();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid", UserMID);
        queryParams.put("subscriptionType", "AMC");
        queryParams.put("subscriptionStatus", "ACTIVE");
        Response fetchAllTerminalResponse = middlewareServicesObject.v1FetchallTerminal(fetchAllTerminalObject, queryParams, AgentToken, version);
        EdcSerialNumber = fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].serialNo").toString();
        EdcModelName = fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].modelName").toString();
        EdcVendorName = fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].vendorName").toString();
        EdcOldTID=fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].tid").toString();
        System.out.println(" EDC Serial number is " + EdcSerialNumber);
        System.out.println(" EDC model name is " + EdcModelName);
        System.out.println(" EDC vendor name is " + EdcVendorName);
        int StatusCode = fetchAllTerminalResponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }

    @Test(priority = 10, description = "Validate EDC QR to return", groups = {"Regression"}, dependsOnMethods = "UnmapEDCfetchAllTerminalFromPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void ValidateEdcQrReturnWithClaimAMC() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        ActiveTerminalInPG(UserMID,EdcSerialNumber,EdcOldTID,EdcModelName,"HEDC");
        validateEDCQr validateEDCQrObject = new validateEDCQr();
        Map<String, String> body = new HashMap<String, String>();
        body.put("modelName", EdcModelName);
        body.put("oem", EdcVendorName);
        body.put("serialNo", EdcSerialNumber);
        body.put("leadId", leadId);
        Response getMerchantResponse = middlewareServicesObject.validateEDCQr(validateEDCQrObject, body, AgentToken, version);
        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }




    @Test(priority = 11, description = "Fetch TnC unmap edc", groups = {"Regression"}, dependsOnMethods = "ValidateEdcQrReturnWithClaimAMC")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void FetchTnCUnmapEDC() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("tncSet", "unmap_edc_tnc");
        queryParams.put("entityType", EntityType);
        queryParams.put("solutionType", "unmap_edc");

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("version", "7.1.7");
        headers.put("session_token", AgentToken);
        headers.put("deviceIdentifier", "OPPO-CPH1859-869003037324211");
        headers.put("Content-Type", "application/json");

        Response ResponseFetchTnCUnmapEDC = middlewareServicesObject.FetchTnCSoundBoxDetails(queryParams, headers);

        int StatusCode = ResponseFetchTnCUnmapEDC.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


        LOGGER.info("Status : " + ResponseFetchTnCUnmapEDC.jsonPath().getString("status"));
        Assert.assertEquals(ResponseFetchTnCUnmapEDC.jsonPath().getString("status"), "SUCCESS");


        LOGGER.info("URL : " + ResponseFetchTnCUnmapEDC.jsonPath().getString("url"));


    }

    @Test(priority = 12,groups = {"Regression"},description = "send otp for unmap edc",dependsOnMethods = "FetchTnCUnmapEDC")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UnMapEdcPositiveSendOtp() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,EntityType,"unmap_edc",AgentToken,version,mobileNo,"unmap_edc_tnc");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        Assert.assertEquals(SendOtpResp.jsonPath().getString("message"), "Otp sent to phone");
    }

    @Test(priority = 13,description = "Positive Validate OTP unMapEdc",dependsOnMethods ="UnMapEdcPositiveSendOtp",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UnMapEdcPositiveValidateOtp() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("ValidateOtpReturnWithClaimAMC"));

      //  OTP = getOTP(mobileNo);
      //  String OTP=getOTPFromSellerPanel(mobileNo);
        OTP="888888";
     //   LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj, EntityType, "unmap_edc", AgentToken, version, mobileNo, "unmap_edc_tnc", State, OTP);
        int StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);


    }


    @Test(priority = 14, description = "Create QR for unmap edc return with claim amc", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createQRforUnmapEDCWithClaimAMC() throws Exception {

        establishConnectiontoServer(AgentToken,5);

        OrderId=createQRviaOMS(leadId,mobileNo,AgentToken);
        System.out.println("Order id is " + OrderId);


    }

    @Test(priority = 15, description = "Making Payment for unMap Edc with claim amc Lead",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UnMapEdcPayMerchant() throws Exception {
        establishConnectiontoServer(AgentToken,5);

                    StatusCodePG = PayMerchant(OrderId, PgTxsAmount, "stag1236977633789432", "NewPaymentBodyRequest");


    }

    @Test(priority = 16,description = "Return EDC Machine",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void UnmapEDCMachine_ReturnWithClaimAMC() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        unmapEDCMachine unmapEDCMachineObject=new unmapEDCMachine(P.TESTDATA.get("UnmapReturnWithClaimAMC"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("modelName",EdcModelName);
        body.put("oem",EdcVendorName);
        body.put("serialNo",EdcSerialNumber);
        body.put("leadId",leadId);
        Response getMerchantResponse=middlewareServicesObject.UnmapEDCMachine(unmapEDCMachineObject,body,AgentToken,version);
        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode,200);
        if(statusCode != 200)
        {
            Response getMerchantResponse1=middlewareServicesObject.UnmapEDCMachine(unmapEDCMachineObject,body,AgentToken,version);
            int statusCode1 = getMerchantResponse1.getStatusCode();
            Assert.assertEquals(statusCode1,200);
        }
        LOGGER.info(" Successfully returned EDC Machine ");
    }
}