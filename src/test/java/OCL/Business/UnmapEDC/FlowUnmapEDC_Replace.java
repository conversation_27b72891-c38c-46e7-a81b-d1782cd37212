package OCL.Business.UnmapEDC;

import Request.MerchantService.v1.EDC.CreateUnmapEdcLead;
import Request.MerchantService.v1.EDC.unmapEDCMachine;
import Request.MerchantService.v1.EDC.validateEDCQr;
import Request.MerchantService.v1.QnA.FetchQnA;
import Request.MerchantService.v1.Resources.Values.UnmapEDCReplaceReasons;
import Request.MerchantService.v1.merchant.fetchAllTerminal;
import Request.MerchantService.v3.*;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import Services.Utilities.Utilities;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

public class FlowUnmapEDC_Replace extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowUnmapEDC_Replace.class);

    public static String AgentToken = "";
    public static String mobileNo = "6543122222";
    public static String version = "7.1.7";
    public static String UserMID = "";
    public static String CustId = "1704128579";
    public static String OTP = "888888";
    public static String State = "";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String EntityType = "";
    public static String companyLeadId = "";
    public static String EdcOldSerialNumber = "";
    public static String EdcOldModelName = "";
    public static String EdcOldVendorName = "";
    public static String EdcOldTID="";
    public static String EdcNewModelName = "";
    public static String MapSerialNo="";
    String solution_type="unmap_edc";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginUnmapEDC() throws Exception {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token  for Unmap EDC : " + AgentToken);
//        establishConnectiontoServer(AgentToken,5);
        waitForLoad(3000);
        /*TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='unmap_edc';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);

    }

    @Test(priority = 1, groups = {"Regression"}, description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcPositiveSendOtpBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        SendOtp v3SendOtp1 = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp1, "UNKNOWN", "company_onboard", AgentToken, version, mobileNo, "company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("Merchant State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
//        Assert.assertEquals(StatusCode,200);
    }

    @Test(priority = 1, description = "Positive Validate OTP for Unmapedc", dependsOnMethods = "UnMapEdcPositiveSendOtpBusiness", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnmapEDCPositiveValidateOtp() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        waitForLoad(2000);
        ValidateOtp validateOtpObj1 = new ValidateOtp(P.TESTDATA.get("ValidateOtpUnmapEDC"));

      //  OTP = getOTP(mobileNo);
        //String OTP=getOTPFromSellerPanel(mobileNo);
        //    OTP="888888";
       // LOGGER.info("This is OTP " + OTP);
        Response validateOtp1 = middlewareServicesObject.v3ValidateOtp(validateOtpObj1, "INDIVIDUAL", "company_onboard", AgentToken, version, mobileNo, "company", State, OTP);
        int StatusCode = validateOtp1.getStatusCode();
        CustId=validateOtp1.jsonPath().getString("custId");
//        Assert.assertEquals(StatusCode, 200);

    }


    @Test(priority = 1, description = "Positive Get Business", groups = {"Regression"}, dependsOnMethods = "UnmapEDCPositiveValidateOtp")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcPositiveGetBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        GetBusinessv3 getBusObj1 = new GetBusinessv3();
        System.out.println("Merchant Cust id is : " + CustId);
        Response getBusResp1 = middlewareServicesObject.v3GetBusiness(getBusObj1, AgentToken, version, CustId);
        kybBusinessId = getBusResp1.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        LOGGER.info("KYB ID is : " + kybBusinessId);
        businessName = getBusResp1.jsonPath().getJsonObject("businesses[0].businessName").toString();
        LOGGER.info("Business Name is : " + businessName);
        int statusCode = getBusResp1.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
        getBusObj1.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");
    }

    @Test(priority = 1, description = "Positive Get Business Profile", groups = {"Regression"}, dependsOnMethods = "UnMapEdcPositiveGetBusiness")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcPositiveGetBusinessProfile() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        BusinessProfile v3BusPro1 = new BusinessProfile();
        System.out.println("Merchant Cust id is : " + CustId);
        System.out.println("Merchant kyb id is : " + kybBusinessId);
        System.out.println("Merchant lead id is : " + companyLeadId);
        Response v3BusProRes1 = middlewareServicesObject.v3BusinessProfile(v3BusPro1, CustId,
                companyLeadId, kybBusinessId, AgentToken, version);
        EntityType = v3BusProRes1.jsonPath().getJsonObject("businessSRO.entityType").toString();
        int StatusCode = v3BusProRes1.getStatusCode();
//        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, description = "Fetch Applicant's MID", groups = {"Regression"},dependsOnMethods = "UnMapEdcPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcPositiveFetchMID() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid1 = new MID();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType", "unmap_edc");
        System.out.println("Merchant Cust id is :" + CustId);
        Response v3FetchMIDResp1 = middlewareServicesObject.v3FetchMID(v3FetchMid1, AgentToken, version, CustId, "EDC");
        if (v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].mid").toString() == null)
        {
            UnMapEdcPositiveFetchMID();
        }
        UserMID = v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].mid").toString();
        LOGGER.info("Applicant's MID is : " + UserMID);
        int statusCode = v3FetchMIDResp1.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
        v3FetchMid1.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }


    @Test(priority = 1, description = "Positive unmap edc create lead", groups = {"Regression"}, dependsOnMethods = "UnMapEdcPositiveFetchMID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcCreateLead() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        CreateUnmapEdcLead CreateUnmapEdcLeadObject = new CreateUnmapEdcLead(P.TESTDATA.get("UnmapEDCCreateLeadRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", EntityType);
        body.put("userCustId", CustId);
        body.put("userMobile", mobileNo);
        body.put("agentCustId","22223");
        body.put("mid", UserMID);
        body.put("wfVersion","V2");
        body.put("solutionType","unmap_edc");
        body.put("serviceReason","Device not working at the time of device delivery");
        body.put("requestType","Replace");
        body.put("kybId", kybBusinessId);
        body.put("businessLeadId", companyLeadId);
        Response getMerchantResponse = middlewareServicesObject.v1UnmapEdcCreateLead(CreateUnmapEdcLeadObject, body, AgentToken, version);
        leadId = getMerchantResponse.jsonPath().getString("leadId");
        int statusCode = getMerchantResponse.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 1, description = "Positive Get Merchant details", groups = {"Regression"}, dependsOnMethods = "UnMapEdcCreateLead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcPositiveGetMerchantDetails() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        GetMerchant getMerchantobj = new GetMerchant(CustId);
        Response getMerchantResponse = middlewareServicesObject.v3GetMerchantUnmapEDC(getMerchantobj, EntityType, "unmap_edc", AgentToken, version, leadId, kybBusinessId);
        int statusCode = getMerchantResponse.getStatusCode();
//        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 1, description = "Fetch unmap edc replace reasons", groups = {"Regression"}, dependsOnMethods = "UnMapEdcPositiveGetMerchantDetails")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void UnMapEdcReplaceReasons() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UnmapEDCReplaceReasons UnmapEDCReplaceReasonsObject = new UnmapEDCReplaceReasons();
        Response getMerchantResponse = middlewareServicesObject.v1UnmapEdcReplaceReasons(UnmapEDCReplaceReasonsObject, AgentToken, version);
        int statusCode = getMerchantResponse.getStatusCode();
//        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 1, description = "Fetch qna for unmap edc",dependsOnMethods = "UnMapEdcReplaceReasons",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void MapEdcFetchQnA() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        FetchQnA v1FetchQnaObj1 = new FetchQnA();

        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType","unmap_edc");
        queryParams.put("entityType",EntityType);
        queryParams.put("questionType","additional");

        Response v1FetchQnaResp1 = middlewareServicesObject.v1FetchQnA(v1FetchQnaObj1,queryParams,AgentToken,version);

        int StatusCode = v1FetchQnaResp1.getStatusCode();
//        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1, description = "Fetch all terminal from pg",dependsOnMethods = "MapEdcFetchQnA",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void fetchAllTerminalFromPG() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        PGToken = ApplicantToken("**********", "paytm@123");
        LOGGER.info("Token for PG : " + PGToken);
        Response res=OnboardBanksONPG(PGToken,UserMID,"HEDC");
        if(res.getStatusCode()==400)
        {
            OnboardBanksONPG(PGToken,UserMID,"HEDC");
        }
     // CreateTerminalInPG(UserMID);
        fetchAllTerminal fetchAllTerminalObject = new fetchAllTerminal();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid",UserMID);
        Response fetchAllTerminalResponse = middlewareServicesObject.v1FetchallTerminal(fetchAllTerminalObject,queryParams,AgentToken,version);
        EdcOldSerialNumber=fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].serialNo").toString();
        EdcOldModelName=fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].modelName").toString();
        EdcOldVendorName=fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].vendorName").toString();
        EdcOldTID=fetchAllTerminalResponse.jsonPath().getJsonObject("response[-1].tid").toString();
        int StatusCode = fetchAllTerminalResponse.getStatusCode();
//        Assert.assertEquals(StatusCode,200);

    }


    @Test(priority = 1,description = "Validate EDC QR to return",groups = {"Regression"} ,dependsOnMethods = "fetchAllTerminalFromPG")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void ValidateEdcQrReplace() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        ActiveTerminalInPG(UserMID,EdcOldSerialNumber,EdcOldTID,EdcOldModelName,"SBPP");
        validateEDCQr validateEDCQrObject=new validateEDCQr();
        Map<String, String> body = new HashMap<String, String>();
        body.put("modelName",EdcOldModelName);
        body.put("oem",EdcOldVendorName);
        body.put("serialNo",EdcOldSerialNumber);
        body.put("leadId",leadId);
        Response getMerchantResponse=middlewareServicesObject.validateEDCQr(validateEDCQrObject,body,AgentToken,version);
        int statusCode = getMerchantResponse.getStatusCode();
//        Assert.assertEquals(statusCode,200);
    }


    @Test(priority = 1, description = "MAP NEW EDC Device",dependsOnMethods = "ValidateEdcQrReplace")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void ReplaceEdcMachineMapping() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        LOGGER.info("Replacing EDC Machine ");

        unmapEDCMachine unmapEDCMachineObject=new unmapEDCMachine(P.TESTDATA.get("ReplaceEDC"));

        if(EdcOldModelName.equalsIgnoreCase("D190") || EdcOldModelName.equalsIgnoreCase("N3"))
        {
            EdcNewModelName=EdcOldModelName;
        }
        else
        {
            LOGGER.info("Old & new Model name are different !!!");
        }

        Integer SerialNo = Utilities.randomNumberGenerator(5);
         MapSerialNo = "JYOT"+SerialNo;
        System.out.println("Generated Serial Number is : "+MapSerialNo);

        Map<String, String> body = new HashMap<String, String>();
        body.put("newmodelName",EdcOldModelName);
        body.put("newoem",EdcOldVendorName);
        body.put("newserialNo",MapSerialNo);
        body.put("oldmodelName",EdcOldModelName);
        body.put("oldoem",EdcOldVendorName);
        body.put("oldserialNo",EdcOldSerialNumber);
        body.put("leadId",leadId);

        Response v1EdcMapNewMachine = middlewareServicesObject.UnmapEDCMachine(unmapEDCMachineObject,body,AgentToken,version);

        int StatusCode = v1EdcMapNewMachine.getStatusCode();

        LOGGER.info("This is Serial No of EDC : " + MapSerialNo);
        LOGGER.info("This is Status Code : " + StatusCode);

        for(int i = 0;i <3;++i)
        {
            LOGGER.info(" Inside Loop and value of counter is : " + i);
            if(StatusCode == 400)
            {
                unmapEDCMachine unmapEDCMachineObject1=new unmapEDCMachine(P.TESTDATA.get("ReplaceEDC"));
                LOGGER.info(" Inside IF condiiton of loop " );

                SerialNo = Utilities.randomNumberGenerator(5);
                MapSerialNo = "JYOTI"+SerialNo;

                Map<String, String> bodyLoop = new HashMap<String, String>();
                body.put("newmodelName",EdcOldModelName);
                body.put("newoem",EdcOldVendorName);
                body.put("newserialNo",MapSerialNo);
                body.put("oldmodelName",EdcOldModelName);
                body.put("oldoem",EdcOldVendorName);
                body.put("oldserialNo",EdcOldSerialNumber);
                body.put("leadId",leadId);

                LOGGER.info(" Calling API inside Loop");
                Response v1EdcMapNewMachineLoop = middlewareServicesObject.UnmapEDCMachine(unmapEDCMachineObject1,bodyLoop,AgentToken,version);
                LOGGER.info(" API has been called inside Loop");
                StatusCode = v1EdcMapNewMachineLoop.getStatusCode();
                LOGGER.info(" Status Code of inside loop : " + StatusCode);

            }
            else
            {
                LOGGER.info(" EDC Machine Replace Successfully ");
                Assert.assertEquals(StatusCode,200);
                break;
            }
        }

//        Assert.assertEquals(StatusCode,200);
    }
}