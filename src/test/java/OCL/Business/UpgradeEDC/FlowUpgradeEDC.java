package OCL.Business.UpgradeEDC;

import Request.MerchantService.LeadInfo.LeadInfo;
import Request.MerchantService.v1.EDC.CreateUnmapEdcLead;
import Request.MerchantService.v1.EDC.FetchEdcUpgradePlans;
import Request.MerchantService.v1.Resources.GetMappedDataByType.UnmapEDCUpgradeReasons;
import Request.MerchantService.v1.merchant.fetchAllTerminal;
import Request.MerchantService.v3.*;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.goldengate.common.RetryAnalyzer;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowUpgradeEDC extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowUpgradeEDC.class);

    public static String AgentToken = "";
    public static String mobileNo = "7770008841";
    public static String version = "5.0.9";
    public static String UserMID = "";
    public static String CustId = "1002359412";
    public static String OTP ="888888";
    public static String State = "";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String EntityType = "";
    public static String solutionType = "edc_device_upgrade";
    public static String OldEdcSerialNumber = "";
    public static String OldEdcModelName = "";
    public static String OldEdcVendorName = "";
    String solution_type="edc_device_upgrade";

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLoginUpgradeEDC() throws Exception
    {
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        LOGGER.info("Agent Token  for Unmap EDC : " + AgentToken);
//        establishConnectiontoServer(AgentToken,5);
        waitForLoad(5000);
        /*TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='edc_device_upgrade';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);

    }

    @Test(priority = 1,groups = {"Regression"},description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_UpgradeEdcPositiveSendOtpBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        SendOtp v3SendOtp1 = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp1,"UNKNOWN","company_onboard",AgentToken,version,mobileNo,"company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("Merchant State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }

    @Test(priority = 1, description = "Positive Validate OTP for upgrade edc", dependsOnMethods = "TC001_UpgradeEdcPositiveSendOtpBusiness", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_UpgradeEDCPositiveValidateOtp() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        waitForLoad(2000);
        ValidateOtp validateOtpObj1 = new ValidateOtp(P.TESTDATA.get("ValidateOtpUnmapEDC"));
       // String OTP=getOTPFromSellerPanel(mobileNo);
      //  LOGGER.info("This is OTP " + OTP);
        Response validateOtp1 = middlewareServicesObject.v3ValidateOtp(validateOtpObj1, "INDIVIDUAL", "company_onboard", AgentToken, version, mobileNo, "company", State, OTP);
      String  CustId = validateOtp1.jsonPath().getString("custId");
        LOGGER.info("Merchant Cust Id is : " + CustId);
        int StatusCode = validateOtp1.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

    }


    @Test(priority = 1,description = "Positive Get Business",groups = {"Regression"},dependsOnMethods = "TC002_UpgradeEDCPositiveValidateOtp", retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_UpgradeEdcPositiveGetBusiness() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        GetBusinessv3 getBusObj1 = new GetBusinessv3();
        System.out.println("Merchant Cust id is : " +CustId);
        Response getBusResp1 = middlewareServicesObject.v3GetBusiness(getBusObj1,AgentToken,version,CustId);
        kybBusinessId = getBusResp1.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        LOGGER.info("KYB ID is : " + kybBusinessId);
        businessName = getBusResp1.jsonPath().getJsonObject("businesses[0].businessName").toString();
        LOGGER.info("Business Name is : " + businessName);
        EntityType = getBusResp1.jsonPath().getJsonObject("businesses[0].entityType").toString();
        LOGGER.info("Entity Name is : " + EntityType);
        int statusCode = getBusResp1.getStatusCode();
        Assert.assertEquals(statusCode,200);
    }

    @Test(priority = 1,description = "Positive Get Business Profile",groups = {"Regression"} ,dependsOnMethods = "TC003_UpgradeEdcPositiveGetBusiness")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_UpgradeEdcPositiveGetBusinessProfile() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        BusinessProfile v3BusPro1 = new BusinessProfile();
        System.out.println("Merchant Cust id is : " +CustId);
        System.out.println("Merchant kyb id is : " +kybBusinessId);
        System.out.println("Merchant lead id is : " +mobileNo);
        Response v3BusProRes1 = middlewareServicesObject.v3BusinessProfile(v3BusPro1,CustId,
                mobileNo,kybBusinessId,AgentToken,version);
        EntityType = v3BusProRes1.jsonPath().getJsonObject("businessSRO.entityType").toString();
        int StatusCode = v3BusProRes1.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }

    @Test(priority = 1,description = "Fetch Applicant's MID",dependsOnMethods ="TC004_UpgradeEdcPositiveGetBusinessProfile",groups = {"Regression"},retryAnalyzer = RetryAnalyzer.class )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_UpgradeEdcPositiveFetchMID() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid1 = new MID();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("solutionType","unmap_edc");
        System.out.println("Merchant Cust id is :" +CustId);
        Response v3FetchMIDResp1 = middlewareServicesObject.v3FetchMID(v3FetchMid1,AgentToken,version,CustId,"EDC");
        if (v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].mid").toString() == null)
        {
            TC005_UpgradeEdcPositiveFetchMID();
        }
        UserMID = v3FetchMIDResp1.jsonPath().getJsonObject("mids[0].mid").toString();
        LOGGER.info("Applicant's MID is : " + UserMID);
        int statusCode = v3FetchMIDResp1.getStatusCode();
        Assert.assertEquals(statusCode,200);
    }

    @Test(priority = 1, description = "Fetch unmap edc upgrade reasons", groups = {"Regression"}, dependsOnMethods = "TC005_UpgradeEdcPositiveFetchMID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC006_UnMapEdcUpgradeReasons() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UnmapEDCUpgradeReasons UnmapEDCUpgradeReasonsObject = new UnmapEDCUpgradeReasons();
        Response getMerchantResponse = middlewareServicesObject.v1UnmapEdcUpgradeReasons(UnmapEDCUpgradeReasonsObject, AgentToken, version);
        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }

    @Test(priority = 1, description = "Positive edc upgrade create lead", groups = {"Regression"}, dependsOnMethods = "TC006_UnMapEdcUpgradeReasons")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC007_UnMapEdcUpgradeCreateLead() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        CreateUnmapEdcLead CreateUnmapEdcLeadObject = new CreateUnmapEdcLead(P.TESTDATA.get("EDCUpgradeCreateLeadRequest"));
        Map<String, String> body = new HashMap<String, String>();
        body.put("entityType", EntityType);
        body.put("userCustId", CustId);
        body.put("agentCustId", "111111111112");
        body.put("userMobile", mobileNo);
        body.put("mid", UserMID);
        body.put("kybId", kybBusinessId);
        Map<String, String> query = new HashMap<String, String>();
        query.put("upgrade","true");
        Response getMerchantResponse = middlewareServicesObject.EdcUpgradeCreateLead(CreateUnmapEdcLeadObject, body, AgentToken, version,query);
        leadId = getMerchantResponse.jsonPath().getString("leadId");
        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }


    @Test(priority = 1, description = "Get merchant details", groups = {"Regression"}, dependsOnMethods = "TC007_UnMapEdcUpgradeCreateLead",retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC008_UnMapEdcUpgradeMerchantDetails() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        GetMerchant getMerchantobj = new GetMerchant(CustId);
            Response getMerchantResponse = middlewareServicesObject.v3GetMerchantUnmapEDC(getMerchantobj, EntityType, solutionType, AgentToken, version, leadId, kybBusinessId);
            int statusCode = getMerchantResponse.getStatusCode();
            Assert.assertEquals(statusCode, 200);
        }

    @Test(priority = 1, description = "Fetch all terminal from pg",dependsOnMethods = "TC008_UnMapEdcUpgradeMerchantDetails",groups = {"Regression"}, retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_fetchAllTerminalFromPG() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        fetchAllTerminal fetchAllTerminalObject = new fetchAllTerminal();
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("mid",UserMID);
        Response fetchAllTerminalResponse = middlewareServicesObject.v1FetchallTerminal(fetchAllTerminalObject,queryParams,AgentToken,version);
//        OldEdcSerialNumber=fetchAllTerminalResponse.jsonPath().getJsonObject("response[0].serialNo").toString();
//        OldEdcModelName=fetchAllTerminalResponse.jsonPath().getJsonObject("response[0].modelName").toString();
//        OldEdcVendorName=fetchAllTerminalResponse.jsonPath().getJsonObject("response[0].vendorName").toString();
//        int StatusCode = fetchAllTerminalResponse.getStatusCode();
//        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 1, description = "Get fetch plan for edc upgrade", groups = {"Regression"}, dependsOnMethods = "TC009_fetchAllTerminalFromPG")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC010_EDCUpgradeFetchPlans() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        FetchEdcUpgradePlans FetchEdcUpgradePlansObj = new FetchEdcUpgradePlans();
        Map<String, String> query = new HashMap<String, String>();
        query.put("leadId","true");
        query.put("deviceType","ANDROID");
        query.put("deviceModel",OldEdcModelName);
        query.put("serialNo",OldEdcSerialNumber);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token",AgentToken);
        headers.put("version",version);
        Response getMerchantResponse = middlewareServicesObject.EdcUpgradeFetchPlan(FetchEdcUpgradePlansObj,headers,query);
        int statusCode = getMerchantResponse.getStatusCode();
        Assert.assertEquals(statusCode, 200);
    }

    @Test(priority = 1, description = "Get Lead Info", groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC011_EDCUpgradeGETdeviceInfo() throws Exception
    {
        LeadInfo leadInfo=new LeadInfo(P.TESTDATA.get("GETleadInfo"));
        Map<String, String> query = new HashMap<String, String>();
        Map<String, String> headers= new HashMap<String, String>();
        query.put("leadId",leadId);
        Response res=middlewareServicesObject.EdcUpgradeGetleadInfo(leadInfo,headers,query);
        int statusCode = res.getStatusCode();
        Assert.assertEquals(statusCode, 200);

    }




    }


