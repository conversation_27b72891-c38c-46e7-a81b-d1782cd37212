package OCL.Business.OrganisedMerchant;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.fileProcess.fileUpload_BusinessStatus;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.panel.v1.business.CompanyOnboardPanel;
import Request.MerchantService.panel.v1.business.FetchPanPanel;
import Request.MerchantService.panel.v1.business.PanelPennyDrop;
import Request.MerchantService.panel.v1.diy.businessprofile.FetchBusinessProfilePanel;
import Request.MerchantService.panel.v1.solution.lead.CreateSolutionPanel;
import Request.MerchantService.panel.v1.solution.lead.UpdateSolutionPanel;
import Request.MerchantService.panel.v2.solution.VerifyDocument;
import Request.MerchantService.v2.lead.v2FetchDynamicTnc;
import Request.MerchantService.v2.lead.v2SaveDynamicTnc;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

public class FlowOrganisedOfflineMerchantWithCMT extends BaseMethod
{
    private static final Logger LOGGER = LogManager.getLogger(FlowOrganisedOfflineMerchantWithCMT.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    Faker GenerateFake = new Faker();

    public String lineOne = GenerateFake.space().planet();
    public String lineTwo = GenerateFake.space().star();
    public String lineThree = GenerateFake.space().constellation();
    public String lat = "28.605410";
    public String longi = "77.341996";
    public String XMWTokenPanel = "";
    public String PAN = "";
    public String mobileNo = "";
    public String CompLeadId = "";
    public String OrgParentLeadId = "";
    public String OrgChildLeadId = "";
    public String KybId = "";
    public String GSTIN = "";
    public String entity = "HINDU_UNDIVIDED_FAMILY";
    public String OePanelDocStatus = "REJECTED";
    public String RejectionReason = "Wrong Photo Provided";
    public String DocumetRequestDeserialised = "";
    public String WorkFlowId = "";
    public static String MerchCustId = "";
    public static String XMWTokenTnC = "";
    public static File fileUpload = new File("FileUpload.csv");

    @BeforeClass
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void BeforeClassData()
    {
        Response responseObject=middlewareServicesObject.v1Token("7771119000","paytm@123");
        XMWTokenPanel=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWTokenPanel);
        //XMWTokenPanel="X-MW-TOKEN=dfb0452b-5f36-4325-93d7-5776d1ce2630";
        //XMWCookie=XMWTokenPanel;

        switch (entity) {
            case "PUBLIC_LIMITED":
            case "PRIVATE_LIMITED": {
                LOGGER.info("Entity is : " + entity);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomPublicPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "TRUST": {
                LOGGER.info("Entity is : " + entity);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomTrustPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "SOCIETY_ASSOCIATION_CLUB": {
                LOGGER.info("Entity is : " + entity);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomSocietyPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "PARTNERSHIP": {
                LOGGER.info("Entity is : " + entity);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomPartnershipPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "HINDU_UNDIVIDED_FAMILY": {
                LOGGER.info("Entity is : " + entity);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomHUFPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
        }

        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number for ORG Merchant : " + mobileNo);

    }
    @Test(description = "Fetching Pan Details",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_FetchPanDetails()
    {
        FetchPanPanel fetchPan = new FetchPanPanel();

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("pan", PAN);
        queryParams.put("channel", "OE_PANEL");

        Response fetchPanResp = middlewareServicesObject.FetchPanPanel(fetchPan,queryParams,XMWTokenPanel);

        int StatusCode = fetchPanResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }

    @Test(description = "Onboarding Company",dependsOnMethods = "TC001_FetchPanDetails",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_CompanyOnboard()
    {
        CompanyOnboardPanel getBus = new CompanyOnboardPanel();

        getBus.getProperties().setProperty("pan",PAN);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("entityType", entity);
        queryParams.put("channel", "OE_PANEL");

        Response getBusResp = middlewareServicesObject.CompanyOnboardPanel(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        CompLeadId = getBusResp.jsonPath().getJsonObject("leadId").toString();
        LOGGER.info("Company Lead Id : " +CompLeadId);
    }

    @Test(description = "Fetching Business Profile",dependsOnMethods = "TC002_CompanyOnboard",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_FetchBusinessProfile()
    {
        FetchBusinessProfilePanel getBus = new FetchBusinessProfilePanel();


        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("merchantCustId", "**********");
        queryParams.put("leadId", CompLeadId);
        queryParams.put("channel", "OE_PANEL");

        Response getBusResp = middlewareServicesObject.FetchBusinessProfilePanel(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        KybId = getBusResp.jsonPath().getJsonObject("businessSRO.kybBusinessId").toString();
        LOGGER.info("Company KYB Id : " +KybId);
    }

    @Test(description = "Creating Organised Merchant Application",dependsOnMethods = "TC003_FetchBusinessProfile",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_CreateSolution()
    {
        CreateSolutionPanel getBus = new CreateSolutionPanel(P.TESTDATA.get("CreateOrgMerchant"));

        getBus.getProperties().setProperty("mobileNumber",mobileNo);
        getBus.getProperties().setProperty("email",mobileNo);
        getBus.getProperties().setProperty("line1",lineOne);
        getBus.getProperties().setProperty("line2",lineTwo);
        getBus.getProperties().setProperty("line3",lineThree);
        getBus.getProperties().setProperty("BUSINESS_SPOC_EMAIL",mobileNo);
        getBus.getProperties().setProperty("BUSINESS_SPOC_NUMBER",mobileNo);
        getBus.getProperties().setProperty("kybId",KybId);
        getBus.getProperties().setProperty("leadId",CompLeadId);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("entityType", entity);
        queryParams.put("solution", "payments_organised_merchant");
        queryParams.put("channel", "OE_PANEL");

        Response getBusResp = middlewareServicesObject.CreateSolutionPanel(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        OrgParentLeadId = getBusResp.jsonPath().getJsonObject("leadId").toString();
        LOGGER.info("Organised Merchant Application Lead Id : " +OrgParentLeadId);
    }

    @Test(description = "Verifying GSTIN",dependsOnMethods = "TC004_CreateSolution",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_VerifyGstin()
    {
        VerifyDocument getBus = new VerifyDocument("payments_organised_merchant");

        getBus.getProperties().setProperty("gstin",GSTIN);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("entityType", entity);
        queryParams.put("solution", "payments_organised_merchant");
        queryParams.put("channel", "OE_PANEL");

        Response getBusResp = middlewareServicesObject.VerifyDocumentPanel(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        //Assert.assertEquals(StatusCode,200);


    }

    @Test(description = "Updating Organised Merchant Application",dependsOnMethods = "TC005_VerifyGstin",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_UpdatingLead()
    {
        UpdateSolutionPanel getBus = new UpdateSolutionPanel(P.TESTDATA.get("UpdateOrgMerchant"));

        getBus.getProperties().setProperty("gstin",GSTIN);
        getBus.getProperties().setProperty("cline1",lineOne);
        getBus.getProperties().setProperty("cline2",lineTwo);
        getBus.getProperties().setProperty("cline3",lineThree);
        getBus.getProperties().setProperty("bline1",lineOne);
        getBus.getProperties().setProperty("bline2",lineTwo);
        getBus.getProperties().setProperty("bline3",lineThree);
        getBus.getProperties().setProperty("ADDRESS_PROOF_NUMBER",mobileNo);
        getBus.getProperties().setProperty("entityType","PUBLIC_LIMITED");
        getBus.getProperties().setProperty("leadId",OrgParentLeadId);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("entityType", entity);
        queryParams.put("solution", "payments_organised_merchant");
        queryParams.put("channel", "OE_PANEL");

        Response getBusResp = middlewareServicesObject.UpdateSolutionPanel(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String expectedMsg = "Your details are saved successfully.You can continue application and submit remaining details.";
        String actualMsg = getBusResp.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertTrue(expectedMsg.contains(actualMsg));
    }

    @Test(description = "Creating Organised Merchant Product for Offline with CMT",dependsOnMethods = "TC006_UpdatingLead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_CreateChildSolution()
    {
        CreateSolutionPanel getBus = new CreateSolutionPanel(P.TESTDATA.get("CreateOfflineWithCmt"));

        getBus.getProperties().setProperty("kybId",KybId);
        getBus.getProperties().setProperty("leadId",CompLeadId);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("entityType", entity);
        queryParams.put("solution", "organised_merchant_product");
        queryParams.put("channel", "OE_PANEL");

        Response getBusResp = middlewareServicesObject.CreateSolutionPanel(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        OrgChildLeadId = getBusResp.jsonPath().getJsonObject("leadId").toString();
        LOGGER.info("Organised Merchant Application Lead Id : " +OrgChildLeadId);
    }

    @Test(description = "Performing PennyDrop ",dependsOnMethods = "TC007_CreateChildSolution",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_PennyDrop()
    {
        PanelPennyDrop getBus = new PanelPennyDrop();

        getBus.getProperties().setProperty("mobile",mobileNo);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("bankName", "INDUSIND BANK");
        queryParams.put("solution", "organised_merchant_product");
        queryParams.put("names", "Aashit Sharma");
        queryParams.put("leadId", OrgChildLeadId);

        Response getBusResp = middlewareServicesObject.PanelPennyDrop(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        Assert.assertEquals(StatusCode,200,500);

    }

    @Test(description = "Updating Organised Merchant Product for Offline with CMT",dependsOnMethods = "TC007_CreateChildSolution",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_UpdatingChildLead()
    {
        UpdateSolutionPanel getBus = new UpdateSolutionPanel(P.TESTDATA.get("UpdateOfflineMerchantWithCmt"));

        getBus.getProperties().setProperty("bankAccountNumber",mobileNo);
        getBus.getProperties().setProperty("mobile",mobileNo);
        getBus.getProperties().setProperty("entityType",entity);
        getBus.getProperties().setProperty("leadId",OrgChildLeadId);

        Map<String, String> queryParams = new HashMap<String, String>();

        queryParams.put("entityType", "PUBLIC_LIMITED");
        queryParams.put("solution", "organised_merchant_product");
        queryParams.put("channel", "OE_PANEL");

        Response getBusResp = middlewareServicesObject.UpdateSolutionPanel(getBus,queryParams,XMWTokenPanel);

        int StatusCode = getBusResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String expectedMsg = "Your details are saved successfully.You can continue application and submit remaining details.";
        String actualMsg = getBusResp.jsonPath().getJsonObject("displayMessage").toString();
        Assert.assertTrue(expectedMsg.contains(actualMsg));
    }

    @Test(description = "Uploading Documents",dependsOnMethods = "TC009_UpdatingChildLead",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0010_UploadingDocs() throws JsonProcessingException, InterruptedException
    {
        File UploadDoc = new File(System.getProperty("user.dir") + "/PaytmImage.jpg");

        Map<String,String> queryParams=new HashMap<String,String>();
        queryParams.put("entityType",entity );
        queryParams.put("solution","organised_merchant_product");
        queryParams.put("channel","OE_PANEL");
        queryParams.put("leadId",OrgChildLeadId);

        Map<String,String> headers=new HashMap<String,String>();
        headers.put("cookie",XMWTokenPanel);
        headers.put("phonenumber","**********");
        headers.put("ssoid","**********");
        headers.put("accept","application/json, text/plain, */*");
        headers.put("content-type","application/json;charset=UTF-8");

        Map<String,String> queryDoc=new HashMap<String,String>();
        queryDoc.put("entityType",entity);
        queryDoc.put("solutionType","organised_merchant_product");
        queryDoc.put("leadId",OrgChildLeadId);
        queryDoc.put("merchantCustId","");

        Map<String,String> headersDoc=new HashMap<String,String>();
        headersDoc.put("Content-Type", "multipart/form-data");
        headersDoc.put("cookie",XMWTokenPanel);
        headersDoc.put("phonenumber","**********");
        headersDoc.put("ssoid","**********");
        headersDoc.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

        PanelMultipartDocUpload(queryParams,headers,queryDoc,headersDoc,UploadDoc);
    }

    @Test(description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC0010_UploadingDocs")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0011_PositiveFetchLeadPanel()
    {

        try
        {
            Map <String,String> RequestPanel = new HashMap<>();
            Map <String,String> ResponsePanel = new HashMap<>();

            RequestPanel.put("docStatus",OePanelDocStatus);
            RequestPanel.put("rejectionReason",RejectionReason);
            RequestPanel.put("leadId",OrgChildLeadId);


            ResponsePanel = FetchPanelLead(RequestPanel);

            DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
            WorkFlowId = ResponsePanel.get("WorkFlowId");
            LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

        }
        catch (Exception e)
        {
            LOGGER.info("Execption " + e);
            LOGGER.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());

        }
    }

    @Test(description = "Submit rejected lead from OE panel ",groups = {"Regression"},dependsOnMethods = "TC0011_PositiveFetchLeadPanel",enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0012__PositiveRejectedLeadPanel()
    {
        waitForLoad(4000);
        ReallocatingAgent(OrgChildLeadId,"1152");

        EditLead v1EditLeadObj=new EditLead(OrgChildLeadId, P.TESTDATA.get("EditLeadUnlimitedNameMatchFailedRejected"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWCookie,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("QC_REJECTED"));
    }

    @Test(description = "Positive Submit Lead Details after Rejection",dependsOnMethods ="TC0012__PositiveRejectedLeadPanel",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0013__PositiveSubmitLeadPanel() throws JsonProcessingException, InterruptedException
    {
        waitForLoad(6000);
        OePanelDocStatus = "APPROVED";
        RejectionReason = null;
        TC009_UpdatingChildLead();
        TC0010_UploadingDocs();
        TC0011_PositiveFetchLeadPanel();
        ReallocatingAgent(OrgChildLeadId,"1152");
        EditLead v1EditLeadObj=new EditLead(OrgChildLeadId, P.TESTDATA.get("EditLeadOrganisedPositive"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWCookie,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));
    }
    @Test(description = "Positive registere email Id",dependsOnMethods = "TC0013__PositiveSubmitLeadPanel",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0019_PostiveRegisterEmail() throws IOException, JSchException, InterruptedException {

        //RegisterEmail(mobileNo,"LAMDA");
        RegisterEmail(mobileNo,"LOCAL");
        waitForLoad(5000);
        MerchCustId = FetchUserDetails(mobileNo+"@yopmail.com","email");
        Assert.assertFalse(MerchCustId.isEmpty());

    }

    @Test(description = "Accepting TnC",dependsOnMethods = "TC0019_PostiveRegisterEmail",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0020_AcceptingTnC()
    {
        Response responseObject=middlewareServicesObject.v1Token(mobileNo+"@yopmail.com","paytm@123");
        XMWTokenTnC=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info("XMW Token for TnC is : " +XMWTokenTnC);

        v2FetchDynamicTnc getTnc = new v2FetchDynamicTnc();

        Map<String,String > queryParamGet = new HashMap<>();
        queryParamGet.put("leadId",OrgChildLeadId);

        Response FetchTnC = middlewareServicesObject.v2FetchDynamicTnc(getTnc,queryParamGet,XMWTokenTnC,MerchCustId);

        String AgreementURL = FetchTnC.jsonPath().getJsonObject("agreementURL").toString();
        String Identifier = FetchTnC.jsonPath().getJsonObject("identifier").toString();
        String Md5 = FetchTnC.jsonPath().getJsonObject("md5Key").toString();

        LOGGER.info("Agreement URL : " + AgreementURL + "\n" + "Identifier : " + Identifier + "\n" + "MD5 Key : " + Md5);

        v2SaveDynamicTnc saveTnc = new v2SaveDynamicTnc();

        Map<String,String > queryParamsave = new HashMap<>();
        queryParamsave.put("leadId",OrgChildLeadId);
        queryParamsave.put("md5",Md5);
        queryParamsave.put("identifier",Identifier);

        Response SaveTnC = middlewareServicesObject.v2SaveDynamicTnc(saveTnc,queryParamsave,XMWTokenTnC,MerchCustId);

        int statusCode = SaveTnC.getStatusCode();

        if (statusCode ==500)
        {
            v2SaveDynamicTnc saveTncErr = new v2SaveDynamicTnc();

            Map<String,String > queryParamsaveErr = new HashMap<>();
            queryParamsaveErr.put("leadId",OrgChildLeadId);
            queryParamsaveErr.put("md5",Md5);
            queryParamsaveErr.put("identifier",Identifier);

            Response SaveTnCErr = middlewareServicesObject.v2SaveDynamicTnc(saveTncErr,queryParamsave,XMWTokenTnC,MerchCustId);

            Assert.assertEquals("Congratulations! You have successfully accepted the Terms and Conditions.",SaveTnCErr.jsonPath().getJsonObject("displayMessage"));

        }
        else
        {
            Assert.assertEquals("Congratulations! You have successfully accepted the Terms and Conditions.",SaveTnC.jsonPath().getJsonObject("displayMessage"));

        }

    }

    @Test(description = "Manual PG CallBack",dependsOnMethods = "TC0020_AcceptingTnC",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0021_ManualPgCallBack()
    {
        PgCallBackFromPanelRef(OrgChildLeadId,"","");
    }

    @Test(description = "Accepting SAP",dependsOnMethods = "TC0020_AcceptingTnC",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0022_PositiveSubmitSap() throws IOException
    {
        waitForLoad(5000);
        FetchLead v1FetchLeadObj = new FetchLead(OrgChildLeadId);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
        String LeadSatge = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();

        if(LeadSatge.equals("SAP_ACTION_PENDING"))
        {
            waitForLoad(4000);
            ReallocatingAgent(OrgChildLeadId, "1152");
            fileUpload_BusinessStatus fileUpload_Status = new fileUpload_BusinessStatus();

            // Create csv file
            FileWriter outputfile = new FileWriter(fileUpload);

            // Write to CSV file which is open
            CSVWriter writer = new CSVWriter(outputfile);

            String[] header = {"LEAD_ID", "SOLUTION", "SAP Code", "Reject Reason"};
            writer.writeNext(header);

            // add data to csv
            String[] data1 = {OrgChildLeadId, "organised_merchant_product", PAN, "APPROVED"};
            writer.writeNext(data1);
            writer.flush();
            writer.close();

            BufferedReader in = new BufferedReader(new FileReader(fileUpload));
            LOGGER.info("Reading Lines from File ");
            String line;
            while((line = in.readLine()) != null)
            {
                LOGGER.info(line);
            }
            in.close();

            Response responseObject = middlewareServicesObject.fileUpload_BusinessStatus(fileUpload_Status, "multipart/form-data", XMWCookie, fileUpload, "SAP_CODE_UPLOAD");

            int StatusCode = responseObject.getStatusCode();
            LOGGER.info("Status Code is " + StatusCode);

            if (StatusCode == 200) {
                String actualMsg = responseObject.jsonPath().getJsonObject("fileStatuses[0].statusMessage");
                String expectedMsg = " has been successfully uploaded";
                Assert.assertTrue(actualMsg.contains(expectedMsg));
                LOGGER.info("File Uploaded ");

            } else {
                LOGGER.info("File not  Uploaded ");
            }
        }
        else
        {
            LOGGER.info("Lead is not in SAP_ACTION_PENDING Stage");
        }
    }

    @Test(description = "Submiting CMT Staging",dependsOnMethods = "TC0022_PositiveSubmitSap",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0023_PositiveSubmitCmtStaging()
    {
        waitForLoad(3000);
        ReallocatingAgent(OrgChildLeadId,"1152");

        FetchLead v1FetchLeadObj = new FetchLead(OrgChildLeadId);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWTokenPanel);
        String WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();


        EditLead v1EditLeadObj=new EditLead(OrgChildLeadId, P.TESTDATA.get("EditLeadCmtStaging"));

        v1EditLeadObj.getProperties().setProperty("mobileNumber",mobileNo);
        v1EditLeadObj.getProperties().setProperty("emailId",mobileNo+"@yopmail.com");
        v1EditLeadObj.getProperties().setProperty("promoCode",PAN);
        v1EditLeadObj.getProperties().setProperty("stagingClientId",GSTIN);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWCookie,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("CMT_PRODUCTION_ALLOCATION_PENDING"));
    }

    @Test(description = "Submiting CMT Production",dependsOnMethods = "TC0023_PositiveSubmitCmtStaging",groups = {"Regression"},enabled = true)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0024_PositiveSubmitCmtProduction()
    {
        waitForLoad(3000);
        ReallocatingAgent(OrgChildLeadId,"1152");

        FetchLead v1FetchLeadObj = new FetchLead(OrgChildLeadId);
        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWTokenPanel);
        String WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();


        EditLead v1EditLeadObj=new EditLead(OrgChildLeadId, P.TESTDATA.get("EditLeadCmtProduction"));
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);
        v1EditLeadObj.getProperties().setProperty("productionClientId",WorkFlowId+PAN);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWCookie,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

       /* String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("BANKING_MODULE_LEAD_CREATION_JOB"));*/
    }
}

