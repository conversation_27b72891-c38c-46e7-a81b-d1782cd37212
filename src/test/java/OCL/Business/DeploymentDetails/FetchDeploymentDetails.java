package OCL.Business.DeploymentDetails;


import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FetchDeploymentDetails extends BaseMethod
{
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FetchDeploymentDetails.class);
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";

    String sessiontoken;
    String agentvar = "7771116065";//

    // sessiontoken = "b4b4506b-7903-47de-88df-9d6c8c955600";
    Map<String, String> headers = new HashMap<String, String>();

    @BeforeClass
    public void beforeclassoperation()
    {

        sessiontoken = ApplicantToken(agentvar, "paytm@123");
        headers.put("deviceidentifier", "OnePlus-CPH2487-b0239246dc8dc4ee");
        headers.put("session_token", sessiontoken);
        headers.put("authority", "goldengate-staging5.paytm.com");
        headers.put("accept", "application/json, text/plain, */*");

    }

    @Test(priority = 0,description = "Checking status code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_StatusCode()
    {
        Map<String, String> queryparams = new HashMap<String, String>();
        queryparams.put("leadId", "d3f864d3-972c-46b4-bdac-63e2a793d572");
        queryparams.put("serialNo", "AERJ399S099991");
        Response respObj = MiddlewareServicesObject.fetchdeploymentdetailsMethod(headers, queryparams);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Business Address Details : " + respObj.jsonPath().getString("allBusinessAddressDetailsSet[0]"));

    }



    @Test(priority = 0,description = "Checking without leadID parameter")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_MissingLeadID()
    {
        Map<String, String> queryparams = new HashMap<String, String>();
        queryparams.put("serialNo", "AERJ399S099991");
        Response respObj = MiddlewareServicesObject.fetchdeploymentdetailsMethod(headers, queryparams);
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0,description = "Checking with an invalid LeadID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_InvalidleadID()
    {
        Map<String, String> queryparams = new HashMap<String, String>();
        queryparams.put("leadId", "d3f864d3-9acv23-23321nxc-72");
        queryparams.put("serialNo", "AERJ399S099991");
        Response respObj = MiddlewareServicesObject.fetchdeploymentdetailsMethod(headers, queryparams);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));
        Assert.assertEquals(respObj.statusCode(), 500);
    }

    @Test(priority = 0,description = "Checking with only invalid LeadID")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_onlyInvalidleadID()
    {
        Map<String, String> queryparams = new HashMap<String, String>();
        queryparams.put("leadId", "d3f864d3-9acv23-23321nxc-72");
       // queryparams.put("serialNo", "AERJ399S099991");
        Response respObj = MiddlewareServicesObject.fetchdeploymentdetailsMethod(headers, queryparams);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));
        Assert.assertEquals(respObj.statusCode(), 400);
    }

    @Test(priority = 0,description = "Giving invalid session token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_Invalidsessiontoken()
    {
        headers.put("session_token", "b4b4506b-7903-47de-8");
        Map<String, String> queryparams = new HashMap<String, String>();
        queryparams.put("leadId", "d3f864d3-972c-46b4-bdac-63e2a793d572");
        queryparams.put("serialNo", "AERJ399S099991");
        Response respObj = MiddlewareServicesObject.fetchdeploymentdetailsMethod(headers, queryparams);
        Assert.assertEquals(respObj.statusCode(), 410);

    }

    @Test(priority = 0,description = "missing session token")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_Missingsessiontoken()
    {
        headers.remove("session_token");
        Map<String, String> queryparams = new HashMap<String, String>();
        queryparams.put("leadId", "d3f864d3-972c-46b4-bdac-63e2a793d572");
        queryparams.put("serialNo", "AERJ399S099991");
        Response respObj = MiddlewareServicesObject.fetchdeploymentdetailsMethod(headers, queryparams);
        Assert.assertEquals(respObj.statusCode(), 401);
    }


    @Test(priority = 0,description = "without device Identifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_missingdeviceIdentifier()
    {
        headers.put("session_token", sessiontoken);
        headers.remove("deviceidentifier");
        Map<String, String> queryparams = new HashMap<String, String>();
        queryparams.put("leadId", "d3f864d3-972c-46b4-bdac-63e2a793d572");
        queryparams.put("serialNo", "AERJ399S099991");
        Response respObj = MiddlewareServicesObject.fetchdeploymentdetailsMethod(headers, queryparams);
        Assert.assertEquals(respObj.statusCode(), 410);
    }


}
