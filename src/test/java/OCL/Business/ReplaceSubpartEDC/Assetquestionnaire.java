package OCL.Business.ReplaceSubpartEDC;

import Request.SoundBox.FetchDeviceQuestions;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class Assetquestionnaire extends BaseMethod
{
	MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
	private static final Logger LOGGER = LogManager.getLogger(EDCreplacesubpartassetcreatelead.class);
	public static String clientId = "OE";
	public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
	//String token = findXMWExTokenforPanel("8010630022", "paytm@123");
	//String sessiontoken = ApplicantToken("8010630022", "paytm@123");
	String sessiontoken = AgentSessionToken("9891497839", "paytm@123");

	String MobileNo = "9999111753";
	@Test(priority = 0, description = "fetching the Charger Question for the Device")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_001_FetchChargerDeviceQuestion()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "charger");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "NEW565A50EDC753");
		queryParams.put("modelName", "A50");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("session_token", sessiontoken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 200);

	}

	@Test(priority = 0, description = "fetching the Battery Question for the Device")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_002_FetchBatteryDeviceQuestion()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "battery");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "NEW565A50EDC753");
		queryParams.put("modelName", "A50");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("session_token", sessiontoken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 200);

	}

	@Test(priority = 0, description = "fetching the other device asset Question for the Device")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_003_FetchDeviceotherassetQuestion()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "cable");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "NEW565A50EDC753");
		queryParams.put("modelName", "A50");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("session_token", sessiontoken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 200);

	}

	@Test(priority = 0, description = "fetching without device information")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_004_missingdeviceinfo()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "battery");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "");
		queryParams.put("modelName", "");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("session_token", sessiontoken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 500);
		LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));
	}

	@Test(priority = 0, description = "fetching questions without device serial number")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_005_noserialnumber()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "cable");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "");
		queryParams.put("modelName", "A50");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("session_token", sessiontoken);
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 500);
		LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));
	}

	@Test(priority = 0, description = "without session token")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_006_withoutsessiontoken()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "battery");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "NEW565A50EDC753");
		queryParams.put("modelName", "A50");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("session_token", "");
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
	}
	@Test(priority = 0, description = "missing session token")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_007_missingsessiontoken()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "battery");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "NEW565A50EDC753");
		queryParams.put("modelName", "A50");

		Map<String, String> headers = new HashMap<String, String>();
		headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
		headers.put("content-type", "application/json; charset=UTF-8");
		headers.put("accept", "application/json, text/plain, */*");
		headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
	}

	@Test(priority = 0, description = "without sesssion token")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_008_missingheaders()
	{

		FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("assetSubType", "battery");
		queryParams.put("solutionType", "asset_replacement");
		queryParams.put("solutionSubType", "edc");
		queryParams.put("serialNo", "NEW565A50EDC753");
		queryParams.put("modelName", "A50");

		Map<String, String> headers = new HashMap<String, String>();

		Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
		Assert.assertEquals(respObj.statusCode(), 200);
	}


}