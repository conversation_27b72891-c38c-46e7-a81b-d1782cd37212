package OCL.Business.ReplaceSubpartEDC;

import Request.CommonOnboardingEDC.sendOTPLead;
import Request.EDCAssetReplace.*;
import Request.SoundBox.FetchDeviceQuestions;
import Request.SoundBox.UpdateAssetAnswers;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class EDCAssetReplaceFlow extends BaseMethod
{

		MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
		private static final Logger LOGGER = LogManager.getLogger(EDCreplacesubpartassetcreatelead.class);
		public static String clientId = "OE";
		public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
		//String token = findXMWExTokenforPanel("8010630022", "paytm@123");
		//String sessiontoken = ApplicantToken("8010630022", "paytm@123");
		String sessiontoken = AgentSessionToken("9891497839", "paytm@123");

		String MobileNo = "9999111753";
		@Test(priority = 0, description = "Creating Lead for asset replace flow")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_001_create_lead()
		{

			Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			// headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

			Map<String, Object> body = new HashMap<String, Object>();
			body.put("solutionType", "asset_replacement");
			body.put("solutionTypeLevel2", "edc");
			body.put("edcQrScanDetailsString", "{modelName:A50,serialNo:NEW565A50EDC753,osType:ANDROID}");
			body.put("userMobile", "9999111753");
			body.put("mid", "ADfyin30849684056371");
			body.put("entityType", "PROPRIETORSHIP");
			body.put("merchantName", "TOUCH WOOD LIMITED");
			body.put("userCustId", "1700943239");
			body.put("agentCustId", "1107195733");
			body.put("solutionSubType", "edc");

			Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
			Assert.assertEquals(respObj.statusCode(), 200);

		}

		@Test(priority = 0, description = "fetching the Battery Question for the Device")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_002_FetchBatteryDeviceQuestion()
		{

			FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("assetSubType", "battery");
			queryParams.put("solutionType", "asset_replacement");
			queryParams.put("solutionSubType", "edc");
			queryParams.put("serialNo", "NEW565A50EDC753");
			queryParams.put("modelName", "A50");

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
			Assert.assertEquals(respObj.statusCode(), 200);

		}

		@Test(priority = 0, description = "battery Question Screen")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_003_BatteryQnA()
		{

			UpdateAssetAnswers obj = new UpdateAssetAnswers();

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Map<String, String> body = new HashMap<String, String>();
			body.put("question", "batteryAvailable");
			body.put("answer", "yes");
			body.put("question2", "batteryWorking");
			body.put("answer2", "no");
			body.put("assetAction", "BATTERY_QNA_SCREEN");
			body.put("userMobile", "9999111753");
			body.put("mid", "ADfyin30849684056371");
			body.put("entityType", "PROPRIETORSHIP");
			body.put("leadId", "36fd7e90-6310-41b1-8425-f694aa3701dd");
			body.put("merchantName", "TOUCH WOOD LIMITED");
			body.put("userCustId", "1700943239");
			body.put("agentCustId", "1107195733");
			body.put("solutionSubType", "edc");

			Response respObj = MiddlewareServicesObject.updateAssetsAnswer(obj,body,headers);
			Assert.assertEquals(respObj.statusCode(), 200);

		}


		@Test(priority = 0, description = "Scan old battery")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_004_ScanOldBattery()
		{

			ScanassetQR obj = new ScanassetQR();

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Map<String, String> body = new HashMap<String, String>();
			body.put("assetAction", "SCAN_OLD_BATTERY");
			body.put("deviceId", "abcd61");
			body.put("userMobile", "9999111753");
			body.put("mid", "ADfyin30849684056371");
			body.put("entityType", "PROPRIETORSHIP");
			body.put("leadId", "36fd7e90-6310-41b1-8425-f694aa3701dd");
			body.put("merchantName", "TOUCH WOOD LIMITED");
			body.put("userCustId", "1700943239");
			body.put("agentCustId", "1107195733");
			body.put("solutionSubType", "edc");

			Response respObj = MiddlewareServicesObject.ScanassetQR(obj,headers,body);
			Assert.assertEquals(respObj.statusCode(), 200);

		}


		@Test(priority = 0, description = "Scan new battery")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_005_ScannewBattery()
		{

			ScanassetQR obj = new ScanassetQR();

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Map<String, String> body = new HashMap<String, String>();
			body.put("assetAction", "SCAN_NEW_BATTERY");
			body.put("deviceId", "abcd62");
			body.put("userMobile", "9999111753");
			body.put("mid", "ADfyin30849684056371");
			body.put("entityType", "PROPRIETORSHIP");
			body.put("leadId", "36fd7e90-6310-41b1-8425-f694aa3701dd");
			body.put("merchantName", "TOUCH WOOD LIMITED");
			body.put("userCustId", "1700943239");
			body.put("agentCustId", "1107195733");
			body.put("solutionSubType", "edc");

			Response respObj = MiddlewareServicesObject.ScanassetQR(obj,headers,body);
			Assert.assertEquals(respObj.statusCode(), 200);

		}

		@Test(priority = 0, description = "fetching the Charger Question for the Device")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_006_FetchChargerDeviceQuestion()
		{

			FetchDeviceQuestions reqobj = new FetchDeviceQuestions();

			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("assetSubType", "charger");
			queryParams.put("solutionType", "asset_replacement");
			queryParams.put("solutionSubType", "edc");
			queryParams.put("serialNo", "NEW565A50EDC753");
			queryParams.put("modelName", "A50");

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Response respObj = MiddlewareServicesObject.fetchDeviceQuestions(reqobj,queryParams,headers);
			Assert.assertEquals(respObj.statusCode(), 200);

		}


		@Test(priority = 0, description = "Charger Question Screen")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_007_ChargerQnA()
		{

			UpdateAssetAnswers obj = new UpdateAssetAnswers();

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Map<String, String> body = new HashMap<String, String>();
			body.put("question", "chargerAvailable");
			body.put("answer", "yes");
			body.put("question2", "chargerWorking");
			body.put("answer2", "no");
			body.put("assetAction", "CHARGER_QNA_SCREEN");
			body.put("userMobile", "9999111753");
			body.put("mid", "ADfyin30849684056371");
			body.put("entityType", "PROPRIETORSHIP");
			body.put("leadId", "36fd7e90-6310-41b1-8425-f694aa3701dd");
			body.put("merchantName", "TOUCH WOOD LIMITED");
			body.put("userCustId", "1700943239");
			body.put("agentCustId", "1107195733");
			body.put("solutionSubType", "edc");

			Response respObj = MiddlewareServicesObject.updateAssetsAnswer(obj,body,headers);
			Assert.assertEquals(respObj.statusCode(), 200);

	}


		@Test(priority = 0, description = "Scan old Charger")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_008_ScanOldCharger()
		{

			ScanassetQR obj = new ScanassetQR();

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Map<String, String> body = new HashMap<String, String>();
			body.put("assetAction", "SCAN_OLD_CHARGER");
			body.put("deviceId", "abcd63");
			body.put("userMobile", "9999111753");
			body.put("mid", "ADfyin30849684056371");
			body.put("entityType", "PROPRIETORSHIP");
			body.put("leadId", "36fd7e90-6310-41b1-8425-f694aa3701dd");
			body.put("merchantName", "TOUCH WOOD LIMITED");
			body.put("userCustId", "1700943239");
			body.put("agentCustId", "1107195733");
			body.put("solutionSubType", "edc");

			Response respObj = MiddlewareServicesObject.ScanassetQR(obj,headers,body);
			Assert.assertEquals(respObj.statusCode(), 200);

		}


		@Test(priority = 0, description = "Scan new Charger")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_009_ScannewCharger()
		{

			ScanassetQR obj = new ScanassetQR();

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Map<String, String> body = new HashMap<String, String>();
			body.put("assetAction", "SCAN_NEW_CHARGER");
			body.put("deviceId", "abcd64");
			body.put("userMobile", "9999111753");
			body.put("mid", "ADfyin30849684056371");
			body.put("entityType", "PROPRIETORSHIP");
			body.put("leadId", "36fd7e90-6310-41b1-8425-f694aa3701dd");
			body.put("merchantName", "TOUCH WOOD LIMITED");
			body.put("userCustId", "1700943239");
			body.put("agentCustId", "1107195733");
			body.put("solutionSubType", "edc");

			Response respObj = MiddlewareServicesObject.ScanassetQR(obj,headers,body);
			Assert.assertEquals(respObj.statusCode(), 200);

		}


		@Test(priority = 0, description = "Fetching TnC")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_010_fetchTnC()
		{
			DeviceFetchTnc obj = new DeviceFetchTnc();

			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId","36fd7e90-6310-41b1-8425-f694aa3701dd");

			Map<String, String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");

			Response respObj = MiddlewareServicesObject.DeviceFetchTncMethod(obj,queryParams,headers);
			Assert.assertEquals(respObj.statusCode(), 200);
		}


		@Test(priority = 0, description = "send otp")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_011_sendotp()
		{
			sendOTPLead obj = new sendOTPLead(P.TESTDATA.get("sendotpassetRequest"));

			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("entityType","PROPRIETORSHIP");
			queryParams.put("solutionType","asset_replacement");

			Map<String,String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


			Map<Object,Object> body = new HashMap<Object, Object>();
			body.put("mobile","9999111753");
			body.put("custId","1700943239");
			body.put("userType","merchant");
			body.put("solutionType","asset_replacement");
			body.put("leadId","36fd7e90-6310-41b1-8425-f694aa3701dd");

			Response respObj = MiddlewareServicesObject.sendOTPLeadPlanMethod(obj,queryParams,headers,body);
			Assert.assertEquals(respObj.statusCode(), 200);
		}

		@Test(priority = 0, description = "validate otp")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_012_vaildateotp()
		{
			ValidateotpEDC obj = new ValidateotpEDC(P.TESTDATA.get("validateotpEDCRequest"));

			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("entityType","PROPRIETORSHIP");
			queryParams.put("solutionType","asset_replacement");

			Map<String,String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


			Map<String,Object> body = new HashMap<String, Object>();
			body.put("otp","888888");
			body.put("state","120e6c29-ef42-5220-b19e-fe015b418689");
			body.put("mobile","9999111753");
			body.put("leadId","36fd7e90-6310-41b1-8425-f694aa3701dd");
			body.put("userType","merchant");

			Response respObj = MiddlewareServicesObject.ValidateotpEDCMethod(obj,queryParams,headers,body);
			Assert.assertEquals(respObj.statusCode(), 200);
		}

		@Test(priority = 0, description = "generate payment QR ")
		@Owner(emailId = "<EMAIL>", isAutomated = true)
		public void TC_013_paymentQR()
		{
			Devicepayment obj = new Devicepayment();

			Map<String, String> queryParams = new HashMap<String, String>();
			queryParams.put("leadId","36fd7e90-6310-41b1-8425-f694aa3701dd");
			queryParams.put("generateQR","true");

			Map<String,String> headers = new HashMap<String, String>();
			headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
			headers.put("session_token", sessiontoken);
			headers.put("content-type", "application/json; charset=UTF-8");
			headers.put("accept", "application/json, text/plain, */*");
			headers.put("accept-language", "en-US,en-IN;q=0.9,en;q=0.8");


			Response respObj = MiddlewareServicesObject.PaymentQRmethod(obj,queryParams,headers);
			Assert.assertEquals(respObj.statusCode(), 200);
		}

	}