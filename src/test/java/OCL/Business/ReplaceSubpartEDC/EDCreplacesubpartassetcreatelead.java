package OCL.Business.ReplaceSubpartEDC;

import Request.EDCAssetReplace.Edcassetreplacecreatelead;

import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;

import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.PatternSyntaxException;

public class EDCreplacesubpartassetcreatelead extends BaseMethod
{
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(EDCreplacesubpartassetcreatelead.class);
    public static String clientId = "OE";
    public static String Key = "c9397a35-36c2-4ed8-a50a-2e0a11ebb3ec";
    //  String token = findXMWExTokenforPanel("8010630022", "paytm@123");
    //String sessiontoken = ApplicantToken("8010630022", "paytm@123");
    String sessiontoken = AgentSessionToken("9891497839", "paytm@123");

    String mobileNo= "9999111753";
    String solution_type="asset_replacement";

    @BeforeClass
    public void closingopenleads() throws Exception
    {

       /* TestBase testBase = new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '" + MobileNo + "' and status = '2' and solution_type='asset_replacement';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " + UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);

    }

    @Test(priority = 0, description = "status code ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001_status_code() throws Exception {

        establishConnectiontoServer(sessiontoken,5);

        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
       // headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");


        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);

    }

    @Test(priority = 0, description = "Merchant mobile number is not mentioned")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_missing_mobile_number() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");


        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));

    }

    @Test(priority = 0, description = "Merchant EDC info is missing")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_missing_EDC_details() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "");//{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");


        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        } catch (Exception e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 500);
        }

    }

    @Test(priority = 0, description = "Merchant info is missing")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_missing_merchant_details() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "");
        body.put("merchantName", "");
        body.put("userCustId", "");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = null;
        try {
            respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        } catch (Exception e) {

        }
        if (respObj != null) {

            int statusCode = respObj.getStatusCode();
            Assert.assertEquals(statusCode, 400);
        }
    }


    @Test(priority = 0, description = "device identifier is missing")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_missing_deviceidentifier() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));
    }


    @Test(priority = 0, description = "device identifier is invalid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_invalid_deviceidentifier() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "invalid");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 200);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));
    }

    @Test(priority = 0, description = "device identifier is invalid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_invalid_session_token() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", "sessiontoken");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 410);
        LOGGER.info("Display Message : " + respObj.jsonPath().getString("displayMessage"));
    }
    @Test(priority = 0, description = "device identifier is missing")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_missing_session_token() throws Exception {


        establishConnectiontoServer(sessiontoken,5);

        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", "");
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 401);

    }


    @Test(priority = 0, description = "changing the solution sub type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_changing_SolutionSubType() throws Exception {

        establishConnectiontoServer(sessiontoken,5);

        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "mco");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 500);

    }


    @Test(priority = 0, description = "agent cust ID is invalid")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010_Invalid_agent_custID() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "abcd");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);

    }


    @Test(priority = 0, description = "wrong entity type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011_wrong_entity_type() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "Individual");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);

    }

    @Test(priority = 0, description = "invalid entity type")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012_Invalid_entity_type() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "limited");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);

    }
    @Test(priority = 0, description = "missing all header parameters")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013_missing_headers() throws Exception {

        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "");
        headers.put("session_token","");
        headers.put("content-type", "");
        headers.put("accept", "");
        headers.put("UncleScrooge", "");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1107195733");
        body.put("solutionSubType", "edc");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 401);

    }


    @Test(priority = 0, description = "missing all body parameters")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014_missing_body() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "");
        headers.put("session_token","");
        headers.put("content-type", "");
        headers.put("accept", "");
        headers.put("UncleScrooge", "");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "");
        body.put("solutionTypeLevel2", "");
        body.put("edcQrScanDetailsString", "");
        body.put("userMobile", "");
        body.put("mid", "");
        body.put("entityType", "");
        body.put("merchantName", "");
        body.put("userCustId", "");
        body.put("agentCustId", "");
        body.put("solutionSubType", "");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 401);

    }


    @Test(priority = 0, description = "missing empty request body")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015_empty_request() throws Exception {
        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "");
        body.put("solutionTypeLevel2", "");
        body.put("edcQrScanDetailsString", "");
        body.put("userMobile", "");
        body.put("mid", "");
        body.put("entityType", "");
        body.put("merchantName", "");
        body.put("userCustId", "");
        body.put("agentCustId", "");
        body.put("solutionSubType", "");

        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 400);

    }


    @Test(priority = 0, description = "null request body")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016_null_request() throws Exception {


        establishConnectiontoServer(sessiontoken,5);
        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));

        Map<String, String> headers = new HashMap<String, String>();

        Map<String, Object> body = new HashMap<String, Object>();


        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 401);

    }


    @Test(priority = 0, description = "agent without permission")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017_without_permission() throws Exception {
        establishConnectiontoServer(sessiontoken,5);


        Edcassetreplacecreatelead reqobj = new Edcassetreplacecreatelead(P.TESTDATA.get("EDCreplacesubpaercreateleadRequest"));


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("deviceidentifier", "OnePlus-IV2201-2fd53c7d86191a3a");
        headers.put("session_token", sessiontoken);
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("accept", "application/json, text/plain, */*");
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");


        Map<String, Object> body = new HashMap<String, Object>();
        body.put("solutionType", "asset_replacement");
        body.put("solutionTypeLevel2", "edc");
        body.put("edcQrScanDetailsString", "{modelName:A910,serialNo:LIFETIMETEST222,osType:ANDROID}");
        body.put("userMobile", "9999111753");
        body.put("mid", "ADfyin30849684056371");
        body.put("entityType", "PROPRIETORSHIP");
        body.put("merchantName", "TOUCH WOOD LIMITED");
        body.put("userCustId", "1700943239");
        body.put("agentCustId", "1001647902");
        body.put("solutionSubType", "soundbox");


        Response respObj = MiddlewareServicesObject.subpartassetupdatecreateleadmethod(reqobj, headers, body);
        Assert.assertEquals(respObj.statusCode(), 500);

    }


}