package OCL.Business.CreateDynamicTnc;

import Request.CIF.FetchVettedKeys;
import Request.CIF.TncAccept;
import Request.CIF.TncUser;
import Request.MerchantService.v2.profile.update.commissiontncs.GetMerchantAgreementTnC;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class FlowCreateDynamicTnc extends BaseMethod
{
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    private static final Logger LOGGER = LogManager.getLogger(FlowCreateDynamicTnc.class);


    public static String setName = "dynamic_tnc_for_retail_edc_set";
    public static String jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0cyI6IjE2OTA4NzIwNjY2OTYiLCJjdXN0X2lkIjoiMTUyMDM0MjAzOTAwMCIsImNsaWVudF9pZCI6IkdHLU9FLXN0YWdpbmcifQ.UaOgdE/ME-Pp4VRK3YK7dqPgjpN09FgrD3aOHXk7M3g";

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch vetted keys from kyb ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_01_FetchVettedKeysFromKyb() {
        FetchVettedKeys FetchVettedKeysObj=new FetchVettedKeys();
        Map<String, String> params = new HashMap<String, String>();
        params.put("setName", setName);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);


        Response FetchVettedKeysObjResp = middlewareServicesObject.fetchVettedKeys(FetchVettedKeysObj,params,headers);
        int StatusCode = FetchVettedKeysObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch vetted keys from kyb with empty setname ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_02_FetchVettedKeysFromKybWithEmptySetname() {
        FetchVettedKeys FetchVettedKeysObj=new FetchVettedKeys();
        Map<String, String> params = new HashMap<String, String>();
        params.put("setName", "");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);


        Response FetchVettedKeysObjResp = middlewareServicesObject.fetchVettedKeys(FetchVettedKeysObj,params,headers);
        int StatusCode = FetchVettedKeysObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch vetted keys from kyb with invalid setname")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_03_FetchVettedKeysFromKybWithInvalidSetname() {
        FetchVettedKeys FetchVettedKeysObj=new FetchVettedKeys();
        Map<String, String> params = new HashMap<String, String>();
        params.put("setName", "dynamic_tnc_for_retail_edc_set1");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);


        Response FetchVettedKeysObjResp = middlewareServicesObject.fetchVettedKeys(FetchVettedKeysObj,params,headers);
        int StatusCode = FetchVettedKeysObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch vetted keys from kyb with invalid jwt")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_04_FetchVettedKeysFromKybWithInvalidJwt() {
        FetchVettedKeys FetchVettedKeysObj=new FetchVettedKeys();
        Map<String, String> params = new HashMap<String, String>();
        params.put("setName", setName);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ91");


        Response FetchVettedKeysObjResp = middlewareServicesObject.fetchVettedKeys(FetchVettedKeysObj,params,headers);
        int StatusCode = FetchVettedKeysObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 410);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch vetted keys from kyb with empty jwt")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_05_FetchVettedKeysFromKybWithEmptyJwt() {
        FetchVettedKeys FetchVettedKeysObj=new FetchVettedKeys();
        Map<String, String> params = new HashMap<String, String>();
        params.put("setName", setName);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", " ");


        Response FetchVettedKeysObjResp = middlewareServicesObject.fetchVettedKeys(FetchVettedKeysObj,params,headers);
        int StatusCode = FetchVettedKeysObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 410);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Fetch tnc url")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_06_FetchTncUrl() {
        GetMerchantAgreementTnC GetMerchantAgreementTnCObj=new GetMerchantAgreementTnC();

        Map<String, String> params = new HashMap<String, String>();
        params.put("solution", "merchant_common_onboard");
        params.put("entityType", "INDIVIDUAL");
        params.put("channel", "UMP_WEB");
        params.put("solutionLeadId", "9dedbbaf-a06d-41d4-9f88-c9ae3417cba6");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("session_token", "875af56e-cf06-4d73-acc7-669c81284900");
        headers.put("content-type","application/json");


        Response GetMerchantAgreementTnCObjResp = middlewareServicesObject.getMerchantAgreementTnC(GetMerchantAgreementTnCObj,params,headers);
        int StatusCode = GetMerchantAgreementTnCObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Accept tnc in kyb")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_07_AcceptTncInKyb() {
        TncAccept TncAcceptObj=new TncAccept(P.TESTDATA.get("TncAcceptInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","5");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid","1700956136");


        Response  TncAcceptObjResp= middlewareServicesObject.tncAcceptInKyb(TncAcceptObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Accept tnc in kyb with empty code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_08_AcceptTncInKybWithEmptyCode() {
        TncAccept TncAcceptObj=new TncAccept(P.TESTDATA.get("TncAcceptInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code"," ");
        body.put("version","5");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid","1700956136");


        Response  TncAcceptObjResp= middlewareServicesObject.tncAcceptInKyb(TncAcceptObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Accept tnc in kyb with invalid code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_09_AcceptTncInKybWithInvalidCode() {
        TncAccept TncAcceptObj=new TncAccept(P.TESTDATA.get("TncAcceptInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc1");
        body.put("version","5");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid","1700956136");


        Response  TncAcceptObjResp= middlewareServicesObject.tncAcceptInKyb(TncAcceptObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Accept tnc in kyb with empty version ")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_10_AcceptTncInKybWithEmptyVersion() {
        TncAccept TncAcceptObj=new TncAccept(P.TESTDATA.get("TncAcceptInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version"," ");



        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid","1700956136");


        Response  TncAcceptObjResp= middlewareServicesObject.tncAcceptInKyb(TncAcceptObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Accept tnc in kyb with invalid version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_11_AcceptTncInKybWithInvalidVersion() {
        TncAccept TncAcceptObj=new TncAccept(P.TESTDATA.get("TncAcceptInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","abc");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);
        headers.put("uid","1700956136");


        Response  TncAcceptObjResp= middlewareServicesObject.tncAcceptInKyb(TncAcceptObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_12_TncUserInkyb() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d14");
        body.put("mode","clickthrough");
        body.put("userId","1701360311");


        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with invalid code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_13_TncUserInkybWithInvalidCode() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc1");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d14");
        body.put("mode","clickthrough");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with empty code")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_14_TncUserInkybWithEmptyCode() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code"," ");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d14");
        body.put("mode","clickthrough");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with invalid version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_15_TncUserInkybWithInvalidVersion() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","5");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d14");
        body.put("mode","clickthrough");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with empty version")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_16_TncUserInkybWithEmptyVersion() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version"," ");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d14");
        body.put("mode","clickthrough");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with empty UniqueIdentifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_17_TncUserInkybWithEmptyUniqueIdentifier() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","6");
        body.put("uniqueIdentifier"," ");
        body.put("mode","clickthrough");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with invalid UniqueIdentifier")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_18_TncUserInkybWithInvalidUniqueIdentifier() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d141");
        body.put("mode","clickthrough");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with invalid mode")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_19_TncUserInkybWithInvalidMode() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d141");
        body.put("mode","clickthrough1");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with empty mode")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_20_TncUserInkybWithEmptyMode() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d141");
        body.put("mode"," ");
        body.put("userId","1701360311");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with invalid userId")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_21_TncUserInkybWithInvalidUserId() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d141");
        body.put("mode","clickthrough");
        body.put("userId","17013603111");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test(priority = 1, groups = {"Regression"}, description = "Tnc user in kyb with empty user id")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_22_TncUserInkybWithEmptyUserId() {
        TncUser TncUserObj=new TncUser(P.TESTDATA.get("TncUserInKybReqBody"));

        Map<String, String> body = new HashMap<String, String>();
        body.put("code","dynamic_tnc_for_retail_edc");
        body.put("version","6");
        body.put("uniqueIdentifier","a0314e7500094d21ae14392863f45d141");
        body.put("mode","clickthrough");
        body.put("userId"," ");

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-jwt-token", jwt);

        Response  TncAcceptObjResp= middlewareServicesObject.tncUserInKyb(TncUserObj,body,headers);
        int StatusCode = TncAcceptObjResp.getStatusCode();
      Assert.assertEquals(StatusCode, 400);
    }



}

