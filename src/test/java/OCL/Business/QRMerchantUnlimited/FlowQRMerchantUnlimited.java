package OCL.Business.QRMerchantUnlimited;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.GetCompany;
import Request.MerchantService.v1.GstExemptionList;
import Request.MerchantService.v1.PostCompany;
import Request.MerchantService.v2.Banks;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.MerchantService.v4.PennyDropMultiNameMatch;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Request.Wallet.CreateUserWallet;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*
public class FlowQRMerchantUnlimited extends BaseMethod

{
    private static final Logger LOGGER = LogManager.getLogger(FlowQRMerchantUnlimited.class);

    public static String AgentToken = "";
    public static String CustId = "";
    public static String mobileNo = "";
    public static String newState = "";
    public static  String version = "4.8.3";
    public static String OTP = "";
    public static String PAN = "";
    public static String MID = "";
    public  static String leadId = "";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String SolutionId = "";
    public static String fileNames = "pan,Cancelled+Cheque+Photo,Merchandise+Photo+1,Merchandise+Photo+2,Merchandise+Photo+3,Paytm+Accepted+Here+Sticker,Paytm+Accepted+Here+Sticker+1";
    public static String nameMatchStatus = "";
    public static String XMWToken = "";

    List <Object> GetDocuments = new ArrayList<>();
    public static String WorkFlowId = "";

    public static String EntityTypeUL = "PUBLIC_LIMITED";
    public static String addressUuid = "";
    public static String rrbUuid = "";
    public static String BillingAddressUUID = "";
    public static String RegisterAddressUUID = "";
    public static String GSTIN ="";
    public static String LeadStagePanel = "";

    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";


    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();



    @BeforeTest
    public void AgentLoginUnlimited()
    {
        /*LOGGER.info(" GG App Agent Login");
        CommonAgentToken = AgentSessionToken("9953828631","paytm@123");
        LOGGER.info(" Common Agent Token is : " + CommonAgentToken);*/

        /*Response responseObject=middlewareServicesObject.v1Token("**********","paytm@123");
        XMWCookie=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWCookie);

        LOGGER.info("Before Unlimited Test, Agent Login");
        AgentToken = CommonAgentToken;
        //AgentToken = AgentSessionToken("7771110007","paytm@123");


        switch (EntityTypeUL) {
            case "PUBLIC_LIMITED":
            case "PRIVATE_LIMITED": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomPublicPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "TRUST": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomTrustPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "SOCIETY_ASSOCIATION_CLUB": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomSocietyPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "PARTNERSHIP": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomPartnershipPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
            case "HINDU_UNDIVIDED_FAMILY": {
                LOGGER.info("Entity is : " + EntityTypeUL);
                Utilities UtilObj = new Utilities();
                PAN = UtilObj.randomHUFPANValueGenerator();
                LOGGER.info("PAN Number is for ORG Merchant : " + PAN);
                GSTIN = "07" + PAN + "7Z9";
                LOGGER.info("Formed GSTIN is : " + GSTIN);
                break;
            }
        }
    }

    @Test(priority = 0,description = "Create Applicant on Oauth",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0001_CreateApplicantOauth()
    {
        //generating new Number
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + mobileNo);
        CreateApplicantOauth(mobileNo);
    }

    @Test(priority = 0,description = "Positive Send OTP for Business",dependsOnMethods ="TC0001_CreateApplicantOauth",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0002_PositiveSendOtpBusinessUnlimited()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"UNKNOWN","company_onboard",AgentToken,version,mobileNo,"company");

        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        CustId=FetchUserDetails(mobileNo, "phone");

      // CustId = SendOtpResp.jsonPath().getString("custId");
        LOGGER.info("Cust Id is : " + CustId);

        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        Assert.assertFalse(CustId.isEmpty());
        Assert.assertNotNull(CustId);

    }



    @Test(priority = 0,description = "Positive Get Business",dependsOnMethods ="TC0002_PositiveSendOtpBusinessUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0003_PositiveGetBusinessUnlimited()
    {
        GetBusinessv3 getBusObj = new GetBusinessv3();

        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);

        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Validate PAN v1 Comapny",dependsOnMethods = "TC0003_PositiveGetBusinessUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0004_PositiveGetComapnyUnlimited()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");


        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,EntityTypeUL,"company_onboard",mobileNo);

        String ExpectedMsg = "Pan is non dedupe";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);


    }

    @Test(priority = 0,description = "Positive Create Comapny",dependsOnMethods = "TC0004_PositiveGetComapnyUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0005_PositivePostComapnyUnlimited()
    {
        PostCompany v1PostComp = new PostCompany();

        Response v1PostCompany = middlewareServicesObject.v1PostCompany(v1PostComp, AgentToken, version, CustId, EntityTypeUL, "company_onboard", mobileNo, "One 97 Communication", PAN);

        String ActualMsg = v1PostCompany.jsonPath().getString("displayMessage");
        String ExpectedMsg = "Lead successfully created.";
        Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

        int StatusCode = v1PostCompany.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        leadId = v1PostCompany.jsonPath().getString("leadId");
        LOGGER.info("LeadId is : " + leadId);

        v1PostComp.validateResponseAgainstJSONSchema("MerchantService/V1/PostCompany/PostCompanyResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Get Business After COmpany Onboard",dependsOnMethods ="TC0005_PositivePostComapnyUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0006_PositiveGetBusinessAfterCompanyUnlimited()
    {
        GetBusinessv3 getBusObj = new GetBusinessv3();

        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);

        kybBusinessId = getBusResp.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        LOGGER.info("KYB ID is : " + kybBusinessId);

        businessName = getBusResp.jsonPath().getJsonObject("businesses[0].businessName").toString();
        LOGGER.info("Business Name is : " + businessName);

        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");

    }

    @Test(priority = 0,description = "Positive Get Business Profile",dependsOnMethods ="TC0006_PositiveGetBusinessAfterCompanyUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0007_PositiveGetBusinessProfileUnlimited()
    {
        BusinessProfile v3BusPro = new BusinessProfile();

        Response v3BusProRes = middlewareServicesObject.v3BusinessProfile(v3BusPro,CustId,leadId,kybBusinessId,AgentToken,version);

        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Validate OTP for QR Merchant",dependsOnMethods ="TC0007_PositiveGetBusinessProfileUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0008_PositiveValidateOtpQRMerchantUnlimited() throws IOException, JSchException
    {
        waitForLoad(500);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpUnlimitedRevamp"));
        validateOtpObj.getProperties().setProperty("leadId", leadId);
        validateOtpObj.getProperties().setProperty("custId", CustId);
        validateOtpObj.getProperties().setProperty("solutionTypeLevel3", "edc_for_store");

        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj, EntityTypeUL, "qr_merchant", AgentToken, version, mobileNo, "merchant", newState, OTP);

        SolutionId = validateOtp.jsonPath().getString("leadId");
    }

    @Test(priority = 0,description = "Positive Send OTP for QR Merchant",dependsOnMethods ="TC0008_PositiveValidateOtpQRMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0008_PositiveSendOtpQRMerchantUnlimited()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,EntityTypeUL,"qr_merchant",AgentToken,version,mobileNo,"merchant_smd");

        newState = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("New State " + newState);

        String expectedErrorMsg = "Otp sent to phone";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("message");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));

        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 0,description = "Positive Get TnC for Unlimited",dependsOnMethods ="TC0008_PositiveSendOtpQRMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0009_PositiveGetTnCUnlimited()
    {
        TnC getTnCobj = new TnC();

        Response getTnCResp = middlewareServicesObject.v2GetTnC(getTnCobj,EntityTypeUL,"qr_merchant",version,AgentToken);

        String ExpectedMsg = "SUCCESS";
        String ActualMsg = getTnCResp.jsonPath().getString("status");
        Assert.assertTrue(ActualMsg.contains(ActualMsg));

        int StatusCode = getTnCResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String TnCURL = getTnCResp.jsonPath().getString("url");
        LOGGER.info("TnC URL is : "+TnCURL);

        getTnCobj.validateResponseAgainstJSONSchema("MerchantService/V2/TnC/TnCResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Validate OTP for QR Merchant",dependsOnMethods ="TC0008_PositiveSendOtpQRMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0010_PositiveValidateOtpQRMerchantUnlimited() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpUnlimited"));
        validateOtpObj.getProperties().setProperty("leadId",leadId);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);

        //OTP = getOTP(mobileNo);
        //OTP="888888";
        OTP = getOTPFromSellerPanel(mobileNo);

        LOGGER.info("This is OTP " + OTP);

        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityTypeUL,"qr_merchant",AgentToken,version,mobileNo,"merchant",newState,OTP);

        SolutionId = validateOtp.jsonPath().getString("leadId");
        int StatusCode = validateOtp.getStatusCode();

        for (int i = 0; i<=3;i++)
        {

            if (SolutionId == null || SolutionId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC0008_PositiveSendOtpQRMerchantUnlimited();

                waitForLoad(5000);
                ValidateOtp revalidateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpUnlimited"));
                revalidateOtpObj.getProperties().setProperty("leadId",leadId);
                revalidateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
                //OTP = getOTP(mobileNo);
                OTP = getOTPFromSellerPanel(mobileNo);

                LOGGER.info("This is OTP " + OTP);
                Response RevalidateOtp = middlewareServicesObject.v3ValidateOtp(revalidateOtpObj,EntityTypeUL,"qr_merchant",AgentToken,version,mobileNo,"merchant",newState,OTP);
                SolutionId = RevalidateOtp.jsonPath().getString("leadId");
                StatusCode = RevalidateOtp.getStatusCode();
            }
            else
            {
                LOGGER.info("This is Solution Lead ID : " + SolutionId);
                Assert.assertNotEquals(SolutionId,null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }

        Assert.assertFalse(SolutionId.isEmpty());

        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get Merchant for Unlimited",dependsOnMethods ="TC0010_PositiveValidateOtpQRMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0011_PositiveGetMerchantUnlimited()
    {
        GetMerchant getMerchObj = new GetMerchant(CustId);

        getMerchObj.addParameter("leadId",SolutionId);
        getMerchObj.addParameter("kybBusinessId",kybBusinessId);

        Response getMerchResp = middlewareServicesObject.v3GetMerchant(getMerchObj,EntityTypeUL,"qr_merchant",AgentToken,version);

        int StatusCode = getMerchResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        getMerchObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetMerchant/GetMerchantResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get Category for Unlimited",dependsOnMethods ="TC0011_PositiveGetMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0012_PositiveGetCategoryUnlimited()
    {
        Category getCatobj = new Category();

        Response getCatResp = uadServicesObject.getCategory(getCatobj,"PUBLIC LIMITED","qr_merchant",AgentToken,version);

        int StatusCode = getCatResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        String expectedMsg = "success";
        String exactMsg = getCatResp.jsonPath().getString("message");
        Assert.assertTrue(exactMsg.contains(expectedMsg));

        getCatobj.validateResponseAgainstJSONSchema("UAD/V1/Category/CategoryResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get Sub-Category for Unlimited",dependsOnMethods ="TC0012_PositiveGetCategoryUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0013_PositiveGetSubCategoryUnlimited()
    {
        SubCategory getSubCatobj = new SubCategory();

        Response getSubCatResp = uadServicesObject.getSubCategory(getSubCatobj,"PUBLIC LIMITED","qr_merchant",AgentToken,version,1);

        int StatusCode = getSubCatResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        String expectedMsg = "success";
        String exactMsg = getSubCatResp.jsonPath().getString("message");
        Assert.assertTrue(exactMsg.contains(expectedMsg));

        getSubCatobj.validateResponseAgainstJSONSchema("UAD/V1/SubCategory/SubCategoryResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Send OTP for Merchant Declaration for Unlimited",dependsOnMethods ="TC0011_PositiveGetMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0014_PositiveSendOtpMerchantDeclareUnlimited()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,EntityTypeUL,"qr_merchant",AgentToken,version,mobileNo,"merchant_smd");

        newState = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("New State " + newState);

        String expectedErrorMsg = "Otp sent to phone";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("message");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));

        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 0,description = "Positive Validate OTP for Merchant Declaration for Unlimited",dependsOnMethods ="TC0014_PositiveSendOtpMerchantDeclareUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0015_PositiveValidateOtpMerchantDeclareUnlimited() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpMerchantDeclare500K"));
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("onlyValidateOtp","true");

       // OTP = getOTP(mobileNo);
        //OTP="888888";
        OTP = getOTPFromSellerPanel(mobileNo);

        LOGGER.info("This is OTP " + OTP);

        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityTypeUL,"qr_merchant",AgentToken,version,mobileNo,"merchant",newState,OTP);

        int StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get GST Exemption List for Unlimited",dependsOnMethods ="TC0014_PositiveSendOtpMerchantDeclareUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0016_PositiveGetGstExemptionUnlimited()
    {
        GstExemptionList v1GstObj = new GstExemptionList();

        Response getGstExemptionResp = middlewareServicesObject.v1GstExemption(v1GstObj,version,AgentToken);

        int StatusCode = getGstExemptionResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        v1GstObj.validateResponseAgainstJSONSchema("MerchantService/V1/GstExemptionList/GstExemptionListResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Get Document Status for Unlimited",dependsOnMethods ="TC0016_PositiveGetGstExemptionUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0017_PositiveGetDocStatusUnlimited()
    {
        GetDocStatus v3DocObj = new GetDocStatus();
        v3DocObj.addParameter("fileNames",fileNames);
        v3DocObj.addParameter("leadId",SolutionId);
        v3DocObj.addParameter("kybBusinessId",kybBusinessId);

        Response v3DocStatResp = middlewareServicesObject.v3getDocStatus(v3DocObj,CustId,EntityTypeUL,"qr_merchant",AgentToken,version);

        String ExpectedErr = "200";
        String ActualErr = v3DocStatResp.jsonPath().getString("errorCode");
        Assert.assertTrue(ActualErr.contains(ExpectedErr));

        int StatusCode = v3DocStatResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        //v3DocObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetDocStatus/GetDocStatusSchemaFor500K.json");
    }

    @Test(priority = 0,description = "Positive Get Banks for Unlimited",dependsOnMethods ="TC0017_PositiveGetDocStatusUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0018_PositiveGetBanksUnlimited()
    {
        IFSC = "INDB0000588";
        Banks getBank = new Banks(IFSC);
        Response getBanksResponse = middlewareServicesObject.v2Banks(getBank,version,AgentToken);

        /*String expectedMsg = "bankName=INDUSIND BANK";
        String actualMsg = getBanksResponse.jsonPath().getJsonObject("bankDetails").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));

        int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        //getBank.validateResponseAgainstJSONSchema("MerchantService/V2/Banks/BanksResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Perform MultiName Penny Drop for Unlimited",dependsOnMethods ="TC0011_PositiveGetMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0019_PositivePennyDropMultiNameMatchUnlimited()
    {
        PennyDropMultiNameMatch v4PennyObj = new PennyDropMultiNameMatch();
        v4PennyObj.addParameter("solutionType","qr_merchant");
        v4PennyObj.addParameter("entityType",EntityTypeUL);
        v4PennyObj.getProperties().setProperty("mobile",mobileNo);
        v4PennyObj.getProperties().setProperty("ifsc","INDB0000431");
        v4PennyObj.getProperties().setProperty("bankAccountNumber","************");

        Response v4PennyResp = middlewareServicesObject.v4PennyDropMultiName(v4PennyObj,version,"AASHIT SHARMA","INDUSIND Bank",CustId,AgentToken);

        nameMatchStatus = v4PennyResp.jsonPath().getString("nameMatchStatus");
        LOGGER.info("Name Match Status = " +nameMatchStatus);

        if(nameMatchStatus == null)
        {
            nameMatchStatus = "false";
            LOGGER.info("Name Match Status when its NULL = " +nameMatchStatus);

        }

        int StatusCode = v4PennyResp.getStatusCode();
        if(StatusCode == 200)
        {
            Assert.assertEquals(StatusCode,200);
        }

        if (StatusCode == 500)
        {
            String expectedFailureMsg = "We are unable to connect with your bank to verify your details. Please try after some time or provide a different bank account";
            String actualFailureMsg = v4PennyResp.jsonPath().getString("message");
            Assert.assertTrue(actualFailureMsg.contains(expectedFailureMsg));

            Assert.assertEquals(StatusCode,500);
        }
        if(StatusCode == 400)
        {
            Assert.assertEquals(StatusCode,400);
        }
        //v4PennyObj.validateResponseAgainstJSONSchema("MerchantService/V3/PennyDrop/PennyDropResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Submit Lead Details for Unlimited",dependsOnMethods ="TC0011_PositiveGetMerchantUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0020_PositiveSubmitLeadUnlimited()
    {
        SubmitMerchant v3SubmitObj = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMerchant500K"));
        v3SubmitObj.getProperties().setProperty("leadId",leadId);
        v3SubmitObj.getProperties().setProperty("nameMatchStatus","true");


        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(v3SubmitObj,mobileNo,mobileNo,"ANDB0002029",version,EntityTypeUL,"qr_merchant",AgentToken);

        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String ExpectedMsg = "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later";
        String ActualMsg = submitMerchResponse.jsonPath().getString("message");
        Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

        v3SubmitObj.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Fetch Documents for QR Merchant Unlimited",dependsOnMethods ="TC0020_PositiveSubmitLeadUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0021_PositiveFetchDocsUnlimited() throws JsonProcessingException
    {
        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", EntityTypeUL);
        queryDocUpload.put("solutionType", "qr_merchant");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", SolutionId);

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", EntityTypeUL);
        queryFetchDoc.put("solution", "qr_merchant");
        queryFetchDoc.put("leadId", SolutionId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);

        GgAppDynamicDocUpload(AgentToken,version,mobileNo,queryFetchDoc,queryDocUpload);


        //v4Docs.validateResponseAgainstJSONSchema("MerchantService/V4/FetchDynamicDocs/FetchDynamicDocQrMerchantSchema.json");
    }



    @Test(priority = 0,description = "Create User at Wallet",groups = {"Regression"},dependsOnMethods = "TC0021_PositiveFetchDocsUnlimited",enabled = false)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0022_PositiveCreateUserWallet()
    {
        CreateUserWallet walletObj = new CreateUserWallet();

        walletObj.getProperties().setProperty("ssoId",CustId);
        walletObj.getProperties().setProperty("mobileNumber",mobileNo);

        Response walletResp = walletServices.createUserWallet(walletObj);

        String msg = walletResp.jsonPath().getString("statusMessage");
        Assert.assertTrue(msg.contains("Wallet Activated"));

        String statusMsg = walletResp.jsonPath().getString("statusCode");
        Assert.assertTrue(statusMsg.contains("SUCCESS"));

    }


    @Test(priority = 0,description = "Get Cookie for OE Panel",groups = {"Regression"},dependsOnMethods = "TC0021_PositiveFetchDocsUnlimited")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0022_PositiveGetOEPanelCookieUnlimited() throws SQLException, JsonProcessingException {
        XMWToken=XMWCookie;

        LOGGER.info("XMW token is :"+XMWToken);

        DbName = DbStaging6;
        LOGGER.info("Hitting Callback for 100Rs MID");
        MID = PG_CallBack_Insatnt50K(CustId);
    }

    @Test(priority = 0,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC0022_PositiveGetOEPanelCookieUnlimited")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0023_PositiveFetchLeadPanelUnlimited()
    {

        try
        {
            Map <String,String> RequestPanel = new HashMap<>();
            Map <String,String> ResponsePanel = new HashMap<>();

            RequestPanel.put("docStatus",OePanelDocStatus);
            RequestPanel.put("rejectionReason",RejectionReason);
            RequestPanel.put("leadId",SolutionId);

            ResponsePanel = FetchPanelLead(RequestPanel);

            DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
            WorkFlowId = ResponsePanel.get("WorkFlowId");
            LeadStagePanel = ResponsePanel.get("LeadStage");
            LOGGER. info("Lead Stage is : " + LeadStagePanel);

        }
        catch (Exception e)
        {
            LOGGER.info("Execption is : " + e);
            LOGGER.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());
        }
    }

    @Test(priority = 0,description = "Reallocating Agent to Rajan",groups = {"Regression"},dependsOnMethods = "TC0023_PositiveFetchLeadPanelUnlimited")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0024_ReallocatingAgentUnlimited()
    {
        LOGGER.info("Inside ReallocatingAgent Method ");

        boolean LeadStage = false;
        if (LeadStagePanel.equals("DATA_ENTRY_ACTION_PENDING"))
        {
            LeadStage = true;
        }

        for (int i = 0; i<=3;i++)
        {
            LOGGER.info("Inside loop to fetch current lead stage & counter is : "+i );
            if(!LeadStage)
            {
                LOGGER.info("Inside If condition for lead stage id not DE");
                FetchLead v1FetchLeadObj = new FetchLead(SolutionId);
                Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
                LeadStagePanel = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
                WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();


                if (LeadStagePanel.equals("DATA_ENTRY_ACTION_PENDING"))
                {
                    LOGGER.info("Inside if condition to change Flag for Lead Stage");
                    LeadStage = true;
                }
            }
            else
            {
                LOGGER.info("Current Lead Stage is : " + LeadStagePanel);
                break;
            }
        }
        Assert.assertEquals(LeadStagePanel,"DATA_ENTRY_ACTION_PENDING");

        ReallocatingAgent(SolutionId,"1152");

    }

    @Test(enabled = true,priority = 0,description = "Submit rejected lead from OE panel ",groups = {"Regression"},dependsOnMethods = "TC0024_ReallocatingAgentUnlimited")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0025_PositiveRejectedLeadPaneUnlimited()
    {
        waitForLoad(4000);

        EditLead v1EditLeadObj=new EditLead(SolutionId, P.TESTDATA.get("EditLeadUnlimitedNameMatchFailedRejected"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("DATA_ENTRY_REJECTED"));
    }

    @Test(enabled = true,priority = 0,description = "Positive fetch merchant status after rejection",dependsOnMethods = "TC0024_ReallocatingAgentUnlimited",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0026_PositiveGetMerchantStatusAfterRejection() throws IOException, JSchException
    {
        LOGGER.info("Inside PositiveGetMerchantStatusAfterRejection Method");
        GetMerchant callv3GetMerch = new GetMerchant(CustId);
        Response resObjGetMerchant = middlewareServicesObject.v3GetMerchant(callv3GetMerch,EntityTypeUL, "qr_merchant", AgentToken, version);

        addressUuid = resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.addressUuid").toString();
        rrbUuid =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.relatedBusinessUuid").toString();
        BillingAddressUUID =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.billingAddressUUID").toString();
        RegisterAddressUUID =  resObjGetMerchant.jsonPath().getJsonObject("merchantDetails.registeredAddressUUID").toString();

        LOGGER.info("Address UUID : " + addressUuid + " RRB UUID : " + rrbUuid + " Owner Address UUID : " + BillingAddressUUID + " Register Address UUID : " + RegisterAddressUUID);

        int StatusCode = resObjGetMerchant.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }


    @Test(enabled = true,priority = 0,description = "Positive Submit Lead Details for Unlimited after Rejection",dependsOnMethods ="TC0026_PositiveGetMerchantStatusAfterRejection",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0027_PositiveSubmitRejectedLeadUnlimited() throws JsonProcessingException
    {
        SubmitMerchant v3SubmitObj = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMerchantRejectedUnlimited"));
        v3SubmitObj.getProperties().setProperty("leadId",leadId);
        v3SubmitObj.getProperties().setProperty("nameMatchStatus","false");
        v3SubmitObj.getProperties().setProperty("addressUuid",addressUuid);
        v3SubmitObj.getProperties().setProperty("billingAddressUUID",BillingAddressUUID);
        v3SubmitObj.getProperties().setProperty("registeredAddressUUID",RegisterAddressUUID);
        v3SubmitObj.getProperties().setProperty("relatedBusinessUuid",rrbUuid);


        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(v3SubmitObj,mobileNo,mobileNo,"ANDB0002029",version,EntityTypeUL,"qr_merchant",AgentToken);

        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String ExpectedMsg = "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later";
        String ActualMsg = submitMerchResponse.jsonPath().getString("message");
        Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

        v3SubmitObj.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");

        if (StatusCode == 200)
        {
            TC0021_PositiveFetchDocsUnlimited();
        }
    }

    @Test(priority = 2,description = "Positive Submit Lead Details for Unlimited after Rejection",dependsOnMethods = "TC0024_ReallocatingAgentUnlimited",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0028_PositiveSubmitLeadPanelUnlimited()
    {
        waitForLoad(6000);
        OePanelDocStatus = "APPROVED";
        RejectionReason = null;
        TC0023_PositiveFetchLeadPanelUnlimited();
        TC0024_ReallocatingAgentUnlimited();
        EditLead v1EditLeadObj=new EditLead(SolutionId, P.TESTDATA.get("EditLeadUnlimtedMatchFailed"));

        v1EditLeadObj.getProperties().setProperty("documents",DocumetRequestDeserialised);
        v1EditLeadObj.getProperties().setProperty("workflowStatusId",WorkFlowId);
        v1EditLeadObj.getProperties().setProperty("custId",CustId);
        v1EditLeadObj.getProperties().setProperty("mobileNumber",mobileNo);
        v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber",mobileNo);

        Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken,"application/json");

        int statusCode = responseObject.getStatusCode();
        Assert.assertEquals(statusCode,200);

        String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
        Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));
    }

    @Test(description = "Fetch Lead Details on Panel for PG Callback",groups = {"Regression"},dependsOnMethods = "TC0028_PositiveSubmitLeadPanelUnlimited")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0029_PgCallBackUnlimited()
    {

        PgCallBackFromPanelRef(SolutionId,CustId,MID);
    }

    @AfterClass(description = "Adding Details on CSV")
    public void AddingDataCsv() throws IOException {
        LOGGER.info("In After Test of " +getClass());

        File fileUpload = new File("OnboardedMerchant.csv") ;
        // Create csv file
        FileWriter outputfile = new FileWriter(fileUpload,true);

        // Write to CSV file which is open
        CSVWriter writer = new CSVWriter(outputfile);

        if(!MID.isEmpty()) {
            LOGGER.info("MID Is not Empty");
            // add data to csv
            String[] data1 = {MID, CustId, mobileNo, "qr_merchant", "Unlimited"};
            writer.writeNext(data1);
            writer.flush();
            writer.close();
        }
        else
        {
            LOGGER.info("MID is Empty");
        }
    }

}*/
