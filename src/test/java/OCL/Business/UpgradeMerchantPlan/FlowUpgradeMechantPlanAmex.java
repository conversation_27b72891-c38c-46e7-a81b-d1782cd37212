package OCL.Business.UpgradeMerchantPlan;

import Request.MerchantService.v1.UpgradePlans;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowUpgradeMechantPlanAmex extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowUpgradeMechantPlanAmex.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public static String AgentToken = CommonAgentToken;
    public static String mobileNo = "7770008841";
    public static  String AgentNo = "";
    public static String version = "5.0.9";
    public static String CustId ="1002359412";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String UserMID = "";
    public static String State = "";
    public static String SolutionId = "";
    public static String OTP ="";
    public static String WorkFlowId = "";
    public static String EntityType = "";
    public static String XMWToken = "";
    public static String PPI_LIMIT = "";
    public static String EDC_BASED_MID = "";
    public static String SMALL_MERCHANT = "";
    String solution_type="upgrade_merchant_plan";


    @BeforeClass
    public void AgentLoginPlanAmexPlan() throws Exception {
        LOGGER.info("Before Upgrade Plan Test, Agent Login");
      //  AgentToken = CommonAgentToken;
        AgentToken = AgentSessionToken("7771216290","paytm@123");
//        establishConnectiontoServer(AgentToken,5);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(7000);
        /*TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='upgrade_merchant_plan';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);
    }

    @Test(priority = 0,groups = {"Regression"},description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_PlanAmexPositiveSendOtpBusiness() throws Exception {
                establishConnectiontoServer(AgentToken,5);

        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"UNKNOWN","company_onboard",AgentToken,version,mobileNo,"company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
      //  CustId = SendOtpResp.jsonPath().getString("custId");
      //  LOGGER.info("Cust Id is : " + CustId);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 0,description = "Positive Get Business",dependsOnMethods ="TC001_PlanAmexPositiveSendOtpBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_PlanAmexPositiveGetBusiness() throws Exception {
                establishConnectiontoServer(AgentToken,5);

        try {
            GetBusinessv3 getBusObj = new GetBusinessv3();
            Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);
            kybBusinessId = getBusResp.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
            LOGGER.info("KYB ID is : " + kybBusinessId);
            businessName = getBusResp.jsonPath().getJsonObject("businesses[0].businessName").toString();
            LOGGER.info("Business Name is : " + businessName);
          //  leadId = IfNotNull(getBusResp,"businesses[0].leadId");
            int statusCode = getBusResp.getStatusCode();
            Assert.assertEquals(statusCode,200);
        }
        catch (Exception E)
        {
            LOGGER.info("Exception occured : " + E);
        }

        //getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");
    }
    @Test(priority = 0,description = "Positive Get Business Profile",dependsOnMethods ="TC002_PlanAmexPositiveGetBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_PlanAmexPositiveGetBusinessProfile() throws Exception {
                establishConnectiontoServer(AgentToken,5);

        BusinessProfile v3BusPro = new BusinessProfile();
        Response v3BusProRes = middlewareServicesObject.v3BusinessProfile(v3BusPro,CustId,
                mobileNo,kybBusinessId,AgentToken,version);
        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        EntityType = v3BusProRes.jsonPath().getJsonObject("businessSRO.entityType").toString();
        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_PlanAmexPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_01_PlanAmexFetchMIDInvalidCustId() throws Exception {
                establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,"AASITCUST","EDC");

        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode,500);
        Assert.assertTrue(v3FetchMIDResp.jsonPath().getString("message").contains("Failed to fetch mids for custId"));
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_PlanAmexPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_02_PlanAmexFetchMIDemptyCustId() throws Exception {
                establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,"","EDC");

        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode,400);
        // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_PlanAmexPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_03_PlanAmexFetchMIDWrongCustId() throws Exception {
                establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,"1001245028","EDC");

        int statusCode = v3FetchMIDResp.getStatusCode();
//        Assert.assertEquals(statusCode,200);
//        Assert.assertTrue(v3FetchMIDResp.jsonPath().getString("message").contains("Mid's not present"));
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_PlanAmexPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_04_PlanAmexFetchMIDEmptyReqType() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,CustId,"");

        int statusCode = v3FetchMIDResp.getStatusCode();
//        Assert.assertEquals(statusCode,200);
        // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_PlanAmexPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_05_PlanAmexFetchMIDInvalidReqType() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,CustId,"50K");

        int statusCode = v3FetchMIDResp.getStatusCode();
//        Assert.assertEquals(statusCode,200);
        // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_PlanAmexPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_06_PlanAmexFetchMIDDIfferentReqType() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,CustId,"QR");

        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode,200);
        // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_PlanAmexPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_07_PlanAmexPositiveFetchMID() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,CustId,"EDC");
        UserMID = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString();
        if (v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString() == null)
        {
            TC004_07_PlanAmexPositiveFetchMID();
        }
        PPI_LIMIT = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].PPI_LIMIT").toString();
        EDC_BASED_MID = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].edcBasedMid").toString();
        SMALL_MERCHANT = IfNotNull(v3FetchMIDResp,"mids[0].smallMerchantDeclaration");
        LOGGER.info("\n Applicant's MID is : " + UserMID + "\n PPI Limit of Merchant is : " + PPI_LIMIT + "\n Is  Merchant EDC Based : " + EDC_BASED_MID + "\n Is Small Merchant : " +SMALL_MERCHANT);

        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode,200);
        // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_01_PlanAmexFetchPlanEmptyMid() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", "");
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_02_PlanAmexFetchPlanInvalidMid() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", "UserMID");
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_03_PlanAmexFetchPlanNoMid() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        //QueryParam.put("mid", "UserMID");
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_04_PlanAmexFetchPlanEmptyEntity() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", "");
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_05_PlanAmexFetchPlanInvalidEntity() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", "AASHIT");
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        Assert.assertEquals(statusCode,500);
        Assert.assertTrue(v1FetchUpgradePlan.jsonPath().getString("message").contains("Failed to fetch plans"));
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_06_PlanAmexFetchPlanDifferentEntity() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", "TRUST");
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_07_PlanAmexFetchPlanNoEntity() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        //QueryParam.put("entityType", "TRUST");
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_08_PlanAmexFetchPlanEmptyCustId() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", "");
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_09_PlanAmexFetchPlanInvalidCustId() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", "AASHITCUST");
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        Assert.assertEquals(statusCode,500);
        Assert.assertTrue(v1FetchUpgradePlan.jsonPath().getString("message").contains("Failed to fetch plans"));

    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_10_PlanAmexFetchPlanDifferentCustId() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", "1001245028");
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_11_PlanAmexFetchPlanNoCustId() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        //QueryParam.put("custId", "1001245028");
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_12_PlanAmexFetchPlanEmptyPPI() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", "");
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_13_PlanAmexFetchPlanInvalidPPI() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", "PPI");
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_14_PlanAmexFetchPlanDifferentPPI() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", "50,000");
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_15_PlanAmexFetchPlanNoPPI() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        //QueryParam.put("ppiLimit", "50,000");
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_16_PlanAmexFetchPlanInvalidEdcFlag() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", "EDC_BASED_MID");

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_17_PlanAmexFetchPlanEmptyEdcFlag() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", "");

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_18_PlanAmexFetchPlanDifferentEdcFlag() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", "false");

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_19_PlanAmexFetchPlanNoEdcFlag() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        //QueryParam.put("edcBasedMid", "false");

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        //Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_07_PlanAmexPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_PlanAmexPositiveFetchUpgradePlans() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);
        QueryParam.put("ppiLimit", PPI_LIMIT);
        QueryParam.put("edcBasedMid", EDC_BASED_MID);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Get TnC for Upgrade Plans",dependsOnMethods ="TC005_PlanAmexPositiveFetchUpgradePlans",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_PlanAmexPositiveFetchTnC() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        TnC getTnCobj = new TnC();
        getTnCobj.addParameter("tncAdditionalParam", "AmexPlan");
        Response getTnCResp = middlewareServicesObject.v2GetTnC(getTnCobj, EntityType, "upgrade_merchant_plan", version, AgentToken);
        String ExpectedMsg = "SUCCESS";
        String ActualMsg = getTnCResp.jsonPath().getString("status");
        Assert.assertTrue(ActualMsg.contains(ActualMsg));
        int StatusCode = getTnCResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0,groups = {"Regression"},description = "Send OTP for Lead Creation",dependsOnMethods = "TC006_PlanAmexPositiveFetchTnC")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_PlanAmexPositiveSendOtpCreate() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_PlanAmexPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_PlanAmexPositiveValidateOtpCreate() throws Exception {
        establishConnectiontoServer(AgentToken,5);

        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeAmexPlan"));
        validateOtpObj.getProperties().setProperty("entityType",EntityType);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("mid",UserMID);
        validateOtpObj.getProperties().setProperty("custId",CustId);
        validateOtpObj.getProperties().setProperty("DOB","1993-08-25");

      //  String OTP=getOTPFromSellerPanel(mobileNo);
       // OTP = getOTP(mobileNo);
        OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant",State,OTP);
        int StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        SolutionId = validateOtp.jsonPath().getString("leadId");
        LOGGER.info("Solution Lead ID is : " + SolutionId);

        for (int i = 0; i<=3;i++)
        {

            if (SolutionId == null || SolutionId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC007_PlanAmexPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeAmexPlan"));
                ValidateOTPobjErr.getProperties().setProperty("entityType",EntityType);
                ValidateOTPobjErr.getProperties().setProperty("kybId",kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("mid",UserMID);
                ValidateOTPobjErr.getProperties().setProperty("custId",CustId);
                ValidateOTPobjErr.getProperties().setProperty("DOB","1993-08-25");
               // OTP = getOTP(mobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,EntityType, "upgrade_merchant_plan", AgentToken, version, mobileNo, "merchant", State,OTP);
                SolutionId = ResNewObj.jsonPath().getString("leadId");
                StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
                LOGGER.info("CustId is : " +CustId);
            }
            else
            {
                LOGGER.info("This is Lead ID : " + SolutionId);
                Assert.assertNotEquals(SolutionId,null);
                LOGGER.info("CustId " + CustId);
                Assert.assertNotEquals(CustId,null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }

        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }



    @Test(priority = 0,description = "PG Callback Upgrade Plans",dependsOnMethods ="TC008_PlanAmexPositiveValidateOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC019_PGCallBackFromPanel()
    {

        PgCallBackFromPanelRef(SolutionId,CustId,UserMID);
    }
}
