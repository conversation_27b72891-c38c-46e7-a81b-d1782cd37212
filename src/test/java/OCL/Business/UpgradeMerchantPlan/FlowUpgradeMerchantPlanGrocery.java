package OCL.Business.UpgradeMerchantPlan;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.UpgradePlans;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.MerchantService.v4.FetchDynamicDocs;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FlowUpgradeMerchantPlanGrocery extends BaseMethod
{

    private static final Logger LOGGER = LogManager.getLogger(FlowUpgradeMerchantPlanGrocery.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public static String AgentToken = CommonAgentToken;
    public static String mobileNo = "7770008841";
    public static  String AgentNo = "";
    public static String version = "5.0.9";
    public static String CustId ="1002359412";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String UserMID = "";
    public static String State = "";
    public static String SolutionId = "";
    public static String OTP ="";
    public static String WorkFlowId = "";
    public static String ShopInnerPhoto = "";
    public static String LOBPhoto = "";
    public static String ShopFrontPhoto= "";
    public static String EntityType = "";
    public static String XMWToken = "";

    List <Object> GetDocuments = new ArrayList<>() ;
    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    public static String OePanelDocStatus = "APPROVED";
    public static String RejectionReason = null;
    public static String DocumetRequestDeserialised = "";
    String solution_type="upgrade_merchant_plan";

    @BeforeClass
    public void AgentLoginUpgradeMerchantPlan() throws Exception {
        LOGGER.info("Before Upgrade Plan Test, Agent Login");
        //AgentToken = CommonAgentToken;
        AgentToken = AgentSessionToken("7771216290", "paytm@123");
        establishConnectiontoServer(AgentToken,5);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(7000);
        /*TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='upgrade_merchant_plan';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
        DBConnection.UpdateQueryToCloseLead(mobileNo,solution_type);}

    @Test(priority = 0,groups = {"Regression"},description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_UpgradeMerchantPositiveSendOtpBusiness()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"UNKNOWN","company_onboard",AgentToken,version,mobileNo,"company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
      //  CustId = SendOtpResp.jsonPath().getString("custId");
      //  LOGGER.info("Cust Id is : " + CustId);
     //   int StatusCode = SendOtpResp.getStatusCode();
      //  Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 0,description = "Positive Get Business",dependsOnMethods ="TC001_UpgradeMerchantPositiveSendOtpBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_UpgradeMerchantPositiveGetBusiness()
    {
        try {
            GetBusinessv3 getBusObj = new GetBusinessv3();
            Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);
            kybBusinessId = getBusResp.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
            LOGGER.info("KYB ID is : " + kybBusinessId);
            businessName = getBusResp.jsonPath().getJsonObject("businesses[0].businessName").toString();
            LOGGER.info("Business Name is : " + businessName);
            //leadId = getBusResp.jsonPath().getJsonObject("businesses[0].leadId").toString();
            //LOGGER.info("Lead Id is : " + leadId);
            int statusCode = getBusResp.getStatusCode();
            Assert.assertEquals(statusCode,200);
        }
        catch (Exception E)
        {
            LOGGER.info("Exception occured : " + E);
        }

        //getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");
    }
    @Test(priority = 0,description = "Positive Get Business Profile",dependsOnMethods ="TC002_UpgradeMerchantPositiveGetBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_UpgradeMerchantPositiveGetBusinessProfile()
    {
        BusinessProfile v3BusPro = new BusinessProfile();
        Response v3BusProRes = middlewareServicesObject.v3BusinessProfile(v3BusPro,CustId,
                mobileNo,kybBusinessId,AgentToken,version);
        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        EntityType = v3BusProRes.jsonPath().getJsonObject("businessSRO.entityType").toString();
        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }
    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_UpgradeMerchantPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_UpgradeMerchantPositiveFetchMID()
    {
        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,CustId,"EDC");
        UserMID = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString();
        if (v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString() == null)
        {
            TC004_UpgradeMerchantPositiveFetchMID();
        }
        LOGGER.info("Applicant's MID is : " + UserMID);
        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode,200);
       // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }
    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_UpgradeMerchantPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_UpgradeMerchantPositiveFetchUpgradePlans()
    {
        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        Assert.assertEquals(statusCode,200);
       // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }
    @Test(priority = 0,description = "Positive Get TnC for Upgrade Plans",dependsOnMethods ="TC005_UpgradeMerchantPositiveFetchUpgradePlans",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_UpgradeMerchantPositiveFetchTnC()
    {
        TnC getTnCobj = new TnC();
        getTnCobj.addParameter("tncAdditionalParam", "Grocery");
        Response getTnCResp = middlewareServicesObject.v2GetTnC(getTnCobj, EntityType, "upgrade_merchant_plan", version, AgentToken);
        String ExpectedMsg = "SUCCESS";
        String ActualMsg = getTnCResp.jsonPath().getString("status");
        Assert.assertTrue(ActualMsg.contains(ActualMsg));
        int StatusCode = getTnCResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0,groups = {"Regression"},description = "Send OTP for Lead Creation",dependsOnMethods = "TC006_UpgradeMerchantPositiveFetchTnC")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_UpgradeMerchantPositiveSendOtpCreate()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_UpgradeMerchantPositiveValidateOtpCreate() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpUpgradePlans"));
        validateOtpObj.getProperties().setProperty("entityType",EntityType);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("mid",UserMID);
     //   OTP = getOTP(mobileNo);
        OTP="888888";
      //  String OTP=getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant",State,OTP);
        int StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        SolutionId = validateOtp.jsonPath().getString("leadId");
        LOGGER.info("Solution Lead ID is : " + SolutionId);

        for (int i = 0; i<=3;i++)
        {

            if (SolutionId == null || SolutionId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC007_UpgradeMerchantPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("validateOtpUpgradePlans"));
                ValidateOTPobjErr.getProperties().setProperty("entityType",EntityType);
                ValidateOTPobjErr.getProperties().setProperty("kybId",kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("mid",UserMID);
                OTP = getOTP(mobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,EntityType, "upgrade_merchant_plan", AgentToken, version, mobileNo, "merchant", State,OTP);
                SolutionId = ResNewObj.jsonPath().getString("leadId");
                StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
                LOGGER.info("CustId is : " +CustId);
            }
            else
            {
                LOGGER.info("This is Lead ID : " + SolutionId);
                Assert.assertNotEquals(SolutionId,null);
                LOGGER.info("CustId " + CustId);
                Assert.assertNotEquals(CustId,null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }

        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }
    @Test(priority = 0,description = "Positive Fetch Documents for Upgrade Plan",dependsOnMethods ="TC008_UpgradeMerchantPositiveValidateOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_UpgradeMerchantPositiveFetchDocs() throws JsonProcessingException, NoSuchFieldException
    {
        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", EntityType);
        queryDocUpload.put("solutionType", "upgrade_merchant_plan");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", SolutionId);
        queryDocUpload.put("solutionTypeLevel2", "Grocery");
        queryDocUpload.put("docId",mobileNo);
        queryDocUpload.put("pageNo","0");
        queryDocUpload.put("docCount","0");

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", EntityType);
        queryFetchDoc.put("solution", "upgrade_merchant_plan");
        queryFetchDoc.put("leadId", SolutionId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);
        queryFetchDoc.put("solutionSubType","Grocery");

        FetchDynamicDocs v4Docs = new FetchDynamicDocs();

        Response v4FetchDocResp = middlewareServicesObject.v4FetchDynamicDocs(v4Docs,AgentToken,version,queryFetchDoc);
        int statusCode = v4FetchDocResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        List<Object> Documents = new ArrayList<>();
        int sizeOfList ;
        int sizeForLoop ;

        Documents = v4FetchDocResp.jsonPath().getList("docDetailsSet");

        if (Documents != null)
        {
            sizeOfList = Documents.size();
            sizeForLoop = sizeOfList - 1;
            LOGGER.info("The is size of list : " + sizeOfList);
            LOGGER.info("The is size of list for loop : " + sizeForLoop);
            LOGGER.info("The is list of Documents: " + Documents);
            ObjectMapper mapper = new ObjectMapper();

            String FetchedDocuments = "";
            FetchedDocuments = mapper.writeValueAsString(Documents);
            LOGGER.info("Deserialized Response is  : " + FetchedDocuments);

            if (FetchedDocuments != null) {
                LOGGER.info(" Inside IF condition for Document Upload ");
                for (int i = 0; i <= sizeForLoop; ++i) {
                    if (i <= sizeForLoop) {
                        LOGGER.info(" Inside Loop for Document Upload ");
                        LOGGER.info("The value of I in loop : " + i);
                        Map docType = (Map) Documents.get(i);
                        LOGGER.info("This is Serialized current docType : " + docType);
                        String currentDocType = (String) docType.get("docType");
                        LOGGER.info("Deserialized Response current of docType  : " + docType.get("docType"));

                        SubmitDocs v3DocSubmit = new SubmitDocs();

                        queryDocUpload.put("docType",currentDocType);

                        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, AgentToken,version,queryDocUpload);
                        int errorCode = submitDocs.jsonPath().getInt("errorCode");
                        Assert.assertEquals(errorCode, 204);

                    } else {
                        LOGGER.info(" Else Condition for Upload Documents ");
                        break;
                    }

                }
            }
        }
        else
        {
            LOGGER.info("No ducuments to upload");
        }

        //v4Docs.validateResponseAgainstJSONSchema("MerchantService/V4/FetchDynamicDocs/FetchDynamicDocResponseSchema.json");
    }



    @Test(priority = 0, description = "Fetch Lead Details on Panel", groups = {"Regression"}, dependsOnMethods = "TC009_UpgradeMerchantPositiveFetchDocs")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0010_UpgradeMerchantPositivePanelSubmit() {
        XMWToken=XMWCookie;
        LOGGER.info("XMW Cookie is : " + XMWToken);

        try {
            FetchLead v1FetchLeadObj = new FetchLead(SolutionId);
            waitForLoad(4000);
            Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWToken);

            String leadStage = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
            LOGGER.info("This is Current Lead Stage : " + leadStage);

            if(leadStage.contains("QC_ACTION_PENDING"))
            {
                Map <String,String> RequestPanel = new HashMap<>();

                RequestPanel.put("docStatus",OePanelDocStatus);
                RequestPanel.put("rejectionReason",RejectionReason);
                RequestPanel.put("leadId",SolutionId);

                Map <String,String> ResponsePanel = FetchPanelLead(RequestPanel);

                DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
                WorkFlowId = ResponsePanel.get("WorkFlowId");
                LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));

                ReallocatingAgent(SolutionId,"1152");

                EditLead v1EditLeadObj = new EditLead(SolutionId, P.TESTDATA.get("EditLeadUpgadePlan"));

                v1EditLeadObj.getProperties().setProperty("documents", DocumetRequestDeserialised);
                v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

                Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "9560526665", "1106992015", XMWToken, "application/json");

                int statusCode = responseObject.getStatusCode();
                Assert.assertEquals(statusCode, 200);


            }

            else
            {
                LOGGER.info("No Documents for QC");

            }

            PgCallBackFromPanelRef(SolutionId,CustId,UserMID);
        }
        catch (Exception e)
        {
            LOGGER.info("Execption " + e);
            LOGGER.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());

        }
    }


}
