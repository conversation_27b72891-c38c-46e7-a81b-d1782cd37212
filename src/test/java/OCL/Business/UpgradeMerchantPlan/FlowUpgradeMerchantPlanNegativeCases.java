package OCL.Business.UpgradeMerchantPlan;

import Request.MerchantService.v1.UpgradePlans;
import Request.MerchantService.v1.merchant.fetchAllTerminal;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class FlowUpgradeMerchantPlanNegativeCases extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowUpgradeMerchantPlanNegativeCases.class);

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();

    public static String AgentToken = CommonAgentToken;
    public static String mobileNo = "6543122222";
    public static  String AgentNo = "";
    public static String version = "5.0.9";
    public static String CustId ="1704128579";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String leadId = "";
    public static String UserMID = "";
    public static String State = "";
    public static String Message = "";
    public static String OTP ="";
    public static String EntityType = "";
    public static String XMWToken = "";
    public static String PPI_LIMIT = "";
    public static String EDC_BASED_MID = "";
    public static String SMALL_MERCHANT = "";

    @BeforeClass
    public void AgentLoginUpgradeMerchantPlan() throws Exception {
        LOGGER.info("Before Upgrade Plan Test, Agent Login");
       // AgentToken = CommonAgentToken;
        AgentToken = AgentSessionToken("7771216290","paytm@123");
        establishConnectiontoServer(AgentToken,5);
        LOGGER.info(" Inside DB execution to reset lead : ");
        waitForLoad(7000);
        DBConnection.UpdateQueryToCloseLead(mobileNo,"upgrade_merchant_plan");

        /*TestBase testBase =new TestBase();
        DbName = DbStagingSprint;
        testBase.UpdateQuery("UPDATE user_business_mapping SET status=2 WHERE mobile_number= '"+mobileNo+"' and status = '0' and solution_type='upgrade_merchant_plan';");
        int UpdateRes = TestBase.UpdateQueryResult;
        LOGGER.info("These are Updated Row/s : " +UpdateRes); */
    }

    @Test(priority = 0,groups = {"Regression"},description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_UpgradeMerchantPositiveSendOtpBusiness()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"UNKNOWN","company_onboard",AgentToken,version,mobileNo,"company");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
    //    CustId = SendOtpResp.jsonPath().getString("custId");
   //     LOGGER.info("Cust Id is : " + CustId);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 0,description = "Positive Get Business",dependsOnMethods ="TC001_UpgradeMerchantPositiveSendOtpBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_UpgradeMerchantPositiveGetBusiness()
    {
        try {
            GetBusinessv3 getBusObj = new GetBusinessv3();
            Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);
            kybBusinessId = getBusResp.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
            LOGGER.info("KYB ID is : " + kybBusinessId);
            businessName = getBusResp.jsonPath().getJsonObject("businesses[0].businessName").toString();
            LOGGER.info("Business Name is : " + businessName);
            //leadId = getBusResp.jsonPath().getJsonObject("businesses[0].leadId").toString();
            //LOGGER.info("Lead Id is : " + leadId);
            int statusCode = getBusResp.getStatusCode();
            Assert.assertEquals(statusCode,200);
        }
        catch (Exception E)
        {
            LOGGER.info("Exception occured : " + E);
        }

        //getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");
    }
    @Test(priority = 0,description = "Positive Get Business Profile",dependsOnMethods ="TC002_UpgradeMerchantPositiveGetBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_UpgradeMerchantPositiveGetBusinessProfile()
    {
        BusinessProfile v3BusPro = new BusinessProfile();
        Response v3BusProRes = middlewareServicesObject.v3BusinessProfile(v3BusPro,CustId,
                mobileNo,kybBusinessId,AgentToken,version);
        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        EntityType = v3BusProRes.jsonPath().getJsonObject("businessSRO.entityType").toString();
        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }
    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_UpgradeMerchantPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_UpgradeMerchantPositiveFetchMID()
    {
        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServicesObject.v3FetchMID(v3FetchMid,AgentToken,version,CustId,"EDC");
        UserMID = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString();
        if (v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString() == null)
        {
            TC004_UpgradeMerchantPositiveFetchMID();
        }
        PPI_LIMIT = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].PPI_LIMIT").toString();
        EDC_BASED_MID = v3FetchMIDResp.jsonPath().getJsonObject("mids[0].edcBasedMid").toString();
        SMALL_MERCHANT = IfNotNull(v3FetchMIDResp,"mids[0].smallMerchantDeclaration");
        LOGGER.info("\n Applicant's MID is : " + UserMID + "\n PPI Limit of Merchant is : " + PPI_LIMIT + "\n Is  Merchant EDC Based : " + EDC_BASED_MID + "\n Is Small Merchant : " +SMALL_MERCHANT);

        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode,200);
        // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }
    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_UpgradeMerchantPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_UpgradeMerchantPositiveFetchUpgradePlans()
    {
        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", UserMID);
        QueryParam.put("entityType", EntityType);
        QueryParam.put("custId", CustId);

        Response v1FetchUpgradePlan = middlewareServicesObject.v1FetchUpgradePlans(v1UpgradePlan,AgentToken,version,QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }
    @Test(priority = 0,description = "Positive Get TnC for Upgrade Plans",dependsOnMethods ="TC005_UpgradeMerchantPositiveFetchUpgradePlans",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_UpgradeMerchantPositiveFetchTnC()
    {
        TnC getTnCobj = new TnC();
        getTnCobj.addParameter("tncAdditionalParam", "Grocery");
        Response getTnCResp = middlewareServicesObject.v2GetTnC(getTnCobj, EntityType, "upgrade_merchant_plan", version, AgentToken);
        String ExpectedMsg = "SUCCESS";
        String ActualMsg = getTnCResp.jsonPath().getString("status");
        Assert.assertTrue(ActualMsg.contains(ActualMsg));
        int StatusCode = getTnCResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0,groups = {"Regression"},description = "Send OTP for Lead Creation",dependsOnMethods = "TC006_UpgradeMerchantPositiveFetchTnC")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_UpgradeMerchantPositiveSendOtpCreate()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        State = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("State is : " + State);
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_GroceryPlanValidateOtp() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpUpgradePlans"));
        validateOtpObj.getProperties().setProperty("entityType",EntityType);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("mid",UserMID);
      //  OTP = getOTP(mobileNo);
        OTP="888888";
//        String OTP=getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant",State,OTP);
        int StatusCode = validateOtp.getStatusCode();
    //    Message = validateOtp.jsonPath().getString("message");
//
       /* for (int i = 0; i<=3;i++)
        {

            if (!Message.contains("nstrument not configured") && StatusCode!=500)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC007_UpgradeMerchantPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("validateOtpUpgradePlans"));
                ValidateOTPobjErr.getProperties().setProperty("entityType",EntityType);
                ValidateOTPobjErr.getProperties().setProperty("kybId",kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("mid",UserMID);
                OTP = getOTP(mobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,EntityType, "upgrade_merchant_plan", AgentToken, version, mobileNo, "merchant", State,OTP);
                Message = ResNewObj.jsonPath().getString("message");
                StatusCode = ResNewObj.getStatusCode();

            }
            else
            {
                LOGGER.info("This is Message : " + Message);
                Assert.assertNotEquals(Message,null);
                Assert.assertEquals(StatusCode, 500);
                break;
            }
        }
        Assert.assertTrue(Message.contains("not configured"));*/
        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_AmexPlanValidateOtp() throws IOException, JSchException, InterruptedException {
        TC007_UpgradeMerchantPositiveSendOtpCreate();
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeAmexPlan"));
        validateOtpObj.getProperties().setProperty("entityType",EntityType);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("mid",UserMID);
        validateOtpObj.getProperties().setProperty("custId",CustId);
        validateOtpObj.getProperties().setProperty("DOB","1993-08-25");

      //  OTP = getOTP(mobileNo);
     //   OTP=getOTPFromSellerPanel(mobileNo);
        OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant",State,OTP);
        int StatusCode = validateOtp.getStatusCode();
      /*  Message = validateOtp.jsonPath().getString("message");
        LOGGER.info("Message is : " + Message);

        for (int i = 0; i<=3;i++)
        {

            if (!Message.contains("nstrument not configured") && StatusCode!=500)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC007_UpgradeMerchantPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeAmexPlan"));
                ValidateOTPobjErr.getProperties().setProperty("entityType",EntityType);
                ValidateOTPobjErr.getProperties().setProperty("kybId",kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("mid",UserMID);
                ValidateOTPobjErr.getProperties().setProperty("custId",CustId);
                ValidateOTPobjErr.getProperties().setProperty("DOB","1993-08-25");

                OTP = getOTP(mobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,EntityType, "upgrade_merchant_plan", AgentToken, version, mobileNo, "merchant", State,OTP);
                Message = ResNewObj.jsonPath().getString("message");
                StatusCode = ResNewObj.getStatusCode();

            }
            else
            {
                LOGGER.info("This is Message : " + Message);
                Assert.assertNotEquals(Message,null);
                Assert.assertEquals(StatusCode, 500);
                break;
            }
        }
        Assert.assertTrue(Message.contains("not configured"));*//**/
        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC010_InternationCardPlanValidateOtp() throws IOException, JSchException, InterruptedException {
        TC007_UpgradeMerchantPositiveSendOtpCreate();
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeInternationalCard"));
        validateOtpObj.getProperties().setProperty("entityType",EntityType);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("mid",UserMID);
        validateOtpObj.getProperties().setProperty("custId",CustId);

    //    OTP = getOTP(mobileNo);
       // OTP=getOTPFromSellerPanel(mobileNo);
        OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant",State,OTP);
        int StatusCode = validateOtp.getStatusCode();
      /*  Message = validateOtp.jsonPath().getString("message");
        LOGGER.info("Message is : " + Message);

        for (int i = 0; i<=3;i++)
        {

            if (!Message.contains("nstrument not configured") && StatusCode!=500)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC007_UpgradeMerchantPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeInternationalCard"));
                ValidateOTPobjErr.getProperties().setProperty("entityType",EntityType);
                ValidateOTPobjErr.getProperties().setProperty("kybId",kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("mid",UserMID);
                ValidateOTPobjErr.getProperties().setProperty("custId",CustId);

                OTP = getOTP(mobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,EntityType, "upgrade_merchant_plan", AgentToken, version, mobileNo, "merchant", State,OTP);
                Message = ResNewObj.jsonPath().getString("message");
                StatusCode = ResNewObj.getStatusCode();

            }
            else
            {
                LOGGER.info("This is Message : " + Message);
                Assert.assertNotEquals(Message,null);
                Assert.assertEquals(StatusCode, 500);
                break;
            }
        }

        Assert.assertTrue(Message.contains("not configured"));*/
        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC011_EmiOfferingPlanValidateOtp() throws IOException, JSchException, InterruptedException {
        TC007_UpgradeMerchantPositiveSendOtpCreate();
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeEmiOffering"));
        validateOtpObj.getProperties().setProperty("entityType",EntityType);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("mid",UserMID);
        validateOtpObj.getProperties().setProperty("custId",CustId);

       // OTP = getOTP(mobileNo);
      //  OTP=getOTPFromSellerPanel(mobileNo);
        OTP="888888";
        LOGGER.info("This is OTP " + OTP);
        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,EntityType,"upgrade_merchant_plan",AgentToken,version,mobileNo,"merchant",State,OTP);
        int StatusCode = validateOtp.getStatusCode();

        /*Message = validateOtp.jsonPath().getString("message");
        LOGGER.info("Message is : " + Message);

        for (int i = 0; i<=3;i++)
        {

            if (!Message.contains("Credit / Debit Card is disabled") && StatusCode!=400)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC007_UpgradeMerchantPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeEmiOffering"));
                ValidateOTPobjErr.getProperties().setProperty("entityType",EntityType);
                ValidateOTPobjErr.getProperties().setProperty("kybId",kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("mid",UserMID);
                ValidateOTPobjErr.getProperties().setProperty("custId",CustId);

                OTP = getOTP(mobileNo);
                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,EntityType, "upgrade_merchant_plan", AgentToken, version, mobileNo, "merchant", State,OTP);
                Message = ResNewObj.jsonPath().getString("message");
                StatusCode = ResNewObj.getStatusCode();

            }
            else
            {
                Assert.assertNotEquals(Message,null);
                Assert.assertEquals(StatusCode, 400);
                break;
            }
        }
        Assert.assertTrue(Message.contains("Credit / Debit Card is disabled for this merchant account. Please enable it before offering EDC EMI to merchant"));*/
        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");
    }


    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC012_EdcAmcPlanValidateOtp()
    {
        fetchAllTerminal fetchTerminl = new fetchAllTerminal();

        Map<String,String>QueryParams = new HashMap<>();
        QueryParams.put("mid",UserMID);
        QueryParams.put("subscriptionType","AMC");
        QueryParams.put("filterBySubscriptionTypeOnly","false");
        QueryParams.put("subscriptionStatus","INACTIVE");


        Response TerminalResp = middlewareServicesObject.v1FetchallTerminal(fetchTerminl,QueryParams,AgentToken,version);
//        Assert.assertTrue(TerminalResp.jsonPath().getString("displayMessage").contains("No EDC Machine is active or mapped for this merchant MID currently. Please map machine first to offer AMC services to this merchant account"));
    }

    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC013_CashAtPosPlanFetchTerminal()
    {
        fetchAllTerminal fetchTerminl = new fetchAllTerminal();

        Map<String,String>QueryParams = new HashMap<>();
        QueryParams.put("mid",UserMID);
        QueryParams.put("deviceType","Android");

        Response TerminalResp = middlewareServicesObject.v1FetchallTerminal(fetchTerminl,QueryParams,AgentToken,version);
     //   Assert.assertTrue(TerminalResp.jsonPath().getString("displayMessage").contains("No Android device is mapped for this merchant MID currently"));
    }

    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_UpgradeMerchantPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC014_PaymentConfirmationPlanFetchTerminal()
    {
        fetchAllTerminal fetchTerminl = new fetchAllTerminal();

        Map<String,String>QueryParams = new HashMap<>();
        QueryParams.put("mid",UserMID);
        QueryParams.put("deviceType","Android");

        Response TerminalResp = middlewareServicesObject.v1FetchallTerminal(fetchTerminl,QueryParams,AgentToken,version);
      //  Assert.assertTrue(TerminalResp.jsonPath().getString("displayMessage").contains("No Android device is mapped for this merchant MID currently"));
    }


}
