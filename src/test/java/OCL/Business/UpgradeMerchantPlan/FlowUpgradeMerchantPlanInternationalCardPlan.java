package OCL.Business.UpgradeMerchantPlan;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.UpgradePlans;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.MerchantService.v4.FetchDynamicDocs;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.TestBase;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FlowUpgradeMerchantPlanInternationalCardPlan extends BaseMethod {

    private static final Logger LOGGER = LogManager.getLogger(FlowUpgradeMerchantPlanInternationalCardPlan.class);
    private static final int MAX_RETRIES = 3;
    private static final int RETRY_DELAY_MS = 5000;
    private static final String SUCCESS_STATUS = "SUCCESS";
    private static final String SOLUTION_TYPE = "upgrade_merchant_plan";
    private static final String SOLUTION_TYPE_LEVEL_2 = "International Card Acceptance Plan";
    
    // Test configuration
    private static final String TEST_MOBILE_NO = "6543122222";
    private static final String TEST_VERSION = "5.0.9";
    private static final String TEST_CUST_ID = "1704128579";
    private static final String TEST_AGENT_MOBILE = "7771216290";
    private static final String TEST_AGENT_PASSWORD = "paytm@123";
    private static final String DEFAULT_OTP = "888888";
    
    private final MiddlewareServices middlewareServices;
    private final Map<String, String> testContext;
    
    public FlowUpgradeMerchantPlanInternationalCardPlan() {
        this.middlewareServices = new MiddlewareServices();
        this.testContext = new HashMap<>();
        initializeTestContext();
    }
    
    private void initializeTestContext() {
        testContext.put("mobileNo", TEST_MOBILE_NO);
        testContext.put("version", TEST_VERSION);
        testContext.put("custId", TEST_CUST_ID);
        testContext.put("oePanelDocStatus", "APPROVED");
    }

    @BeforeClass
    public void setup() throws Exception {
        LOGGER.info("Setting up test prerequisites");
        testContext.put("agentToken", AgentSessionToken(TEST_AGENT_MOBILE, TEST_AGENT_PASSWORD));
        establishConnectiontoServer(testContext.get("agentToken"), 5);
        
        LOGGER.info("Resetting lead status in database");
        DBConnection.UpdateQueryToCloseLeadsolnlevel2(
            testContext.get("mobileNo"),
            SOLUTION_TYPE,
            SOLUTION_TYPE_LEVEL_2
        );
    }

    @Test(priority = 0, groups = {"Regression"}, description = "Send OTP for Business")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC001_InternationalCardPositiveSendOtpBusiness() {
        SendOtp sendOtp = new SendOtp();
        Response response = middlewareServices.v3SentOtp(
            sendOtp,
            "UNKNOWN",
            "company_onboard",
            testContext.get("agentToken"),
            testContext.get("version"),
            testContext.get("mobileNo"),
            "company"
        );
        
        validateResponse(response, "Send OTP");
        
        String status = response.jsonPath().getString("status");
        Assert.assertTrue(status.contains(SUCCESS_STATUS),
            String.format("Expected status '%s' but got '%s'", SUCCESS_STATUS, status));
    }

    @Test(priority = 0, description = "Get Business Details", 
          dependsOnMethods = "TC001_InternationalCardPositiveSendOtpBusiness",
          groups = {"Regression"})
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC002_InternationalCardPositiveGetBusiness() {
        try {
            GetBusinessv3 getBusiness = new GetBusinessv3();
            Response response = middlewareServices.v3GetBusiness(
                getBusiness,
                testContext.get("agentToken"),
                testContext.get("version"),
                testContext.get("custId")
            );
            
            validateResponse(response, "Get Business");
            
            testContext.put("kybBusinessId", response.jsonPath().getString("businesses[0].kybBusinessId"));
            testContext.put("businessName", response.jsonPath().getString("businesses[0].businessName"));
            
            LOGGER.info("KYB ID: {}, Business Name: {}", 
                testContext.get("kybBusinessId"), 
                testContext.get("businessName"));
        } catch (Exception e) {
            LOGGER.error("Failed to get business details", e);
            Assert.fail("Failed to get business details: " + e.getMessage());
        }
    }

    @Test(priority = 0,description = "Positive Get Business Profile",dependsOnMethods ="TC002_InternationalCardPositiveGetBusiness",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_InternationalCardPositiveGetBusinessProfile()
    {
        BusinessProfile v3BusPro = new BusinessProfile();
        Response v3BusProRes = middlewareServices.v3BusinessProfile(v3BusPro,testContext.get("custId"),
                testContext.get("mobileNo"),testContext.get("kybBusinessId"),testContext.get("agentToken"),testContext.get("version"));
        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode,200);
        testContext.put("entityType", v3BusProRes.jsonPath().getJsonObject("businessSRO.entityType").toString());
        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant's MID",dependsOnMethods = "TC003_InternationalCardPositiveGetBusinessProfile")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_InternationalCardPositiveFetchMID()
    {
        MID v3FetchMid = new MID();
        Response v3FetchMIDResp = middlewareServices.v3FetchMID(v3FetchMid,testContext.get("agentToken"),testContext.get("version"),testContext.get("custId"),"EDC");
        if (v3FetchMIDResp.getStatusCode()==400)
        {
            TC004_InternationalCardPositiveFetchMID();
        }
        testContext.put("userMid", v3FetchMIDResp.jsonPath().getJsonObject("mids[0].mid").toString());
        testContext.put("ppilimit", v3FetchMIDResp.jsonPath().getJsonObject("mids[0].PPI_LIMIT").toString());
        testContext.put("edcBasedMid", v3FetchMIDResp.jsonPath().getJsonObject("mids[0].edcBasedMid").toString());
        testContext.put("smallMerchantDeclaration", IfNotNull(v3FetchMIDResp,"mids[0].smallMerchantDeclaration"));
        LOGGER.info("\n Applicant's MID is : " + testContext.get("userMid") + "\n PPI Limit of Merchant is : " + testContext.get("ppilimit") + "\n Is  Merchant EDC Based : " + testContext.get("edcBasedMid") + "\n Is Small Merchant : " +testContext.get("smallMerchantDeclaration"));

        int statusCode = v3FetchMIDResp.getStatusCode();
        Assert.assertEquals(statusCode,200);
        // v3FetchMid.validateResponseAgainstJSONSchema("MerchantService/V3/GetMID/GetMIDSchema.json");
    }

    @Test(priority = 0,description = "Fetch Applicant Upgrade Plans",dependsOnMethods = "TC004_InternationalCardPositiveFetchMID",groups = {"Regression"})
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_InternationalCardPositiveFetchUpgradePlans()
    {
        UpgradePlans v1UpgradePlan = new UpgradePlans();

        Map<String,String> QueryParam = new HashMap<>();
        QueryParam.put("mid", testContext.get("userMid"));
        QueryParam.put("entityType", testContext.get("entityType"));
        QueryParam.put("custId", testContext.get("custId"));
        QueryParam.put("ppiLimit", testContext.get("ppilimit"));
        QueryParam.put("edcBasedMid", testContext.get("edcBasedMid"));

        Response v1FetchUpgradePlan = middlewareServices.v1FetchUpgradePlans(v1UpgradePlan,testContext.get("agentToken"),testContext.get("version"),QueryParam);
        int statusCode =  v1FetchUpgradePlan.getStatusCode();
        Assert.assertEquals(statusCode,200);
        // v1UpgradePlan.validateResponseAgainstJSONSchema("MerchantService/V1/GetUpgradePlans/UpgradePlansResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Get TnC for Upgrade Plans",dependsOnMethods ="TC005_InternationalCardPositiveFetchUpgradePlans",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_InternationalCardPositiveFetchTnC()
    {
        TnC getTnCobj = new TnC();
        getTnCobj.addParameter("tncAdditionalParam", "InternationalCardAcceptancePlan");
        Response getTnCResp = middlewareServices.v2GetTnC(getTnCobj, testContext.get("entityType"), "upgrade_merchant_plan", testContext.get("version"), testContext.get("agentToken"));
        String ExpectedMsg = "SUCCESS";
        String ActualMsg = getTnCResp.jsonPath().getString("status");
        Assert.assertTrue(ActualMsg.contains(ActualMsg));
        int StatusCode = getTnCResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }
    @Test(priority = 0,groups = {"Regression"},description = "Send OTP for Lead Creation",dependsOnMethods = "TC006_InternationalCardPositiveFetchTnC")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_InternationalCardPositiveSendOtpCreate()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServices.v3SentOtp(v3SendOtp,testContext.get("entityType"),"upgrade_merchant_plan",testContext.get("agentToken"),testContext.get("version"),testContext.get("mobileNo"),"merchant");
        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        testContext.put("state", SendOtpResp.jsonPath().getString("state"));
        LOGGER.info("State is : " + testContext.get("state"));
        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);
    }
    @Test(priority = 0,description = "Positive Validate OTP Upgrade Plans",dependsOnMethods ="TC007_InternationalCardPositiveSendOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_InternationalCardPositiveValidateOtpCreate() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeInternationalCard"));
        validateOtpObj.getProperties().setProperty("entityType",testContext.get("entityType"));
        validateOtpObj.getProperties().setProperty("kybId",testContext.get("kybBusinessId"));
        validateOtpObj.getProperties().setProperty("mid",testContext.get("userMid"));
        validateOtpObj.getProperties().setProperty("custId",testContext.get("custId"));

        //   OTP = getOTP(mobileNo);
        testContext.put("otp", DEFAULT_OTP);
        //  String OTP=getOTPFromSellerPanel(mobileNo);
        LOGGER.info("This is OTP " + testContext.get("otp"));
        Response validateOtp = middlewareServices.v3ValidateOtp(validateOtpObj,testContext.get("entityType"),"upgrade_merchant_plan",testContext.get("agentToken"),testContext.get("version"),testContext.get("mobileNo"),"merchant",testContext.get("state"),testContext.get("otp"));
        int StatusCode = validateOtp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        testContext.put("solutionId", validateOtp.jsonPath().getString("leadId"));
        LOGGER.info("Solution Lead ID is : " + testContext.get("solutionId"));

        for (int i = 0; i<=3;i++)
        {

            if (testContext.get("solutionId") == null || testContext.get("solutionId").isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC007_InternationalCardPositiveSendOtpCreate();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("ValidateOtpUpgradeInternationalCard"));
                ValidateOTPobjErr.getProperties().setProperty("entityType",testContext.get("entityType"));
                ValidateOTPobjErr.getProperties().setProperty("kybId",testContext.get("kybBusinessId"));
                ValidateOTPobjErr.getProperties().setProperty("mid",testContext.get("userMid"));
                ValidateOTPobjErr.getProperties().setProperty("custId",testContext.get("custId"));
                testContext.put("otp", getOTP(testContext.get("mobileNo")));
                LOGGER.info("This is OTP " + testContext.get("otp"));
                Response ResNewObj = middlewareServices.v3ValidateOtp(ValidateOTPobjErr,testContext.get("entityType"), "upgrade_merchant_plan", testContext.get("agentToken"), testContext.get("version"), testContext.get("mobileNo"), "merchant", testContext.get("state"),testContext.get("otp"));
                testContext.put("solutionId", ResNewObj.jsonPath().getString("leadId"));
                StatusCode = ResNewObj.getStatusCode();
                testContext.put("custId", ResNewObj.jsonPath().getString("custId"));
                LOGGER.info("CustId is : " +testContext.get("custId"));
            }
            else
            {
                LOGGER.info("This is Lead ID : " + testContext.get("solutionId"));
                Assert.assertNotEquals(testContext.get("solutionId"),null);
                LOGGER.info("CustId " + testContext.get("custId"));
                Assert.assertNotEquals(testContext.get("custId"),null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }
    }

    @Test(priority = 0,description = "Positive Fetch Documents for Upgrade Plan",dependsOnMethods ="TC008_InternationalCardPositiveValidateOtpCreate",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_InternationalCardPositiveFetchDocs() throws JsonProcessingException, NoSuchFieldException
    {
        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", testContext.get("entityType"));
        queryDocUpload.put("solutionType", "upgrade_merchant_plan");
        queryDocUpload.put("merchantCustId", testContext.get("custId"));
        queryDocUpload.put("leadId", testContext.get("solutionId"));
        queryDocUpload.put("solutionTypeLevel2", "International Card Acceptance Plan");
        queryDocUpload.put("docId",testContext.get("mobileNo"));
        queryDocUpload.put("pageNo","0");
        queryDocUpload.put("docCount","0");

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", testContext.get("entityType"));
        queryFetchDoc.put("solution", "upgrade_merchant_plan");
        queryFetchDoc.put("leadId", testContext.get("solutionId"));
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", testContext.get("custId"));
        queryFetchDoc.put("solutionSubType","International Card Acceptance Plan");

        FetchDynamicDocs v4Docs = new FetchDynamicDocs();

        Response v4FetchDocResp = middlewareServices.v4FetchDynamicDocs(v4Docs,testContext.get("agentToken"),testContext.get("version"),queryFetchDoc);
        int statusCode = v4FetchDocResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        List<Object> Documents = new ArrayList<>();
        int sizeOfList ;
        int sizeForLoop ;

        Documents = v4FetchDocResp.jsonPath().getList("docDetailsSet");

        if (Documents != null)
        {
            sizeOfList = Documents.size();
            sizeForLoop = sizeOfList - 1;
            LOGGER.info("The is size of list : " + sizeOfList);
            LOGGER.info("The is size of list for loop : " + sizeForLoop);
            LOGGER.info("The is list of Documents: " + Documents);
            ObjectMapper mapper = new ObjectMapper();

            String FetchedDocuments = "";
            FetchedDocuments = mapper.writeValueAsString(Documents);
            LOGGER.info("Deserialized Response is  : " + FetchedDocuments);

            if (FetchedDocuments != null) {
                LOGGER.info(" Inside IF condition for Document Upload ");
                for (int i = 0; i <= sizeForLoop; ++i) {
                    if (i <= sizeForLoop) {
                        LOGGER.info(" Inside Loop for Document Upload ");
                        LOGGER.info("The value of I in loop : " + i);
                        Map docType = (Map) Documents.get(i);
                        LOGGER.info("This is Serialized current docType : " + docType);
                        String currentDocType = (String) docType.get("docType");
                        LOGGER.info("Deserialized Response current of docType  : " + docType.get("docType"));

                        SubmitDocs v3DocSubmit = new SubmitDocs();

                        queryDocUpload.put("docType",currentDocType);

                        Response submitDocs = middlewareServices.V3SubmitDocs(v3DocSubmit, testContext.get("agentToken"),testContext.get("version"),queryDocUpload);
                        int errorCode = submitDocs.jsonPath().getInt("errorCode");
                        Assert.assertEquals(errorCode, 204);

                    } else {
                        LOGGER.info(" Else Condition for Upload Documents ");
                        break;
                    }

                }
            }
        }
        else
        {
            LOGGER.info("No ducuments to upload");
        }

        //v4Docs.validateResponseAgainstJSONSchema("MerchantService/V4/FetchDynamicDocs/FetchDynamicDocResponseSchema.json");
    }



    @Test(priority = 0, description = "Fetch Lead Details on Panel", groups = {"Regression"}, dependsOnMethods = "TC009_InternationalCardPositiveFetchDocs")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC010_InternationalCardPositivePanelSubmit() throws SQLException
    {

        try {
            FetchLead v1FetchLeadObj = new FetchLead(testContext.get("solutionId"));
            waitForLoad(5000);
            String cookie = findXMWTokenforPanel(TEST_AGENT_MOBILE, TEST_AGENT_PASSWORD);
            Response v1FetchLeadResp = middlewareServices.v1FetchLeadPanel(v1FetchLeadObj, cookie);

            testContext.put("leadStage", v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString());
            LOGGER.info("This is Current Lead Stage : " + testContext.get("leadStage"));
            testContext.put("workFlowId", v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.timelineDetail[2].workflowStatusId").toString());
            LOGGER.info("The QC workflow id is : " + testContext.get("workFlowId"));
            testContext.put("docId", v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.documents[0].uuid").toString());
            LOGGER.info("New Doc uuid after doc upload is : " + testContext.get("docId"));

            if (testContext.get("leadStage").equals("QC_ACTION_PENDING")) {
               /* TestBase testBase = new TestBase();
                DbName = DbStagingSprint;
                testBase.assignAgentviaDB(mobileNo, "upgrade_merchant_plan", "International Card Acceptance Plan");
                int UpdateRes = TestBase.UpdateQueryResult;
                LOGGER.info("These are Updated Row/s : " + UpdateRes);*/
                int UBMID = DBConnection.getUserBusinessMappingId(testContext.get("mobileNo"), "upgrade_merchant_plan", "International Card Acceptance Plan");
                System.out.println("UBM ID IS "+UBMID);
                DBConnection.assignAgentViaDB("1152",UBMID);
            } else {
                LOGGER.info("Lead is not in QC stage");
            }

            EditLead v1EditLeadObj = new EditLead(testContext.get("solutionId"), P.TESTDATA.get("EditLeadUpgradePlan"));

            v1EditLeadObj.getProperties().setProperty("uuid", testContext.get("docId"));
            v1EditLeadObj.getProperties().setProperty("uuids", testContext.get("docId"));
            v1EditLeadObj.getProperties().setProperty("workflowStatusId", testContext.get("workFlowId"));
            v1EditLeadObj.getProperties().setProperty("segment", "BFSI");
            v1EditLeadObj.getProperties().setProperty("subSegment", "Insurance");

            Response responseObject = middlewareServices.v1EditLeadOE(v1EditLeadObj, "SUBMIT", TEST_AGENT_MOBILE, "1001647902", cookie, "application/json");

            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 200);

            String LeadStage = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadStage.contains("PANEL_SUCCESS"));


        }
        catch (Exception e)
        {
            LOGGER.info("Execption " + e);
            LOGGER.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());

        }
    }



    @Test(priority = 0,description = "PG Callback Upgrade Plans",dependsOnMethods ="TC010_InternationalCardPositivePanelSubmit",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC011_PGCallBackFromPanel()
    {
        if (testContext.get("leadStage").equals("PANEL_SUCCESS")) {
            Response resObjGetMerchant = FetchPanelLeadviaLeadID(testContext.get("solutionId"));
            testContext.put("pgRequestID", resObjGetMerchant.jsonPath().getJsonObject("leadDetails.additionalDetails.pgReferenceId").toString());
            LOGGER.info("PG Request id is : " + testContext.get("pgRequestID"));
            ManualPgCallBack(testContext.get("custId"), testContext.get("pgRequestID"), testContext.get("userMid"));
        }
        else
        {
            LOGGER.info("Lead is not in correct stage");
        }

    }

    private void validateResponse(Response response, String operationName) {
        int statusCode = response.getStatusCode();
        if (statusCode != 200) {
            LOGGER.error("{} request failed with status code: {}", operationName, statusCode);
            LOGGER.error("Response body: {}", response.getBody().asString());
            Assert.fail(String.format("%s request failed with status code: %d", operationName, statusCode));
        }
    }

    private void retryOperation(Runnable operation, String operationName) throws InterruptedException {
        Exception lastException = null;
        for (int i = 0; i < MAX_RETRIES; i++) {
            try {
                operation.run();
                return;
            } catch (Exception e) {
                lastException = e;
                LOGGER.warn("Attempt {} failed for {}: {}", i + 1, operationName, e.getMessage());
                if (i < MAX_RETRIES - 1) {
                    Thread.sleep(RETRY_DELAY_MS);
                }
            }
        }
        throw new RuntimeException("Operation " + operationName + " failed after " + MAX_RETRIES + " attempts", lastException);
    }
}

