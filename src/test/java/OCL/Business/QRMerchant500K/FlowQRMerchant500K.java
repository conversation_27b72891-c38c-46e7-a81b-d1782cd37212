package OCL.Business.QRMerchant500K;

import Request.MerchantService.oe.panel.v1.editLead.EditLead;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Request.MerchantService.v1.GetCompany;
import Request.MerchantService.v1.GstExemptionList;
import Request.MerchantService.v1.PostCompany;
import Request.MerchantService.v2.Banks;
import Request.MerchantService.v2.TnC;
import Request.MerchantService.v3.*;
import Request.MerchantService.v4.PennyDropMultiNameMatch;
import Request.UAD.Category;
import Request.UAD.SubCategory;
import Request.Wallet.CreateUserWallet;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import Services.Wallet.WalletServices;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.goldengate.common.BaseMethod;
import com.jcraft.jsch.JSchException;
import com.opencsv.CSVWriter;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*

public class FlowQRMerchant500K extends BaseMethod {

    private static final Logger LOGGER = Logger.getLogger(FlowQRMerchant500K.class);

    public static String AgentToken = "";
    public static String CustId = "";
    public static String mobileNo = "";
    public static String newState = "";
    public static  String version = "4.8.3";
    public static String OTP = "";
    public static String PAN = "";
    public  static String MID = "";
    public  static String leadId = "";
    public static String kybBusinessId = "";
    public static String businessName = "";
    public static String SolutionId = "";
    public static String fileNames = "pan,Cancelled+Cheque+Photo,Merchandise+Photo+1,Merchandise+Photo+2,Merchandise+Photo+3,Paytm+Accepted+Here+Sticker,Paytm+Accepted+Here+Sticker+1";
    public static String nameMatchStatus = "";
    public static String XMWToken = "";

    public static String WorkFlowId = "";



    public static List<Object> DMS = new ArrayList<>();
    public static List<Object> DMSforUUID = new ArrayList<>();
    public static List<Object> DocType = new ArrayList<>();
    public static List<Object> DocProvided = new ArrayList<>();
    public static List<Object> DocumentRequest = new ArrayList<>();
    public static List<Object> NameOfDoc = new ArrayList<>();
    public static List<Object> TypeOfDoc = new ArrayList<>();
    public static String OePanelDocStatus = "APPROVED";
    public static String RejectionReason = null;
    public static String DocumetRequestDeserialised = "";
    public static String LeadStagePanel = "";
    List <Object> GetDocuments = new ArrayList<>();

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    WalletServices walletServices = new WalletServices();


    @BeforeClass
    public void AgentLogin500K()
    {
        LOGGER.info("Before 500K Test, Agent Login");
     //   AgentToken = CommonAgentToken;
        AgentToken = ApplicantToken("9953828631", "paytm@123");
        LOGGER.info("Merchant token for QR 500k : " + AgentToken);
      //  Response responseObject=middlewareServicesObject.v1Token("7771216290","paytm@123");
      //  XMWCookie=responseObject.getHeader("Set-Cookie").toString();
        LOGGER.info(" OE Panel Cookie is  : " + XMWCookie);

        Utilities UtilObj = new Utilities();
        PAN = UtilObj.randomIndividualPANValueGenerator();
        LOGGER.info("PAN Number is : " + PAN);
    }

    @Test(priority = 0,description = "Create Applicant on Oauth",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC001_CreateApplicantOauth()
    {
        //generating new Number
        Utilities accObj = new Utilities();
        mobileNo = accObj.randomMobileNumberGenerator();
        LOGGER.info("New Number is : " + mobileNo);
        CreateApplicantOauth(mobileNo);
    }

    @Test(priority = 0,description = "Positive Send OTP for Business",dependsOnMethods ="TC001_CreateApplicantOauth",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC002_PositiveSendOtpBusiness500K()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"UNKNOWN","company_onboard",AgentToken,version,mobileNo,"company");

        String expectedErrorMsg = "SUCCESS";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("status");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));
        CustId=FetchUserDetails(mobileNo, "phone");

       // CustId = SendOtpResp.jsonPath().getString("custId");
        LOGGER.info("Cust Id is : " + CustId);

        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    /*@Test(priority = 0,description = "Positive Validate OTP for Business",dependsOnMethods ="PositiveSendOtpBusiness500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void PositiveValidateOtpBusiness500K() throws IOException, JSchException
    {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtp"));
        //OTP = getOTP(mobileNo);
        OTP="888888";
        System.out.println("This is OTP " + OTP);

        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,"INDIVIDUAL","company_onboard",AgentToken,version,mobileNo,"company",newState,OTP);

        int StatusCode = validateOtp.getStatusCode();
        //TODO:Setting Value of CustID
        CustId = validateOtp.jsonPath().getString("custId");
        System.out.println("CustId " + CustId);
        Assert.assertEquals(StatusCode, 200);
        if (null != CustId)
        {
            Assert.assertNotEquals(CustId, null);
        }
        else
        {
            System.out.println("Failed Because of Oauth");
        }


    }

    @Test(priority = 0,description = "Positive Get Business",dependsOnMethods ="TC002_PositiveSendOtpBusiness500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC003_PositiveGetBusiness500K()
    {
        GetBusinessv3 getBusObj = new GetBusinessv3();

        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);

        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema.json");

    }

    @Test(priority = 0,description = "Providing Invalid CustId in Fetch Business",dependsOnMethods ="TC002_PositiveSendOtpBusiness500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC004_InvalidCustIdGetBusiness500K()
    {
        GetBusinessv3 getBusObj = new GetBusinessv3();

        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,"AASHIT123");

        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode,500);

        String ExpctdMsg = "Failed to fetch user businesses";
        String ActualMsg = getBusResp.jsonPath().getString("message");
        Assert.assertTrue(ActualMsg.contains(ExpctdMsg));
    }

    @Test(priority = 0,description = "Providing Empty CustId in Fetch Business",dependsOnMethods ="TC002_PositiveSendOtpBusiness500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC005_EmptyCustIdGetBusiness500K()
    {
        GetBusinessv3 getBusObj = new GetBusinessv3();

        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,"");

        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode,400);
    }


    @Test(priority = 0,description = "Positive Validate PAN v1 Comapny",dependsOnMethods = "TC003_PositiveGetBusiness500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC006_PositiveGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");


        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PROPRIETORSHIP","company_onboard",mobileNo);

        String ExpectedMsg = "Pan is non dedupe";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);


    }

    @Test(priority = 0,description = "Providing Invalid PAN",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC007_InvalidPANGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany("**********");
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PROPRIETORSHIP","company_onboard",mobileNo);

        String ExpectedMsg = "Please enter a valid PAN";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,400);


    }

    @Test(priority = 0,description = "Providing PAN of different Entity",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC008_DiffEntityPANGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany("**********");
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PROPRIETORSHIP","company_onboard",mobileNo);

        String ExpectedMsg = "Please enter a valid PAN";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,400);


    }


    @Test(priority = 0,description = "Providing Diffrent solution type",dependsOnMethods = "TC006_PositiveGetComapny500K" )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC009_DiffSolutionGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PROPRIETORSHIP","p2p_100k",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,400);

        String ExpectedMsg = "Invalid solution";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));

    }

    @Test(priority = 0,description = "Providing Invalid solution type",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0010_InvalidSolutionGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PROPRIETORSHIP","company_onbo",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,500);

        String ExpectedMsg = "Failed to validate Pancard";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));
    }

    @Test(priority = 0,description = "Providing Empty solution type",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0011_EmptySolutionGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PROPRIETORSHIP","",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,500);

        String ExpectedMsg = "Failed to validate Pancard";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));
    }

    @Test(priority = 0,description = "Providing Different Entity type",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0012_DiffEntityGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PUBLIC_LIMITED","company_onboard",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,400);

        String ExpectedMsg = "Please enter a valid PAN";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));
    }

    @Test(priority = 0,description = "Providing Invalid Entity type",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0013_InvalidEntityGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PUBLIC_","company_onboard",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,500);

        String ExpectedMsg = "Failed to validate Pancard";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));
    }

    @Test(priority = 0,description = "Providing Empty Entity type",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0014_EmptyEntityGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"","company_onboard",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,500);

        String ExpectedMsg = "Failed to validate Pancard";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));
    }

    @Test(priority = 0,description = "Providing Invalid CustID",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0015_InvalidCustIDGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,"CustId","PROPRIETORSHIP","company_onboard",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,500);

        String ExpectedMsg = "Failed to validate Pancard";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));
    }

    @Test(priority = 0,description = "Providing Invalid CustID",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0016_EmptyCustIDGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","true");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,"","PROPRIETORSHIP","company_onboard",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,500);

        String ExpectedMsg = "Failed to validate Pancard";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));
    }


    @Test(priority = 0,description = "Providing Empty IsProp Flag",dependsOnMethods = "TC006_PositiveGetComapny500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0017_FalseIsPropGetComapny500K()
    {
        GetCompany v1CompObj = new GetCompany(PAN);
        v1CompObj.addParameter("proprietor","false");

        Response v1compResp = middlewareServicesObject.v1GetCompany(v1CompObj,AgentToken,version,CustId,"PROPRIETORSHIP","company_onboard",mobileNo);

        int StatusCode = v1compResp.getStatusCode();
        Assert.assertEquals(StatusCode,400);

        String ExpectedMsg = "Invalid request";
        String ActualdMsg = v1compResp.jsonPath().getString("displayMessage");
        Assert.assertTrue(ActualdMsg.contains(ExpectedMsg));

    }

    @Test(priority = 0,description = "Positive Create Comapny",dependsOnMethods = "TC006_PositiveGetComapny500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0018_PositivePostComapny500K()
    {
        PostCompany v1PostComp = new PostCompany();

        Response v1PostCompany = middlewareServicesObject.v1PostCompany(v1PostComp, AgentToken, version, CustId, "PROPRIETORSHIP", "company_onboard", mobileNo, "AASHIT SHARMA", PAN);

        String ActualMsg = v1PostCompany.jsonPath().getString("displayMessage");
        String ExpectedMsg = "Lead successfully created.";
        Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

        int StatusCode = v1PostCompany.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        leadId = v1PostCompany.jsonPath().getString("leadId");
        System.out.println("LeadId is : " + leadId);

        v1PostComp.validateResponseAgainstJSONSchema("MerchantService/V1/PostCompany/PostCompanyResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Get Business After COmpany Onboard",dependsOnMethods ="TC0018_PositivePostComapny500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0019_PositiveGetBusinessAfterCompany500K()
    {
        GetBusinessv3 getBusObj = new GetBusinessv3();

        Response getBusResp = middlewareServicesObject.v3GetBusiness(getBusObj,AgentToken,version,CustId);

        kybBusinessId = getBusResp.jsonPath().getJsonObject("businesses[0].kybBusinessId").toString();
        System.out.println("KYB ID is : " + kybBusinessId);

        businessName = getBusResp.jsonPath().getJsonObject("businesses[0].businessName").toString();
        System.out.println("Business Name is : " + businessName);

        int statusCode = getBusResp.getStatusCode();
        Assert.assertEquals(statusCode,200);

        getBusObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusiness/GetBusinessResponseSchema2.json");

    }

    @Test(priority = 0,description = "Positive Get Business Profile",dependsOnMethods ="TC0019_PositiveGetBusinessAfterCompany500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0020_PositiveGetBusinessProfile500K()
    {
        BusinessProfile v3BusPro = new BusinessProfile();

        Response v3BusProRes = middlewareServicesObject.v3BusinessProfile(v3BusPro,CustId,leadId,kybBusinessId,AgentToken,version);

        int StatusCode = v3BusProRes.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        //v3BusPro.validateResponseAgainstJSONSchema("MerchantService/V3/GetBusinessProfile/GetBusinessProfileResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Send OTP for QR Merchant",dependsOnMethods ="TC0020_PositiveGetBusinessProfile500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0021_PositiveSendOtpQRMerchant500K()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"PROPRIETORSHIP","qr_merchant",AgentToken,version,mobileNo,"Merchant");

        newState = SendOtpResp.jsonPath().getString("state");
        LOGGER.info("New State " + newState);

        String expectedErrorMsg = "Otp sent to phone";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("message");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));

        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 0,description = "Positive Get TnC for 500K",dependsOnMethods ="TC0021_PositiveSendOtpQRMerchant500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0022_PositiveGetTnC500K()
    {
        TnC getTnCobj = new TnC();
        getTnCobj.addParameter("tncAdditionalParam","standard");
        getTnCobj.addParameter("tncSet","merchant");

        Response getTnCResp = middlewareServicesObject.v2GetTnC(getTnCobj,"PROPRIETORSHIP","qr_merchant",version,AgentToken);

        String ExpectedMsg = "SUCCESS";
        String ActualMsg = getTnCResp.jsonPath().getString("status");
        Assert.assertTrue(ActualMsg.contains(ActualMsg));

        int StatusCode = getTnCResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String TnCURL = getTnCResp.jsonPath().getString("url");
        System.out.println("TnC URL is : "+TnCURL);

        getTnCobj.validateResponseAgainstJSONSchema("MerchantService/V2/TnC/TnCResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Validate OTP for QR Merchant",dependsOnMethods ="TC0021_PositiveSendOtpQRMerchant500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0023_PositiveValidateOtpQRMerchant500K() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtp500K"));
        validateOtpObj.getProperties().setProperty("leadId",leadId);
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("solutionSubType","standard");
        validateOtpObj.getProperties().setProperty("tncAdditionalParam","standard");

        //OTP = getOTP(mobileNo);
       // OTP="888888";
        OTP = getOTPFromSellerPanel(mobileNo);

        System.out.println("This is OTP " + OTP);

        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,"PROPRIETORSHIP","qr_merchant",AgentToken,version,mobileNo,"merchant",newState,OTP);

        SolutionId = validateOtp.jsonPath().getString("leadId");
        int StatusCode = validateOtp.getStatusCode();

        for (int i = 0; i<=3;i++)
        {

            if (SolutionId == null || SolutionId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC0021_PositiveSendOtpQRMerchant500K();

                waitForLoad(5000);
                ValidateOtp validateOtpObjErr = new ValidateOtp(P.TESTDATA.get("validateOtp500K"));
                validateOtpObjErr.getProperties().setProperty("leadId",leadId);
                validateOtpObjErr.getProperties().setProperty("kybId",kybBusinessId);
                validateOtpObjErr.getProperties().setProperty("solutionSubType","standard");
                validateOtpObjErr.getProperties().setProperty("tncAdditionalParam","standard");
                //OTP = getOTP(mobileNo);
                OTP = getOTPFromSellerPanel(mobileNo);

                LOGGER.info("This is OTP " + OTP);
                Response RevalidateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObjErr,"PROPRIETORSHIP","qr_merchant",AgentToken,version,mobileNo,"merchant",newState,OTP);
                SolutionId = RevalidateOtp.jsonPath().getString("leadId");
                StatusCode = RevalidateOtp.getStatusCode();
            }
            else
            {
                LOGGER.info("This is Solution Lead ID : " + SolutionId);
                Assert.assertNotEquals(SolutionId,null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }




        //validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get Merchant for 500K",dependsOnMethods ="TC0023_PositiveValidateOtpQRMerchant500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0024_PositiveGetMerchant500K()
    {
        GetMerchant getMerchObj = new GetMerchant(CustId);

        getMerchObj.addParameter("leadId",SolutionId);
        getMerchObj.addParameter("kybBusinessId",kybBusinessId);

        Response getMerchResp = middlewareServicesObject.v3GetMerchant(getMerchObj,"PROPRIETORSHIP","qr_merchant",AgentToken,version);

        int StatusCode = getMerchResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
        getMerchObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetMerchant/GetMerchantResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get Category for 500K",dependsOnMethods ="TC0024_PositiveGetMerchant500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0025_PositiveGetCategory500K()
    {
        Category getCatobj = new Category();

        Response getCatResp = uadServicesObject.getCategory(getCatobj,"PROPRIETORSHIP","qr_merchant",AgentToken,version);

        int StatusCode = getCatResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        String expectedMsg = "success";
        String exactMsg = getCatResp.jsonPath().getString("message");
        Assert.assertTrue(exactMsg.contains(expectedMsg));

        getCatobj.validateResponseAgainstJSONSchema("UAD/V1/Category/CategoryResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get Sub-Category for 500K",dependsOnMethods ="TC0025_PositiveGetCategory500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0026_PositiveGetSubCategory500K()
    {
        SubCategory getSubCatobj = new SubCategory();

        Response getSubCatResp = uadServicesObject.getSubCategory(getSubCatobj,"PROPRIETORSHIP","qr_merchant",AgentToken,version,1);

        int StatusCode = getSubCatResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        String expectedMsg = "success";
        String exactMsg = getSubCatResp.jsonPath().getString("message");
        Assert.assertTrue(exactMsg.contains(expectedMsg));

        getSubCatobj.validateResponseAgainstJSONSchema("UAD/V1/SubCategory/SubCategoryResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Send OTP for Merchant Declaration for 500K",dependsOnMethods ="TC0024_PositiveGetMerchant500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0027_PositiveSendOtpMerchantDeclare500K()
    {
        SendOtp v3SendOtp = new SendOtp();
        Response SendOtpResp = middlewareServicesObject.v3SentOtp(v3SendOtp,"PROPRIETORSHIP","qr_merchant",AgentToken,version,mobileNo,"merchant_smd");

        newState = SendOtpResp.jsonPath().getString("state");
        System.out.println("New State " + newState);

        String expectedErrorMsg = "Otp sent to phone";
        String actualErrorMsg = SendOtpResp.jsonPath().getString("message");
        Assert.assertTrue(actualErrorMsg.contains(expectedErrorMsg));

        int StatusCode = SendOtpResp.getStatusCode();
        Assert.assertEquals(StatusCode,200);

    }

    @Test(priority = 0,description = "Positive Validate OTP for Merchant Declaration for 500K",dependsOnMethods ="TC0027_PositiveSendOtpMerchantDeclare500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0028_PositiveValidateOtpMerchantDeclare500K() throws IOException, JSchException, InterruptedException {
        waitForLoad(2000);
        ValidateOtp validateOtpObj = new ValidateOtp(P.TESTDATA.get("validateOtpMerchantDeclare500K"));
        validateOtpObj.getProperties().setProperty("kybId",kybBusinessId);
        validateOtpObj.getProperties().setProperty("onlyValidateOtp","true");

       // OTP = getOTP(mobileNo);
        //OTP="888888";
        OTP = getOTPFromSellerPanel(mobileNo);

        System.out.println("This is OTP " + OTP);

        Response validateOtp = middlewareServicesObject.v3ValidateOtp(validateOtpObj,"PROPRIETORSHIP","qr_merchant",AgentToken,version,mobileNo,"merchant",newState,OTP);
        SolutionId = validateOtp.jsonPath().getString("leadId");
        int StatusCode = validateOtp.getStatusCode();
        for (int i = 0; i<=3;i++)
        {

            if (SolutionId == null || SolutionId.isEmpty() || StatusCode!=200)
            {
                LOGGER.info("Lead Id is null, Inside loop : " +i);
                TC0027_PositiveSendOtpMerchantDeclare500K();
                waitForLoad(5000);
                ValidateOtp ValidateOTPobjErr = new ValidateOtp(P.TESTDATA.get("validateOtpMerchantDeclare500K"));
                ValidateOTPobjErr.getProperties().setProperty("kybId",kybBusinessId);
                ValidateOTPobjErr.getProperties().setProperty("onlyValidateOtp","true");
                //OTP = getOTP(mobileNo);
                OTP = getOTPFromSellerPanel(mobileNo);

                LOGGER.info("This is OTP " + OTP);
                Response ResNewObj = middlewareServicesObject.v3ValidateOtp(ValidateOTPobjErr,"PROPRIETORSHIP", "qr_merchant", AgentToken, version, mobileNo, "merchant", newState,OTP);
                SolutionId = ResNewObj.jsonPath().getString("leadId");
                StatusCode = ResNewObj.getStatusCode();
                CustId = ResNewObj.jsonPath().getString("custId");
            }
            else
            {
                LOGGER.info("This is Lead ID : " + SolutionId);
                Assert.assertNotEquals(SolutionId,null);
                LOGGER.info("CustId " + CustId);
                Assert.assertNotEquals(CustId,null);
                Assert.assertEquals(StatusCode, 200);
                break;
            }
        }


       // validateOtpObj.validateResponseAgainstJSONSchema("MerchantService/V3/ValidateOtp/ValidateOtpResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Get GST Exemption List for 500K",dependsOnMethods ="TC0027_PositiveSendOtpMerchantDeclare500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0029_PositiveGetGstExemption500K()
    {
        GstExemptionList v1GstObj = new GstExemptionList();

        Response getGstExemptionResp = middlewareServicesObject.v1GstExemption(v1GstObj,version,AgentToken);

        int StatusCode = getGstExemptionResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        v1GstObj.validateResponseAgainstJSONSchema("MerchantService/V1/GstExemptionList/GstExemptionListResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Get Document Status for 500K",dependsOnMethods ="TC0029_PositiveGetGstExemption500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0030_PositiveGetDocStatus500K()
    {
        GetDocStatus v3DocObj = new GetDocStatus();
        v3DocObj.addParameter("fileNames",fileNames);
        v3DocObj.addParameter("leadId",SolutionId);
        v3DocObj.addParameter("kybBusinessId",kybBusinessId);

        Response v3DocStatResp = middlewareServicesObject.v3getDocStatus(v3DocObj,CustId,"PROPRIETORSHIP","qr_merchant",AgentToken,version);

        String ExpectedErr = "200";
        String ActualErr = v3DocStatResp.jsonPath().getString("errorCode");
        Assert.assertTrue(ActualErr.contains(ExpectedErr));

        int StatusCode = v3DocStatResp.getStatusCode();
        Assert.assertEquals(StatusCode, 200);

        //v3DocObj.validateResponseAgainstJSONSchema("MerchantService/V3/GetDocStatus/GetDocStatusSchemaFor500K.json");
    }

    @Test(priority = 0,description = "Positive Get Banks for 500K",dependsOnMethods ="TC0030_PositiveGetDocStatus500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0031_PositiveGetBanks500K()
    {
        IFSC = "INDB0000588";
        Banks getBank = new Banks(IFSC);
        Response getBanksResponse = middlewareServicesObject.v2Banks(getBank,version,AgentToken);

        String expectedMsg = "bankName=INDUSIND BANK";
        String actualMsg = getBanksResponse.jsonPath().getJsonObject("bankDetails").toString();
        Assert.assertTrue(actualMsg.contains(expectedMsg));

       int StatusCode = getBanksResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        getBank.validateResponseAgainstJSONSchema("MerchantService/V2/Banks/BanksResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Perform MultiName Penny Drop for 500K",dependsOnMethods ="TC0030_PositiveGetDocStatus500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0032_PositivePennyDropMultiNameMatch500K() throws Exception
    {
        PennyDropMultiNameMatch v4PennyObj = new PennyDropMultiNameMatch();
        v4PennyObj.addParameter("solutionType","qr_merchant");
        v4PennyObj.addParameter("entityType","PROPRIETORSHIP");
        v4PennyObj.getProperties().setProperty("mobile",mobileNo);
        v4PennyObj.getProperties().setProperty("ifsc","INDB0000431");
        v4PennyObj.getProperties().setProperty("bankAccountNumber","************");

        Response v4PennyResp = middlewareServicesObject.v4PennyDropMultiName(v4PennyObj,version,"AASHIT SHARMA","INDUSIND Bank",CustId,AgentToken);

        nameMatchStatus = v4PennyResp.jsonPath().getString("nameMatchStatus");
        LOGGER.info("Name Match Status = " +nameMatchStatus);

        if(nameMatchStatus == null)
        {
            nameMatchStatus = "false";
            LOGGER.info("Name Match Status when its NULL = " +nameMatchStatus);

        }

        int StatusCode = v4PennyResp.getStatusCode();
        if(StatusCode == 200)
        {
            Assert.assertEquals(StatusCode,200);
        }

        if (StatusCode == 500)
        {
            String expectedFailureMsg = "We are unable to connect with your bank to verify your details. Please try after some time or provide a different bank account";
            String actualFailureMsg = v4PennyResp.jsonPath().getString("message");
            Assert.assertTrue(actualFailureMsg.contains(expectedFailureMsg));

            Assert.assertEquals(StatusCode,500);
        }
        //v4PennyObj.validateResponseAgainstJSONSchema("MerchantService/V3/PennyDrop/PennyDropResponseSchema.json");

    }

    @Test(priority = 0,description = "Positive Submit Lead Details for 500K",dependsOnMethods ="TC0030_PositiveGetDocStatus500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0033_PositiveSubmitLead500K()
    {
        SubmitMerchant v3SubmitObj = new SubmitMerchant(CustId,P.TESTDATA.get("SubmitMerchant500K"));
        v3SubmitObj.getProperties().setProperty("leadId",leadId);
        v3SubmitObj.getProperties().setProperty("nameMatchStatus","true");


        Response submitMerchResponse = middlewareServicesObject.v3SubmitMerchant(v3SubmitObj,mobileNo,mobileNo,"ANDB0002029",version,"PROPRIETORSHIP","qr_merchant",AgentToken);

        int StatusCode = submitMerchResponse.getStatusCode();
        Assert.assertEquals(StatusCode,200);

        String ExpectedMsg = "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later";
        String ActualMsg = submitMerchResponse.jsonPath().getString("message");
        Assert.assertTrue(ActualMsg.contains(ExpectedMsg));

        v3SubmitObj.validateResponseAgainstJSONSchema("MerchantService/V3/SubmitMerchant/SubmitMerchantResponseSchema.json");
    }

    @Test(priority = 0,description = "Positive Fetch Documents for QR Merchant 500K",dependsOnMethods ="TC0033_PositiveSubmitLead500K",groups = {"Regression"} )
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0034_PositiveFetchDocs500K() throws JsonProcessingException
    {
        Map<String,String>queryDocUpload = new HashMap<>();
        queryDocUpload.put("type", "jpg");
        queryDocUpload.put("entityType", "PROPRIETORSHIP");
        queryDocUpload.put("solutionType", "qr_merchant");
        queryDocUpload.put("merchantCustId", CustId);
        queryDocUpload.put("leadId", SolutionId);

        Map<String,String>queryFetchDoc = new HashMap<>();
        queryFetchDoc.put("entityType", "PROPRIETORSHIP");
        queryFetchDoc.put("solution", "qr_merchant");
        queryFetchDoc.put("leadId", SolutionId);
        queryFetchDoc.put("channel", "GG_APP");
        queryFetchDoc.put("merchantCustId", CustId);

        GgAppDynamicDocUpload(AgentToken,version,mobileNo,queryFetchDoc,queryDocUpload);

        //v4Docs.validateResponseAgainstJSONSchema("MerchantService/V4/FetchDynamicDocs/FetchDynamicDocQrMerchantSchema.json");
    }


    @Test(priority = 0,description = "Create User at Wallet",groups = {"Regression"},dependsOnMethods = "TC0034_PositiveFetchDocs500K",timeOut = 5000)
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0035_PositiveCreateUserWallet()
    {
        CreateUserWallet walletObj = new CreateUserWallet();

        walletObj.getProperties().setProperty("ssoId",CustId);
        walletObj.getProperties().setProperty("mobileNumber",mobileNo);

        Response walletResp = walletServices.createUserWallet(walletObj);

        String msg = walletResp.jsonPath().getString("statusMessage");
        Assert.assertTrue(msg.contains("Wallet Activated"));

        String statusMsg = walletResp.jsonPath().getString("statusCode");
        Assert.assertTrue(statusMsg.contains("SUCCESS"));

    }


    @Test(priority = 0,description = "Get Cookie for OE Panel",groups = {"Regression"},dependsOnMethods = "TC0034_PositiveFetchDocs500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0036_PositiveGetOEPanelCookie500K() throws SQLException, JsonProcessingException {
        XMWToken=XMWCookie;

        System.out.println("XMW token is :"+XMWToken);

        LOGGER.info("Hitting Callback for 100Rs MID");
        DbName = DbStaging6;
        MID = PG_CallBack_Insatnt50K(CustId);
    }

    @Test(priority = 0,description = "Fetch Lead Details on Panel",groups = {"Regression"},dependsOnMethods = "TC0036_PositiveGetOEPanelCookie500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0037_PositiveFetchLeadPanel500K()
    {

        try
        {
            Map <String,String> RequestPanel = new HashMap<>();
            Map <String,String> ResponsePanel = new HashMap<>();

            RequestPanel.put("docStatus",OePanelDocStatus);
            RequestPanel.put("rejectionReason",RejectionReason);
            RequestPanel.put("leadId",SolutionId);


            ResponsePanel = FetchPanelLead(RequestPanel);

            DocumetRequestDeserialised = ResponsePanel.get("DocumenstArray");
            WorkFlowId = ResponsePanel.get("WorkFlowId");
            LOGGER. info("Lead Stage is : " + ResponsePanel.get("LeadStage"));
        }
        catch (Exception e)
        {
            LOGGER.info("Execption " + e);
            LOGGER.info(" Line No. at : " + e.getStackTrace()[0].getLineNumber());
        }
    }

    @Test(priority = 0,description = "Reallocating Agent to Rajan",groups = {"Regression"},dependsOnMethods = "TC0037_PositiveFetchLeadPanel500K")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC0038_ReallocatingAgent500K()
    {
        LOGGER.info("Inside ReallocatingAgent Method ");


        boolean LeadStage = false;
        if (LeadStagePanel.equals("DATA_ENTRY_ACTION_PENDING"))
        {
            LeadStage = true;
        }

        for (int i = 0; i<=3;i++)
        {
            LOGGER.info("Inside loop to fetch current lead stage & counter is : "+(i+1) );
            if(!LeadStage)
            {
                LOGGER.info("Inside If condition for lead stage id not DE");
                FetchLead v1FetchLeadObj = new FetchLead(SolutionId);
                Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj, XMWCookie);
                LeadStagePanel = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.subStage").toString();
                WorkFlowId = v1FetchLeadResp.jsonPath().getJsonObject("leadDetails.leadInfo.workflowStatusId").toString();

                if (LeadStagePanel.equals("DATA_ENTRY_ACTION_PENDING"))
                {
                    LOGGER.info("Inside if condition to change Flag for Lead Stage");
                    LeadStage = true;
                }
            }
            else
            {
                LOGGER.info("Current Lead Stage is : " + LeadStagePanel);
                break;
            }
        }
        Assert.assertEquals(LeadStagePanel,"DATA_ENTRY_ACTION_PENDING");

        ReallocatingAgent(SolutionId,"1152");

    }

    @Test(priority = 0,description = "Submit 500K Lead form OE Panel",groups = {"Regression"},dependsOnMethods = "TC0038_ReallocatingAgent500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0039_PositiveSubmitLeadPanel500K()
    {

            EditLead v1EditLeadObj = new EditLead(SolutionId, P.TESTDATA.get("EditLead500KNameMatchFailed"));

            v1EditLeadObj.getProperties().setProperty("custId", CustId);
            v1EditLeadObj.getProperties().setProperty("mobileNumber", mobileNo);
            v1EditLeadObj.getProperties().setProperty("documents", DocumetRequestDeserialised);
            v1EditLeadObj.getProperties().setProperty("reEnterAccountNumber", mobileNo);
            v1EditLeadObj.getProperties().setProperty("workflowStatusId", WorkFlowId);

            Response responseObject = middlewareServicesObject.v1EditLeadOE(v1EditLeadObj, "SUBMIT", "**********", "**********", XMWToken, "application/json");

            int statusCode = responseObject.getStatusCode();
            Assert.assertEquals(statusCode, 200);

            String LeadSatge = responseObject.jsonPath().getJsonObject("leadDetails.leadInfo.subStage");
            Assert.assertTrue(LeadSatge.contains("PANEL_SUCCESS"));

            //v1EditLeadObj.validateResponseAgainstJSONSchema("MerchantServiceOEPanelV1EditLead/EditLead500KSchema.json");


    }
    @Test(enabled = true,description = "Fetch Lead Details on Panel for PG Callback",groups = {"Regression"},dependsOnMethods = "TC0039_PositiveSubmitLeadPanel500K")
    @Owner(emailId = "<EMAIL>",isAutomated = true)
    public void TC0040_PgCallBack500K()
    {

        PgCallBackFromPanelRef(SolutionId,CustId,MID);
    }

    @AfterClass(description = "Adding Details on CSV")
    public void AddingDataCsv() throws IOException {
        LOGGER.info("In After Test of " +getClass());

        File fileUpload = new File("OnboardedMerchant.csv") ;
        // Create csv file
        FileWriter outputfile = new FileWriter(fileUpload,true);

        // Write to CSV file which is open
        CSVWriter writer = new CSVWriter(outputfile);

        if(!MID.isEmpty()) {
            LOGGER.info("MID Is not Empty");
            // add data to csv
            String[] data1 = {MID, CustId, mobileNo, "qr_merchant", "500K"};
            writer.writeNext(data1);
            writer.flush();
            writer.close();
        }
        else
        {
            LOGGER.info("MID is Empty");
        }
    }

}
*/
