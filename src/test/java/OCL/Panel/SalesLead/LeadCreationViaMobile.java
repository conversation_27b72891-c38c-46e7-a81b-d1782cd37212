package OCL.Panel.SalesLead;


import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class LeadCreationViaMobile extends BaseMethod {
	
	oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
	
	 String agent_session_token = "";
	 String applicant_mobile_number = "";
	 String applicant_custId = "";
	 String leadId="";
	 
	 Map<String,String> panelHeaders;
	  
	@BeforeClass()
	 public void intitializeInputData() {
	
	 agent_session_token = oAuthServicesObject.getoAuthSessionTokenCustID("9560526665","paytm@123",true);
	 applicant_mobile_number = UtilitiesObject.randomMobileNumberGenerator();
	 UtilitiesObject.createNewAuthUser(applicant_mobile_number,"test@123");
	 panelHeaders=setPanelHeaders();
	

	}
	   @Owner(emailId = "<EMAIL>")
	    @Test(groups = {"Regression"})
	    
	      public void TC001_RegisterLeadViaMobileNumber(){
	    	
	    	       
		  Map<String,String> queryParams=new HashMap<String,String>();
		  queryParams.put("mobile",applicant_mobile_number );
		  queryParams.put("solutionType","register_lead");
    	  queryParams.put("solutionSubtype","sales_lead");
		
		   Map<String,String> headers=new HashMap<String,String>();
	       headers = panelHeaders;
	        
	       Map<String,Object> body = new HashMap<String, Object>();
	  	   body.put("solutionRequested","QR Services");
		
		  Response responseObject= MiddlewareServicesObject.v1PanelRegisterLead(queryParams, headers,body);
		  
		  System.out.println("STEP 2- Verify Status Code ");
		  verifyResponseCodeAs200OK(responseObject);
		  
		  leadId = responseObject.jsonPath().getString("leadId");
		    
	        
	    }

	   @Owner(emailId = "<EMAIL>")
	    @Test(groups = {"Regression"})
	    
	      public void TC002_GetDetailsOfRegisteredLead(){
	    	    	       
		  Map<String,String> queryParams=new HashMap<String,String>();
		  queryParams.put("entityType","INDIVIDUAL" );
		  queryParams.put("solution","register_lead");
   	      queryParams.put("solutionTypeLevel2","sales_lead");
   	      queryParams.put("channel","OE_PANEL");
   	      queryParams.put("leadId",leadId);
		
		   Map<String,String> headers=new HashMap<String,String>();
	       headers = panelHeaders;
	    
	       Response responseObject= MiddlewareServicesObject.v1getLeadDetails(queryParams, headers);
		  
		  System.out.println("STEP 2- Verify Status Code ");
		  verifyResponseCodeAs200OK(responseObject);
		  
		  Assert.assertEquals(responseObject.jsonPath().getString("displayMessage"),"User Data Fetch Successfully");
		  String stage=responseObject.jsonPath().getString("stage");
		  Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
		  
	        
	    }
	   

	   @Owner(emailId = "<EMAIL>")
	    @Test(groups = {"Regression"})
	    
	      public void TC003_SubmitRegisterLead(){
	    	
	    	       
		  Map<String,String> queryParams=new HashMap<String,String>();
		  queryParams.put("mobile",applicant_mobile_number );
		  queryParams.put("solutionType","register_lead");
    	  queryParams.put("solutionSubtype","sales_lead");
		
		   Map<String,String> headers=new HashMap<String,String>();
	       headers = panelHeaders;
	        
	       Map<String,Object> body = new HashMap<String, Object>();
	  	   body.put("solutionRequested","QR Services");
	  	   body.put("BUSINESS_NAME", "shivangi"+UtilitiesObject.randomNumberGenerator(5));
	  	   body.put("REMARKS", "testdata"+UtilitiesObject.randomNumberGenerator(5));
	  	   body.put("INTEREST_STATUS", "hot");
		   body.put("businessType", "PUBLIC_LIMITED");
		   body.put("NEXT_FOLLOWUP_DATE", "");
		   body.put("COMPETITORS_PRESENT", "BharatPe,AmazonPay,PhonePe");
		   body.put("PAN", "shivangi"+UtilitiesObject.randomPublicPANValueGenerator());
		   body.put("ADDITIONAL_EMAIL", "<EMAIL>");
		   body.put("ADDITIONAL_MOBILE_NUMBER", "6111111147");
		   body.put("line1", "A-03");
		   body.put("line2", "Saket");
		   body.put("line3", "148 civil lines");
		   body.put("pincode", "243001");
		   body.put("state", "Uttar Pradesh");
		   body.put("city", "Bareilly");
		   body.put("country", "INDIA");
		   body.put("leadId", leadId);
		  Response responseObject= MiddlewareServicesObject.v1PanelRegisterLead(queryParams, headers,body,true);
		  
		  System.out.println("STEP 2- Verify Status Code ");
		  verifyResponseCodeAs200OK(responseObject);
		  
		  Assert.assertEquals(responseObject.jsonPath().getString("leadId"),leadId);
	    }
	   
	   @Owner(emailId = "<EMAIL>")
	    @Test(groups = {"Regression"})
	    
	      public void TC004_GetListOfBusinessDocuments(){
	    	    	       
		  Map<String,String> queryParams=new HashMap<String,String>();
		  queryParams.put("entityType","INDIVIDUAL" );
		  queryParams.put("solution","register_lead");
  	      queryParams.put("channel","OE_PANEL");
  	      queryParams.put("leadId",leadId);
		
		   Map<String,String> headers=new HashMap<String,String>();
	       headers = panelHeaders;
	    
	       Response responseObject= MiddlewareServicesObject.v1getBusinessDocDetails(queryParams, headers);
		  
		  System.out.println("STEP 2- Verify Status Code ");
		  verifyResponseCodeAs200OK(responseObject);
		  
		 
		  String listOfDocs =responseObject.jsonPath().getString("optionalDocDetails.optionalDocsDetails.docProvidedDisplayName");
		 	  
		  Assert.assertEquals(responseObject.jsonPath().getString("solutionLeadId"),leadId);
		  
	        
	    }

	   
	   @Owner(emailId = "<EMAIL>")
	    @Test(groups = {"Regression"})
	    
	      public void TC005_UploadDocuments() throws InterruptedException{
	    	    	       
		 MiddlewareServicesObject.utilityForDocumentUpload("register_lead", "INDIVIDUAL",leadId,panelHeaders);
		  
		 	    }

   public Map<String, String> setPanelHeaders() {


    Map<String, String> headers = new HashMap<String, String>();
    headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:68.0) Gecko/20100101 Firefox/68.0");
    headers.put("Accept","application/json, text/plain, */*");
    headers.put("Accept-Language","en-US,en;q=0.5");
    headers.put("Referer","https://oe-staging.paytm.com/search");
    headers.put("Content-Type","application/json;charset=utf-8");
    headers.put("phonenumber","9560526665");
    headers.put("ssoid","1106992015");
    headers.put("Origin","https://oe-staging.paytm.com");
    headers.put("Connection","keep-alive");
    headers.put("Cookie",XMWCookie);
    headers.put("TE","3.5.2");

    return headers;
       }
   
   /**
    * Verify  REsponse Code as 200 OK
    * @param responseObject
    */
	public void verifyResponseCodeAs200OK(Response responseObject) {
    	
    	System.out.println("Status Code : " +responseObject.getStatusCode());  
    	Assert.assertEquals(responseObject.getStatusCode(),200);
    	 
    }
  

}
