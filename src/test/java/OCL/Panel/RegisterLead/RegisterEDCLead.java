package OCL.Panel.RegisterLead;

import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

public class RegisterEDCLead extends BaseMethod {
	
	oAuthServices oAuthServicesObject = new oAuthServices();
    MiddlewareServices MiddlewareServicesObject = new MiddlewareServices();
    Utilities UtilitiesObject = new Utilities();
	
	 String agent_session_token = "";
	 String applicant_mobile_number = "";
	 String applicant_custId = "";
	 String leadId="";
	 
	 Map<String,String> panelHeaders;
	  
	@BeforeClass()
	 public void intitializeInputData() {
	
	 agent_session_token = oAuthServicesObject.getoAuthSessionTokenCustID("9560526665","paytm@123",true);
	 applicant_mobile_number = UtilitiesObject.randomMobileNumberGenerator();
	 UtilitiesObject.createNewAuthUser(applicant_mobile_number,"test@123");
	 panelHeaders=setPanelHeaders();
	

	}
	   @Owner(emailId = "<EMAIL>")
	    @Test(groups = {"Regression"})
	    
	      public void TC001_RegisterEDCLeadViaMobileNumber(){
	    	
	           
		  Map<String,String> queryParams=new HashMap<String,String>();
		  queryParams.put("typeList","service_solution" );
		  		
		   Map<String,String> headers=new HashMap<String,String>();
	       headers = panelHeaders;
	    	       	
		  Response responseObject= MiddlewareServicesObject.v1getAllResouces(queryParams, headers);
		  
		  System.out.println("STEP 2- Verify Status Code ");
		  verifyResponseCodeAs200OK(responseObject);
		  
		  Assert.assertEquals(responseObject.jsonPath().getString("message"),"SUCCESS");
		    
	        
	    }
	   @Owner(emailId = "<EMAIL>")
	    @Test(groups = {"Regression"})
	   public void TC002_SubmitLeadData(){
	    	
	       
			  Map<String,String> queryParams=new HashMap<String,String>();
			  queryParams.put("mobile",applicant_mobile_number );
			  queryParams.put("solutionType","register_lead");
	    	  queryParams.put("solutionSubtype","edc_service_lead");
			
			   Map<String,String> headers=new HashMap<String,String>();
		       headers = panelHeaders;
		        
		       Map<String,Object> body = new HashMap<String, Object>();
		  	   body.put("queryType","shivangi"+UtilitiesObject.randomNumberGenerator(5));
		  	   body.put("queryType2", "test"+UtilitiesObject.randomNumberGenerator(5));
		  	   body.put("name", "testdata"+UtilitiesObject.randomNumberGenerator(5));
		  	   body.put("message", "test");
			   body.put("businessType", "PUBLIC_LIMITED");
			   body.put("numberOfBranches", "1");
			   body.put("details", "create edc lead");
			   body.put("line1", "A-03");
			   body.put("line2", "Saket");
			   body.put("line3", "148 civil lines");
			   body.put("pincode", "243001");
			   body.put("state", "Uttar Pradesh");
			   body.put("city", "Bareilly");
			   body.put("country", "INDIA");
			   
			  Response responseObject= MiddlewareServicesObject.v1PanelRegisterLead(queryParams, headers,body,"edc");
			  
			  System.out.println("STEP 2- Verify Status Code ");
			  verifyResponseCodeAs200OK(responseObject);
			  
			  leadId=responseObject.jsonPath().getString("leadId");
			  
			  
		    }
	   public Map<String, String> setPanelHeaders() {


		    Map<String, String> headers = new HashMap<String, String>();
		    headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:68.0) Gecko/20100101 Firefox/68.0");
		    headers.put("Accept","application/json, text/plain, */*");
		    headers.put("Accept-Language","en-US,en;q=0.5");
		    headers.put("Referer","https://oe-staging.paytm.com/search");
		    headers.put("Content-Type","application/json;charset=utf-8");
		    headers.put("phonenumber","9560526665");
		    headers.put("ssoid","1106992015");
		    headers.put("Origin","https://oe-staging.paytm.com");
		    headers.put("Connection","keep-alive");
		    headers.put("Cookie",XMWCookie);
		    headers.put("TE","3.5.2");

		    return headers;
		       }
		   
		   /**
		    * Verify  REsponse Code as 200 OK
		    * @param responseObject
		    */
			public void verifyResponseCodeAs200OK(Response responseObject) {
		    	
		    	System.out.println("Status Code : " +responseObject.getStatusCode()); // Use logger instead
		    	Assert.assertEquals(responseObject.getStatusCode(),200);
		    	 
		    }
		  


}
