package OCL.Deals;

import OCL.Subscription.FetchPlanSubscription;
import Request.CreateDeals.CreateDeals;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Services.CreateDeals.GetCreateLeadP4b;
import Services.MechantService.MiddlewareServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class CreateDealsP4bAppFlow extends BaseMethod
{



    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    GetCreateLeadP4b getCreateLeadP4bResponse = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="PROPRIETORSHIP";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "DEALS_ONBOARDING";
    public static String merchantMobileNumber = "9497887646";
    public static String mid = "T4sept63649571299882";
    public static String successMessage = "Request Submitted Successfully";

    String email = "<EMAIL>";
    String storeNumber = "9098978367";
    String storeDisplayName = "store display 34";
    String shopId = "565678";
    String instoreCategory = "DEALS_ONBOARDING";
    String businesslat = "23.52";
    String businessLong = "77.8";

    public static String source = "DIY_P4B_APP";
    //public static String solution = "solution";
    public static String solutionTypeLevel2 = "DEALS_ONBOARDING";
    public static String solutionTypeLevel3 = "payments_offline";


    String registerLead = "true";
    String triggerValue = "3";
    String leadId ;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();




    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        XMWCookie=findXMWTokenforPanel("7771216290","paytm@123");
        LOGGER.info("Token for PG : " + Token);
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }

    @Test(priority = 1,description = "To check that create deals lead api is giving 200 and lead is created", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_CreateLeadForDeals_CreateLead() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

         requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
         subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);
        leadId =subfetchResponse.jsonPath().getString("leadId");

    }


    @Test(priority = 1,description = "To check that add store api is working ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002_CreateLeadForDeals_AddStoreDetails() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);

    }

    @Test(priority = 1,description = "To check that add ofer api is working ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003_CreateLeadForDeals_AddOfferAndMOV() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);

    }


    @Test(priority = 1,description = "To check that add time api is working fine for deals ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004_CreateLeadForDeals_AddTime() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);


    }


    @Test(priority = 1,description = "To check that fetch TNC for deals  api is working ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005_CreateLeadForDeals_FetchTnc() throws JSONException {

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("session_token", Token);
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers1, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        md5Key = subfetchResponse.jsonPath().getString("md5Key");
        identifier = subfetchResponse.jsonPath().getString("identifier");

        JsonPath js = new JsonPath(subfetchResponse.toString());





    }


    @Test(priority = 1,description = "To check that lead is submitted sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006_CreateLeadForDeals_SubmitLead() throws JSONException {


        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("MD_5_KEY",md5Key);
        body.put("TNC_IDENTIFIER",identifier);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsSubmitDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);

    }


    @Test(priority = 1,description = "To check that lead is submitted sucessfully", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007_validatePanelData() throws JSONException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(leadId);

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj,XMWCookie);

        System.out.println(v1FetchLeadResp.asString());
       // Assert.assertEquals(subfetchResponse.jsonPath().getString("storeDealDetails.1[0]."),successMessage);

        Assert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.solutionTypeLevel2"),solutionTypeLevel2);
        Assert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.solution"),solution);
        Assert.assertEquals(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.solutionTypeLevel3"),solutionTypeLevel3);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.category"));
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.subCategory"));
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.segment"));
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.businessEntityDetails.subSegment"));
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.leadInfo.source"),source);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.leadInfo.LeadNumber"),leadId);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.leadInfo.channel"),channel);



        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.StoreEmail"),email);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.storeNumber"),storeNumber);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.storeDisplayName"),storeDisplayName);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.pgMid"),mid);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.leadStatus"),"Approved");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.shopId"),shopId);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.instoreCategory"),instoreCategory);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.businesslat"),businesslat);
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.additionalDetails.businessLong"),businessLong);




    }

    @Test(priority = 1,description = "To check that lead is submitted sucessfully and timeline is correctly saved at panel", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008_ValidateTimelineDetails() throws JSONException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(leadId);

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj,XMWCookie);

        System.out.println(v1FetchLeadResp.asString());
        // Assert.assertEquals(subfetchResponse.jsonPath().getString("storeDealDetails.1[0]."),successMessage);

        //validating lead created stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"LEAD_CREATED");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"GG");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"false");


        //validating Document uploaded stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"DOCUMENT_UPLOADED");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"GG");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"false");



        //validating lead posted stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"LEAD_POSTED");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"GG");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"false");

        //validating Data Entry stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].subStage"),"DATA_ENTRY_ACTION_PENDING");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].stage"),"DATA_ENTRY");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.timelineDetail.[0].isActive"),"true");




    }


    @Test(priority = 1,description = "To check that lead is submitted sucessfully and timeline is correctly saved in store info", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009_ValidateStoreInfo() throws JSONException {

        MiddlewareServices middlewareServicesObject = new MiddlewareServices();

        FetchLead v1FetchLeadObj = new FetchLead(leadId);

        Response v1FetchLeadResp = middlewareServicesObject.v1FetchLeadPanel(v1FetchLeadObj,XMWCookie);

        System.out.println(v1FetchLeadResp.asString());
        // Assert.assertEquals(subfetchResponse.jsonPath().getString("storeDealDetails.1[0]."),successMessage);

        //validating lead created stage in timeline
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.storeInfo.storeDisplayName"),"store display 34");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.storeInfo.storeContactNumber"),"9098978367");
        Assert.assertNotNull(v1FetchLeadResp.jsonPath().getString("leadDetails.storeInfo.storeEmail"),"<EMAIL>");

    }







}
