package OCL.Deals;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateDeals.GetCreateLeadP4b;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class DealsAddStoreDetail {



    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    GetCreateLeadP4b getCreateLeadP4bResponse = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "DEALS_ONBOARDING";
    public static String merchantMobileNumber = "8888868061";
    public static String mid = "sanwIl60828762845089";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();




    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);


    }


    @Test(priority = 1,description = "To validate that add new address api gives error when token is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws JSONException {

        headers.put("session_token", " ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error when token is invlaid ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws JSONException {

        headers.put("session_token", "invalidToken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error when token key not passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws JSONException {

        headers.put(" ", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error when no header is passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws JSONException {

        Map<String,String> emptyHeader = new HashMap<>();

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,emptyHeader, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error on empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid"," ");

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error when mid is incorrect", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid","IncorrectMID");

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error on sending incorrect MID key", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("IncorrectmidKey",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error when solutionSubType is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", " ");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }
    @Test(priority = 1,description = "To validate that add new address api gives error when channel is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", " ");
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error when entitype is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", " ");
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error when solution is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", " ");
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error on sending invalid MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid","InvalidMid");

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error on sending incorrect solution  key", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("incorrectsolution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error on sending incorrect entityType  key", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error on sending incorrect channel  key", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("incorrectchannel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To validate that add new address api gives error on sending incorrect sub solution type  key", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_017() throws JSONException {

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("incorrectsolutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsStoreDetailsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }






}
