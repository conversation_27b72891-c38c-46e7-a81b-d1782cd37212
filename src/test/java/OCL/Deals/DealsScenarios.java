package OCL.Deals;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateDeals.GetCreateLeadP4b;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class DealsScenarios {

    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    GetCreateLeadP4b getCreateLeadP4bResponse = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "DEALS_ONBOARDING";
    public static String merchantMobileNumber = "8888868061";
    public static String mid = "sanwIl60828762845089";
    public static String invalidMid = "invalidMID";
    public static String invalidToken = "invalidToken";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();




    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);
        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

    }

    @Test(priority = 1,description = "To check that deals is not created for Empty MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_001_InvalidDeals() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",invalidMid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }


    @Test(priority = 1,description = "To check that deals is not created for invalid MID", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_002_DealsInvalidMid() throws JSONException {

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid"," ");
        body.put("registerLead",registerLead);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }


    @Test(priority = 1,description = "To check that deals is not created for invalid token", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_003_InvalidToken() throws JSONException {

        headers.put("session_token", invalidToken);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that deals is not created request not having mandatory MID param", groups = { "Regression" })
    public void TC_004_NoMIDParam() throws JSONException {

        headers.put("session_token", Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        //body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that deals is not created for registerLead param ", groups = { "Regression" })
    public void TC_005_NoRegisterLeadParam() throws JSONException {

        headers.put("session_token", Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);


        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }


    @Test(priority = 1,description = "To check that deals is not created for registerLead param ", groups = { "Regression" })
    public void TC_006_NoRegisterLeadParam() throws JSONException {

        headers.put("session_token", Token);

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }


}
