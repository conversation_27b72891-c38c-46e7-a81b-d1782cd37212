package OCL.Deals;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateDeals.GetCreateLeadP4b;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class DealsFetchTnc {


    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    GetCreateLeadP4b getCreateLeadP4bResponse = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="INDIVIDUAL";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "DEALS_ONBOARDING";
    public static String merchantMobileNumber = "5599890053";
    public static String mid = "Aadish88578187541868";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;



    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);


    }

    @Test(priority = 1,description = "To create a lead for fetching tnc via lead ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void TC_000_Prerequisite() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        body.put("registerLead",registerLead);

        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,200);
        Assert.assertEquals(subfetchResponse.jsonPath().getString("displayMessage"),successMessage);
        leadId =subfetchResponse.jsonPath().getString("leadId");

    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when token is empty  ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", " ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");



        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when token is invalid  ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "invalid");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");




        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when token key session_token is not sent ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put(" ", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");



        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when token key vakue in not sent  ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        //headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when lead is invalid ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", "invalid");

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,500);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when lead key is empty", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put(" ", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when lead is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("session_token", Token);
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", " ");

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers1, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,500);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when lead key values are not passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("session_token", Token);
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");


        Map<String,String> params=new HashMap<String,String>();
       // params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers1, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when lead id is null ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("session_token", Token);
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", "null");

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers1, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,500);


    }

    @Test(priority = 1,description = "To check that fetch TNC for deals  api is giving error when token is null  ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "null");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String, String> headers1 = new HashMap<String, String>();
        headers1.put("session_token", Token);
        headers1.put("Accept-Language", "en");
        headers1.put("Content-Type", "application/json");


        Map<String,String> params=new HashMap<String,String>();
        params.put("leadId", leadId);

        Map<String, String> body = new HashMap<String, String>();

        //requestPath = "MerchantServicev1sdMerchantlead/CreateDealsOffersRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetDealsTncResponse(headers1, body,params);

        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,500);


    }







}



