package OCL.Deals;

import OCL.Subscription.FetchPlanSubscription;
import Services.CreateDeals.GetCreateLeadP4b;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.goldengate.common.BaseMethod.ApplicantToken;

public class DealsAddTime {

    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    GetCreateLeadP4b getCreateLeadP4bResponse = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="PROPRIETORSHIP";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "DEALS_ONBOARDING";
    public static String merchantMobileNumber = "5555444423";
    public static String mid = "hHtCRh26057098258965";
    public static String successMessage = "Request Submitted Successfully";
    String registerLead = "true";
    String triggerValue = "3";
    String leadId;
    String requestPath;
    String md5Key;
    String identifier;



    @BeforeClass
    public void generateToken(){
        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
        LOGGER.info("Token for PG : " + Token);


    }


    @Test(priority = 1,description = "To check that add ofer api is giving error when token is not passed ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_001() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", " ");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when invalid token is sent ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_002() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", "InvalidToken");
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when Session_key is passed as blank ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_003() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put(" ", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when session token and token is not passed in header ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_004() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        // headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,401);


    }

    @Test(priority = 1,description = "To check that add ofer api is thorowing error when MID is invalid ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_005() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid","Invalid");
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }
    @Test(priority = 1,description = "To check that add ofer api is throwing error when MID is empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_006() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid"," ");
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when MID key is not passed in request  ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_007() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        //body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when solution sub type is passed empty ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_008() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", " ");

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when solution sub type  key is passed empty  ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_009() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put(" ", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when channel value is passed as blank ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_010() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put("channel", " ");
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is throwing error when channel key is passed as blank", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_011() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", entityType);
        params.put(" ", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is is throwing error when entityType value is passed as blank ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_012() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put("entityType", " ");
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }
    @Test(priority = 1,description = "To check that add ofer api is is throwing error when entityType key is passed as blank ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_013() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", solution);
        params.put(" ", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is is throwing error when solution value is passed as blank ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_014() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put("solution", " ");
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }
    @Test(priority = 1,description = "To check that add ofer api is is throwing error when solution key is passed as blank ", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_015() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
        headers.put("version","7.3.0");
        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("androidId", "AashitAndroid");
        headers.put("browserName", "chrome");
        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put(" ", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }

    @Test(priority = 1,description = "To check that add ofer api is is throwing error when we are only passing token in headers", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC_016() throws JSONException {

        Map<String, String> headers = new HashMap<String, String>();

        headers.put("session_token", Token);
//        headers.put("version","7.3.0");
//        headers.put("UncleScrooge","BabaBlackSheepWeAreInShitDeep");
//        headers.put("accept", "*/*");
//        headers.put("Accept-Language", "en");
//        headers.put("Content-Type", "application/json");
//        headers.put("androidId", "AashitAndroid");
//        headers.put("browserName", "chrome");
//        headers.put("browserVersion", "4.6.3");

        Map<String,String> params=new HashMap<String,String>();
        params.put(" ", solution);
        params.put("entityType", entityType);
        params.put("channel", channel);
        params.put("solutionSubType", solutionSubType);

        Map<String, String> body = new HashMap<String, String>();
        body.put("mid",mid);
        requestPath = "MerchantServicev1sdMerchantlead/CreateDealsAddOfferTimeRequest.json";
        subfetchResponse= getCreateLeadP4bResponse.GetCreateLeadP4bResponse(requestPath,headers, body,params);


        int httpcode = subfetchResponse.getStatusCode();

        Assert.assertEquals(httpcode,400);


    }







}

