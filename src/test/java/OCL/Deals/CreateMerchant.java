package OCL.Deals;

import OCL.Subscription.FetchPlanSubscription;
import Request.CreateDeals.CreateDeals;
import Request.MerchantService.oe.panel.v1.lead.FetchLead;
import Services.CreateDeals.GetCreateLeadP4b;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import TestingLogics.RestAssuredRequestUtil;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;


public class CreateMerchant extends BaseMethod{

    private static final Logger LOGGER = LogManager.getLogger(FetchPlanSubscription.class);
    public String Token ;


    Response subfetchResponse = null;
    GetCreateLeadP4b getCreateLeadP4bResponse = new GetCreateLeadP4b();
    public static String solution="pg_profile_update";
    public static String entityType="PROPRIETORSHIP";
    public static String channel = "DIY_P4B_APP" ;
    public static String solutionSubType = "DEALS_ONBOARDING";
    public static String merchantMobileNumber = "9497887646";
    public static String mid = "T4sept63649571299882";
    public static String successMessage = "Request Submitted Successfully";

    String email = "<EMAIL>";
    String storeNumber = "9098978367";
    String storeDisplayName = "store display 34";
    String shopId = "565678";
    String instoreCategory = "DEALS_ONBOARDING";
    String businesslat = "23.52";
    String businessLong = "77.8";

    public static String source = "DIY_P4B_APP";
    //public static String solution = "solution";
    public static String solutionTypeLevel2 = "DEALS_ONBOARDING";
    public static String solutionTypeLevel3 = "payments_offline";


    String registerLead = "true";
    String triggerValue = "3";
    String leadId ;
    String requestPath;
    String md5Key;
    String identifier;

    Map<String, String> headers = new HashMap<String, String>();
    RestAssuredRequestUtil rutil = new RestAssuredRequestUtil(P.API.get("Boss_url"));
    Utilities Util = new Utilities();

    String requestId = Util.generateRandomBankAccountNumber();
    String num2  = Util.randomMobileNumberGenerator();
    String num = num2.replaceFirst("5","9");

    String requestBody = "{\"createMerReq\":" +
            "{\"CREATED_BY\":\"sobeer_sales\"," +
            "\"ACTION\":\"SubmitforApproval\"," +
            "\"MERCHANT_DETAILS\":{\"REQUEST_ID\":\""+requestId+"\"," +
            "\"USER_NAME\":\"*****************\"," +
            "\"ACCOUNT_FOR\":\"unifiedMerchantPanel\"," +
            "\"SOURCE_ID\":\"OE\",\"MERCHANT_TYPE\":\"NonSD\"," +
            "\"OFFLINE_ENABLED\":\"FALSE\"," +
            "\"PPI_LIMITED_MERCHANT\":\"0\"," +
            "\"BUSINESS_NAME\":\"ANMOLJAIN\"," +
            "\"BUSINESS_TYPE\":\"INDIVIDUAL\"," +
            "\"CALLBACK_URL_ENABLED\":\"TRUE\"," +
            "\"CUSTOM\":\"SYSTEMGENERATED\"," +
            "\"MERCHANT_NAME\":\"ANMOLJAIN\"," +
            "\"CURRENCY\":\"INR\"," +
            "\"REFUND_TO_BANK_ENABLED\":\"TRUE\"," +
            "\"STORE_CARD_DETAILS\":\"NO\"," +
            "\"ADD_MONEY_ENABLE\":\"TRUE\"," +
            "\"CHECKSUM_ENABLED\":\"TRUE\"," +
            "\"NUMBER_OF_RETRY\":\"1\"," +
            "\"CATEGORY\":\"Food\"," +
            "\"SUB_CATEGORY\":\"Restaurant\"," +
            "\"INDUSTRY_TYPE\":\"Retail\"," +
            "\"WALLET_RECHARGE_OPT\":\"MANUAL_RECHARGE\"," +
            "\"PROFILE_ID\":\"1\"," +
            "\"EMAIL_ALERT\":\"TRUE\"," +
            "\"KYB_ID\":\"B034wx7fvohvw377\"," +
            "\"CONVENIENCE_FEE_TYPE\":\"1\"," +
            "\"VALID_FROM\":\"05/19/2023\"," +
            "\"VALID_TO\":\"05/18/2026\"," +
            "\"MULTI_SUPPORT\":\"YES\"," +
            "\"HOW_MANY\":\"3\"," +
            "\"OCP\":\"TRUE\"," +
            "\"REQUEST_NAME\":\"DP2Web\"," +
            "\"FIRST_NAME\":\"ANMOL\"," +
            "\"LAST_NAME\":\"JAIN\"," +
            "\"MOBILE_NUMBER\":\""+num+"\"," +
            "\"PHONE_NUMBER\":\"\""+num+"\"\"," +
            "\"MerchUniqRef\":\"X\"," +
            "\"ACCOUNT_PRIMARY\":\"FALSE\"," +
            "\"CAN_EDIT_PMOBILE\":\"TRUE\"," +
            "\"IS_SUB_USER\":\"FALSE\"," +
            "\"ADDRESS1\":\"F1\"," +
            "\"ADDRESS3\":\"TrikutaOuterRoad\"," +
            "\"COUNTRY\":\"India\"," +
            "\"STATE\":\"JammuandKashmir\"," +
            "\"CITY\":\"Jammu\"," +
            "\"PIN\":\"180012\"," +
            "\"SAME_AS_BUSINESS_ADDR\":\"TRUE\"," +
            "\"COMMUNICATION_ADDRESS1\":\"F1\"," +
            "\"COMMUNICATION_ADDRESS3\":\"TrikutaOuterRoad\"," +
            "\"COMMUNICATION_COUNTRY\":\"India\"," +
            "\"COMMUNICATION_STATE\":\"JammuandKashmir\"," +
            "\"COMMUNICATION_CITY\":\"Jammu\"," +
            "\"COMMUNICATION_PIN\":\"180012\"," +
            "\"COMMUNICATION_LATITUDE\":\"28.550667\"," +
            "\"COMMUNICATION_LONGITUDE\":\"77.268952\"," +
            "\"KYC_BANK_NAME\":\"ICICIbank\"," +
            "\"KYC_BANK_ACCOUNT_HOLDER_NAME\":\"ANMOLJAIN\"," +
            "\"KYC_BANK_ACCOUNT_NO\":\"**************\"," +
            "\"KYC_BUSINESS_IFSC_NO\":\"ICIC0004145\"," +
            "\"KYC_AUTHORIZED_SIGNATORY_NAME\":\"ANMOLJAIN\"," +
            "\"COMM_STAT_SELECT\":\"1\"," +
            "\"EMAIL_MERCHANT\":\"TRUE\"," +
            "\"EMAIL_CONSUMER\":\"FALSE\"," +
            "\"REQUEST_TYPE_NAME\":\"DEFAULT\"," +
            "\"WEBSITE_NAME\":\"paytm\"," +
            "\"SIZE_OF_KEY\":\"16\"," +
            "\"SMS_MERCHANT\":\"FALSE\"," +
            "\"PAYOUT_DAYS\":\"1\"," +
            "\"ONLINE_SETTLEMENT\":\"FALSE\"," +
            "\"FLAG_MERCHANT\":\"FALSE\"," +
            "\"BW_ENABLED\":\"TRUE\"," +
            "\"BW_CONFIG\":{\"TRANSFER_MODE\":\"M2B\"," +
            "\"AUTO\":\"TRUE\"," +
            "\"TRIGGER_MODE\":\"TIME_INTERVAL\"," +
            "\"TRIGGER_VALUE\":\"1\"}," +
            "\"API_DISABLED\":\"TRUE\"," +
            "\"P2M_ENABLED\":\"TRUE\"," +
            "\"OB_CHANNEL\":\"DIY_P4B_APP\"," +
            "\"STATIC_PREF\":[\"UPI_PAYOPT_ONBOARDED_MERCHANT\"]," +
            "\"TRANSACTION_WISE_SETTLEMENT\":\"FALSE\"," +
            "\"SOLUTION_TYPE\":\"OFFLINE\"," +
            "\"STATIC_PREFERENCES\":{\"P2PM_MERCHANT\":\"N\"}}," +
            "\"URL_DETAILS\":[{\"WEBSITE_NAME\":\"dp2wen\",\"" +
            "REQUEST_URL\":\"https://secure.paytm.in/MerchantSite/bankResponse\"," +
            "\"RESPONSE_URL\":\"https://secure.paytm.in/oltp-web/smsInvoiceAddMoney/displayPaymentStatus?ORDER_ID=m1\"," +
            "\"PEON_URL\":\"http://dp2web.com\"," +
            "\"IMAGE_NAME\":\"paytm_log\"}," +
            "{\"WEBSITE_NAME\":\"paytm\"," +
            "\"REQUEST_URL\":\"https://www.paytm.com\"," +
            "\"RESPONSE_URL\":\"https://cart-beta.paytm.com/payment/status\"," +
            "\"IMAGE_NAME\":\"paytm_log\"}," +
            "{\"WEBSITE_NAME\":\"retail\"," +
            "\"REQUEST_URL\":\"https://www.paytm.com\"," +
            "\"RESPONSE_URL\":\"https://cart-beta.paytm.com/payment/status\"," +
            "\"IMAGE_NAME\":\"paytm_log\"}]," +
            "\"DOCS_DETAILS\":{\"DETAILED_LIST\":[\"PANCARD\"]}}," +
            "\"configVelocity\":{\"VELOCITIES\":[{\"VELOCITY_TYPE\":\"PER_MID\"," +
            "\"VELOCITY_DETAILS\":{\"MAX_AMT_PER_DAY\":\"5000\"," +
            "\"MAX_AMT_PER_MONTH\":\"10000\"}}]}," +
            "\"configureMbidAndInstrument\":{\"configureMerchantCommission\":{\"ACTION\":\"EDIT\"," +
            "\"COMMISSION\":{\"FEE_TYPE\":\"simple\"," +
            "\"PERCENT_COMMISSION\":\"0\"," +
            "\"COMMISSION_TYPE_BOTH\":\"FALSE\"}}," +
            "\"TXN_TYPES\":[{\"TXN_TYPE\":\"Payments\"," +
            "\"PAY_MODES\":[{\"PAY_MODE\":\"UPI\"," +
            "\"COMMISSION\":{\"FEE_TYPE\":\"simple\"," +
            "\"PERCENT_COMMISSION\":\"0\"," +
            "\"COMMISSION_TYPE_BOTH\":\"FALSE\"}," +
            "\"BANKS\":[]}]}]},\"CUST_ID\":\"0\"}";

    String endpoint = "/admin/app/api/v3/submitCreateMerchantAPIRequest";




    @BeforeClass
    public void generateToken(){
//        Token = ApplicantToken(merchantMobileNumber, "paytm@123");
//        XMWCookie=findXMWTokenforPanel("**********","paytm@123");
//        {
//            if(Util.createNewAuthUser(num,"paytm@123")){
//                Token = BaseMethod.ApplicantToken(num, "paytm@123");
//            }
//            else{
//                Assert.assertFalse(false,"Number was not registered at Outh end");
//            }
//        }


        headers.put("Postman-Token","436f9a0e-b312-4c5c-bfd7-5ee0ff0eb2ec");
        headers.put("Cookie","JSESSIONID=B910C97B7F4126274EB931FB9F7D0329.adminjvm1; JSESSIONID=********************************");
        headers.put("accept", "*/*");
        headers.put("Accept-Language", "en");
        headers.put("Content-Type", "application/json");
        headers.put("Cache-Control", "no-cache");
    }


    @Test(priority = 1,description = "To check that create deals lead api is giving 200 and lead is created", groups = { "Regression" })
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void createMerchant() throws Exception{

        String resp = rutil.postRequest(endpoint,headers,requestBody,200);

    }


}

