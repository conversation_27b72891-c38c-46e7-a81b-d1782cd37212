package OCL.CommonOnboarding;

import Request.CommonOnboardingEDC.sendOTPLead;
import Request.MerchantService.v3.SubmitMerchant;
import Request.MerchantService.v3.ValidateOtp;
import Request.MerchantService.v3.merchant.fetch.FetchV3Merchant;
import Services.DBConnection.DBConnection;
import Services.MechantService.MiddlewareServices;
import Services.Utilities.Utilities;
import Services.oAuth.oAuthServices;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
import org.apache.commons.math3.analysis.function.Add;
import org.testng.Assert;
import org.testng.annotations.BeforeSuite;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.io.FileWriter;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;


public class FlowCommonOnboardingRekycKyc_UpdateAutoQC_Individual_WithPAN extends BaseMethod {



    public int stagingServer = getCurrentStagingServer();

    public int getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;  // preprod PAN
        }
        return 6; // default
    }

    public String MCOIndividualWorkflowStatusId = "";

    public String sToken = "";

    public String AgentCustID = "";
    public String QCAgentsToken = "";

    String BusinessOwnerPhotoDMSID = "";
    String AadharPhotoDMSID1 = "";
    String AadharPhotoDMSID2 = "";
    String PanphotoDMSID = "";
    String CancelledChequePhotoDMSID = "";
    String ShopphotoDMSID = "";

    //businessProof
    String BusinessProofPhotoDMSID = "";
    public String AgreementOTPState = "";
    //   String DocPath = "src/test/resources/MerchantService/V1/sdMerchant/lead/LeadCreateDIYMCO/DocImage.png";
    String requestPath = "";
    String endPoint = P.API.get("SubmitDocs");

    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    oAuthServices oAuthServicesObj = new oAuthServices();
    Utilities utilities = new Utilities();

    // create a final variable for mobile number
    final String Mobile = getMobileNumberForEnvironment();
    final String Mid = getMidForEnvironment();
    final String PAN = getPanForEnvironment();

    boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

    public static String version = "7.2.8";

    String custId = "";

    String state = "";
    String leadId = "";
    String AddressUUID = "";

    String relatedBusinessUuid = "";
    String aadharNumber = utilities.generateRandomAadhaar();



    Map<String, String> headers = new HashMap<>();

    private static final String LOG_FILE = "src/test/resources/logs/api_responses.log";

    private void logResponse(String testName, Response response) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(LOG_FILE, true))) {
            LocalDateTime now = LocalDateTime.now();
            writer.println("Test: " + testName);
            writer.println("Time: " + now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            writer.println("Status Code: " + response.getStatusCode());
            writer.println("Response Body: " + response.getBody().asString());
            writer.println("----------------------------------------");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Response executeWithRetry(String testName, Supplier<Response> apiCall) throws Exception {
        int maxRetries = 5;
        int retryCount = 0;
        Response response = null;

        while (retryCount < maxRetries) {
            response = apiCall.get();
            logResponse(testName, response);

            if (response.getStatusCode() != 500) {
                break;
            }
            retryCount++;
            if (retryCount < maxRetries) {
                Thread.sleep(1000); // Wait 1 second before retry
            }
        }

        if (response.getStatusCode() == 307 && retryCount == maxRetries) {
            throw new Exception(testName + " failed after " + maxRetries + " retries");
        }

        return response;
    }
    private String generateRandomFseBeatTagMappingId() {
        // Generate a random 10-digit number
        StringBuilder sb = new StringBuilder("Beat");
        for (int i = 0; i < 7; i++) {
            sb.append((int) (Math.random() * 10));
        }
        return sb.toString();
    }

    public FlowCommonOnboardingRekycKyc_UpdateAutoQC_Individual_WithPAN() throws Exception {
    }

    @BeforeSuite
    @Owner(emailId = "<EMAIL>", isAutomated = true)

    public void BeforeSuiteLogin() throws Exception {
        sToken = CommonAgentToken;
        QCAgentsToken = XMWCookie;

        DBConnection dbConnection = new DBConnection();
        dbConnection.UpdateQueryToCloseLead(Mobile, "merchant_common_onboard");

    //    dbConnection.UpdateQueryToCloseLeadsolnlevel3(Mobile, "merchant_common_onboard", "KYC_UPDATE");
        //dbConnection.UpdateQueryToCloseLeads(Mobile, "merchant_common_onboard");

        // sToken = AgentSessionToken("8010630022", "paytm@123");
        // QCAgentsToken = AgentSessionToken("7771216290", "paytm@123");
        // XMWCookie = findXMWTokenforPanel("7771216290", "paytm@123");

       // establishConnectiontoServer(sToken,stagingServer);
        waitForLoad(3000);

        //boolean oauthuser = utilities.createNewAuthUser(Mobile, "Paytm@123");

        custId = GetResourceOwnerId(Mobile, "paytm@123");
        System.out.println("custId is" + custId);
        AgentCustID = GetResourceOwnerId("8010630022", "paytm@123");
        System.out.println("AgentCustID is" + AgentCustID);



    }



    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {


        establishConnectiontoServer(sToken,stagingServer);

        headers.put("session_token", sToken); // Updated from first list
        headers.put("accept", "application/json, text/plain, */*"); // No change
        headers.put("content-type", "application/json; charset=UTF-8");
        headers.put("version", "7.2.8"); // No change
        headers.put("deviceidentifier", "OPPO-CPH1859-869003037324211"); // Updated from first list
        headers.put("accept-language", "en,en-IN;q=0.9,en-US;q=0.8"); // No change
        headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep"); // No match in first list, left unchanged


    }
    @Test(retryAnalyzer = RetryAnalyzer.class)
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC01_Create_Lead() throws Exception {
        Response createMCOLead = executeWithRetry("TC01_Create_Lead", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOTPRequestMCO_KycUpdate_Rekyc_AutoQC.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("fseBeatTagMappingId", generateRandomFseBeatTagMappingId());
            ValidateOtp.getProperties().setProperty("mid", Mid);


            //Mid
            ValidateOtp.getProperties().setProperty("referenceValue", Mid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });

        leadId = createMCOLead.path("leadId");
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC01_Create_Lead")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC02_SendOtp() throws Exception {
        Response createMCOLead = executeWithRetry("TC02_SendOtp", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMco.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
           sendOTPobj.getProperties().setProperty("mobile", Mobile);
          sendOTPobj.getProperties().setProperty("custId", custId);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });

        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        state = createMCOLead.path("state");
    }

    @Owner(emailId = "<EMAIL>", isAutomated = true)
    @Test(description = ")", priority = 3, dependsOnMethods = "TC02_SendOtp")
    public void TC03_ValidateOTP() throws Exception {
        Response createMCOLead = executeWithRetry("TC03_ValidateOTP", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoKycUpdate_Rekyc.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", state);
            ValidateOtp.getProperties().setProperty("otp", "888888");
           ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);
            ValidateOtp.getProperties().setProperty("agentCustId", AgentCustID);
           ValidateOtp.getProperties().setProperty("custId", custId);
          ValidateOtp.getProperties().setProperty("mid", Mid);


            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }


//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = ")", priority = 4, dependsOnMethods = "TC04_ValidateOTP")
//    public void TC05_AddBankDetail() throws Exception {
//        Response createMCOLead = executeWithRetry("TC05_AddBankDetail", () -> {
//            requestPath = "MerchantService/V3/SubmitMerchant/SubmitMCOAddBankDeatil.json";
//            SubmitMerchant AddBankDetail = new SubmitMerchant(custId, requestPath);
//            AddBankDetail.getProperties().setProperty("bankAccountNumber", Mobile);
//
//            Map<String, String> queryParams = new HashMap<>();
//            queryParams.put("solutionType", "merchant_common_onboard");
//            queryParams.put("entityType", "INDIVIDUAL");
//            queryParams.put("leadId", leadId);
//
//            return middlewareServicesObject.AddBankZDetail(AddBankDetail, queryParams, headers);
//        });
//        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
//        relatedBusinessUuid = createMCOLead.path("relatedBusinessUuid");
//    }


//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    @Test(description = ")", priority = 5, dependsOnMethods = "TC05_AddBankDetail")
//    public void TC06_ConfirmBerau() throws Exception {
//        Response createMCOLead = executeWithRetry("TC06_ConfirmBerau", () -> {
//            requestPath = "MerchantService/V3/SubmitMerchant/ConfirmBearau.json";
//            SubmitMerchant confirmbearu = new SubmitMerchant(custId, requestPath);
//            confirmbearu.getProperties().setProperty("bankAccountNumber", Mobile);
//            confirmbearu.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
//
//            Map<String, String> queryParams = new HashMap<>();
//            queryParams.put("solutionType", "merchant_common_onboard");
//            queryParams.put("entityType", "INDIVIDUAL");
//            queryParams.put("leadId", leadId);
//
//            return middlewareServicesObject.AddBankZDetail(confirmbearu, queryParams, headers);
//        });
//        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
//        String displayMessage = createMCOLead.path("displayMessage");
//        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_ConfirmBerau")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC07_AADHAR_OCR_INITIATED() throws Exception {
//        Response createMCOLead = executeWithRetry("TC07_AADHAR_OCR_INITIATED", () -> {
//            requestPath = "MerchantService/V3/SubmitMerchant/AadharOCRInitiate.json";
//            SubmitMerchant AADHAR_OCR_INITIATED_obj = new SubmitMerchant(custId, requestPath);
//            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("bankAccountNumber", Mobile);
//            AADHAR_OCR_INITIATED_obj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
//
//            Map<String, String> queryParams = new HashMap<>();
//            queryParams.put("solutionType", "merchant_common_onboard");
//            queryParams.put("entityType", "INDIVIDUAL");
//            queryParams.put("leadId", leadId);
//
//            return middlewareServicesObject.AddBankZDetail(AADHAR_OCR_INITIATED_obj, queryParams, headers);
//        });
//        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
//        String displayMessage = createMCOLead.path("displayMessage");
//        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_AADHAR_OCR_INITIATED")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC08_SubmitAadharOCRDetails() throws Exception {
//        Response createMCOLead = executeWithRetry("TC08_SubmitAadharOCRDetails", () -> {
//            requestPath = "MerchantService/V3/SubmitMerchant/SubmitAadharOCRDetails.json";
//            SubmitMerchant SubmitAadharOCRDetailsObj = new SubmitMerchant(custId, requestPath);
//            SubmitAadharOCRDetailsObj.getProperties().setProperty("bankAccountNumber", Mobile);
//            SubmitAadharOCRDetailsObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
//            SubmitAadharOCRDetailsObj.getProperties().setProperty("aadharNumber", aadharNumber);
//
//            Map<String, String> queryParams = new HashMap<>();
//            queryParams.put("solutionType", "merchant_common_onboard");
//            queryParams.put("entityType", "INDIVIDUAL");
//            queryParams.put("leadId", leadId);
//
//            return middlewareServicesObject.AddBankZDetail(SubmitAadharOCRDetailsObj, queryParams, headers);
//        });
//        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
//        String displayMessage = createMCOLead.path("displayMessage");
//        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC03_ValidateOTP")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC04_FetchLeadDetail() throws Exception {
        Response RespV3Fetch = executeWithRetry("TC04_FetchLeadDetail", () -> {
            FetchV3Merchant v3Merch = new FetchV3Merchant(leadId);
            Map<String, String> query = new HashMap<>();
            query.put("entityType", "INDIVIDUAL");
            query.put("solutionType", "merchant_common_onboard");
            query.put("custId", custId);

            return middlewareServicesObject.FetchV3MerchantLead(v3Merch, query, headers);
        });
        AddressUUID = RespV3Fetch.path("merchantDetails.suggestedRelatedBusinesses[0].address.addressUuid");
       relatedBusinessUuid = RespV3Fetch.path("merchantDetails.relatedBusinessUuid");



        //relatedBusinessUuid = RespV3Fetch.path("merchantDetails.relatedBusinessUuid");

        System.out.println("These are Variables" +  AddressUUID + relatedBusinessUuid);

    }
    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC04_FetchLeadDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC05_SubmitPanDetail() throws Exception {
        Response createMCOLead = executeWithRetry("TC05_SubmitPanDetail", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitPanDetailMcoRekyc_AutoQC.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
           SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("addressUuid", AddressUUID);

            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }



//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_SubmitAadharOCRDetails")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC11_uploadBankProofDocument() throws Exception {
//        Response submitDocs = executeWithRetry("TC11_uploadBankProofDocument", () -> {
//            Map<String, String> params = new HashMap<String, String>();
//            params.put("solutionType", "merchant_common_onboard");
//            params.put("entityType", "INDIVIDUAL");
//            params.put("solutionLeadId", leadId);
//            params.put("pageNo", "0");
//            params.put("docCount", "0");
//            params.put("docType", "bankProof");
//            params.put("docProvided", "cancelledChequePhoto");
//            params.put("merchantCustId", custId);
//            params.put("type", "jpg");
//            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));
//
//            SubmitDocs v3DocSubmit = new SubmitDocs();
//            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
//        });
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//        Assert.assertEquals(errorCode, 204);
//    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC05_SubmitPanDetail")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC06_SubmitGSTNSkippedMCO() throws Exception {
        Response createMCOLead = executeWithRetry("TC06_SubmitGSTNSkippedMCO", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SubmitGSTNSkippedMCO_AutoQCInd.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
           // SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_SubmitGSTNSkippedMCO")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC12_UploadBusinessOwnerPhoto() throws Exception {
//        Response submitDocs = executeWithRetry("TC12_UploadBusinessOwnerPhoto", () -> {
//            Map<String, String> params = new HashMap<String, String>();
//            params.put("solutionType", "merchant_common_onboard");
//            params.put("entityType", "INDIVIDUAL");
//            params.put("solutionLeadId", leadId);
//            params.put("pageNo", "0");
//            params.put("docCount", "0");
//            params.put("docType", "businessOwnerPhoto");
//            params.put("docProvided", "businessOwnerPhoto");
//            params.put("merchantCustId", custId);
//            params.put("type", "jpg");
//            params.put("docId", utilities.randomMobileNumberGeneratorStartWith(9));
//
//            SubmitDocs v3DocSubmit = new SubmitDocs();
//            return middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
//        });
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//        Assert.assertEquals(errorCode, 204);
//    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC12_UploadBusinessOwnerPhoto")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS() throws Exception {
//        Response createMCOLead = executeWithRetry("TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS", () -> {
//            requestPath = "MerchantService/V3/SubmitMerchant/MerchantPhotoSkippedAfterAllAtempts.json";
//            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
//            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
//            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
//            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
//
//            Map<String, String> queryParams = new HashMap<>();
//            queryParams.put("solutionType", "merchant_common_onboard");
//            queryParams.put("entityType", "INDIVIDUAL");
//            //
//            queryParams.put("leadId", leadId);
//
//            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
//        });
//        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
//        String displayMessage = createMCOLead.path("displayMessage");
//        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC13_MERCHANT_PHOTO_SKIPPED_AFTER_EXHAUSTING_ATTEMPTS")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC14_qna_submit() throws Exception {
//        Response createMCOLead = executeWithRetry("TC14_qna_submit", () -> {
//            requestPath = "MerchantService/V3/SubmitMerchant/QNA.json";
//            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
//            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
//            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
//            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
//
//            Map<String, String> queryParams = new HashMap<>();
//            queryParams.put("solutionType", "merchant_common_onboard");
//            queryParams.put("entityType", "INDIVIDUAL");
//            queryParams.put("leadId", leadId);
//
//            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
//        });
//        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
//        String displayMessage = createMCOLead.path("displayMessage");
//        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC06_SubmitGSTNSkippedMCO")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC07_SegmentSubSegmentSubmit() throws Exception {
        Response createMCOLead = executeWithRetry("TC07_SegmentSubSegmentSubmit", () -> {
            requestPath = "MerchantService/V3/SubmitMerchant/SegmentSubSegmentSubmit_AutoQC_Ind.json";
            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
           // SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");
            queryParams.put("leadId", leadId);

            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC15_SegmentSubSegmentSubmit")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC16_UAC_Submit() throws Exception {
//        Response createMCOLead = executeWithRetry("TC16_UAC_Submit", () -> {
//            requestPath = "MerchantService/V3/SubmitMerchant/UACSubmit.json";
//            SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
//            SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
//            SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
//            SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
//
//            Map<String, String> queryParams = new HashMap<>();
//            queryParams.put("solutionType", "merchant_common_onboard");
//            queryParams.put("entityType", "INDIVIDUAL");
//            queryParams.put("leadId", leadId);
//
//            return middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
//        });
//        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
//        String displayMessage = createMCOLead.path("displayMessage");
//        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//    }



    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC07_SegmentSubSegmentSubmit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC08_RBDScreen_Submit() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/RBDSubmit_AutoQC_Rekyc.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);

       // SubmitPanDetailMcoObj.getProperties().setProperty("bankAccountNumber", Mobile);
        SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
        SubmitPanDetailMcoObj.getProperties().setProperty("PAN", PAN);
        SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);


        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }

    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC08_RBDScreen_Submit")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC09_SendOtpforAgreement() throws Exception {
        Response createMCOLead = executeWithRetry("TC09_SendOtpforAgreement", () -> {
            requestPath = "MerchantService/V3/SendOtp/SendOtpMcoForAgreement.json";
            sendOTPLead sendOTPobj = new sendOTPLead(requestPath);
            sendOTPobj.getProperties().setProperty("mobile", Mobile);
            sendOTPobj.getProperties().setProperty("custId", custId);
            sendOTPobj.getProperties().setProperty("lead_id", leadId);
            
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.SendOtpMco(sendOTPobj, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        AgreementOTPState = createMCOLead.path("state");
    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC09_SendOtpforAgreement")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC10_ValidateOtpforAgreement() throws Exception {
        Response createMCOLead = executeWithRetry("TC10_ValidateOtpforAgreement", () -> {
            requestPath = "MerchantService/V3/ValidateOtp/ValidateOtpMcoForAgreementKycUpdate_Rekyc.json";
            ValidateOtp ValidateOtp = new ValidateOtp(requestPath);
            ValidateOtp.getProperties().setProperty("state", AgreementOTPState);
            ValidateOtp.getProperties().setProperty("otp", "888888");
            ValidateOtp.getProperties().setProperty("mobile", Mobile);
            ValidateOtp.getProperties().setProperty("leadId", leadId);
            ValidateOtp.getProperties().setProperty("mid", Mid);

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("solutionType", "merchant_common_onboard");
            queryParams.put("entityType", "INDIVIDUAL");

            return middlewareServicesObject.CreateLead(ValidateOtp, queryParams, headers);
        });
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC20_ValidateOtpforAgreement")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC21_UploadShopPhoto() throws Exception {
//
//        // String endPoint = P.API.get("SubmitDocs");
//
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("solutionType", "merchant_common_onboard");
//        //  params.put("channel", channel);
//        params.put("entityType", "INDIVIDUAL");
//        params.put("leadId", leadId);
//        params.put("pageNo", "0");
//        params.put("docCount", "0");
//        params.put("docType", "shopFrontPhoto");
//        params.put("docProvided", "shopFrontPhoto");
//        params.put("merchantCustId", custId);
//        params.put("docLat", "28.5682852");
//        params.put("docLong", "77.3956855");
//        params.put("docDistance", "0.3397966438531691");
//
//        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
//        params.put("docId", randomID);
//
//
//        SubmitDocs v3DocSubmit = new SubmitDocs();
//
//        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//        Assert.assertEquals(errorCode, 204);
//
//
//    }

//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC21_UploadShopPhoto")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC22_UploadAadharPhoto1() throws Exception {
//
//        // String endPoint = P.API.get("SubmitDocs");
//
//
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("solutionType", "merchant_common_onboard");
//        //  params.put("channel", channel);
//        params.put("entityType", "INDIVIDUAL");
//        params.put("leadId", leadId);
//        params.put("pageNo", "1");
//        params.put("docCount", "2");
//        params.put("docType", "poi");
//        params.put("docProvided", "aadhaar");
//        params.put("merchantCustId", custId);
//        params.put("docLat", "28.5682852");
//        params.put("docLong", "77.3956855");
//        params.put("docDistance", "0.3397966438531691");
//
//        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
//        params.put("docId", randomID);
//
//
//        SubmitDocs v3DocSubmit = new SubmitDocs();
//
//        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//        Assert.assertEquals(errorCode, 204);
//
//
//    }
//
//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto1")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC22_UploadAadharPhoto2() throws Exception {
//
//        // String endPoint = P.API.get("SubmitDocs");
//
//
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("solutionType", "merchant_common_onboard");
//        //  params.put("channel", channel);
//        params.put("entityType", "INDIVIDUAL");
//        params.put("leadId", leadId);
//        params.put("pageNo", "2");
//        params.put("docCount", "2");
//        params.put("docType", "poi");
//        params.put("docProvided", "aadhaar");
//        params.put("merchantCustId", custId);
//        params.put("docLat", "28.5682852");
//        params.put("docLong", "77.3956855");
//        params.put("docDistance", "0.3397966438531691");
//
//        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
//        params.put("docId", randomID);
//
//
//        SubmitDocs v3DocSubmit = new SubmitDocs();
//
//        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//        Assert.assertEquals(errorCode, 204);
//
//
//    }




//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC22_UploadAadharPhoto2")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC23_UploadPanDoc() throws Exception {
//
//        // String endPoint = P.API.get("SubmitDocs");
//
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("solutionType", "merchant_common_onboard");
//        //  params.put("channel", channel);
//        params.put("entityType", "INDIVIDUAL");
//        params.put("leadId", leadId);
//        params.put("pageNo", "0");
//        params.put("docCount", "0");
//        params.put("docType", "pan");
//        params.put("docProvided", "pan");
//        params.put("merchantCustId", custId);
//        params.put("docLat", "28.5682852");
//        params.put("docLong", "77.3956855");
//        params.put("docDistance", "0.3397966438531691");
//
//        String randomID = utilities.randomMobileNumberGeneratorStartWith(9);
//        params.put("docId", randomID);
//
//
//        SubmitDocs v3DocSubmit = new SubmitDocs();
//
//        Response submitDocs = middlewareServicesObject.V3SubmitDocs(v3DocSubmit, params, headers);
//        int errorCode = submitDocs.jsonPath().getInt("errorCode");
//        Assert.assertEquals(errorCode, 204);
//
//
//    }


    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC10_ValidateOtpforAgreement")
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void TC11_McoLeadSubmitAfterUploadingAllDocs() throws Exception {

        requestPath = "MerchantService/V3/SubmitMerchant/FinalMcoLeadSubmitAfterUploadingAllDocs.json";
        SubmitMerchant SubmitPanDetailMcoObj = new SubmitMerchant(custId, requestPath);
      //  SubmitPanDetailMcoObj.getProperties().setProperty("relatedBusinessUuid", relatedBusinessUuid);
       // SubmitPanDetailMcoObj.getProperties().setProperty("AddressUUID", AddressUUID);


        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solutionType", "merchant_common_onboard");
        queryParams.put("entityType", "INDIVIDUAL");
        queryParams.put("leadId", leadId);

        Response createMCOLead = middlewareServicesObject.AddBankZDetail(SubmitPanDetailMcoObj, queryParams, headers);
        Assert.assertEquals(createMCOLead.getStatusCode(), 200);
        String displayMessage = createMCOLead.path("displayMessage");
        Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");

        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
        //  entities = entities.replace("[", "").replace("]", "");
        // String[] entityList = entities.split(" ");
    }


//    @Test(retryAnalyzer = RetryAnalyzer.class, dependsOnMethods = "TC25_McoLeadSubmitAfterUploadingAllDocs")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC26_McoLeadFetchDocStatus() throws Exception {
//
//        LoanIntentfetchDocumentDetails obj = new LoanIntentfetchDocumentDetails();
//
//        Map<String, String> queryParams = new HashMap<>();
//        queryParams.put("solution", "merchant_common_onboard");
//        queryParams.put("entityType", "INDIVIDUAL");
//        queryParams.put("leadId", leadId);
//        queryParams.put("merchantCustId", custId);
//        queryParams.put("channel", "GG_APP");
//
//
//        Response respObj = middlewareServicesObject.revisitLoanIntentFetchDoc(obj, headers, queryParams);
//
//        Assert.assertEquals(respObj.getStatusCode(), 200);
//        ShopphotoDMSID = respObj.path("uploadedDocDetailsSet[0].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
//        BusinessOwnerPhotoDMSID = respObj.path("uploadedDocDetailsSet[1].uploadedDocs[0].uuid");
//        AadharPhotoDMSID1 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['1']");
//        AadharPhotoDMSID2 = respObj.path("uploadedDocDetailsSet[2].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['2']");
//        PanphotoDMSID = respObj.path("uploadedDocDetailsSet[3].uploadedDocs[0].uuid");
//        CancelledChequePhotoDMSID = respObj.path("uploadedDocDetailsSet[4].uploadedDocs[0].multiPageDocDetails.multiPageDocAdditionalInfo['0']");
//
//
//        System.out.println(ShopphotoDMSID + BusinessOwnerPhotoDMSID + AadharPhotoDMSID1 + AadharPhotoDMSID2 + PanphotoDMSID + CancelledChequePhotoDMSID);
//        // Assert.assertEquals(displayMessage, "Details saved. Images will be submitted in the background. You can provide remaining details (if any) later");
//
//        //  String entities = createMCOLead.jsonPath().getString("dataMap.COEntities");
//        //  entities = entities.replace("[", "").replace("]", "");
//        // String[] entityList = entities.split(" ");
//    }
//
//    @Test(dependsOnMethods = "TC26_McoLeadFetchDocStatus")
//    @Owner(emailId = "<EMAIL>", isAutomated = true)
//    public void TC27_McoIndividualLeadQC() throws Exception {
//
//        DBConnection dbConnectionObj = new DBConnection();
//
//
//        int Ubmid = dbConnectionObj.getUserBusinessMappingId(Mobile, "merchant_common_onboard");
//
//        dbConnectionObj.assignAgentViaDB("1152", Ubmid);
//
//
//        System.out.println("Ubmid is " + Ubmid);
//
//        Long MCOIndividualWorkflowStatusId = dbConnectionObj.getWorkflowStatusID(Ubmid);
//
//        System.out.println("MCOIndividualWorkflowStatusId is " + MCOIndividualWorkflowStatusId);
//
//        //Save value of MCOIndividualWorkflowStatusId in string
//        String wfsid = String.valueOf(MCOIndividualWorkflowStatusId);
//        System.out.println("wfsid is " + wfsid);
//
//        requestPath = "MerchantServiceOEPanelV1EditLead/EditLeadMcoIndividualWithPanKycUpdate_Rekyc.json";
//        EditLead EditLeadObj = new EditLead(leadId, requestPath);
//        EditLeadObj.getProperties().setProperty("uuidAadharPhoto1", AadharPhotoDMSID1);
//        EditLeadObj.getProperties().setProperty("uuidAadharPhoto2", AadharPhotoDMSID2);
//        EditLeadObj.getProperties().setProperty("uuidShopPhoto", ShopphotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidBankPhoto", CancelledChequePhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto", BusinessOwnerPhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidBusinessOwnerPhoto1", BusinessOwnerPhotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidPanPhoto", PanphotoDMSID);
//        EditLeadObj.getProperties().setProperty("uuidPanPhoto1", PanphotoDMSID);
//        EditLeadObj.getProperties().setProperty("workflowStatusId", wfsid);
//        Map<String, String> queryParams = new HashMap<>();
//        queryParams.put("action", "SUBMIT");
//        Map<String, String> headers = new HashMap<>();
//        //headers.put("Host", "goldengate-staging7.paytm.com");
//        // headers.put("session_token", QCAgentsToken);
//        headers.put("content-type", "application/json; charset=UTF-8");
//        headers.put("Cookie", XMWCookie);
//        Response QCMCOLead = middlewareServicesObject.v1EditLeadOEMco(EditLeadObj, queryParams, headers);
//
//    }

    private String getMobileNumberForEnvironment() {
        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return "6385742870";  // staging mobile number
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return "6385742870";  // preprod mobile number
        } 
            return "6385742870";
    }

    private String getMidForEnvironment() {
        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return "nHbCFU13660857670511";  // staging MID
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return "nHbCFU13660857670511";  // preprod MID
        } 
        return "nunHbCFU13660857670511ll"; // default
    }

    private String getPanForEnvironment() {
        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return "**********";  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return "**********";  // preprod PAN
        } 
        return "**********"; // default
    }
}

