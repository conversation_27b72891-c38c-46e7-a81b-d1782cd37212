package OCL.CommonOnboarding;

import Request.MerchantService.v1.sfcrm.Lead;
import Services.MechantService.MiddlewareServices;
import Services.UAD.UADServices;
import Services.Utilities.Utilities;
import com.github.javafaker.Faker;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;
import io.restassured.response.Response;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;
public class FlowCommonOnboardingLightWeightAPI extends BaseMethod  {


   // private static final Logger LOGGER = Logger.getLogger(OCL.Business.AssistedMerchantOnboard.FlowAssistedMerchantOnboardFixed.class);
    MiddlewareServices middlewareServicesObject = new MiddlewareServices();
    UADServices uadServicesObject = new UADServices();
    Faker GenerateFake = new Faker();
    Utilities utilities = new Utilities();
    String AuthorizationToken = "";

    //public static String AgentToken = "6d21d26e-3e1a-4465-a101-7ee9c9697500";
    public static String CustId = "";
    public static String mobileNo = "5112717570";
    public static String newState = "";
    public static String version = "4.7.3";
    public static String OTP = "";
    public static String PAN = "";
    public static String leadId = "f58a0800-c4b3-4127-8bfd-24cce82cb3eb";
    public static String RRBUUID = "";
    public static String nameMatchStatus = "";
    public String lineOne = GenerateFake.address().streetAddress();
    public String lineTwo = GenerateFake.address().cityName();
    public String lineThree = GenerateFake.address().streetName();
    public static String OePanelDocStatus = "REJECTED";
    public static String RejectionReason = "Wrong Photo";
    public static String DocumetRequestDeserialised = "";
    public static String LeadStagePanel = "";
    public static String WorkFlowId = "";
    public static String XMWToken = "";
    public static String MID = "";
    public static String content_type="application/json; charset=UTF-8";

    public int stagingServer = getCurrentStagingServer();

    public int getCurrentStagingServer() {


        String apiUrl = P.API.get("api_url");
        if (apiUrl.contains("https://goldengate-staging7.paytm.com/MerchantService")) {
            return 7;  // staging PAN
        } else if (apiUrl.contains("https://goldengate-staging6.paytm.com/MerchantService")) {
            return 6;  // preprod PAN
        }
        return 5; // default
    }
    public  String session_token = AgentSessionToken(mobileNo, "paytm@123");

    @BeforeTest
    @Owner(emailId = "<EMAIL>", isAutomated = true)
    public void AgentLogin() throws Exception {

        establishConnectiontoServer(session_token, stagingServer);
    }

    public Map<String,String> setCommonParams(){
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("solution","merchant_common_onboard");
        //queryParams.put("leadId","f58a0800-c4b3-4127-8bfd-24cce82cb3eb");
        queryParams.put("entityType","");

        queryParams.put("solutionLeadId","f58a0800-c4b3-4127-8bfd-24cce82cb3eb");
        queryParams.put("strategy","P4B_MERCHANTNOTPRESENT");
        return  queryParams;
    }

    public Map<String,String> setCommonHeaders(){
        Map<String,String> headers= new HashMap<>();
        headers.put("Content-Type","application/json");
        headers.put("version","4.7.3");
        headers.put("custId",CustId);
        headers.put("Authorization",AuthorizationToken);
        return headers;
    }




    @Test()
    public void TC0001_fetchLeadStatusPositive() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        //LOGGER.info(" AuthorizationToken: " + AuthorizationToken);
        Map<String,String> headers= setCommonHeaders();
        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);

        System.out.println("########################");
        System.out.println(leadReponse);
        System.out.println("########################");
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test()
    public void TC0002_fetchLeadStatusWrongAuthorizationToken() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        AuthorizationToken+="wrongToken";
        Map<String,String> headers= setCommonHeaders();


        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test()
    public void TC0002_002_fetchLeadStatusEmptyAuthorizationToken() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="1002301925";
        //AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        AuthorizationToken="";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }
    @Test()
    public void TC0002_003_fetchLeadStatusNoAuthorizationToken() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        Map<String,String> headers= setCommonHeaders();
        headers.remove("Authorization");

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }


    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_1() throws Exception {
        // Authtoken is generated from wrong custid
        //
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="0000000000";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200);
    }

    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_2() throws Exception {
        // Authtoken is generated from wrong custid
        //CustId reverted back to Correct CustId
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="0000000000";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="1002301925";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }

    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_2_1() throws Exception {
        // Authtoken is generated from wrong custid
        //AuthToken is changed
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="0000000000";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        AuthorizationToken+="WrongToken";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }

    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_3() throws Exception {
        // Authtoken is generated from Correct custid
        //CustId changed wrong CustId
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="1000000000";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }

    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_4() throws Exception {
        // Authtoken is generated from Correct custid
        //AuthToken changed
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();
        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        AuthorizationToken+="WrongToken";
        Map<String,String> headers=setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }


    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_5() throws Exception {
        // Authtoken is generated from Correct custid
        //AuthToken emptied
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        AuthorizationToken="";
        Map<String,String> headers=setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }

    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_6() throws Exception {
        // Authtoken is generated from Correct custid
        //CustId emptied
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="";
        Map<String,String> headers=setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }

    @Test()
    public void TC0002_004_fetchLeadStatusAuthorizationToken_7() throws Exception {
        // Authtoken is generated from Correct custid
        //Both emptied
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="";
        AuthorizationToken="";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);
    }


    @Test
    public void TC0003_fetchLeadStatusWrongLeadId() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test
    public void TC0003_002_fetchLeadStatusEmptyLeadId() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","");

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test
    public void TC0003_002_fetchLeadStatusDifferentLeadId() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        String originalLeadId=leadId;
        //changed lead id
        queryParams.put("solutionLeadId","d3913bab-9226-4f85-963d-1937e38901bc");

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        String responseLeadId = leadReponse.jsonPath().getString("leadId");
        System.out.println("responseLeadId= " + responseLeadId);
        //Assert.assertTrue(!(originalLeadId.contains(responseLeadId)));
        Assert.assertTrue(!(originalLeadId==responseLeadId));
        // even in this case with lead if of different lead it is working fine but
        //returning response for different lead
    }


    @Test
    public void TC0004_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_001() throws Exception {
        // Wrong leadId
        // created auth token for wrong CustId
        // same CustId is passed
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301888";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        //CustId="1002301925";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test
    public void TC0004_002_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_002() throws Exception {
        // Wrong leadId
        // created auth token for wrong CustId
        // original CustId is passed
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301888";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="1002301925";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test
    public void TC0004_003_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_003() throws Exception {
        // Wrong leadId
        // created auth token for wrong CustId
        // CustId is different(which is present in db as TnC accept success)
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301888";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="1002330202";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test
    public void TC0004_004_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_004() throws Exception {
        // Wrong leadId
        // created auth token for correct CustId
        // CustId is changed (not present)
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="1002330888";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }
    @Test
    public void TC0004_005_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_005() throws Exception {
        // Wrong leadId
        // created auth token for correct CustId
        // CustId is emptied
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test
    public void TC0004_006_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_006() throws Exception {
        // Wrong leadId
        // created auth token for correct CustId
        // CustId is changed to different custid(which is present in db)
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        CustId="1002330202";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test
    public void TC0004_007_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_007() throws Exception {
        // Wrong leadId
        // auth token is empty
        // CustId is original
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301925";
        //AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        AuthorizationToken="";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test
    public void TC0004_008_fetchLeadStatusWrongLeadId_WrongPairOfAuth_Cust_008() throws Exception {
        // Wrong leadId
        // auth token is wrong (against no custid)
        // CustId is original
        Lead LeadObj=new Lead();
        Map<String,String> queryParams = setCommonParams();
        queryParams.put("solutionLeadId","wrong-solution-lead-id-07");

        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        AuthorizationToken+="wrongtoken";
        Map<String,String> headers= setCommonHeaders();

        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 401);

    }

    @Test()
    public void TC0005_fetchLeadStatusNoStrategy() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();
        queryParams.remove(("strategy"));
        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        //LOGGER.info(" AuthorizationToken: " + AuthorizationToken);
        Map<String,String> headers= setCommonHeaders();
        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        Assert.assertTrue(leadReponse.path("solution") != null);

    }

    @Test()
    public void TC0005_002_fetchLeadStatusWrongStrategy() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("strategy","wrongstrategy");
        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        //LOGGER.info(" AuthorizationToken: " + AuthorizationToken);
        Map<String,String> headers= setCommonHeaders();
        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        Assert.assertTrue(leadReponse.path("solution") == null);

    }

    @Test()
    public void TC0006_fetchLeadStatusNoSolution() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();
        queryParams.remove("solution");
        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        //LOGGER.info(" AuthorizationToken: " + AuthorizationToken);
        Map<String,String> headers= setCommonHeaders();
        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 400);

    }

    @Test()
    public void TC0006_002_fetchLeadStatusWrongSolution() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("solution","wrong solution");
        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        //LOGGER.info(" AuthorizationToken: " + AuthorizationToken);
        Map<String,String> headers= setCommonHeaders();
        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 500);

    }

    @Test(description = "fetchLeadStatusDifferentSolution")
    public void TC0006_003_fetchLeadStatusDifferentSolution() throws Exception {
        Lead LeadObj=new Lead();
        Map<String,String> queryParams=setCommonParams();
        queryParams.put("solution","qr_merchant");
        CustId="1002301925";
        AuthorizationToken=utilities.generateJwtTokenForMerchantCommonOnbaord(CustId);
        //LOGGER.info(" AuthorizationToken: " + AuthorizationToken);
        Map<String,String> headers= setCommonHeaders();
        Response leadReponse=middlewareServicesObject.v1fetchLeadStatus(LeadObj,queryParams,headers);
        int StatusCode = leadReponse.getStatusCode();
        Assert.assertEquals(StatusCode, 200) ;

    }

}
