package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.cKYC;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class getCKYC9 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new  CreateMCOLead2();
	sendingOTPLead4 otplead=new  sendingOTPLead4();

	private static final Logger LOGGER = LogManager.getLogger(GettingMerchantBusiness6.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "5555549829";
//    public static String ExistingMerchantmobileNo = "5555595699";
    public static String ExistingMerchantmobileNo = "5555631843";

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";
public static String STATUS="SUCCESS";
public static String SUCCESSMESSAGE="Otp sent to phone";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";
    public static String displayMessageWithoutsolutionType="Failed to validate OTP (Ref: G-JoWQ-101-500)";
    public static String displayMessageWithoutentity="Failed to validate OTP (Ref: G-JoV_-101-500)";
    public static String displayMessageInvaliduserType="Invalid action (Ref: G-JoWY-101-400)";
    public static String displayMessageInvalidMobileNumber="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";
    public static String displayMessageWithoutLeadID="Please provide a Paytm registered mobile number. (Ref: O-Jobi-101-200)";


    String sToken = AgentSessionToken("8010630022","paytm@123");
//    String sToken = AgentSessionToken("7771216290","paytm@123");

    String existingLead="6527b8d3-c576-4dc8-b386-0d2141ac0704";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("8010630022", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}
    

	@Test(priority = 0, description = "TC_1_getExistingLeadckyc", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_getExistingLeadckyc() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> cKYC=cKYCStatus("a1c38e9f-883b-4984-8334-6f216a05a066");
	    String ckycDone=cKYC.get(0);
		String kycDone=cKYC.get(1);
		String panValidated=cKYC.get(2);
		
	

		System.out.println("ckycDone :"+ckycDone);
		System.out.println("kycDone :"+kycDone);
		System.out.println("panValidated :"+panValidated);
		
		
	} 
	

	
	
	
	
	@Test(priority = 0, description = "TC_2_ErrorWithoutSessionTokengetExistingLeadckyc", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_ErrorWithoutSessionTokengetExistingLeadckyc() throws Exception {
		establishConnectiontoServer(sToken,5);

		cKYC kyc=new cKYC(P.TESTDATA.get("CKYC"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", "a1c38e9f-883b-4984-8334-6f216a05a066");
	queryParam.put("isGstReq", "true");
	queryParam.put("isDMSURLRequired", "true");

	headers.put("Content-Type", "application/json");
//	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getCKYCMethod(kyc, queryParam, headers);
	

    int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 401, "Testcase Failed");
	} 


//	@Test(priority = 1, description = "Test case description", groups = { "Regression" })
//	@Owner(emailId = "<EMAIL>", isAutomated = true)
//	public void testMethodName() {
//		cKYC kyc = new cKYC(P.TESTDATA.get("CKYC"));
//	
//		Map<String, String> queryParam = new HashMap<String, String>();
//		Map<String, String> headers = new HashMap<String, String>();
//	
//		queryParam.put("leadId", null);
//		queryParam.put("isGstReq", "false");
//		queryParam.put("isDMSURLRequired", "false");
//	
//		headers.put("Content-Type", "application/json");
//		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
//	
//		Response respObj = middlewareServicesObject.getCKYCMethod(kyc, queryParam, headers);
//	
//		int httpcode = respObj.getStatusCode();
//		Assert.assertTrue(httpcode == 401, "Testcase Failed");
//	}

	
	@Test(priority = 0, description = "TC_3_ErrorWithoutVersiongetExistingLeadckyc", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_ErrorWithoutVersiongetExistingLeadckyc() throws Exception {
		establishConnectiontoServer(sToken,5);

		cKYC kyc=new cKYC(P.TESTDATA.get("CKYC"));
			
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
		queryParam.put("leadId", "a1c38e9f-883b-4984-8334-6f216a05a066");
		queryParam.put("isGstReq", "true");
		queryParam.put("isDMSURLRequired", "true");

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
		headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

		Response respObj = middlewareServicesObject.getCKYCMethod(kyc, queryParam, headers);
		

	    int httpcode = respObj.getStatusCode();
		
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");
    String actualdisplaymessage=respObj.jsonPath().get("displayMessage");
//    Assert.assertEquals(actualdisplaymessage,displayMessage);
    String actualmessage=respObj.jsonPath().get("message");
//    Assert.assertEquals(actualmessage,message);
    String actualerrorCode=respObj.jsonPath().get("errorCode");
//    Assert.assertEquals(actualerrorCode,errorCode);
    
	} 
	

	@Test(priority = 0, description = "TC_4_GetCKYCErrorWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_GetCKYCErrorWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);


//getBank getBank=new getBank(P.TESTDATA.get("GetBank"),"**********");
	
cKYC kyc=new cKYC(P.TESTDATA.get("CKYC"));

Map<String, String> queryParam = new HashMap<String, String>();
Map<String, String> headers = new HashMap<String, String>();
queryParam.put("leadId", "a1c38e9f-883b-4984-8334-6f216a05a066");
queryParam.put("isGstReq", "true");
queryParam.put("isDMSURLRequired", "true");

headers.put("Content-Type", "application/json");
headers.put("session_token", sToken);
//headers.put("deviceidentifier", deviceIdentifer);
headers.put("version", version);
headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

Response respObj = middlewareServicesObject.getCKYCMethod(kyc, queryParam, headers);


int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 410, "Testcase Failed");
    
	} 
	
	
    
	public List<String> cKYCStatus(String LeadID)
	{
		cKYC kyc=new cKYC(P.TESTDATA.get("CKYC"));
		
		Map<String, String> queryParam = new HashMap<String, String>();
		Map<String, String> headers = new HashMap<String, String>();
	queryParam.put("leadId", LeadID);
	queryParam.put("isGstReq", "true");
	queryParam.put("isDMSURLRequired", "true");

	headers.put("Content-Type", "application/json");
	headers.put("session_token", sToken);
	headers.put("deviceidentifier", deviceIdentifer);
	headers.put("version", version);
	headers.put("UncleScrooge", "BabaBlackSheepWeAreInShitDeep");

	Response respObj = middlewareServicesObject.getCKYCMethod(kyc, queryParam, headers);
	List<String> ltnew=new ArrayList<String>();
	ltnew.add(respObj.jsonPath().get("ckycDone").toString());
	ltnew.add(respObj.jsonPath().get("kycDone").toString());
	ltnew.add(respObj.jsonPath().get("panValidated").toString());
	

int httpcode = respObj.getStatusCode();
//    Assert.assertTrue(httpcode == 200, "Testcase Failed");

	return ltnew;
	}

	// Copilot suggestion


 @Test(priority = 3, description = "EmptyLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
 public void TC_4_EmptyLeadID() {
     List<String> cKYC = cKYCStatus("");
 }




	

}


