package OCL.CommonOnboarding.EDC;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.createDeviceLead;
import Request.CommonOnboardingEDC.partialSaveCall;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class CreateDeviceOnboardingLead13 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	AddBank8 objbank=new AddBank8();
	PartialSave18 parobj=new PartialSave18();
	private static final Logger LOGGER = LogManager.getLogger(CreateDeviceOnboardingLead13.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
  

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("**********","paytm@123");

//    String sToken = "4d97b959-9746-4633-8136-a39586555600";

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}

	@Test(priority = 0, description = "TC_1_CreateDeviceOnboardingLead", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_CreateDeviceOnboardingLead() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=CreateDeviceOnboardingLead("5975932129");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
	}



	

	@Test(priority = 0, description = "CreateDeviceOnboardingLead", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public List<Object> CreateDeviceOnboardingLead(String mobileno) throws Exception {
		establishConnectiontoServer(sToken,5);

		List<String> obj=objbank.TC_5_addBank(mobileno);
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

//		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
//			Map<String, String> queryParam = new HashMap<String, String>();
//			Map<String, String> headers = new HashMap<String, String>();
//			Map<String, Object> mybody = new HashMap<String, Object>();
//
//		queryParam.put("solutionType", "merchant_common_onboard");
//		queryParam.put("entityType", entityType);
//		queryParam.put("leadId", leadId);
//
//		headers.put("Content-Type", "application/json");
//		headers.put("session_token", sToken);
//		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
//		headers.put("UncleScrooge", xmwChecksumBypassValue);
//	
//	       String questionList="["
//	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
//	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
//	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
//	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
//	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
//	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
//	      
//	       mybody.put("syncChildLeads", true);
//			mybody.put("partialSave", true);
//			mybody.put("onlyValidateBankDetails", false);
//			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
//			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
//			mybody.put("QUESTIONS_LIST", questionList);
//
//
//		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
//      
        

        createDeviceLead createDeviceLeadobj = new createDeviceLead(P.TESTDATA.get("createDeviceLeadReq"));
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
		queryParam1.put("parentLeadId", leadId);
		queryParam1.put("custId", custId);
		headers1.put("Content-Type", "application/json");
		headers1.put("session_token", sToken);
		headers1.put("deviceidentifier", deviceIdentifer);
		headers1.put("version", version);
		headers1.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.createDeviceLeadPost(createDeviceLeadobj,queryParam1,headers1);
		int httpcodeNew = respObjnew.getStatusCode();
		Assert.assertEquals(httpcodeNew, 200, "Expected HTTP status code 200 but got " + httpcodeNew);


		Object deviceOnboardingLeadID=respObjnew.jsonPath().get("leadId");
	     Object isLeadAlreadyExists=respObjnew.jsonPath().get("isLeadAlreadyExists");
	     Object entityTypeLead=respObjnew.jsonPath().get("entityType");
	     Object hasEdcContext=respObjnew.jsonPath().get("hasEdcContext");
	     Object hasEdcContextDropped=respObjnew.jsonPath().get("hasEdcContextDropped");
	     List<Object> lt=new ArrayList<Object>();
	     lt.add(deviceOnboardingLeadID);
	     lt.add(leadId);
	     lt.add(custId);
	     lt.add(isLeadAlreadyExists);
	     lt.add(entityTypeLead);
	     lt.add(hasEdcContext);
	     lt.add(hasEdcContextDropped);
         return lt;
	     		
	} 


    
	
	

	
	@Test(priority = 0, description = "TC_2_CreateDeviceOnboardingLeadWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_CreateDeviceOnboardingLeadWithoutSessiontoken() throws Exception {

		establishConnectiontoServer(sToken,5);

List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();

		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
	
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	      
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);


		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
      
        

        createDeviceLead createDeviceLeadobj = new createDeviceLead(P.TESTDATA.get("createDeviceLeadReq"));
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
			
		queryParam1.put("parentLeadId", leadId);
		queryParam1.put("custId", custId);
	

		headers1.put("Content-Type", "application/json");
//		headers1.put("session_token", sToken);
		headers1.put("deviceidentifier", deviceIdentifer);
		headers1.put("version", version);
		headers1.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.createDeviceLeadPost(createDeviceLeadobj,queryParam1,headers1);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 401, "Testcase Failed");
	
		
	} 
	
	
	@Test(priority = 0, description = "TC_3_CreateDeviceOnboardingLeadWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_CreateDeviceOnboardingLeadWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);


		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();

		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
	
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	      
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);


		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
      
        

        createDeviceLead createDeviceLeadobj = new createDeviceLead(P.TESTDATA.get("createDeviceLeadReq"));
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
			
		queryParam1.put("parentLeadId", leadId);
		queryParam1.put("custId", custId);
	

		headers1.put("Content-Type", "application/json");
		headers1.put("session_token", sToken);
//		headers1.put("deviceidentifier", deviceIdentifer);
		headers1.put("version", version);
		headers1.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.createDeviceLeadPost(createDeviceLeadobj,queryParam1,headers1);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 410, "Testcase Failed");
	
		
	} 


	@Test(priority = 0, description = "TC_4_CreateDeviceOnboardingLeadWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_CreateDeviceOnboardingLeadWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);


		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();

		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
	
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	      
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);


		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
      
        

        createDeviceLead createDeviceLeadobj = new createDeviceLead(P.TESTDATA.get("createDeviceLeadReq"));
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
			
		queryParam1.put("parentLeadId", leadId);
		queryParam1.put("custId", custId);
	

		headers1.put("Content-Type", "application/json");
		headers1.put("session_token", sToken);
		headers1.put("deviceidentifier", deviceIdentifer);
//		headers1.put("version", version);
		headers1.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.createDeviceLeadPost(createDeviceLeadobj,queryParam1,headers1);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	
		
	} 	



	@Test(priority = 0, description = "TC_5_CreateDeviceOnboardingLeadWithInvalidLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_CreateDeviceOnboardingLeadWithInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);


		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();

		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
	
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	      
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);


		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
      
        

        createDeviceLead createDeviceLeadobj = new createDeviceLead(P.TESTDATA.get("createDeviceLeadReq"));
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
			
		queryParam1.put("parentLeadId", "234-4325-3445");
		queryParam1.put("custId", custId);
	

		headers1.put("Content-Type", "application/json");
		headers1.put("session_token", sToken);
		headers1.put("deviceidentifier", deviceIdentifer);
		headers1.put("version", version);
		headers1.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.createDeviceLeadPost(createDeviceLeadobj,queryParam1,headers1);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
	        String actualMessage=respObjnew.jsonPath().get("displayMessage");
	        String expectedMessage="invalid create lead request, Parent lead details not found. Please try again.";
//            Assert.assertEquals(actualMessage, expectedMessage);
		
	} 	
	 
	
	@Test(priority = 0, description = "TC_6_CreateDeviceOnboardingLeadWithoutLeadID", groups = { "Regression" })
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_CreateDeviceOnboardingLeadWithoutLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);


		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();

		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
	
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	      
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);


		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
      
        

        createDeviceLead createDeviceLeadobj = new createDeviceLead(P.TESTDATA.get("createDeviceLeadReq"));
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
			
//		queryParam1.put("parentLeadId", "234-4325-3445");
		queryParam1.put("custId", custId);
	

		headers1.put("Content-Type", "application/json");
		headers1.put("session_token", sToken);
		headers1.put("deviceidentifier", deviceIdentifer);
		headers1.put("version", version);
		headers1.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.createDeviceLeadPost(createDeviceLeadobj,queryParam1,headers1);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
	} 	
	

	@Test(priority = 0, description = "TC_6_CreateDeviceOnboardingLeadWithoutcustId", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_CreateDeviceOnboardingLeadWithoutcustId() throws Exception {
		establishConnectiontoServer(sToken,5);


		List<String> obj=objbank.TC_5_addBank("**********");
		
		String custId=obj.get(0);
		String leadId=obj.get(1);
		String entityType=obj.get(2);
		

		partialSaveCall partialSaveCallobject = new partialSaveCall(P.TESTDATA.get("PartialSaveCalling"),custId);
			Map<String, String> queryParam = new HashMap<String, String>();
			Map<String, String> headers = new HashMap<String, String>();
			Map<String, Object> mybody = new HashMap<String, Object>();

		queryParam.put("solutionType", "merchant_common_onboard");
		queryParam.put("entityType", entityType);
		queryParam.put("leadId", leadId);

		headers.put("Content-Type", "application/json");
		headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
	
	       String questionList="["
	       		+ "{\\\"questionAlias\\\":\\\"cardPaymentsPerMonth\\\","
	       		+ "\\\"answerAlias\\\":\\\"15 - 30 Lakh\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"numberOfEmployees\\\","
	       		+ "\\\"answerAlias\\\":\\\"Upto 5\\\"},"
	       		+ "{\\\"questionAlias\\\":\\\"shopRentedOrOwned\\\","
	       		+ "\\\"answerAlias\\\":\\\"Shop Rented\\\"}]";
	      
	       mybody.put("syncChildLeads", true);
			mybody.put("partialSave", true);
			mybody.put("onlyValidateBankDetails", false);
			mybody.put("NUMBER_OF_EDC_MACHINES_REQUIRED", "Single");
			mybody.put("SMALL_MERCHANT_DECLARATION", "false");
			mybody.put("QUESTIONS_LIST", questionList);


		Response respObj = middlewareServicesObject.partialSaving(partialSaveCallobject,queryParam,headers,mybody);
      
        

        createDeviceLead createDeviceLeadobj = new createDeviceLead(P.TESTDATA.get("createDeviceLeadReq"));
			Map<String, String> queryParam1 = new HashMap<String, String>();
			Map<String, String> headers1 = new HashMap<String, String>();
			
		queryParam1.put("parentLeadId", leadId);
//		queryParam1.put("custId", custId);
	

		headers1.put("Content-Type", "application/json");
		headers1.put("session_token", sToken);
		headers1.put("deviceidentifier", deviceIdentifer);
		headers1.put("version", version);
		headers1.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.createDeviceLeadPost(createDeviceLeadobj,queryParam1,headers1);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
	} 		
	
}

