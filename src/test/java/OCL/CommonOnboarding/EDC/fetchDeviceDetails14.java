package OCL.CommonOnboarding.EDC;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.goldengate.common.RetryAnalyzer;
//import org.apache.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.goldengate.common.BaseMethod;
import com.paytm.apitools.core.P;
import com.paytm.apitools.util.annotations.Owner;

import Request.CommonOnboardingEDC.devicedetailsfetch;
import Services.MechantService.MiddlewareServices;
import io.restassured.response.Response;

public class fetchDeviceDetails14 extends BaseMethod {
	
	MiddlewareServices middlewareServicesObject = new MiddlewareServices();
	CreateMCOLead2 mcoLead=new CreateMCOLead2();
	CreateDeviceOnboardingLead13 leadObj=new CreateDeviceOnboardingLead13();
	AddBank8 objbank=new AddBank8();
	PartialSave18 parobj=new PartialSave18();
	private static final Logger LOGGER = LogManager.getLogger(fetchDeviceDetails14.class);

    public static String deviceIdentifer = "OnePlus-GM1901-9046eec81af9340b";
    public static String version = "5.1.1";
    public static String NewMerchantmobileNo = "**********";
  

    public static String Individualentity = "INDIVIDUAL";
    public static String user_Type = "common_merchant";
    boolean skipOTPTRUE=true;    boolean skipOTPFalse=false;
boolean onlySaveTncFalse=false;  boolean onlySaveTncTrue=true;
public static String xmwChecksumBypassValue = "BabaBlackSheepWeAreInShitDeep";


    public static String displayMessage="version is empty in header";
    public static String message="version is empty in header";
    public static String errorCode="VERSION_FAILURE";


    String sToken = AgentSessionToken("**********","paytm@123");
//    String sToken = AgentSessionToken("**********","paytm@123");

	@BeforeTest
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void AgentLogin() throws Exception {
		sToken = AgentSessionToken("**********", "paytm@123");
		LOGGER.info("Agent Token  : " + sToken);
//		establishConnectiontoServer(sToken,5);
		waitForLoad(3000);
	}


	@Test(priority = 0, description = "TC_1_fetchdevicedetails", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@JsonProperty(value = "jsondata")
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_1_fetchdevicedetails() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5822532129");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);

	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	        System.out.println("Device Model :"+respObjnew.jsonPath().get("deviceInfo[0].model").toString());
	        System.out.println("Device OS :"+respObjnew.jsonPath().get("deviceInfo[0].osType").toString());
	        System.out.println("Device Name :"+respObjnew.jsonPath().get("deviceInfo[0].metadata.displayName").toString());
	        System.out.println("Device Model :"+respObjnew.jsonPath().get("deviceInfo[1].model").toString());
	        System.out.println("Device OS :"+respObjnew.jsonPath().get("deviceInfo[1].osType").toString());
	        System.out.println("Device Name :"+respObjnew.jsonPath().get("deviceInfo[1].metadata.displayName").toString());
	        System.out.println("Device Model :"+respObjnew.jsonPath().get("deviceInfo[2].model").toString());
	        System.out.println("Device OS :"+respObjnew.jsonPath().get("deviceInfo[2].osType").toString());
	        System.out.println("Device Name :"+respObjnew.jsonPath().get("deviceInfo[2].metadata.displayName").toString());
	        System.out.println("Device Model :"+respObjnew.jsonPath().get("deviceInfo[3].model").toString());
	        System.out.println("Device OS :"+respObjnew.jsonPath().get("deviceInfo[3].osType").toString());
	        System.out.println("Device Name :"+respObjnew.jsonPath().get("deviceInfo[3].metadata.displayName").toString());
	        System.out.println("Device Model :"+respObjnew.jsonPath().get("deviceInfo[4].model").toString());
	        System.out.println("Device OS :"+respObjnew.jsonPath().get("deviceInfo[4].osType").toString());
	        System.out.println("Device Name :"+respObjnew.jsonPath().get("deviceInfo[4].metadata.displayName").toString());
	        System.out.println("Device Model :"+respObjnew.jsonPath().get("deviceInfo[5].model").toString());
	        System.out.println("Device OS :"+respObjnew.jsonPath().get("deviceInfo[5].osType").toString());
	        System.out.println("Device Name :"+respObjnew.jsonPath().get("deviceInfo[5].metadata.displayName").toString());

	        System.out.println("Plan Chosen :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].name"));
	        System.out.println("Plan ID Selected :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].id"));
	        System.out.println("Pricing component :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.lifeTimeFee.label"));
	        System.out.println("Lifetime fee Amount :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.lifeTimeFee.amount"));
	        System.out.println("Lifetime fee Optional :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.lifeTimeFee.optional"));
	        System.out.println("Pricing component :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.usageDeposit.label"));
	        System.out.println("Usage deposit :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.usageDeposit.amount"));
	        System.out.println("Usage deposit Optional :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.usageDeposit.optional"));        
	       
	        System.out.println("Pricing Component :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.advanceRentForFirstMonth.label"));
	        System.out.println("Advance Rent charge :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.advanceRentForFirstMonth.amount"));
	        System.out.println("Advance Rent charge Optional"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.advanceRentForFirstMonth.optional"));


	        System.out.println("Pricing Component :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.installationCharge.label"));
	        System.out.println("Installation charge :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.installationCharge.amount"));
	        System.out.println("Installation charge Optional"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.installationCharge.optional"));


	        System.out.println("Pricing component :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.totalUpfrontCharge.label"));
	        System.out.println("Total Upfront charge :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.totalUpfrontCharge.amount"));
	        System.out.println("Total Upfront charge Optional :"+respObjnew.jsonPath().get("deviceInfo[5].plans[2].pricingComponent.oneTimeComponent.totalUpfrontCharge.optional"));
	        
	        

	        
	        


	        
	
	} 
	

	

	
	@Test(priority = 0, description = "TC_2_fetchdevicedetailsWithoutSessiontoken", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_2_fetchdevicedetailsWithoutSessiontoken() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5822532129");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
//			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);
	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 401, "Testcase Failed");
	
		
	} 
	
	
	@Test(priority = 0, description = "TC_3_fetchdevicedetailsWithoutDeviceIdentifer", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_3_fetchdevicedetailsWithoutDeviceIdentifer() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5822532129");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
//		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);
	
	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 410, "Testcase Failed");
	
		
	} 


	@Test(priority = 0, description = "TC_4_fetchdevicedetailsWithoutVersion", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_4_fetchdevicedetailsWithoutVersion() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5822532129");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
//		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);
	
	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 200, "Testcase Failed");
	
		
	} 	



	@Test(priority = 0, description = "TC_5_fetchdevicedetailsLeadWithInvalidLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_5_fetchdevicedetailsLeadWithInvalidLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5822532129");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
			queryParam.put("leadId", "1223");
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);
	
	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 500, "Testcase Failed");
		
	} 	
	 
	
	@Test(priority = 0, description = "TC_6_fetchdevicedetailsLeadWithoutLeadID", groups = { "Regression" }, retryAnalyzer = RetryAnalyzer.class)
	@Owner(emailId = "<EMAIL>", isAutomated = true)
	public void TC_6_fetchdevicedetailsLeadWithoutLeadID() throws Exception {
		establishConnectiontoServer(sToken,5);

		List<Object> data=leadObj.CreateDeviceOnboardingLead("5822532129");

		Object deviceOnboardingLeadID=data.get(0);
		Object mcoLead=data.get(1);
		Object custId=data.get(2);
		Object hasEDCContextDropped=data.get(6);
		
		System.out.println("DeviceOnboardingLead :"+deviceOnboardingLeadID+" MCO Lead :"+mcoLead+
				" Customer Id :"+custId+" hasEDCContextDropped :"+hasEDCContextDropped
				);
		
     devicedetailsfetch devicedetailsfetchobj = new devicedetailsfetch(P.TESTDATA.get("FetchdevicedetailsReq"));
			Map<String, Object> queryParam = new HashMap<String, Object>();
			Map<String, Object> headers = new HashMap<String, Object>();
			
//			queryParam.put("leadId", deviceOnboardingLeadID);
			headers.put("Content-Type", "application/json");
			headers.put("session_token", sToken);
		headers.put("deviceidentifier", deviceIdentifer);
		headers.put("version", version);
		headers.put("UncleScrooge", xmwChecksumBypassValue);
		Response respObjnew = middlewareServicesObject.fetchDeviceLeadGet(devicedetailsfetchobj,queryParam,headers);
	
		
	        int httpcode = respObjnew.getStatusCode();
//	        Assert.assertTrue(httpcode == 400, "Testcase Failed");
		
	} 	
	

	
}


